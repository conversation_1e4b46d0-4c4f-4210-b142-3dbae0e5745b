﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;
using System.Data.SqlClient;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;
        //private clScrMng goScr;
        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";


        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();

            Util.SetSessionValue("SkipQLSpecificLogic", "Y");

        }


        public ScriptsCustom()
        {
            Initialize();
        }

        // SGR 25102016 
        public string CO_GenerateSequentialCustomerNo()
        {

            long iCustomerNo = Convert.ToInt64(goMeta.LineRead("", "OTH_CUSTOMERNOSEQUENTIAL", "NEXTCUSTOMERNO"));
            iCustomerNo = iCustomerNo + 1;
            string iCustomerNoText = Convert.ToString(iCustomerNo);
            goMeta.LineWrite("", "OTH_CUSTOMERNOSEQUENTIAL", "NEXTCUSTOMERNO", iCustomerNoText, ref par_oConnection, "");
            return iCustomerNoText.ToString();
        }

        public string CN_GenerateSequentialCustomerNo()
        {
            long iConCustomerNo = Convert.ToInt64(goMeta.LineRead("", "OTH_CUSTOMERNOSEQUENTIAL", "NEXTCONTACTNO"));
            iConCustomerNo = iConCustomerNo + 1;
            string iConCustomerNoText = Convert.ToString(iConCustomerNo);
            goMeta.LineWrite("", "OTH_CUSTOMERNOSEQUENTIAL", "NEXTCONTACTNO", iConCustomerNoText, ref par_oConnection, "");
            return iConCustomerNoText.ToString();
        }


        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }


        public bool AutoCOUpdate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS ******** TKT#1305 : Profile Matrix
            // TLD 7/24/2012
            // PURPOSE:
            // Called by agent to update 3 custom date fields that
            // record latest AC of type Sales Visit, latest OP and latest Quote
            // A_01_EXECUTE=AutoCOUpdate
            // A_01_OBJSHARED = 1
            // A_01_TYPE = RUNSCRIPT
            // A_ORDER=1,
            // ACTIONS=1,
            // ACTIVE = 1
            // E_TM_HOUR = 19 '7:00 PM
            // E_TM_INTERVAL = 3 'Every day
            // E_TM_MINUTE = 0 'top of hour
            // EVENT=TIMER
            // US_NAME=AutoCOUpdate
            // US_PURPOSE=Runs daily Update of Company records
            // SORTVALUE1=TIMER_ACTIVE

            long lBiID = 0;
            long lastBiId = 0;
            clRowSet doRS;
            clRowSet doNewRS;
            long iCount = 0;
            bool bUpdateCO = false;
            string par_sDelim = " ";
            string sNowDate = goTR.DateTimeToSysString(goTR.NowLocal(), ref par_iValid, ref par_sDelim);
            string sRecID = "";
            string sWOP = goMeta.PageRead("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED");
            int iFailedOther = 0;
            int iFailedPerm = 0;
            int iFailedTotal = 0;
            int iSuccess = 0;

            try
            {
                do
                {
                    doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT", 50, "", "", "", "", "", true, true, true);
                    iCount = iCount + doRS.Count();
                    if (doRS.GetFirst() == 1)
                    {
                        lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                        do
                        {
                            bUpdateCO = false;
                            sRecID = (doRS.GetCurrentRecID() == null) ? "" : (doRS.GetCurrentRecID().ToString());

                            // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                            if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                            {
                                doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            // TLD 5/18/2011 -----------Added to include date of latest OP
                            if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                            {
                                doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            // TLD 5/18/2011 -----------Added to include date of latest QT
                            if (doRS.GetLinkCount("LNK_Received_QT") > 0)
                            {
                                doNewRS = new clRowSet("QT", 3, "LNK_To_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }

                            // Update CO
                            if (bUpdateCO == true)
                            {
                                if (doRS.Commit() == 0)
                                {
                                    if (goErr.GetLastError("NUMBER") == "E47250")
                                    {
                                        // Commit failed b/c user has no permissions to edit record; log it, but proceed
                                        iFailedPerm = iFailedPerm + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + doRS.GetFieldVal("TXT_CompanyName") + " due to permissions.", -1, true, true);
                                    }
                                    else
                                    {
                                        // Commit failed for some other reason.
                                        iFailedOther = iFailedOther + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + doRS.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                    }
                                }
                            }

                            if (doRS.GetNext() == 0)
                                break;
                        }
                        while (true);
                        lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                    }
                    else
                        break;
                }
                while (true)// until set to true below// testing// testing// get last BI__ID processed
        ;

                // Check once more for any newly added records
                doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT", -1, "", "", "", "", "", true, true, true);
                iCount = iCount + doRS.Count();
                if (doRS.GetFirst() == 1)
                {
                    do
                    {
                        bUpdateCO = false;
                        sRecID = (doRS.GetCurrentRecID() == null) ? "" : (doRS.GetCurrentRecID().ToString());

                        // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                        if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                        {
                            doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        // TLD 5/18/2011 -----------Added to include date of latest OP
                        if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                        {
                            doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        // TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        // TLD 5/18/2011 -----------Added to include date of latest QT
                        if (doRS.GetLinkCount("LNK_Received_QT") > 0)
                        {
                            doNewRS = new clRowSet("QT", 3, "LNK_To_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }

                        // Update CO
                        if (bUpdateCO == true)
                        {
                            if (doRS.Commit() == 0)
                            {
                                if (goErr.GetLastError("NUMBER") == "E47250")
                                {
                                    // Commit failed b/c user has no permissions to edit record; log it, but proceed
                                    iFailedPerm = iFailedPerm + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + doRS.GetFieldVal("TXT_CompanyName") + " due to permissions.", -1, false, true);
                                }
                                else
                                {
                                    // Commit failed for some other reason.
                                    iFailedOther = iFailedOther + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + doRS.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                }
                            }
                        }

                        if (doRS.GetNext() == 0)
                            break;
                    }
                    while (true)// until set to true below// testing// testing
        ;
                    lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));  // get last bi__id processed        
                }
                iFailedTotal = iFailedOther + iFailedPerm;
                iSuccess = Convert.ToInt32(iCount) - iFailedTotal;

                // Write to WOP
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Started " + sNowDate + " and Completed " + goTR.DateTimeToSysString(goTR.NowLocal(), ref par_iValid, ref par_sDelim) + " with " + iSuccess + " successful updates; " + iFailedPerm + " failed updates due to permissions; " + iFailedOther + " total failed updates.");
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);

                iCount = 0;
                iFailedOther = 0;
                iFailedPerm = 0;
                iFailedTotal = 0;
                iSuccess = 0;
                doRS = null/* TODO Change to default(_) if this is not a reference type */;
                lBiID = 0;
            }
            catch (Exception ex)
            {
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Failed at Record " + sRecID + " " + goErr.GetLastError("NUMBER"));
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);
            }

            return true;
        }


        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // VS ******** TKT#1305 : Profile Matrix
            // CS 11/19/2014 If Activity is of type Sales Visit, update linked Related Company.Last AC Sales date
            if (doRS.GetInfo("TYPE") == "2")
            {
                if (Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2)) == 11)
                {
                    if (doRS.GetLinkCount("LNK_RELATED_CO") > 0)
                    {
                        clArray doCompanies = new clArray();
                        doCompanies = (clArray)doRS.GetFieldVal("LNK_RELATED_CO", 2);
                        // Bipass reconsave and validation to speed up AC save
                        for (int i = 1; i <= doCompanies.GetDimension(); i++)
                        {
                            clRowSet doRSCompany = new clRowSet("CO", 1, "GID_ID='" + doCompanies.GetItem(i) + "'", "", "DTT_LastACSales", 1, "", "", "", "", "", true, true);
                            if (doRSCompany.GetFirst() == 1)
                            {
                                doRSCompany.SetFieldVal("DTT_LastACSales", doRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                if (doRSCompany.Commit() == 0)
                                    goLog.Log(sProc, "CO update of last AC sales visit date field failed for CO " + doRSCompany.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                            }
                            doRSCompany = null/* TODO Change to default(_) if this is not a reference type */;
                        }
                    }
                }
                else
        // Activity is not of type sales visit
        if (doRS.GetLinkCount("LNK_RELATED_CO") > 0)
                {
                    clArray doCompanies = new clArray();
                    doCompanies = (clArray)doRS.GetFieldVal("LNK_RELATED_CO", 2);
                    // Bipass reconsave and validation to speed up AC save
                    for (int i = 1; i <= doCompanies.GetDimension(); i++)
                    {
                        clRowSet doRSCompany = new clRowSet("CO", 1, "GID_ID='" + doCompanies.GetItem(i) + "'", "", "DTT_LastAC", -1, "", "", "", "", "", true, true);
                        if (doRSCompany.GetFirst() == 1)
                        {
                            doRSCompany.SetFieldVal("DTT_LastAC", doRS.GetFieldVal("DTT_CreationTime", 2), 2);
                            if (doRSCompany.Commit() == 0)
                                goLog.Log(sProc, "CO update of last AC date field failed for CO " + doRSCompany.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".", -1, true, true);
                        }
                        doRSCompany = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                }
            }

            // CS 11/19/2014 If Activity is of type Sales Visit, update linked Related Contact.Last AC Sales Date
            if (Convert.ToString(doRS.GetInfo("TYPE")) == "2")
            {
                if (Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2)) == 11)
                {
                    if (doRS.GetLinkCount("LNK_RELATED_CN") > 0)
                    {
                        clArray doContacts = new clArray();
                        doContacts = (clArray)doRS.GetFieldVal("LNK_RELATED_CN", 2);
                        // Bipass reconsave and validation to speed up AC save
                        for (int i = 1; i <= doContacts.GetDimension(); i++)
                        {
                            clRowSet doRSContact = new clRowSet("CN", 1, "GID_ID='" + doContacts.GetItem(i) + "'", null, "DTT_LastACSales", -1, "", "", "", "", "", true, true);
                            if (doRSContact.GetFirst() == 1)
                            {
                                doRSContact.SetFieldVal("DTT_LastACSales", doRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                if (doRSContact.Commit() == 0)
                                    goLog.Log(sProc, "CN update of last AC sales visit date field failed for CN " + doRSContact.GetFieldVal("TXT_NameLast") + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                            }
                            doRSContact = null/* TODO Change to default(_) if this is not a reference type */;
                        }
                    }
                }
                else
        // Activity is not of type sales visit, so update Related Contact. Last AC date
        if (doRS.GetLinkCount("LNK_RELATED_CN") > 0)
                {
                    clArray doContacts = new clArray();
                    doContacts = (clArray)doRS.GetFieldVal("LNK_RELATED_CN", 2);
                    // Bipass reconsave and validation to speed up AC save
                    for (int i = 1; i <= doContacts.GetDimension(); i++)
                    {
                        clRowSet doRSContact = new clRowSet("CN", 1, "GID_ID='" + doContacts.GetItem(i) + "'", null/* Conversion error: Set to default value for this argument */, "DTT_LastAC", -1, "", "", "", "", "", true, true);
                        if (doRSContact.GetFirst() == 1)
                        {
                            doRSContact.SetFieldVal("DTT_LastAC", doRS.GetFieldVal("DTT_CreationTime", 2), 2);
                            if (doRSContact.Commit() == 0)
                                goLog.Log(sProc, "CN update of last AC date field failed for CN " + doRSContact.GetFieldVal("TXT_NameLast") + " with error " + goErr.GetLastError("NUMBER") + ".", -1, false, true);
                        }
                        doRSContact = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                }
            }


            // SGR TKT:# 1389 29122016
            clArray doOpp = new clArray();
            doOpp = (clArray)doRS.GetFieldVal("LNK_RELATED_OP", 2);
            for (int i = 1; i <= doOpp.GetDimension(); i++)
            {
                clRowSet doRSOpp = new clRowSet("OP", 3, "GID_ID='" + doOpp.GetItem(i) + "'", "", "GID_ID,MLS_MAINBLANKET", -1, "", "", "", "", "", true, true);
                if (doRSOpp.GetFirst() == 1)
                {
                    if (Convert.ToInt32(doRSOpp.GetFieldVal("MLS_MAINBLANKET", 2)) == 0)
                    {
                        doOpp = new clArray();
                        doOpp = (clArray)doRSOpp.GetFieldVal("LNK_BLANKETOPPS_OP", 2);
                        doRS.SetFieldVal("LNK_RELATED_OP", doOpp, 2);
                    }
                }
                doRSOpp = null/* TODO Change to default(_) if this is not a reference type */;
            }

            par_doCallingObject = doRS;
            return true;
        }


        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // SGR 25102016
            Form doForm = (Form)par_doCallingObject;


            doForm.SetControlState("TXT_CONTACTNO", 4);
            if (doForm.GetMode() == "CREATION")
            {
                string sCustNo = "S" + CN_GenerateSequentialCustomerNo();
                doForm.doRS.SetFieldVal("TXT_CONTACTNO", sCustNo);
            }

            // SKO ******** TKT1322: disable Merged
            doForm.SetControlState("CHK_Merged", 4);

            par_doCallingObject = doForm;
            return true;
        }


        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO ******** TKT1322: Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");

                return false;
            }

            if (doForm.doRS.GetFieldVal("EML_EMAIL").ToString().Length > 100)
            {
                doForm.MessageBox("Email cannot be bigger than 100 characters.");
                return false;
            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO ******** TKT1322: Merge Functionality - run at end of CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CN_Merge")) != "1")
                    {
                        // Don't allow merge of contact to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID"))
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CN", "MergeFail");
                        else
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CN", "Merge");
                    }
                }
            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_FormAfterSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // Invoke SIS
            string sOutput = CallSISWebservice("CN");

            if (sOutput != "")
                goErr.SetWarning(30029, sProc, sOutput, "", "", "", "", "", "", "", "", "", "");

            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // VS 12132016
            doRS.oVar.SetVar("DeleteContNO", doRS.GetFieldVal("TXT_ContactNO"));

            par_doCallingObject = doRS;
            return true;
        }

        public bool CN_RecordAfterDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // VS 12132016
            string sOutput = CallSISWebserviceDelete("CN", doRS.oVar.GetVar("DeleteContNO").ToString()); // doRS.GetFieldVal("TXT_ContactNO"))

            if (sOutput != "")
                goErr.SetWarning(30029, sProc, sOutput, "", "", "", "", "", "", "", "", "", "");


            par_doCallingObject = doRS;
            return true;
        }

        public string CallSISWebservice(string sFile)
        {
            if (goMeta.LineRead("GLOBAL", "OTH_SISWEBSERVICE_DATA", "ACTIVE", "", false, "XX") != "1")
                return "";

            string sProcessID = goMeta.LineRead("GLOBAL", "OTH_SISWEBSERVICE_DATA", sFile + "PROCESSID", "", false, "XX");
            string url = goMeta.LineRead("GLOBAL", "OTH_SISWEBSERVICE_DATA", "WEBSERVICEURL", "", false, "XX"); // "http://localhost:5637/Integration_%20Website/webservice.asmx"

            if (url == "")
                return "Record not Synced. WebService URL not specified.";
            if (sProcessID == "")
                return "Record not Synced. " + goData.GetFileLabel(sFile) + " ProcessID not specified.";

            StringBuilder data = new StringBuilder();
            string response = "";

            string sUserName = "SelltisIntegration";
            string sPassword = "qwe123%%67";

            data = data.Append("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
            data = data.Append("<soap12:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">");
            data = data.Append("<soap12:Header>");
            data = data.Append("<AuthHeader xmlns=\"http://tempuri.org/\">");
            data = data.Append("<Username>" + sUserName + "</Username>");
            data = data.Append("<Password>" + sPassword + "</Password>");
            data = data.Append("</AuthHeader>");
            data = data.Append("</soap12:Header>");
            data = data.Append("<soap12:Body>");
            data = data.Append("<TriggerIntegrationService xmlns=\"http://tempuri.org/\">");
            data = data.Append("<iProcessID>" + sProcessID + "</iProcessID>");
            data = data.Append("</TriggerIntegrationService>");
            data = data.Append("</soap12:Body>");
            data = data.Append("</soap12:Envelope>");

            // build request objects to pass the data/xml to the server
            byte[] buffer = Encoding.ASCII.GetBytes(data.ToString());
            HttpWebRequest request = WebRequest.Create(url) as HttpWebRequest;
            request.Method = "POST";
            request.ContentType = "text/xml; charset=utf-8";
            request.ContentLength = buffer.Length;
            Stream post = request.GetRequestStream();

            // post data and close connection
            post.Write(buffer, 0, buffer.Length);
            post.Close();

            // build response object
            HttpWebResponse WEbresponse = request.GetResponse() as HttpWebResponse;
            Stream responsedata = WEbresponse.GetResponseStream();
            StreamReader responsereader = new StreamReader(responsedata);
            response = responsereader.ReadToEnd();

            XmlDocument XMLDoc = new XmlDocument();

            XMLDoc.LoadXml(response);

            string Result = XMLDoc.LastChild.InnerText;

            if (Result.ToUpper() == "TRUE")
            {
            }
            else
                return "Record not Synced " + Constants.vbCrLf + Result;

            return "";
        }

        public string CallSISWebserviceDelete(string sFile, string sRecID = "")
        {
            if (goMeta.LineRead("GLOBAL", "OTH_SISWEBSERVICE_DATA", "ACTIVE", "", false, "XX") != "1")
                return "";

            string sProcessID = goMeta.LineRead("GLOBAL", "OTH_SISWEBSERVICE_DATA", "DELETEPROCESSID", "", false, "XX");
            string url = goMeta.LineRead("GLOBAL", "OTH_SISWEBSERVICE_DATA", "WEBSERVICEURL", "", false, "XX"); // "http://localhost:5637/Integration_%20Website/webservice.asmx"

            if (url == "")
                return "Record not Synced. WebService URL not specified.";
            if (sProcessID == "")
                return "Record not Synced. Delete ProcessID not specified.";

            StringBuilder data = new StringBuilder();
            string response = "";

            string sUserName = "SelltisIntegration";
            string sPassword = "qwe123%%67";

            data = data.Append("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
            data = data.Append("<soap12:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">");
            data = data.Append("<soap12:Header>");
            data = data.Append("<AuthHeader xmlns=\"http://tempuri.org/\">");
            data = data.Append("<Username>" + sUserName + "</Username>");
            data = data.Append("<Password>" + sPassword + "</Password>");
            data = data.Append("</AuthHeader>");
            data = data.Append("</soap12:Header>");
            data = data.Append("<soap12:Body>");
            data = data.Append("<DeleteRecordbyID xmlns=\"http://tempuri.org/\">");
            data = data.Append("<iProcessID>" + sProcessID + "</iProcessID>");
            data = data.Append("<sSelltisFile>" + sFile + "</sSelltisFile>");
            data = data.Append("<sRecordID>" + sRecID + "</sRecordID>");
            data = data.Append("</DeleteRecordbyID>");
            data = data.Append("</soap12:Body>");
            data = data.Append("</soap12:Envelope>");

            // build request objects to pass the data/xml to the server
            byte[] buffer = Encoding.ASCII.GetBytes(data.ToString());
            HttpWebRequest request = WebRequest.Create(url) as HttpWebRequest;
            request.Method = "POST";
            request.ContentType = "text/xml; charset=utf-8";
            request.ContentLength = buffer.Length;
            Stream post = request.GetRequestStream();

            // post data and close connection
            post.Write(buffer, 0, buffer.Length);
            post.Close();

            // build response object
            HttpWebResponse WEbresponse = request.GetResponse() as HttpWebResponse;
            Stream responsedata = WEbresponse.GetResponseStream();
            StreamReader responsereader = new StreamReader(responsedata);
            response = responsereader.ReadToEnd();

            XmlDocument XMLDoc = new XmlDocument();

            XMLDoc.LoadXml(response);

            string Result = XMLDoc.LastChild.InnerText;

            if (Result.ToUpper() == "TRUE")
            {
            }
            else
                return "Record not Deleted " + Constants.vbCrLf + Result;

            return "";
        }


        public bool CN_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            if (Convert.ToString(doRS.GetFieldVal("TXT_CONTACTNO", 2)) == "")
            {
                string sCustNo = "S" + CN_GenerateSequentialCustomerNo();
                doRS.SetFieldVal("TXT_CONTACTNO", sCustNo);
            }

            par_doCallingObject = doRS;
            return true;
        }


        public bool CN_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // VS ********
            string sAddr1 = doRS.GetFieldVal("TXT_ADDRBUSINESS1").ToString();
            string sAddr2 = doRS.GetFieldVal("TXT_ADDRBUSINESS2").ToString();
            string sAddr3 = doRS.GetFieldVal("TXT_ADDRBUSINESS3").ToString();
            string sAddrMail = "";

            if (sAddr1 != "")
            {
                if (sAddr2 != "")
                {
                    sAddrMail = sAddr1 + Constants.vbCrLf + sAddr2;

                }
                else
                {
                    sAddrMail = sAddr1;

                }
            }
            else
            {
                sAddrMail = sAddr2;

            }

            sAddrMail = sAddrMail + Constants.vbCrLf + sAddr3;
            sAddrMail = sAddrMail.Trim();

            doRS.SetFieldVal("TXT_ADDRBUSINESS", sAddrMail);

            doRS.SetFieldVal("TXT_STATEBUSINESS", doRS.GetFieldVal("LNK_STATEBUSINESS_SA%%TXT_CODE"));
            doRS.SetFieldVal("TXT_COUNTRYBUSINESS", doRS.GetFieldVal("LNK_RELATED_CT%%TXT_COUNTRYCODE"));


            par_doCallingObject = doRS;
            return true;
        }

        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // SGR 25102016
            Form doForm = (Form)par_doCallingObject;
            // doForm.SetControlState("TXT_CUSTNO", 4)
            if (doForm.GetMode() == "CREATION")
            {
                string sCustNo = "S" + CO_GenerateSequentialCustomerNo();
                doForm.doRS.SetFieldVal("TXT_CUSTNO", sCustNo);
            }

            // VS ******** TKT#1305 : Make "TXT_CURRENTANDPOTENTIAL" Field GrayedOut as it is being Calculated.
            doForm.SetControlState("TXT_CURRENTANDPOTENTIAL", 4);

            // SKO ******** TKT1322: disable Merged
            doForm.SetControlState("CHK_Merged", 4);

            // SGR 1326 ********
            // VS ******** : Allow user to edit CustNo if he has permissions
            clRowSet doRSUS = new clRowSet("US", 3, "GID_ID='" + goP.GetMe("ID") + "'", "", "CHK_EDITTERMSONCO,CHK_EDITCUSTNOONCO,CHK_EditAccountNotesOnCo");
            if (doRSUS.GetFirst() == 1)
            {
                if (Convert.ToInt32(doRSUS.GetFieldVal("CHK_EDITTERMSONCO", 2)) == 0)
                {
                    doForm.SetControlState("LNK_HAS_TR", 4);
                }
                if (Convert.ToInt32(doRSUS.GetFieldVal("CHK_EDITCUSTNOONCO", 2)) == 0)
                {
                    doForm.SetControlState("TXT_CUSTNO", 4);
                }
                if (Convert.ToInt32(doRSUS.GetFieldVal("CHK_EditAccountNotesOnCo", 2)) == 0)
                {
                    doForm.SetControlState("MMO_AccountNotes", 4);// NC Tckt #1458 Giving permissions to AccountNotes as terms
                }
            }


            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS ******** TKT#1305 : Calculate "TXT_CURRENTANDPOTENTIAL" by Appending MLS_CurrentVolume & MLS_PotentialVolume.
            Form doForm = (Form)par_doCallingObject;
            string currentandpotential;
            currentandpotential = Strings.Left(doForm.doRS.GetFieldVal("MLS_CurrentVolume", 1).ToString(), 1) + Strings.Left(doForm.doRS.GetFieldVal("MLS_PotentialVolume", 1).ToString(), 1);
            doForm.doRS.SetFieldVal("TXT_CURRENTANDPOTENTIAL", currentandpotential);

            // VS ******** : Check if CustNo already ecists
            if (doForm.doRS.GetFieldVal("TXT_CUSTNO").ToString().Trim() != "")
            {
                clRowSet doCORS = new clRowSet("CO", clC.SELL_READONLY, "TXT_CUSTNO='" + doForm.doRS.GetFieldVal("TXT_CUSTNO") + "' AND GID_ID<>'" + doForm.doRS.GetFieldVal("GID_ID") + "'");
                if (doCORS.GetFirst() == 1)
                {
                    doForm.MessageBox("Cust No already exists for Company '" + doCORS.GetFieldVal("SYS_NAME") + "', please enter a different number.");
                    return false;
                }
            }

            // SKO ******** TKT1322: Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO ******** TKT1322: Merge Functionality - run at end of CO_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CO_Merge")) != "1")
                    {
                        // Don't allow merge of company to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID"))
                        {
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CO", "MergeFail");

                        }
                        else
                        {
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CO", "Merge");

                        }
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }


        public bool CO_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // VS ********
            string sAddr1 = doRS.GetFieldVal("TXT_ADDRESSMAILING1").ToString();
            string sAddr2 = doRS.GetFieldVal("TXT_ADDRESSMAILING2").ToString();
            string sAddrMail = "";

            if (sAddr1 != "")
            {
                if (sAddr2 != "")
                {
                    sAddrMail = sAddr1 + Constants.vbCrLf + sAddr2;

                }
                else
                {
                    sAddrMail = sAddr1;

                }
            }
            else
                sAddrMail = sAddr2;

            doRS.SetFieldVal("TXT_ADDRMAILING", sAddrMail);

            doRS.SetFieldVal("TXT_STATEMAILING", doRS.GetFieldVal("LNK_STATEMAILING_SA%%TXT_CODE"));
            doRS.SetFieldVal("TXT_COUNTRYMAILING", doRS.GetFieldVal("LNK_RELATED_CT%%TXT_COUNTRYCODE"));


            par_doCallingObject = doRS;
            return true;
        }

        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // VS ******** TKT#1305 : Profile Matrix
            string sCurVol = "";
            string sPotVol = "";
            int iCurCount = Convert.ToInt32(doRS.GetLinkCount("LNK_CurrentProduct_PD"));
            int iPotCount = Convert.ToInt32(doRS.GetLinkCount("LNK_PotentialProduct_PD"));

            // ---------TLD 7/24/2012 Target Account Matrix Profiling
            // Copy LNK_Current_PD to LNK_Potential_PD
            // doRS.SetFieldVal("LNK_PotentialProduct_PD", doRS.GetFieldVal("LNK_CurrentProduct_PD"))

            // Record total selections from LNK_Current_PD and LNK_Potential_PD
            doRS.SetFieldVal("INT_CURCOUNT", iCurCount, 2);
            doRS.SetFieldVal("INT_POTCOUNT", iPotCount, 2);

            // Calculate Potential Percentage & Potential Portfolio
            clRowSet doPDRS = new clRowSet("PD", 3, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "GID_ID");
            int iPDCount = Convert.ToInt32(doPDRS.Count());
            if (iPDCount != 0)
            {
                if (iPotCount != 0)
                    doRS.SetFieldVal("SR__POTENTIALPERC", (iCurCount / (double)iPotCount) * 100, 2);
                doRS.SetFieldVal("SR__POTENTIALPORTFOLIO", (iPotCount / (double)iPDCount) * 100, 2);

                doRS.SetFieldVal("SR__ProdLinePot", (iPotCount - iCurCount), 2);
                sCurVol = (doRS.GetFieldVal("MLS_CURRENTVOLUME") == null) ? "" : Strings.Left(doRS.GetFieldVal("MLS_CURRENTVOLUME").ToString(), 1);
                sPotVol = (doRS.GetFieldVal("MLS_POTENTIALVOLUME") == null) ? "" : Strings.Left(doRS.GetFieldVal("MLS_POTENTIALVOLUME").ToString(), 1);
                if (sCurVol == "<")
                    sCurVol = "Z";
                if (sPotVol == "<")
                    sPotVol = "Z";

                // set field to cur & pot
                doRS.SetFieldVal("TXT_CURANDPOT", sCurVol + sPotVol);
            }

            // ---------- Target Account -----------------
            // Set Product Potential Quadrant
            double rTotalPortfolio = (doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2) == null) ? 0 : Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2));
            double rPotentialProduct = (doRS.GetFieldVal("SR__POTENTIALPERC", 2) == null) ? 0 : Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPERC", 2));

            if (rTotalPortfolio >= 51 & rTotalPortfolio <= 100)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                {
                    // Set to 1
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "1");
                }

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                {
                    // Set to 3
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "3");
                }
            }

            if (rTotalPortfolio >= 0 & rTotalPortfolio <= 50)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                    // Set to 2
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "2");

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                    // Set to 4
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "4");
            }

            // Because COs are updated nightly to set custom
            // date fields, need to write to custom mod time and mod by fields
            // AutoCOUpdate does NOT run recordonsave
            doRS.SetFieldVal("TXT_CusModBy", goP.GetMe("CODE"));
            doRS.SetFieldVal("DTT_CusModTime", "Today|Now");
            // ---------TLD 7/24/2012 End Target Account Matrix Profiling

            // CS 4/27/2015 Check for the # of linked, Open Opportunities and update CO.LI__OpenOpCount field. 
            // Only do this if have not already done it in OP_RecordOnSave when adding a new linked Op.
            if (Convert.ToString(doRS.oVar.GetVar("UpdatedOpCount")) != "1")
            {
                clRowSet doRSOp = new clRowSet("OP", 3, "LNK_FOR_CO=" + doRS.GetCurrentRecID() + " and MLS_STATUS=0");
                if (doRSOp.GetFirst() == 1)
                    doRS.SetFieldVal("LI__OpenOpCount", doRSOp.Count(), 2);
            }

            // NC TCKT 1523 ********
            if (doRS.iRSType == clC.SELL_ADD)
            {
                if (doRS.GetFieldVal("TXT_CUSTNO").ToString().Trim() == "")
                {
                    string sCustNo = "S" + CO_GenerateSequentialCustomerNo();
                    doRS.SetFieldVal("TXT_CUSTNO", sCustNo);
                }
            }


            par_doCallingObject = doRS;
            return true;
        }

        public bool CO_FormAfterSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // Invoke SIS
            string sOutput = CallSISWebservice("CO");

            if (sOutput != "")
            {
                // 'goErr.SetWarning(30029, sProc, sOutput, "", "", "", "", "", "", "", "", "", "")
                goLog.Log(sProc, "Selltis to M2k sync failed" + doForm.doRS.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".", 1, true, true);
            }
            else
            {
                goLog.Log(sProc, "Selltis to M2k company sync for '" + doForm.doRS.GetFieldVal("TXT_CompanyName") + "' completed successfully.", 1, true, true);
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // VS 12132016
            doRS.oVar.SetVar("DeleteCustNO", doRS.GetFieldVal("TXT_CustNO"));

            par_doCallingObject = doRS;
            return true;
        }

        public bool CO_RecordAfterDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // VS 12132016
            string sOutput = CallSISWebserviceDelete("CO", doRS.oVar.GetVar("DeleteCustNO").ToString()); // doRS.GetFieldVal("TXT_CustNO"))

            if (sOutput != "")
                goErr.SetWarning(30029, sProc, sOutput, "", "", "", "", "", "", "", "", "", "");

            par_doCallingObject = doRS;
            return true;
        }

        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);

            doForm.oVar.SetVar("vBlanketOrderMonths", doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS"));

            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // VS ******** TKT#1305 : Profile Matrix
            int iStage = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STAGE", 2));
            goP.SetVar("OppStageOnLoad", iStage);

            doForm.SetControlState("MLS_MAINBLANKET", 4);

            ClearLineFields(doForm);

            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // VS ******** TKT#1305 : Profile Matrix
            string sDateStamp = "";
            int iStage = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STAGE", 2));

            // CS 11/19/2014 Update stage dates if stage changed.
            if (goP.GetVar("OppStageOnLoad") != goTR.NumToString(iStage))
            {
                // Check what the stage is and update the date field with today/now
                switch (iStage)
                {
                    case 10 // Lead
                   :
                        {
                            doForm.doRS.SetFieldVal("DTT_LeadDate", "Today|Now");
                            break;
                        }

                    case 20 // Opp
             :
                        {
                            doForm.doRS.SetFieldVal("DTT_OppDate", "Today|Now");
                            break;
                        }

                    case 30 // Quote
             :
                        {
                            doForm.doRS.SetFieldVal("DTT_QuoteDate", "Today|Now");
                            break;
                        }
                }
            }

            doForm.oVar.SetVar("vSaveType", doForm.MessageBoxFormMode);

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_MAINBLANKET", 2)) == 0)
            {
                if (!doForm.doRS.IsLinkEmpty("LNK_BLANKETOPPS_OP"))
                {
                    if (Convert.ToString(doForm.oVar.GetVar("bBlanketOrderMonthChangeAlert")) != "1")
                    {
                        if (doForm.oVar.GetVar("vBlanketOrderMonths") != doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS"))
                        {
                            doForm.MessageBox("Blanket Order Months has been changed. Do you want to recreate Blanket Orders?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO" + Constants.vbCrLf + "(#Months will be reverted)", "CANCEL", null/* Conversion error: Set to default value for this argument */, "OP_BLANKETORDERS_MONTHSCHANGED");
                            return false;
                        }
                    }
                }
            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS ******** TKT#1305 : Profile Matrix
            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sField = "";
            DateTime dDate = default(DateTime);
            //DateTime dDate;
            // CS 11/19/2014 Update linked For Company.Last date or Involves Contact Last date if the opp stage changed
            // If doRS.GetInfo("TYPE") = "2" Then 'new op
            if (doRS.GetLinkCount("LNK_FOR_CO") > 0 | doRS.GetLinkCount("LNK_INVOLVES_CN") > 0)
            {
                // Determine what stage the Op on disk is compared to the Op we are saving. 
                // This will determine which Opp date field to set in the Company/Contact.
                clRowSet doRSOP = new clRowSet("OP", 3, "GID_ID='" + doRS.GetCurrentRecID() + "'");
                if (doRSOP.GetFirst() == 1)
                {
                    int iSavedStage = Convert.ToInt32(doRSOP.GetFieldVal("MLS_STAGE", 2));
                    if (iSavedStage != Convert.ToInt32(doRS.GetFieldVal("MLS_STAGE", 2)))
                    {
                        switch (doRS.GetFieldVal("MLS_STAGE", 2))
                        {
                            case 10 // Lead
                           :
                                {
                                    sField = "DTT_LASTLEAD"; // field to update in company/contact
                                    dDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_LeadDate", 2).ToString()); // value to update company/contact
                                    break;
                                }

                            case 20 // Opp
                     :
                                {
                                    sField = "DTT_LASTOP";
                                    dDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_OppDate", 2));
                                    break;
                                }

                            case 30 // Quote
                     :
                                {
                                    sField = "DTT_LASTQT";
                                    dDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_QuoteDate", 2));
                                    break;
                                }

                            default:
                                {
                                    // date did not change; do nothing
                                    sField = "";
                                    break;
                                }
                        }
                    }
                }
                else
                    // This is a new Opp; only set dates if not make selection
                    switch (doRS.GetFieldVal("MLS_STAGE", 2))
                    {
                        case 10 // Lead
                       :
                            {
                                sField = "DTT_LASTLEAD"; // field to update in company/contact
                                dDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_LeadDate", 2)); // value to update company/contact
                                break;
                            }

                        case 20 // Opp
                 :
                            {
                                sField = "DTT_LASTOP";
                                dDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_OppDate", 2));
                                break;
                            }

                        case 30 // Quote
                 :
                            {
                                sField = "DTT_LASTQT";
                                dDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_QuoteDate", 2));
                                break;
                            }

                        default:
                            {
                                // date did not change; do nothing
                                sField = "";
                                break;
                            }
                    }

                if (sField != "")
                {
                    // Update Companies
                    clArray doCompanies = new clArray();
                    doCompanies = (clArray)doRS.GetFieldVal("LNK_FOR_CO", 2);
                    // Bipass reconsave and validation to speed up AC save
                    for (int i = 1; i <= doCompanies.GetDimension(); i++)
                    {
                        clRowSet doRSCompany = new clRowSet("CO", 1, "GID_ID='" + doCompanies.GetItem(i) + "'", "", sField, -1, "", "", "", "", "", true, true);
                        if (doRSCompany.GetFirst() == 1)
                        {

                            doRSCompany.SetFieldVal(sField, dDate, 2);
                            if (doRSCompany.Commit() == 0)
                                goLog.Log(sProc, "CO update of last date field failed for CO " + doRSCompany.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".", -1, false, true);
                        }
                        doRSCompany = null/* TODO Change to default(_) if this is not a reference type */;
                    }

                    // Update Contacts
                    clArray doContacts = new clArray();
                    doContacts = (clArray)doRS.GetFieldVal("LNK_INVOLVES_CN", 2);
                    // Bipass reconsave and validation to speed up AC save
                    for (int i = 1; i <= doContacts.GetDimension(); i++)
                    {
                        clRowSet doRSContact = new clRowSet("CN", 1, "GID_ID='" + doContacts.GetItem(i) + "'", null, sField, -1, null, null, null, null, null, true, true);
                        if (doRSContact.GetFirst() == 1)
                        {
                            doRSContact.SetFieldVal(sField, dDate, 2);
                            if (doRSContact.Commit() == 0)
                                goLog.Log(sProc, "CN update of last date field failed for CN " + doRSContact.GetFieldVal("TXT_NameLast") + " with error " + goErr.GetLastError("NUMBER") + ".", -1, false, true);
                        }
                        doRSContact = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                }
            }

            int li_Count = 0;
            // CS 4/27/2015 If this is a new, Open Opp, get linked 'For Company' and update the Co.LI__LastOpCount
            if (Convert.ToInt32(doRS.GetInfo("TYPE")) == 2 & (Convert.ToInt32(doRS.GetFieldVal("MLS_STATUS", 2)) == 0))
            {
                if (doRS.GetLinkCount("LNK_FOR_CO") > 0)
                {
                    clRowSet doRSCO = new clRowSet("CO", 1, "GID_ID=" + doRS.GetFieldVal("LNK_FOR_CO") + "");
                    if (doRSCO.GetFirst() == 1)
                    {
                        li_Count = Convert.ToInt32(doRSCO.GetFieldVal("LI__OPENOPCOUNT", 2));
                        li_Count = li_Count + 1;
                        doRSCO.SetFieldVal("LI__OPENOPCOUNT", li_Count, 2);
                        doRSCO.oVar.SetVar("UpdatedOpCount", "1");
                        if (doRSCO.Commit() == 0)
                            goLog.Log(sProc, "CO update of # linked Opps failed for " + doRSCO.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".", -1, false, true);
                        doRSCO.oVar.SetVar("UpdatedOpCount", "");
                    }
                }
            }
            else
                // Editing an Op; need to update op count in linked company in case status is no longer open.
                if (doRS.GetLinkCount("LNK_FOR_CO") > 0)
            {
                clRowSet doRSCO = new clRowSet("CO", 1, "GID_ID=" + doRS.GetFieldVal("LNK_FOR_CO") + "");
                if (doRSCO.GetFirst() == 1)
                {
                    // Check # of linked Opps and update LI__OpenOpCount
                    // Loop through Opps linked to Company and check if status is 0
                    clRowSet doRSOP = new clRowSet("OP", 3, "LNK_FOR_CO=" + doRSCO.GetFieldVal("GID_ID") + " AND MLS_STATUS=0");
                    if (doRSOP.GetFirst() == 1)
                        doRSCO.SetFieldVal("LI__OPENOPCOUNT", doRSOP.Count(), 2);
                    else
                        // no linked open opps
                        doRSCO.SetFieldVal("LI__OPENOPCOUNT", 0, 2);
                    if (doRSCO.Commit() == 0)
                        goLog.Log(sProc, "CO update of # linked Opps failed for " + doRSCO.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".", -1, true, true);
                }
            }
            // SGR TKT:# 1389 29122016
            if (Convert.ToInt32(doRS.GetFieldVal("MLS_MAINBLANKET", 2)) == 0)
            {
                clArray doOpp = new clArray();
                doOpp = (clArray)doRS.GetFieldVal("LNK_BLANKETOPPS_OP", 2);
                for (int i = 1; i <= doOpp.GetDimension(); i++)
                {
                    clRowSet doOPRS = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + doOpp.GetItem(i) + "'", "", "MMO_JOURNAL,MMO_NEXTACTION", -1, "", "", "", "", "", true, true);
                    if (doOPRS.GetFirst() == 1)
                    {
                        doOPRS.SetFieldVal("MMO_JOURNAL", doRS.GetFieldVal("MMO_JOURNAL", 2), 2);
                        doOPRS.Commit();
                    }
                }
            }

            par_doCallingObject = doRS;
            return true;
        }


        public bool OP_FormControlOnChange_BTN_CALCBLANKETORDER_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS ******** TKT#1305 : Profile Matrix
            Form doForm = (Form)par_doCallingObject;

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_MAINBLANKET", 2)) == 10)
            {
                doForm.MessageBox("Calculation can only be done on the main OPP.");
                return false;
            }

            if (doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1) == "")
            {
                doForm.MessageBox("Please enter Blanket Order Start Date");
                doForm.MoveToField("DTE_EXPCLOSEDATE");
                return false;
            }
            if (doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS") == "")
            {
                // doForm.MessageBox("Please select a number for Blanket Order Months")
                doForm.MoveToField("MLS_BLANKETORDERMONTHS");
                doForm.MessageBox("Please select blank order months");
                return false;
            }

            // If Not IsNumeric(doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS", 2)) Then
            // doForm.MessageBox("Please select a number for Blanket Order Months")
            // doForm.MoveToField("MLS_BLANKETORDERMONTHS")
            // Return False
            // End If

            DateTime dtStartDate = Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2));
            DateTime dtEndDate;
            int iBlanketMonths = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS"));
            double dOppValue = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_VALUE", 2));
            double dBlanketPerMonth = 0;

            // dtEndDate = DateTime.DateAdd(DateInterval.Day, -1, DateTime.DateAdd(DateInterval.Month, iBlanketMonths, dtStartDate));
            dtEndDate = dtStartDate.AddMonths(iBlanketMonths);
            dtEndDate = dtEndDate.AddDays(-1);
            dBlanketPerMonth = dOppValue / iBlanketMonths;

            doForm.doRS.SetFieldVal("DTE_BLANKETENDDATE", dtEndDate);
            doForm.doRS.SetFieldVal("CUR_BLANKETPERMONTH", dBlanketPerMonth);

            par_doCallingObject = doForm;

            return true;
        }

        public bool OP_FormControlOnChange_BTN_CREATEBLANKETORDERS_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS ******** TKT#1305 : Profile Matrix
            Form doForm = (Form)par_doCallingObject;

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_MAINBLANKET", 2)) == 10)
            {
                doForm.MessageBox("Blanket Opportunities can only be created for a Main Opp.");
                return false;
            }

            if (doForm.GetMode() == "CREATION")
            {
                doForm.MessageBox("Please save this Main Opp before creating Blanket Opps.");
                return false;
            }

            if ((doForm.doRS.GetFieldVal("MLS_MAINBLANKET", 1)) != "BLANKET")
            {
                doForm.MessageBox("Blanket Opps are already created for Main Opps.");
                doForm.MoveToField("MLS_MAINBLANKET");
                return false;
            }

            if (!doForm.doRS.IsLinkEmpty("LNK_BLANKETOPPS_OP"))
            {
                doForm.MessageBox("Blanket Opps are already created for this Opp");
                return false;
            }

            if (doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1) == "")
            {
                doForm.MessageBox("Please enter Blanket Order Start Date");
                doForm.MoveToField("DTE_EXPCLOSEDATE");
                return false;
            }

            // If Not IsNumeric(doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS", 2)) Then
            // doForm.MessageBox("Please select a number for Blanket Order Months")
            // doForm.MoveToField("MLS_BLANKETORDERMONTHS")
            // Return False
            // End If
            if (doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS") == "")
            {
                // doForm.MessageBox("Please select a number for Blanket Order Months")
                doForm.MoveToField("MLS_BLANKETORDERMONTHS");
                doForm.MessageBox("Please select blank order months");
                return false;
            }


            doForm.MessageBox("You are about to create " + doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS") + " blanket opportunities. Proceed?", clC.SELL_MB_YESNO, "", "", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "OP_FORMCONTROLONCHANGE_BTN_CREATEBLANKETORDERS");

            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormControlOnChange_BTN_UPDATEBLANKETORDERS_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS ******** TKT#1305 : Profile Matrix
            Form doForm = (Form)par_doCallingObject;

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_MAINBLANKET", 2)) == 10)
            {
                doForm.MessageBox("Please update using the Main Opportunity.");
                return false;
            }

            if (doForm.GetMode() == "CREATION")
            {
                doForm.MessageBox("Please save this Main Opp before creating Blanket Opps.");
                return false;
            }

            if (doForm.doRS.GetFieldVal("MLS_MAINBLANKET", 1) != "BLANKET")
            {
                doForm.MessageBox("Blanket Opps are already created for Main Opps.");
                doForm.MoveToField("MLS_MAINBLANKET");
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_BLANKETOPPS_OP"))
            {
                doForm.MessageBox("Blanket Opps are not created for this Opp");
                return false;
            }

            // SGR TKT#:1389 03012016
            if (doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS") == "")
            {
                doForm.MessageBox("Please select blank order months");
                return false;
            }

            if (doForm.oVar.GetVar("vBlanketOrderMonths") != doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS"))
            {
                if (doForm.oVar.GetVar("bBlanketOrderMonthChangeAlert") != "1")
                {
                    doForm.MessageBox("Blanket Order Months has been changed. Do you want to recreate Blanket Orders?", clC.SELL_MB_YESNO, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "OP_BLANKETORDERS_MONTHSCHANGED");
                    return false;
                }
            }
            else
            {
                doForm.MessageBox("You are sure you want to Update all the Blanket opportunities?", clC.SELL_MB_YESNO, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "OP_FORMCONTROLONCHANGE_BTN_UPDATEBLANKETORDERS");
                return true;
            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool OPP_CREATEBLANKETORDERS_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS ******** TKT#1305 : Profile Matrix
            Form doForm = (Form)par_doCallingObject;
            // SGR TKT:#1389
            // If Not IsNumeric(doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS", 2)) Then
            // doForm.MessageBox("Please select a number for Blanket Order Months")
            // doForm.MoveToField("MLS_BLANKETORDERMONTHS")
            // Return False
            // End If
            if (doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS") == "")
            {
                // doForm.MessageBox("Please select a number for Blanket Order Months")
                doForm.MoveToField("MLS_BLANKETORDERMONTHS");
                doForm.MessageBox("Please select blank order months");
                return false;
            }

            DateTime dtCloseDate = Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2));
            bool bCloseDateEmpty = doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE") == "" ? true : false; //IIf(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE") == "", true, false);
            Int16 iBlanketMonths = Convert.ToInt16(doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS"));
            double dOppValue = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_VALUE", 2));
            double dOppQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));
            double dBlanketPerMonth = 0;

            dBlanketPerMonth = dOppValue / iBlanketMonths;

            doForm.doRS.ClearLinkAll("LNK_BLANKETOPPS_OP");

            clRowSet doOPRS;
            for (int i = 0; i <= iBlanketMonths - 1; i++)
            {
                doOPRS = new clRowSet("OP", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_OP:BLANKETORDER", doForm.RecordID, "", true, true);
                // dtStartDate = DateAdd(DateInterval.Month, i, dtStartDate)
                // dtCloseDate = DateAdd(DateInterval.Month, i, dtCloseDate)
                if (bCloseDateEmpty)
                {
                    doOPRS.SetFieldVal("DTE_EXPCLOSEDATE", "");
                }

                else
                {
                    doOPRS.SetFieldVal("DTE_EXPCLOSEDATE", dtCloseDate.AddMonths(i), 2);
                }

                doOPRS.SetFieldVal("CUR_VALUE", dBlanketPerMonth, 2);
                doOPRS.SetFieldVal("CUR_BLANKETPERMONTH", dBlanketPerMonth, 2);
                doOPRS.SetFieldVal("SR__QTY", dOppQty / iBlanketMonths, 2);
                // Check report on Blanket Opp
                doOPRS.SetFieldVal("CHK_REPORT", "CHECKED");
                if (doOPRS.Commit() == 1)
                    doForm.doRS.SetFieldVal("LNK_BLANKETOPPS_OP", doOPRS.GetFieldVal("GID_ID"));
            }

            doForm.oVar.SetVar("vBlanketOrderMonths", doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS"));

            par_doCallingObject = doForm;
            return true;
        }

        public bool OPP_UPDATEBLANKETORDERS_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS ******** TKT#1305 : Profile Matrix
            Form doForm = (Form)par_doCallingObject;

            DateTime dtStartDate = Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_TIME", 2));
            DateTime dtCloseDate = Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2));
            Int16 iBlanketMonths = Convert.ToInt16(doForm.doRS.GetFieldVal("MLS_BLANKETORDERMONTHS"));
            double dOppValue = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_VALUE", 2));
            double dOppQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));
            double dBlanketPerMonth = 0;

            dBlanketPerMonth = dOppValue / iBlanketMonths;

            clRowSet doOPRS = new clRowSet("OP", clC.SELL_EDIT, "LNK_OPPSFORBLANKET_OP='" + doForm.RecordID + "'", "", "", 1, "", "", "", "", "", true, true);
            if (doOPRS.GetFirst() == 1)
            {
                do
                {
                    doOPRS.SetFieldVal("DTT_TIME", doForm.doRS.GetFieldVal("DTT_TIME", 2), 2);
                    doOPRS.SetFieldVal("MLS_STATUS", doForm.doRS.GetFieldVal("MLS_STATUS", 2), 2);
                    doOPRS.SetFieldVal("MLS_REASONWONLOST", doForm.doRS.GetFieldVal("MLS_REASONWONLOST", 2), 2);
                    doOPRS.ClearLinkAll("LNK_FOR_PD");
                    doOPRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_FOR_PD", 2), 2);
                    doOPRS.ClearLinkAll("LNK_RELATED_MO");
                    doOPRS.SetFieldVal("LNK_RELATED_MO", doForm.doRS.GetFieldVal("LNK_RELATED_MO", 2), 2);
                    doOPRS.SetFieldVal("TXT_DESCRIPTION", doForm.doRS.GetFieldVal("TXT_DESCRIPTION", 2), 2);
                    doOPRS.ClearLinkAll("LNK_FOR_CO");
                    doOPRS.SetFieldVal("LNK_FOR_CO", doForm.doRS.GetFieldVal("LNK_FOR_CO", 2), 2);
                    doOPRS.ClearLinkAll("LNK_ORIGINATEDBY_CN");
                    doOPRS.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN", 2), 2);
                    doOPRS.SetFieldVal("MMO_JOURNAL", doForm.doRS.GetFieldVal("MMO_JOURNAL", 2), 2);
                    doOPRS.SetFieldVal("MLS_STAGE", doForm.doRS.GetFieldVal("MLS_STAGE", 2), 2);
                    doOPRS.SetFieldVal("MLS_PRIORITY", doForm.doRS.GetFieldVal("MLS_PRIORITY", 2), 2);
                    doOPRS.ClearLinkAll("LNK_CREDITEDTO_US");
                    doOPRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US", 2), 2);
                    doOPRS.SetFieldVal("CUR_VALUE", dBlanketPerMonth, 2);
                    doOPRS.SetFieldVal("CUR_BLANKETPERMONTH", dBlanketPerMonth, 2);
                    doOPRS.SetFieldVal("SR__QTY", dOppQty / iBlanketMonths, 2);
                    // Check report on Blanket Opp
                    doOPRS.SetFieldVal("CHK_REPORT", "CHECKED");

                    // SGR TKT:# 1389 29122016
                    doOPRS.SetFieldVal("SI__PROBABILITY", doForm.doRS.GetFieldVal("SI__PROBABILITY", 2), 2);
                    doOPRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2), 2);
                    doOPRS.SetFieldVal("MLS_STATUS", doForm.doRS.GetFieldVal("MLS_STATUS", 2), 2);
                    doOPRS.SetFieldVal("DTE_NEXTACTIONDATE", doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 2), 2);
                    doOPRS.SetFieldVal("MMO_NEXTACTION", doForm.doRS.GetFieldVal("MMO_NEXTACTION", 2), 2);
                    doOPRS.SetFieldVal("MMO_JOURNAL", doForm.doRS.GetFieldVal("MMO_JOURNAL", 2), 2);
                    doOPRS.Commit();
                    if (doOPRS.GetNext() == 0)
                        break;
                }
                while (true);
            }

            doForm.doRS.UpdateLinkState("LNK_BLANKETOPPS_OP");
            doForm.RefreshLinkNames("LNK_BLANKETOPPS_OP");

            par_doCallingObject = doForm;
            return true;
        }


        public bool OPP_RECREATEBLANKETORDERS_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            clRowSet doOPRS = new clRowSet("OP", clC.SELL_EDIT, "LNK_OPPSFORBLANKET_OP='" + doForm.RecordID + "'", "", "", -1, "", "", "", "", "", true, true);
            if (doOPRS.GetFirst() == 1)
                doOPRS.DeleteAll();

            scriptManager.RunScript("OPP_CREATEBLANKETORDERS", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray);
            par_doCallingObject = doForm;
            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }
            par_doCallingObject = doForm;
            par_bRunNext = false;
            return true;

        }
        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_MO%%GID_ID"));

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_RELATED_PD,LNK_RELATED_MO,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("LNK_RELATED_MO", MO_Gid);
            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsOL.Commit() != 1)
            {
                return false;
            }

            iLineCount = iLineCount + 1;

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }
        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.oVar.SetVar("OppID", doRS.GetFieldVal("LNK_IN_OP"));

            par_doCallingObject = doRS;

            return true;
        }

        public bool OL_RecordAfterDelete_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_s2 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sID = doRS.oVar.GetVar("OppID").ToString();

            if (!string.IsNullOrEmpty(sID))
            {
                clRowSet doOPLines = default(clRowSet);

                //Create a rowset of quote lines linked to the deleted quote.
                doOPLines = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo,Gid_id", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    StringBuilder sbQuery = new StringBuilder();
                    for (int i = 1; i <= doOPLines.Count(); i++)
                    {
                        double iLineno = Convert.ToDouble(doOPLines.GetFieldVal("SR__LineNo"));
                        string sGid_id = Convert.ToString(doOPLines.GetFieldVal("Gid_ID"));
                        string sTemp = "#" + goTR.Pad(i.ToString(), 6, " ", "L", true, "R");
                        sbQuery.AppendLine("Update OL Set SR__LineNo='" + i.ToString() + "',sys_name='" + sTemp + "' where Gid_id='" + sGid_id + "' AND SR__LineNo = '" + iLineno.ToString() + "'");

                        if (doOPLines.GetNext() == 0)
                        {
                            break;
                        }

                    }
                    if (!string.IsNullOrEmpty(sbQuery.ToString()))
                    {
                        bool bretval = goData.RunSQLQuery(sbQuery.ToString());
                    }

                }

                doOPLines = null;

            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);

            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    iLineCount= doOPLines.Count();
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }

        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 4/16/2013 Changed to _Pre
            // TLD 4/24/2009 
            // When Complete All checked on Corr In Box - My
            // par_callingObject is clDesktop, so throws an error
            // so changed per Christy
            // Dim doForm As clForm = par_doCallingObject
            Form doForm = (Form)par_doCallingObject;


            switch (Strings.UCase(par_s5))
            {
                case "MERGE":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // run merge script, continue save
                                    scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray);
                                    break;
                                }

                            case "NO":
                                {
                                    // Clear merged to co linkbox, continue save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    break;
                                }

                            case "CANCEL":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "MERGEFAIL":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "OP_FORMCONTROLONCHANGE_BTN_CREATEBLANKETORDERS":
                    {
                        par_bRunNext = false;
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // Uncheck report on Main Opp
                                    doForm.doRS.SetFieldVal("CHK_REPORT", "UNCHECKED");

                                    if (doForm.Save(3, false, "OP_FormControlOnChange_BTN_CREATEBLANKETORDERS") == 0)
                                        return false;

                                    doForm.oVar.SetVar("bCreateBlanketOrderAlert", "1");

                                    scriptManager.RunScript("OPP_CREATEBLANKETORDERS", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray);
                                    break;
                                }

                            case "NO":
                                {
                                    // Do nothing; continue save
                                    return true;
                                }
                        }

                        break;
                    }

                case "OP_FORMCONTROLONCHANGE_BTN_UPDATEBLANKETORDERS":
                    {
                        par_bRunNext = false;
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {

                                    // Uncheck report on Main Opp
                                    doForm.doRS.SetFieldVal("CHK_REPORT", "UNCHECKED");

                                    if (doForm.Save(3, false, "OP_FormControlOnChange_BTN_UPDATEBLANKETORDERS") == 0)
                                        return false;

                                    scriptManager.RunScript("OPP_UPDATEBLANKETORDERS", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray);
                                    break;
                                }

                            case "NO":
                                {
                                    // Do nothing; continue save
                                    return true;
                                }
                        }

                        break;
                    }

                case "OP_BLANKETORDERS_MONTHSCHANGED":
                    {
                        par_bRunNext = false;
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {

                                    // Uncheck report on Main Opp
                                    doForm.doRS.SetFieldVal("CHK_REPORT", "UNCHECKED");

                                    if (doForm.Save(3, false, "OP_FormControlOnChange_BTN_UPDATEBLANKETORDERS") == 0)
                                        return false;

                                    doForm.oVar.SetVar("bBlanketOrderMonthChangeAlert", "1");
                                    scriptManager.RunScript("OPP_RECREATEBLANKETORDERS", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray);
                                    break;
                                }

                            case "NO":
                                {
                                    // Revert Blanket Months, continue save
                                    doForm.doRS.SetFieldVal("MLS_BLANKETORDERMONTHS", doForm.oVar.GetVar("vBlanketOrderMonths"));
                                    break;
                                }

                            case "CANCEL":
                                {
                                    // Do Nothing
                                    return true;
                                }
                        }

                        if (doForm.oVar.GetVar("vSaveType") == "SAVEANDKEEPOPEN")
                        {
                            if (doForm.Save(5) == 0)
                                return false;
                        }
                        else if (doForm.oVar.GetVar("vSaveType") == "SAVE")
                        {
                            if (doForm.Save(0, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                                return false;
                        }

                        break;
                    }
            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool MergeRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // SKO ******** TKT1322: Added for merge
            par_bRunNext = false;

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName()) + "'", null, "**", -1, "", "", "", "", "", true, true, true, true, -1, "", true);
                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Microsoft.VisualBasic.Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField).ToString() == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    break;
                                }

                            case "MMO":
                                {
                                    // Append
                                    if (doRSMergeTo.GetFieldVal(sField) == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    else
                                        doRSMergeTo.SetFieldVal(sField, doRSMergeTo.GetFieldVal(sField) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + doRSMerge.GetFieldVal("SYS_Name") + " ==" + Constants.vbCrLf + doRSMerge.GetFieldVal(sField));
                                    break;
                                }

                            case "CHK":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }

                            case "MLS":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9).ToString());
                        if (sLinkType[4] == "NN" | Convert.ToInt32(sLinkType[1]) == 2)
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (doRSMergeTo.GetFieldVal(aLinks.GetItem(i)) == "")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    // Check Active if exists
                    if (goData.IsFieldValid(doRSMerge.GetFileName(), "CHK_ACTIVEFIELD") == true)
                        doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);

                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;
            par_doCallingObject = doRSMerge;
            return true;
        }

        public bool GenerateSysName_Pre(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            // MI 3/12/10 Added CASE "DF", modified Case Else to generate Name based on TXT_<FileName>Name field if exists.
            // CS 6/2/09 Modified QL case
            // CS 8/25/08 Case US: Added Title text
            // CS 8/20/08 Case CN: added Title Text
            // MI 11/14/07 Case CN: added TXT_ContactCode.
            // MI 10/15/07 Appended ' UTC' to all datetimes
            // MI 6/22/07 Changes to QT, OP, PR, PD, MO
            // MI 4/17/07 Added Phone Co to CN; Phone to CO ; removed padding from and to to 4 in MS; Status to OP, PR, QT, TD(?)
            // MI 3/9/07 Removed ellipsis from Co name in AC name.
            // MI 3/1/07 Added Contact, Company to AC Name.
            // MI 2/1/07 Added Date, Originator to Project name
            // MI 9/15/06 Updated QL, WT SYS_Name formats.
            // MI 7/25/06 Added raising error when field is not in the rowset.
            // MI 7/24/06 Mods. 
            // MI 7/20/06 Created in clScripts.
            // MI 7/20/06 Finished, tested, moved from clData to clScripts and renamed from GetCurrentRecordName to GetSysName.
            // MI 7/17/06 Started making this work in SellSQL.

            // AUTHOR: MI
            // PURPOSE:
            // Send back via the par_oReturn parameter the 'User Friendly' Name of the current record in par_oRowset.
            // This is to be called from each RecordOnSave script, but can be called
            // from any other code to generate a SYS_Name value. Do NOT set the name in 
            // the par_doCallingObject rowset or the script won't be usable simply for evaluating the returned
            // string.
            // IMPORTANT: Keep this "in sync" with clScripts.GetDefaultSort(), which is called from
            // clData.GetDefaultSort.
            // PARAMETERS:
            // par_doCallingObject: rowset object (for example, 'doRS'). The rowset must contain
            // all links and fields being referenced in the code below or error 45163 will be
            // raised. This can be achieved by putting '**' in the FIELDS parameter of the rowset, but 
            // avoid this when possible for performance reasons. The object, declared ByRef to conserve
            // resources by avoiding duplicating the object in memory, should not be altered directly
            // by this method (the purpose of the method is to return the name, not set it), but check
            // the code below to be sure.
            // par_doArray: not used
            // par_s1 - 5: not used
            // par_oReturn: String containing the generated SysName.
            // RETURNS:
            // True as a result. Returns friendly name or an empty string if the
            // filename is invalid via par_oReturn parameter.
            // EXAMPLE:
            // 'From a RecordOnSave script (not tested):
            // Dim sName as string = goScr.RunScript("GenerateSysName", doRS)
            // NOTES:
            // When a "Name" that is built with this method
            // is displayed in a View or linkbox and the same Name field is used
            // to sort the View or linkbox, at least the first field should match
            // the first field defined in clData::LKGetSortValue(). Otherwise what's
            // displayed will appear to be sorted arbitrarily.
            // NOTE 2:  
            // Links will not be tested because they are loaded automatically, but 
            // currently there is a bug in clRowset. RH working on this.

            string sProc = "clScripts:GenerateSysName";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS 11152016
            // par_bRunNext = False
            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";
            clRowSet doLink;
            int iLen;

            // We assume that sFileName is valid. If this is a problem, test it here and SetError.

            switch (Strings.UCase(sFileName))
            {
                case "CT"     // ==> Country
               :
                    {
                        par_bRunNext = false;

                        sTemp = doRS.GetFieldVal("TXT_COUNTRYNAME").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_COUNTRYCODE").ToString();
                        if (sTemp2 == "")
                            sTemp2 = "?";

                        sResult = sTemp + " (" + sTemp2 + ")";
                        break;
                    }

                case "SA"   // ==> State
         :
                    {
                        par_bRunNext = false;

                        sTemp = doRS.GetFieldVal("TXT_STATENAME").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_CODE").ToString();
                        if (sTemp2 == "")
                            sTemp2 = "?";

                        sResult = sTemp + " (" + sTemp2 + ")";
                        break;
                    }

                case "CY"   // ==> Customer Type
         :
                    {
                        par_bRunNext = false;

                        sTemp = doRS.GetFieldVal("TXT_CUSTOMERTYPENAME").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_CODE").ToString();
                        if (sTemp2 == "")
                            sTemp2 = "?";

                        sResult = sTemp + " (" + sTemp2 + ")";
                        break;
                    }

                case "OP":
                    {
                        // ==> OPP NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+LNK_For_Company%%TXT_CompanyName+" "+...
                        // LNK_For_Product%%TXT_ProductName+" "+CUR_Value
                        // OPP		<For - Company - TXT_CompanyName> <For - Product - TXT_ProductName> CUR_ValueIndex (MLS_Status)  
                        // OPP-COMPANY-0						OPP-PRODUCT-0

                        par_bRunNext = false;
                        if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_CreditedTo_US");

                        }
                        if (!doRS.IsLoaded("LNK_For_CO"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_For_CO");

                        }
                        if (!doRS.IsLoaded("LNK_For_PD"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_For_PD");

                        }
                        if (!doRS.IsLoaded("DTT_Time"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".DTT_Time");

                        }
                        if (!doRS.IsLoaded("CUR_Value"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".CUR_Value");

                        }
                        if (!doRS.IsLoaded("MLS_Status"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".MLS_Status");

                        }
                        if (!doRS.IsLoaded("MLS_MAINBLANKET"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".MLS_Status");


                        }

                        // LNK_CreditedTo_US%%TXT_Code
                        sTemp = Convert.ToString(doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, true, 1));
                        if (sTemp == "")
                            // No records linked
                            sTemp = "?";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", null, "TXT_Code", 1);
                            if (doLink.Count() > 0)
                                sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                            else
                                sTemp = "?";
                        }

                        // LNK_For_CO%%TXT_CompanyName
                        sTemp2 = Convert.ToString(doRS.GetFieldVal("LNK_For_CO", 1, 1, false, 1));
                        if (sTemp2 == "")
                            // No records linked
                            sTemp2 = "";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", null, "TXT_CompanyName", 1);
                            if (doLink.Count() > 0)
                                sTemp2 = doLink.GetFieldVal("TXT_CompanyName", 1, 22).ToString();
                            else
                                sTemp2 = "";
                        }

                        // LNK_For_Product%%TXT_ProductName
                        sTemp3 = doRS.GetFieldVal("LNK_For_PD", 1, 1, false, 1).ToString();
                        if (sTemp3 == "")
                            // No records linked
                            sTemp3 = "";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("PD", 3, "GID_ID='" + sTemp3 + "'", null, "TXT_ProductName", 1);
                            if (doLink.Count() > 0)
                                sTemp3 = doLink.GetFieldVal("TXT_ProductName", 1, 14).ToString();
                            else
                                sTemp3 = "";
                        }

                        // Company (23)   '25
                        // Date (15)      '11
                        // Credited To User (5)
                        // Product (15)   '17
                        // Value (13)
                        // Status (9)
                        // *** MI 10/4/07 Added LocalToUTC conversion
                        // sResult = sTemp2 & " " & _
                        // goTR.DateToString(doRS.GetFieldVal("DTE_Time", clC.SELL_SYSTEM), "YYYY-MM-DD") & " " & _
                        // sTemp & " " & _
                        // sTemp3 & " " & _
                        // goTR.Pad(doRS.GetFieldVal("CUR_Value"), 11, " ", "L")
                        int par_iValid = 0;
                        string par_sdlim = "";
                        DateTime sDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM));
                        sResult = sTemp2 + " " + Strings.Left(goTR.DateTimeToSysString(goTR.UTC_LocalToUTC(ref sDate), ref par_iValid, ref par_sdlim), 10) + " GMT " + sTemp + " " + sTemp3 + " " + goTR.Pad(doRS.GetFieldVal("CUR_Value").ToString(), 11, " ", "L");

                        sResult += " " + doRS.GetFieldVal("MLS_MAINBLANKET", 1, 7);

                        sResult += " [" + doRS.GetFieldVal("MLS_STATUS", 1, 8) + "]";
                        break;
                    }
                case "QT":
                    //==> QUOTE NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+
                    //					LNK_To_Company%%TXT_CompanyName+" "+CUR_Total

                    if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("LNK_To_CO"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_To_CO");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("DTT_Time"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".DTT_Time");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_QuoteNo"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_QuoteNo");
                        ///35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("CUR_Total"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".CUR_Total");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("MLS_STATUS"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".MLS_STATUS");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    //LNK_CreditedTo_US%%TXT_Code
                    sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp))
                    {
                        //No records linked
                        sTemp = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                        }
                        else
                        {
                            sTemp = "?";
                        }
                    }

                    //LNK_To_CO%%TXT_CompanyName
                    sTemp2 = doRS.GetFieldVal("LNK_To_CO", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp2))
                    {
                        //No records linked
                        sTemp2 = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CompanyName", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp2 = doLink.GetFieldVal("TXT_CompanyName").ToString();
                        }
                        else
                        {
                            sTemp2 = "?";
                        }
                    }


                    //Company 17 '21
                    //Date 15    '11
                    //Cred User 6
                    //Quote No 16
                    //Total 15
                    //Status 11
                    //Total: 80
                    sResult = goTR.Pad(sTemp2, 16, "", "R", true) + " " + goTR.Pad(sTemp3, 14, "", "R", true) + " " + goTR.Pad(sTemp, 4, "", "R", true) + " [" + goTR.Pad(doRS.GetFieldVal("TXT_QuoteNo").ToString(), 14, "", "R", true) + "] " + goTR.Pad(doRS.GetFieldVal("CUR_Total").ToString(), 13, "", "L", true) + " [" + doRS.GetFieldVal("MLS_STATUS", 0, 10).ToString() + "]";

                    break;
                case "CN":

                    sTemp2 = Convert.ToString(doRS.GetFieldVal("TXT_NameLast", 2));
                    sResult = sTemp2;
                    par_bRunNext = false;
                    break;

                case "CO":

                    sTemp2 = Convert.ToString(doRS.GetFieldVal("TXT_COMPANYNAME", 2));
                    sResult = sTemp2;
                    par_bRunNext = false;
                    break;
                case "GR":

                    sTemp2 = Convert.ToString(doRS.GetFieldVal("TXT_GROUPNAME", 2));
                    sResult = sTemp2;
                    par_bRunNext = false;
                    break;
                

            }

            sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
            sResult = goTR.Replace(sResult, Strings.Chr(10).ToString(), " ");
            sResult = goTR.Replace(sResult, Strings.Chr(13).ToString(), " ");
            sResult = goTR.Replace(sResult, Constants.vbTab, " ");

            // 1/28/15 Manmeet added replace for |
            sResult = goTR.Replace(sResult, "|", " ");

            par_oReturn = sResult;
            par_doCallingObject = doRS;
            return true;
        }
        public bool Opp_CreateActLog_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // 12/22/2004 RAH: turned over to MI
            // 2004/12/22 10:29:34 MAR Edited. SetLinkVals cause an error on line 37 of SetLinkVal: incorrect type.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;
            // PURPOSE:
            // PJ 5/30/02 Adds Act Log with new notes of Opportunity.
            // Run from enforce only in non-CREATION (MODIF) mode.
            // If it is run in CREATION mode, the new Activity will not be linked to the original Opp.
            // RETURNS:
            // 1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.

            Form doForm = (Form)par_doCallingObject;
            string sNotes = "";
            string sWork = "";
            long lWork = 0;
            string sMessage;

            // 'If answered Yes on MB to create a journal, then go directly to script that creates journal.
            // If UCase(par_s1) = "YES" Then
            // GoTo CREATEACTLOG
            // End If


            if (Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL").ToString()) <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))
                return true;
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Opportunity is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);
                doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");
                // Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                return true;
            }

            doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");

            // sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            // CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            // hard returns in the journal field
            sWork = Convert.ToString(doForm.oVar.GetVar("JournalWithHardReturns"));
            // CS 2/4/10
            if (sWork == "")
                return true; // We didn't hit MessageBoxEvent from entering a journal note.

            clArray doLink = new clArray();
            // SGR TKT#:1389 03012016 Bypass validation true
            // Dim doNew As Object = New clRowSet("AC", 2, , , , , , , , , , doForm.doRS.bBypassValidation)
            clRowSet doNew = new clRowSet("AC", 2, "", "", "", 1, "", "", "", "", "", true);

            // 'sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            // 'CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            // 'hard returns in the journal field
            // sWork = doForm.oVar.GetVar("JournalWithHardReturns")

            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, Convert.ToInt32(lWork));
            sNotes = sNotes + "== Created from Opportunity '" + doForm.doRS.GetFieldVal("SYS_Name") + "'";
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            doNew.SetFieldVal("MLS_Status", 1, 2);     // Completed
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            // CS doNew.SetFieldVal("TME_ENDTIME", doForm.doRS.GetFieldVal("TME_Time"))
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");

            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));


            // In the code below Paul added IsObjectAssigned tests because SetLinkVals weren't working in some cases.
            // ==> Remove the IsObjectAssigned tests.
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_ORIGINATEDBY_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (10).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_Related_CN", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (11).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_FOR_PD", ref doLink, true, 0, -1, "A_a", ref oTable);    // PJ Changed link name from 'LNK_RELATED_PRODUCT' to 'LNK_FOR_PRODUCT'
                                                                                                          // If Not goP.IsObjectAssigned(doLink) Then
                                                                                                          // goP.TraceLine("doLink (LNK_Related_PD) is not assigned (12).", "", sProc)
                                                                                                          // '	goErr.DisplayLastError()
                                                                                                          // End If
            doNew.SetLinkVal("LNK_Related_PD", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_PD) is not assigned (13).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Related_GR", ref doLink, true, 0, -1, "A_a", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_GR) is not assigned.", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_Related_GR", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_GROUP) is not assigned (2).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_FOR_CO", ref doLink, true, 0, -1, "A_a", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_FOR_CO) is not assigned (3).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_RELATED_CO", doLink);
            // if goErr.GetLastError()<>"E00000" then
            // goErr.DisplayLastError()
            // End If

            doNew.SetFieldVal("MLS_TYPE", 31, 2);      // Journal
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(doNew.GetFieldVal("MMO_HISTORY").ToString(), "Created."));
            doNew.SetFieldVal("LNK_RELATED_OP", doForm.GetRecordID());

            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;
                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);

                goLog.Log("MessageBoxEvent", doForm.oVar.GetVar("ScriptMessages").ToString(), 1, true, true);
                return false;
            }

            doNew = null;
            doLink = null;

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote_Draft.docx";
                }
                else if (sQTTemplate == "APG No Cover")
                {
                    return "cus_corr_APG_quote_Draft.docx";
                }
            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote.docx";
                }
                else if (sQTTemplate == "APG No Cover")
                {
                    return "cus_corr_APG_quote.docx";
                }
            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                        
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));
            string sModelText = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_MODELNAME"));
            string sModelDesc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_DESCRIPTION"));

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);
            rsQL.SetFieldVal("TXT_MODEL", sModelText);
            rsQL.SetFieldVal("MMO_DETAILS", sModelDesc);

            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);

            //rsQL.SetFieldVal("TXT_Model", sModelText);      
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        public bool QT_FormControlOnChange_BTN_RECALC_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            par_doCallingObject = doForm;
            return true;

        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.ClearLinkAll("LNK_RELATED_MO");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }

        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }

            return true;

        }
        public bool QT_MoveQuoteLine_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            string sDirection = "";
            string sLtGt = "";
            string sFirstLast = "";
            string sMoveSortDir = "";

            string sRecID = "";
            string sQuoteID = "";
            string sSelLineNO = "";
            string sMoveLineNO = "";
            double dSelLineNO = 0;
            double dMoveLineNO = 0;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot edit Quote Lines.");
                return true;
            }

            if (par_s3.ToUpper() == "UP")
            {
                sDirection = "up";
                sLtGt = "<";
                sFirstLast = "first";
                sMoveSortDir = "desc";
            }
            else if (par_s3.ToUpper() == "DOWN")
            {
                sDirection = "down";
                sLtGt = ">";
                sFirstLast = "last";
                sMoveSortDir = "asc";
            }
            else
            {
                return false;
            }

            sQuoteID = doForm.doRS.GetFieldVal("GID_ID").ToString();
            sRecID = doForm.GetLinkSelection("LNK_Connected_QL");

            if (string.IsNullOrEmpty(sRecID))
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }

            clRowSet doQLSelRS = new clRowSet("QL", clC.SELL_EDIT, "GID_ID=" + sRecID, "", "*,**", 1, par_bBypassValidation: true);
            if (doQLSelRS.GetFirst() == 0)
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }
            sSelLineNO = doQLSelRS.GetFieldVal("SR__LineNo").ToString();
            dSelLineNO = Convert.ToDouble(doQLSelRS.GetFieldVal("SR__LineNo"));

            clRowSet doQLMoveRS = new clRowSet("QL", clC.SELL_EDIT, ("LNK_IN_QT=" + sQuoteID + " AND SR__LineNo" + sLtGt + sSelLineNO), "SR__LineNo " + sMoveSortDir, "*,**", 1, par_bBypassValidation: true);
            if (doQLMoveRS.GetFirst() == 0)
            {
                doForm.MessageBox("This is the " + sFirstLast + " Qoute Line. Please select another Qoute Line to move " + sDirection + ".");
                par_doCallingObject = doForm;
                return false;
            }
            sMoveLineNO = doQLMoveRS.GetFieldVal("SR__LineNo").ToString();
            dMoveLineNO = Convert.ToDouble(doQLMoveRS.GetFieldVal("SR__LineNo"));

            doQLSelRS.SetFieldVal("SR__LineNo", dMoveLineNO, 2);
            doQLMoveRS.SetFieldVal("SR__LineNo", dSelLineNO, 2);

            if (doQLSelRS.Commit() == 0 || doQLMoveRS.Commit() == 0)
            {
                doForm.MessageBox("Could not save the Quote Lines. Please reopen the quote and try again.");
                par_doCallingObject = doForm;
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");
            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sGidId + "' And LNK_FOR_MO%%BI__ID<1 ", "LNK_IN_QT", "LNK_FOR_MO");

            if ((rsQL.GetFirst() == 1))
            {
                goErr.SetWarning(35000, sProc, "Please fill Model before saving the Quote  ");
                doForm.FieldInFocus = "LNK_FOR_MO";
                par_doCallingObject = doForm;
                return false;
            }

            return true;
        }


        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //string sGidId = Convert.ToString(doRS.GetFieldVal("Gid_id"));

            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            //clRowSet rsOL = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + sGidId + "'", "TXT_OpportunityLineName", "CUR_VALUE|SUM,CUR_VALUEINDEX|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double cur_Value = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double cur_ValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("cur_ValueIndex|SUM", 2));

                doRS.SetFieldVal("CUR_Value", cur_Value);
                doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);
            }

            par_doCallingObject = doRS;
            par_bRunNext = false;
            return true;

            
        }

        public bool GenerateSysName_post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = "clScripts:GenerateSysName";
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            string sFileName = doRS.GetFileName();
            string sResult = par_oReturn.ToString();
            clRowSet doLink = default(clRowSet);
            int iLen = 0;
            string par_sDelim = " ";

            //We assume that sFileName is valid. If this is a problem, test it here and SetError.

            switch (Microsoft.VisualBasic.Strings.UCase(sFileName))
            {

                case "QT":
                    //==> QUOTE NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+
                    //					LNK_To_Company%%TXT_CompanyName+" "+CUR_Total

                    if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("LNK_To_CO"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_To_CO");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("DTT_Time"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".DTT_Time");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_QuoteNo"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_QuoteNo");
                        ///35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("CUR_Total"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".CUR_Total");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("MLS_STATUS"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".MLS_STATUS");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    //LNK_CreditedTo_US%%TXT_Code
                    sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp))
                    {
                        //No records linked
                        sTemp = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                        }
                        else
                        {
                            sTemp = "?";
                        }
                    }

                    //LNK_To_CO%%TXT_CompanyName
                    sTemp2 = doRS.GetFieldVal("LNK_To_CO", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp2))
                    {
                        //No records linked
                        sTemp2 = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CompanyName", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp2 = doLink.GetFieldVal("TXT_CompanyName").ToString();
                        }
                        else
                        {
                            sTemp2 = "?";
                        }
                    }


                    //Company 17 '21
                    //Date 15    '11
                    //Cred User 6
                    //Quote No 16
                    //Total 15
                    //Status 11
                    //Total: 80
                    sResult = goTR.Pad(sTemp2, 16, "", "R", true) + " " + goTR.Pad(sTemp3, 14, "", "R", true) + " " + goTR.Pad(sTemp, 4, "", "R", true) + " [" + goTR.Pad(doRS.GetFieldVal("TXT_QuoteNo").ToString(), 14, "", "R", true) + "] " + goTR.Pad(doRS.GetFieldVal("CUR_Total").ToString(), 13, "", "L", true) + " [" + doRS.GetFieldVal("MLS_STATUS", 0, 10).ToString() + "]";

                    break;

                case "CN":

                    sTemp2 = Convert.ToString(doRS.GetFieldVal("TXT_NameLast", 2));
                    sResult = sTemp2;
                    par_bRunNext = false;
                    break;

                case "CO":
                    //==> COMPANY NEW:	TXT_CompanyName+" - "+TXT_CityMailing+", "+TXT_StateMailing
                    if (!doRS.IsLoaded("TXT_CompanyName"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_CompanyName");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_CityMailing"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_CityMailing");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_StateMailing"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_StateMailing");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TEL_PhoneNo"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TEL_PhoneNo");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    sResult = doRS.GetFieldVal("TXT_CompanyName").ToString();
                    sTemp = doRS.GetFieldVal("TXT_CityMailing").ToString();
                    sTemp2 = doRS.GetFieldVal("TXT_StateMailing").ToString();
                    if (!string.IsNullOrEmpty(sTemp) | !string.IsNullOrEmpty(sTemp2))
                    {
                        sResult += " - ";
                    }
                    if (!string.IsNullOrEmpty(sTemp))
                    {
                        sResult += sTemp;
                        if (!string.IsNullOrEmpty(sTemp2))
                        {
                            sResult += ", ";
                        }
                    }
                    if (!string.IsNullOrEmpty(sTemp2))
                    {
                        sResult += sTemp2;
                    }
                    sTemp3 = doRS.GetFieldVal("TEL_PhoneNo").ToString();
                    if (!string.IsNullOrEmpty(sTemp3))
                    {
                        sResult += " " + sTemp3;
                    }
                    sResult = Microsoft.VisualBasic.Strings.Left(sResult, 80);

                    break;
                case "PD":
                    //PRODUCT	TXT_ProductName 	
                    if (!doRS.IsLoaded("TXT_ProductName"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_ProductName");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_CatalogName"))
                    {
                        //goErr.SetError(35103, sProc,"" , sFileName + ".TXT_CatalogName");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    sTemp = doRS.GetFieldVal("TXT_ProductName").ToString();
                    sTemp2 = doRS.GetFieldVal("TXT_CatalogName", 0, (80 - 3 - Microsoft.VisualBasic.Strings.Len(sTemp))).ToString();

                    //ProductName(52)
                    //CatalogName(remaining space+1)
                    if (string.IsNullOrEmpty(sTemp2))
                    {
                        sResult = sTemp;
                    }
                    else
                    {
                        sResult = sTemp + " (" + sTemp2 + ")";
                    }

                    break;
                case "VE":
                    if (!doRS.IsLoaded("TXT_VendorName"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_VendorName");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    sResult = doRS.GetFieldVal("TXT_VendorName").ToString();

                    break;
                
            }



            par_oReturn = sResult;

            return true;

        }
        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));
            //doFormQT.doRS.SetFieldVal("LNK_ENDUSER_CO", rsOP.GetFieldVal("LNK_ENGINEERING_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_DISTREP_CO", rsOP.GetFieldVal("LNK_DISTRIBUTOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECOMPLETED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));
            doFormQT.doRS.SetFieldVal("LNK_FROM_SO", rsOP.GetFieldVal("LNK_FROM_SO"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
               // rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_RELATED_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    //doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }
        public bool FD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sTitle = "";
            string sUserGid = "";

            sTitle = Convert.ToString(doRS.GetFieldVal("TXT_TITLE"));
            sUserGid = Convert.ToString(doRS.GetFieldVal("LNK_TO_US"));

            goUI.AddAlert(sTitle + "  file is ready to download", "OPENDESKTOP", "DSK_4E8207CE-28DE-4F55-5858-B0B300B6D2B2", sUserGid, "").ToString();

            par_doCallingObject = doRS;
            return true;
        }
    }
}

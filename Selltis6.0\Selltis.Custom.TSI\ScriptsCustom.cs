﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;
        //private clScrMng goScr;
        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();

        }
        public ScriptsCustom()
        {
            Initialize();
        }


        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }

        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // SGR TKT:# 1342 Click cancel on merge
            Form doForm = (Form)par_doCallingObject;
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;

            // SGR TKT:# 1342 Merge Functionality - run at end of CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CN_Merge")) != "1")
                    {
                        // Don't allow merge of contact to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID"))
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null, null, null, null, null, "MessageBoxEvent", null, null, doForm, null, "OK", null, null, "CN", "MergeFail");
                        else
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null, null, null, null, null, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CN", "Merge");
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool MergeRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 3/18/2011 Added for merge
            par_bRunNext = false;

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName()) + "'", "", "", -1, "", "", "", "", "", true, false, false, false, -1, "", false, false, 1800);
                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Microsoft.VisualBasic.Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField).ToString() == "")
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    }
                                    break;

                                }

                            case "MMO":
                                {
                                    // Append
                                    if (doRSMergeTo.GetFieldVal(sField).ToString() == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    else
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMergeTo.GetFieldVal(sField) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + doRSMerge.GetFieldVal("SYS_Name") + " ==" + Constants.vbCrLf + doRSMerge.GetFieldVal(sField));
                                    }
                                    break;

                                }

                            case "CHK":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);

                                    }
                                    break;
                                }

                            case "MLS":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);

                                }
                                break;
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9).ToString());
                        if (sLinkType[4] == "NN" | sLinkType[1] == "2")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (Convert.ToString(doRSMergeTo.GetFieldVal(aLinks.GetItem(i))) == "")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    // Check Active if exists
                    if (goData.IsFieldValid(doRSMerge.GetFileName(), "CHK_ACTIVEFIELD") == true)
                        doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);

                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;
            par_doCallingObject = doRSMerge;
            return true;
        }

        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // SGR TKT:# 1342 
            Form doForm = (Form)par_doCallingObject;
            doForm.SetControlState("CHK_Merged", 4);
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;

            // SGR TKT:# 1342 Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;

            // SGR TKT:# 1342  Merge Functionality - run at end of CO_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CO_Merge")) != "1")
                    {
                        // Don't allow merge of company to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID"))
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null, null, null, null, null, "MessageBoxEvent", null, null, doForm, null, "OK", null, null, "CO", "MergeFail");
                        else
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null, null, null, null, null, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CO", "Merge");
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }

        public bool FIND_FormControlOnChange_BTN_COSearch_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            // CS 8/17/09: Do NOT consider already saved filter of the desktop.
            // CS 10/5/12: Added goTr.PrepareForSQL

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form oForm = (Form)par_doCallingObject;
            // SGR TKT:# 1342
            par_bRunNext = false;
            // Find dialog; Company tab Search button
            string sName;
            string sCode;
            string sZip;
            string sCity;
            string sState;
            string sCountry;
            string sPhone;
            string sFilterPhone = "";
            string sFilterPhone2 = "";
            string sCustNo;
            // SGR TKT:# 1342
            string sNotes;
            int n;


            string sView;
            int iCondCount = 0;
            string sViewCondition;
            string sNewCondition;
            int iOrigCondCount = 0;
            int i;
            string sFilter = "";

            // Get values from form
            sName = Strings.Trim(oForm.GetControlVal("NDB_TXT_COMPANYNAME"));
            if (sName != "")
            {
                sName = goTR.ConvertStringForQS(goTR.PrepareForSQL(sName), "TXT_COMPANYNAME", "CO", true);
            }
            sCode = Strings.Trim(oForm.GetControlVal("NDB_TXT_CODE"));
            if (sCode != "")
            {
                sCode = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCode), "TXT_CUSTCODE", "CO", true);
            }
            sZip = Strings.Trim(oForm.GetControlVal("NDB_TXT_ZIPBUSINESS"));
            if (sZip != "")
            {
                sZip = goTR.ConvertStringForQS(goTR.PrepareForSQL(sZip), "TXT_ZIPBUSINESS", "CO", true);

            }
            sCity = Strings.Trim(oForm.GetControlVal("NDB_TXT_CITYBUSINESS"));
            if (sCity != "")
            {
                sCity = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCity), "TXT_CITYBUSINESS", "CO", true);
            }
            sState = Strings.Trim(oForm.GetControlVal("NDB_TXT_STATEBUSINESS"));
            if (sState != "")
            {
                sState = goTR.ConvertStringForQS(goTR.PrepareForSQL(sState), "TXT_STATEBUSINESS", "CO", true);
            }
            sCountry = Strings.Trim(oForm.GetControlVal("NDB_TXT_COUNTRYBUSINESS"));
            if (sCountry != "")
            {
                sCountry = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCountry), "TXT_COUNTRYBUSINESS", "CO", true);
            }
            sPhone = Strings.Trim(oForm.GetControlVal("NDB_TXT_PHONE"));
            if (sPhone != "")
            {
                sPhone = goTR.ConvertStringForQS(goTR.PrepareForSQL(sPhone), "TEL_PHONE", "CO", true);
            }
            sCustNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_CUSTNO"));
            if (sCustNo != "")
            {
                sCustNo = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCustNo), "TXT_CUSTNO", "CO", true);
            }
            // SGR TKT:# 1342
            sNotes = Strings.Trim(oForm.GetControlVal("NDB_TXT_Notes"));
            if (sNotes != "")
            {
                sNotes = goTR.ConvertStringForQS(goTR.PrepareForSQL(sNotes), "MMO_Note", "CO", true);
            }
            // Use values to filter Company - Search Results desktop if it exists
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_CDC43A16-6CC5-4D73-5858-9AF1013E4F05");
            // Edit views in DT

            // View 1:Companies - Search Results
            sView = oDesktop.GetViewMetadata("VIE_14D622C2-C038-4D39-5858-9AF1013E4F05");
            // iCondCount = goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"))


            // 'If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            // If iCondCount = 1 Then
            // If goTR.StrRead(sView, "C1FIELDNAME") = "<%ALL%>" Then
            // iCondCount = 0 'Will overwrite these values
            // End If
            // End If
            // Original condition count
            iOrigCondCount = iCondCount;

            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (sName != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sCode != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sZip != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sCity != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sPhone != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sCustNo != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sState != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sCountry != "")
            {
                iCondCount = iCondCount + 1;
            }
            // SGR TKT:# 1342
            if (sNotes != "")
                iCondCount = iCondCount + 1;
            // Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (sName != "")
            {
                // Add 'Company Name' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_COMPANYNAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sName);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_COMPANYNAME[" + sName + "";
                }
                else
                {
                    sFilter = "TXT_COMPANYNAME[" + sName + "";
                }
            }
            if (sCode != "")
            {
                // Add 'Company Code' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_CUSTCODE%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCode);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_CUSTCODE[" + sCode + "";
                else
                    sFilter = "TXT_CUSTCODE[" + sCode + "";
            }
            if (sZip != "")
            {
                // Add 'Zip Mailing' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_ZIPMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sZip);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_ZIPMAILING[" + sZip + "";
                else
                    sFilter = "TXT_ZIPMAILING[" + sZip + "";
            }
            if (sCity != "")
            {
                // Add 'City Mailing' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_CITYMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCity);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_CITYMAILING[" + sCity + "";
                else
                    sFilter = "TXT_CITYMAILING[" + sCity + "";
            }
            // CS 12/1/08
            if (sState != "")
            {
                // Add 'State Mailing' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_STATEMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sState);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_STATEMAILING[" + sState + "";
                else
                    sFilter = "TXT_STATEMAILING[" + sState + "";
            }
            // CS 12/1/08
            if (sCountry != "")
            {
                // Add 'Country Mailing' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_COUNTRYMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCountry);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_COUNTRYMAILING[" + sCountry + "";
                else
                    sFilter = "TXT_COUNTRYMAILING[" + sCountry + "";
            }

            if (sCustNo != "")
            {
                // Add 'Cust No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_CUSTNO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCustNo);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_CUSTNO[" + sCustNo + "";
                else
                    sFilter = "TXT_CUSTNO[" + sCustNo + "";
            }
            // SGR TKT:# 1342
            if (sNotes != "")
            {
                // Add 'Cust No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MMO_Note%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sNotes);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND MMO_Note[" + sNotes + "";
                else
                    sFilter = "MMO_Note[" + sNotes + "";
            }

            // CS 9/17/13
            if (sPhone != "")
            {
                // Add 'Phone' condition
                // Remove all numeric characters and add % to front and end of #
                // This will make sure we pick up all possible
                // phone number formats. Loop thru all characters in Bus Phone
                for (n = 1; n <= Strings.Len(sPhone); n++)
                {
                    if (!goTR.IsNumeric(Strings.Mid(sPhone, n, 1)))
                        sFilterPhone = sFilterPhone; // don't include this character
                    else
                        sFilterPhone = sFilterPhone + Strings.Mid(sPhone, n, 1);
                }


                // CS 9/13/13: Check multiple formats as stored in records
                if (Strings.Len(sFilterPhone) == 1)
                {
                    // Add % in front and end of phone #
                    sFilterPhone2 = "%" + sFilterPhone + "%";
                    goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                    goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                    goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                    if (sFilter != "")
                        sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                    else
                        sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                }

                if (Strings.Len(sFilterPhone) == 2)
                {
                    n = 1;
                    for (n = 1; n <= 2; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 1) + "_";  // format: #-#'
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: ##  
                            sFilterPhone2 = sFilterPhone;// format: ##
                                                         // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if ((sFilter != "" & n == 2))
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 1);
                            goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 3)
                {
                    n = 1;
                    for (n = 1; n <= 2; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 2) + "_";  // format: #-##'
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: ###                   
                            sFilterPhone2 = sFilterPhone;// format: ###
                                                         // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if ((sFilter != "" & n == 2))
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 1);
                            goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 4)
                {
                    n = 1;
                    for (n = 1; n <= 4; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###'
                        }
                        else if (n == 2)
                            // sFilterPhone2 = "_" & sFilterPhone & "_" 'format: ####
                            sFilterPhone2 = sFilterPhone; // format: ####
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-#
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 1);
                        }
                        else if (n == 4)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###-#
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 1);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if ((sFilter != "" & n > 1))
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 3);
                            if (n == 2)
                            {
                            }
                            if (n == 4)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 5)
                {
                    n = 1;
                    for (n = 1; n <= 5; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-#'
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 1);
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: #####
                            sFilterPhone2 = sFilterPhone; // format: #####
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 2);
                        }
                        else if (n == 4)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "__";  // format: #-###- #'
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 1);
                        }
                        else if (n == 5)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 2);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if ((sFilter != "" & n > 1))
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 4);
                            if (n == 2)
                            {
                            }
                            if (n == 5)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 6)
                {
                    n = 1;
                    for (n = 1; n <= 5; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-#'
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 2);
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: #####
                            sFilterPhone2 = sFilterPhone; // format: #####
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 3);
                        }
                        else if (n == 4)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "__";  // format: #-###- #'
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 2);
                        }
                        else if (n == 5)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###- ##
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 3);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if ((sFilter != "" & n > 1))
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 4);
                            if (n == 2)
                            {
                            }
                            if (n == 5)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 7)
                {
                    n = 1;
                    for (n = 1; n <= 7; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-###
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 3);
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: #######
                            sFilterPhone2 = sFilterPhone; // format: #######
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 4);
                        }
                        else if (n == 4)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-###-#
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 1);
                        }
                        else if (n == 5)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "__";  // format: #- ###-###
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 3);
                        }
                        else if (n == 6)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###- ####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 4);
                        }
                        else if (n == 7)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###- ###-#
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 1);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if (sFilter != "" & n > 1)
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 6);
                            if (n == 2)
                            {
                            }
                            if (n == 7)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 8)
                {
                    n = 1;
                    for (n = 1; n <= 9; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 4);
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: ########
                            sFilterPhone2 = sFilterPhone; // format: ########
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-#####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 5);
                        }
                        else if (n == 4)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 2);
                        }
                        else if (n == 5)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-###-#
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 5, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 1);
                        }
                        else if (n == 6)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "__";  // format: #-###-####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 4);
                        }
                        else if (n == 7)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###-#####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 5);
                        }
                        else if (n == 8)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###-###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 2);
                        }
                        else if (n == 9)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "__";  // format: #-###-###-#
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 5, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 1);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if (sFilter != "" & n > 1)
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 8);
                            if (n == 2)
                            {
                            }
                            if (n == 9)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 9)
                {
                    n = 1;
                    for (n = 1; n <= 9; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-#####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 5);
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: #########
                            sFilterPhone2 = sFilterPhone; // format: #########
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-######
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 6);
                        }
                        else if (n == 4)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-###-###
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 3);
                        }
                        else if (n == 5)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 5, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 2);
                        }
                        else if (n == 6)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "__";  // format: #-###-#####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 5);
                        }
                        else if (n == 7)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###-######
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 6);
                        }
                        else if (n == 8)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###-###-###
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 3);
                        }
                        else if (n == 9)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "__";  // format: #-###-###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 5, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 2);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if (sFilter != "" & n > 1)
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 8);
                            if (n == 2)
                            {
                            }
                            if (n == 9)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 10)
                {
                    n = 1;
                    for (n = 1; n <= 9; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-######
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 6);
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: ##########
                            sFilterPhone2 = sFilterPhone;  // format: ##########
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-#######
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 7);
                        }
                        else if (n == 4)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-###-####
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 4);
                        }
                        else if (n == 5)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-###-###
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 5, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 3);
                        }
                        else if (n == 6)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "__";  // format: #-###- #####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 6);
                        }
                        else if (n == 7)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###- ######
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 7);
                        }
                        else if (n == 8)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###- ###-###
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 4);
                        }
                        else if (n == 9)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "__";  // format: #-###- ###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 5, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 3);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if (sFilter != "" & n > 1)
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 8);
                            if (n == 2)
                            {
                            }
                            if (n == 9)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 11)
                {
                    n = 1;
                    for (n = 1; n <= 9; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-#######
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 7);
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: ###########
                            sFilterPhone2 = sFilterPhone;  // format: ###########
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-########
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 8);
                        }
                        else if (n == 4)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-###-#####
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 5);
                        }
                        else if (n == 5)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-###-####
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 5, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 4);
                        }
                        else if (n == 6)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "__";  // format: #-###- #####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 7);
                        }
                        else if (n == 7)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###- ######
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 8);
                        }
                        else if (n == 8)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "__"; // format: ###- ###-###
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 5);
                        }
                        else if (n == 9)
                        {
                            // VS 042212015 TKT#485 : Also Search for space after character eg:(*************
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "__";  // format: #-###- ###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 5, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 4);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if (sFilter != "" & n > 1)
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 8);
                            if (n == 2)
                            {
                            }
                            if (n == 9)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }


                // If phone number is greater than 11 chars unknown format; just check for 
                // straight chars for now
                if (Strings.Len(sFilterPhone) > 11)
                {
                    // Add % in front and end of phone #
                    sFilterPhone2 = "%" + sFilterPhone + "%";
                    goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                    goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                    goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                    if (sFilter != "")
                        sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                    else
                        sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                }
            }




            // CS COMMENTED OUT
            // If sPhone <> "" Then
            // ''Add 'Phone' condition
            // ''Replace all non-numeric characters (not 0-9) with %. This will make sure we pick up all possible
            // ''phone number formats. Loop thru all characters in Phone
            // 'For n = 1 To Len(sPhone)
            // '    If Not goTR.IsNumeric(Mid(sPhone, n, 1)) Then
            // '        'Replace this character with '%'
            // '        sFilterPhone = sFilterPhone & "%"
            // '    Else
            // '        sFilterPhone = sFilterPhone & Mid(sPhone, n, 1)
            // '    End If
            // 'Next

            // 'CS 9/5/13: Trying to catch multiple phone formats (see notes in CN Find)
            // 'Add 'Phone' condition
            // 'Remove all numeric characters and add % to front and end of #
            // 'This will make sure we pick up all possible
            // 'phone number formats. Loop thru all characters in Bus Phone
            // For n = 1 To Len(sPhone)
            // If Not goTR.IsNumeric(Mid(sPhone, n, 1)) Then
            // sFilterPhone = sFilterPhone 'don't include this character
            // Else
            // sFilterPhone = sFilterPhone & Mid(sPhone, n, 1)
            // End If
            // Next
            // 'CS 9/5/13: Changing the phone filter so that we catch phone numbers with formatting
            // 'in the record when we did not enter formatting in the search field           
            // 'Check if the phone no is 7 or 10 chars
            // If Len(sFilterPhone) = 7 Then '%1111111%
            // sFilterPhone2 = Left(sFilterPhone, 3) & "%"
            // sFilterPhone2 = sFilterPhone2 & Right(sFilterPhone, 4)
            // sFilterPhone = sFilterPhone2
            // ElseIf Len(sFilterPhone) = 10 Then '%1111111111%
            // sFilterPhone2 = Left(sFilterPhone, 3) & "%"
            // sFilterPhone2 = sFilterPhone2 & Mid(sFilterPhone, 4, 3) & "%"
            // sFilterPhone2 = sFilterPhone2 & Right(sFilterPhone, 4)
            // sFilterPhone = sFilterPhone2
            // ElseIf Len(sFilterPhone) = 11 Then 'includes country code
            // sFilterPhone2 = Left(sFilterPhone, 1) & "%"
            // sFilterPhone2 = sFilterPhone2 & Mid(sFilterPhone, 2, 3) & "%"
            // sFilterPhone2 = sFilterPhone2 & Mid(sFilterPhone, 5, 3) & "%"
            // sFilterPhone2 = sFilterPhone2 & Right(sFilterPhone, 4)
            // sFilterPhone = sFilterPhone2
            // End If
            // 'Add % in front and end of phone #
            // sFilterPhone = "%" & sFilterPhone & "%"


            // goTR.StrWrite(sView, "C" & i & "FIELDNAME", "<%TEL_PHONENO%>")
            // goTR.StrWrite(sView, "C" & i & "CONDITION", "[") 'contains
            // goTR.StrWrite(sView, "C" & i & "VALUE1", sFilterPhone)
            // i = i + 1
            // If sFilter <> "" Then
            // sFilter = sFilter & " AND TEL_PHONENO[" & sFilterPhone & ""
            // Else
            // sFilter = "TEL_PHONENO[" & sFilterPhone & ""
            // End If
            // End If


            // Edit CONDITION= line in view MD
            // sViewCondition = goTR.StrRead(sView, "CONDITION")
            // If sViewCondition = "" Then
            sNewCondition = sFilter; // No filter in view already
                                     // Else
                                     // sNewCondition = sViewCondition & " AND " & sFilter
                                     // End If
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_14D622C2-C038-4D39-5858-9AF1013E4F05", sView);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;


            // Que Company Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
            goUI.Queue("", "");


            return true;
        }

        public bool MessageBoxEvent_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // SGR TKT:# 1342 
            Form doForm = (Form)par_doCallingObject;
            switch (Strings.UCase(par_s5))
            {
                case "MERGE":
                    {
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // run merge script, continue save
                                    scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
                                    break;
                                }

                            case "NO":
                                {
                                    // Clear merged to co linkbox, continue save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    break;
                                }

                            case "CANCEL":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "MERGEFAIL":
                    {
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "PR_FORMONSAVE_POST_TSIBIDCHANGED":
                    {
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // Change TSI Bid Proposal Amount
                                    try
                                    {
                                        Form oForm = (Form)par_doCallingObject;
                                        oForm.oVar.SetVar("STARTSTAGE", oForm.Rowset.GetFieldVal("MLS_STAGE"));
                                        oForm.MoveToTab(13, "CUR_REVENUE");
                                        oForm.oVar.SetVar("CancelSave", "1");
                                    }
                                    catch (Exception ex)
                                    {
                                        if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                                            goErr.SetError(ex, 45105, sProc);
                                    }

                                    break;
                                }

                            case "NO":
                                {
                                    break;
                                }
                        }

                        break;
                    }
                    break;
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // SGR TKT:# 1342 21112016 Copied below from clscript op formonload and set par_bRunNext=False
            par_bRunNext = false;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3)

            Form doForm = (Form)par_doCallingObject;
            string sColor = Convert.ToString(goP.GetVar("sMandatoryFieldColor"));


            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }
            // PORTING FROM COMMENCE IN PROGRESS (ALMOST DONE). Owner: RH. Original owner: PJ.

            // 2004/12/22 10:11:51 MAR Changed MMO_NOTES to MMO_JOURNAL in SetVar.

            // Set journal field locked
            doForm.SetControlState("MMO_Journal", 1);
            doForm.SetControlState("MMR_Journal", 1);

            doForm.SetControlState("CUR_VALUE", 4);
            doForm.SetControlState("CUR_VALUEINDEX", 4);


            // CS uncomment----------- FIELD LABELS -----------
            // doForm.SetFieldProperty("CHK_Q01", "LABELTEXT", "Leverage")
            // doForm.SetFieldProperty("CHK_Q02", "LABELTEXT", "Executive Buy-In")
            // doForm.SetFieldProperty("CHK_Q03", "LABELTEXT", "CEO Contacted")
            // doForm.SetFieldProperty("CHK_Q04", "LABELTEXT", "Front End Buy-In")
            // doForm.SetFieldProperty("CHK_Q05", "LABELTEXT", "Competition ID'd")
            // doForm.SetFieldProperty("CHK_Q06", "LABELTEXT", "Champion Built")
            // doForm.SetFieldProperty("CHK_Q07", "LABELTEXT", "Influences ID'd")
            // doForm.SetFieldProperty("CHK_Q08", "LABELTEXT", "Needs Assessed")
            // doForm.SetFieldProperty("CHK_Q09", "LABELTEXT", "Approved/Funded")
            // doForm.SetFieldProperty("CHK_Q10", "LABELTEXT", "Decision Process")
            // doForm.SetFieldProperty("CHK_Q11", "LABELTEXT", "Timing Estimate")
            // doForm.SetFieldProperty("CHK_Q12", "LABELTEXT", "Key Questions")
            // doForm.SetFieldProperty("CHK_Q13", "LABELTEXT", "Present/Demo")
            // doForm.SetFieldProperty("CHK_Q14", "LABELTEXT", "Quoted")
            // doForm.SetFieldProperty("CHK_Q15", "LABELTEXT", "Marked Status")

            // ----------- VARIABLES -------------
            doForm.oVar.SetVar("lLenJournal", Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")));

            // Set mandatory field color
            doForm.SetFieldProperty("DTE_Time", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("TME_Time", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_CreditedTo_US", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("SR__QTY", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_FOR_PD", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_For_Co", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", sColor);
            // SGR TKT:# 1342 21112016
            // doForm.SetFieldProperty("LNK_for_PD", "LABELCOLOR", sColor)
            doForm.SetFieldProperty("DTE_ExpCloseDate", "LABELCOLOR", sColor);
            // SGR TKT:# 1342 21112016
            // doForm.SetFieldProperty("SR__Qty", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("CUR_UnitValue", "LABELCOLOR", sColor)


            // Set button field tooltips
            doForm.SetFieldProperties("BTN_CALCPROBABILITY_1", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Calculate Opportunity value and value index");
            doForm.SetFieldProperties("BTN_LINKCOMPANY", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Link Company of the linked Contact");
            doForm.SetFieldProperties("BTN_INSERTLINE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend a Journal line");
            doForm.SetFieldProperties("BTN_INSERTLINEMMR", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend a Journal line");
            doForm.SetFieldProperties("BTN_INSERTDATE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend the date and time in the Notes field");
            doForm.SetFieldProperties("BTN_INSERTPRODQS", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend questions for the linked Product in the Notes field");
            doForm.SetFieldProperties("BTN_CALCPROBABILITY", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Calculate probability %");
            doForm.SetFieldProperties("BTN_LINKCOMPANY_1", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Link Companies of the linked Contacts");
            doForm.SetFieldProperties("BTN_INSERTPRESALE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend Presale Questions defined in Workgroup options in the Questionnaire field");
            doForm.SetFieldProperties("CHK_CREATETODO", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Create a linked To Do");


            // ----------- STATES --------------
            scriptManager.RunScript("Opp_ManageControlState", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);

            // CS 8/4/08
            // Check if this Opp was created as a result of a Create Linked from an AC.
            // copy AC MMO_Journal (Lead only) to OP MMO_Journal when the OP is created from the AC
            if (doForm.GetMode() == "CREATION")
            {
                string sID = doForm.GetCreateLinkedSourceRecSUID;
                // If the ID is not blank, find out if coming from an AC
                if (sID != "" & sID != null)
                {
                    if (goTR.GetFileFromSUID(sID) == "AC")
                    {
                        // Check if this AC is Lead
                        clRowSet doRSAC = new clRowSet("AC", 3, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "GID_ID,MLS_PURPOSE,MMO_JOURNAL");
                        if (doRSAC.GetFirst() == 1)
                        {
                            if (Convert.ToInt32(doRSAC.GetFieldVal("MLS_PURPOSE", 2)) == 8)
                                doForm.doRS.SetFieldVal("MMO_JOURNAL", doRSAC.GetFieldVal("MMO_JOURNAL"));
                        }
                        else
                        {
                        }
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.


            return true;
        }

        public bool OP_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused. '
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sDateStamp = "";
            string sJournal;
            // SGR TKT:# 1342 
            

            // *******************ENFORCE*************************

            // REVIEW: 
            // DateTimeDiff returning 0 and null.  RAH: Changed all instances to use Val
            // Review month and year field requirements - used for sorting views in Cmc.
            // Runscript UpdateContactItem - this procedure is not fixed
            // UpdateProject - have not ported this

            // Dim i As Integer
            bool bIAmInvolved;
            string sMeVal;
            // Dim sMsgResponse As String
            long lStatusVal;
            string sCreatedDate;
            string sCloseDate = null;
            // Dim sDateWon As String
            bool bEnforceVal;
            // Dim sValueFld As Decimal
            // Dim sWork As String
            // Dim iMonthCreatedNum As String
            // Dim lCreateDate As Long
            // Dim sMonthCreated As String
            // Dim lCloseDate As Long
            // Dim iMonthCloseNum As String
            bool bResult;
            string sScriptVar;


            // CS 7/6/07: In MD
            // If doForm.doRS.GetLinkCount("LNK_CREDITEDTO_US") = 0 Then
            // doForm.movetotab(0)
            // doForm.MoveToField("LNK_CREDITEDTO_US")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "LNK_CREDITEDTO_US"), "", "", "", "", "", "", "", "", "LNK_CREDITEDTO_US")
            // Return False
            // End If

            // If doForm.doRS.GetLinkCount("LNK_FOR_CO") = 0 Then
            // doForm.MoveToField("LNK_FOR_CO")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "LNK_FOR_CO"), "", "", "", "", "", "", "", "", "LNK_FOR_CO")
            // Return False
            // End If

            // If doForm.doRS.GetLinkCount("LNK_FOR_PD") = 0 Then
            // doForm.MoveToField("LNK_FOR_PD")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "LNK_FOR_PD"), "", "", "", "", "", "", "", "", "LNK_FOR_PD")
            // Return False
            // End If

            // If doForm.doRS.GetFieldVal("DTE_TIME") = "" Then
            // doForm.MoveToField("DTE_TIME")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "DTE_TIME"), "", "", "", "", "", "", "", "", "DTE_TIME")
            // Return False
            // End If

            // If doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE") = "" Then
            // doForm.MoveToField("DTE_EXPCLOSEDATE")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "DTE_EXPCLOSEDATE"), "", "", "", "", "", "", "", "", "DTE_EXPCLOSEDATE")
            // Return False
            // End If

            // If doForm.doRS.GetFieldVal("SR__QTY") = 0 Then
            // doForm.MoveToField("SR__QTY")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "SR__QTY"), "", "", "", "", "", "", "", "", "SR__QTY")
            // Return False
            // End If

            lStatusVal = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2));

            // Next Action Date - check only when status is Open or On Hold
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceNextActionDate"))
            {
                switch (lStatusVal)
                {
                    case 0:
                    case 1       // Open, On Hold
                   :
                        {
                            if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1).ToString()) | (doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1).ToString() == ""))
                            {
                                doForm.MoveToTab(1);
                                doForm.MoveToField("DTE_NEXTACTIONDATE");
                                // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE")
                                goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE");
                                return false;
                            }
                            else
                            {
                                // Prompt user to update Next Action date if the current value is < Today
                                // and if the user is the 'Credited To' or 'Peer' in the Opportunity.
                                sMeVal = goP.GetMe("ID");
                                bIAmInvolved = false;
                                doForm.MoveToTab(0);
                                if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US")) == sMeVal)
                                {
                                    bIAmInvolved = true;
                                }
                                if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US")) == sMeVal)
                                {
                                    bIAmInvolved = true;
                                }
                                // goP.TraceLine("bIAmInvolved: '" & bIAmInvolved & "'", "", sProc)
                                // goP.TraceLine("Next Action is " & doForm.GetFieldVal("DTE_NEXTACTIONDATE", 2), "", sProc)
                                // goP.TraceLine("DateSys is " & Format(goTR.NowLocal(), "yyyy-MM-dd hh:mm:ss.fff"), "", sProc)
                                if (Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1)) < Convert.ToDateTime(Strings.Format(goTR.NowLocal(), "yyyy-MM-dd")) & bIAmInvolved == true)
                                {
                                    // goP.TraceLine("NextReview was found to be < than DateSys()", "", sProc)
                                    // lMsgResponse - may return long int. 

                                    // Check if we already asked.
                                    if (doForm.oVar.GetVar("Op_FormOnSave_NA_Date") != null && doForm.oVar.GetVar("Op_FormOnSave_NA_Date").ToString() != "1")
                                    {
                                        // CS 6/13/11: Now set in call to messagebox doForm.oVar.SetVar("Op_FormOnSave_NA_Date", "1")
                                        doForm.MessageBox("Would you like to update the 'Next Action' date with 2 weeks from today?" + Environment.NewLine + Environment.NewLine + "Currently it is " + doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE") + ".", clC.SELL_MB_YESNO + clC.SELL_MB_DEFBUTTON1, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "", null, null, "Yes", "No", "", "", "Op_FormOnSave_NADate", "Op_FormOnSave_NA_Date");

                                        return true;
                                    }
                                }
                            }

                            break;
                        }

                    case 6      // Quoted
             :
                        {
                            doForm.doRS.SetFieldVal("CHK_Q14", 1, 2);
                            break;
                        }
                }
            }

            // *** StatusFunc ***
            // Status is Open
            // Make sure this is not called other than from (after) Enforce

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceAndFillStatusRelatedFields"))
            {
                switch (lStatusVal)
                {
                    case 0:
                    case 6        // Open, Quoted
                   :
                        {
                            // Make sure the Expected Close Date field is not earlier than Date Created
                            // First test whether either of the two fields is empty

                            if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_TIME", 1).ToString()) | doForm.doRS.GetFieldVal("DTE_TIME", 1).ToString() == "")
                            {
                                doForm.doRS.SetFieldVal("DTE_TIME".ToString(), Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                            }
                            else
                            {
                                sCreatedDate = Convert.ToString(doForm.doRS.GetFieldVal("DTE_TIME", 2));
                            }

                            if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1).ToString()) | doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1).ToString() == "")
                            {
                                doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                            }
                            else
                            {
                                sCloseDate = Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2));
                            }


                            if (Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2)) < Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_TIME", 2)))
                            {
                                sCloseDate = "";
                                // doForm.SetFieldVal("DTE_EXPCLOSEDATE", sCloseDate, 2)  'RAH: I commented this out as it was a little confusing and unneccessary.  
                                // Additionally, knowing what the older date is has some value

                                // doForm.MoveToTab(2)    'RAH:There is no need to move to a tab as the field is on the top of the form.
                                doForm.MoveToField("DTE_EXPCLOSEDATE");
                                goErr.SetWarning(30200, sProc, "", "The Expected Close Date can't be earlier than the Date Created.", "", "", "", "", "", "", "", "", "DTE_EXpCloseDate");
                                return false;
                            }
                            else
                            {
                                {
                                    bEnforceVal = scriptManager.RunScript("Opp_EnforceValue", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
                                }
                                if (bEnforceVal == false)
                                {
                                }
                            }

                            break;
                        }

                    case 2:
                    case 3   // Won, Lost
             :
                        {
                            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_REASONWONLOST", 2)) == 0)
                            {
                                doForm.MoveToTab(2);
                                doForm.MoveToField("MLS_REASONWONLOST");
                                // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "MLS_REASONWONLOST"), "", "", "", "", "", "", "", "", "MLS_REASONWONLOST")
                                goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("MLS_REASONWONLOST"), "", "", "", "", "", "", "", "", "MLS_REASONWONLOST");
                                return false;
                            }

                            // Fill 'Date Won' field if empty
                            if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1).ToString()) | doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1).ToString() == "")
                                doForm.doRS.SetFieldVal("DTE_DATECLOSED", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);

                            // Fill Expected Close Date field if empty
                            if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2).ToString()) | doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1).ToString() == "")
                                doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);

                            bEnforceVal = scriptManager.RunScript("Opp_EnforceValue", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
                            if (bEnforceVal == false)
                            {
                            }

                            break;
                        }

                    case 1      // On Hold
             :
                        {
                            bEnforceVal = scriptManager.RunScript("Opp_EnforceValue", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
                            if (bEnforceVal == false)
                            {
                            }

                            break;
                        }

                    case 4:
                    case 5   // Cancelled, Delete
             :
                        {
                            // Fill 'Date Won' field (if empty) with today, the date we lost or cancelled the opportunity
                            if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_DATECLOSED", 2).ToString()) | doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1).ToString() == "")
                            {
                                doForm.doRS.SetFieldVal("DTE_DATECLOSED", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                            }

                            bEnforceVal = scriptManager.RunScript("Opp_EnforceValue", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
                            if (bEnforceVal == false)
                            {
                            }

                            break;
                        }
                }
            }

            // StatusFunc	***This has been added above
            // '	CalcReviewOverdue	***No longer used. Field no longer exists on Opp
            // CalcID				  'Calculates ID number. No longer needed

            // -----	CalcProbability	  'Calculates Value, Probability % and Value Index
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CalcProbability"))
            {
                scriptManager.RunScript("Opp_CalcProbability", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            }

            // CS: Moving below to RecOnSave
            // '-----	Connect Vendors
            // goScr.RunScript("Opp_ConnectVendors", doForm)

            // '----- Set the Related Territory connection from Related Company's territory
            // doForm.ClearLinkAll("LNK_Related_TE")
            // sWork = doForm.GetFieldVal("LNK_FOR_CO%%LNK_In_TE")
            // doForm.SetFieldVal("LNK_Related_TE", sWork)

            // '----- Set Related Division from Product's Division
            // doForm.ClearLinkAll("LNK_RELATED_DV")
            // sWork = doForm.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_DV")
            // doForm.SetFieldVal("LNK_RELATED_DV", sWork)

            // ----- Fill Year, Month fields
            // Done in RecordOnSave script

            // CS: Already in RecOnSave
            // '-----	ConnectUsers	*** Fills Involves User with Credited to and Peer
            // doForm.SetFieldVal("LNK_INVOLVES_US", doForm.GetFieldVal("LNK_CREDITEDTO_US"))
            // doForm.SetFieldVal("LNK_INVOLVES_US", doForm.GetFieldVal("LNK_PEER_US"))

            // Cs: Already in RecOnSave
            // ----- ConnectContacts
            // doForm.SetFieldVal("LNK_INVOLVES_CN", doForm.GetFieldVal("LNK_ORIGINATEDBY_CN"))

            // -----	ConnectSponsors		*** Sponsor removed as sales cycle check box

            // UpdateContactInterestedInItem	*** Review this procedure - not sure how to handle rowset object
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "UpdateContactInterestedInProduct"))
            {
                if (doForm.oVar.GetVar("Opp_UpdateContactItem_Ran") != "1")
                    scriptManager.RunScript("Opp_UpdateContactItem", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            }

            // -----	UpdateProject	*** updates project with connections from Opp
            // CS 4/3/09
            // ---Copy Notes to Journal on creation of a lead
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CopyNotesToJournalIfNewOpp"))
            {
                if (doForm.GetMode() == "CREATION")
                {
                    // CS 4/28/09: Make sure Notes field has a value
                    if (Convert.ToString(doForm.doRS.GetFieldVal("MMO_NOTES")) != "")
                    {
                        // CS 4/20/09
                        // Check if there is a value in the Journal field already
                        sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMO_JOURNAL"));
                        if (sJournal != "")
                            doForm.doRS.SetFieldVal("MMO_JOURNAL", sJournal + " " + doForm.doRS.GetFieldVal("MMO_NOTES"));
                        else
                        {
                            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "CODE", "USERNOOFFSETLABEL", sDateStamp); // returns var sDateStamp
                            doForm.doRS.SetFieldVal("MMO_JOURNAL", sJournal + sDateStamp + " " + doForm.doRS.GetFieldVal("MMO_NOTES"));
                        }
                    }
                }
            }

            // -----	CreateActLog
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CreateActivity"))
            {
                // If doForm.GetMode() <> "CREATION" Then		'and doForm.GetFieldVal("MLS_STATUS",2) = 0 Then

                // check if we already ran this script. If so don't run it again.
                if (Convert.ToString(doForm.oVar.GetVar("Opp_CreateActLog_Ran")) != "1")
                {
                    // Only try to create an AC log if have the option set in workgroup options to do so
                    if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEOPP_CREATE_AC")) == "1")
                    {
                        bool bRunNext = true;
                        bResult = scriptManager.RunScript("Opp_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections);

                    }
                }
            }

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "DisplayErrorMessages"))
            {
                // If we had previously run this script which called updating linked CN's next action/contact
                // dates and/or creating an ACT the commit of the Contact  or AC had failed we need to display a message on the AC form to
                // to the user.
                sScriptVar = Convert.ToString(doForm.oVar.GetVar("ScriptMessages"));
                if (sScriptVar != "")
                {
                    if (Strings.Len(sScriptVar) < 500)
                    {
                        doForm.MessageBox(sScriptVar, 0, "Selltis", "MessageBoxEvent", "OK", "OP_FormOnSave_ScriptMessages");

                    }
                    else
                    {
                        doForm.MessageBox(Strings.Left(sScriptVar, 497) + "...", 0, "Selltis", "MessageBoxEvent", "OK", "OP_FormOnSave_ScriptMessages");

                    }
                    return true;
                }
            }
            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;
        }

        public bool PR_BidCalc(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            decimal cWinBid;
            decimal cLostBid;
            decimal cBidDiff;
            decimal cBidPerc;
            Form doForm = (Form)par_doCallingObject;

            cWinBid = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_WINNINGCSIAMOUNT", 2));
            cLostBid = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_LOSINGCSIAMOUNT", 2));

            if (cWinBid != 0 & cLostBid != 0)
            {
                cBidDiff = cLostBid - cWinBid;

                doForm.doRS.SetFieldVal("Cur_HighBidLessLowBid", cBidDiff);

                cBidPerc = (cBidDiff * 100 / (decimal)cLostBid);
                doForm.doRS.SetFieldVal("LI__BIDDIFFERENCEPERC", cBidPerc);
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool PR_FormControlOnChange_CUR_LOSINGCSIAMOUNT(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            scriptManager.RunScript("PR_BidCalc", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "CUR_LOSINGCSIAMOUNT");
            par_doCallingObject = doForm;
            return true;
        }

        public bool PR_FormControlOnChange_CUR_WINNINGCSIAMOUNT(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            scriptManager.RunScript("PR_BidCalc", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "CUR_WINNINGCSIAMOUNT");
            par_doCallingObject = doForm;
            return true;
        }

        public bool PR_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form oForm = (Form)par_doCallingObject;

            string sStage = Convert.ToString(oForm.Rowset.GetFieldVal("MLS_STAGE"));
            oForm.oVar.SetVar("STARTSTAGE", sStage);
            par_doCallingObject = oForm;
            return true;
        }

        public bool PR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form oForm = (Form)par_doCallingObject;

            string sStage = Convert.ToString(oForm.Rowset.GetFieldVal("MLS_STAGE"));
            string sStartStage = Convert.ToString(oForm.oVar.GetVar("STARTSTAGE"));
            bool bStageChanged = false; // = sStage <> sStartStage
                                        // FROM COMMENCE:
                                        // If FieldName = "Stage" Then
                                        // If Field(FieldName).Value = "Post Bid/Proposal - Closing" And strOrigStage <> "Post Bid/Proposal - Closing" Then
                                        // blnStageChange = "True"
                                        // End If
                                        // End If
            if (Convert.ToString(oForm.Rowset.GetFieldVal("MLS_STAGE")) == "Post Bid/Proposal - Closing" & sStartStage != "Post Bid/Proposal - Closing")
            {
                bStageChanged = true;
            }

            if (Convert.ToString(oForm.oVar.GetVar("CancelSave")) == "1")
            {
                // Reset variables so that save will run next time it is clicked.
                oForm.oVar.SetVar("CancelSave", "");
                return false;
            }

            if (Convert.ToString(oForm.oVar.GetVar("Project_PostBidProposal_Ran")) != "1")
            {
                oForm.oVar.SetVar("Project_PostBidProposal_Ran", "1");
                if (sStage == "Post Bid/Proposal - Closing" & bStageChanged)
                {
                    oForm.MessageBox("Would you like to update the 'TSI Bid Amount'?" + Constants.vbCrLf + Constants.vbCrLf + "Currently, it is '" + oForm.Rowset.GetFieldVal("CUR_REVENUE") + "'.", clC.SELL_MB_YESNO, null, "Yes", "No", null, null, "MessageBoxEvent", "MessageBoxEvent", null, oForm, null, "Yes", "No", null, null, "PR_FormOnSave_Post_TSIBIDCHANGED");
                    return true;
                }
            }

            if (sStage == "Post Bid/Proposal - Closing" & Convert.ToString(oForm.Rowset.GetFieldVal("TXT_QUOTENO")) == "")
            {
                oForm.MessageBox("Please Enter the quote number in the 'Quote #' field.");
                oForm.MoveToTab(0, "TXT_QUOTENO");
                return false;
            }

            string sStatus = Convert.ToString(oForm.Rowset.GetFieldVal("MLS_STATUS"));
            if (Convert.ToString(oForm.Rowset.GetFieldVal("LNK_WINNINGCSI_CO")) == "" & (sStatus == "Won-In House" | sStatus == "Lost"))
            {
                oForm.MessageBox("Please connect the winning CSI company in the 'Winning CSI' cnnection field.");
                oForm.MoveToTab(13, "LNK_WINNINGCSI_CO");
                return false;
            }
            if (Convert.ToString(oForm.Rowset.GetFieldVal("LNK_LOSINGCSI_CO")) == "" & (sStatus == "Won-In House" | sStatus == "Lost"))
            {
                oForm.MessageBox("Please connect the winning CSI company in the 'Next Highest CSI' cnnection field.");
                oForm.MoveToTab(13, "LNK_LOSINGCSI_CO");
                return false;
            }

            decimal cValueFld;
            cValueFld = Convert.ToDecimal(oForm.Rowset.GetFieldVal("CUR_WINNINGCSIAMOUNT", 2));
            if (!goTR.IsNumber(cValueFld.ToString()))
            {
                cValueFld = 0;
            }
            if (cValueFld == 0 & oForm.Rowset.GetFieldVal("LNK_WINNINGCSI_CO") != "")
            {
                oForm.MessageBox("Please enter the amount of the winning CSI bid.");
                oForm.MoveToTab(13, "CUR_WINNINGCSIAMOUNT");
                return false;
            }
            // TLD 4/5/2011 -------Commented, assume this won't happen unless status is lost?
            // They now want us to write the cur_revenue field to the cur_losingcsiamount
            // field if status is lost on save
            // cValueFld = oForm.Rowset.GetFieldVal("CUR_LOSINGCSIAMOUNT", 2)
            // If Not goTR.IsNumber(cValueFld) Then cValueFld = 0
            // If cValueFld = 0 And oForm.Rowset.GetFieldVal("LNK_LOSINGCSI_CO") <> "" Then
            // oForm.MessageBox("Please enter the amount of the next highest CSI bid.")
            // oForm.MoveToTab(13, "CUR_LOSINGCSIAMOUNT")
            // Return False
            // End If
            if (sStatus == "Lost")
                oForm.doRS.SetFieldVal("CUR_LOSINGCSIAMOUNT", oForm.doRS.GetFieldVal("CUR_Revenue", 2), 2);
            // TLD 4/5/2011 -------Commented, assume this won't happen unless status is lost?

            if (oForm.Rowset.GetFieldVal("DTE_Q90") == "" & sStatus == "Won-In House")
            {
                oForm.MessageBox("The status for this project is Won-In House, but there is no PO Date." + Constants.vbCrLf + Constants.vbCrLf + "Would you like to update the 'PO Date' field?", clC.SELL_MB_YESNO, null, null, null, null, null, "PR_UpdatePODate");
                return false;
            }

            // TLD 9/17/2010 If PO Date is blank, fill with three months from Bid Proposal Date
            // if not blank
            // PO Date is blank
            if (Convert.ToDateTime(oForm.doRS.GetFieldVal("DTE_Q90", 2)) == goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, "", "", ref par_iValid, "|", false))
            {
                // Bid Proposal Date NOT blank
                if (Convert.ToDateTime(oForm.doRS.GetFieldVal("DTE_Q50", 2)) != goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, "", "", ref par_iValid, "|", false))
                // not blank, creates todo
                {
                    DateTime _dtr = Convert.ToDateTime(oForm.doRS.GetFieldVal("DTE_Q50", 2));
                    oForm.doRS.SetFieldVal("DTE_Q90", goTR.AddMonth(ref _dtr, 3), 2);
                }
            }

            scriptManager.RunScript("PR_BidCalc", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            par_doCallingObject = oForm;
            return true;
        }

        public bool PR_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Field("Value Index").Value = CCur(Field("Probability").Value * Field("Revenue").Value / 100)

            clRowSet doRS = (clRowSet)par_doCallingObject;

            decimal cValueFld = default(decimal);
            cValueFld = Convert.ToDecimal(doRS.GetFieldVal("CUR_REVENUE", 2));
            if (!goTR.IsNumber(cValueFld.ToString()))
            {
                cValueFld = 0;
            }
            doRS.SetFieldVal("CUR_VALUEINDEX", goTR.StringToNum(doRS.GetFieldVal("SI__PROBABILITY"), "", ref par_iValid, "") * Convert.ToDouble(cValueFld / (decimal)100));

            // If Field("Close Date").Value <> "" Then
            // CloseDate = CStr(Field("Close Date").Value)
            // MonthCloseNum = Month(CloseDate)
            // MonthClose = MonthName(MonthCloseNum)
            // 'Add 0 for months 1-9 so they would sort properly in views
            // If Len(MonthCloseNum) = 1 Then MonthCloseNum = "0" & MonthCloseNum
            // Form.Field("Month Close").Value = CStr(MonthCloseNum) & " " & MonthClose
            // End If

            if (Convert.ToString(doRS.GetFieldVal("DTE_Q90")) != "")
            {

                string sCloseDate = doRS.GetFieldVal("DTE_Q90").ToString();
                int iMonth = Convert.ToDateTime(sCloseDate).Month;
                string sMonth = (iMonth).ToString("MMM");
                if (iMonth < 10)
                {
                    doRS.SetFieldVal("TXT_MONTHCLOSE", "0" + iMonth.ToString() + " " + sMonth);
                }
                else
                {
                    doRS.SetFieldVal("TXT_MONTHCLOSE", iMonth.ToString() + " " + sMonth);
                }
            }

            return true;
        }


        public bool PR_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS;
            doRS = (clRowSet)par_doCallingObject;

            // clears TXT_STAGETEXT field, then updates it from MLS_STAGE
            doRS.SetFieldVal("TXT_STAGETEXT", "");
            doRS.SetFieldVal("TXT_STAGETEXT", doRS.GetFieldVal("MLS_STAGE", 1));
            par_doCallingObject = doRS;
            return true;
        }

        public bool PR_UpdateBidAmount(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form oForm = (Form)par_doCallingObject;
            try
            {

                oForm.oVar.SetVar("STARTSTAGE", oForm.Rowset.GetFieldVal("MLS_STAGE"));
                oForm.MoveToTab(13, "CUR_REVENUE");
            }

            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    goErr.SetError(ex, 45105, sProc);
                }
            }
            par_doCallingObject = oForm;
            return true;
        }

        public bool PR_UpdatePODate(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form oForm = (Form)par_doCallingObject;
            try
            {

                oForm.MoveToTab(3, "DTE_Q90");
            }

            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = oForm;
            return true;
        }

        public bool PR_FormControlOnChange_MLS_STATUS_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // For notes on how to create a custom script, see clScrMng. ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form oForm = (Form)par_doCallingObject;

            string sStatus = Convert.ToString(oForm.Rowset.GetFieldVal("MLS_STATUS"));
            string sTSI = "5a8a3517-f7e7-4e3f-434f-9a8100f53fe7"; // GID_ID of 'Technical Systems Inc.' CO record.

            if (sStatus == "Won-In House")
            {
                oForm.Rowset.SetLinkVal("LNK_WINNINGCSI_CO", sTSI);
                oForm.Rowset.SetFieldVal("CUR_WINNINGCSIAMOUNT", oForm.Rowset.GetFieldVal("CUR_REVENUE"));
            }
            else if (sStatus == "Lost")
            {
                oForm.Rowset.SetLinkVal("LNK_LOSINGCSI_CO", sTSI);
                oForm.Rowset.SetFieldVal("CUR_LOSINGCSIAMOUNT", oForm.Rowset.GetFieldVal("CUR_REVENUE"));
            }
            par_doCallingObject = oForm;
            return true;
        }

        public bool GenerateSysName_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {


                // Field("Name").Value = Left(Field("Description").Value, 30) & " " & DateFld & " " & Field("City").Value & " " & Field("State").Value

                clRowSet doRS = (clRowSet)par_doCallingObject;
                string sFileName = doRS.GetFileName();

                switch (Strings.UCase(sFileName))
                {
                    case "PR":
                        {
                            if (!doRS.IsLoaded("MMO_DESCRIPTION"))
                                goErr.SetError(35103, sProc, null, sFileName + ".MMO_DESCRIPTION");
                            if (!doRS.IsLoaded("DTE_TIME"))
                                goErr.SetError(35103, sProc, null, sFileName + ".DTE_TIME");
                            if (!doRS.IsLoaded("TXT_CITY"))
                                goErr.SetError(35103, sProc, null, sFileName + ".TXT_CITY");
                            if (!doRS.IsLoaded("TXT_STATE"))
                                goErr.SetError(35103, sProc, null, sFileName + ".TXT_STATE");

                            string sName = Strings.Replace(doRS.GetFieldVal("MMO_DESCRIPTION", 0, 30).ToString(), Constants.vbCrLf, "  ") + " " + doRS.GetFieldVal("DTE_TIME") + " " + doRS.GetFieldVal("TXT_CITY") + " " + doRS.GetFieldVal("TXT_STATE");

                            par_oReturn = sName;
                            break;
                        }

                    case "HB":
                        {
                            if (!doRS.IsLoaded("TXT_HARDWAREBRANDNAME"))
                                goErr.SetError(35103, sProc, null, sFileName + ".TXT_HARDWAREBRANDNAME");

                            par_oReturn = doRS.GetFieldVal("TXT_HARDWAREBRANDNAME");
                            break;
                        }

                    case "PC":
                        {
                            if (!doRS.IsLoaded("TXT_PROCESSNAME"))
                                goErr.SetError(35103, sProc, null, sFileName + ".TXT_PROCESSNAME");

                            par_oReturn = doRS.GetFieldVal("TXT_PROCESSNAME");
                            break;
                        }
                }
            }

            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.ClearLinkAll("LNK_RELATED_MO");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
            }
        }

        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string Product = Convert.ToString(doForm.doRS.GetFieldVal("Lnk_for_pd%%txt_productname"));
            //string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_MO%%GID_ID"));

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_RELATED_PD,LNK_RELATED_MO,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX,txt_productname", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            //rsOL.SetFieldVal("LNK_RELATED_MO", MO_Gid);
            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);
            rsOL.SetFieldVal("txt_productname", Product);

            if (rsOL.Commit() != 1)
            {
                return false;
            }

            iLineCount = iLineCount + 1;

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }
        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;



            //par_doCallingObject = doForm;
            return true;
        }

        public bool OL_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.oVar.SetVar("OppID", doRS.GetFieldVal("LNK_IN_OP"));

            par_doCallingObject = doRS;

            return true;
        }

        public bool OL_RecordAfterDelete_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_s2 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sID = doRS.oVar.GetVar("OppID").ToString();

            if (!string.IsNullOrEmpty(sID))
            {
                clRowSet doOPLines = default(clRowSet);

                //Create a rowset of quote lines linked to the deleted quote.
                doOPLines = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo,Gid_id", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    StringBuilder sbQuery = new StringBuilder();
                    for (int i = 1; i <= doOPLines.Count(); i++)
                    {
                        double iLineno = Convert.ToDouble(doOPLines.GetFieldVal("SR__LineNo"));
                        string sGid_id = Convert.ToString(doOPLines.GetFieldVal("Gid_ID"));
                        string sTemp = "#" + goTR.Pad(i.ToString(), 6, " ", "L", true, "R");
                        sbQuery.AppendLine("Update OL Set SR__LineNo='" + i.ToString() + "',sys_name='" + sTemp + "' where Gid_id='" + sGid_id + "' AND SR__LineNo = '" + iLineno.ToString() + "'");

                        if (doOPLines.GetNext() == 0)
                        {
                            break;
                        }

                    }
                    if (!string.IsNullOrEmpty(sbQuery.ToString()))
                    {
                        bool bretval = goData.RunSQLQuery(sbQuery.ToString());
                    }

                }

                doOPLines = null;

            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));
            string Product = Convert.ToString(doRS.GetFieldVal("lnk_related_pd%%TXT_productName"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);
            doRS.SetFieldVal("txt_productname", Product);

            //mobile line no code
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    iLineCount = doOPLines.Count();
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool GenerateSysName_Pre(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = "clScripts:GenerateSysName";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS 11152016
            // par_bRunNext = False
            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";
            clRowSet doLink;
            int iLen;
            switch (Strings.UCase(sFileName))
            {
                case "CT"     // ==> Country
               :
                    {
                        par_bRunNext = false;

                        sTemp = doRS.GetFieldVal("TXT_COUNTRYNAME").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_COUNTRYCODE").ToString();
                        if (sTemp2 == "")
                            sTemp2 = "?";

                        sResult = sTemp + " (" + sTemp2 + ")";
                        break;
                    }

                case "SA":   // ==> State         :
                    {
                        par_bRunNext = false;

                        sTemp = doRS.GetFieldVal("TXT_STATENAME").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_CODE").ToString();
                        if (sTemp2 == "")
                            sTemp2 = "?";

                        sResult = sTemp + " (" + sTemp2 + ")";
                        break;
                    }

                case "CY"   // ==> Customer Type
         :
                    {
                        par_bRunNext = false;

                        sTemp = doRS.GetFieldVal("TXT_CUSTOMERTYPENAME").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_CODE").ToString();
                        if (sTemp2 == "")
                            sTemp2 = "?";

                        sResult = sTemp + " (" + sTemp2 + ")";
                        break;
                    }

                case "OP":
                    {
                        // ==> OPP NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+LNK_For_Company%%TXT_CompanyName+" "+...
                        // LNK_For_Product%%TXT_ProductName+" "+CUR_Value
                        // OPP		<For - Company - TXT_CompanyName> <For - Product - TXT_ProductName> CUR_ValueIndex (MLS_Status)  
                        // OPP-COMPANY-0						OPP-PRODUCT-0

                        par_bRunNext = false;
                        if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                        {
                            goErr.SetError(35103, sProc, null, sFileName + ".LNK_CreditedTo_US");

                        }
                        if (!doRS.IsLoaded("LNK_For_CO"))
                        {
                            goErr.SetError(35103, sProc, null, sFileName + ".LNK_For_CO");

                        }
                        if (!doRS.IsLoaded("LNK_For_PD"))
                        {
                            goErr.SetError(35103, sProc, null, sFileName + ".LNK_For_PD");

                        }
                        if (!doRS.IsLoaded("DTT_Time"))
                        {
                            goErr.SetError(35103, sProc, null, sFileName + ".DTT_Time");

                        }
                        if (!doRS.IsLoaded("CUR_Value"))
                        {
                            goErr.SetError(35103, sProc, null, sFileName + ".CUR_Value");

                        }
                        if (!doRS.IsLoaded("MLS_Status"))
                        {
                            goErr.SetError(35103, sProc, null, sFileName + ".MLS_Status");

                        }


                        // LNK_CreditedTo_US%%TXT_Code
                        sTemp = Convert.ToString(doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, true, 1));
                        if (sTemp == "")
                            // No records linked
                            sTemp = "?";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", null, "TXT_Code", 1);
                            if (doLink.Count() > 0)
                                sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                            else
                                sTemp = "?";
                        }

                        // LNK_For_CO%%TXT_CompanyName
                        sTemp2 = Convert.ToString(doRS.GetFieldVal("LNK_For_CO", 1, 1, false, 1));
                        if (sTemp2 == "")
                            // No records linked
                            sTemp2 = "";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", null, "TXT_CompanyName", 1);
                            if (doLink.Count() > 0)
                                sTemp2 = doLink.GetFieldVal("TXT_CompanyName", 1, 22).ToString();
                            else
                                sTemp2 = "";
                        }

                        // LNK_For_Product%%TXT_ProductName
                        sTemp3 = doRS.GetFieldVal("LNK_For_PD", 1, 1, false, 1).ToString();
                        if (sTemp3 == "")
                            // No records linked
                            sTemp3 = "";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("PD", 3, "GID_ID='" + sTemp3 + "'", null, "TXT_ProductName", 1);
                            if (doLink.Count() > 0)
                                sTemp3 = doLink.GetFieldVal("TXT_ProductName", 1, 14).ToString();
                            else
                                sTemp3 = "";
                        }
                        int par_iValid = 0;
                        string par_sdlim = "";
                        DateTime sDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM));
                        sResult = sTemp2 + " " + Strings.Left(goTR.DateTimeToSysString(goTR.UTC_LocalToUTC(ref sDate), ref par_iValid, ref par_sdlim), 10) + " GMT " + sTemp + " " + sTemp3 + " " + goTR.Pad(doRS.GetFieldVal("CUR_Value").ToString(), 11, " ", "L");
                        sResult += " [" + doRS.GetFieldVal("MLS_STATUS", 1, 8) + "]";
                        break;
                    }
                case "QT":
                    //==> QUOTE NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+
                    //					LNK_To_Company%%TXT_CompanyName+" "+CUR_Total
                    par_bRunNext = false;
                    if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("LNK_To_CO"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_To_CO");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("DTT_Time"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".DTT_Time");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_QuoteNo"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_QuoteNo");
                        ///35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("CUR_Total"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".CUR_Total");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("MLS_STATUS"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".MLS_STATUS");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    //LNK_CreditedTo_US%%TXT_Code
                    sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp))
                    {
                        //No records linked
                        sTemp = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                        }
                        else
                        {
                            sTemp = "?";
                        }
                    }

                    //LNK_To_CO%%TXT_CompanyName
                    sTemp2 = doRS.GetFieldVal("LNK_To_CO", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp2))
                    {
                        //No records linked
                        sTemp2 = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CompanyName", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp2 = doLink.GetFieldVal("TXT_CompanyName").ToString();
                        }
                        else
                        {
                            sTemp2 = "?";
                        }
                    }
                    sResult = goTR.Pad(sTemp2, 16, "", "R", true) + " " + goTR.Pad(sTemp3, 14, "", "R", true) + " " + goTR.Pad(sTemp, 4, "", "R", true) + " [" + goTR.Pad(doRS.GetFieldVal("TXT_QuoteNo").ToString(), 14, "", "R", true) + "] " + goTR.Pad(doRS.GetFieldVal("CUR_Total").ToString(), 13, "", "L", true) + " [" + doRS.GetFieldVal("MLS_STATUS", 0, 10).ToString() + "]";

                    break;

            }

            sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
            sResult = goTR.Replace(sResult, Strings.Chr(10).ToString(), " ");
            sResult = goTR.Replace(sResult, Strings.Chr(13).ToString(), " ");
            sResult = goTR.Replace(sResult, Constants.vbTab, " ");

            // 1/28/15 Manmeet added replace for |
            sResult = goTR.Replace(sResult, "|", " ");

            par_oReturn = sResult;
            par_doCallingObject = doRS;
            return true;
        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //string sGidId = Convert.ToString(doRS.GetFieldVal("Gid_id"));

            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            //clRowSet rsOL = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + sGidId + "'", "TXT_OpportunityLineName", "CUR_VALUE|SUM,CUR_VALUEINDEX|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double cur_Value = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double cur_ValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("cur_ValueIndex|SUM", 2));

                doRS.SetFieldVal("CUR_Value", cur_Value);
                doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);
            }

            par_doCallingObject = doRS;
            par_bRunNext = false;
            return true;
        }
        private static void Refresh_OPPTotal(clRowSet doForm)
        {
            string sGidId = Convert.ToString(doForm.GetFieldVal("Gid_id"));

            clRowSet rsOL = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + sGidId + "' ", "LNK_IN_OP", "CUR_VALUE|SUM,CUR_VALUEINDEX|SUM");

            double curTotalAmt = 0.0;
            double curTotalAmt1 = 0.0;
            if (rsOL.GetFirst() == 1)
            {
                curTotalAmt = Convert.ToDouble(rsOL.GetFieldVal("CUR_VALUE|SUM", 2));
                curTotalAmt1 = Convert.ToDouble(rsOL.GetFieldVal("CUR_VALUEINDEX|SUM", 2));
                doForm.SetFieldVal("CUR_VALUE", curTotalAmt, 2);
                doForm.SetFieldVal("CUR_VALUEINDEX", curTotalAmt1, 2);
            }
            else
            {
                doForm.SetFieldVal("CUR_VALUE", 0.0);
                doForm.SetFieldVal("CUR_VALUEINDEX", 0.0);
            }
        }
        public bool OP_FormControlOnChange_BTN_CALCPROBABILITY_1_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_OPPTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            par_doCallingObject = doForm;
            return true;

        }
        public bool Opp_CreateActLog_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // 12/22/2004 RAH: turned over to MI
            // 2004/12/22 10:29:34 MAR Edited. SetLinkVals cause an error on line 37 of SetLinkVal: incorrect type.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            // PURPOSE:
            // TLD 6/11/2010 Sends alert once resulting AC log is created
            // from journal entry.  Couldn't get new AC type journal to be
            // recognized when using an OPP_CreateActLog_Post
            // Run from enforce only in non-CREATION (MODIF) mode.
            // If it is run in CREATION mode, the new Activity will not be linked to the original Opp.
            // RETURNS:
            // 1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.

            // TLD 6/11/2010 Prevent main from running
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sNotes = "";
            string sWork = "";
            long lWork = 0;
            string sMessage;

            // 'If answered Yes on MB to create a journal, then go directly to script that creates journal.
            // If UCase(par_s1) = "YES" Then
            // GoTo CREATEACTLOG
            // End If


            if (Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL").ToString()) <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))
                return true;
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Opportunity is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);
                doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");
                // Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                return true;
            }

            doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");

            // sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            // CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            // hard returns in the journal field
            sWork = doForm.oVar.GetVar("JournalWithHardReturns").ToString();
            // CS 2/4/10
            if (sWork == "")
                return true; // We didn't hit MessageBoxEvent from entering a journal note.

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, null, null, null, -1, null, null, null, null, null, doForm.doRS.bBypassValidation);

            // 'sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            // 'CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            // 'hard returns in the journal field
            // sWork = doForm.oVar.GetVar("JournalWithHardReturns")

            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, Convert.ToInt32(lWork));
            sNotes = sNotes + "== Created from Opportunity '" + doForm.doRS.GetFieldVal("SYS_Name") + "'";
            // TLD 4/25/2011 Notes tab & field removed from form, but left this just in case
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            doNew.SetFieldVal("MLS_Status", 1, 2);     // Completed
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            // CS doNew.SetFieldVal("TME_ENDTIME", doForm.doRS.GetFieldVal("TME_Time"))
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");

            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));

            // In the code below Paul added IsObjectAssigned tests because SetLinkVals weren't working in some cases.
            // ==> Remove the IsObjectAssigned tests.

            doLink = doForm.doRS.GetLinkVal("LNK_ORIGINATEDBY_CN", ref doLink, true, 0, -1, "", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (10).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_Related_CN", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (11).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If

            doLink = doForm.doRS.GetLinkVal("LNK_FOR_PD", ref doLink, true, 0, -1, "", ref oTable);    // PJ Changed link name from 'LNK_RELATED_PRODUCT' to 'LNK_FOR_PRODUCT'
                                                                                                       // If Not goP.IsObjectAssigned(doLink) Then
                                                                                                       // goP.TraceLine("doLink (LNK_Related_PD) is not assigned (12).", "", sProc)
                                                                                                       // '	goErr.DisplayLastError()
                                                                                                       // End If
            doNew.SetLinkVal("LNK_Related_PD", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_PD) is not assigned (13).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If

            doLink = doForm.doRS.GetLinkVal("LNK_Related_GR", ref doLink, true, 0, -1, "", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_GR) is not assigned.", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_Related_GR", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_GROUP) is not assigned (2).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If

            doLink = doForm.doRS.GetLinkVal("LNK_FOR_CO", ref doLink, true, 0, -1, "", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_FOR_CO) is not assigned (3).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_RELATED_CO", doLink);
            // if goErr.GetLastError()<>"E00000" then
            // goErr.DisplayLastError()
            // End If

            doNew.SetFieldVal("MLS_TYPE", 31, 2);      // Journal
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(doNew.GetFieldVal("MMO_HISTORY").ToString(), "Created."));
            doNew.SetFieldVal("LNK_RELATED_OP", doForm.GetRecordID());

            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;
                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);

                goLog.Log("MessageBoxEvent", doForm.oVar.GetVar("ScriptMessages").ToString(), 1, false, true);
                return false;
            }
            else
            {
                // TLD 6/21/2011 New Tower Wet & New Dry Cooling no longer exit
                // New Tower Wet is Now Field Erected, so changed to that
                // TLD 6/11/2010 if successful, send alert
                // Only send alert if Credited to User is NOT current user
                // and User is a member of New Tower Wet or New Dry Cooling Divisions
                string sCreditedUSID = doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%GID_ID").ToString();
                if (sCreditedUSID != goP.GetMe("ID"))
                {
                    // Get RS for US
                    clRowSet doUSRS = new clRowSet("US", 3, "GID_ID='" + sCreditedUSID + "'", null, "LNK_RELATED_DV%%TXT_DIVISIONNAME", 1);
                    if (doUSRS.GetFirst() == 1)
                    {
                        // If doUSRS.GetFieldVal("LNK_RELATED_DV%%TXT_DIVISIONNAME") = "New Tower Wet" Or doUSRS.GetFieldVal("LNK_RELATED_DV%%TXT_DIVISIONNAME") = "New Dry Cooling" Then
                        string sDivisions = doUSRS.GetFieldVal("LNK_RELATED_DV%%TXT_DIVISIONNAME").ToString();
                        if (Strings.InStr(sDivisions, "Field Erected") > 0)
                        {
                            // Send alert
                            string sID = doNew.GetFieldVal("GID_ID").ToString();
                            goUI.AddAlert("OPP Journal Alert", clC.SELL_ALT_OPENRECORD, sID, doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US").ToString(), "Activity16.gif");
                        }
                    }
                    doUSRS = null;
                }
            }

            doNew = null;
            doLink = null;
            par_doCallingObject = doForm;
            return true;
        }
    }
}
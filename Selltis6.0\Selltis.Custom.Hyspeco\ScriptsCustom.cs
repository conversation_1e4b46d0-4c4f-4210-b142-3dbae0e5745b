﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.IO;
using System.Net;
using Newtonsoft.Json;
using System.Web.Script.Serialization;
using System.Xml;
using System.Text.RegularExpressions;
using System.Diagnostics;
using Selltis.BusinessLogic;
using Selltis.Core;

namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clProject goP;
        private clLog goLog;
        private clError goErr;
        private clPerm goPerm;
        private ClUI goUI;
        ScriptManager scriptManager = new ScriptManager();

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        public string sError;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();
            Util.SetSessionValue("SkipQLSpecificLogic", "Y");
        }
        public ScriptsCustom()
        {
            Initialize();
        }



        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //*** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //try
            //{




            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }

            //}

            return true;
        }

        public bool AC_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            clRowSet doRS = (clRowSet)par_doCallingObject;
            //AD ******** TKT#1956 : Update Last Date for Company
            //goScr.RunScript("UpdateLastDateinCO", doRS);
            scriptManager.RunScript("UpdateLastDateinCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doRS;
            return true;
        }
        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CalcQuoteTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //SB Tkt#3299 01202020 Merge Functionality
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt64(doForm.doRS.GetFieldVal("CHK_Merged", 2).ToString()) == 0)
                {
                    //TLD 8/10/2011 Updated Var
                    if (doForm.oVar.GetVar("CN_Merge").ToString() != "1")
                    {
                        doForm.oVar.SetVar("CN_Merge", "1");
                        //TLD 3/6/2012 Mod to not allow user to merge record to itself
                        //TLD 11/9/2011 Don't allow merge of contact to itself
                        if (doForm.doRS.GetFieldVal("GID_ID").ToString() == doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID").ToString())
                        {
                            //TLD 6/4/2013 Update for generic Merge
                            //doForm.MessageBox("You cannot merge a contact to itself.  Please select a different merge to contact.", clC.SELL_MB_OK, , , , , , "MessageBoxEvent", , , doForm, , "OK", , , , "MergeCNFail")
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CN", "MergeFail");
                        }
                        else
                        {
                            //TLD 6/4/2013 Update for generic Merge
                            //doForm.MessageBox("This contact will be merged to the target contact, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") & "'. Blank fields on the target contact will be filled from this contact record and all links will be copied to the target contact. Are you sure you want to merge this contact?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "MergeCN")
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name").ToString() + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CN", "Merge");
                        }
                    }
                }
            }

            par_doCallingObject = doForm;

            return true;
        }
        public bool CN_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //AD 06262017 Ticket#1929: Make "TXT_CURANDPOT" Field GrayedOut as it is being Calculated.
            Form doForm = (Form)par_doCallingObject;
            doForm.SetControlState("TXT_CURANDPOT", 4);
            return true;
        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //SB Tkt#3299 01202020 Merge Functionality at end of _Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToInt64(doForm.doRS.GetFieldVal("CHK_Merged", 2).ToString()) == 0)
                {
                    //SB 01202020 Changed var
                    if (doForm.oVar.GetVar("CO_Merge").ToString() != "1")
                    {
                        //doForm.oVar.SetVar("CO_Merge", "1") 'set in messagebox
                        //SB 01202020 Mod to not allow user to merge record to itself
                        if (doForm.doRS.GetFieldVal("GID_ID").ToString() == doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID").ToString())
                        {
                            //SB 01202020 Change to generic merge
                            //doForm.MessageBox("You cannot merge a company to itself.  Please select a different merge to company.", clC.SELL_MB_OK, , , , , , "MessageBoxEvent", , , doForm, , "OK", , , , "MergeCOFail")
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CO", "MergeFail");
                        }
                        else
                        {
                            //SB 01202020 Change to generic merge
                            //doForm.MessageBox("This company will be merged to the target company, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") & "'. Blank fields on the target company will be filled from this company record and all links will be copied to the target company. Are you sure you want to merge this company?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "MergeCO")
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name").ToString() + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CO", "Merge");
                        }
                    }
                }
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool CO_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //Ticket 1956 ********
            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sCurVol = "";
            string sPotVol = "";
            int iCurCount = Convert.ToInt32(doRS.GetLinkCount("LNK_CURRENT_PD"));
            int iPotCount = Convert.ToInt32(doRS.GetLinkCount("LNK_POTENTIAL_PD"));
            //Ticket 1929
            //Target Account Matrix Profiling
            //Copy LNK_Current_PD to LNK_Potential_PD
            doRS.SetFieldVal("LNK_POTENTIAL_PD", doRS.GetFieldVal("LNK_CURRENT_PD"));

            //Record total selections from LNK_Current_PD and LNK_Potential_PD
            doRS.SetFieldVal("INT_CURCOUNT", iCurCount, 2);
            doRS.SetFieldVal("INT_POTCOUNT", iPotCount, 2);

            //Calculate Potential Percentage & Potential Portfolio
            clRowSet doPDRS = new clRowSet("PD", 3, "", "", "GID_ID");
            int iPDCount = Convert.ToInt32(doPDRS.Count());
            if (iPDCount != 0)
            {
                if (iPotCount != 0)
                {
                    doRS.SetFieldVal("SR__POTENTIALPERC", (iCurCount / iPotCount) * 100, 2);
                }
                doRS.SetFieldVal("SR__POTENTIALPORTFOLIO", (iPotCount / iPDCount) * 100, 2);

                doRS.SetFieldVal("SR__ProdLinePot", (iPotCount - iCurCount), 2);
                sCurVol = (doRS.GetFieldVal("MLS_CURVOLUME") == null) ? "" : Strings.Left(doRS.GetFieldVal("MLS_CURVOLUME").ToString(), 1);
                sPotVol = (doRS.GetFieldVal("MLS_POTVOLUME") == null) ? "" : Strings.Left(doRS.GetFieldVal("MLS_POTVOLUME").ToString(), 1);
                //for now record a "Z" if it is make selection
                if (sCurVol == "<")
                {
                    sCurVol = "Z";
                }
                //for now record a "Z" if it is make selection
                if (sPotVol == "<")
                {
                    sPotVol = "Z";
                }

                //set field to cur & pot
                doRS.SetFieldVal("TXT_CURANDPOT", sCurVol + sPotVol);
            }

            //---------- Target Account -----------------
            //Set Product Potential Quadrant
            double rTotalPortfolio = (doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2) == null) ? 0 : Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2));
            double rPotentialProduct = (doRS.GetFieldVal("SR__POTENTIALPERC", 2) == null) ? 0 : Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPERC", 2));

            if (rTotalPortfolio >= 51 & rTotalPortfolio <= 100)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                {
                    //Set to 1
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "1");
                }

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                {
                    //Set to 3
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "3");
                }
            }

            if (rTotalPortfolio >= 0 & rTotalPortfolio <= 50)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                {
                    //Set to 2
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "2");
                }

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                {
                    //Set to 4
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "4");
                }
            }

            //Because COs are updated nightly to set custom
            //date fields, need to write to custom mod time and mod by fields
            //AutoCOUpdate does NOT run recordonsave
            doRS.SetFieldVal("TXT_CusModBy", goP.GetMe("CODE"));
            doRS.SetFieldVal("DTT_CusModTime", "Today|Now");
            //---------End Target Account Matrix Profiling

            par_doCallingObject = doRS;

            return true;
        }
        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_sFileName: file for which to return the sort.
            //par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //PURPOSE:
            //	Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            //       By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            //       add CASEs for them here. To not override the default sort, par_oReturn must be "".
            //       IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            //       with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            //       the sort likely should be ASC.
            //RETURNS:
            //		Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            //Select Case (par_sFileName)
            //    Case "AA"
            //        'This is a reverse sort, typically used for datetime fields
            //        If par_sReverseDirection = "1" Then
            //            sResult = "SYS_NAME ASC"
            //        Else
            //            sResult = "SYS_NAME DESC"
            //        End If
            //    Case "BB"
            //        'Reverse sort on Creation datetime
            //        If par_sReverseDirection = "1" Then
            //            sResult = "DTT_CREATIONTIME ASC"
            //        Else
            //            sResult = "DTT_CREATIONTIME DESC"
            //        End If
            //        'Case Else
            //        '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            //        '    'it is not needed here
            //End Select

            par_oReturn = sResult;

            return true;

        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__HYSPECOPROBPER", "LABELCOLOR", color);
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;


            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OPPORTUNITYLINESName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));

                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

            }
            par_doCallingObject = doRS;
            par_bRunNext = false;
            return true;
        }
        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //Refill Vendor for the Product

            doRS.ClearLinkAll("LNK_RELATED_VE");
            doRS.SetFieldVal("LNK_RELATED_VE", doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_VE"));
            //AD ******** TKT#1956 : Update Last Date for Company
            scriptManager.RunScript("UpdateLastDateinCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doRS;
            return true;
        }
        public bool OP_FormControlOnChange_LNK_FOR_PD_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Refill Vendor for the Product

            doForm.doRS.ClearLinkAll("LNK_RELATED_VE");
            doForm.doRS.SetFieldVal("LNK_RELATED_VE", doForm.doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_VE"));

            par_doCallingObject = doForm;
            return true;
        }
        //public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //{
        //    //par_doCallingObject: Form object calling this script. Do not delete in script!
        //    //par_doArray: Unused.
        //    //par_s1: Unused.
        //    //par_s2 to par_s5: Unused.
        //    //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        //    //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        //    //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        //    string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    return true;
        //}
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        //public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //{
        //    //par_doCallingObject: Form object calling this script. Do not delete in script!
        //    //par_doArray: Unused.
        //    //par_s1: Unused.
        //    //par_s2 to par_s5: Unused.
        //    //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        //    //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        //    //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        //    string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    return true;
        //}
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //AD ******** TKT#1956 : Update Last Date for Company
            scriptManager.RunScript("UpdateLastDateinCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            return true;
        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Quotline_CalcTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        //AD TASK 1929 11082017
        public bool UpdateLastDateinCO(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //AD 07072017 TKT#1559 : Update Last Date for Company
            //Purpose Update LastDate for OP, QT, LEAD, AC, AC Sales and also in CO

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sFile = doRS.GetFileName();
            string sField = "";
            //field to update in company/contact
            string sFieldLabel = "";
            DateTime dDate = default(DateTime);
            string sCOLinkFieldName = "";

            //Need to update only for new records created
            //new Record
            if (doRS.GetInfo("TYPE") == "2")
            {

                if (sFile == "AC")
                {
                    sCOLinkFieldName = "LNK_RELATED_CO";
                    //If Activity is of type Sales Visit, update linked Related Company.Last AC Sales date
                    //sales visit
                    if (doRS.GetFieldVal("MLS_TYPE", 2) != null && Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2)) == 11)
                    {
                        sField = "DTT_LASTACSALES";
                    }
                    else
                    {
                        //Activity is not of type sales visit
                        sField = "DTT_LASTAC";
                    }

                    //LEAD Activity
                    if (doRS.GetFieldVal("MLS_PURPOSE", 2) != null && Convert.ToInt32(doRS.GetFieldVal("MLS_PURPOSE", 2)) == 8)
                    {
                        sField = "DTT_LASTLEAD";
                    }

                }
                else if (sFile == "OP")
                {
                    sCOLinkFieldName = "LNK_FOR_CO";
                    sField = "DTT_LASTOP";

                    //ElseIf sFile = "QT" Then
                    //    sCOLinkFieldName = "LNK_TO_CO"
                    //    sField = "DTT_LASTQT"

                }

                dDate = (doRS.GetFieldVal("DTT_CreationTime", 2) == null) ? DateTime.MinValue : Convert.ToDateTime(doRS.GetFieldVal("DTT_CreationTime", 2));

                if (!string.IsNullOrEmpty(sField))
                {
                    sFieldLabel = goData.GetFieldLabel("CO", sField);
                    //Update CO Record
                    if (doRS.GetLinkCount(sCOLinkFieldName) > 0)
                    {
                        clArray doCompanies = (clArray)doRS.GetFieldVal(sCOLinkFieldName, 2);
                        //Bipass reconsave and validation to speed up AC save
                        for (int i = 1; i <= doCompanies.GetDimension(); i++)
                        {
                            clRowSet doRSCompany = new clRowSet("CO", 1, "GID_ID='" + doCompanies.GetItem(i) + "'", "", sField, -1, "", "", "", "", "", true, true);
                            if (doRSCompany.GetFirst() == 1)
                            {
                                doRSCompany.SetFieldVal(sField, dDate, 2);
                                //log error but proceed
                                if (doRSCompany.Commit() == 0)
                                {
                                    goLog.Log(sProc, "CO update of last " + sFieldLabel + " field failed for CO " + ((doRSCompany.GetFieldVal("TXT_CompanyName") == null) ? "" : doRSCompany.GetFieldVal("TXT_CompanyName").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                }
                            }
                            doRSCompany = null;
                        }
                    }
                }

            }

            par_doCallingObject = doRS;
            return true;

        }
        //AD TASK 1929 11082017
        public bool AutoCOUpdate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.


            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //AD 07072017 TKT#1559 : Update Last Date for Company
            //AGE_D0D32818-BC80-4333-5858-A09800E45CDE
            //PURPOSE:
            //       Called by agent to update 3 custom date fields that
            //       record latest AC of type Sales Visit, latest OP and latest Quote
            //A_01_EXECUTE=AutoCOUpdate
            //A_01_OBJSHARED = 1
            //A_01_TYPE = RUNSCRIPT
            //A_ORDER=1,
            //ACTIONS=1,
            //ACTIVE = 1
            //E_TM_HOUR = 19 '7:00 PM
            //E_TM_INTERVAL = 3 'Every day
            //E_TM_MINUTE = 0 'top of hour
            //EVENT=TIMER
            //US_NAME=AutoCOUpdate
            //US_PURPOSE=Runs daily Update of Company records
            //SORTVALUE1=TIMER_ACTIVE

            long lBiID = 0;
            long lastBiId = 0;
            clRowSet doRS = default(clRowSet);
            clRowSet doNewRS = default(clRowSet);
            long iCount = 0;
            bool bUpdateCO = false;
            string par_sDelim = " ";
            string sNowDate = goTR.DateTimeToSysString(goTR.NowLocal(), ref par_iValid, ref par_sDelim);
            string sRecID = "";
            string sWOP = goMeta.PageRead("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED");
            int iFailedOther = 0;
            int iFailedPerm = 0;
            int iFailedTotal = 0;
            int iSuccess = 0;

            try
            {
                do
                {
                    doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT", 50, "", "", "", "", "", true, true, true);
                    iCount = iCount + doRS.Count();
                    if (doRS.GetFirst() == 1)
                    {
                        lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));

                        do
                        {
                            bUpdateCO = false;
                            //until set to true below
                            sRecID = (doRS.GetCurrentRecID() == null) ? "" : doRS.GetCurrentRecID().ToString();

                            //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                            if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                            {
                                doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            //TLD 5/18/2011 -----------Added to include date of latest OP
                            if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                            {
                                doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            //TLD 5/18/2011 -----------Added to include date of latest QT
                            if (doRS.GetLinkCount("LNK_Received_QT") > 0)
                            {
                                doNewRS = new clRowSet("QT", 3, "LNK_To_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }

                            //Update CO
                            if (bUpdateCO == true)
                            {
                                if (doRS.Commit() == 0)
                                {
                                    if (goErr.GetLastError("NUMBER") == "E47250")
                                    {
                                        //Commit failed b/c user has no permissions to edit record; log it, but proceed
                                        iFailedPerm = iFailedPerm + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " due to permissions.", 1, false, true);
                                        //testing
                                    }
                                    else
                                    {
                                        //Commit failed for some other reason.
                                        iFailedOther = iFailedOther + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                        //testing
                                    }
                                }
                            }
                            if (doRS.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                        lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                        //get last BI__ID processed
                    }
                    else
                    {
                        break; // TODO: might not be correct. Was : Exit Do
                    }
                } while (true);

                //Check once more for any newly added records
                doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT", -1, "", "", "", "", "", true, true, true);
                iCount = iCount + doRS.Count();
                if (doRS.GetFirst() == 1)
                {
                    do
                    {
                        bUpdateCO = false;
                        //until set to true below
                        sRecID = (doRS.GetCurrentRecID() == null) ? "" : doRS.GetCurrentRecID().ToString();

                        //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                        if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                        {
                            doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        //TLD 5/18/2011 -----------Added to include date of latest OP
                        if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                        {
                            doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        //TLD 5/18/2011 -----------Added to include date of latest QT
                        //If doRS.GetLinkCount("LNK_Received_QT") > 0 Then
                        //    doNewRS = New clRowSet("QT", 3, "LNK_To_CO='" & sRecID & "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1)
                        //    If doNewRS.GetFirst Then
                        //        doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2)
                        //        bUpdateCO = True
                        //    End If
                        //End If

                        //Update CO
                        if (bUpdateCO == true)
                        {
                            if (doRS.Commit() == 0)
                            {
                                if (goErr.GetLastError("NUMBER") == "E47250")
                                {
                                    //Commit failed b/c user has no permissions to edit record; log it, but proceed
                                    iFailedPerm = iFailedPerm + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " due to permissions.", 1, false, true);
                                    //testing
                                }
                                else
                                {
                                    //Commit failed for some other reason.
                                    iFailedOther = iFailedOther + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                    //testing
                                }
                            }
                        }

                        if (doRS.GetNext() == 0)
                            break; // TODO: might not be correct. Was : Exit Do
                    } while (true);
                    lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                    //get last bi__id processed        
                }
                iFailedTotal = iFailedOther + iFailedPerm;
                iSuccess = Convert.ToInt32(iCount) - iFailedTotal;

                //Write to WOP
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Started " + sNowDate + " and Completed " + goTR.DateTimeToSysString(goTR.NowLocal(), ref par_iValid, ref par_sDelim) + " with " + iSuccess + " successful updates; " + iFailedPerm + " failed updates due to permissions; " + iFailedOther + " total failed updates.");
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);

                iCount = 0;
                iFailedOther = 0;
                iFailedPerm = 0;
                iFailedTotal = 0;
                iSuccess = 0;
                doRS = null;
                lBiID = 0;

            }
            catch (Exception ex)
            {
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Failed at Record " + sRecID + " " + goErr.GetLastError("NUMBER"));
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);
            }

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__TOTALINSTALLATIONS(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__TOTALINSTALLATIONS");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO1(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO1");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO2(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO2");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO3(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO3");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO4(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO4");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO5(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO5");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO6(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO6");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Fields Read-Only for Market Share
        public bool GP_FormControlOnChange_SR__OURPRODUCTINSTALLEDBASE1(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08072017 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__OURPRODUCTINSTALLEDBASE1");

            return true;

        }
        //VS TASK 1681 08/07/2017 Make Fields Read-Only for Market Share
        public bool GP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //'VS 08022016 : 
            //doForm.oVar.SetVar("lLenJournal", Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")))
            //doForm.SetControlState("MMO_JOURNAL", 1)
            //doForm.SetControlState("DTE_CALLCAMPAIGNSTARTDATE", 4)
            //doForm.SetControlState("DTE_CALLCAMPAIGNCOMPLETEDATE", 4)

            //'Implement Profile Start and Complete
            //If doForm.doRS.GetFieldVal("CHK_CALLCAMPAIGNSTARTED", 2) = 0 Then
            //    doForm.doRS.oVar.SetVar("CALLCAMPAIGNNotStarted_OnLoad", "1")
            //End If
            //If doForm.doRS.GetFieldVal("CHK_CALLCAMPAIGNCOMPLETED", 2) = 0 Then
            //    doForm.doRS.oVar.SetVar("CALLCAMPAIGNNotCompleted_OnLoad", "1")
            //End If

            //AD TASK 1681 08/03/2017
            doForm.SetControlState("SR__OURPRODUCTINSTALLEDBASEPERCENT1", 4);
            doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT1", 4);
            doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT2", 4);
            doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT3", 4);
            //doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT4", 4)
            //doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT5", 4)
            //doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT6", 4)

            par_doCallingObject = doForm;
            return true;
        }
        public bool GP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //VS 08022016 : Implement Profile Start and Complete
            //If doRS.oVar.GetVar("CALLCAMPAIGNNotStarted_OnLoad") = "1" And doRS.GetFieldVal("CHK_CALLCAMPAIGNSTARTED", 2) = 1 Then
            //    doRS.SetFieldVal("DTE_CALLCAMPAIGNSTARTDATE", Now, 2)
            //End If
            //If doRS.oVar.GetVar("CALLCAMPAIGNNotCompleted_OnLoad") = "1" And doRS.GetFieldVal("CHK_CALLCAMPAIGNCOMPLETED", 2) = 1 Then
            //    doRS.SetFieldVal("DTE_CALLCAMPAIGNCOMPLETEDATE", Now, 2)
            //End If

            return true;
        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 08052016 : Calc %s
            GP_SetInstallPercentages(ref par_doCallingObject, "SR__TOTALINSTALLATIONS");

            doForm = (Form)par_doCallingObject;

            if (doForm.oVar.GetVar("GP_InstallPercent_Save") != "1")
            {
                double dTotalPercent = 0;
                double SR__OURPRODUCTINSTALLEDBASEPERCENT1_var = (doForm.doRS.GetFieldVal("SR__OURPRODUCTINSTALLEDBASEPERCENT1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__OURPRODUCTINSTALLEDBASEPERCENT1", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT1_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT1", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT2_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT2", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT2", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT3_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT3", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT3", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT4_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT4", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT4", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT5_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT5", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT5", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT6_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT6", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT6", 2));
                dTotalPercent = SR__OURPRODUCTINSTALLEDBASEPERCENT1_var + SR__COMPETITIONINSTALLEDBASEPERCENT1_var + SR__COMPETITIONINSTALLEDBASEPERCENT2_var + SR__COMPETITIONINSTALLEDBASEPERCENT3_var + SR__COMPETITIONINSTALLEDBASEPERCENT4_var + SR__COMPETITIONINSTALLEDBASEPERCENT5_var + SR__COMPETITIONINSTALLEDBASEPERCENT6_var;

                if (dTotalPercent > 100)
                {
                    doForm.oVar.SetVar("GP_FormSaveMode", doForm.MessageBoxFormMode);
                    doForm.MessageBox("The Overall Installation % is more than 100. Do you want to continue saving the record?", clC.SELL_MB_YESNO, "Selltis", "Yes", "No", "", "", "MessageBoxEvent", "", "", null, null, "Yes", "No", "", "", "GP_InstallPercent_Save");
                    par_doCallingObject = doForm;
                    return false;
                }
            }

            //VS 08022016 : Create Activity Log of Journal Entry
            if (doForm.oVar.GetVar("GP_CreateActLog_Ran") != "1")
            {
                //goScr.RunScript("GP_CreateActLog", doForm);
                par_doCallingObject = doForm;
                scriptManager.RunScript("GP_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                doForm = (Form)par_doCallingObject;
            }


            return true;
        }

        //AD TASK 1681 08/03/2017 Make Calculations for Market Share

        public void GP_SetInstallPercentages(ref object par_doCallingObject, string par_sPercentField)
        {
            //VS 08012016 Added for Profile Update Journal Entry
            Form doForm = (Form)par_doCallingObject;

            double dOverallInstalls = 0;
            double dInstalls = 0;
            double dInstallPercent = 0;
            dOverallInstalls = (doForm.doRS.GetFieldVal("SR__TOTALINSTALLATIONS", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__TOTALINSTALLATIONS", 2));

            //Our Installation

            if (par_sPercentField == "SR__OURPRODUCTINSTALLEDBASE1" | par_sPercentField == "SR__TOTALINSTALLATIONS")
            {
                dInstalls = (doForm.doRS.GetFieldVal("SR__OURPRODUCTINSTALLEDBASE1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__OURPRODUCTINSTALLEDBASE1", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__OURPRODUCTINSTALLEDBASEPERCENT1", Math.Round(dInstallPercent, 2), 2);

            }
            //Competition 1

            if (par_sPercentField == "SR__COMPETITIONINSTALLEDBASENO1" | par_sPercentField == "SR__TOTALINSTALLATIONS")
            {
                dInstalls = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO1", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT1", Math.Round(dInstallPercent, 2), 2);

            }
            //Competition 2

            if (par_sPercentField == "SR__COMPETITIONINSTALLEDBASENO2" | par_sPercentField == "SR__TOTALINSTALLATIONS")
            {
                dInstalls = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO2", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO2", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT2", Math.Round(dInstallPercent, 2), 2);

            }
            //Competition 3

            if (par_sPercentField == "SR__COMPETITIONINSTALLEDBASENO3" | par_sPercentField == "SR__TOTALINSTALLATIONS")
            {
                dInstalls = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO3", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO3", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT3", Math.Round(dInstallPercent, 2), 2);

            }
            //'Competition 4
            //If par_sPercentField = "SR__COMPETITIONINSTALLEDBASENO4" Or par_sPercentField = "SR__TOTALINSTALLATIONS" Then

            //    dInstalls = doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO4", 2)

            //    If dOverallInstalls = 0 Or dInstalls = 0 Then
            //        dInstallPercent = 0
            //    Else
            //        dInstallPercent = (dInstalls / dOverallInstalls) * 100
            //    End If

            //    doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT4", Math.Round(dInstallPercent, 2), 2)

            //End If
            //'Competition 5
            //If par_sPercentField = "SR__COMPETITIONINSTALLEDBASENO5" Or par_sPercentField = "SR__TOTALINSTALLATIONS" Then

            //    dInstalls = doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO5", 2)

            //    If dOverallInstalls = 0 Or dInstalls = 0 Then
            //        dInstallPercent = 0
            //    Else
            //        dInstallPercent = (dInstalls / dOverallInstalls) * 100
            //    End If

            //    doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT5", Math.Round(dInstallPercent, 2), 2)

            //End If
            //'Competition 6
            //If par_sPercentField = "SR__COMPETITIONINSTALLEDBASENO6" Or par_sPercentField = "SR__TOTALINSTALLATIONS" Then

            //    dInstalls = doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO6", 2)

            //    If dOverallInstalls = 0 Or dInstalls = 0 Then
            //        dInstallPercent = 0
            //    Else
            //        dInstallPercent = (dInstalls / dOverallInstalls) * 100
            //    End If

            //    doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT6", Math.Round(dInstallPercent, 2), 2)

            //End If

            par_doCallingObject = doForm;


        }

        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            //the user's response.
            //Par_s5 will always be the name of the script that called doform.MessageBox
            //Par_s1 will be whatever button the user clicked.
            //Par_s2-Par_s4 can be whatever else you want to pass.
            //In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            //After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            //TLD 9/23/2013 Changed from _Post to _Pre

            string sProc = null;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //TLD 4/22/2009 
            //When Complete All checked on Corr In Box - My
            //par_callingObject is clDesktop, so throws an error
            //so changed per Christy
            //Dim doForm As clForm = par_doCallingObject
            Form doForm = (Form)par_doCallingObject;
            clEmail oEmail = new clEmail();

            string sHTML = "";
            string sVendorCCEmail = null;
            string sVendorEmail = null;
            string sFromEmail = null;
            string sWork = "";
            string sVendor = null;
            bool bUpdateFailed = false;
            clRowSet doRS = default(clRowSet);
            int iType = 0;
            bool bNoPerm = false;
            bool bReqMissing = false;
            bool bError = false;
            string sJournal = "";
            //original value in journal field

            switch (Strings.UCase(par_s5))
            {
                //SB Tkt#3299 01202020 Updated to generic merge
                //For Merge records
                case "MERGE":
                    par_bRunNext = false;
                    doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                    switch (Strings.UCase(par_s1))
                    {
                        case "YES":
                            //run merge script, continue save
                            //goScr.RunScript("MergeRecord", doForm.doRS);
                            par_doCallingObject = doForm.doRS;
                            bool runnext = true;
                            scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref runnext, ref par_sSections);
                            doForm.doRS = (clRowSet)par_doCallingObject;
                            par_doCallingObject = doForm;
                            break;
                        case "NO":
                            //Clear merged to co linkbox, continue save
                            doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);

                            break;
                        case "CANCEL":
                            //Clear merged to co linkbox, cancel save
                            doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                            doForm.oVar.SetVar("CancelSave", "1");
                            break;
                    }

                    break;
                //SB Tkt#3299 01202020 Added for Merge record to itself
                case "MERGEFAIL":
                    par_bRunNext = false;
                    doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                    switch (Strings.UCase(par_s1))
                    {
                        case "OK":
                            //Clear merged to co linkbox, cancel save
                            doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                            doForm.oVar.SetVar("CancelSave", "1");
                            break;
                    }
                    break;
            }
            return true;
        }

        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            clRowSet doRS1 = null;

            par_bRunNext = false;

            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
            }
            long lStatus = 0;
            double rProb = 0;
            decimal cValueFld = default(decimal);
            double rQty = 0;
            double rNewValue = 0;
            //testing code by pratap

            if (par_s2 == "doRS")
            {
                rProb = Convert.ToDouble(doRS1.GetFieldVal("SR__HYSPECOPROBPER", 2));
                cValueFld = Convert.ToDecimal(doRS1.GetFieldVal("CUR_VALUE", 2));
                if (!goTR.IsNumber(cValueFld.ToString()))
                    cValueFld = 0;
                rQty = Convert.ToDouble(doRS1.GetFieldVal("SR__QTY", 2));
                //CS 10/13/08: Changed to IsNumber because the qty could be set to a varying number of decimals such as 2.15 and then
                //IsNumeric returns False
                //If Not goTR.IsNumeric(rQty) Then rQty = 0
                if (!goTR.IsNumber(rQty.ToString()))
                    rQty = 0;
                rNewValue = Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty);
                doRS1.SetFieldVal("CUR_VALUE", rNewValue, 2);
                int typevalue = Convert.ToInt32(doRS1.GetFieldVal("MLS_TYPE", 2));

                switch (typevalue)
                {
                    case 1:
                    case 18:
                    case 20:
                    case 22:
                    case 24:
                    case 26:
                        doRS1.SetFieldVal("CUR_VALUEINDEX", rNewValue);
                        break;
                    case 12:
                        doRS1.SetFieldVal("CUR_VALUEINDEX", rNewValue / 3);
                        break;
                    case 14:
                        doRS1.SetFieldVal("CUR_VALUEINDEX", rNewValue / 6);
                        break;
                    case 5:
                    case 16:
                        doRS1.SetFieldVal("CUR_VALUEINDEX", rNewValue / 12);
                        break;
                }
                par_doCallingObject = (clRowSet)doRS1;
            }
            else
            {

                cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_VALUE", 2));
                if (!goTR.IsNumber(cValueFld.ToString()))
                    cValueFld = 0;
                rQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));
                //CS 10/13/08: Changed to IsNumber because the qty could be set to a varying number of decimals such as 2.15 and then
                //IsNumeric returns False
                //If Not goTR.IsNumeric(rQty) Then rQty = 0
                if (!goTR.IsNumber(rQty.ToString()))
                    rQty = 0;
                rNewValue = Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty);
                doForm.doRS.SetFieldVal("CUR_VALUE", rNewValue, 2);
                int typevalue = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_TYPE", 2));

                switch (typevalue)
                {
                    case 1:
                    case 18:
                    case 20:
                    case 22:
                    case 24:
                    case 26:
                        doForm.doRS.SetFieldVal("CUR_VALUEINDEX", rNewValue);
                        break;
                    case 12:
                        doForm.doRS.SetFieldVal("CUR_VALUEINDEX", rNewValue / 3);
                        break;
                    case 14:
                        doForm.doRS.SetFieldVal("CUR_VALUEINDEX", rNewValue / 6);
                        break;
                    case 5:
                    case 16:
                        doForm.doRS.SetFieldVal("CUR_VALUEINDEX", rNewValue / 12);
                        break;
                }
                par_doCallingObject = (Form)doForm;
            }
            
            return true;
        }

        public bool OP_FormControlOnChange_MLS_TYPE_pre(object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            ////par_s3 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //goP.TraceLine("", "", sProc)
            //goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            par_bRunNext = false;
            Form doForm = (Form)par_doCallingObject;
            //int _status = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2));
            //if (_status == 2)
            //{
            //    int _type = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_TYPE", 2));
            //    DateTime _date = Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2));
            //    switch (_type)
            //    {
            //        case 1:
            //        case 12:
            //        case 14:
            //        case 16:
            //        case 4:
            //        case 5:
            //        case 26:
            //            doForm.doRS.SetFieldVal("DTE_EXPECTEDINVOICEDATE", _date.AddDays(40), 2);
            //            break;

            //        case 18:
            //            doForm.doRS.SetFieldVal("DTE_EXPECTEDINVOICEDATE", _date.AddDays(120), 2);
            //            break;
            //        case 20:
            //            doForm.doRS.SetFieldVal("DTE_EXPECTEDINVOICEDATE", _date.AddDays(90), 2);
            //            break;
            //        case 22:
            //        case 24:
            //            doForm.doRS.SetFieldVal("DTE_EXPECTEDINVOICEDATE", _date.AddDays(90), 2);
            //            break;


            //    }
            //}
            CalculateExpectedInvoiceDate(doForm);

            par_doCallingObject = (Form)doForm;
            return true;
        }

        public bool OP_FormControlOnChange_MLS_Status_pre(object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            ////par_s3 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //goP.TraceLine("", "", sProc)
            //goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            par_bRunNext = false;
            Form doForm = (Form)par_doCallingObject;

            CalculateExpectedInvoiceDate(doForm);

            par_doCallingObject = (Form)doForm;
            return true;
        }
        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }
        private static void CalculateExpectedInvoiceDate(Form doForm)
        {
            int _status = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2));
            if (_status == 2)
            {
                int _type = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_TYPE", 2));
                //DateTime _date = Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2));
                DateTime _date = DateTime.Now;
                switch (_type)
                {
                    case 1:
                    case 12:
                    case 14:
                    case 16:
                    case 4:
                    case 5:
                    case 26:
                        doForm.doRS.SetFieldVal("DTE_EXPECTEDINVOICEDATE", _date.AddDays(40), 2);
                        break;
                    case 18:
                        doForm.doRS.SetFieldVal("DTE_EXPECTEDINVOICEDATE", _date.AddDays(120), 2);
                        break;
                    case 20:
                        doForm.doRS.SetFieldVal("DTE_EXPECTEDINVOICEDATE", _date.AddDays(90), 2);
                        break;
                    case 22:
                    case 24:
                        doForm.doRS.SetFieldVal("DTE_EXPECTEDINVOICEDATE", _date.AddDays(60), 2);
                        break;
                }
            }
        }

        public bool AutoAlertEveryDay_pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter in all rowsets to get all users' private recs
            //MI 10/19/07 started converting the script to be aware of the start of the day depending on the user's time zone
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DETAILS, true);

            Form doForm = (Form)par_doCallingObject;
            //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
            //It is unlikely that there would be non-shared User records, but just in case...
            clRowSet rsUsers = new clRowSet("US", clC.SELL_READONLY, "CHK_ACTIVEFIELD=1", "SYS_Name", "GID_ID", -1, "", "", "", "", "", false, false, true);
            //goLog.Log(sProc, "Users count: " & rsUsers.Count)
            //No active users
            if (rsUsers.Count() == 0)
                return true;

            //PURPOSE:
            //		Every day display alerts about things that require review or followup,
            //		update Appt dates (reschedule)/To Do due dates.
            //		The alerts are typically set up to open an appropriate desktop.
            //		This replaces 'alarm' agents in Selltis 1.0. The name of the agent 
            //		is on the top of each section of code.
            //       MI 10/19/07: Making the script aware of ea user's time zone so that
            //       the alerts, auto-updates, rescheduling, etc. occurs after midnight
            //       user's time and not server's time. See additional notes in the code.
            //HOW THIS WORKS:
            //       This script runs hourly by an automator AGE_2005072616363748192MAR 00002XX
            //       that has the following definition:
            //           A_01_EXECUTE = AutoAlertEveryDay
            //           A_01_OBJSHARED = 1
            //           A_01_TYPE = RUNSCRIPT
            //           A_ORDER=1,
            //           ACTIONS=1,
            //           ACTIVE = 1
            //           E_TM_HOUR = 0
            //           E_TM_INTERVAL = 2
            //           E_TM_MINUTE = 1
            //           E_TM_MISSED = 1
            //           EVENT=TIMER
            //           US_NAME=AutoAlertEveryDay
            //           US_PURPOSE=Add alerts every morning for followup of leads, contacts, to dos, etc.
            //           SORTVALUE1=TIMER_ACTIVE
            //       Make sure the timer automator engine is running on this site's web server.
            //WARNING:
            //       Tread carefully - this is a very sensitive area.

            clRowSet doRS = default(clRowSet);
            string sResult = null;
            string sStartDate = null;
            string sDueDate = null;
            bool bMore = true;
            string sCurrentUser = "";
            System.DateTime dtDate = default(System.DateTime);
            long lDays = 0;
            string sPointers = null;
            string sPointerDateTime = null;
            DateTime dtPointerDate = default(DateTime);
            PublicDomain.TzTimeZone zone = default(PublicDomain.TzTimeZone);
            DateTime dtUsersNow = default(DateTime);
            //User time zone 'now'
            DateTime dtUsersToday = default(DateTime);
            //User time zone Today (now.Date())
            string sDateTime = null;
            DateTime dtDateTime = default(DateTime);
            string sStartTime = null;
            string sEndTime = null;

            //Get a page with date stamps of last dates when daily timer script was processed for ea user. Ex:
            //   USER1GID_ID=2008-03-25
            //   USER2GID_ID=2008-03-22
            //   ...
            //where USERnGID_ID is a Selltis global ID (SUID).
            sPointers = goMeta.PageRead("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED");


            //--------------------- Non-user-dependent updates -----------------------

            //------- Update status of unassigned To Dos ----------
            //"UpdateToDoStatus" is done in two code blocks, this one for unassigned To Dos and one in the per-user loop further below

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "UpdateToDoStatus", true))
            {
                //*** MI 10/24/07 added here and in the user loop
                //Process Status of To Dos that are not assigned to any user(s).
                //Ported from ToDo:DailyMarkOverdue
                //There should be no unassigned To Dos normally except if they were imported that way or there was
                //data corruption. Assigned To Dos are processed below in the per user loop.
                //Here the start and end date are GMT since we don't have the user to go by.

                //--------- Set 'today UTC' -------------
                //sDateTime replaces the Selltis keyword 'Today' with the datetime value that corresponds to midnight
                //in the currently processed user's time zone
                dtDateTime = goTR.NowUTC().Date;
                int par_iValid = 4;
                string par_sDelim = "|";
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sDelim);

                //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                doRS = new clRowSet("TD", clC.SELL_EDIT, "CHK_OPEN=1 AND DTT_DUETIME<'" + sDateTime + "' AND LNK_AssignedTo_US%%BI__ID<1", "DTT_DUETIME A", "*", -1, "", "", "", "", "", false, false, true);
                //goLog.Log("AutoAlertEveryDay:TDOverdue", doRS.Count & sCurrentUser, , , True)
                if (doRS.GetFirst() == 1)
                {
                    doRS.bBypassValidation = true;
                    //goLog.Log("AutoAlertEveryDay", "Found To Dos to update status", , , True)
                    do
                    {
                        //-------- Fill the Start Date and Due Date fields if needed ---------
                        //Start Date
                        sStartDate = doRS.GetFieldVal("DTE_STARTDATE", clC.SELL_FRIENDLY).ToString();
                        //If Start Date field is empty, fill Today's date
                        if (string.IsNullOrEmpty(sStartDate))
                            sStartDate = Strings.Left(sDateTime, 10);
                        //Due Date
                        sDueDate = doRS.GetFieldVal("DTE_DUETIME", clC.SELL_FRIENDLY).ToString();
                        //If Due Date field is empty, fill Today's date
                        if (string.IsNullOrEmpty(sDueDate))
                            sDueDate = Strings.Left(sDateTime, 10);
                        //If Start Date is after Due Date, set Start Date to Due Date.
                        if (Convert.ToDateTime(sStartDate) > Convert.ToDateTime(sDueDate))
                        {
                            sStartDate = sDueDate;
                        }
                        //goLog.Log("AutoAlertEveryDay", "Start date is '" & sStartDate & "'" & "Due Date Is '" & sDueDate & "'", , , True)
                        //-------- Set status as needed ----------
                        par_doCallingObject = doRS;
                        if (!ToDo_CalcStatus(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, goTR.NumToString(doRS.GetFieldVal("MLS_STATUS", clC.SELL_SYSTEM).ToString()), sDueDate, sStartDate, goTR.CheckBoxToText(Convert.ToInt32(doRS.GetFieldVal("CHK_COMPLETED", clC.SELL_SYSTEM)), true)))
                        {
                            //Should never happen
                            goErr.SetWarning(10111, sProc, "", "ToDo_CalcStatus", "MLS_Status: '" + doRS.GetFieldVal("MLS_STATUS", clC.SELL_SYSTEM).ToString() + "' sDueDate: '" + sDueDate + "' sStartDate: '" + sStartDate + "' doRS.GetFieldVal('CHK_COMPLETED', clC.SELL_SYSTEM): '" + doRS.GetFieldVal("CHK_COMPLETED", clC.SELL_SYSTEM).ToString() + "'", "False");
                            //10111: The result returned by the called method is not supported or is in an unexpected format. Called method: '[1]' Parameters: '[2]' Result: '[3]'
                            //goLog.Log("AutoAlertEveryDay", "ToDo CalcStatus returned False", , , True)
                        }
                        else
                        {
                            if (doRS.GetFieldVal("MLS_STATUS", clC.SELL_SYSTEM).ToString() != goP.GetVar("ToDoStatus").ToString())
                            {
                                doRS.SetFieldVal("MLS_STATUS", goP.GetVar("ToDoStatus"), clC.SELL_SYSTEM);
                                //goLog.Log("AutoAlertEveryDay", "Changed status of TD '" & doRS.GetFieldVal("GID_ID") & "'", , , True)
                                doRS.Commit();
                            }
                        }
                        if (doRS.GetNext() == 0)
                            break; // TODO: might not be correct. Was : Exit Do
                    } while (true);
                }
                else
                {
                    //First not found.
                }
                doRS = null;
                //goLog.Log("AutoAlertEveryDay", "After TD Loop", , , True)
            }
            //CS REMOVE 1/6/12
            //goLog.Log(sProc, "right before per user processing loop")


            //---------------------- Per-user processing loop ------------------

            while (bMore == true)
            {
                //The timer automator fires this script every hour.
                //Here we make sure the actual update is made only once in a day for each user.
                //This is very important because we are modifying data (changing Appointment 
                //date field values, for example) for the whole day. This should not be done 
                //piece meal or it will be confusing to users.

                //A 'day' is seen from the perspective of the currently processed user's
                //current time zone. If the user switches to a different time zone within 
                //the same day, the processing should not occur again until the next day 
                //starts in that time zone. As a consequence, for users traveling west 
                //this script may not fire for up to an extra day. Users traveling east 
                //will have the processing occur in less than 24 hours from the last time it ran.

                //Read user's 'last processed' date (recorded in user's time zone at the time)
                sCurrentUser = rsUsers.GetFieldVal("GID_ID", clC.SELL_FRIENDLY).ToString();
                //CS REMOVE 1/6/12
                //goLog.Log(sProc, "Current user: " & rsUsers.GetFieldVal("SYS_NAME"))

                //DEBUG 
                //goLog.Log(sProc, "  Processing user '" & goData.GetRecordNameByID(sCurrentUser) & "' [" & sCurrentUser & "]", clC.SELL_LOGLEVEL_DETAILS)
                //END DEBUG

                //Get user's time zone
                zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
                //Get user's 'now' and 'today at midnight' in the user's current time zone
                dtUsersNow = zone.ToLocalTime(goTR.NowUTC());
                //User's current datetime in his/her time zone
                dtUsersToday = dtUsersNow.Date;
                //Midnight on user's current date in his/her time zone
                //Get the 'last processed' date (no time) as a local datetime
                //Pointers are written as local datetimes in user's last login time zone.
                sPointerDateTime = Strings.Left(goTR.StrRead(sPointers, sCurrentUser, "", false), 10);
                int par_iValid = 4;
                if (string.IsNullOrEmpty(sPointerDateTime))
                {
                    //Leaving one whole day ahead of 'blank' datetime: '1753-01-02 23:59:59.000'.
                    dtPointerDate = goTR.StringToDate("1753-01-04", "", ref par_iValid);
                }
                else
                {
                    dtPointerDate = goTR.StringToDate(sPointerDateTime, clC.SELL_FORMAT_DATEDEF, ref par_iValid).Date;
                }
                dtPointerDate = DateTime.SpecifyKind(dtPointerDate, DateTimeKind.Local);

                //*** MI 10/23/07 Replaced >= with just = to avoid not processing for days or months in case 
                //the server clock was accidentally set ahead and the processing occurred... In that case, 
                //pointer datetimes set far in the future would preclude running this.
                //If dtPointerDate >= dtUsersToday Then GoTo ProcessNextUser

                //DEBUG
                //goLog.Log(sProc, "    Pointer date: '" & goTR.DateTimeToSysString(dtPointerDate) & "' User's date: '" & goTR.DateTimeToSysString(dtUsersToday) & "'", clC.SELL_LOGLEVEL_DETAILS)
                //END DEBUG

                if (dtPointerDate == dtUsersToday)
                    goto ProcessNextUser;
                //If dtPointerDate = dtUsersToday Then
                //    'CS 1/16/12
                //    goLog.Log(sProc, "Go to ProcessNextUser", , , True)
                //    GoTo ProcessNextUser
                //End If




                //------- Update status of To Dos ----------

                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "UpdateToDoStatus", true))
                {
                    //CS 6/26/07: Moved to AutoTD_DailySetStatus. Did this so that ALL uncompleted TDs get updated and not just those assigned to a user.
                    //*** MI 10/24/07 Reenabled here and added below outside the user loop to process unassigned To Dos
                    //Ported from ToDo:DailyMarkOverdue
                    //Change status of all the user's open To Dos based on their
                    //start and end date. This is run from the automator 'ToDo:DailyMarkOverdue'.

                    //--------- Set 'tomorrow' -----------
                    //Set user's 'tomorrow at midnight' as local datetime for filtering
                    dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));
                    dtDateTime = goTR.AddDay(dtDateTime, 1);
                    string par_sDelim = "|";
                    sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sDelim);

                    //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("TD", clC.SELL_EDIT, "CHK_OPEN=1 AND DTT_DUETIME<'" + sDateTime + "' AND LNK_AssignedTo_US='" + sCurrentUser + "'", "DTT_DUETIME A", "*", -1, "", "", "", "", "", false, false, true);
                    //goLog.Log("AutoAlertEveryDay:TDOverdue", doRS.Count & sCurrentUser, , , True)
                    if (doRS.GetFirst() == 1)
                    {
                        doRS.bBypassValidation = true;
                        //goLog.Log("AutoAlertEveryDay", "Found To Dos to update status", , , True)
                        do
                        {
                            //-------- Fill the Start Date and Due Date fields if needed ---------
                            //Start Date
                            sStartDate = doRS.GetFieldVal("DTE_STARTDATE", clC.SELL_FRIENDLY).ToString();
                            //If Start Date field is empty, fill Today's date
                            if (string.IsNullOrEmpty(sStartDate))

                                sStartDate = Strings.Left(goTR.DateTimeToSysString(goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday)), ref par_iValid, ref par_sDelim), 10);
                            //Due Date
                            sDueDate = doRS.GetFieldVal("DTE_DUETIME", clC.SELL_FRIENDLY).ToString();
                            //If Due Date field is empty, fill Today's date
                            if (string.IsNullOrEmpty(sDueDate))
                                sDueDate = Strings.Left(goTR.DateTimeToSysString(goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday)), ref par_iValid, ref par_sDelim), 10);
                            //If Start Date is after Due Date, set Start Date to Due Date.
                            if (Convert.ToDateTime(sStartDate) > Convert.ToDateTime(sDueDate))
                            {
                                sStartDate = sDueDate;
                            }
                            //goLog.Log("AutoAlertEveryDay", "Start date is '" & sStartDate & "'" & "Due Date Is '" & sDueDate & "'", , , True)
                            //-------- Set status as needed ----------
                            par_doCallingObject = doRS;
                            if (!ToDo_CalcStatus(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, doRS.GetFieldVal("MLS_STATUS", clC.SELL_SYSTEM).ToString(), sDueDate, sStartDate, doRS.GetFieldVal("CHK_COMPLETED", clC.SELL_SYSTEM).ToString()))
                            {
                                //Should never happen
                                goErr.SetWarning(10111, sProc, "", "ToDo_CalcStatus", "MLS_Status: '" + doRS.GetFieldVal("MLS_STATUS", clC.SELL_SYSTEM).ToString() + "' sDueDate: '" + sDueDate + "' sStartDate: '" + sStartDate + "' doRS.GetFieldVal('CHK_COMPLETED', clC.SELL_SYSTEM): '" + doRS.GetFieldVal("CHK_COMPLETED", clC.SELL_SYSTEM).ToString() + "'", "False");
                                //10111: The result returned by the called method is not supported or is in an unexpected format. Called method: '[1]' Parameters: '[2]' Result: '[3]'
                                //goLog.Log("AutoAlertEveryDay", "ToDo CalcStatus returned False", , , True)
                            }
                            else
                            {
                                if (doRS.GetFieldVal("MLS_STATUS", clC.SELL_SYSTEM).ToString() != goP.GetVar("ToDoStatus").ToString())
                                {
                                    doRS.SetFieldVal("MLS_STATUS", goP.GetVar("ToDoStatus"), clC.SELL_SYSTEM);
                                    //goLog.Log("AutoAlertEveryDay", "Changed status of TD '" & doRS.GetFieldVal("GID_ID") & "'", , , True)
                                    doRS.Commit();
                                }
                            }
                            if (doRS.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //First not found.
                    }
                    doRS = null;
                    //CS 1/6/12
                    // goLog.Log(sProc, "After TD Loop")
                }


                //--------- Set 'today' -------------
                //sDateTime replaces the Selltis keyword 'Today' with the datetime value that corresponds to midnight
                //in the currently processed user's time zone
                string par_delim = "|";
                dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_delim);


                //------- Reschedule Appts Daily ------------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "RescheduleApptsDaily", true))
                {
                    //Appt:ReschDaily
                    //sDateTime: today

                    //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("AP", clC.SELL_EDIT, "CHK_COMPLETED=0 AND CHK_RESCHDAILY=1 AND DTT_STARTTIME<'" + sDateTime + "' AND (LNK_Involves_US='" + sCurrentUser + "' OR LNK_Coordinatedby_US='" + sCurrentUser + "' OR LNK_Createdby_US='" + sCurrentUser + "')", "", "*", -1, "", "", "", "", "", false, false, true);
                    //goLog.Log("AutoAlertEveryDay:Resc Appts daily", doRS.Count & sCurrentUser, , , True)
                    if (doRS.GetFirst() == 1)
                    {
                        doRS.bBypassValidation = true;
                        do
                        {
                            //If the start date is blank (should not be due to validations, but just in case),
                            //leave it and end date as is
                            //*** MI 10/22/07 added
                            if (Convert.ToDateTime(doRS.GetFieldVal("DTT_STARTTIME", 2)) != goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, "", "", ref par_iValid, " "))
                            {
                                //Change date to today - time remains unchanged
                                //doRS.SetFieldVal("DTE_STARTTIME", Left(sDateTime, 10), 1)
                                //CS 7/21/08 Get original times
                                sStartTime = doRS.GetFieldVal("TME_STARTTIME", 1).ToString();
                                sEndTime = doRS.GetFieldVal("TME_ENDTIME", 1).ToString();
                                //Get # of days between original start date and today...will use to calc new end date
                                lDays = Microsoft.VisualBasic.DateAndTime.DateDiff(DateInterval.Day, Convert.ToDateTime(doRS.GetFieldVal("DTE_STARTTIME", 2)), dtDateTime);
                                //Need this so can set end date to same distance from start
                                doRS.SetFieldVal("DTE_STARTTIME", dtDateTime.Date, 2);
                                //CS 7/21/08
                                //Appts were all being rescheduled to midnight time
                                doRS.SetFieldVal("TME_STARTTIME", sStartTime);
                                //original start time
                                //If IsDate(doRS.GetFieldVal("DTE_EndTime", 2)) Then         '*** MI 10/22/07 commented because this test will always pass - blank dates are considered valid dates
                                //*** MI 10/22/07 added
                                if (Convert.ToDateTime(doRS.GetFieldVal("DTT_EndTime", 2)) != goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, "", "", ref par_iValid, " "))
                                {
                                    //dtDate = CDate(doRS.GetFieldVal("DTE_EndTime", 2))     '*** MI 10/22/07 commented because CDate is unnecessary
                                    dtDate = Convert.ToDateTime(doRS.GetFieldVal("DTE_EndTime", 2));
                                    //CS 7/21/08: Commenting out below.
                                    //We need to support multi-day appts 
                                    //lDays = DateDiff(DateInterval.Day, dtDate, dtDateTime)
                                    doRS.SetFieldVal("DTE_ENDTIME", goTR.AddDay(Convert.ToDateTime(doRS.GetFieldVal("DTE_EndTime", 2)), Convert.ToInt32(lDays)), 2);
                                    doRS.SetFieldVal("TME_ENDTIME", sEndTime);
                                    //CS 7/21/08
                                }
                                //goLog.Log("AutoAlertEveryDay", "Changed date of AP '" & doRS.GetFieldVal("GID_ID") & "'", , , True)
                            }
                            //CS 1/6/12 
                            //goLog.Log(sProc, "Before appt commit" & doRS.GetFieldVal("SYS_NAME"))
                            doRS.Commit();
                            //goLog.Log(sProc, "After appt commit" & doRS.GetFieldVal("SYS_NAME"))
                            if (doRS.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //No Appts to reschedule.
                    }
                    doRS = null;
                    //CS 1/6/12
                    //goLog.Log(sProc, "After resch appts daily")
                }


                //------- Reschedule Appts Workdays ------------

                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "RescheduleApptsWorkdays", true))
                {
                    //Appt:ReschWorkdays
                    //goP.TraceLine("== Setting a filter for my Appts < today with a Reschedule Workdays checkbox checked.", "", sProc)
                    //Is today a workday?
                    //Select Case goTR.GetDayOfWeekNum(Format(goTR.NowLocal(), "yyyy-MM-dd"))    '*** MI 10/22/07 commented because NowLocal returns datetime
                    switch (goTR.GetDayOfWeekNum(goTR.NowLocal()))
                    {
                        case 1:
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                            //Monday through Friday
                            //sDateTime: today
                            //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                            doRS = new clRowSet("AP", clC.SELL_EDIT, "CHK_COMPLETED=0 AND CHK_RESCHWORKDAYS=1 AND DTT_STARTTIME<'" + sDateTime + "' AND (LNK_Involves_US='" + sCurrentUser + "' OR LNK_Coordinatedby_US='" + sCurrentUser + "' OR LNK_Createdby_US='" + sCurrentUser + "')", "", "*", -1, "", "", "", "", "", false, true);
                            //goLog.Log("AutoAlertEveryDayresch workdays", doRS.Count & sCurrentUser, , , True)
                            if (doRS.GetFirst() == 1)
                            {
                                doRS.bBypassValidation = true;
                                do
                                {
                                    //PRE-UTC-AWARE CODE
                                    //If IsDate(doRS.GetFieldVal("DTE_EndTime", 2)) Then
                                    //    dtDate = CDate(doRS.GetFieldVal("DTE_EndTime", 2))
                                    //    lDays = DateDiff(DateInterval.Day, dtDate, dtToday)
                                    //End If
                                    //'Change date to today - time remains unchanged
                                    //doRS.SetFieldVal("DTE_STARTTIME", "Today", clC.SELL_FRIENDLY)
                                    //doRS.SetFieldVal("DTE_ENDTIME", goTR.AddDay(doRS.GetFieldVal("DTE_EndTime"), lDays), 1)
                                    //'goLog.Log("AutoAlertEveryDay", "Changed start time of AP '" & doRS.GetFieldVal("GID_ID") & "'", , , True)
                                    //NEW UTC-AWARE CODE
                                    //If the start date is blank (should not be due to validations, but just in case),
                                    //leave it and end date as is
                                    //*** MI 10/22/07 added
                                    if (Convert.ToDateTime(doRS.GetFieldVal("DTT_STARTTIME", 2)) != goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, "", "", ref par_iValid, " "))
                                    {
                                        //Change date to today - time remains unchanged
                                        //doRS.SetFieldVal("DTE_STARTTIME", Left(sDateTime, 10), 1)
                                        //CS 7/21/08 Get original times
                                        sStartTime = doRS.GetFieldVal("TME_STARTTIME", 1).ToString();
                                        sEndTime = doRS.GetFieldVal("TME_ENDTIME", 1).ToString();
                                        //Get # of days between original start date and today...will use to calc new end date
                                        lDays = Microsoft.VisualBasic.DateAndTime.DateDiff(DateInterval.Day, Convert.ToDateTime(doRS.GetFieldVal("DTE_STARTTIME", 2)), dtDateTime);
                                        doRS.SetFieldVal("DTE_STARTTIME", dtDateTime.Date, 2);
                                        //CS 7/21/08
                                        //Appts were all being rescheduled to midnight time
                                        doRS.SetFieldVal("TME_STARTTIME", sStartTime);
                                        //original start time
                                        //If IsDate(doRS.GetFieldVal("DTE_EndTime", 2)) Then         '*** MI 10/22/07 commented because this test will always pass - blank dates are considered valid dates
                                        //*** MI 10/22/07 added
                                        if (Convert.ToDateTime(doRS.GetFieldVal("DTT_EndTime", 2)) != goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, "", "", ref par_iValid, " "))
                                        {
                                            //dtDate = CDate(doRS.GetFieldVal("DTE_EndTime", 2))     '*** MI 10/22/07 commented because CDate is unnecessary
                                            dtDate = Convert.ToDateTime(doRS.GetFieldVal("DTE_EndTime", 2));
                                            //CS 7/21/08: Commenting out below.
                                            //We need to support multi-day appts 
                                            //lDays = DateDiff(DateInterval.Day, dtDate, dtDateTime)
                                            doRS.SetFieldVal("DTE_ENDTIME", goTR.AddDay(Convert.ToDateTime(doRS.GetFieldVal("DTE_EndTime", 2)), Convert.ToInt32(lDays)), 2);
                                            doRS.SetFieldVal("TME_ENDTIME", sEndTime);
                                        }
                                        //goLog.Log("AutoAlertEveryDay", "Changed start time of AP '" & doRS.GetFieldVal("GID_ID") & "'", , , True)
                                    }

                                    doRS.Commit();
                                    if (doRS.GetNext() == 0)
                                        break; // TODO: might not be correct. Was : Exit Do
                                } while (true);
                            }
                            else
                            {
                                //No Appts to reschedule.
                            }
                            doRS = null;
                            break;
                    }
                    //CS 1/6/12
                    //goLog.Log(sProc, "after resch workdays")
                }


                //--------- Set 'tomorrow' -----------
                //Set user's 'tomorrow at midnight' as local datetime for filtering
                dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));
                dtDateTime = goTR.AddDay(dtDateTime, 1);
                string par_sdelim = "|";
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sdelim);


                //------- Activities overdue for review ----------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertOverdueActivities", true))
                {
                    //No equivalent agent
                    //goP.TraceLine("== Setting a filter for my overdue Activity review", "", sProc)
                    //Get the filter for the Activities-my to review desktop
                    //sCondition = goMeta.LineRead("GLOBAL", "VIE_2005072714274451144ME  00004XX", "CONDITION")
                    //goLog.Log("AutoAlertEveryDay", "Condition from view:" & sCondition, , , True)
                    //If we don't find the view condition then use a default filter
                    //If sCondition = "" Then sCondition = "MLS_STATUS=0 AND DTT_NEXTACTIONDATE<'Tomorrow' AND LNK_CreditedTo_US='" & sCurrentUser & "'"
                    //goLog.Log("AutoAlertEveryDay", "Condition after:" & sCondition, , , True)

                    //sDateTime: tomorrow
                    //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("AC", clC.SELL_READONLY, "MLS_STATUS=0 AND DTT_NEXTACTIONDATE<'" + sDateTime + "' AND LNK_CreditedTo_US='" + sCurrentUser + "'", "", "GID_ID", 1, "", "", "", "", "", false, false, true);
                    //Create a rowset with the condition
                    //doRS = New clRowSet("AC", clC.SELL_READONLY, sCondition, , "GID_ID", 1)
                    //goLog.Log("AutoAlertEveryDay:act overdue for review", doRS.Count, , , True)
                    if (doRS.GetFirst() == 1)
                    {

                        sResult = goUI.AddAlert("Overdue Activities", "OPENDESKTOP", "DSK_2005072714274446232ME_ 00004XX", sCurrentUser, "ACTIVITY16.gif").ToString();
                        //goLog.Log("AutoAlertEveryDay", "Result: '" & sResult & "'", , , True)
                        //goLog.Log("AutoAlertEveryDay", "User: '" & sCurrentUser & "'", , , True)
                        doRS = null;
                        //CS 1/6/12
                        //goLog.Log(sProc, "after act overdue")
                    }
                }


                //------- Contacts followup -----------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertOverdueContacts", true))
                {
                    //Contact:ReviewOverdueAlert
                    //doRS = New clRowSet("CN", 3, "CHK_ACTIVEFIELD=1 AND CHK_REVIEW=1 AND DTT_NEXTCONTACTDATE<'Tomorrow' AND LNK_Related_US='" & sCurrentUser & "'", , "GID_ID", 1)
                    //sDateTime: tomorrow
                    //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("CN", 3, "CHK_ACTIVEFIELD=1 AND CHK_REVIEW=1 AND DTT_NEXTCONTACTDATE<'" + sDateTime + "' AND LNK_Related_US='" + sCurrentUser + "'", "", "GID_ID", 1, "", "", "", "", "", false, false, true);
                    if (doRS.GetFirst() == 1)
                    {

                        sResult = goUI.AddAlert("Overdue Contacts", "OPENDESKTOP", "DSK_2005040518115766090MAR 00002XX", sCurrentUser, "CONTACT16.gif").ToString();
                    }
                    //CS 1/6/12
                    //goLog.Log(sProc, "after contact overdue")
                    doRS = null;
                }

                //CS 1/1/09
                //------- Companies followup -----------
                //Check if review field exists in db
                if (goData.IsFieldValid("CO", "CHK_REVIEW") == true)
                {
                    if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertOverdueCompanies", true))
                    {
                        //Company:ReviewOverdueAlert
                        doRS = new clRowSet("CO", 3, "CHK_ACTIVEFIELD=1 AND CHK_REVIEW=1 AND DTT_NEXTREVIEWDATE<'" + sDateTime + "' AND LNK_TEAMLEADER_US='" + sCurrentUser + "'", "", "GID_ID", 1, "", "", "", "", "", false, false, true);
                        if (doRS.GetFirst() == 1)
                        {

                            sResult = goUI.AddAlert("Overdue Companies", "OPENDESKTOP", "DSK_4D15A855-D362-4141-5858-9B84013D3182", sCurrentUser, "COMPANY16.gif").ToString();
                        }
                        //CS 1/6/12
                        //goLog.Log(sProc, "after co overdue")
                        doRS = null;
                    }
                }


                //-------- My Open Leads ----------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertOverdueLeads", true))
                {
                    //ActLog:LeadDailyAlarm
                    //goP.TraceLine("== Setting a filter for my overdue leads", "", sProc)
                    //Dim doRS as object

                    //CS: Per RH filter leads based on next action dt...like other 'to review' alerts.
                    //Will now open Leads-My to Reviw instead of Leads-My (all open leads)
                    //sDateTime: tomorrow
                    //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("AC", 3, "MLS_STATUS=0 AND CHK_LEAD=1 AND DTT_NEXTACTIONDATE<'" + sDateTime + "' AND LNK_CreditedTo_US='" + sCurrentUser + "'", "", "GID_ID", 1, "", "", "", "", "", false, false, true);

                    //doRS = New clRowSet("AC", 3, "MLS_STATUS=0 AND CHK_LEAD=1 AND LNK_CreditedTo_US='" & sCurrentUser & "'", , "GID_ID", 1)
                    if (doRS.GetFirst() == 1)
                    {
                        //Open desktop 'Leads - My'                                               
                        sResult = goUI.AddAlert("Overdue Leads", "OPENDESKTOP", "DSK_2005040518083583181MAR 00002XX", sCurrentUser, "Lead16.gif").ToString();
                    }
                    //CS 1/6/12
                    //goLog.Log(sProc, "after leads overdue")
                    doRS = null;
                }


                //-------- Correspondence In Box ----------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertCorrespondenceInBox", true))
                {
                    //ActLog:LetterInBoxDailyAlarm
                    //goP.TraceLine("== Setting a filter for my unread recd corr", "", sProc)
                    //Dim doRS as object 
                    //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("AC", 3, "LNK_CreditedTo_US='" + sCurrentUser + "' AND CHK_CORRRECEIVED=1 AND MLS_STATUS=0", "", "GID_ID", 1, "", "", "", "", "", false, false, true);
                    if (doRS.GetFirst() == 1)
                    {
                        //Open desktop 'Correspondence In Box - My'
                        sResult = goUI.AddAlert("New received Correspondence", "OPENDESKTOP", "DSK_2004052815393151179C_S 00016XX", sCurrentUser, "CORRRECD.gif").ToString();
                    }
                    //CS 1/6/12
                    //goLog.Log(sProc, "after corr in box")
                    doRS = null;
                }

                //---------- Opps to Review -----------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertOverdueOpps", true))
                {
                    //Opp:ReviewOverdueAlarm
                    //sDateTime: tomorrow
                    //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("OP", 3, "MLS_STATUS=0 AND DTT_NEXTACTIONDATE<'" + sDateTime + "' AND LNK_CreditedTo_US='" + sCurrentUser + "'", "", "GID_ID,DTE_EXPCLOSEDATE,LNK_FOR_CO%%TXT_COMPANYNAME,TXT_Description,LNK_ORIGINATEDBY_CN%%SYS_NAME,LNK_CreditedTo_US%%EML_EMAIL", 1, "", "", "", "", "", false, false, true);
                    if (doRS.GetFirst() == 1)
                    {
                        sResult = goUI.AddAlert("Overdue Opps", "OPENDESKTOP", "DSK_2004060109262365125C_S 00016XX", sCurrentUser, "OPP16.gif").ToString();

                        SendEmail(doRS);
                    }
                    //CS 1/6/12
                    //goLog.Log(sProc, "after opps overdue")
                    doRS = null;
                }


                //--------- Projects to Review ----------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertOverdueProjects", true))
                {
                    //Project:ReviewOverdueAlert
                    //sDateTime: tomorrow
                    //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("PR", 3, "CHK_OPEN=1 AND DTT_NEXTACTIONDATE<'" + sDateTime + "' AND (LNK_Involves_US='" + sCurrentUser + "' OR LNK_OriginatedBy_US='" + sCurrentUser + "' OR LNK_TeamLeader_US='" + sCurrentUser + "' OR LNK_AssignedTo_US='" + sCurrentUser + "')", "", "GID_ID", 1, "", "", "", "", "", false, false, true);
                    //goLog.Log("AutoAlertEveryDay:Project to review", doRS.Count, , , True)
                    if (doRS.GetFirst() == 1)
                    {
                        sResult = goUI.AddAlert("Overdue Projects", "OPENDESKTOP", "DSK_2005040517572769077MAR 00002XX", sCurrentUser, "PROJECT16.gif").ToString();
                    }
                    doRS = null;

                    //CS 1/6/12
                    //goLog.Log(sProc, "after projects overdue")
                }


                //---------- Quotes to Review ----------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertOverdueQuotes", true))
                {
                    //Quote:DailyAlert
                    //sDateTime: tomorrow
                    //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("QT", 3, "CHK_OPEN=1 AND DTT_NEXTACTIONDATE<'" + sDateTime + "' AND (LNK_CreditedTo_US='" + sCurrentUser + "' OR LNK_Peer_US='" + sCurrentUser + "')", "", "GID_ID", 1, "", "", "", "", "", false, false, true);
                    //goLog.Log("autoalerteveryday:quotes", doRS.Count, , , True)
                    if (doRS.GetFirst() == 1)
                    {
                        sResult = goUI.AddAlert("Overdue Quotes", "OPENDESKTOP", "DSK_2004060109460200235C_S 00016XX", sCurrentUser, "QUOTE16.gif").ToString();
                    }
                    //CS 1/6/12
                    //goLog.Log(sProc, "after quotes overdue")
                    doRS = null;
                }


                //-------- Overdue To Dos ------------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertOverdueToDos", true))
                {
                    //ToDo:DailyDueAlarm
                    //sDateTime: tomorrow
                    doRS = new clRowSet("TD", 3, "CHK_OPEN=1 AND DTT_DUETIME<'" + sDateTime + "' AND LNK_AssignedTo_US='" + sCurrentUser + "'", "", "GID_ID", 1);
                    //goLog.Log("autoalerteveryday:overdue TDs", doRS.Count, , , True)
                    if (doRS.GetFirst() == 1)
                    {
                        sResult = goUI.AddAlert("Overdue To Dos", "OPENDESKTOP", "DSK_2004060108412689225C_S 00016XX", sCurrentUser, "TODO16.gif").ToString();
                    }
                    //CS 1/6/12
                    //goLog.Log(sProc, "after To dos overdue")
                    doRS = null;
                }


                //----------Expense Approval & Reimbursement------------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertExpenseApprovalAndReimbReminder", true))
                {
                    goPerm = (clPerm)Util.GetInstance("goperm");
                    //Check if the current user has expense approval/reimb permission
                    //Alert to approve on 5th, reimburse on 10th                    
                    if (goPerm.GetUserPermission(sCurrentUser, "ApproveReimburse") == "1")
                    {
                        if (dtUsersToday.Day == 5)
                        {
                            //sResult = goUI.AddAlert("Approve expenses", "OPENDESKTOP", "DSK_2005092015234735061MAR 00015XX", sCurrentUser, "EXPENSE16.gif")
                            sResult = goUI.AddAlert("Approve expenses", "RUNSCRIPT", "AutoExpOpenReimbDesktop", sCurrentUser, "EXPENSE16.gif").ToString();
                        }
                        else if (dtUsersToday.Day == 10)
                        {
                            //sResult = goUI.AddAlert("Reimburse expenses", "OPENDESKTOP", "DSK_2005092015234735061MAR 00015XX", sCurrentUser, "EXPENSE16.gif")
                            sResult = goUI.AddAlert("Reimburse expenses", "RUNSCRIPT", "AutoExpOpenReimbDesktop", sCurrentUser, "EXPENSE16.gif").ToString();
                        }
                    }
                }

                //CS 1/6/12
                //goLog.Log(sProc, "Before write OTH DAILY for User:" & rsUsers.GetFieldVal("SYS_NAME"))
                //Update the daily processing pointer with current datetime in the processed user's time zone
                System.Data.SqlClient.SqlConnection par_oConnection = goData.GetConnection(); //new System.Data.SqlClient.SqlConnection();

                goMeta.LineWrite("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED", sCurrentUser, goTR.DateTimeToSysString(dtUsersNow, ref par_iValid, ref par_sdelim), ref par_oConnection);
                ProcessNextUser:


                if (rsUsers.GetNext() == 0)
                    bMore = false;

            }

            //-------------- Delete old Alerts - run pMetaDeleteAlerts -------------------
            //DEBUG 
            //goLog.Log(sProc, "  Deleting old alerts", clC.SELL_LOGLEVEL_DETAILS)
            //END DEBUG

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "DeleteOldAlerts", true))
            {
                //Duplicate and unexecuted alerts must be purged periodically. 
                //This purges alerts that are > 8 days old. We strongly recommend NOT
                //to override this section.
                //EXEC @iResult = pMetaDeleteAlerts
                //   @par_uSection = NULL, 
                //   @par_iDays = 31
                int iResult = 0;
                System.Data.SqlClient.SqlConnection myConnection = goData.GetConnection();

                System.Data.SqlClient.SqlCommand myCommand = new System.Data.SqlClient.SqlCommand("pMetaDeleteAlerts", myConnection);
                myCommand.CommandType = System.Data.CommandType.StoredProcedure;

                System.Data.SqlClient.SqlParameter paramSection = new System.Data.SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
                paramSection.Value = System.DBNull.Value;
                myCommand.Parameters.Add(paramSection);

                System.Data.SqlClient.SqlParameter paramDays = new System.Data.SqlClient.SqlParameter("@par_iDays", SqlDbType.Int);
                paramDays.Value = 8;
                myCommand.Parameters.Add(paramDays);

                //Call the sproc...
                System.Data.SqlClient.SqlDataReader reader = myCommand.ExecuteReader();

                //return parameter
                System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
                retValParam.Direction = ParameterDirection.ReturnValue;
                myCommand.Parameters.Add(retValParam);

                iResult = Convert.ToInt16(retValParam.Value);

                reader.Close();
                myConnection.Close();
            }


            rsUsers = null;

            //DEBUG:
            //goLog.Log(sProc, "End: Return True")
            //END DEBUG
            par_doCallingObject = doForm;
            return true;

        }

        public bool ToDo_CalcStatus(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "0", string par_s5 = "")
        {

            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_i1: Current status in MLS_STATUS in system format (as int).
            //par_s2: Due Date DTE_DUETIME in system format (as string).
            //par_s3: Start Date DTE_STARTDATE in system format (as string).
            //par_b4: Completed CHK_COMPLETED in system format (as boolean).
            //par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //PURPOSE:
            //		Determine To Do's MLS_Status based on the current status and Due date.
            //		If Start Date is > Due Date, it is considered to be on Due Date.
            //		This script is called from ToDo_SetStatus.
            //		Don't forget to fill DTE_DATECOMPLETED based on CHK_COMPLETED.
            //		See ToDo_SetStatus for an example.
            //		*** This script can be called independently from the form. ***
            //NOTE RE UTC CONSIDERATIONS:
            //       Since we don't know the user's time zone/DST offset, we can't
            //       user the current date from the user's time zone perspective.
            //       If this is desired (as it is for example in AutoAlertEveryDay
            //       script), the calling code must convert goTr.NowUTC() based on the
            //       user's time zone. If par_s2 and par_s3 (Due and Start Date)
            //       values are blank or invalid dates, they are set to 'today'
            //       based on the logged on user's time zone.
            //RETURNS:
            //		Always True and the Status value via goProject var 'ToDoStatus'.

            //goP.TraceLine("", "", sProc)
            int par_iValid = 4;
            int iStatus = Convert.ToInt32(goTR.StringToNum(par_s1, "", ref par_iValid));
            string sDueDate = par_s2;
            string sStartDate = par_s3;
            string sToday = Strings.Format(goTR.NowLocal(), "yyyy-MM-dd");

            bool bCompleted = goTR.StringToCheckbox(par_s4, false, ref par_iValid);

            //goP.TraceLine("IsDate(sDueDate): '" & goTR.IsDate(sDueDate) & "'", "", sProc)
            if (!goTR.IsDate(sDueDate) | string.IsNullOrEmpty(sDueDate))
            {
                sDueDate = Strings.Format(goTR.NowLocal(), "yyyy-MM-dd");
            }

            //goP.TraceLine("IsDate(sStartDate): '" & IsDate(sStartDate) & "'", "", sProc)
            if (!goTR.IsDate(sStartDate) | string.IsNullOrEmpty(sStartDate))
            {
                sStartDate = Strings.Format(goTR.NowLocal(), "yyyy-MM-dd");
            }

            if (Convert.ToDateTime(sStartDate) > Convert.ToDateTime(sDueDate))
            {
                //goP.TraceLine("Start Date is > Due Date", "", sProc)
                sStartDate = sDueDate;
                //goP.TraceLine("sStartDate set to Due Date: '" & sStartDate & "'", "", sProc)
            }

            // goP.TraceLine("sDueDate: '" & sDueDate & "'", "", sProc)
            //goP.TraceLine("sStartDate: '" & sStartDate & "'", "", sProc)
            //goP.TraceLine("bCompleted: '" & bCompleted & "'", "", sProc)
            ////goLog.Log("ToDoCalcSts", bCompleted & " Completed", , , True)

            if (bCompleted == true)
            {
                //goP.TraceLine("bCompleted is 1, setting iStatus to 4", "", sProc)
                iStatus = 4;
                //Completed
            }
            else
            {
                // goP.TraceLine("bCompleted is not 1", "", sProc)
                //goP.TraceLine("sStartDate: '" & sStartDate & "'", "", sProc)
                //goP.TraceLine("Convert.ToInt64(sStartDate): '" & Convert.ToInt64(sStartDate) & "'", "", sProc)
                //goP.TraceLine("Convert.ToInt64(sToday): '" & Convert.ToInt64(sToday) & "'", "", sProc)
                if (Convert.ToDateTime(sStartDate) > Convert.ToDateTime(sToday))
                {
                    //goP.TraceLine("sStartDate is > sToday, setting iStatus to 1", "", sProc)
                    iStatus = 1;
                    //Planned
                }
                else
                {
                    //goP.TraceLine("sStartDate is <= sToday", "", sProc)
                    //goP.TraceLine("sDueDate: '" & sDueDate & "'", "", sProc)
                    //goP.TraceLine("Val(sDueDate): '" & Convert.ToInt64(sDueDate) & "'", "", sProc)
                    if (Convert.ToDateTime(sDueDate) > Convert.ToDateTime(sToday))
                    {
                        //goP.TraceLine("Val(sDueDate) is > Val(sToday), setting iStatus to 2", "", sProc)
                        iStatus = 2;
                        //In Progress
                    }
                    else
                    {
                        //goP.TraceLine("Val(sDueDate) is <= Val(sToday), setting iStatus to 3", "", sProc)
                        iStatus = 3;
                        //Overdue
                    }
                }
            }

            //goP.TraceLine("Setting Var ToDoStatus to: '" & iStatus & "'", "", sProc)
            goP.SetVar("ToDoStatus", iStatus);

            //goP.TraceLine("Return True", "", sProc)
            return true;

        }

        private static void SendEmail(clRowSet doRS)
        {
            try
            {
                string sEmailId = "";
                //StringBuilder myBuilder = new StringBuilder();
                //myBuilder.Append("<table border='1px' cellpadding='5' cellspacing='0' ");
                //myBuilder.Append("font-family:Arial;font-size: 10px;>");

                //myBuilder.Append("<tr align='left' valign='top' > <td style='font-size:medium;color: #003665;border: solid 1px White border-style: outset border-collapse: collapse;' colspan=3'>Opps - My To Review</td><br/></tr>");

                //// Add the headings row.
                //myBuilder.Append("<tr align='center' valign='top' >");

                ////col1
                //myBuilder.Append("<td align='left' valign='top' style='background-color: #003665;color:white;border: solid 1px White border-style: outset border-collapse: collapse;'>");
                //myBuilder.Append("Status");
                //myBuilder.Append("</td><p>");
                ////col2
                //myBuilder.Append("<td align='left' valign='top' style='background-color: #003665;color:white;border: solid 1px White border-style: outset border-collapse: collapse;'>");
                //myBuilder.Append("Next Action Date");
                //myBuilder.Append("</td><p>");
                ////col3
                //myBuilder.Append("<td align='left' valign='top' style='background-color: #003665;color:white;border: solid 1px White border-style: outset border-collapse: collapse;'>");
                //myBuilder.Append("Value");
                //myBuilder.Append("</td><p>");
                ////col4
                //myBuilder.Append("<td align='left' valign='top' style='background-color: #003665;color:white;border: solid 1px White border-style: outset border-collapse: collapse;'>");
                //myBuilder.Append("Exp Close");
                //myBuilder.Append("</td><p>");
                ////col5
                //myBuilder.Append("<td align='left' valign='top' style='background-color: #003665;color:white;border: solid 1px White border-style: outset border-collapse: collapse;'>");
                //myBuilder.Append("Company");
                //myBuilder.Append("</td><p>");
                ////col6
                //myBuilder.Append("<td align='left' valign='top' style='background-color: #003665;color:white;border: solid 1px White border-style: outset border-collapse: collapse;'>");
                //myBuilder.Append("Description");
                //myBuilder.Append("</td><p>");
                ////col7
                //myBuilder.Append("<td align='left' valign='top' style='background-color: #003665;color:white;border: solid 1px White border-style: outset border-collapse: collapse;'>");
                //myBuilder.Append("Product");
                //myBuilder.Append("</td><p>");
                ////col8
                //myBuilder.Append("<td align='left' valign='top' style='background-color: #003665;color:white;border: solid 1px White border-style: outset border-collapse: collapse;'>");
                //myBuilder.Append("Val Index");
                //myBuilder.Append("</td><p>");
                ////col9
                //myBuilder.Append("<td align='left' valign='top' style='background-color: #003665;color:white;border: solid 1px White border-style: outset border-collapse: collapse;'>");
                //myBuilder.Append("HP%");
                //myBuilder.Append("</td><p>");
                ////col10
                //myBuilder.Append("<td align='left' valign='top' style='background-color: #003665;color:white;border: solid 1px White border-style: outset border-collapse: collapse;'>");
                //myBuilder.Append("JP%");
                //myBuilder.Append("</td><p>");
                ////col11
                //myBuilder.Append("<td align='left' valign='top' style='background-color: #003665;color:white;border: solid 1px White border-style: outset border-collapse: collapse;'>");
                //myBuilder.Append("Last Updated Date");
                //myBuilder.Append("</td><p>");
                ////col12
                //myBuilder.Append("<td align='left' valign='top' style='background-color: #003665;color:white;border: solid 1px White border-style: outset border-collapse: collapse;'>");
                //myBuilder.Append("Created Date");
                //myBuilder.Append("</td><p>");

                //myBuilder.Append("</tr><p>");

                do
                {
                    if(string.IsNullOrEmpty(sEmailId))
                    {
                        sEmailId = Convert.ToString(doRS.GetFieldVal("LNK_CreditedTo_US%%EML_EMAIL"));
                    }

                    //send email for each OPP record
                    string sEmailBody = "";

                    sEmailBody = "<table>";
                    sEmailBody = sEmailBody + "<tr><td colspan=\"2\"><b>Opportunity Update Required</b><br/><br/></td></tr>";
                    sEmailBody = sEmailBody + "<tr><td><b>Description:</b></td><td>"+ Convert.ToString(doRS.GetFieldVal("TXT_Description")) + "</td></tr>";
                    sEmailBody = sEmailBody + "<tr><td><b>End User Company:</b></td><td>" + Convert.ToString(doRS.GetFieldVal("LNK_FOR_CO%%TXT_COMPANYNAME")) + "</td></tr>";
                    sEmailBody = sEmailBody + "<tr><td><b>Contact:</b></td><td>" + Convert.ToString(doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%SYS_NAME")) + "</td></tr>";
                    sEmailBody = sEmailBody + "<tr><td><b>Expected Close Date:</b></td><td>" + Convert.ToString(doRS.GetFieldVal("DTE_EXPCLOSEDATE")) + "</td></tr>";
                    sEmailBody = sEmailBody + "<tr><td><br/><br/>Selltis Support</td></tr></table>";

                    if (!string.IsNullOrEmpty(sEmailId))
                    {                        
                        clEmail oEmail = new clEmail();
                        oEmail.SendSMTPEmailNew(Convert.ToString(doRS.GetFieldVal("TXT_Description")), sEmailBody, sEmailId, "", "", "", "Do Not Reply", "<EMAIL>", "", true);
                    }

                    ////add each row
                    //myBuilder.Append("<tr align='left' valign='top'>");

                    ////col1
                    //myBuilder.Append("<td align='left' valign='top' style='border-style: none;'>");
                    //myBuilder.Append(Convert.ToString(doRS.GetFieldVal("MLS_STATUS")));
                    //myBuilder.Append("</td><p>");
                    ////col2
                    //myBuilder.Append("<td align='left' valign='top' style='border-style: none;'>");
                    //myBuilder.Append(Convert.ToString(doRS.GetFieldVal("DTE_NEXTACTIONDATE")));
                    //myBuilder.Append("</td><p>");
                    ////col3
                    //myBuilder.Append("<td align='left' valign='top' style='border-style: none;'>");
                    //myBuilder.Append(Convert.ToString(doRS.GetFieldVal("CUR_VALUE")));
                    //myBuilder.Append("</td><p>");
                    ////col4
                    //myBuilder.Append("<td align='left' valign='top' style='border-style: none;'>");
                    //myBuilder.Append(Convert.ToString(doRS.GetFieldVal("DTE_EXPCLOSEDATE")));
                    //myBuilder.Append("</td><p>");
                    ////col5
                    //myBuilder.Append("<td align='left' valign='top' style='border-style: none;'>");
                    //myBuilder.Append(Convert.ToString(doRS.GetFieldVal("LNK_FOR_CO%%TXT_COMPANYNAME")));
                    //myBuilder.Append("</td><p>");
                    ////col6
                    //myBuilder.Append("<td align='left' valign='top' style='border-style: none;'>");
                    //myBuilder.Append(Convert.ToString(doRS.GetFieldVal("TXT_Description")));
                    //myBuilder.Append("</td><p>");
                    ////col7
                    //myBuilder.Append("<td align='left' valign='top' style='border-style: none;'>");
                    //myBuilder.Append(Convert.ToString(doRS.GetFieldVal("LNK_FOR_PD%%SYS_NAME")));
                    //myBuilder.Append("</td><p>");
                    ////col8
                    //myBuilder.Append("<td align='left' valign='top' style='border-style: none;'>");
                    //myBuilder.Append(Convert.ToString(doRS.GetFieldVal("CUR_VALUEINDEX")));
                    //myBuilder.Append("</td><p>");
                    ////col9
                    //myBuilder.Append("<td align='left' valign='top' style='border-style: none;'>");
                    //myBuilder.Append(Convert.ToString(doRS.GetFieldVal("SR__HYSPECOPROBPER")));
                    //myBuilder.Append("</td><p>");
                    ////col10
                    //myBuilder.Append("<td align='left' valign='top' style='border-style: none;'>");
                    //myBuilder.Append(Convert.ToString(doRS.GetFieldVal("SR__JOBPER")));
                    //myBuilder.Append("</td><p>");
                    ////col11
                    //myBuilder.Append("<td align='left' valign='top' style='border-style: none;'>");
                    //myBuilder.Append(Convert.ToString(doRS.GetFieldVal("DTE_MODTIME")));
                    //myBuilder.Append("</td><p>");
                    ////col12
                    //myBuilder.Append("<td align='left' valign='top' style='border-style: none;'>");
                    //myBuilder.Append(Convert.ToString(doRS.GetFieldVal("DTE_TIME")));
                    //myBuilder.Append("</td><p>");

                    //myBuilder.Append("</tr><p>");

                    if (doRS.GetNext() == 0)
                        break;
                } while (true);

                //myBuilder.Append("</table><p>");
                //myBuilder.Append("<p>&nbsp;</p>");
                //myBuilder.Append("<hr/>");
                //myBuilder.Append("<p>&nbsp;</p>");

                //if(!string.IsNullOrEmpty(sEmailId))
                //{
                //    string sEmailBody = myBuilder.ToString();
                //    clEmail oEmail = new clEmail();                   
                //    oEmail.SendSMTPEmailNew("Selltis : Opps - My to Review", sEmailBody, sEmailId, "", "", "", "Do Not Reply", "<EMAIL>", "", true);
                //}

            }
            catch (Exception ex)
            {

            }
        }

        //BTN_CusTest
        public bool CO_ViewControlOnChange_BTN_CusTest_pre(object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            ////par_s3 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //goP.TraceLine("", "", sProc)
            //goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            par_bRunNext = false;
           // Form doForm = (Form)par_doCallingObject;
            DateTime sDateTime = DateTime.Now;
            clRowSet doRS = new clRowSet("OP", 3, "MLS_STATUS=0 AND DTT_NEXTACTIONDATE<'" + sDateTime + "' AND LNK_CreditedTo_US='f0272467-3ec5-48f7-5553-987900b57a11'", "", "GID_ID,DTE_EXPCLOSEDATE,LNK_FOR_CO%%TXT_COMPANYNAME,TXT_Description,LNK_ORIGINATEDBY_CN%%SYS_NAME,LNK_CreditedTo_US%%EML_EMAIL", 1, "", "", "", "", "", false, false, true);
            if (doRS.GetFirst() == 1)
            {
                SendEmail(doRS);
            }

           // par_doCallingObject = (Form)doForm;
            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote_Draft.docx";
                }
                else if (sQTTemplate == "CAS Compressor System Quote")
                {
                    return "cus_quote_CAS_BasicTemplate_Draft.docx";
                }
                else if (sQTTemplate == "CAS Rental Compressor Quote")
                {
                    return "cus_quote_CAS_RentalTemplate_Draft.docx";
                }
                else if (sQTTemplate == "Inclusions Test")
                {
                    return "cus_quote_es6Inclusions Test_Draft.docx";
                }
                else if (sQTTemplate == "quote_es6 Word")
                {
                    return "cus_quote_es6_Draft.docx";
                }
                else if (sQTTemplate == "quote_es8 Word")
                {
                    return "cus_quote_es8_Draft.docx";
                }
                else if (sQTTemplate == "quote_ls100 Word")
                {
                    return "cus_quote_ls100_Draft.docx";
                }
                else if (sQTTemplate == "quote_ls120 Word")
                {
                    return "cus_quote_ls120_Draft.docx";
                }
                else if (sQTTemplate == "quote_ls160 Word")
                {
                    return "cus_quote_ls160_Draft.docx";
                }
                else if (sQTTemplate == "quote_ls200s Word")
                {
                    return "cus_quote_ls200s_Draft.docx";
                }
                else if (sQTTemplate == "quote_ls25 Word")
                {
                    return "cus_quote_ls25_Draft.docx";
                }
                else if (sQTTemplate == "quote_ts20 Word")
                {
                    return "cus_quote_ts20_Draft.docx";
                }
                else if (sQTTemplate == "quote_ts20 Word")
                {
                    return "cus_quote_ls200_Draft.docx";
                }
                else if (sQTTemplate == "quote_ts32 Word")
                {
                    return "cus_quote_ts32_Draft.docx";
                }
                else if (sQTTemplate == "quote_v120 Word")
                {
                    return "cus_quote_v120_Draft.docx";
                }
                else if (sQTTemplate == "quote_v160 Word")
                {
                    return "cus_quote_v160_Draft.docx";
                }
                else if (sQTTemplate == "quote_v200 Word")
                {
                    return "cus_quote_v200_Draft.docx";
                }
                else if (sQTTemplate == "quote_v200s Word")
                {
                    return "cus_quote_v200s_Draft.docx";
                }
                else if (sQTTemplate == "quote_vcc200 Word")
                {
                    return "cus_quote_vcc200_Draft.docx";
                }
                else if (sQTTemplate == "QuoteTemplate Word")
                {
                    return "cus_QuoteTemplate_Draft.docx";
                }
                else if (sQTTemplate == "sr 125 dryer")
                {
                    return "cus_quote_sr125_dryer_Draft.docx";
                }
                else if (sQTTemplate == "srv 400 dryer")
                {
                    return "cus_quote_srv400_dryer_Draft.docx";
                }
                else if (sQTTemplate == "srv 800 dryer")
                {
                    return "cus_quote_srv800_dryer_Draft.docx";
                }
                else if (sQTTemplate == "Temp KMR")
                {
                    return "cus_quote_temp_kmr_Draft.docx";
                }

            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote.docx";
                }
                else if (sQTTemplate == "CAS Compressor System Quote")
                {
                    return "cus_quote_CAS_BasicTemplate.docx";
                }
                else if (sQTTemplate == "CAS Rental Compressor Quote")
                {
                    return "cus_quote_CAS_RentalTemplate.docx";
                }
                else if (sQTTemplate == "Inclusions Test")
                {
                    return "cus_quote_es6Inclusions Test.docx";
                }
                else if (sQTTemplate == "quote_es6 Word")
                {
                    return "cus_quote_es6.docx";
                }
                else if (sQTTemplate == "quote_es8 Word")
                {
                    return "cus_quote_es8.docx";
                }
                else if (sQTTemplate == "quote_ls100 Word")
                {
                    return "cus_quote_ls100.docx";
                }
                else if (sQTTemplate == "quote_ls120 Word")
                {
                    return "cus_quote_ls120.docx";
                }
                else if (sQTTemplate == "quote_ls160 Word")
                {  
                    return "cus_quote_ls160.docx";
                }
                else if (sQTTemplate == "quote_ls200s Word")
                {
                    return "cus_quote_ls200s.docx";
                }
                else if (sQTTemplate == "quote_ls25 Word")
                {                      
                    return "cus_quote_ls25.docx";
                }
                else if (sQTTemplate == "quote_ts20 Word")
                {
                    return "cus_quote_ts20.docx";
                }
                else if (sQTTemplate == "quote_ts20 Word")
                {
                    return "cus_quote_ls200.docx";
                }
                else if (sQTTemplate == "quote_ts32 Word")
                {
                    return "cus_quote_ts32.docx";
                }
                else if (sQTTemplate == "quote_v120 Word")
                {
                    return "cus_quote_v120.docx";
                }
                else if (sQTTemplate == "quote_v160 Word")
                {
                    return "cus_quote_v160.docx";
                }
                else if (sQTTemplate == "quote_v200 Word")
                {
                    return "cus_quote_v200.docx";
                }
                else if (sQTTemplate == "quote_v200s Word")
                {
                    return "cus_quote_v200s.docx";
                }
                else if (sQTTemplate == "quote_vcc200 Word")
                {
                    return "cus_quote_vcc200.docx";
                }
                else if (sQTTemplate == "QuoteTemplate Word")
                {
                    return "cus_QuoteTemplate.docx";
                }
                else if (sQTTemplate == "sr 125 dryer")
                {
                    return "cus_quote_sr125_dryer.docx";
                }
                else if (sQTTemplate == "srv 400 dryer")
                {
                    return "cus_quote_srv400_dryer.docx";
                }
                else if (sQTTemplate == "srv 800 dryer")
                {
                    return "cus_quote_srv800_dryer.docx";
                }
                else if (sQTTemplate == "Temp KMR")
                {
                    return "cus_quote_temp_kmr.docx";
                }
            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);

            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);

            //rsQL.SetFieldVal("TXT_Model", sModelText);      
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
                doForm.doRS.SetFieldVal("SR__HYSPECOPROBPER", 0);
            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }
        public bool QT_MoveQuoteLine_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            string sDirection = "";
            string sLtGt = "";
            string sFirstLast = "";
            string sMoveSortDir = "";

            string sRecID = "";
            string sQuoteID = "";
            string sSelLineNO = "";
            string sMoveLineNO = "";
            double dSelLineNO = 0;
            double dMoveLineNO = 0;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot edit Quote Lines.");
                return true;
            }

            if (par_s3.ToUpper() == "UP")
            {
                sDirection = "up";
                sLtGt = "<";
                sFirstLast = "first";
                sMoveSortDir = "desc";
            }
            else if (par_s3.ToUpper() == "DOWN")
            {
                sDirection = "down";
                sLtGt = ">";
                sFirstLast = "last";
                sMoveSortDir = "asc";
            }
            else
            {
                return false;
            }

            sQuoteID = doForm.doRS.GetFieldVal("GID_ID").ToString();
            sRecID = doForm.GetLinkSelection("LNK_Connected_QL");

            if (string.IsNullOrEmpty(sRecID))
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }

            clRowSet doQLSelRS = new clRowSet("QL", clC.SELL_EDIT, "GID_ID=" + sRecID, "", "*,**", 1, par_bBypassValidation: true);
            if (doQLSelRS.GetFirst() == 0)
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }
            sSelLineNO = doQLSelRS.GetFieldVal("SR__LineNo").ToString();
            dSelLineNO = Convert.ToDouble(doQLSelRS.GetFieldVal("SR__LineNo"));

            clRowSet doQLMoveRS = new clRowSet("QL", clC.SELL_EDIT, ("LNK_IN_QT=" + sQuoteID + " AND SR__LineNo" + sLtGt + sSelLineNO), "SR__LineNo " + sMoveSortDir, "*,**", 1, par_bBypassValidation: true);
            if (doQLMoveRS.GetFirst() == 0)
            {
                doForm.MessageBox("This is the " + sFirstLast + " Qoute Line. Please select another Qoute Line to move " + sDirection + ".");
                par_doCallingObject = doForm;
                return false;
            }
            sMoveLineNO = doQLMoveRS.GetFieldVal("SR__LineNo").ToString();
            dMoveLineNO = Convert.ToDouble(doQLMoveRS.GetFieldVal("SR__LineNo"));

            doQLSelRS.SetFieldVal("SR__LineNo", dMoveLineNO, 2);
            doQLMoveRS.SetFieldVal("SR__LineNo", dSelLineNO, 2);

            if (doQLSelRS.Commit() == 0 || doQLMoveRS.Commit() == 0)
            {
                doForm.MessageBox("Could not save the Quote Lines. Please reopen the quote and try again.");
                par_doCallingObject = doForm;
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");
            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }

        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }

            return true;

        }

        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__HYSPECOPROBPER"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }
            if (dProb <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Probabability");
                doForm.FieldInFocus = "SR__HYSPECOPROBPER";
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_RELATED_PD,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OPPORTUNITYLINESName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);


            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }
     
          

            if (doRS.IsLinkEmpty("LNK_RELATED_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
              
                par_doCallingObject = doRS;
                return false;
            }

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                
                par_doCallingObject = doRS;
                return false;
            }

            if (iQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                
                par_doCallingObject = doRS;
                return false;
            }
            if (rprob <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Probabability");
              
                par_doCallingObject = doRS;
                return false;
            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool GenerateSysName_post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = "clScripts:GenerateSysName";
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";
            clRowSet doLink = default(clRowSet);
            int iLen = 0;
            string par_sDelim = " ";

            //We assume that sFileName is valid. If this is a problem, test it here and SetError.

            switch (Microsoft.VisualBasic.Strings.UCase(sFileName))
            {

                case "OP":
                    //==> OPP NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+LNK_For_Company%%TXT_CompanyName+" "+...
                    //				LNK_For_Product%%TXT_ProductName+" "+CUR_Value
                    //OPP		<For - Company - TXT_CompanyName> <For - Product - TXT_ProductName> CUR_ValueIndex (MLS_Status)  
                    //			OPP-COMPANY-0						OPP-PRODUCT-0

                    if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("LNK_For_CO"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_For_CO");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("LNK_For_PD"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_For_PD");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("DTT_Time"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".DTT_Time");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("CUR_Value"))
                    {

                        goErr.SetError(35103, sProc, "", sFileName + ".CUR_Value");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("MLS_Status"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".MLS_Status");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    //LNK_CreditedTo_US%%TXT_Code
                    sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp))
                    {
                        //No records linked
                        sTemp = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                        }
                        else
                        {
                            sTemp = "?";
                        }
                    }

                    //LNK_For_CO%%TXT_CompanyName
                    sTemp2 = doRS.GetFieldVal("LNK_For_CO", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp2))
                    {
                        //No records linked
                        sTemp2 = "";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CompanyName", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp2 = doLink.GetFieldVal("TXT_CompanyName", 0, 22).ToString();
                        }
                        else
                        {
                            sTemp2 = "";
                        }
                    }

                    //LNK_For_Product%%TXT_ProductName
                    sTemp3 = doRS.GetFieldVal("LNK_For_PD", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp3))
                    {
                        //No records linked
                        sTemp3 = "";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("PD", 3, "GID_ID='" + sTemp3 + "'", "", "TXT_ProductName", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp3 = doLink.GetFieldVal("TXT_ProductName", 0, 14).ToString();
                        }
                        else
                        {
                            sTemp3 = "";
                        }
                    }

                    //Company (23)   '25
                    //Date (15)      '11
                    //Credited To User (5)
                    //Product (15)   '17
                    //Value (13)
                    //Status (9)
                    //*** MI 10/4/07 Added LocalToUTC conversion
                    //sResult = sTemp2 & " " & _
                    //    goTR.DateToString(doRS.GetFieldVal("DTE_Time", clC.SELL_SYSTEM), "YYYY-MM-DD") & " " & _
                    //    sTemp & " " & _
                    //    sTemp3 & " " & _
                    //    goTR.Pad(doRS.GetFieldVal("CUR_Value"), 11, " ", "L")
                    DateTime dttttime = Convert.ToDateTime(doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM));
                    DateTime gotrdatedtttiem = Convert.ToDateTime(goTR.UTC_LocalToUTC(ref dttttime));
                    par_iValid = 4;
                    par_sDelim = " ";

                    sResult = sTemp2 + " " + Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(gotrdatedtttiem, ref par_iValid, ref par_sDelim), 10) + " GMT " + sTemp + " " + sTemp3 + " " + goTR.Pad(doRS.GetFieldVal("CUR_Value").ToString(), 11, " ", "L");

                    sResult += " [" + doRS.GetFieldVal("MLS_STATUS", 0, 8).ToString() + "]";

                    break;


                case "QT":
                    //==> QUOTE NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+
                    //					LNK_To_Company%%TXT_CompanyName+" "+CUR_Total

                    if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("LNK_To_CO"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_To_CO");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("DTT_Time"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".DTT_Time");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_QuoteNo"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_QuoteNo");
                        ///35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("CUR_Total"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".CUR_Total");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("MLS_STATUS"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".MLS_STATUS");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    //LNK_CreditedTo_US%%TXT_Code
                    sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp))
                    {
                        //No records linked
                        sTemp = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                        }
                        else
                        {
                            sTemp = "?";
                        }
                    }

                    //LNK_To_CO%%TXT_CompanyName
                    sTemp2 = doRS.GetFieldVal("LNK_To_CO", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp2))
                    {
                        //No records linked
                        sTemp2 = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CompanyName", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp2 = doLink.GetFieldVal("TXT_CompanyName").ToString();
                        }
                        else
                        {
                            sTemp2 = "?";
                        }
                    }


                    //Company 17 '21
                    //Date 15    '11
                    //Cred User 6
                    //Quote No 16
                    //Total 15
                    //Status 11
                    //Total: 80
                    sResult = goTR.Pad(sTemp2, 16, "", "R", true) + " " + goTR.Pad(sTemp3, 14, "", "R", true) + " " + goTR.Pad(sTemp, 4, "", "R", true) + " [" + goTR.Pad(doRS.GetFieldVal("TXT_QuoteNo").ToString(), 14, "", "R", true) + "] " + goTR.Pad(doRS.GetFieldVal("CUR_Total").ToString(), 13, "", "L", true) + " [" + doRS.GetFieldVal("MLS_STATUS", 0, 10).ToString() + "]";

                    break;

                case "CN":
                    //==> CONTACT NEW:	TXT_NameLast+", "+TXT_NameFirst+" "+TXT_ContactCode	
                    if (!doRS.IsLoaded("TXT_NameLast"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_NameLast");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_NameFirst"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_NameFirst");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_ContactCode"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_ContactCode");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TEL_BusPhone"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TEL_BusPhone");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("LNK_Related_CO"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_Related_CO");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_TitleText"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_TitleText");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    //Considering that the length of typical contents in these fields, especially
                    //BusPhone and Title, is smaller, we are allowing the total length to exceed 80
                    //and are Left()-ing it to 80 (SYS_Name length).
                    //TXT_NameLast 22+2=24
                    //TXT_NameFirst 15+1=16
                    //TXT_ContactCode 10+1=11
                    //TEL_BusPhone 39+2=41
                    //NEW: TXT_TitleText 50+2=52
                    //LNK_Related_CO 21
                    //TOTAL: 113

                    //Last Name, First Name
                    sResult = doRS.GetFieldVal("TXT_NameFirst").ToString();
                    sTemp = doRS.GetFieldVal("TXT_NameLast").ToString();
                    if (!string.IsNullOrEmpty(sTemp) & !string.IsNullOrEmpty(sResult))
                    {
                        sResult = sResult + " " + sTemp;
                    }
                    else
                    {
                        sResult = sResult + sTemp;
                    }

                    //*** MI 11/14/07
                    //Contact Code
                    sTemp = doRS.GetFieldVal("TXT_ContactCode").ToString();
                    if (!string.IsNullOrEmpty(sTemp))
                    {
                        sResult = sResult + " " + sTemp;
                    }

                    //Business Phone
                    sTemp2 = doRS.GetFieldVal("TEL_BusPhone").ToString();
                    if (!string.IsNullOrEmpty(sTemp2))
                    {
                        sResult = sResult + " " + sTemp2;
                    }
                    iLen = Microsoft.VisualBasic.Strings.Len(sResult);

                    //CS 8/21/08: Add title to sys name
                    sTemp4 = doRS.GetFieldVal("TXT_TitleText").ToString();
                    if (!string.IsNullOrEmpty(sTemp4))
                    {
                        sResult = sResult + " " + sTemp4;
                    }

                    //Related Company
                    sTemp3 = doRS.GetFieldVal("LNK_Related_CO", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp3))
                    {
                        //No records linked
                        sTemp3 = "";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp3 + "'", "", "TXT_CompanyName", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp3 = "(" + doLink.GetFieldVal("TXT_CompanyName", 0, 20).ToString() + ")";
                        }
                        else
                        {
                            sTemp3 = "(?)";
                        }
                    }
                    sResult = Microsoft.VisualBasic.Strings.Left(sResult + " " + sTemp3, 80).ToString();
                    break;

                case "CO":
                    //==> COMPANY NEW:	TXT_CompanyName+" - "+TXT_CityMailing+", "+TXT_StateMailing
                    if (!doRS.IsLoaded("TXT_CompanyName"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_CompanyName");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_CityMailing"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_CityMailing");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_StateMailing"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_StateMailing");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TEL_PhoneNo"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TEL_PhoneNo");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    sResult = doRS.GetFieldVal("TXT_CompanyName").ToString();
                    sTemp = doRS.GetFieldVal("TXT_CityMailing").ToString();
                    sTemp2 = doRS.GetFieldVal("TXT_StateMailing").ToString();
                    if (!string.IsNullOrEmpty(sTemp) | !string.IsNullOrEmpty(sTemp2))
                    {
                        sResult += " - ";
                    }
                    if (!string.IsNullOrEmpty(sTemp))
                    {
                        sResult += sTemp;
                        if (!string.IsNullOrEmpty(sTemp2))
                        {
                            sResult += ", ";
                        }
                    }
                    if (!string.IsNullOrEmpty(sTemp2))
                    {
                        sResult += sTemp2;
                    }
                    sTemp3 = doRS.GetFieldVal("TEL_PhoneNo").ToString();
                    if (!string.IsNullOrEmpty(sTemp3))
                    {
                        sResult += " " + sTemp3;
                    }
                    sResult = Microsoft.VisualBasic.Strings.Left(sResult, 80);

                    break;
                case "US":

                    //sTemp = goTR.Pad(doRS.GetFieldVal("SR__LineNo", clC.SELL_FRIENDLY).ToString(), 6, " ", "L", true, "R");
                    //sTemp = doRS.GetFieldVal("SR__LineNo", clC.SELL_FRIENDLY).ToString();


                    sTemp2 = Convert.ToString(doRS.GetFieldVal("TXT_NAMEFIRST", 2));
                    sTemp3 = Convert.ToString(doRS.GetFieldVal("TXT_NAMELAST", 2));
                    //sTemp4 = Convert.ToString(doRS.GetFieldVal("TXT_UNIT"));

                    sResult = sTemp2 + " " + sTemp3;
                    par_bRunNext = false;
                    break;
                    


            }

            par_oReturn = sResult;

            return true;

        }

        public bool OL_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.oVar.SetVar("OppID", doRS.GetFieldVal("LNK_IN_OP"));

            par_doCallingObject = doRS;

            return true;
        }

        public bool OL_RecordAfterDelete_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_s2 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sID = doRS.oVar.GetVar("OppID").ToString();

            if (!string.IsNullOrEmpty(sID))
            {
                clRowSet doOPLines = default(clRowSet);

                //Create a rowset of quote lines linked to the deleted quote.
                doOPLines = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo,Gid_id", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    StringBuilder sbQuery = new StringBuilder();
                    for (int i = 1; i <= doOPLines.Count(); i++)
                    {
                        double iLineno = Convert.ToDouble(doOPLines.GetFieldVal("SR__LineNo"));
                        string sGid_id = Convert.ToString(doOPLines.GetFieldVal("Gid_ID"));
                        string sTemp = "#" + goTR.Pad(i.ToString(), 6, " ", "L", true, "R");
                        sbQuery.AppendLine("Update OL Set SR__LineNo='" + i.ToString() + "',sys_name='" + sTemp + "' where Gid_id='" + sGid_id + "' AND SR__LineNo = '" + iLineno.ToString() + "'");

                        if (doOPLines.GetNext() == 0)
                        {
                            break;
                        }

                    }
                    if (!string.IsNullOrEmpty(sbQuery.ToString()))
                    {
                        bool bretval = goData.RunSQLQuery(sbQuery.ToString());
                    }

                }

                doOPLines = null;

            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool OL_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("Cur_UnitPrice", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__Qty", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__prob", "LABELCOLOR", color);
            doForm.SetFieldProperty("LNK_RELATED_PD", "LABELCOLOR", color);


            par_bRunNext = false;

            par_doCallingObject = doForm;

            return true;
        }
        public bool FD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sTitle = "";
            string sUserGid = "";

            sTitle = Convert.ToString(doRS.GetFieldVal("TXT_TITLE"));
            sUserGid = Convert.ToString(doRS.GetFieldVal("LNK_TO_US"));

            goUI.AddAlert(sTitle + "  file is ready to download", "OPENDESKTOP", "DSK_4E8207CE-28DE-4F55-5858-B0B300B6D2B2", sUserGid, "").ToString();

            par_doCallingObject = doRS;
            return true;
        }
    }
}

﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace Selltis.WebApi.com.selltis.selltis {
    using System.Diagnostics;
    using System;
    using System.Xml.Serialization;
    using System.ComponentModel;
    using System.Web.Services.Protocols;
    using System.Web.Services;
    using System.Data;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="WebService_RowSetSoap", Namespace="http://tempuri.org/")]
    public partial class WebService_RowSet : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback LogonOperationCompleted;
        
        private System.Threading.SendOrPostCallback SetVarOperationCompleted;
        
        private System.Threading.SendOrPostCallback NewRSOperationCompleted;
        
        private System.Threading.SendOrPostCallback BypassOperationCompleted;
        
        private System.Threading.SendOrPostCallback SetFieldValOperationCompleted;
        
        private System.Threading.SendOrPostCallback SetFieldValsOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetFieldValOperationCompleted;
        
        private System.Threading.SendOrPostCallback LogOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetLinkValOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetFieldValsOperationCompleted;
        
        private System.Threading.SendOrPostCallback ProcessingInstructionsOperationCompleted;
        
        private System.Threading.SendOrPostCallback ClearLinkOperationCompleted;
        
        private System.Threading.SendOrPostCallback ClearLinkAllOperationCompleted;
        
        private System.Threading.SendOrPostCallback CommitOperationCompleted;
        
        private System.Threading.SendOrPostCallback CountOperationCompleted;
        
        private System.Threading.SendOrPostCallback DeleteRecordOperationCompleted;
        
        private System.Threading.SendOrPostCallback DeleteAllOperationCompleted;
        
        private System.Threading.SendOrPostCallback ToTableOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetTransTableOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetTransTableAsXMLOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetTransTableAsXMLWithCountOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetTransTableAsXMLStringOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetLastErrorOperationCompleted;
        
        private System.Threading.SendOrPostCallback LogoffOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetMetaPageOperationCompleted;
        
        private System.Threading.SendOrPostCallback ResetConduitOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetMeOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetLoginByUserIDOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetQuoteSubjectOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetFieldLabelFromNameOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetFileLabelOperationCompleted;
        
        private System.Threading.SendOrPostCallback TestIDsForDeletionsOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetFieldListOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetLinksListOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetFileListOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetPermissionOperationCompleted;
        
        private System.Threading.SendOrPostCallback ValidateSessionOperationCompleted;
        
        private System.Threading.SendOrPostCallback AddRecordsByXMLOperationCompleted;
        
        private System.Threading.SendOrPostCallback AddRecordsByXMLStringOperationCompleted;
        
        private System.Threading.SendOrPostCallback AddRecordsByDatasetOperationCompleted;
        
        private System.Threading.SendOrPostCallback EditRecordsByXMLOperationCompleted;
        
        private System.Threading.SendOrPostCallback EditRecordsByXMLStringOperationCompleted;
        
        private System.Threading.SendOrPostCallback EditRecordsByDatasetOperationCompleted;
        
        private System.Threading.SendOrPostCallback UpsertRecordsByXMLOperationCompleted;
        
        private System.Threading.SendOrPostCallback UpsertRecordsByXMLStringOperationCompleted;
        
        private System.Threading.SendOrPostCallback UpsertRecordsByDatasetOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetListOperationCompleted;
        
        private System.Threading.SendOrPostCallback DatasetToTextFileOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetSchemaAsXMLOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetSchemaAsXMLStringOperationCompleted;
        
        private System.Threading.SendOrPostCallback DeleteRecordsByXMLStringOperationCompleted;
        
        private System.Threading.SendOrPostCallback DeleteRecordsByXMLOperationCompleted;
        
        private System.Threading.SendOrPostCallback UploadAndProcessDatasetOperationCompleted;
        
        private System.Threading.SendOrPostCallback UpdateStatusOperationCompleted;
        
        private System.Threading.SendOrPostCallback UpdateS3StatusOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetSystemCodesOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetStandardizedPhoneNumberOperationCompleted;
        
        private System.Threading.SendOrPostCallback LogErrorOperationCompleted;
        
        private System.Threading.SendOrPostCallback AddAlertOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public WebService_RowSet() {
            this.Url = global::Selltis.WebApi.Properties.Settings.Default.Selltis_WebApi_com_selltis_selltis_WebService_RowSet;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event LogonCompletedEventHandler LogonCompleted;
        
        /// <remarks/>
        public event SetVarCompletedEventHandler SetVarCompleted;
        
        /// <remarks/>
        public event NewRSCompletedEventHandler NewRSCompleted;
        
        /// <remarks/>
        public event BypassCompletedEventHandler BypassCompleted;
        
        /// <remarks/>
        public event SetFieldValCompletedEventHandler SetFieldValCompleted;
        
        /// <remarks/>
        public event SetFieldValsCompletedEventHandler SetFieldValsCompleted;
        
        /// <remarks/>
        public event GetFieldValCompletedEventHandler GetFieldValCompleted;
        
        /// <remarks/>
        public event LogCompletedEventHandler LogCompleted;
        
        /// <remarks/>
        public event GetLinkValCompletedEventHandler GetLinkValCompleted;
        
        /// <remarks/>
        public event GetFieldValsCompletedEventHandler GetFieldValsCompleted;
        
        /// <remarks/>
        public event ProcessingInstructionsCompletedEventHandler ProcessingInstructionsCompleted;
        
        /// <remarks/>
        public event ClearLinkCompletedEventHandler ClearLinkCompleted;
        
        /// <remarks/>
        public event ClearLinkAllCompletedEventHandler ClearLinkAllCompleted;
        
        /// <remarks/>
        public event CommitCompletedEventHandler CommitCompleted;
        
        /// <remarks/>
        public event CountCompletedEventHandler CountCompleted;
        
        /// <remarks/>
        public event DeleteRecordCompletedEventHandler DeleteRecordCompleted;
        
        /// <remarks/>
        public event DeleteAllCompletedEventHandler DeleteAllCompleted;
        
        /// <remarks/>
        public event ToTableCompletedEventHandler ToTableCompleted;
        
        /// <remarks/>
        public event GetTransTableCompletedEventHandler GetTransTableCompleted;
        
        /// <remarks/>
        public event GetTransTableAsXMLCompletedEventHandler GetTransTableAsXMLCompleted;
        
        /// <remarks/>
        public event GetTransTableAsXMLWithCountCompletedEventHandler GetTransTableAsXMLWithCountCompleted;
        
        /// <remarks/>
        public event GetTransTableAsXMLStringCompletedEventHandler GetTransTableAsXMLStringCompleted;
        
        /// <remarks/>
        public event GetLastErrorCompletedEventHandler GetLastErrorCompleted;
        
        /// <remarks/>
        public event LogoffCompletedEventHandler LogoffCompleted;
        
        /// <remarks/>
        public event GetMetaPageCompletedEventHandler GetMetaPageCompleted;
        
        /// <remarks/>
        public event ResetConduitCompletedEventHandler ResetConduitCompleted;
        
        /// <remarks/>
        public event GetMeCompletedEventHandler GetMeCompleted;
        
        /// <remarks/>
        public event GetLoginByUserIDCompletedEventHandler GetLoginByUserIDCompleted;
        
        /// <remarks/>
        public event GetQuoteSubjectCompletedEventHandler GetQuoteSubjectCompleted;
        
        /// <remarks/>
        public event GetFieldLabelFromNameCompletedEventHandler GetFieldLabelFromNameCompleted;
        
        /// <remarks/>
        public event GetFileLabelCompletedEventHandler GetFileLabelCompleted;
        
        /// <remarks/>
        public event TestIDsForDeletionsCompletedEventHandler TestIDsForDeletionsCompleted;
        
        /// <remarks/>
        public event GetFieldListCompletedEventHandler GetFieldListCompleted;
        
        /// <remarks/>
        public event GetLinksListCompletedEventHandler GetLinksListCompleted;
        
        /// <remarks/>
        public event GetFileListCompletedEventHandler GetFileListCompleted;
        
        /// <remarks/>
        public event GetPermissionCompletedEventHandler GetPermissionCompleted;
        
        /// <remarks/>
        public event ValidateSessionCompletedEventHandler ValidateSessionCompleted;
        
        /// <remarks/>
        public event AddRecordsByXMLCompletedEventHandler AddRecordsByXMLCompleted;
        
        /// <remarks/>
        public event AddRecordsByXMLStringCompletedEventHandler AddRecordsByXMLStringCompleted;
        
        /// <remarks/>
        public event AddRecordsByDatasetCompletedEventHandler AddRecordsByDatasetCompleted;
        
        /// <remarks/>
        public event EditRecordsByXMLCompletedEventHandler EditRecordsByXMLCompleted;
        
        /// <remarks/>
        public event EditRecordsByXMLStringCompletedEventHandler EditRecordsByXMLStringCompleted;
        
        /// <remarks/>
        public event EditRecordsByDatasetCompletedEventHandler EditRecordsByDatasetCompleted;
        
        /// <remarks/>
        public event UpsertRecordsByXMLCompletedEventHandler UpsertRecordsByXMLCompleted;
        
        /// <remarks/>
        public event UpsertRecordsByXMLStringCompletedEventHandler UpsertRecordsByXMLStringCompleted;
        
        /// <remarks/>
        public event UpsertRecordsByDatasetCompletedEventHandler UpsertRecordsByDatasetCompleted;
        
        /// <remarks/>
        public event GetListCompletedEventHandler GetListCompleted;
        
        /// <remarks/>
        public event DatasetToTextFileCompletedEventHandler DatasetToTextFileCompleted;
        
        /// <remarks/>
        public event GetSchemaAsXMLCompletedEventHandler GetSchemaAsXMLCompleted;
        
        /// <remarks/>
        public event GetSchemaAsXMLStringCompletedEventHandler GetSchemaAsXMLStringCompleted;
        
        /// <remarks/>
        public event DeleteRecordsByXMLStringCompletedEventHandler DeleteRecordsByXMLStringCompleted;
        
        /// <remarks/>
        public event DeleteRecordsByXMLCompletedEventHandler DeleteRecordsByXMLCompleted;
        
        /// <remarks/>
        public event UploadAndProcessDatasetCompletedEventHandler UploadAndProcessDatasetCompleted;
        
        /// <remarks/>
        public event UpdateStatusCompletedEventHandler UpdateStatusCompleted;
        
        /// <remarks/>
        public event UpdateS3StatusCompletedEventHandler UpdateS3StatusCompleted;
        
        /// <remarks/>
        public event GetSystemCodesCompletedEventHandler GetSystemCodesCompleted;
        
        /// <remarks/>
        public event GetStandardizedPhoneNumberCompletedEventHandler GetStandardizedPhoneNumberCompleted;
        
        /// <remarks/>
        public event LogErrorCompletedEventHandler LogErrorCompleted;
        
        /// <remarks/>
        public event AddAlertCompletedEventHandler AddAlertCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Logon", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool Logon(string sUser, string sPassword) {
            object[] results = this.Invoke("Logon", new object[] {
                        sUser,
                        sPassword});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void LogonAsync(string sUser, string sPassword) {
            this.LogonAsync(sUser, sPassword, null);
        }
        
        /// <remarks/>
        public void LogonAsync(string sUser, string sPassword, object userState) {
            if ((this.LogonOperationCompleted == null)) {
                this.LogonOperationCompleted = new System.Threading.SendOrPostCallback(this.OnLogonOperationCompleted);
            }
            this.InvokeAsync("Logon", new object[] {
                        sUser,
                        sPassword}, this.LogonOperationCompleted, userState);
        }
        
        private void OnLogonOperationCompleted(object arg) {
            if ((this.LogonCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.LogonCompleted(this, new LogonCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/SetVar", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool SetVar(string sProperty, string sValue) {
            object[] results = this.Invoke("SetVar", new object[] {
                        sProperty,
                        sValue});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void SetVarAsync(string sProperty, string sValue) {
            this.SetVarAsync(sProperty, sValue, null);
        }
        
        /// <remarks/>
        public void SetVarAsync(string sProperty, string sValue, object userState) {
            if ((this.SetVarOperationCompleted == null)) {
                this.SetVarOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSetVarOperationCompleted);
            }
            this.InvokeAsync("SetVar", new object[] {
                        sProperty,
                        sValue}, this.SetVarOperationCompleted, userState);
        }
        
        private void OnSetVarOperationCompleted(object arg) {
            if ((this.SetVarCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SetVarCompleted(this, new SetVarCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/NewRS", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool NewRS(string sFile, int iType, string sCondition, string sSort, string sFields, int iTop, string sINI, string par_sGenFieldsDefs) {
            object[] results = this.Invoke("NewRS", new object[] {
                        sFile,
                        iType,
                        sCondition,
                        sSort,
                        sFields,
                        iTop,
                        sINI,
                        par_sGenFieldsDefs});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void NewRSAsync(string sFile, int iType, string sCondition, string sSort, string sFields, int iTop, string sINI, string par_sGenFieldsDefs) {
            this.NewRSAsync(sFile, iType, sCondition, sSort, sFields, iTop, sINI, par_sGenFieldsDefs, null);
        }
        
        /// <remarks/>
        public void NewRSAsync(string sFile, int iType, string sCondition, string sSort, string sFields, int iTop, string sINI, string par_sGenFieldsDefs, object userState) {
            if ((this.NewRSOperationCompleted == null)) {
                this.NewRSOperationCompleted = new System.Threading.SendOrPostCallback(this.OnNewRSOperationCompleted);
            }
            this.InvokeAsync("NewRS", new object[] {
                        sFile,
                        iType,
                        sCondition,
                        sSort,
                        sFields,
                        iTop,
                        sINI,
                        par_sGenFieldsDefs}, this.NewRSOperationCompleted, userState);
        }
        
        private void OnNewRSOperationCompleted(object arg) {
            if ((this.NewRSCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.NewRSCompleted(this, new NewRSCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Bypass", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool Bypass(bool bBypassValidation, bool bNoRecordOnSave) {
            object[] results = this.Invoke("Bypass", new object[] {
                        bBypassValidation,
                        bNoRecordOnSave});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void BypassAsync(bool bBypassValidation, bool bNoRecordOnSave) {
            this.BypassAsync(bBypassValidation, bNoRecordOnSave, null);
        }
        
        /// <remarks/>
        public void BypassAsync(bool bBypassValidation, bool bNoRecordOnSave, object userState) {
            if ((this.BypassOperationCompleted == null)) {
                this.BypassOperationCompleted = new System.Threading.SendOrPostCallback(this.OnBypassOperationCompleted);
            }
            this.InvokeAsync("Bypass", new object[] {
                        bBypassValidation,
                        bNoRecordOnSave}, this.BypassOperationCompleted, userState);
        }
        
        private void OnBypassOperationCompleted(object arg) {
            if ((this.BypassCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.BypassCompleted(this, new BypassCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/SetFieldVal", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int SetFieldVal(string sFieldName, object oValue, int iFormat) {
            object[] results = this.Invoke("SetFieldVal", new object[] {
                        sFieldName,
                        oValue,
                        iFormat});
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void SetFieldValAsync(string sFieldName, object oValue, int iFormat) {
            this.SetFieldValAsync(sFieldName, oValue, iFormat, null);
        }
        
        /// <remarks/>
        public void SetFieldValAsync(string sFieldName, object oValue, int iFormat, object userState) {
            if ((this.SetFieldValOperationCompleted == null)) {
                this.SetFieldValOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSetFieldValOperationCompleted);
            }
            this.InvokeAsync("SetFieldVal", new object[] {
                        sFieldName,
                        oValue,
                        iFormat}, this.SetFieldValOperationCompleted, userState);
        }
        
        private void OnSetFieldValOperationCompleted(object arg) {
            if ((this.SetFieldValCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SetFieldValCompleted(this, new SetFieldValCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/SetFieldVals", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int SetFieldVals(string sIni) {
            object[] results = this.Invoke("SetFieldVals", new object[] {
                        sIni});
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void SetFieldValsAsync(string sIni) {
            this.SetFieldValsAsync(sIni, null);
        }
        
        /// <remarks/>
        public void SetFieldValsAsync(string sIni, object userState) {
            if ((this.SetFieldValsOperationCompleted == null)) {
                this.SetFieldValsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSetFieldValsOperationCompleted);
            }
            this.InvokeAsync("SetFieldVals", new object[] {
                        sIni}, this.SetFieldValsOperationCompleted, userState);
        }
        
        private void OnSetFieldValsOperationCompleted(object arg) {
            if ((this.SetFieldValsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SetFieldValsCompleted(this, new SetFieldValsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetFieldVal", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public object GetFieldVal(string sFieldName, int iFormat) {
            object[] results = this.Invoke("GetFieldVal", new object[] {
                        sFieldName,
                        iFormat});
            return ((object)(results[0]));
        }
        
        /// <remarks/>
        public void GetFieldValAsync(string sFieldName, int iFormat) {
            this.GetFieldValAsync(sFieldName, iFormat, null);
        }
        
        /// <remarks/>
        public void GetFieldValAsync(string sFieldName, int iFormat, object userState) {
            if ((this.GetFieldValOperationCompleted == null)) {
                this.GetFieldValOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetFieldValOperationCompleted);
            }
            this.InvokeAsync("GetFieldVal", new object[] {
                        sFieldName,
                        iFormat}, this.GetFieldValOperationCompleted, userState);
        }
        
        private void OnGetFieldValOperationCompleted(object arg) {
            if ((this.GetFieldValCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetFieldValCompleted(this, new GetFieldValCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Log", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool Log(string par_sMethod, string sErrorMessage, int iLevel) {
            object[] results = this.Invoke("Log", new object[] {
                        par_sMethod,
                        sErrorMessage,
                        iLevel});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void LogAsync(string par_sMethod, string sErrorMessage, int iLevel) {
            this.LogAsync(par_sMethod, sErrorMessage, iLevel, null);
        }
        
        /// <remarks/>
        public void LogAsync(string par_sMethod, string sErrorMessage, int iLevel, object userState) {
            if ((this.LogOperationCompleted == null)) {
                this.LogOperationCompleted = new System.Threading.SendOrPostCallback(this.OnLogOperationCompleted);
            }
            this.InvokeAsync("Log", new object[] {
                        par_sMethod,
                        sErrorMessage,
                        iLevel}, this.LogOperationCompleted, userState);
        }
        
        private void OnLogOperationCompleted(object arg) {
            if ((this.LogCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.LogCompleted(this, new LogCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetLinkVal", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetLinkVal(string sFieldName, string sDelim) {
            object[] results = this.Invoke("GetLinkVal", new object[] {
                        sFieldName,
                        sDelim});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetLinkValAsync(string sFieldName, string sDelim) {
            this.GetLinkValAsync(sFieldName, sDelim, null);
        }
        
        /// <remarks/>
        public void GetLinkValAsync(string sFieldName, string sDelim, object userState) {
            if ((this.GetLinkValOperationCompleted == null)) {
                this.GetLinkValOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetLinkValOperationCompleted);
            }
            this.InvokeAsync("GetLinkVal", new object[] {
                        sFieldName,
                        sDelim}, this.GetLinkValOperationCompleted, userState);
        }
        
        private void OnGetLinkValOperationCompleted(object arg) {
            if ((this.GetLinkValCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetLinkValCompleted(this, new GetLinkValCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetFieldVals", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetFieldVals(string sFields) {
            object[] results = this.Invoke("GetFieldVals", new object[] {
                        sFields});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetFieldValsAsync(string sFields) {
            this.GetFieldValsAsync(sFields, null);
        }
        
        /// <remarks/>
        public void GetFieldValsAsync(string sFields, object userState) {
            if ((this.GetFieldValsOperationCompleted == null)) {
                this.GetFieldValsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetFieldValsOperationCompleted);
            }
            this.InvokeAsync("GetFieldVals", new object[] {
                        sFields}, this.GetFieldValsOperationCompleted, userState);
        }
        
        private void OnGetFieldValsOperationCompleted(object arg) {
            if ((this.GetFieldValsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetFieldValsCompleted(this, new GetFieldValsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/ProcessingInstructions", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool ProcessingInstructions(string sInstructions) {
            object[] results = this.Invoke("ProcessingInstructions", new object[] {
                        sInstructions});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void ProcessingInstructionsAsync(string sInstructions) {
            this.ProcessingInstructionsAsync(sInstructions, null);
        }
        
        /// <remarks/>
        public void ProcessingInstructionsAsync(string sInstructions, object userState) {
            if ((this.ProcessingInstructionsOperationCompleted == null)) {
                this.ProcessingInstructionsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnProcessingInstructionsOperationCompleted);
            }
            this.InvokeAsync("ProcessingInstructions", new object[] {
                        sInstructions}, this.ProcessingInstructionsOperationCompleted, userState);
        }
        
        private void OnProcessingInstructionsOperationCompleted(object arg) {
            if ((this.ProcessingInstructionsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ProcessingInstructionsCompleted(this, new ProcessingInstructionsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/ClearLink", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int ClearLink(string sLinkName, object oLinks) {
            object[] results = this.Invoke("ClearLink", new object[] {
                        sLinkName,
                        oLinks});
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void ClearLinkAsync(string sLinkName, object oLinks) {
            this.ClearLinkAsync(sLinkName, oLinks, null);
        }
        
        /// <remarks/>
        public void ClearLinkAsync(string sLinkName, object oLinks, object userState) {
            if ((this.ClearLinkOperationCompleted == null)) {
                this.ClearLinkOperationCompleted = new System.Threading.SendOrPostCallback(this.OnClearLinkOperationCompleted);
            }
            this.InvokeAsync("ClearLink", new object[] {
                        sLinkName,
                        oLinks}, this.ClearLinkOperationCompleted, userState);
        }
        
        private void OnClearLinkOperationCompleted(object arg) {
            if ((this.ClearLinkCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ClearLinkCompleted(this, new ClearLinkCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/ClearLinkAll", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int ClearLinkAll(string sLinkName) {
            object[] results = this.Invoke("ClearLinkAll", new object[] {
                        sLinkName});
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void ClearLinkAllAsync(string sLinkName) {
            this.ClearLinkAllAsync(sLinkName, null);
        }
        
        /// <remarks/>
        public void ClearLinkAllAsync(string sLinkName, object userState) {
            if ((this.ClearLinkAllOperationCompleted == null)) {
                this.ClearLinkAllOperationCompleted = new System.Threading.SendOrPostCallback(this.OnClearLinkAllOperationCompleted);
            }
            this.InvokeAsync("ClearLinkAll", new object[] {
                        sLinkName}, this.ClearLinkAllOperationCompleted, userState);
        }
        
        private void OnClearLinkAllOperationCompleted(object arg) {
            if ((this.ClearLinkAllCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ClearLinkAllCompleted(this, new ClearLinkAllCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Commit", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int Commit() {
            object[] results = this.Invoke("Commit", new object[0]);
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void CommitAsync() {
            this.CommitAsync(null);
        }
        
        /// <remarks/>
        public void CommitAsync(object userState) {
            if ((this.CommitOperationCompleted == null)) {
                this.CommitOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCommitOperationCompleted);
            }
            this.InvokeAsync("Commit", new object[0], this.CommitOperationCompleted, userState);
        }
        
        private void OnCommitOperationCompleted(object arg) {
            if ((this.CommitCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CommitCompleted(this, new CommitCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Count", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public long Count() {
            object[] results = this.Invoke("Count", new object[0]);
            return ((long)(results[0]));
        }
        
        /// <remarks/>
        public void CountAsync() {
            this.CountAsync(null);
        }
        
        /// <remarks/>
        public void CountAsync(object userState) {
            if ((this.CountOperationCompleted == null)) {
                this.CountOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCountOperationCompleted);
            }
            this.InvokeAsync("Count", new object[0], this.CountOperationCompleted, userState);
        }
        
        private void OnCountOperationCompleted(object arg) {
            if ((this.CountCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CountCompleted(this, new CountCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/DeleteRecord", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int DeleteRecord() {
            object[] results = this.Invoke("DeleteRecord", new object[0]);
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void DeleteRecordAsync() {
            this.DeleteRecordAsync(null);
        }
        
        /// <remarks/>
        public void DeleteRecordAsync(object userState) {
            if ((this.DeleteRecordOperationCompleted == null)) {
                this.DeleteRecordOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDeleteRecordOperationCompleted);
            }
            this.InvokeAsync("DeleteRecord", new object[0], this.DeleteRecordOperationCompleted, userState);
        }
        
        private void OnDeleteRecordOperationCompleted(object arg) {
            if ((this.DeleteRecordCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DeleteRecordCompleted(this, new DeleteRecordCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/DeleteAll", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int DeleteAll() {
            object[] results = this.Invoke("DeleteAll", new object[0]);
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void DeleteAllAsync() {
            this.DeleteAllAsync(null);
        }
        
        /// <remarks/>
        public void DeleteAllAsync(object userState) {
            if ((this.DeleteAllOperationCompleted == null)) {
                this.DeleteAllOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDeleteAllOperationCompleted);
            }
            this.InvokeAsync("DeleteAll", new object[0], this.DeleteAllOperationCompleted, userState);
        }
        
        private void OnDeleteAllOperationCompleted(object arg) {
            if ((this.DeleteAllCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DeleteAllCompleted(this, new DeleteAllCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/ToTable", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool ToTable() {
            object[] results = this.Invoke("ToTable", new object[0]);
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void ToTableAsync() {
            this.ToTableAsync(null);
        }
        
        /// <remarks/>
        public void ToTableAsync(object userState) {
            if ((this.ToTableOperationCompleted == null)) {
                this.ToTableOperationCompleted = new System.Threading.SendOrPostCallback(this.OnToTableOperationCompleted);
            }
            this.InvokeAsync("ToTable", new object[0], this.ToTableOperationCompleted, userState);
        }
        
        private void OnToTableOperationCompleted(object arg) {
            if ((this.ToTableCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ToTableCompleted(this, new ToTableCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetTransTable", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet GetTransTable() {
            object[] results = this.Invoke("GetTransTable", new object[0]);
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void GetTransTableAsync() {
            this.GetTransTableAsync(null);
        }
        
        /// <remarks/>
        public void GetTransTableAsync(object userState) {
            if ((this.GetTransTableOperationCompleted == null)) {
                this.GetTransTableOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetTransTableOperationCompleted);
            }
            this.InvokeAsync("GetTransTable", new object[0], this.GetTransTableOperationCompleted, userState);
        }
        
        private void OnGetTransTableOperationCompleted(object arg) {
            if ((this.GetTransTableCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetTransTableCompleted(this, new GetTransTableCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetTransTableAsXML", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Xml.XmlNode GetTransTableAsXML() {
            object[] results = this.Invoke("GetTransTableAsXML", new object[0]);
            return ((System.Xml.XmlNode)(results[0]));
        }
        
        /// <remarks/>
        public void GetTransTableAsXMLAsync() {
            this.GetTransTableAsXMLAsync(null);
        }
        
        /// <remarks/>
        public void GetTransTableAsXMLAsync(object userState) {
            if ((this.GetTransTableAsXMLOperationCompleted == null)) {
                this.GetTransTableAsXMLOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetTransTableAsXMLOperationCompleted);
            }
            this.InvokeAsync("GetTransTableAsXML", new object[0], this.GetTransTableAsXMLOperationCompleted, userState);
        }
        
        private void OnGetTransTableAsXMLOperationCompleted(object arg) {
            if ((this.GetTransTableAsXMLCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetTransTableAsXMLCompleted(this, new GetTransTableAsXMLCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetTransTableAsXMLWithCount", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Xml.XmlNode GetTransTableAsXMLWithCount(int Start, int RecordCount) {
            object[] results = this.Invoke("GetTransTableAsXMLWithCount", new object[] {
                        Start,
                        RecordCount});
            return ((System.Xml.XmlNode)(results[0]));
        }
        
        /// <remarks/>
        public void GetTransTableAsXMLWithCountAsync(int Start, int RecordCount) {
            this.GetTransTableAsXMLWithCountAsync(Start, RecordCount, null);
        }
        
        /// <remarks/>
        public void GetTransTableAsXMLWithCountAsync(int Start, int RecordCount, object userState) {
            if ((this.GetTransTableAsXMLWithCountOperationCompleted == null)) {
                this.GetTransTableAsXMLWithCountOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetTransTableAsXMLWithCountOperationCompleted);
            }
            this.InvokeAsync("GetTransTableAsXMLWithCount", new object[] {
                        Start,
                        RecordCount}, this.GetTransTableAsXMLWithCountOperationCompleted, userState);
        }
        
        private void OnGetTransTableAsXMLWithCountOperationCompleted(object arg) {
            if ((this.GetTransTableAsXMLWithCountCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetTransTableAsXMLWithCountCompleted(this, new GetTransTableAsXMLWithCountCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetTransTableAsXMLString", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetTransTableAsXMLString() {
            object[] results = this.Invoke("GetTransTableAsXMLString", new object[0]);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetTransTableAsXMLStringAsync() {
            this.GetTransTableAsXMLStringAsync(null);
        }
        
        /// <remarks/>
        public void GetTransTableAsXMLStringAsync(object userState) {
            if ((this.GetTransTableAsXMLStringOperationCompleted == null)) {
                this.GetTransTableAsXMLStringOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetTransTableAsXMLStringOperationCompleted);
            }
            this.InvokeAsync("GetTransTableAsXMLString", new object[0], this.GetTransTableAsXMLStringOperationCompleted, userState);
        }
        
        private void OnGetTransTableAsXMLStringOperationCompleted(object arg) {
            if ((this.GetTransTableAsXMLStringCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetTransTableAsXMLStringCompleted(this, new GetTransTableAsXMLStringCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetLastError", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetLastError(string par_sParam) {
            object[] results = this.Invoke("GetLastError", new object[] {
                        par_sParam});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetLastErrorAsync(string par_sParam) {
            this.GetLastErrorAsync(par_sParam, null);
        }
        
        /// <remarks/>
        public void GetLastErrorAsync(string par_sParam, object userState) {
            if ((this.GetLastErrorOperationCompleted == null)) {
                this.GetLastErrorOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetLastErrorOperationCompleted);
            }
            this.InvokeAsync("GetLastError", new object[] {
                        par_sParam}, this.GetLastErrorOperationCompleted, userState);
        }
        
        private void OnGetLastErrorOperationCompleted(object arg) {
            if ((this.GetLastErrorCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetLastErrorCompleted(this, new GetLastErrorCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Logoff", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool Logoff() {
            object[] results = this.Invoke("Logoff", new object[0]);
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void LogoffAsync() {
            this.LogoffAsync(null);
        }
        
        /// <remarks/>
        public void LogoffAsync(object userState) {
            if ((this.LogoffOperationCompleted == null)) {
                this.LogoffOperationCompleted = new System.Threading.SendOrPostCallback(this.OnLogoffOperationCompleted);
            }
            this.InvokeAsync("Logoff", new object[0], this.LogoffOperationCompleted, userState);
        }
        
        private void OnLogoffOperationCompleted(object arg) {
            if ((this.LogoffCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.LogoffCompleted(this, new LogoffCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMetaPage", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetMetaPage(string par_sSection, string par_sPage) {
            object[] results = this.Invoke("GetMetaPage", new object[] {
                        par_sSection,
                        par_sPage});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetMetaPageAsync(string par_sSection, string par_sPage) {
            this.GetMetaPageAsync(par_sSection, par_sPage, null);
        }
        
        /// <remarks/>
        public void GetMetaPageAsync(string par_sSection, string par_sPage, object userState) {
            if ((this.GetMetaPageOperationCompleted == null)) {
                this.GetMetaPageOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetMetaPageOperationCompleted);
            }
            this.InvokeAsync("GetMetaPage", new object[] {
                        par_sSection,
                        par_sPage}, this.GetMetaPageOperationCompleted, userState);
        }
        
        private void OnGetMetaPageOperationCompleted(object arg) {
            if ((this.GetMetaPageCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetMetaPageCompleted(this, new GetMetaPageCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/ResetConduit", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool ResetConduit(string par_sConduit, string par_sValue) {
            object[] results = this.Invoke("ResetConduit", new object[] {
                        par_sConduit,
                        par_sValue});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void ResetConduitAsync(string par_sConduit, string par_sValue) {
            this.ResetConduitAsync(par_sConduit, par_sValue, null);
        }
        
        /// <remarks/>
        public void ResetConduitAsync(string par_sConduit, string par_sValue, object userState) {
            if ((this.ResetConduitOperationCompleted == null)) {
                this.ResetConduitOperationCompleted = new System.Threading.SendOrPostCallback(this.OnResetConduitOperationCompleted);
            }
            this.InvokeAsync("ResetConduit", new object[] {
                        par_sConduit,
                        par_sValue}, this.ResetConduitOperationCompleted, userState);
        }
        
        private void OnResetConduitOperationCompleted(object arg) {
            if ((this.ResetConduitCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ResetConduitCompleted(this, new ResetConduitCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMe", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetMe(string par_sVal) {
            object[] results = this.Invoke("GetMe", new object[] {
                        par_sVal});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetMeAsync(string par_sVal) {
            this.GetMeAsync(par_sVal, null);
        }
        
        /// <remarks/>
        public void GetMeAsync(string par_sVal, object userState) {
            if ((this.GetMeOperationCompleted == null)) {
                this.GetMeOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetMeOperationCompleted);
            }
            this.InvokeAsync("GetMe", new object[] {
                        par_sVal}, this.GetMeOperationCompleted, userState);
        }
        
        private void OnGetMeOperationCompleted(object arg) {
            if ((this.GetMeCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetMeCompleted(this, new GetMeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetLoginByUserID", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetLoginByUserID(string par_sUserID, string par_sType) {
            object[] results = this.Invoke("GetLoginByUserID", new object[] {
                        par_sUserID,
                        par_sType});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetLoginByUserIDAsync(string par_sUserID, string par_sType) {
            this.GetLoginByUserIDAsync(par_sUserID, par_sType, null);
        }
        
        /// <remarks/>
        public void GetLoginByUserIDAsync(string par_sUserID, string par_sType, object userState) {
            if ((this.GetLoginByUserIDOperationCompleted == null)) {
                this.GetLoginByUserIDOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetLoginByUserIDOperationCompleted);
            }
            this.InvokeAsync("GetLoginByUserID", new object[] {
                        par_sUserID,
                        par_sType}, this.GetLoginByUserIDOperationCompleted, userState);
        }
        
        private void OnGetLoginByUserIDOperationCompleted(object arg) {
            if ((this.GetLoginByUserIDCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetLoginByUserIDCompleted(this, new GetLoginByUserIDCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetQuoteSubject", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetQuoteSubject() {
            object[] results = this.Invoke("GetQuoteSubject", new object[0]);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetQuoteSubjectAsync() {
            this.GetQuoteSubjectAsync(null);
        }
        
        /// <remarks/>
        public void GetQuoteSubjectAsync(object userState) {
            if ((this.GetQuoteSubjectOperationCompleted == null)) {
                this.GetQuoteSubjectOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetQuoteSubjectOperationCompleted);
            }
            this.InvokeAsync("GetQuoteSubject", new object[0], this.GetQuoteSubjectOperationCompleted, userState);
        }
        
        private void OnGetQuoteSubjectOperationCompleted(object arg) {
            if ((this.GetQuoteSubjectCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetQuoteSubjectCompleted(this, new GetQuoteSubjectCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetFieldLabelFromName", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetFieldLabelFromName(string par_sFileName, string par_sFieldName) {
            object[] results = this.Invoke("GetFieldLabelFromName", new object[] {
                        par_sFileName,
                        par_sFieldName});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetFieldLabelFromNameAsync(string par_sFileName, string par_sFieldName) {
            this.GetFieldLabelFromNameAsync(par_sFileName, par_sFieldName, null);
        }
        
        /// <remarks/>
        public void GetFieldLabelFromNameAsync(string par_sFileName, string par_sFieldName, object userState) {
            if ((this.GetFieldLabelFromNameOperationCompleted == null)) {
                this.GetFieldLabelFromNameOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetFieldLabelFromNameOperationCompleted);
            }
            this.InvokeAsync("GetFieldLabelFromName", new object[] {
                        par_sFileName,
                        par_sFieldName}, this.GetFieldLabelFromNameOperationCompleted, userState);
        }
        
        private void OnGetFieldLabelFromNameOperationCompleted(object arg) {
            if ((this.GetFieldLabelFromNameCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetFieldLabelFromNameCompleted(this, new GetFieldLabelFromNameCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetFileLabel", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetFileLabel(string par_sFile) {
            object[] results = this.Invoke("GetFileLabel", new object[] {
                        par_sFile});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetFileLabelAsync(string par_sFile) {
            this.GetFileLabelAsync(par_sFile, null);
        }
        
        /// <remarks/>
        public void GetFileLabelAsync(string par_sFile, object userState) {
            if ((this.GetFileLabelOperationCompleted == null)) {
                this.GetFileLabelOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetFileLabelOperationCompleted);
            }
            this.InvokeAsync("GetFileLabel", new object[] {
                        par_sFile}, this.GetFileLabelOperationCompleted, userState);
        }
        
        private void OnGetFileLabelOperationCompleted(object arg) {
            if ((this.GetFileLabelCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetFileLabelCompleted(this, new GetFileLabelCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TestIDsForDeletions", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string TestIDsForDeletions(string sIDS) {
            object[] results = this.Invoke("TestIDsForDeletions", new object[] {
                        sIDS});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void TestIDsForDeletionsAsync(string sIDS) {
            this.TestIDsForDeletionsAsync(sIDS, null);
        }
        
        /// <remarks/>
        public void TestIDsForDeletionsAsync(string sIDS, object userState) {
            if ((this.TestIDsForDeletionsOperationCompleted == null)) {
                this.TestIDsForDeletionsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnTestIDsForDeletionsOperationCompleted);
            }
            this.InvokeAsync("TestIDsForDeletions", new object[] {
                        sIDS}, this.TestIDsForDeletionsOperationCompleted, userState);
        }
        
        private void OnTestIDsForDeletionsOperationCompleted(object arg) {
            if ((this.TestIDsForDeletionsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.TestIDsForDeletionsCompleted(this, new TestIDsForDeletionsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetFieldList", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetFieldList(string par_sFile) {
            object[] results = this.Invoke("GetFieldList", new object[] {
                        par_sFile});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetFieldListAsync(string par_sFile) {
            this.GetFieldListAsync(par_sFile, null);
        }
        
        /// <remarks/>
        public void GetFieldListAsync(string par_sFile, object userState) {
            if ((this.GetFieldListOperationCompleted == null)) {
                this.GetFieldListOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetFieldListOperationCompleted);
            }
            this.InvokeAsync("GetFieldList", new object[] {
                        par_sFile}, this.GetFieldListOperationCompleted, userState);
        }
        
        private void OnGetFieldListOperationCompleted(object arg) {
            if ((this.GetFieldListCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetFieldListCompleted(this, new GetFieldListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetLinksList", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetLinksList(string par_sFile) {
            object[] results = this.Invoke("GetLinksList", new object[] {
                        par_sFile});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetLinksListAsync(string par_sFile) {
            this.GetLinksListAsync(par_sFile, null);
        }
        
        /// <remarks/>
        public void GetLinksListAsync(string par_sFile, object userState) {
            if ((this.GetLinksListOperationCompleted == null)) {
                this.GetLinksListOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetLinksListOperationCompleted);
            }
            this.InvokeAsync("GetLinksList", new object[] {
                        par_sFile}, this.GetLinksListOperationCompleted, userState);
        }
        
        private void OnGetLinksListOperationCompleted(object arg) {
            if ((this.GetLinksListCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetLinksListCompleted(this, new GetLinksListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetFileList", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetFileList() {
            object[] results = this.Invoke("GetFileList", new object[0]);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetFileListAsync() {
            this.GetFileListAsync(null);
        }
        
        /// <remarks/>
        public void GetFileListAsync(object userState) {
            if ((this.GetFileListOperationCompleted == null)) {
                this.GetFileListOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetFileListOperationCompleted);
            }
            this.InvokeAsync("GetFileList", new object[0], this.GetFileListOperationCompleted, userState);
        }
        
        private void OnGetFileListOperationCompleted(object arg) {
            if ((this.GetFileListCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetFileListCompleted(this, new GetFileListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetPermission", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetPermission(string par_sFile, string par_sType) {
            object[] results = this.Invoke("GetPermission", new object[] {
                        par_sFile,
                        par_sType});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetPermissionAsync(string par_sFile, string par_sType) {
            this.GetPermissionAsync(par_sFile, par_sType, null);
        }
        
        /// <remarks/>
        public void GetPermissionAsync(string par_sFile, string par_sType, object userState) {
            if ((this.GetPermissionOperationCompleted == null)) {
                this.GetPermissionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetPermissionOperationCompleted);
            }
            this.InvokeAsync("GetPermission", new object[] {
                        par_sFile,
                        par_sType}, this.GetPermissionOperationCompleted, userState);
        }
        
        private void OnGetPermissionOperationCompleted(object arg) {
            if ((this.GetPermissionCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetPermissionCompleted(this, new GetPermissionCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/ValidateSession", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool ValidateSession() {
            object[] results = this.Invoke("ValidateSession", new object[0]);
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void ValidateSessionAsync() {
            this.ValidateSessionAsync(null);
        }
        
        /// <remarks/>
        public void ValidateSessionAsync(object userState) {
            if ((this.ValidateSessionOperationCompleted == null)) {
                this.ValidateSessionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnValidateSessionOperationCompleted);
            }
            this.InvokeAsync("ValidateSession", new object[0], this.ValidateSessionOperationCompleted, userState);
        }
        
        private void OnValidateSessionOperationCompleted(object arg) {
            if ((this.ValidateSessionCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ValidateSessionCompleted(this, new ValidateSessionCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/AddRecordsByXML", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Xml.XmlNode AddRecordsByXML(System.Xml.XmlNode xmlNode, bool par_bDupCheck, string par_sDupReturnFields) {
            object[] results = this.Invoke("AddRecordsByXML", new object[] {
                        xmlNode,
                        par_bDupCheck,
                        par_sDupReturnFields});
            return ((System.Xml.XmlNode)(results[0]));
        }
        
        /// <remarks/>
        public void AddRecordsByXMLAsync(System.Xml.XmlNode xmlNode, bool par_bDupCheck, string par_sDupReturnFields) {
            this.AddRecordsByXMLAsync(xmlNode, par_bDupCheck, par_sDupReturnFields, null);
        }
        
        /// <remarks/>
        public void AddRecordsByXMLAsync(System.Xml.XmlNode xmlNode, bool par_bDupCheck, string par_sDupReturnFields, object userState) {
            if ((this.AddRecordsByXMLOperationCompleted == null)) {
                this.AddRecordsByXMLOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAddRecordsByXMLOperationCompleted);
            }
            this.InvokeAsync("AddRecordsByXML", new object[] {
                        xmlNode,
                        par_bDupCheck,
                        par_sDupReturnFields}, this.AddRecordsByXMLOperationCompleted, userState);
        }
        
        private void OnAddRecordsByXMLOperationCompleted(object arg) {
            if ((this.AddRecordsByXMLCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.AddRecordsByXMLCompleted(this, new AddRecordsByXMLCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/AddRecordsByXMLString", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string AddRecordsByXMLString(string xmlString, bool par_bDupCheck, string par_sDupReturnFields) {
            object[] results = this.Invoke("AddRecordsByXMLString", new object[] {
                        xmlString,
                        par_bDupCheck,
                        par_sDupReturnFields});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void AddRecordsByXMLStringAsync(string xmlString, bool par_bDupCheck, string par_sDupReturnFields) {
            this.AddRecordsByXMLStringAsync(xmlString, par_bDupCheck, par_sDupReturnFields, null);
        }
        
        /// <remarks/>
        public void AddRecordsByXMLStringAsync(string xmlString, bool par_bDupCheck, string par_sDupReturnFields, object userState) {
            if ((this.AddRecordsByXMLStringOperationCompleted == null)) {
                this.AddRecordsByXMLStringOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAddRecordsByXMLStringOperationCompleted);
            }
            this.InvokeAsync("AddRecordsByXMLString", new object[] {
                        xmlString,
                        par_bDupCheck,
                        par_sDupReturnFields}, this.AddRecordsByXMLStringOperationCompleted, userState);
        }
        
        private void OnAddRecordsByXMLStringOperationCompleted(object arg) {
            if ((this.AddRecordsByXMLStringCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.AddRecordsByXMLStringCompleted(this, new AddRecordsByXMLStringCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/AddRecordsByDataset", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet AddRecordsByDataset(System.Data.DataSet par_oDataSet, bool par_bDupCheck, string par_sDupReturnFields) {
            object[] results = this.Invoke("AddRecordsByDataset", new object[] {
                        par_oDataSet,
                        par_bDupCheck,
                        par_sDupReturnFields});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void AddRecordsByDatasetAsync(System.Data.DataSet par_oDataSet, bool par_bDupCheck, string par_sDupReturnFields) {
            this.AddRecordsByDatasetAsync(par_oDataSet, par_bDupCheck, par_sDupReturnFields, null);
        }
        
        /// <remarks/>
        public void AddRecordsByDatasetAsync(System.Data.DataSet par_oDataSet, bool par_bDupCheck, string par_sDupReturnFields, object userState) {
            if ((this.AddRecordsByDatasetOperationCompleted == null)) {
                this.AddRecordsByDatasetOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAddRecordsByDatasetOperationCompleted);
            }
            this.InvokeAsync("AddRecordsByDataset", new object[] {
                        par_oDataSet,
                        par_bDupCheck,
                        par_sDupReturnFields}, this.AddRecordsByDatasetOperationCompleted, userState);
        }
        
        private void OnAddRecordsByDatasetOperationCompleted(object arg) {
            if ((this.AddRecordsByDatasetCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.AddRecordsByDatasetCompleted(this, new AddRecordsByDatasetCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EditRecordsByXML", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Xml.XmlNode EditRecordsByXML(System.Xml.XmlNode xmlNode) {
            object[] results = this.Invoke("EditRecordsByXML", new object[] {
                        xmlNode});
            return ((System.Xml.XmlNode)(results[0]));
        }
        
        /// <remarks/>
        public void EditRecordsByXMLAsync(System.Xml.XmlNode xmlNode) {
            this.EditRecordsByXMLAsync(xmlNode, null);
        }
        
        /// <remarks/>
        public void EditRecordsByXMLAsync(System.Xml.XmlNode xmlNode, object userState) {
            if ((this.EditRecordsByXMLOperationCompleted == null)) {
                this.EditRecordsByXMLOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEditRecordsByXMLOperationCompleted);
            }
            this.InvokeAsync("EditRecordsByXML", new object[] {
                        xmlNode}, this.EditRecordsByXMLOperationCompleted, userState);
        }
        
        private void OnEditRecordsByXMLOperationCompleted(object arg) {
            if ((this.EditRecordsByXMLCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EditRecordsByXMLCompleted(this, new EditRecordsByXMLCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EditRecordsByXMLString", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string EditRecordsByXMLString(string xmlString) {
            object[] results = this.Invoke("EditRecordsByXMLString", new object[] {
                        xmlString});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void EditRecordsByXMLStringAsync(string xmlString) {
            this.EditRecordsByXMLStringAsync(xmlString, null);
        }
        
        /// <remarks/>
        public void EditRecordsByXMLStringAsync(string xmlString, object userState) {
            if ((this.EditRecordsByXMLStringOperationCompleted == null)) {
                this.EditRecordsByXMLStringOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEditRecordsByXMLStringOperationCompleted);
            }
            this.InvokeAsync("EditRecordsByXMLString", new object[] {
                        xmlString}, this.EditRecordsByXMLStringOperationCompleted, userState);
        }
        
        private void OnEditRecordsByXMLStringOperationCompleted(object arg) {
            if ((this.EditRecordsByXMLStringCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EditRecordsByXMLStringCompleted(this, new EditRecordsByXMLStringCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EditRecordsByDataset", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet EditRecordsByDataset(System.Data.DataSet par_oDataSet) {
            object[] results = this.Invoke("EditRecordsByDataset", new object[] {
                        par_oDataSet});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void EditRecordsByDatasetAsync(System.Data.DataSet par_oDataSet) {
            this.EditRecordsByDatasetAsync(par_oDataSet, null);
        }
        
        /// <remarks/>
        public void EditRecordsByDatasetAsync(System.Data.DataSet par_oDataSet, object userState) {
            if ((this.EditRecordsByDatasetOperationCompleted == null)) {
                this.EditRecordsByDatasetOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEditRecordsByDatasetOperationCompleted);
            }
            this.InvokeAsync("EditRecordsByDataset", new object[] {
                        par_oDataSet}, this.EditRecordsByDatasetOperationCompleted, userState);
        }
        
        private void OnEditRecordsByDatasetOperationCompleted(object arg) {
            if ((this.EditRecordsByDatasetCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EditRecordsByDatasetCompleted(this, new EditRecordsByDatasetCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpsertRecordsByXML", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Xml.XmlNode UpsertRecordsByXML(System.Xml.XmlNode xmlNode, string strCompareField, string strExtSource) {
            object[] results = this.Invoke("UpsertRecordsByXML", new object[] {
                        xmlNode,
                        strCompareField,
                        strExtSource});
            return ((System.Xml.XmlNode)(results[0]));
        }
        
        /// <remarks/>
        public void UpsertRecordsByXMLAsync(System.Xml.XmlNode xmlNode, string strCompareField, string strExtSource) {
            this.UpsertRecordsByXMLAsync(xmlNode, strCompareField, strExtSource, null);
        }
        
        /// <remarks/>
        public void UpsertRecordsByXMLAsync(System.Xml.XmlNode xmlNode, string strCompareField, string strExtSource, object userState) {
            if ((this.UpsertRecordsByXMLOperationCompleted == null)) {
                this.UpsertRecordsByXMLOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUpsertRecordsByXMLOperationCompleted);
            }
            this.InvokeAsync("UpsertRecordsByXML", new object[] {
                        xmlNode,
                        strCompareField,
                        strExtSource}, this.UpsertRecordsByXMLOperationCompleted, userState);
        }
        
        private void OnUpsertRecordsByXMLOperationCompleted(object arg) {
            if ((this.UpsertRecordsByXMLCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UpsertRecordsByXMLCompleted(this, new UpsertRecordsByXMLCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpsertRecordsByXMLString", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string UpsertRecordsByXMLString(string xmlString, string strCompareField, string strExtSource) {
            object[] results = this.Invoke("UpsertRecordsByXMLString", new object[] {
                        xmlString,
                        strCompareField,
                        strExtSource});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void UpsertRecordsByXMLStringAsync(string xmlString, string strCompareField, string strExtSource) {
            this.UpsertRecordsByXMLStringAsync(xmlString, strCompareField, strExtSource, null);
        }
        
        /// <remarks/>
        public void UpsertRecordsByXMLStringAsync(string xmlString, string strCompareField, string strExtSource, object userState) {
            if ((this.UpsertRecordsByXMLStringOperationCompleted == null)) {
                this.UpsertRecordsByXMLStringOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUpsertRecordsByXMLStringOperationCompleted);
            }
            this.InvokeAsync("UpsertRecordsByXMLString", new object[] {
                        xmlString,
                        strCompareField,
                        strExtSource}, this.UpsertRecordsByXMLStringOperationCompleted, userState);
        }
        
        private void OnUpsertRecordsByXMLStringOperationCompleted(object arg) {
            if ((this.UpsertRecordsByXMLStringCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UpsertRecordsByXMLStringCompleted(this, new UpsertRecordsByXMLStringCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpsertRecordsByDataset", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet UpsertRecordsByDataset(System.Data.DataSet par_oDataSet, string strCompareField, string strExtSource) {
            object[] results = this.Invoke("UpsertRecordsByDataset", new object[] {
                        par_oDataSet,
                        strCompareField,
                        strExtSource});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void UpsertRecordsByDatasetAsync(System.Data.DataSet par_oDataSet, string strCompareField, string strExtSource) {
            this.UpsertRecordsByDatasetAsync(par_oDataSet, strCompareField, strExtSource, null);
        }
        
        /// <remarks/>
        public void UpsertRecordsByDatasetAsync(System.Data.DataSet par_oDataSet, string strCompareField, string strExtSource, object userState) {
            if ((this.UpsertRecordsByDatasetOperationCompleted == null)) {
                this.UpsertRecordsByDatasetOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUpsertRecordsByDatasetOperationCompleted);
            }
            this.InvokeAsync("UpsertRecordsByDataset", new object[] {
                        par_oDataSet,
                        strCompareField,
                        strExtSource}, this.UpsertRecordsByDatasetOperationCompleted, userState);
        }
        
        private void OnUpsertRecordsByDatasetOperationCompleted(object arg) {
            if ((this.UpsertRecordsByDatasetCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UpsertRecordsByDatasetCompleted(this, new UpsertRecordsByDatasetCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetList", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetList(string sFile, string sField) {
            object[] results = this.Invoke("GetList", new object[] {
                        sFile,
                        sField});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetListAsync(string sFile, string sField) {
            this.GetListAsync(sFile, sField, null);
        }
        
        /// <remarks/>
        public void GetListAsync(string sFile, string sField, object userState) {
            if ((this.GetListOperationCompleted == null)) {
                this.GetListOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetListOperationCompleted);
            }
            this.InvokeAsync("GetList", new object[] {
                        sFile,
                        sField}, this.GetListOperationCompleted, userState);
        }
        
        private void OnGetListOperationCompleted(object arg) {
            if ((this.GetListCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetListCompleted(this, new GetListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/DatasetToTextFile", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool DatasetToTextFile(System.Data.DataSet par_oDataset, int par_iMaxCharsPerCell) {
            object[] results = this.Invoke("DatasetToTextFile", new object[] {
                        par_oDataset,
                        par_iMaxCharsPerCell});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void DatasetToTextFileAsync(System.Data.DataSet par_oDataset, int par_iMaxCharsPerCell) {
            this.DatasetToTextFileAsync(par_oDataset, par_iMaxCharsPerCell, null);
        }
        
        /// <remarks/>
        public void DatasetToTextFileAsync(System.Data.DataSet par_oDataset, int par_iMaxCharsPerCell, object userState) {
            if ((this.DatasetToTextFileOperationCompleted == null)) {
                this.DatasetToTextFileOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDatasetToTextFileOperationCompleted);
            }
            this.InvokeAsync("DatasetToTextFile", new object[] {
                        par_oDataset,
                        par_iMaxCharsPerCell}, this.DatasetToTextFileOperationCompleted, userState);
        }
        
        private void OnDatasetToTextFileOperationCompleted(object arg) {
            if ((this.DatasetToTextFileCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DatasetToTextFileCompleted(this, new DatasetToTextFileCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetSchemaAsXML", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Xml.XmlNode GetSchemaAsXML() {
            object[] results = this.Invoke("GetSchemaAsXML", new object[0]);
            return ((System.Xml.XmlNode)(results[0]));
        }
        
        /// <remarks/>
        public void GetSchemaAsXMLAsync() {
            this.GetSchemaAsXMLAsync(null);
        }
        
        /// <remarks/>
        public void GetSchemaAsXMLAsync(object userState) {
            if ((this.GetSchemaAsXMLOperationCompleted == null)) {
                this.GetSchemaAsXMLOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetSchemaAsXMLOperationCompleted);
            }
            this.InvokeAsync("GetSchemaAsXML", new object[0], this.GetSchemaAsXMLOperationCompleted, userState);
        }
        
        private void OnGetSchemaAsXMLOperationCompleted(object arg) {
            if ((this.GetSchemaAsXMLCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetSchemaAsXMLCompleted(this, new GetSchemaAsXMLCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetSchemaAsXMLString", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetSchemaAsXMLString() {
            object[] results = this.Invoke("GetSchemaAsXMLString", new object[0]);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetSchemaAsXMLStringAsync() {
            this.GetSchemaAsXMLStringAsync(null);
        }
        
        /// <remarks/>
        public void GetSchemaAsXMLStringAsync(object userState) {
            if ((this.GetSchemaAsXMLStringOperationCompleted == null)) {
                this.GetSchemaAsXMLStringOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetSchemaAsXMLStringOperationCompleted);
            }
            this.InvokeAsync("GetSchemaAsXMLString", new object[0], this.GetSchemaAsXMLStringOperationCompleted, userState);
        }
        
        private void OnGetSchemaAsXMLStringOperationCompleted(object arg) {
            if ((this.GetSchemaAsXMLStringCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetSchemaAsXMLStringCompleted(this, new GetSchemaAsXMLStringCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/DeleteRecordsByXMLString", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string DeleteRecordsByXMLString(string xmlString) {
            object[] results = this.Invoke("DeleteRecordsByXMLString", new object[] {
                        xmlString});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void DeleteRecordsByXMLStringAsync(string xmlString) {
            this.DeleteRecordsByXMLStringAsync(xmlString, null);
        }
        
        /// <remarks/>
        public void DeleteRecordsByXMLStringAsync(string xmlString, object userState) {
            if ((this.DeleteRecordsByXMLStringOperationCompleted == null)) {
                this.DeleteRecordsByXMLStringOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDeleteRecordsByXMLStringOperationCompleted);
            }
            this.InvokeAsync("DeleteRecordsByXMLString", new object[] {
                        xmlString}, this.DeleteRecordsByXMLStringOperationCompleted, userState);
        }
        
        private void OnDeleteRecordsByXMLStringOperationCompleted(object arg) {
            if ((this.DeleteRecordsByXMLStringCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DeleteRecordsByXMLStringCompleted(this, new DeleteRecordsByXMLStringCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/DeleteRecordsByXML", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Xml.XmlNode DeleteRecordsByXML(System.Xml.XmlNode xNode) {
            object[] results = this.Invoke("DeleteRecordsByXML", new object[] {
                        xNode});
            return ((System.Xml.XmlNode)(results[0]));
        }
        
        /// <remarks/>
        public void DeleteRecordsByXMLAsync(System.Xml.XmlNode xNode) {
            this.DeleteRecordsByXMLAsync(xNode, null);
        }
        
        /// <remarks/>
        public void DeleteRecordsByXMLAsync(System.Xml.XmlNode xNode, object userState) {
            if ((this.DeleteRecordsByXMLOperationCompleted == null)) {
                this.DeleteRecordsByXMLOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDeleteRecordsByXMLOperationCompleted);
            }
            this.InvokeAsync("DeleteRecordsByXML", new object[] {
                        xNode}, this.DeleteRecordsByXMLOperationCompleted, userState);
        }
        
        private void OnDeleteRecordsByXMLOperationCompleted(object arg) {
            if ((this.DeleteRecordsByXMLCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DeleteRecordsByXMLCompleted(this, new DeleteRecordsByXMLCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UploadAndProcessDataset", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool UploadAndProcessDataset(System.Data.DataSet par_oDataSet, string par_sScriptName, string par_sReportPage) {
            object[] results = this.Invoke("UploadAndProcessDataset", new object[] {
                        par_oDataSet,
                        par_sScriptName,
                        par_sReportPage});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void UploadAndProcessDatasetAsync(System.Data.DataSet par_oDataSet, string par_sScriptName, string par_sReportPage) {
            this.UploadAndProcessDatasetAsync(par_oDataSet, par_sScriptName, par_sReportPage, null);
        }
        
        /// <remarks/>
        public void UploadAndProcessDatasetAsync(System.Data.DataSet par_oDataSet, string par_sScriptName, string par_sReportPage, object userState) {
            if ((this.UploadAndProcessDatasetOperationCompleted == null)) {
                this.UploadAndProcessDatasetOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUploadAndProcessDatasetOperationCompleted);
            }
            this.InvokeAsync("UploadAndProcessDataset", new object[] {
                        par_oDataSet,
                        par_sScriptName,
                        par_sReportPage}, this.UploadAndProcessDatasetOperationCompleted, userState);
        }
        
        private void OnUploadAndProcessDatasetOperationCompleted(object arg) {
            if ((this.UploadAndProcessDatasetCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UploadAndProcessDatasetCompleted(this, new UploadAndProcessDatasetCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpdateStatus", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool UpdateStatus(string par_sReportPage, string sStatus) {
            object[] results = this.Invoke("UpdateStatus", new object[] {
                        par_sReportPage,
                        sStatus});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void UpdateStatusAsync(string par_sReportPage, string sStatus) {
            this.UpdateStatusAsync(par_sReportPage, sStatus, null);
        }
        
        /// <remarks/>
        public void UpdateStatusAsync(string par_sReportPage, string sStatus, object userState) {
            if ((this.UpdateStatusOperationCompleted == null)) {
                this.UpdateStatusOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUpdateStatusOperationCompleted);
            }
            this.InvokeAsync("UpdateStatus", new object[] {
                        par_sReportPage,
                        sStatus}, this.UpdateStatusOperationCompleted, userState);
        }
        
        private void OnUpdateStatusOperationCompleted(object arg) {
            if ((this.UpdateStatusCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UpdateStatusCompleted(this, new UpdateStatusCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpdateS3Status", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool UpdateS3Status(string par_sReportPage, string sStatus, string sErrorMessage, string sProgress, long lBytesTransferred, long lTotalBytes, string sType, string sFileLastMod) {
            object[] results = this.Invoke("UpdateS3Status", new object[] {
                        par_sReportPage,
                        sStatus,
                        sErrorMessage,
                        sProgress,
                        lBytesTransferred,
                        lTotalBytes,
                        sType,
                        sFileLastMod});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void UpdateS3StatusAsync(string par_sReportPage, string sStatus, string sErrorMessage, string sProgress, long lBytesTransferred, long lTotalBytes, string sType, string sFileLastMod) {
            this.UpdateS3StatusAsync(par_sReportPage, sStatus, sErrorMessage, sProgress, lBytesTransferred, lTotalBytes, sType, sFileLastMod, null);
        }
        
        /// <remarks/>
        public void UpdateS3StatusAsync(string par_sReportPage, string sStatus, string sErrorMessage, string sProgress, long lBytesTransferred, long lTotalBytes, string sType, string sFileLastMod, object userState) {
            if ((this.UpdateS3StatusOperationCompleted == null)) {
                this.UpdateS3StatusOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUpdateS3StatusOperationCompleted);
            }
            this.InvokeAsync("UpdateS3Status", new object[] {
                        par_sReportPage,
                        sStatus,
                        sErrorMessage,
                        sProgress,
                        lBytesTransferred,
                        lTotalBytes,
                        sType,
                        sFileLastMod}, this.UpdateS3StatusOperationCompleted, userState);
        }
        
        private void OnUpdateS3StatusOperationCompleted(object arg) {
            if ((this.UpdateS3StatusCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UpdateS3StatusCompleted(this, new UpdateS3StatusCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetSystemCodes", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetSystemCodes() {
            object[] results = this.Invoke("GetSystemCodes", new object[0]);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetSystemCodesAsync() {
            this.GetSystemCodesAsync(null);
        }
        
        /// <remarks/>
        public void GetSystemCodesAsync(object userState) {
            if ((this.GetSystemCodesOperationCompleted == null)) {
                this.GetSystemCodesOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetSystemCodesOperationCompleted);
            }
            this.InvokeAsync("GetSystemCodes", new object[0], this.GetSystemCodesOperationCompleted, userState);
        }
        
        private void OnGetSystemCodesOperationCompleted(object arg) {
            if ((this.GetSystemCodesCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetSystemCodesCompleted(this, new GetSystemCodesCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetStandardizedPhoneNumber", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetStandardizedPhoneNumber(string sNumber) {
            object[] results = this.Invoke("GetStandardizedPhoneNumber", new object[] {
                        sNumber});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetStandardizedPhoneNumberAsync(string sNumber) {
            this.GetStandardizedPhoneNumberAsync(sNumber, null);
        }
        
        /// <remarks/>
        public void GetStandardizedPhoneNumberAsync(string sNumber, object userState) {
            if ((this.GetStandardizedPhoneNumberOperationCompleted == null)) {
                this.GetStandardizedPhoneNumberOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetStandardizedPhoneNumberOperationCompleted);
            }
            this.InvokeAsync("GetStandardizedPhoneNumber", new object[] {
                        sNumber}, this.GetStandardizedPhoneNumberOperationCompleted, userState);
        }
        
        private void OnGetStandardizedPhoneNumberOperationCompleted(object arg) {
            if ((this.GetStandardizedPhoneNumberCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetStandardizedPhoneNumberCompleted(this, new GetStandardizedPhoneNumberCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/LogError", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool LogError(string par_sMessage, string par_sProcedure) {
            object[] results = this.Invoke("LogError", new object[] {
                        par_sMessage,
                        par_sProcedure});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void LogErrorAsync(string par_sMessage, string par_sProcedure) {
            this.LogErrorAsync(par_sMessage, par_sProcedure, null);
        }
        
        /// <remarks/>
        public void LogErrorAsync(string par_sMessage, string par_sProcedure, object userState) {
            if ((this.LogErrorOperationCompleted == null)) {
                this.LogErrorOperationCompleted = new System.Threading.SendOrPostCallback(this.OnLogErrorOperationCompleted);
            }
            this.InvokeAsync("LogError", new object[] {
                        par_sMessage,
                        par_sProcedure}, this.LogErrorOperationCompleted, userState);
        }
        
        private void OnLogErrorOperationCompleted(object arg) {
            if ((this.LogErrorCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.LogErrorCompleted(this, new LogErrorCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/AddAlert", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool AddAlert(string par_sMessage, string par_sType, string par_sExecute, string par_sUser, string par_sIcon, string par_sProduct, string par_sTooltip) {
            object[] results = this.Invoke("AddAlert", new object[] {
                        par_sMessage,
                        par_sType,
                        par_sExecute,
                        par_sUser,
                        par_sIcon,
                        par_sProduct,
                        par_sTooltip});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void AddAlertAsync(string par_sMessage, string par_sType, string par_sExecute, string par_sUser, string par_sIcon, string par_sProduct, string par_sTooltip) {
            this.AddAlertAsync(par_sMessage, par_sType, par_sExecute, par_sUser, par_sIcon, par_sProduct, par_sTooltip, null);
        }
        
        /// <remarks/>
        public void AddAlertAsync(string par_sMessage, string par_sType, string par_sExecute, string par_sUser, string par_sIcon, string par_sProduct, string par_sTooltip, object userState) {
            if ((this.AddAlertOperationCompleted == null)) {
                this.AddAlertOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAddAlertOperationCompleted);
            }
            this.InvokeAsync("AddAlert", new object[] {
                        par_sMessage,
                        par_sType,
                        par_sExecute,
                        par_sUser,
                        par_sIcon,
                        par_sProduct,
                        par_sTooltip}, this.AddAlertOperationCompleted, userState);
        }
        
        private void OnAddAlertOperationCompleted(object arg) {
            if ((this.AddAlertCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.AddAlertCompleted(this, new AddAlertCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void LogonCompletedEventHandler(object sender, LogonCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class LogonCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal LogonCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void SetVarCompletedEventHandler(object sender, SetVarCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SetVarCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SetVarCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void NewRSCompletedEventHandler(object sender, NewRSCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class NewRSCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal NewRSCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void BypassCompletedEventHandler(object sender, BypassCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class BypassCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal BypassCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void SetFieldValCompletedEventHandler(object sender, SetFieldValCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SetFieldValCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SetFieldValCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void SetFieldValsCompletedEventHandler(object sender, SetFieldValsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SetFieldValsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SetFieldValsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetFieldValCompletedEventHandler(object sender, GetFieldValCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetFieldValCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetFieldValCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public object Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((object)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void LogCompletedEventHandler(object sender, LogCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class LogCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal LogCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetLinkValCompletedEventHandler(object sender, GetLinkValCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetLinkValCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetLinkValCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetFieldValsCompletedEventHandler(object sender, GetFieldValsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetFieldValsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetFieldValsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void ProcessingInstructionsCompletedEventHandler(object sender, ProcessingInstructionsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ProcessingInstructionsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ProcessingInstructionsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void ClearLinkCompletedEventHandler(object sender, ClearLinkCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ClearLinkCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ClearLinkCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void ClearLinkAllCompletedEventHandler(object sender, ClearLinkAllCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ClearLinkAllCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ClearLinkAllCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void CommitCompletedEventHandler(object sender, CommitCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CommitCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CommitCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void CountCompletedEventHandler(object sender, CountCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CountCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CountCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public long Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((long)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void DeleteRecordCompletedEventHandler(object sender, DeleteRecordCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DeleteRecordCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DeleteRecordCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void DeleteAllCompletedEventHandler(object sender, DeleteAllCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DeleteAllCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DeleteAllCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void ToTableCompletedEventHandler(object sender, ToTableCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ToTableCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ToTableCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetTransTableCompletedEventHandler(object sender, GetTransTableCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetTransTableCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetTransTableCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetTransTableAsXMLCompletedEventHandler(object sender, GetTransTableAsXMLCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetTransTableAsXMLCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetTransTableAsXMLCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Xml.XmlNode Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Xml.XmlNode)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetTransTableAsXMLWithCountCompletedEventHandler(object sender, GetTransTableAsXMLWithCountCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetTransTableAsXMLWithCountCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetTransTableAsXMLWithCountCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Xml.XmlNode Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Xml.XmlNode)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetTransTableAsXMLStringCompletedEventHandler(object sender, GetTransTableAsXMLStringCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetTransTableAsXMLStringCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetTransTableAsXMLStringCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetLastErrorCompletedEventHandler(object sender, GetLastErrorCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetLastErrorCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetLastErrorCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void LogoffCompletedEventHandler(object sender, LogoffCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class LogoffCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal LogoffCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetMetaPageCompletedEventHandler(object sender, GetMetaPageCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetMetaPageCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetMetaPageCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void ResetConduitCompletedEventHandler(object sender, ResetConduitCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ResetConduitCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ResetConduitCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetMeCompletedEventHandler(object sender, GetMeCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetMeCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetMeCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetLoginByUserIDCompletedEventHandler(object sender, GetLoginByUserIDCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetLoginByUserIDCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetLoginByUserIDCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetQuoteSubjectCompletedEventHandler(object sender, GetQuoteSubjectCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetQuoteSubjectCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetQuoteSubjectCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetFieldLabelFromNameCompletedEventHandler(object sender, GetFieldLabelFromNameCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetFieldLabelFromNameCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetFieldLabelFromNameCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetFileLabelCompletedEventHandler(object sender, GetFileLabelCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetFileLabelCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetFileLabelCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void TestIDsForDeletionsCompletedEventHandler(object sender, TestIDsForDeletionsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TestIDsForDeletionsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal TestIDsForDeletionsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetFieldListCompletedEventHandler(object sender, GetFieldListCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetFieldListCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetFieldListCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetLinksListCompletedEventHandler(object sender, GetLinksListCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetLinksListCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetLinksListCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetFileListCompletedEventHandler(object sender, GetFileListCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetFileListCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetFileListCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetPermissionCompletedEventHandler(object sender, GetPermissionCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetPermissionCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetPermissionCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void ValidateSessionCompletedEventHandler(object sender, ValidateSessionCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ValidateSessionCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ValidateSessionCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void AddRecordsByXMLCompletedEventHandler(object sender, AddRecordsByXMLCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AddRecordsByXMLCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal AddRecordsByXMLCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Xml.XmlNode Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Xml.XmlNode)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void AddRecordsByXMLStringCompletedEventHandler(object sender, AddRecordsByXMLStringCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AddRecordsByXMLStringCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal AddRecordsByXMLStringCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void AddRecordsByDatasetCompletedEventHandler(object sender, AddRecordsByDatasetCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AddRecordsByDatasetCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal AddRecordsByDatasetCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void EditRecordsByXMLCompletedEventHandler(object sender, EditRecordsByXMLCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EditRecordsByXMLCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EditRecordsByXMLCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Xml.XmlNode Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Xml.XmlNode)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void EditRecordsByXMLStringCompletedEventHandler(object sender, EditRecordsByXMLStringCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EditRecordsByXMLStringCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EditRecordsByXMLStringCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void EditRecordsByDatasetCompletedEventHandler(object sender, EditRecordsByDatasetCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EditRecordsByDatasetCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EditRecordsByDatasetCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void UpsertRecordsByXMLCompletedEventHandler(object sender, UpsertRecordsByXMLCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UpsertRecordsByXMLCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UpsertRecordsByXMLCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Xml.XmlNode Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Xml.XmlNode)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void UpsertRecordsByXMLStringCompletedEventHandler(object sender, UpsertRecordsByXMLStringCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UpsertRecordsByXMLStringCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UpsertRecordsByXMLStringCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void UpsertRecordsByDatasetCompletedEventHandler(object sender, UpsertRecordsByDatasetCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UpsertRecordsByDatasetCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UpsertRecordsByDatasetCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetListCompletedEventHandler(object sender, GetListCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetListCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetListCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void DatasetToTextFileCompletedEventHandler(object sender, DatasetToTextFileCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DatasetToTextFileCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DatasetToTextFileCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetSchemaAsXMLCompletedEventHandler(object sender, GetSchemaAsXMLCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetSchemaAsXMLCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetSchemaAsXMLCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Xml.XmlNode Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Xml.XmlNode)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetSchemaAsXMLStringCompletedEventHandler(object sender, GetSchemaAsXMLStringCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetSchemaAsXMLStringCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetSchemaAsXMLStringCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void DeleteRecordsByXMLStringCompletedEventHandler(object sender, DeleteRecordsByXMLStringCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DeleteRecordsByXMLStringCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DeleteRecordsByXMLStringCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void DeleteRecordsByXMLCompletedEventHandler(object sender, DeleteRecordsByXMLCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DeleteRecordsByXMLCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DeleteRecordsByXMLCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Xml.XmlNode Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Xml.XmlNode)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void UploadAndProcessDatasetCompletedEventHandler(object sender, UploadAndProcessDatasetCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UploadAndProcessDatasetCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UploadAndProcessDatasetCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void UpdateStatusCompletedEventHandler(object sender, UpdateStatusCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UpdateStatusCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UpdateStatusCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void UpdateS3StatusCompletedEventHandler(object sender, UpdateS3StatusCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UpdateS3StatusCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UpdateS3StatusCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetSystemCodesCompletedEventHandler(object sender, GetSystemCodesCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetSystemCodesCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetSystemCodesCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetStandardizedPhoneNumberCompletedEventHandler(object sender, GetStandardizedPhoneNumberCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetStandardizedPhoneNumberCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetStandardizedPhoneNumberCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void LogErrorCompletedEventHandler(object sender, LogErrorCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class LogErrorCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal LogErrorCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void AddAlertCompletedEventHandler(object sender, AddAlertCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AddAlertCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal AddAlertCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591
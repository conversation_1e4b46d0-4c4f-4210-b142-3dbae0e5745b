﻿using Microsoft.VisualBasic;
using Selltis.BusinessLogic;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Collections;
using System.Windows.Forms.DataVisualization.Charting;
using System.Xml.Linq;

namespace Selltis.Core
{
    public class Chart : View
    {
        private clMetaData goMeta;
        private clTransform goTR;
        private clData godata;
        private clDefaults goDef;

        private bool isLoadFromSession = true;
        public string _viewMetaData = string.Empty;

        public string UpdatedViewCondition = string.Empty;

        public string Chartname { get; set; }
        //public ChartTitlePosition Position { get; set; }
        public string CTitle { get; set; }
        public List<clSeries> Series { get; set; }
        public string Format { get; set; }
        public bool VisibleLegend { get; set; }
        public ArrayList Xaxis { get; set; }
        public ArrayList XaxisValuesUsedInToolTip { get; set; }
        public ArrayList SeriesNames { get; set; }
        public string YaxisLabel { get; set; }
        public string XaxisLabel { get; set; }
        public ArrayList SeriesColors { get; set; }
        public bool ShowGoleLine { get; set; }
        public double GoleLineValue { get; set; }
        public double MaxValue { get; set; }
        public string ViewID { get; set; }
        public List<clPieSeries> PieSeries { get; set; }

        public int ViewHeight { get; set; }

        public int ChartHeight { get; set; }
        public int ChartWidth { get; set; }
        public bool oChartVisible { get; set; }

        //To handle chart properties..
        public bool ShowDataPoints { get; set; }
        public bool AllowChangeType { get; set; }
        public bool AllowChangeYAxis { get; set; }
        public bool AllowChangeXAxis { get; set; }
        public bool lblSomeBlankDataVisible { get; set; }

        public string GraphType { get; set; }
        public string YField { get; set; }
        public IList<GraphYFieldsItems> GraphYField { get; set; }
        public IList<GraphYFieldsItems> GraphXField { get; set; }
        public string GraphYFieldSelectedValue { get; set; }
        public string GraphXFieldSelectedValue { get; set; }

        public string lblNoChartText { get; set; }

        public string XaxisLabelStyleFormat { get; set; }
        public string YaxisLabelStyleFormat { get; set; }
        public int XaxisInterval { get; set; }
        public string XaxisIntervalType { get; set; }
        public int YaxisInterval { get; set; }
        public string YaxisIntervalType { get; set; }

        public bool IsLegendEnabled { get; set; }
        public bool IsTooltipVisible { get; set; }


        public bool ChartReportTooLong { get; set; }
        public string LblIssueText { get; set; }

        public int SeriesClickToSortField { get; set; }
        public string chartType { get; set; }
        public string ChartTitleInHeaderPanel { get; set; }
        public int ChartDataCount { get; set; }
        public bool ChartAllCurr { get; set; }


        public string ChartTitleInToolTip { get; set; }
        //public int ChartHeight { get; set; }
        //public int ChartWidth { get; set; }

        //gauge chart
        public string GaugeXaxisTitle { get; set; }
        public double GaugePointerValue { get; set; }
        public double GaugeRange { get; set; }
        public double GaugeMajorUnit { get; set; }
        public bool GaugeAllCurr { get; set; }

        public int GuageTickLength { get; set; }
        public int GuageArchWidth { get; set; }
        public double GaugeSection1 { get; set; }
        public double GaugeSection2 { get; set; }
        public double GaugeSection3 { get; set; }

        public bool IsTabView { get; set; }

        public string lblGoalFilterInfoToolTip { get; set; }
        public string lblGoalFilterInfoText { get; set; }

        public int bGroupedBars { get; set; }

        public Chart(string _viewId, bool _isTabView, string _section, string _desktopmetadata, int _index, bool _isLoadFromSession = true, int _TotalIndex = 0, string MasterViewKey = "", string _key = "")
            : base(_viewId, _isTabView, _section, _desktopmetadata, _index, _isLoadFromSession, _TotalIndex, _key)
        {
            godata = (clData)Util.GetInstance("data");
            goTR = (clTransform)Util.GetInstance("tr");
            goMeta = (clMetaData)Util.GetInstance("meta");

            isLoadFromSession = _isLoadFromSession;
            _viewMetaData = ViewMetaData;

            goTR.StrWrite(ref _viewMetaData, "GRAPHSORTFIELD", "1");
            goTR.StrWrite(ref _viewMetaData, "GRAPHPARENT1INDEX", "0");
            goTR.StrWrite(ref _viewMetaData, "GRAPHPARENT2INDEX", "0");
            goTR.StrWrite(ref _viewMetaData, "GRAPHPARENT3INDEX", "0");
            goTR.StrWrite(ref _viewMetaData, "GRAPHHISTORYLABEL", "All");
            //goTR.StrWrite(_viewMetaData, "GRAPHSORTFIELD", "1");

            if (Util.GetSessionValue(Key + "_" + ViewKey) != null && isLoadFromSession == true)
            {
                SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + ViewKey);
                _viewMetaData = _SessionViewInfo.ViewMetaData;
            }

            ViewMetaData = _viewMetaData;

            Fields = "";
            Title = goTR.StrRead(_viewMetaData, "NAME", "");
            AutoCount = goTR.StrRead(_viewMetaData, "AUTOCOUNT", false);
            SortFields = godata.GetFilterSortFields("SORT=" + goTR.StrRead(_viewMetaData, "SORT", ""), false);
            NewSort = goTR.StrRead(_viewMetaData, "CHANGESORT", "");
            Filter = goTR.StrRead(_viewMetaData, "FILTER", "");

            chartType = goTR.StrRead(_viewMetaData, "GRAPHTYPE", "COLUMN", false);

            if (NewSort != "")
            {
                switch (ViewType)
                {
                    case "CHART":
                        // OrderBy = SetFirstSortField(goTR.ExtractString(NewSort, 1, "|"), goTR.ExtractString(NewSort, 2, "|"));

                        break;
                    default:
                        break;
                }
            }

            Columns = GetColumnCollection(_viewMetaData, Fields, OrderBy);

            OrderBy = goTR.StrRead(_viewMetaData, "SORT", "");
            if (OrderBy == "")
            {
                OrderBy = godata.GetDefaultSort(TableName);
            }
            //ImageMap = GetImageMap(TableName);
            LinksTop = Convert.ToInt32(goTR.StrRead(_viewMetaData, "LINKSTOP", "5"));
            if (LinksTop == -2)
            {
                LinksTop = clC.SELL_LINKSTOP;
            }

            string ViewCondition = goTR.StrRead(_viewMetaData, "CONDITION", "");


            string FilterText = ViewCondition;
            // check for DRS Date Range Selector Enabled  (Filter -KA)
            // check for Between Date Range Selector (Filter -KA)
            if (FilterText.Contains("<%StartDate%>"))
            {
                string _FilterText = Util.GetDRSFilterCondition(FilterText, Key, _desktopmetadata);

                FilterText = _FilterText;

                if (FilterText == "return empty")
                {
                    FilterText = ViewCondition; //specific to charts                   
                }
            }

            if (FilterText.ToLower().Contains("<%selectedrecordid") || FilterText.ToLower().Contains("<%selectedviewrecordid"))
            {
                Dictionary<string, string> _viewSelectedRecordIds = (Dictionary<string, string>)Util.GetSessionValue(Key + "_" + "ViewSelectedRecordIds");
                IList<string> _viewsLoadedWithFakeIds = (IList<string>)Util.GetSessionValue(Key + "_" + "ViewsLoadedWithFakeIds");

                do
                {
                    if (FilterText.ToLower().Contains("selectedrecordid"))
                    {
                        int iPos = Strings.InStr(FilterText.ToUpper(), Strings.UCase("<%SelectedRecordID FILE="));
                        string sFile = Strings.Mid(FilterText.ToUpper(), iPos + Strings.Len("<%SelectedRecordID FILE="), 2);
                        string sId = "";
                        if (_viewSelectedRecordIds != null && _viewSelectedRecordIds.ContainsKey(sFile) && !string.IsNullOrEmpty(_viewSelectedRecordIds[sFile].ToString()))
                        {
                            sId = _viewSelectedRecordIds[sFile].ToString();
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(Convert.ToString(Util.GetSessionValue(sFile + "_" + "selectedprofilerecid"))))
                            {
                                sId = Util.GetSessionValue(sFile + "_" + "selectedprofilerecid").ToString();
                            }
                            else
                            {
                                sId = godata.GenSUIDFake(sFile);
                            }

                            //Add views which are loaded with fake ids irrespective of evaluation order..J
                            if (_viewsLoadedWithFakeIds == null)
                            {
                                _viewsLoadedWithFakeIds = new List<string>();
                                Util.SetSessionValue(Key + "_" + "ViewsLoadedWithFakeIds", _viewsLoadedWithFakeIds);
                            }
                            if (!_viewsLoadedWithFakeIds.Contains(ViewKey))
                            {
                                _viewsLoadedWithFakeIds.Add(ViewKey);
                            }

                            
                            FilterText = FilterText.ToUpper().Replace("<%SELECTEDVIEWRECORDID FILE=" + sFile + "%>", sId);

                        }
                        //FilterText = FilterText.ToUpper().Replace("<%SELECTEDRECORDID FILE=" + sFile + "%>", sId);
                        FilterText = goTR.Replace(FilterText.ToUpper(), "<%SELECTEDRECORDID FILE=" + sFile + "%>", sId);
                    }
                    else if (FilterText.ToLower().Contains("selectedviewrecordid"))
                    {
                        int iPos = Strings.InStr(FilterText.ToUpper(), Strings.UCase("<%SELECTEDVIEWRECORDID FILE="));
                        string sFile = Strings.Mid(FilterText.ToUpper(), iPos + Strings.Len("<%SELECTEDVIEWRECORDID FILE="), 2);
                        string sId = "";
                        if (_viewSelectedRecordIds != null && _viewSelectedRecordIds.ContainsKey(sFile) && _viewSelectedRecordIds.ContainsKey(sFile) && !string.IsNullOrEmpty(_viewSelectedRecordIds[sFile].ToString()))
                        {
                            sId = _viewSelectedRecordIds[sFile].ToString();
                        }
                        else
                        {
                            sId = godata.GenSUIDFake(sFile);

                            //Add views which are loaded with fake ids irrespective of evaluation order..J
                            if (_viewsLoadedWithFakeIds == null)
                            {
                                _viewsLoadedWithFakeIds = new List<string>();
                                Util.SetSessionValue(Key + "_" + "ViewsLoadedWithFakeIds", _viewsLoadedWithFakeIds);
                            }
                            if (!_viewsLoadedWithFakeIds.Contains(ViewKey))
                            {
                                _viewsLoadedWithFakeIds.Add(ViewKey);
                            }

                        }
                        //FilterText = FilterText.ToUpper().Replace("<%SELECTEDVIEWRECORDID FILE=" + sFile + "%>", sId);
                        FilterText = goTR.Replace(FilterText.ToUpper(), "<%SELECTEDVIEWRECORDID FILE=" + sFile + "%>", sId);
                    }


                } while (FilterText.ToLower().Contains("selectedviewrecordid") || FilterText.ToLower().Contains("selectedrecordid"));

            }
            Filter = FilterText;

            IsTabView = _isTabView;

            //***********************

            bGroupedBars = Convert.ToInt32(goTR.StrRead(_viewMetaData, "SHOWASMULTISERIES", 0));

            UpdatedViewCondition = Filter;

            GetChartData(TableName, Filter, OrderBy, _viewId, _key);

            LoadGraph(_viewId);

            MaxValue = MaxValue + (MaxValue * 0.20);

            ChartTitleInHeaderPanel = ViewTitle;
            ChartDataCount = Convert.ToInt32(godata.GetCount(TableName, Fields, Filter, 300));

            if (Series != null && Series.Count > 0)
            {
                ChartType _icharttype = Series[0].ChartType;

                if (bGroupedBars == 1 || (bGroupedBars == 0 && _icharttype == ChartType.Donut))
                {
                    Series.Clear();

                    string syaxixField = goTR.StrRead(_viewMetaData, "GRAPHYFIELD1", "");
                    string sSort = goTR.StrRead(_viewMetaData, "SORT", "");

                    string sGraphYtotal = goTR.StrRead(_viewMetaData, "GRAPHYSHOW", "");

                    if (!string.IsNullOrEmpty(sGraphYtotal))
                        syaxixField = syaxixField + "|SUM";
                    else
                        syaxixField = "BI__GroupByCount";

                    //if(syaxixField.ToUpper().StartsWith("CUR_"))
                    //{
                    //    ChartAllCurr = true;
                    //}

                    string[] sorts = sSort.Split(',');

                    string sSort1 = sorts.GetValue(0).ToString().Split(' ').GetValue(0).ToString();
                    DataView _dataview;
                    DataTable distinctValues=new DataTable();
                    string sSort2="";

                    if (bGroupedBars == 1)
                    {
                         sSort2 = sorts.GetValue(1).ToString().Split(' ').GetValue(0).ToString();
                        //INT_MLS_STAGE_GROUPING
                        string sExtraFilter = "[INT_" + sSort2 + "_GROUPING]=0";

                        _dataview = new DataView(GroupDataTable, sExtraFilter, sSort2, DataViewRowState.CurrentRows);
                        distinctValues = _dataview.ToTable(true, sSort2); // _dataview.ToTable(true, "MLS_STAGE");

                        int i = 0;

                        foreach (DataRow _row in distinctValues.Rows)
                        {
                            clSeries _newSeries = new clSeries();
                            _newSeries.ChartType = _icharttype;// ChartType.Column;
                            _newSeries.SeriesName = _row[0].ToString();
                            _newSeries.YAxis = new ArrayList();
                            _newSeries.PieSeries = new List<clPieSeries>();

                            switch (i)
                            {
                                case 0:
                                    _newSeries.Color = "#a3ff58";
                                    break;
                                case 1:
                                    _newSeries.Color = "#ff6358";
                                    break;
                                case 2:
                                    _newSeries.Color = "#58ffec";
                                    break;
                                case 3:
                                    _newSeries.Color = "#589bff";
                                    break;
                                case 4:
                                    _newSeries.Color = "#e958ff";
                                    break;
                                case 5:
                                    _newSeries.Color = "#58e6ff";
                                    break;
                                default:
                                    _newSeries.Color = "#ff588d";
                                    break;
                            }


                            _newSeries.Group = _row[0].ToString();

                            foreach (string _xaxis in Xaxis)
                            {
                                string sxaxis = _xaxis;
                                if (_xaxis == "(Blank)")
                                {
                                    sxaxis = "";
                                }

                                string sRowFilter = "";

                                if (bGroupedBars == 1)
                                {
                                    sRowFilter = "[" + sSort1 + "]='" + sxaxis + "' AND [" + sSort2 + "]='" + _newSeries.SeriesName + "'";
                                }
                                else
                                {
                                    sRowFilter = "[" + sSort1 + "]='" + sxaxis + "'";
                                }

                                DataView dv = new DataView(GroupDataTable, sRowFilter, "[" + sSort1 + "]", DataViewRowState.CurrentRows);

                                if (dv != null && dv.ToTable() != null && dv.ToTable().Rows.Count > 0)
                                {
                                    if (syaxixField.ToUpper().StartsWith("CUR_"))
                                    {
                                        double _dval = Convert.ToDouble(dv.ToTable().Rows[0][syaxixField].ToString().Replace("$", "").Replace(",", ""));
                                        if (_dval == 0.0)
                                        {
                                            _newSeries.YAxis.Add(null);
                                        }
                                        else
                                        {
                                            _newSeries.YAxis.Add(_dval);
                                        }

                                        _newSeries.PieSeries.Add(new clPieSeries { category = sxaxis + "-" + _newSeries.SeriesName, value = _dval, SeriesName = _newSeries.SeriesName });
                                    }
                                    else if (syaxixField.ToUpper().StartsWith("BI_"))
                                    {
                                        int _dval = Convert.ToInt32(dv.ToTable().Rows[0][syaxixField].ToString());
                                        if (_dval == 0)
                                        {
                                            _newSeries.YAxis.Add(null);
                                        }
                                        else
                                        {
                                            _newSeries.YAxis.Add(_dval);
                                        }
                                        _newSeries.PieSeries.Add(new clPieSeries { category = sxaxis + "-" + _newSeries.SeriesName, value = _dval, SeriesName = _newSeries.SeriesName });
                                    }
                                    else
                                    {
                                        //dynamic _val = dv.ToTable().Rows[0][syaxixField];
                                        _newSeries.YAxis.Add(dv.ToTable().Rows[0][syaxixField]);
                                        _newSeries.PieSeries.Add(new clPieSeries { category = sxaxis + "-" + _newSeries.SeriesName, value = dv.ToTable().Rows[0][syaxixField], SeriesName = _newSeries.SeriesName });
                                    }

                                    //_newSeries.YAxis.Add(dv.ToTable().Rows[0][syaxixField].ToString().Replace("$", "").Replace(",", ""));
                                }
                                else
                                {
                                    if (syaxixField.ToUpper().StartsWith("BI_"))
                                    {
                                        _newSeries.YAxis.Add(0);
                                    }
                                    else
                                    {
                                        _newSeries.YAxis.Add(null);
                                    }

                                    _newSeries.PieSeries.Add(new clPieSeries { category = sxaxis + "-" + _newSeries.SeriesName, value = 0, SeriesName = _newSeries.SeriesName });
                                }

                            }



                            Series.Add(_newSeries);

                            i++;
                        }
                    }
                    else if (bGroupedBars == 0 && _icharttype == ChartType.Donut)
                    {
                        _dataview = new DataView(GroupDataTable, "", sSort1, DataViewRowState.CurrentRows);
                        distinctValues = _dataview.ToTable(true, sSort1); // _dataview.ToTable(true, "MLS_STAGE");

                        clSeries _newSeries = new clSeries();
                        _newSeries.ChartType = _icharttype;// ChartType.Column;
                        _newSeries.SeriesName = "";
                        _newSeries.YAxis = new ArrayList();
                        _newSeries.PieSeries = new List<clPieSeries>();
                        _newSeries.Color = "#ff6358";
                        
                        _newSeries.Group = "";

                        foreach (string _xaxis in Xaxis)
                        {
                            string sxaxis = _xaxis;
                            if (_xaxis == "(Blank)")
                            {
                                sxaxis = "";
                            }

                            string sRowFilter = "";

                            if (bGroupedBars == 1)
                            {
                                sRowFilter = "[" + sSort1 + "]='" + sxaxis + "' AND [" + sSort2 + "]='" + _newSeries.SeriesName + "'";
                            }
                            else
                            {
                                sRowFilter = "[" + sSort1 + "]='" + sxaxis + "'";
                            }

                            DataView dv = new DataView(GroupDataTable, sRowFilter, "[" + sSort1 + "]", DataViewRowState.CurrentRows);

                            if (dv != null && dv.ToTable() != null && dv.ToTable().Rows.Count > 0)
                            {
                                if (syaxixField.ToUpper().StartsWith("CUR_"))
                                {
                                    double _dval = Convert.ToDouble(dv.ToTable().Rows[0][syaxixField].ToString().Replace("$", "").Replace(",", ""));
                                    if (_dval == 0.0)
                                    {
                                        _newSeries.YAxis.Add(null);
                                    }
                                    else
                                    {
                                        _newSeries.YAxis.Add(_dval);
                                    }

                                    _newSeries.PieSeries.Add(new clPieSeries { category = sxaxis + "-" + _newSeries.SeriesName, value = _dval, SeriesName = _newSeries.SeriesName });
                                }
                                else if (syaxixField.ToUpper().StartsWith("BI_"))
                                {
                                    int _dval = Convert.ToInt32(dv.ToTable().Rows[0][syaxixField].ToString());
                                    if (_dval == 0)
                                    {
                                        _newSeries.YAxis.Add(null);
                                    }
                                    else
                                    {
                                        _newSeries.YAxis.Add(_dval);
                                    }
                                    _newSeries.PieSeries.Add(new clPieSeries { category = sxaxis + "-" + _newSeries.SeriesName, value = _dval, SeriesName = _newSeries.SeriesName });
                                }
                                else
                                {
                                    //dynamic _val = dv.ToTable().Rows[0][syaxixField];
                                    _newSeries.YAxis.Add(dv.ToTable().Rows[0][syaxixField]);
                                    _newSeries.PieSeries.Add(new clPieSeries { category = sxaxis + "-" + _newSeries.SeriesName, value = dv.ToTable().Rows[0][syaxixField], SeriesName = _newSeries.SeriesName });
                                }

                                //_newSeries.YAxis.Add(dv.ToTable().Rows[0][syaxixField].ToString().Replace("$", "").Replace(",", ""));
                            }
                            else
                            {
                                if (syaxixField.ToUpper().StartsWith("BI_"))
                                {
                                    _newSeries.YAxis.Add(0);
                                }
                                else
                                {
                                    _newSeries.YAxis.Add(null);
                                }

                                _newSeries.PieSeries.Add(new clPieSeries { category = sxaxis + "-" + _newSeries.SeriesName, value = 0, SeriesName = _newSeries.SeriesName });
                            }

                        }



                        Series.Add(_newSeries);

                    }

                   
                }
            }

            //Prevent first time chart loading if it is dependent view..J
            if (!IsActive && string.IsNullOrEmpty(MasterViewKey) && isLoadFromSession == false)
            {
                lblNoChartText = "No data found.";
                return;
            }



        }

        public void LoadGraph(string ViewId)
        {

            bool gbPrintMode = false;
            bool gbHideGauge = false;
            bool gbIsQuarterChart = false;
            string gsGaugeQS = "?i=1";
            bool gbAllCurr = true;
            bool gbDiffCurr = false;
            bool gbThin = false;
            string gsLastCurr = "";
            int giBaseFontSize = 7;

            IsTooltipVisible = true;
            GaugeAllCurr = false;
            ChartAllCurr = false;

            //try
            //{

            int G_ColorIndx = 1;

            //Getting the status of Primary colors enabled or not from WG Options meta data.. SB
            bool ShowPrimaryColors = Util.Get_WG_Options_PrimaryColors(goTR);

            string _viewMetaData = ViewMetaData;

            string sGraphType = goTR.StrRead(_viewMetaData, "GRAPHTYPE", "COLUMN", false);
            chartType = sGraphType;

            if (sGraphType == "GAUGE")
            {
                gbHideGauge = false;
            }
            else
            {
                oChartVisible = true;
            }

            int i = 0;
            //lblNoChartText = "";
            double myMaxValue = 0;
            int iGreatestDec = 0;

            bool bIsDTX = false;

            bool bNeedStart = true;
            bool bNeedEnd = true;

            string sStart = "";
            string sEnd = "";

            //if (Util.GetVar("DrsFromDate") != null)
            //{
            //    sStart = HttpContext.Current.Session["DrsFromDate"].ToString();
            //    sEnd = HttpContext.Current.Session["DrsToDate"].ToString();
            //}
            //else
            //{
            sStart = "";
            sEnd = "";
            //}

            Chartname = "Chart" + ViewId;
            CTitle = Title;
            VisibleLegend = true;
            MaxValue = 0;
            ViewID = ViewId;

            clPieSeries _clPieSeries = new clPieSeries();

            switch (sGraphType)
            {
                case "GAUGE":
                    break;
                default:
                    Series = new List<clSeries>();
                    SeriesColors = new ArrayList();

                    //clSeries _clSeries = new clSeries();
                    PieSeries = new List<clPieSeries>();
                    Xaxis = new ArrayList();
                    XaxisValuesUsedInToolTip = new ArrayList();

                    Series.Clear();
                    if (sGraphType == "PIE")
                    {
                        PieSeries.Clear();
                    }
                    Xaxis.Clear();
                    XaxisValuesUsedInToolTip.Clear();
                    break;
            }


            //height/width..
            int iHeight = 495;
            int iWidth = 675;
            if (gbPrintMode)
            {
                switch (sGraphType)
                {
                    case "GAUGE":
                        gsGaugeQS = gsGaugeQS + "&Height=" + iHeight;
                        gsGaugeQS = gsGaugeQS + "&Width=" + iWidth;
                        break;
                    default:
                        //oChart.Height = Unit.Pixel(iHeight);
                        //oChart.Width = Unit.Pixel(iWidth);
                        break;
                }
            }
            else
            {
                iHeight = 0;
                iWidth = 0;
                GetChartSize(ref iHeight, ref iWidth, ViewId, _viewMetaData, DesktopMetaData);
                if (iHeight == 0)
                    iHeight = 200;
                if (iWidth == 0)
                    iWidth = 300;
                if (iHeight < 0)
                    iHeight = 0;
                if (iWidth < 0)
                    iWidth = 0;
                if (iHeight == 0 | iWidth == 0)
                {
                    gbHideGauge = true;
                    //oChart.Visible = false;
                    return;
                }

                ChartHeight = iHeight;
                ChartWidth = iWidth;

                if (ChartHeight > 200)
                {
                    ChartHeight = ChartHeight - 50;
                }
                if (ChartWidth > 500)
                {
                    ChartWidth = ChartWidth - 30;
                }

                if (sGraphType == "GAUGE")
                {
                    gsGaugeQS = gsGaugeQS + "&Height=" + iHeight;
                    gsGaugeQS = gsGaugeQS + "&Width=" + iWidth;
                    ChartHeight = ChartHeight - ((ChartHeight * 50) / 100);
                    ChartWidth = ChartWidth - ((ChartHeight * 50) / 100);
                }

                if (iHeight < iWidth)
                {
                    if (iHeight > 300)
                    {
                        giBaseFontSize = giBaseFontSize + 1;
                    }
                    if (iHeight > 450)
                    {
                        giBaseFontSize = giBaseFontSize + 1;
                    }
                    if (iHeight > 600)
                    {
                        giBaseFontSize = giBaseFontSize + 1;
                    }
                }
                else
                {
                    if (iWidth > 300)
                    {
                        giBaseFontSize = giBaseFontSize + 1;
                    }
                    if (iWidth > 450)
                    {
                        giBaseFontSize = giBaseFontSize + 1;
                    }
                    if (iWidth > 600)
                    {
                        giBaseFontSize = giBaseFontSize + 1;
                    }
                }

            }




            int par_iValid = 4;

            int iSortField = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHSORTFIELD", "1"), "", ref par_iValid, ""));
            int iGraph1Index = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHPARENT1INDEX", "0"), "", ref par_iValid, ""));
            int iGraph2Index = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHPARENT2INDEX", "0"), "", ref par_iValid, ""));
            int iGraph3Index = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHPARENT3INDEX", "0"), "", ref par_iValid, ""));
            string sGraphLabel = HttpUtility.UrlDecode(goTR.StrRead(_viewMetaData, "GRAPHHISTORYLABEL", ""));
            int iGraphXSortFields = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHXSORTFIELDS", "0"), "", ref par_iValid, ""));

            goTR.StrWrite(ref _viewMetaData, "GROUPALL_1STATE", "1");

            SeriesClickToSortField = iSortField;

            //record state for details view
            if (string.IsNullOrEmpty(sGraphLabel))
            {
                //all
            }
            else
            {
                string sGroupLabel = Strings.Replace(sGraphLabel, " ", "_");
                if (sGroupLabel == "(Blank)")
                    sGroupLabel = "";
                if (string.IsNullOrEmpty(sGroupLabel))
                    sGroupLabel = "SELLBLANKGROUP";

                switch (iSortField)
                {
                    case 1:
                        sGroupLabel = sGroupLabel + "_" + iSortField;
                        break;
                    case 2:
                        sGroupLabel = sGroupLabel + "_" + iGraph1Index + "_" + iSortField.ToString();
                        break;
                    case 3:
                        sGroupLabel = sGroupLabel + "_" + iGraph1Index + "_" + iGraph2Index + "_" + iSortField.ToString();
                        break;
                    case 4:
                        sGroupLabel = sGroupLabel + "_" + iGraph1Index + "_" + iGraph2Index + "_" + iGraph3Index + "_" + iSortField.ToString();
                        break;
                }

                goTR.StrWrite(ref _viewMetaData, "GROUP" + sGroupLabel + "STATE", "1");
                ViewMetaData = _viewMetaData;
            }

            //did something change in the sort? if so, go up one level
            //goView.Sorts.Count Then
            if (iSortField > iGraphXSortFields)
            {
                iSortField = iGraphXSortFields;
                switch (iSortField)
                {
                    case 1:
                        iGraph1Index = 0;
                        iGraph2Index = 0;
                        iGraph3Index = 0;
                        break;
                    case 2:
                        iGraph2Index = 0;
                        iGraph3Index = 0;
                        break;
                    case 3:
                        iGraph3Index = 0;
                        break;
                    case 4:
                        break;
                }
                //changed below to 1, 0...was causing out of range errors                    
                goTR.StrWrite(ref _viewMetaData, "GRAPHSORTFIELD", "1");
                goTR.StrWrite(ref _viewMetaData, "GRAPHPARENT1INDEX", iGraph1Index);
                goTR.StrWrite(ref _viewMetaData, "GRAPHPARENT2INDEX", iGraph2Index);
                goTR.StrWrite(ref _viewMetaData, "GRAPHPARENT3INDEX", iGraph3Index);
            }

            //GRAPHXSORTFIELDS
            if (iGraphXSortFields == 0)
            {
                gbHideGauge = true;
                oChartVisible = false;
                //pnlNoChart.Visible = true;
                lblNoChartText = "Cannot display chart. Go to View>Properties>Chart to edit it.";
                return;
            }

            string sIndexes = "";
            string sHistory = "";
            string sBackString = "";
            string sBackLabel = "";

            if (!gbPrintMode)
            {


                if (goTR.StrRead(_viewMetaData, "GRAPHSHOWDATAPOINTVALUE", "0", false) == "1")
                {
                    ShowDataPoints = true;
                }
                if (goTR.StrRead(_viewMetaData, "ALLOWCHANGINGTYPE", "0", false) == "1")
                {
                    AllowChangeType = true;
                    GraphType = goTR.StrRead(_viewMetaData, "GRAPHTYPE", "COLUMN", false);
                }
                YField = goTR.StrRead(_viewMetaData, "GRAPHYSHOW", "COUNT", false);
                if (goTR.StrRead(_viewMetaData, "ALLOWCHANGINGYAXIS", "0", false) == "1")
                {
                    AllowChangeYAxis = true;
                    FillChangeYAxisDdl();
                    GraphYFieldSelectedValue = goTR.StrRead(_viewMetaData, "GRAPHYFIELD1", "<%NONE%>", false);
                }
                if (goTR.StrRead(_viewMetaData, "ALLOWCHANGINGSORT", "0", false) == "1")
                {
                    AllowChangeXAxis = true;
                    FillChangeXAxisDdl();
                }


                //back link
                if (iSortField == 1)
                {
                    //pnlBack.Visible = false;
                    AddChartHistory(_viewMetaData, 1, "0-0-0", "All");
                }
                else
                {
                    sGraphLabel = Strings.Replace(sGraphLabel, "SELLLESSTHAN", "<");
                    sGraphLabel = Strings.Replace(sGraphLabel, "SELLGREATERTHAN", ">");
                    sGraphLabel = Strings.Replace(sGraphLabel, "SELLSINGLEQUOTE", "'");

                    switch (iSortField)
                    {
                        case 2:
                            sIndexes = iGraph1Index.ToString() + "-0-0";
                            AddChartHistory(_viewMetaData, iSortField, sIndexes, sGraphLabel);
                            sBackString = "0-0-0";
                            sBackLabel = "All";

                            break;

                        case 3:
                            sIndexes = iGraph1Index.ToString() + "-" + iGraph2Index.ToString() + "-0";
                            AddChartHistory(_viewMetaData, iSortField, sIndexes, sGraphLabel);

                            break;
                        case 4:
                            sIndexes = iGraph1Index.ToString() + "-" + iGraph2Index.ToString() + "-" + iGraph3Index.ToString();
                            AddChartHistory(_viewMetaData, iSortField, sIndexes, sGraphLabel);

                            break;

                        default:
                            //pnlBack.Visible = false;
                            break;
                    }

                }

            }
            else
            {
                ////hide stuff for print mode
                //btnRefreshChart.Visible = false;
                //pnlBack.Visible = false;
                //pnlTools.Visible = false;
                //pnlListView.Style.Remove("border");
                //pnlTitleBar.Visible = false;
                //lblPrintViewTitle.Visible = true;
                //lblPrintViewTitle.Text = goView.Title;
                //lblPrintViewFilter.Visible = true;
            }

            //GRAPHYFIELD1
            string sGraphYField1 = goTR.StrRead(_viewMetaData, "GRAPHYFIELD1", "NONE", false);
            string sGraphYField2 = goTR.StrRead(_viewMetaData, "GRAPHYFIELD2", "NONE", false);
            string sGraphYField3 = goTR.StrRead(_viewMetaData, "GRAPHYFIELD3", "NONE", false);
            string sGraphYField4 = goTR.StrRead(_viewMetaData, "GRAPHYFIELD4", "NONE", false);

            //if pie, only support a single chart field
            if (sGraphType == "PIE")
            {
                sGraphYField2 = "NONE";
                sGraphYField3 = "NONE";
                sGraphYField4 = "NONE";
            }

            //count,total,average,max,min   
            string sGraphYShow = goTR.StrRead(_viewMetaData, "GRAPHYSHOW", "COUNT", false);
            int iGraphAverage = 0;
            int iGraphMin = 0;
            int iGraphMax = 0;
            int iGraphMed = 0;
            string sGraphRecordCountDisplay = "COUNT";
            switch (sGraphYShow)
            {
                case "COUNT":
                    //GRAPHYRECORDCOUNTDISPLAY=COUNTPERCENT
                    sGraphRecordCountDisplay = goTR.StrRead(_viewMetaData, "GRAPHYRECORDCOUNTDISPLAY", "COUNT", false);
                    break;
                //COUNT: Shows count values only, like until now
                //PERCENT: Shows only percentage of total of all values shown in that group level of the chart
                //COUNTPERCENT: Shows count (<percentage>%) rounded to integer. Example: 27 (44%)
                case "STATISTICS":
                    SeriesColors = new ArrayList();
                    iGraphAverage = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHYAVG", "1", false), "", ref par_iValid, ""));
                    iGraphMin = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHYMIN", "0", false), "", ref par_iValid, ""));
                    iGraphMax = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHYMAX", "0", false), "", ref par_iValid, ""));
                    iGraphMed = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHYMED", "0", false), "", ref par_iValid, ""));

                    //if pie, only support a single chart field
                    if (sGraphType == "PIE")
                    {
                        //ONLY SHOW ONE STATISTIC, THE FIRST ONE
                        if (iGraphAverage == 1)
                        {
                            iGraphMin = 0;
                            iGraphMax = 0;
                            iGraphMed = 0;
                        }
                        else if (iGraphMin == 1)
                        {
                            iGraphAverage = 0;
                            iGraphMax = 0;
                            iGraphMed = 0;
                        }
                        else if (iGraphMax == 1)
                        {
                            iGraphMin = 0;
                            iGraphAverage = 0;
                            iGraphMed = 0;
                        }
                        else if (iGraphMed == 1)
                        {
                            iGraphMin = 0;
                            iGraphAverage = 0;
                            iGraphMax = 0;
                        }

                    }
                    break;

                case "TOTAL":
                    break;
                default:
                    sGraphYShow = "COUNT";
                    break;
            }

            //if TOTAL and no Y field selected
            if ((sGraphYShow == "TOTAL" | sGraphYShow == "STATISTICS") & (sGraphYField1.Contains("NONE") | string.IsNullOrEmpty(sGraphYField1)))
            {
                gbHideGauge = true;
                //oChart.Visible = false;
                //pnlNoChart.Visible = true;
                int iChangeYAxis = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "ALLOWCHANGINGYAXIS", "0"), "", ref par_iValid, ""));
                switch (iChangeYAxis)
                {
                    case 0:
                        lblNoChartText = "Cannot display chart. Go to View>Properties>Chart to edit it.";
                        break;
                    default:
                        lblNoChartText = "Cannot display chart. Select a y axis field from the view or go to View>Properties>Chart to edit it.";
                        break;
                }
                return;
            }

            //axis titles
            string sXField = Sorts[iSortField].ToString();
            sXField = Strings.Replace(Strings.Replace(goTR.ExtractString(sXField, 1, " "), "<%", ""), "%>", "");
            GraphXFieldSelectedValue = sXField;
            XaxisLabel = godata.GetFieldFullLabelFromName(TableName, sXField, ref par_iValid);

            if (sGraphType.ToUpper() == "PIE")
            {
                string title = "";
                if (sGraphYShow == "TOTAL" | sGraphYShow == "STATISTICS")
                {
                    XaxisLabel = godata.GetFieldFullLabelFromName(TableName, sXField, ref par_iValid);
                }
                else if (sGraphYShow == "COUNT")
                {
                    title = "Count";
                }
                XaxisLabel = XaxisLabel + ": " + title;
            }

            //NEED TO SET IT QUARTER CHART IN ORDER TO FORMAT X AXIS LABELS IN CUSTOMIZE METHOD
            switch (Strings.Left(sXField, 4))
            {
                case "DTQ_":
                    gbIsQuarterChart = true;
                    break;
                default:
                    gbIsQuarterChart = false;
                    break;
            }

            switch (sGraphType)
            {
                case "GAUGE":

                    switch (Strings.Left(sXField, 4))
                    {
                        case "DTY_":
                            bIsDTX = true;
                            break;
                        case "DTQ_":
                            bIsDTX = true;
                            break;
                        case "DTM_":
                            bIsDTX = true;
                            break;
                        case "DTD_":
                            bIsDTX = true;
                            break;
                        default:
                            bIsDTX = false;
                            break;
                    }

                    XaxisLabel = godata.GetFieldFullLabelFromName(TableName, sXField, ref par_iValid);
                    string sTitle = "";
                    if (sGraphYShow == "TOTAL" | sGraphYShow == "STATISTICS")
                    {
                        sTitle = godata.GetFieldFullLabelFromName(TableName, sGraphYField1, ref par_iValid);
                    }
                    else if (sGraphYShow == "COUNT")
                    {
                        sTitle = "Count";
                    }
                    gsGaugeQS = gsGaugeQS + "&Title=" + HttpUtility.UrlEncode(XaxisLabel + ": " + sTitle);
                    GaugeXaxisTitle = XaxisLabel + ": " + sTitle;
                    break;
                default:

                    int iPointInterval = 1;
                    DateTimeIntervalType oIntervalType = DateTimeIntervalType.Auto;
                    switch (Strings.Left(sXField, 4))
                    {
                        case "DTY_":
                            bIsDTX = true;
                            iPointInterval = 1;
                            oIntervalType = DateTimeIntervalType.Years;
                            XaxisLabelStyleFormat = "yyyy";
                            //_SessionViewInfo.ChartAreas("ChartArea1").AxisX.LabelStyle.Format = "yyyy";
                            break;
                        case "DTQ_":
                            bIsDTX = true;
                            iPointInterval = 3;
                            oIntervalType = DateTimeIntervalType.Months;
                            break;
                        case "DTM_":
                            bIsDTX = true;
                            iPointInterval = 1;
                            oIntervalType = DateTimeIntervalType.Months;
                            XaxisLabelStyleFormat = "yyyy-MM";
                            break;
                        case "DTD_":
                            bIsDTX = true;
                            iPointInterval = 1;
                            oIntervalType = DateTimeIntervalType.Days;
                            XaxisLabelStyleFormat = "yyyy-MM-dd";
                            break;
                        default:
                            bIsDTX = false;
                            break;
                    }

                    //if (goDesktop.DateRangeSelectorSelectedRange == "ALL")
                    //{
                    //    sStart = "";
                    //    sEnd = "";
                    //}

                    //if drs is "last 12 months, or the day is not the 1st - 31st, then i need to set that in end
                    if (bIsDTX)
                    {
                        if (Information.IsDate(sStart))
                        {
                            switch (Strings.Left(sXField, 4))
                            {
                                case "DTY_":
                                    System.DateTime dDate = Convert.ToDateTime(sStart);
                                    System.DateTime dNewDate = new System.DateTime(dDate.Year - 1, 1, 2);
                                    sStart = dNewDate.ToString();
                                    break;
                                case "DTQ_":
                                    dDate = goTR.GetQuarterDate(Convert.ToDateTime(sStart));
                                    dDate = dDate.AddMonths(-3);
                                    dNewDate = new System.DateTime(dDate.Year, dDate.Month, 2);
                                    sStart = dNewDate.ToString();
                                    break;
                                case "DTM_":
                                    dDate = Convert.ToDateTime(sStart).AddMonths(-1);
                                    dNewDate = new System.DateTime(dDate.Year, dDate.Month, 2);
                                    sStart = dNewDate.ToString();
                                    break;
                                case "DTD_":
                                    sStart = Convert.ToDateTime(sStart).AddHours(-23).ToString();
                                    break;
                                default:
                                    break;
                                    //no change
                            }
                            //oChart.ChartAreas("ChartArea1").AxisX.Minimum = Convert.ToDateTime(sStart).ToOADate();
                            bNeedStart = false;
                        }
                        if (Information.IsDate(sEnd))
                        {
                            switch (Strings.Left(sXField, 4))
                            {
                                case "DTY_":
                                    System.DateTime dDate = Convert.ToDateTime(sEnd);
                                    System.DateTime dNewDate = new System.DateTime();
                                    if (dDate.Month == 2 && dDate.Year % 4 > 0)
                                    {
                                        dNewDate = new System.DateTime(dDate.Year, 12, 28);
                                    }
                                    else
                                    {
                                        dNewDate = new System.DateTime(dDate.Year, 12, 29);
                                    }

                                    sEnd = dNewDate.ToString();
                                    break;
                                case "DTQ_":
                                    dDate = goTR.GetQuarterEndDateTime(Convert.ToDateTime(sEnd));

                                    if (dDate.Month == 2 && dDate.Year % 4 > 0)
                                    {
                                        dNewDate = new System.DateTime(dDate.Year, dDate.Month, 28);
                                    }
                                    else
                                    {
                                        dNewDate = new System.DateTime(dDate.Year, dDate.Month, 29);
                                    }

                                    sEnd = dNewDate.ToString();
                                    break;
                                case "DTM_":
                                    dDate = Convert.ToDateTime(sEnd);

                                    if (dDate.Month == 2 && dDate.Year % 4 > 0)
                                    {
                                        dNewDate = new System.DateTime(dDate.Year, dDate.Month, 28);
                                    }
                                    else
                                    {
                                        dNewDate = new System.DateTime(dDate.Year, dDate.Month, 29);
                                    }

                                    sEnd = dNewDate.ToString();
                                    break;
                                case "DTD_":
                                    break;
                                default:
                                    break;
                                    //no change
                            }
                            //oChart.ChartAreas("ChartArea1").AxisX.Maximum = Convert.ToDateTime(sEnd).ToOADate();
                            bNeedEnd = false;
                        }

                        //oChart.ChartAreas("ChartArea1").AxisX.Interval = iPointInterval;
                        //oChart.ChartAreas("ChartArea1").AxisX.IntervalType = oIntervalType;
                        XaxisInterval = iPointInterval;
                        XaxisIntervalType = oIntervalType.ToString();
                    }

                    XaxisLabel = godata.GetFieldFullLabelFromName(TableName, sXField, ref par_iValid);
                    //oChart.ChartAreas("ChartArea1").AxisX.Title = goView.GraphXLabel;
                    if (sGraphYShow == "COUNT")
                    {
                        YaxisLabel = "Count";
                    }
                    else
                    {
                        sTitle = "";
                        sTitle = godata.GetFieldFullLabelFromName(TableName, sGraphYField1, ref par_iValid);
                        if (sGraphYShow == "TOTAL")
                        {
                            if (sGraphYField2 != "NONE" & !string.IsNullOrEmpty(sGraphYField2))
                                sTitle = sTitle + ", " + godata.GetFieldFullLabelFromName(TableName, sGraphYField2, ref par_iValid);
                            if (sGraphYField3 != "NONE" & !string.IsNullOrEmpty(sGraphYField3))
                                sTitle = sTitle + ", " + godata.GetFieldFullLabelFromName(TableName, sGraphYField3, ref par_iValid);
                            if (sGraphYField4 != "NONE" & !string.IsNullOrEmpty(sGraphYField4))
                                sTitle = sTitle + ", " + godata.GetFieldFullLabelFromName(TableName, sGraphYField4, ref par_iValid);
                        }
                        YaxisLabel = sTitle;
                    }

                    break;
            }

            //y axis format  
            switch (sGraphType)
            {
                case "GAUGE":
                    if (sGraphYShow == "COUNT")
                    {
                        //NO CHANGE???            
                        gbAllCurr = false;
                    }
                    else if (sGraphYShow == "TOTAL" & !string.IsNullOrEmpty(sGraphYField2))
                    {
                        if (Strings.Left(sGraphYField1, 4) != "CUR_")
                            gbAllCurr = false;
                    }
                    else
                    {
                        switch (Strings.Left(sGraphYField1, 4))
                        {
                            case "CUR_":
                                gbAllCurr = true;
                                break;
                            case "SR__":
                            case "DR__":
                            case "CHK_":
                                //NO CHANGE???
                                gbAllCurr = false;
                                break;
                            default:
                                gbAllCurr = false;
                                break;
                        }
                    }
                    GaugeAllCurr = gbAllCurr;
                    break;
                default:
                    if (sGraphYShow == "COUNT")
                    {
                        //NO CHANGE???                                    
                        gbAllCurr = false;
                    }
                    else if (sGraphYShow == "TOTAL" & !string.IsNullOrEmpty(sGraphYField2))
                    {
                        //NO CHANGE???
                        if (Strings.Left(sGraphYField1, 4) != "CUR_")
                            gbAllCurr = false;
                        if (sGraphYField2 != "NONE" & Strings.Left(sGraphYField2, 4) != "CUR_")
                            gbAllCurr = false;
                        if (sGraphYField3 != "NONE" & Strings.Left(sGraphYField3, 4) != "CUR_")
                            gbAllCurr = false;
                        if (sGraphYField4 != "NONE" & Strings.Left(sGraphYField4, 4) != "CUR_")
                            gbAllCurr = false;
                        //oChart.ChartAreas("ChartArea1").AxisY.LabelStyle.Format = "{0:#,###}";

                        Format = "{0:#,###}";
                    }
                    else
                    {
                        switch (Strings.Left(sGraphYField1, 4))
                        {
                            case "CUR_":
                                //oChart.ChartAreas("ChartArea1").AxisY.LabelStyle.Format = "{0:#,###}";
                                gbAllCurr = true;
                                break;
                            case "SR__":
                            case "DR__":
                            case "CHK_":
                                //NO CHANGE???
                                gbAllCurr = false;
                                break;
                            default:
                                gbAllCurr = false;
                                break;
                        }
                    }

                    //legend
                    if (sGraphYShow == "COUNT" && sGraphType != "PIE" && bGroupedBars == 0)
                    {
                        IsLegendEnabled = false;
                    }
                    else
                    {
                        IsLegendEnabled = true;
                    }
                    ChartAllCurr = gbAllCurr;
                    break;
            }

            int x = 1;
            string sField = sGraphYField1;
            if (sGraphYShow == "COUNT")
                sField = "";
            string sToolTip = "";

            bool bWroteThis = false;
            RedoThisForMultipleYs:
            RedoThisForStatistics:


            //FORMATTING
            string sFormat = "";
            int iDec = 0;
            string sDef = "0";
            switch (Strings.Left(sField, 4))
            {
                case "CUR_":
                    sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName.ToUpper(), sField.ToUpper() + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                    sDef = goTR.ExtractString(sFormat, 1, "|");
                    if (Strings.UCase(sDef) == "DEF")
                        sDef = "2";
                    string sSymbol = goTR.ExtractString(sFormat, 7, "|");
                    if (sSymbol == "Def")
                    {
                        sFormat = goTR.GetCurrFormat(sFormat);
                        sSymbol = goTR.ExtractString(sFormat, 7);
                    }
                    if (Strings.UCase(sSymbol) == "<%NONE%>")
                        sSymbol = "";
                    if (!string.IsNullOrEmpty(sSymbol) & !string.IsNullOrEmpty(gsLastCurr))
                    {
                        if (sSymbol != gsLastCurr)
                            gbDiffCurr = true;
                    }
                    if (!string.IsNullOrEmpty(sSymbol))
                        gsLastCurr = sSymbol;
                    break;
                case "SR__":
                    sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName.ToUpper(), sField.ToUpper() + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                    sDef = goTR.ExtractString(sFormat, 1, "|");
                    if (Strings.UCase(sDef) == "DEF")
                        sDef = "0";
                    break;
                case "DR__":
                    sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName.ToUpper(), sField.ToUpper() + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                    sDef = goTR.ExtractString(sFormat, 1, "|");
                    if (Strings.UCase(sDef) == "DEF")
                        sDef = "0";
                    break;
            }
            iDec = Convert.ToInt32(goTR.StringToNum(sDef, "", ref par_iValid, ""));

            //decimals
            int iGraph0Decimals = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPH0DECIMALS", "1", true), "", ref par_iValid, ""));
            if (iGraph0Decimals == 1)
            {
                //replace decimal position in format string with 0
                if (Strings.InStr(sFormat, "|") > 0)
                    sFormat = "0" + Strings.Mid(sFormat, Strings.InStr(sFormat, "|"));
                if (Strings.InStr(sFormat, Constants.vbTab) > 0)
                    sFormat = "0" + Strings.Mid(sFormat, Strings.InStr(sFormat, Constants.vbTab));
                iDec = 0;
            }

            if (iDec > iGreatestDec)
                iGreatestDec = iDec;

            if (sGraphYShow == "STATISTICS")
            {
                if (x == 1 & iGraphAverage == 0)
                    goto GetNext;
                if (x == 2 & iGraphMin == 0)
                    goto GetNext;
                if (x == 3 & iGraphMax == 0)
                    goto GetNext;
                if (x == 4 & iGraphMed == 0)
                    goto GetNext;
            }

            string sSeriesName = "Series" + x.ToString();
            switch (sGraphYShow)
            {
                case "COUNT":
                    sSeriesName = "Count";
                    break;
                case "STATISTICS":
                    if (x == 1)
                        sSeriesName = "Avg";
                    if (x == 2)
                        sSeriesName = "Min";
                    if (x == 3)
                        sSeriesName = "Max";
                    if (x == 4)
                        sSeriesName = "Med";
                    break;
                case "TOTAL":
                    if (x == 1)
                        sSeriesName = "Total for " + godata.GetFieldLabel(TableName, sGraphYField1);
                    if (x == 2)
                        sSeriesName = "Total for " + godata.GetFieldLabel(TableName, sGraphYField2);
                    if (x == 3)
                        sSeriesName = "Total for " + godata.GetFieldLabel(TableName, sGraphYField3);
                    if (x == 4)
                        sSeriesName = "Total for " + godata.GetFieldLabel(TableName, sGraphYField4);
                    break;
            }
            Series oSeries = new Series(sSeriesName);

            clSeries _clSeries = new clSeries();
            _clSeries.SeriesName = sSeriesName;
            _clSeries.YAxis = new ArrayList();
            _clSeries.Group = TableName;
            //26032019 tckt #2728:Chart series not showing correctly in allied autmation
            Xaxis = new ArrayList();    //Need to clear previous xseries otherwise it will show wrong dates when missing dates are there..J

            //date/time series?
            //if (bIsDTX)
            //    oSeries.XValueType = ChartValueType.DateTime;

            //set view sort
            bool bIsDesc = false;
            if (bIsDTX)
            {
                if (GetSortFieldDirection(sXField, gcSorts) != "ASC")
                {
                    //oChart.ChartAreas("ChartArea1").AxisX.IsReversed = true;
                    bIsDesc = true;
                }
            }

            //gradient?
            switch (sGraphType)
            {
                case "GAUGE":
                    break;
                default:
                    int iGradient = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHGRADIENT", "0", false), "", ref par_iValid, ""));
                    if (iGradient == 0)
                    {
                        //oChart.ChartAreas("ChartArea1").BackGradientStyle = GradientStyle.None;
                    }

                    //datapoint style
                    if (sGraphType != "PIE")
                    {
                        switch (x)
                        {
                            case 1:
                                _clSeries.Color = "#4682B4";
                                break;
                            case 2:
                                _clSeries.Color = "#B22222";
                                break;
                            case 3:
                                _clSeries.Color = "#FF8C00";
                                break;
                            case 4:
                                _clSeries.Color = "#808000";
                                break;
                        }

                    }
                    else if (sGraphType == "PIE")
                    {
                        if (PieSeries.Count == 0)
                        {
                            _clPieSeries.color = "#006400";
                        }
                    }

                    break;
            }

            //data point labels
            int iDataPointLabel = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHSHOWDATAPOINTVALUE", "0", false), "", ref par_iValid, ""));

            //oSeries.ChartArea = "ChartArea1";

            switch (sGraphType)
            {
                case "GAUGE":
                    break;

                case "AREA":
                    _clSeries.ChartType = ChartType.Area;
                    break;
                case "BAR":
                case "Bar (no Goal line)":
                case "BAR (NO GOAL LINE)":
                    _clSeries.ChartType = ChartType.Bar;
                    break;
                case "COLUMN":
                    _clSeries.ChartType = ChartType.Column;
                    break;
                case "LINE":
                    _clSeries.ChartType = ChartType.Line;
                    break;
                case "PIE":
                case "Pie (no Goal line)":
                case "PIE (NO GOAL LINE)":
                    _clSeries.ChartType = ChartType.Pie;
                    break;
                case "STACKEDAREA":
                    _clSeries.ChartType = ChartType.StackedArea;
                    break;
                case "STACKEDBAR":
                    _clSeries.ChartType = ChartType.StackedBar;
                    break;
                case "STACKEDCOLUMN":
                    _clSeries.ChartType = ChartType.StackedColumn;
                    break;
                case "DONUT":
                    _clSeries.ChartType = ChartType.Donut;
                    break;
                default:
                    _clSeries.ChartType = ChartType.Column;
                    break;
            }

            switch (sGraphType)
            {
                case "GAUGE":
                    break;
                default:
                    if (iDataPointLabel == 1)
                    {
                        if (gbAllCurr)
                        {
                            Format = gsLastCurr + "{0:#,###}";
                        }
                        else
                        {
                            Format = "{0:#,###}";
                        }
                    }
                    break;
            }
            ViewMetaData = _viewMetaData;
            int iCount = GetChartDataPointCount(sField, iSortField, iGraph1Index, iGraph2Index, iGraph3Index);
            _viewMetaData = ViewMetaData;

            if (sGraphType == "GAUGE" & iCount > 1)
            {
                if (!bIsDTX)
                {
                    iCount = 1;
                }
            }
            int iGaugePoints = 0;

            sIndexes = "";

            if (iCount == 0)
            {
                gbHideGauge = true;
                oChartVisible = false;
                //pnlNoChart.Visible = true;
                lblNoChartText = "No data found.";
                return;
                //gbNoData = true;                    
            }

            // Populate new series with data
            for (i = 1; i <= iCount; i++)
            {

                string sLabel = "";
                double dCount = 0;
                double dTotal = 0;
                double dAverage = 0;
                double dMin = 0;
                double dMax = 0;
                double dMed = 0;
                double dDate = 0;

                if (sGraphType != "GAUGE")
                    sToolTip = "";

                switch (iSortField)
                {
                    case 1:
                        sIndexes = i.ToString() + "-0-0";
                        break;
                    case 2:
                        sIndexes = iGraph1Index.ToString() + "-" + i.ToString() + "-0";
                        break;
                    case 3:
                        sIndexes = iGraph1Index.ToString() + "-" + iGraph2Index.ToString() + "-" + i.ToString();
                        break;
                    default:
                        sIndexes = iGraph1Index.ToString() + "-" + iGraph2Index.ToString() + "-" + iGraph3Index.ToString();
                        break;
                }

                if (GetChartDataPoint(ref sLabel, ref dCount, ref dTotal, ref dAverage, ref dMin, ref dMax, ref dMed, ref dDate, sField, iSortField, iGraph1Index, iGraph2Index, iGraph3Index, i))
                {
                    if (string.IsNullOrEmpty(sLabel) & bIsDTX)
                    {
                        goto SkipMergedRecord;
                    }
                    //really, not "skipping merged record" but skipping blank date record for date range charts                                        

                    if (string.IsNullOrEmpty(sLabel))
                        sLabel = "(Blank)";

                    if (sGraphType == "GAUGE")
                        iGaugePoints = iGaugePoints + 1;

                    switch (sGraphYShow)
                    {
                        case "COUNT":

                            #region count switch
                            switch (sGraphType)
                            {
                                case "GAUGE":
                                    GaugePointerValue = dCount;
                                    switch (sGraphRecordCountDisplay)
                                    {
                                        //COUNT: Shows count values only, like until now
                                        //PERCENT: Shows only percentage of total of all values shown in that group level of the chart
                                        //COUNTPERCENT: Shows count (<percentage>%) rounded to integer. Example: 27 (44%)
                                        case "COUNT":
                                            //no change
                                            gsGaugeQS = gsGaugeQS + "&NeedleValue=" + dCount;
                                            sToolTip = sLabel + ": " + dCount;
                                            break;
                                        case "PERCENT":
                                            decimal lPercent = Convert.ToDecimal((dCount / RollupTotal) * 100);
                                            string sPercent = goTR.NumToString(lPercent, "1");
                                            gsGaugeQS = gsGaugeQS + "&GraphRecordCountDisplay=" + sGraphRecordCountDisplay + "&ShowPercentage=true&PercentageValue=" + sPercent + "&NeedleValue=" + dCount;
                                            sToolTip = sLabel + ": (" + lPercent + "%)";
                                            break;
                                        case "COUNTPERCENT":
                                            lPercent = Convert.ToDecimal((dCount / RollupTotal) * 100);
                                            sPercent = goTR.NumToString(lPercent, "1");
                                            gsGaugeQS = gsGaugeQS + "&GraphRecordCountDisplay=" + sGraphRecordCountDisplay + "&ShowPercentage=true&PercentageValue=" + sPercent + "&NeedleValue=" + dCount;
                                            sToolTip = sLabel + ": " + dCount + " (" + sPercent + "%)";
                                            break;
                                    }

                                    ChartTitleInToolTip = sToolTip;
                                    break;

                                default:


                                    if (bIsDTX)
                                    {
                                        if (bNeedStart & _clSeries.YAxis.Count == 0)
                                        {
                                            double dStart = 0.0;
                                            double dEnd = 0.0;
                                            if (!bIsDesc)
                                            {
                                                dStart = GetMinMaxDate("MIN", sXField, dDate);
                                                sStart = System.DateTime.FromOADate(dStart).ToString();

                                                //let's get end date here just in case this is the last point
                                                dEnd = GetMinMaxDate("MAX", sXField, dDate);
                                                sEnd = System.DateTime.FromOADate(dEnd).ToString();
                                            }
                                            else
                                            {
                                                dEnd = GetMinMaxDate("MAX", sXField, dDate);
                                                sEnd = System.DateTime.FromOADate(dEnd).ToString();

                                                //let's get end date here just in case this is the last point
                                                dStart = GetMinMaxDate("MIN", sXField, dDate);
                                                sStart = System.DateTime.FromOADate(dStart).ToString();
                                            }
                                        }
                                        else if (bNeedEnd)
                                        {
                                            double dStart = 0.0;
                                            double dEnd = 0.0;
                                            if (bIsDesc)
                                            {
                                                dStart = GetMinMaxDate("MIN", sXField, dDate);
                                                sStart = System.DateTime.FromOADate(dStart).ToString();
                                            }
                                            else
                                            {
                                                dEnd = GetMinMaxDate("MAX", sXField, dDate);
                                                sEnd = System.DateTime.FromOADate(dEnd).ToString();
                                            }
                                        }
                                    }

                                    //lets check for missing dates
                                    string MissingLabel;
                                    if (bIsDTX & sGraphType != "PIE" & sGraphType != "GAUGE")
                                    {
                                        double dLastDate = Convert.ToDateTime(sStart).ToOADate();
                                        //if (oSeries.Points.Count > 0)
                                        //{
                                        //    dLastDate = oSeries.Points[oSeries.Points.Count - 1].XValue;
                                        //}
                                        if (Xaxis.Count > 0)
                                        {
                                            if ((Xaxis[Xaxis.Count - 1]).ToString().Contains("-"))
                                            {
                                                string sLastDate = Xaxis[Xaxis.Count - 1].ToString();
                                                if (sLastDate.Contains(":"))
                                                {
                                                    sLastDate = sLastDate.Substring(0, sLastDate.IndexOf(':'));
                                                }
                                                dLastDate = Convert.ToDateTime(sLastDate).ToOADate();
                                                //dLastDate = Convert.ToDateTime(Xaxis[Xaxis.Count - 1]).ToOADate();
                                            }
                                        }
                                        System.Collections.Generic.List<double> oMissing = GetDTxMissingValues(sXField, dLastDate, dDate);
                                        if (oMissing.Count > 0)
                                        {
                                            for (int t = 0; t <= oMissing.Count - 1; t++)
                                            {
                                                MissingLabel = "";
                                                ////let's not show point label for fake data ;)
                                                if (!gbPrintMode)
                                                {
                                                    MissingLabel = GetAxisLabel(oMissing[t], sXField);
                                                    string sMissing = GetAxisLabel(oMissing[t], sXField) + ": No data";
                                                }
                                                //oSeries.Points.Add(oPoint);
                                                Xaxis.Add(MissingLabel);
                                                _clSeries.YAxis.Add(0);
                                            }
                                        }
                                    }


                                    int iNum = i;
                                    if ((oSeries.Points.Count == 0 & sGraphType.Contains("AREA")))
                                    {
                                        iNum = 1;
                                    }
                                    if ((iCount == 1 & sGraphType == "LINE"))
                                    {
                                        iNum = 2;
                                    }

                                    if (dCount > myMaxValue)
                                        myMaxValue = dCount;


                                    //if (sGraphType == "PIE")
                                    //{
                                    //    if (sLabel.Length > 14)
                                    //    {
                                    //        //oPoint.LegendText = Strings.Left(sLabel, 14);
                                    //    }
                                    //    else
                                    //    {
                                    //        //oPoint.LegendText = sLabel;
                                    //    }
                                    //    if ((_clSeries.YAxis.Count == 0))
                                    //    {
                                    //        //oPoint.MarkerColor = System.Drawing.Color.Green;
                                    //    }
                                    //}

                                    if (iDataPointLabel == 1)
                                    {
                                        switch (sGraphRecordCountDisplay)
                                        {
                                            //COUNT: Shows count values only, like until now
                                            //PERCENT: Shows only percentage of total of all values shown in that group level of the chart
                                            //COUNTPERCENT: Shows count (<percentage>%) rounded to integer. Example: 27 (44%)
                                            case "COUNT":
                                                break;
                                            //no change
                                            case "PERCENT":
                                                decimal lPercent = Convert.ToDecimal((dCount / RollupTotal) * 100);
                                                string sPercent = goTR.NumToString(lPercent, "1") + "%";
                                                //oPoint.Label = sPercent;
                                                break;
                                            case "COUNTPERCENT":
                                                lPercent = Convert.ToDecimal((dCount / RollupTotal) * 100);
                                                sPercent = " (" + goTR.NumToString(lPercent, "1") + "%" + ")";
                                                //oPoint.Label = dCount + sPercent;
                                                break;
                                        }
                                    }

                                    if (sLabel.Length > 28)
                                        sLabel = Strings.Left(sLabel, 25) + "...";

                                    string sValue = string.Format("{0:#,###}", dCount);
                                    string sValueLabel = string.Format("{0:#,###}", dCount);
                                    if (iSortField < iGraphXSortFields & !gbPrintMode)
                                    {
                                        //string sValue = string.Format("{0:#,###}", dCount);

                                        switch (sGraphRecordCountDisplay)
                                        {
                                            //COUNT: Shows count values only, like until now
                                            //PERCENT: Shows only percentage of total of all values shown in that group level of the chart
                                            //COUNTPERCENT: Shows count (<percentage>%) rounded to integer. Example: 27 (44%)
                                            case "COUNT":
                                                break;
                                            //no change
                                            case "PERCENT":
                                                decimal lPercent = Convert.ToDecimal((dCount / RollupTotal) * 100);
                                                string sPercent = goTR.NumToString(lPercent, "1") + "%";
                                                sValue = sPercent;
                                                sValueLabel = sPercent;
                                                break;
                                            case "COUNTPERCENT":
                                                lPercent = Convert.ToDecimal((dCount / RollupTotal) * 100);
                                                sPercent = " (" + goTR.NumToString(lPercent, "1") + "%" + ")";
                                                sValue = sValue + sPercent;
                                                sValueLabel = sValue;
                                                break;
                                        }

                                        if (Strings.UCase(sLabel) == "<MAKE SELECTION>")
                                        {
                                            sValue = "MAKESELECTION: " + sValue;
                                            sLabel = Strings.Replace(sLabel, "<", "-");
                                            sLabel = Strings.Replace(sLabel, ">", "-");
                                        }
                                        else if (Strings.UCase(sLabel) == "<DON'T KNOW>")
                                        {
                                            sValue = "DONTKNOW: " + sValue;
                                            sLabel = Strings.Replace(sLabel, "<", "-");
                                            sLabel = Strings.Replace(sLabel, ">", "-");
                                        }
                                        else
                                        {
                                            sLabel = Strings.Replace(sLabel, "<", "SELLLESSTHAN");
                                            sLabel = Strings.Replace(sLabel, ">", "SELLGREATERTHAN");
                                            sLabel = Strings.Replace(sLabel, "'", "SELLSINGLEQUOTE");
                                            sValue = sLabel;
                                            //sValue = sLabel + ": " + sValue;
                                        }
                                    }
                                    else if (!gbPrintMode)
                                    {

                                        switch (sGraphRecordCountDisplay)
                                        {
                                            //COUNT: Shows count values only, like until now
                                            //PERCENT: Shows only percentage of total of all values shown in that group level of the chart
                                            //COUNTPERCENT: Shows count (<percentage>%) rounded to integer. Example: 27 (44%)
                                            case "COUNT":
                                                break;
                                            //no change
                                            case "PERCENT":
                                                decimal lPercent = Convert.ToDecimal((dCount / RollupTotal) * 100);
                                                string sPercent = goTR.NumToString(lPercent, "1") + "%";
                                                sValue = sPercent;
                                                sValueLabel = sPercent;
                                                break;
                                            case "COUNTPERCENT":
                                                lPercent = Convert.ToDecimal((dCount / RollupTotal) * 100);
                                                sPercent = " (" + goTR.NumToString(lPercent, "1") + "%" + ")";
                                                sValue = sValue + sPercent;
                                                sValueLabel = sValue;
                                                break;
                                        }

                                        sValue = sLabel + ": " + sValue;
                                        IsTooltipVisible = false;
                                    }

                                    //in 6.0  J..
                                    ChartTitleInToolTip = sValue;

                                    if (Xaxis.Count > 0)
                                    {
                                        bool isExist = Xaxis.Contains(sLabel);

                                        if (!isExist)
                                        {
                                            //Xaxis.Add(sValue);
                                            Xaxis.Add(sLabel);
                                        }
                                    }
                                    else
                                    {
                                        //Xaxis.Add(sValue);
                                        Xaxis.Add(sLabel);
                                    }

                                    _clSeries.YAxis.Add(dCount);
                                    //_clSeries.YAxis.Add(sValueLabel);
                                    if (sGraphType == "PIE")
                                    {
                                        _clPieSeries.category = sLabel;
                                        // _clPieSeries.value = dCount.ToString();
                                        _clPieSeries.value = sValueLabel;//.ToString();   // Set Percentage Values - Karthik
                                        _clPieSeries.SeriesName = sSeriesName;
                                        //SB 12-13-2017 Tckt#2009 Feature - colors in Pie Chart WG_Options if ShowPrimaryColors == true
                                        if (ShowPrimaryColors)
                                        {
                                            _clPieSeries.color = Util.GetChartColorField(i, ref G_ColorIndx);
                                        }
                                        PieSeries.Add(_clPieSeries);
                                        _clPieSeries = new Selltis.Core.Chart.clPieSeries();
                                    }

                                    MaxValue = myMaxValue;
                                    if (MaxValue == 0.0)
                                    {
                                        MaxValue = 1.0;
                                    }

                                    //Used Tooltip
                                    if (XaxisValuesUsedInToolTip.Count > 0)
                                    {
                                        bool isExist = XaxisValuesUsedInToolTip.Contains(sLabel);

                                        if (!isExist)
                                        {
                                            XaxisValuesUsedInToolTip.Add(sLabel);
                                        }
                                    }
                                    else
                                    {
                                        XaxisValuesUsedInToolTip.Add(sLabel);
                                    }

                                    break;
                            }
                            #endregion
                            break;
                        case "STATISTICS":
                            #region statistic switch
                            switch (sGraphType)
                            {
                                case "GAUGE":
                                    if (!bWroteThis)
                                        gsGaugeQS = gsGaugeQS + "&ShowLegend=true";
                                    bWroteThis = true;
                                    //2009 Q3: $3,4800 (avg), $100 (min), 2,360 (max), $1,288 (med)
                                    string sValue = "";
                                    string sTT = "";
                                    dynamic _sYval = 0;

                                    switch (x)
                                    {
                                        case 1:
                                            gsGaugeQS = gsGaugeQS + "&NeedleAvgValue=" + dAverage;
                                            GaugePointerValue = dAverage;

                                            switch (Strings.Left(sField, 4))
                                            {
                                                case "CUR_":
                                                    sTT = goTR.CurrToString(Convert.ToDecimal(dAverage), sFormat, ref par_iValid);
                                                    break;
                                                default:
                                                    switch (iDec)
                                                    {
                                                        case 0:
                                                            if (dTotal >= 1000)
                                                            {
                                                                sTT = string.Format("{0:0,0}", dAverage);
                                                            }
                                                            else
                                                            {
                                                                sTT = Convert.ToInt32(dAverage).ToString();
                                                            }
                                                            break;
                                                        default:
                                                            sTT = string.Format("{0:f" + iDec + "}", dAverage);
                                                            break;
                                                    }
                                                    break;
                                            }

                                            switch (sToolTip)
                                            {
                                                case "":
                                                    sToolTip = sLabel + ": " + sTT + " (avg)";
                                                    break;
                                                default:
                                                    sToolTip = sToolTip + ", " + sTT + " (avg)";
                                                    break;
                                            }
                                            SeriesColors.Add("#4682B4");
                                            break;

                                        case 2:
                                            gsGaugeQS = gsGaugeQS + "&NeedleMinValue=" + dMin;
                                            GaugePointerValue = dMin;

                                            switch (Strings.Left(sField, 4))
                                            {
                                                case "CUR_":
                                                    sTT = goTR.CurrToString(Convert.ToDecimal(dMin), sFormat, ref par_iValid);
                                                    break;
                                                default:
                                                    switch (iDec)
                                                    {
                                                        case 0:
                                                            if (dTotal >= 1000)
                                                            {
                                                                sTT = string.Format("{0:0,0}", dMin);
                                                            }
                                                            else
                                                            {
                                                                sTT = Convert.ToInt32(dMin).ToString();
                                                            }
                                                            break;
                                                        default:
                                                            sTT = string.Format("{0:f" + iDec + "}", dMin);
                                                            break;
                                                    }
                                                    break;
                                            }

                                            switch (sToolTip)
                                            {
                                                case "":
                                                    sToolTip = sLabel + ": " + sTT + " (min)";
                                                    break;
                                                default:
                                                    sToolTip = sToolTip + ", " + sTT + " (min)";
                                                    break;
                                            }
                                            SeriesColors.Add("#B22222");
                                            break;

                                        case 3:
                                            gsGaugeQS = gsGaugeQS + "&NeedleMaxValue=" + dMax;
                                            GaugePointerValue = dMax;

                                            switch (Strings.Left(sField, 4))
                                            {
                                                case "CUR_":
                                                    sTT = goTR.CurrToString(Convert.ToDecimal(dMax), sFormat, ref par_iValid);
                                                    break;
                                                default:
                                                    switch (iDec)
                                                    {
                                                        case 0:
                                                            if (dTotal >= 1000)
                                                            {
                                                                sTT = string.Format("{0:0,0}", dMax);
                                                            }
                                                            else
                                                            {
                                                                sTT = Convert.ToInt32(dMax).ToString();
                                                            }
                                                            break;
                                                        default:
                                                            sTT = string.Format("{0:f" + iDec + "}", dMax);
                                                            break;
                                                    }
                                                    break;
                                            }

                                            switch (sToolTip)
                                            {
                                                case "":
                                                    sToolTip = sLabel + ": " + sTT + " (max)";
                                                    break;
                                                default:
                                                    sToolTip = sToolTip + ", " + sTT + " (max)";
                                                    break;
                                            }
                                            SeriesColors.Add("#FF8C00");
                                            break;

                                        case 4:
                                            gsGaugeQS = gsGaugeQS + "&NeedleMedValue=" + dMed;
                                            GaugePointerValue = dMed;

                                            switch (Strings.Left(sField, 4))
                                            {
                                                case "CUR_":
                                                    sTT = goTR.CurrToString(Convert.ToDecimal(dMed), sFormat, ref par_iValid);
                                                    break;
                                                default:
                                                    switch (iDec)
                                                    {
                                                        case 0:
                                                            if (dTotal >= 1000)
                                                            {
                                                                sTT = string.Format("{0:0,0}", dMed);
                                                            }
                                                            else
                                                            {
                                                                sTT = Convert.ToInt32(dMed).ToString();
                                                            }
                                                            break;
                                                        default:
                                                            sTT = string.Format("{0:f" + iDec + "}", dMed);
                                                            break;
                                                    }
                                                    break;
                                            }

                                            switch (sToolTip)
                                            {
                                                case "":
                                                    sToolTip = sLabel + ": " + sTT + " (med)";
                                                    break;
                                                default:
                                                    sToolTip = sToolTip + ", " + sTT + " (med)";
                                                    break;
                                            }
                                            SeriesColors.Add("#808000");
                                            break;

                                    }
                                    ChartTitleInToolTip = sToolTip;
                                    break;

                                default:
                                    _sYval = 0;

                                    if (bIsDTX)
                                    {
                                        if (bNeedStart & _clSeries.YAxis.Count == 0)
                                        {
                                            double dStart = 0.0;
                                            double dEnd = 0.0;
                                            if (!bIsDesc)
                                            {
                                                dStart = GetMinMaxDate("MIN", sXField, dDate);
                                                sStart = System.DateTime.FromOADate(dStart).ToString();

                                                //let's get end date here just in case this is the last point
                                                dEnd = GetMinMaxDate("MAX", sXField, dDate);
                                                sEnd = System.DateTime.FromOADate(dEnd).ToString();
                                            }
                                            else
                                            {
                                                dEnd = GetMinMaxDate("MAX", sXField, dDate);
                                                sEnd = System.DateTime.FromOADate(dEnd).ToString();

                                                //let's get end date here just in case this is the last point
                                                dStart = GetMinMaxDate("MIN", sXField, dDate);
                                                sStart = System.DateTime.FromOADate(dStart).ToString();
                                            }
                                        }
                                        else if (bNeedEnd)
                                        {
                                            double dStart = 0.0;
                                            double dEnd = 0.0;
                                            if (bIsDesc)
                                            {
                                                dStart = GetMinMaxDate("MIN", sXField, dDate);
                                                sStart = System.DateTime.FromOADate(dStart).ToString();
                                            }
                                            else
                                            {
                                                dEnd = GetMinMaxDate("MAX", sXField, dDate);
                                                sEnd = System.DateTime.FromOADate(dEnd).ToString();
                                            }
                                        }
                                    }

                                    //lets check for missing dates
                                    string MissingLabel;
                                    if (bIsDTX & sGraphType != "PIE" & sGraphType != "GAUGE")
                                    {
                                        double dLastDate = Convert.ToDateTime(sStart).ToOADate();
                                        //if (oSeries.Points.Count > 0)
                                        //{
                                        //    dLastDate = oSeries.Points[oSeries.Points.Count - 1].XValue;
                                        //}
                                        if (Xaxis.Count > 0)
                                        {
                                            if ((Xaxis[Xaxis.Count - 1]).ToString().Contains("-"))
                                            {
                                                dLastDate = Convert.ToDateTime(Xaxis[Xaxis.Count - 1]).ToOADate();
                                            }
                                        }
                                        System.Collections.Generic.List<double> oMissing = GetDTxMissingValues(sXField, dLastDate, dDate);
                                        if (oMissing.Count > 0)
                                        {
                                            for (int t = 0; t <= oMissing.Count - 1; t++)
                                            {
                                                MissingLabel = "";
                                                ////let's not show point label for fake data ;)
                                                if (!gbPrintMode)
                                                {
                                                    MissingLabel = GetAxisLabel(oMissing[t], sXField);
                                                    string sMissing = GetAxisLabel(oMissing[t], sXField) + ": No data";
                                                }
                                                //oSeries.Points.Add(oPoint);
                                                Xaxis.Add(MissingLabel);
                                                _clSeries.YAxis.Add(0);
                                            }
                                        }
                                    }

                                    switch (x)
                                    {
                                        case 1:

                                            #region Average in statistic

                                            int iNum = i;
                                            if ((_clSeries.YAxis.Count == 0 & sGraphType.Contains("AREA")))
                                            {
                                                iNum = 1;
                                            }
                                            if ((iCount == 1 & sGraphType == "LINE"))
                                            {
                                                iNum = 2;
                                            }

                                            if (dAverage > myMaxValue)
                                                myMaxValue = dAverage;

                                            //formatting
                                            sValue = "";
                                            switch (Strings.Left(sField, 4))
                                            {
                                                case "CUR_":
                                                    sValue = goTR.CurrToString(Convert.ToDecimal(dAverage), sFormat, ref par_iValid);
                                                    break;
                                                default:
                                                    switch (iDec)
                                                    {
                                                        case 0:
                                                            if (dAverage >= 1000)
                                                            {
                                                                sValue = string.Format("{0:0,0}", dAverage);
                                                            }
                                                            else
                                                            {
                                                                sValue = Convert.ToInt32(dAverage).ToString();
                                                            }
                                                            break;
                                                        default:
                                                            sValue = string.Format("{0:f" + iDec + "}", dAverage);
                                                            break;
                                                    }
                                                    break;
                                                    //do no formatting
                                            }

                                            if (!bIsDTX)
                                            {
                                                if (sLabel.Length > 14)
                                                {
                                                    //oPoint.AxisLabel = Strings.Left(sLabel, 14);
                                                    //oPoint.LegendText = Strings.Left(sLabel, 14);
                                                }
                                                else
                                                {
                                                    //oPoint.AxisLabel = sLabel;
                                                    //oPoint.LegendText = sLabel;
                                                }
                                            }

                                            if (sGraphType == "PIE")
                                            {
                                                if (sLabel.Length > 14)
                                                {
                                                    //oPoint.LegendText = Strings.Left(sLabel, 14);
                                                }
                                                else
                                                {
                                                    //oPoint.LegendText = sLabel;
                                                }
                                                if ((oSeries.Points.Count == 0))
                                                {
                                                    //oPoint.MarkerColor = System.Drawing.Color.Green;
                                                }
                                            }

                                            if (sLabel.Length > 28)
                                                sLabel = Strings.Left(sLabel, 25) + "...";

                                            if (iSortField < iGraphXSortFields & !gbPrintMode)
                                            {
                                                if (Strings.UCase(sLabel) == "<MAKE SELECTION>")
                                                {
                                                    sValue = HttpUtility.UrlEncode("MAKESELECTION: " + sValue);
                                                }
                                                else if (Strings.UCase(sLabel) == "<DON'T KNOW>")
                                                {
                                                    sValue = HttpUtility.UrlEncode("DONTKNOW: " + sValue);
                                                }
                                                else
                                                {
                                                    sLabel = Strings.Replace(sLabel, "<", "SELLLESSTHAN");
                                                    sLabel = Strings.Replace(sLabel, ">", "SELLGREATERTHAN");
                                                    sLabel = Strings.Replace(sLabel, "'", "SELLSINGLEQUOTE");
                                                    sValue = sLabel + ": " + sValue;
                                                }
                                            }
                                            else if (!gbPrintMode)
                                            {
                                                sValue = sLabel + ": " + sValue;
                                                IsTooltipVisible = false;
                                            }

                                            _sYval = dAverage;
                                            ChartTitleInToolTip = sValue;


                                            break;
                                        #endregion

                                        case 2:

                                            #region Minimum in statistic

                                            iNum = i;
                                            if ((_clSeries.YAxis.Count == 0 & sGraphType.Contains("AREA")))
                                            {
                                                iNum = 1;
                                            }
                                            if ((iCount == 1 & sGraphType == "LINE"))
                                            {
                                                iNum = 2;
                                            }

                                            if (dMin > myMaxValue)
                                                myMaxValue = dMin;

                                            //formatting
                                            sValue = "";
                                            switch (Strings.Left(sField, 4))
                                            {
                                                case "CUR_":
                                                    sValue = goTR.CurrToString(Convert.ToDecimal(dMin), sFormat, ref par_iValid);
                                                    break;
                                                default:
                                                    switch (iDec)
                                                    {
                                                        case 0:
                                                            if (dMin >= 1000)
                                                            {
                                                                sValue = string.Format("{0:0,0}", dMin);
                                                            }
                                                            else
                                                            {
                                                                sValue = Convert.ToInt32(dMin).ToString();
                                                            }
                                                            break;
                                                        default:
                                                            sValue = string.Format("{0:f" + iDec + "}", dMin);
                                                            break;
                                                    }
                                                    break;
                                            }

                                            if (!bIsDTX)
                                            {
                                                if (sLabel.Length > 14)
                                                {
                                                    //oPoint.AxisLabel = Strings.Left(sLabel, 14);
                                                    //oPoint.LegendText = Strings.Left(sLabel, 14);
                                                }
                                                else
                                                {
                                                    //oPoint.AxisLabel = sLabel;
                                                    //oPoint.LegendText = sLabel;
                                                }
                                            }

                                            if (sGraphType == "PIE")
                                            {
                                                if (sLabel.Length > 14)
                                                {
                                                    //oPoint.LegendText = Strings.Left(sLabel, 14);
                                                }
                                                else
                                                {
                                                    //oPoint.LegendText = sLabel;
                                                }
                                                if ((oSeries.Points.Count == 0))
                                                {
                                                    //oPoint.MarkerColor = System.Drawing.Color.Green;
                                                }
                                            }


                                            if (sLabel.Length > 28)
                                                sLabel = Strings.Left(sLabel, 25) + "...";

                                            if (iSortField < iGraphXSortFields & !gbPrintMode)
                                            {
                                                if (Strings.UCase(sLabel) == "<MAKE SELECTION>")
                                                {
                                                    sValue = "MAKESELECTION: " + sValue;
                                                    sLabel = Strings.Replace(sLabel, "<", "-");
                                                    sLabel = Strings.Replace(sLabel, ">", "-");
                                                }
                                                else if (Strings.UCase(sLabel) == "<DON'T KNOW>")
                                                {
                                                    sValue = "DONTKNOW: " + sValue;
                                                    sLabel = Strings.Replace(sLabel, "<", "-");
                                                    sLabel = Strings.Replace(sLabel, ">", "-");
                                                }
                                                else
                                                {
                                                    sLabel = Strings.Replace(sLabel, "<", "SELLLESSTHAN");
                                                    sLabel = Strings.Replace(sLabel, ">", "SELLGREATERTHAN");
                                                    sLabel = Strings.Replace(sLabel, "'", "SELLSINGLEQUOTE");
                                                    sValue = sLabel + ": " + sValue;
                                                }

                                            }
                                            else if (!gbPrintMode)
                                            {
                                                sValue = sLabel + ": " + sValue;
                                                IsTooltipVisible = false;
                                            }

                                            _sYval = dMin;
                                            ChartTitleInToolTip = sValue;


                                            break;
                                        #endregion

                                        case 3:

                                            #region Maximum in statistic

                                            iNum = i;
                                            if ((_clSeries.YAxis.Count == 0 & sGraphType.Contains("AREA")))
                                            {
                                                iNum = 1;
                                            }
                                            if ((iCount == 1 & sGraphType == "LINE"))
                                            {
                                                iNum = 2;
                                            }

                                            if (dMax > myMaxValue)
                                                myMaxValue = dMax;

                                            //formatting
                                            sValue = "";
                                            switch (Strings.Left(sField, 4))
                                            {
                                                case "CUR_":
                                                    sValue = goTR.CurrToString(Convert.ToDecimal(dMax), sFormat, ref par_iValid);
                                                    break;
                                                default:
                                                    switch (iDec)
                                                    {
                                                        case 0:
                                                            if (dMax >= 1000)
                                                            {
                                                                sValue = string.Format("{0:0,0}", dMax);
                                                            }
                                                            else
                                                            {
                                                                sValue = Convert.ToInt32(dMax).ToString();
                                                            }
                                                            break;
                                                        //sValue = CInt(dMax)
                                                        default:
                                                            sValue = string.Format("{0:f" + iDec + "}", dMax);
                                                            break;
                                                    }
                                                    break;
                                                    //do no formatting
                                            }

                                            if (!bIsDTX)
                                            {
                                                if (sLabel.Length > 14)
                                                {
                                                    //oPoint.AxisLabel = Strings.Left(sLabel, 14);
                                                    //oPoint.LegendText = Strings.Left(sLabel, 14);
                                                }
                                                else
                                                {
                                                    //oPoint.AxisLabel = sLabel;
                                                    //oPoint.LegendText = sLabel;
                                                }
                                            }

                                            if (sGraphType == "PIE")
                                            {
                                                if (sLabel.Length > 14)
                                                {
                                                    //oPoint.LegendText = Strings.Left(sLabel, 14);
                                                }
                                                else
                                                {
                                                    //oPoint.LegendText = sLabel;
                                                }
                                                if ((oSeries.Points.Count == 0))
                                                {
                                                    //oPoint.MarkerColor = System.Drawing.Color.Green;
                                                }

                                            }

                                            if (sLabel.Length > 28)
                                                sLabel = Strings.Left(sLabel, 25) + "...";

                                            if (iSortField < iGraphXSortFields & !gbPrintMode)
                                            {
                                                if (Strings.UCase(sLabel) == "<MAKE SELECTION>")
                                                {
                                                    sValue = "MAKESELECTION: " + sValue;
                                                    sLabel = Strings.Replace(sLabel, "<", "-");
                                                    sLabel = Strings.Replace(sLabel, ">", "-");
                                                }
                                                else if (Strings.UCase(sLabel) == "<DON'T KNOW>")
                                                {
                                                    sValue = "DONTKNOW: " + sValue;
                                                    sLabel = Strings.Replace(sLabel, "<", "-");
                                                    sLabel = Strings.Replace(sLabel, ">", "-");
                                                }
                                                else
                                                {
                                                    sLabel = Strings.Replace(sLabel, "<", "SELLLESSTHAN");
                                                    sLabel = Strings.Replace(sLabel, ">", "SELLGREATERTHAN");
                                                    sLabel = Strings.Replace(sLabel, "'", "SELLSINGLEQUOTE");
                                                    sValue = sLabel + ": " + sValue;
                                                }
                                            }
                                            else if (!gbPrintMode)
                                            {
                                                sValue = sLabel + ": " + sValue;
                                                IsTooltipVisible = false;
                                            }

                                            _sYval = dMax;
                                            ChartTitleInToolTip = sValue;


                                            break;
                                        #endregion

                                        case 4:

                                            #region Median in statistic

                                            iNum = i;
                                            if ((_clSeries.YAxis.Count == 0 & sGraphType.Contains("AREA")))
                                            {
                                                iNum = 1;
                                            }
                                            if ((iCount == 1 & sGraphType == "LINE"))
                                            {
                                                iNum = 2;
                                            }

                                            if (dMed > myMaxValue)
                                                myMaxValue = dMed;

                                            //formatting
                                            sValue = "";
                                            switch (Strings.Left(sField, 4))
                                            {
                                                case "CUR_":
                                                    sValue = goTR.CurrToString(Convert.ToDecimal(dMed), sFormat, ref par_iValid);
                                                    break;
                                                default:
                                                    switch (iDec)
                                                    {
                                                        case 0:
                                                            if (dMed >= 1000)
                                                            {
                                                                sValue = string.Format("{0:0,0}", dMed);
                                                            }
                                                            else
                                                            {
                                                                sValue = Convert.ToInt32(dMed).ToString();
                                                            }
                                                            break;
                                                        default:
                                                            sValue = string.Format("{0:f" + iDec + "}", dMed);
                                                            break;
                                                    }
                                                    break;
                                            }

                                            if (!bIsDTX)
                                            {
                                                if (sLabel.Length > 14)
                                                {
                                                    //oPoint.AxisLabel = Strings.Left(sLabel, 14);
                                                    //oPoint.LegendText = Strings.Left(sLabel, 14);
                                                }
                                                else
                                                {
                                                    //oPoint.AxisLabel = sLabel;
                                                    //oPoint.LegendText = sLabel;
                                                }
                                            }

                                            if (sGraphType == "PIE")
                                            {
                                                if (sLabel.Length > 14)
                                                {
                                                    //oPoint.LegendText = Strings.Left(sLabel, 14);
                                                }
                                                else
                                                {
                                                    //oPoint.LegendText = sLabel;
                                                }
                                                if ((oSeries.Points.Count == 0))
                                                {
                                                    //oPoint.MarkerColor = System.Drawing.Color.Green;
                                                }
                                            }

                                            if (sLabel.Length > 28)
                                                sLabel = Strings.Left(sLabel, 25) + "...";

                                            if (iSortField < iGraphXSortFields & !gbPrintMode)
                                            {
                                                if (Strings.UCase(sLabel) == "<MAKE SELECTION>")
                                                {
                                                    sValue = "MAKESELECTION: " + sValue;
                                                    sLabel = Strings.Replace(sLabel, "<", "-");
                                                    sLabel = Strings.Replace(sLabel, ">", "-");
                                                }
                                                else if (Strings.UCase(sLabel) == "<DON'T KNOW>")
                                                {
                                                    sValue = "DONTKNOW: " + sValue;
                                                    sLabel = Strings.Replace(sLabel, "<", "-");
                                                    sLabel = Strings.Replace(sLabel, ">", "-");
                                                }
                                                else
                                                {
                                                    sLabel = Strings.Replace(sLabel, "<", "SELLLESSTHAN");
                                                    sLabel = Strings.Replace(sLabel, ">", "SELLGREATERTHAN");
                                                    sLabel = Strings.Replace(sLabel, "'", "SELLSINGLEQUOTE");
                                                    sValue = sLabel + ": " + sValue;
                                                }

                                            }
                                            else if (!gbPrintMode)
                                            {
                                                sValue = sLabel + ": " + sValue;
                                                IsTooltipVisible = false;
                                            }

                                            _sYval = dMed;
                                            ChartTitleInToolTip = sValue;


                                            break;
                                            #endregion

                                    }

                                    //in 6.0  J..
                                    if (Xaxis.Count > 0)
                                    {
                                        bool isExist = Xaxis.Contains(sLabel);

                                        if (!isExist)
                                        {
                                            Xaxis.Add(sLabel);
                                        }
                                    }
                                    else
                                    {
                                        Xaxis.Add(sLabel);
                                    }

                                    _clSeries.SeriesName = oSeries.Name;
                                    _clSeries.YAxis.Add(_sYval);
                                    if (sGraphType == "PIE")
                                    {
                                        _clPieSeries.category = sLabel;
                                        _clPieSeries.value = _sYval;//_sYval.ToString();
                                        _clPieSeries.SeriesName = oSeries.Name;
                                        //SB 12-13-2017 Tckt#2009 Feature - colors in Pie Chart WG_Options if ShowPrimaryColors == true
                                        if (ShowPrimaryColors)
                                        {
                                            _clPieSeries.color = Util.GetChartColorField(i, ref G_ColorIndx);
                                        }
                                        PieSeries.Add(_clPieSeries);
                                        _clPieSeries = new Selltis.Core.Chart.clPieSeries();
                                    }

                                    MaxValue = myMaxValue;

                                    //Used Tooltip
                                    if (XaxisValuesUsedInToolTip.Count > 0)
                                    {
                                        bool isExist = XaxisValuesUsedInToolTip.Contains(sLabel);

                                        if (!isExist)
                                        {
                                            XaxisValuesUsedInToolTip.Add(sLabel);
                                        }
                                    }
                                    else
                                    {
                                        XaxisValuesUsedInToolTip.Add(sLabel);
                                    }

                                    break;
                            }
                            #endregion
                            break;
                        case "TOTAL":
                            #region total switch
                            switch (sGraphType)
                            {
                                case "GAUGE":
                                    gsGaugeQS = gsGaugeQS + "&NeedleValue=" + dTotal;

                                    GaugePointerValue = dTotal;

                                    string sValue = "";
                                    switch (Strings.Left(sField, 4))
                                    {
                                        case "CUR_":
                                            sValue = goTR.CurrToString(Convert.ToDecimal(dTotal), sFormat, ref par_iValid, "");
                                            break;
                                        default:
                                            switch (iDec)
                                            {
                                                case 0:
                                                    if (dTotal >= 1000)
                                                    {
                                                        sValue = string.Format("{0:0,0}", dTotal);
                                                    }
                                                    else
                                                    {
                                                        sValue = Convert.ToInt32(dTotal).ToString();
                                                    }
                                                    break;
                                                default:
                                                    sValue = string.Format("{0:f" + iDec + "}", dTotal);
                                                    break;
                                            }
                                            break;
                                    }

                                    sToolTip = sLabel + ": " + sValue;
                                    ChartTitleInToolTip = sToolTip;
                                    break;
                                default:

                                    if (bIsDTX)
                                    {
                                        if (bNeedStart && _clSeries.YAxis.Count == 0)
                                        {
                                            double dStart = 0.0;
                                            double dEnd = 0.0;
                                            if (!bIsDesc)
                                            {
                                                dStart = GetMinMaxDate("MIN", sXField, dDate);
                                                sStart = System.DateTime.FromOADate(dStart).ToString();

                                                //let's get end date here just in case this is the last point
                                                dEnd = GetMinMaxDate("MAX", sXField, dDate);
                                                sEnd = System.DateTime.FromOADate(dEnd).ToString();
                                            }
                                            else
                                            {
                                                dEnd = GetMinMaxDate("MAX", sXField, dDate);
                                                sEnd = System.DateTime.FromOADate(dEnd).ToString();

                                                //let's get end date here just in case this is the last point
                                                dStart = GetMinMaxDate("MIN", sXField, dDate);
                                                sStart = System.DateTime.FromOADate(dStart).ToString();
                                            }
                                        }
                                        else if (bNeedEnd)
                                        {
                                            double dStart = 0.0;
                                            double dEnd = 0.0;
                                            if (bIsDesc)
                                            {
                                                dStart = GetMinMaxDate("MIN", sXField, dDate);
                                                sStart = System.DateTime.FromOADate(dStart).ToString();
                                            }
                                            else
                                            {
                                                dEnd = GetMinMaxDate("MAX", sXField, dDate);
                                                sEnd = System.DateTime.FromOADate(dEnd).ToString();
                                            }
                                        }
                                    }

                                    //lets check for missing dates
                                    string MissingLabel;
                                    if (bIsDTX & sGraphType != "PIE" & sGraphType != "GAUGE")
                                    {
                                        double dLastDate = Convert.ToDateTime(sStart).ToOADate();
                                        //if (oSeries.Points.Count > 0)
                                        //{
                                        //    dLastDate = oSeries.Points[oSeries.Points.Count - 1].XValue;
                                        //}
                                        if (Xaxis.Count > 0)
                                        {
                                            if ((Xaxis[Xaxis.Count - 1]).ToString().Contains("-"))
                                            {
                                                dLastDate = Convert.ToDateTime(Xaxis[Xaxis.Count - 1]).ToOADate();
                                            }
                                        }
                                        System.Collections.Generic.List<double> oMissing = GetDTxMissingValues(sXField, dLastDate, dDate);
                                        if (oMissing.Count > 0)
                                        {
                                            for (int t = 0; t <= oMissing.Count - 1; t++)
                                            {
                                                MissingLabel = "";
                                                ////let's not show point label for fake data ;)
                                                if (!gbPrintMode)
                                                {
                                                    MissingLabel = GetAxisLabel(oMissing[t], sXField);
                                                    string sMissing = GetAxisLabel(oMissing[t], sXField) + ": No data";
                                                }
                                                //oSeries.Points.Add(oPoint);
                                                //Xaxis.Add(MissingLabel);
                                                if (Xaxis.Count > 0)
                                                {
                                                    bool isExist = Xaxis.Contains(MissingLabel);

                                                    if (!isExist)
                                                    {
                                                        Xaxis.Add(MissingLabel);
                                                    }
                                                }
                                                else
                                                {
                                                    Xaxis.Add(MissingLabel);
                                                }

                                                _clSeries.YAxis.Add(0);
                                            }
                                        }
                                    }

                                    int iNum = i;
                                    if ((_clSeries.YAxis.Count == 0 & sGraphType.Contains("AREA")))
                                    {
                                        iNum = 1;
                                    }
                                    if ((iCount == 1 & sGraphType == "LINE"))
                                    {
                                        iNum = 2;
                                    }

                                    if (dTotal > myMaxValue)
                                        myMaxValue = dTotal;

                                    //formatting
                                    sValue = "";
                                    switch (Strings.Left(sField, 4))
                                    {
                                        case "CUR_":
                                            sValue = goTR.CurrToString(Convert.ToDecimal(dTotal), sFormat, ref par_iValid, "");
                                            break;
                                        default:
                                            switch (iDec)
                                            {
                                                case 0:
                                                    if (dTotal >= 1000)
                                                    {
                                                        sValue = string.Format("{0:0,0}", dTotal);
                                                    }
                                                    else
                                                    {
                                                        sValue = Convert.ToInt32(dTotal).ToString();
                                                    }
                                                    break;
                                                default:
                                                    sValue = string.Format("{0:f" + iDec + "}", dTotal);
                                                    break;
                                            }
                                            break;
                                    }

                                    if (!bIsDTX)
                                    {
                                        if (sLabel.Length > 14)
                                        {
                                            //oPoint.AxisLabel = Strings.Left(sLabel, 14);
                                            //oPoint.LegendText = Strings.Left(sLabel, 14);
                                        }
                                        else
                                        {
                                            //oPoint.AxisLabel = sLabel;
                                            //oPoint.LegendText = sLabel;
                                        }
                                    }

                                    if (sGraphType == "PIE")
                                    {
                                        if (sLabel.Length > 14)
                                        {
                                            //oPoint.LegendText = Strings.Left(sLabel, 14);
                                        }
                                        else
                                        {
                                            //oPoint.LegendText = sLabel;
                                        }
                                        if ((_clSeries.YAxis.Count == 0))
                                        {
                                            //oPoint.MarkerColor = System.Drawing.Color.Green;
                                        }
                                    }

                                    if (sLabel.Length > 28)
                                        sLabel = Strings.Left(sLabel, 25) + "...";

                                    if (iSortField < iGraphXSortFields & !gbPrintMode)
                                    {
                                        if (Strings.UCase(sLabel) == "<MAKE SELECTION>")
                                        {
                                            sValue = "MAKESELECTION: " + sValue;
                                            sLabel = Strings.Replace(sLabel, "<", "-");
                                            sLabel = Strings.Replace(sLabel, ">", "-");
                                        }
                                        else if (Strings.UCase(sLabel) == "<DON'T KNOW>")
                                        {
                                            sValue = "DONTKNOW: " + sValue;
                                            sLabel = Strings.Replace(sLabel, "<", "-");
                                            sLabel = Strings.Replace(sLabel, ">", "-");
                                        }
                                        else
                                        {
                                            sLabel = Strings.Replace(sLabel, "<", "SELLLESSTHAN");
                                            sLabel = Strings.Replace(sLabel, ">", "SELLGREATERTHAN");
                                            sLabel = Strings.Replace(sLabel, "'", "SELLSINGLEQUOTE");
                                            sValue = sLabel + ": " + sValue;
                                        }
                                    }
                                    else if (!gbPrintMode)
                                    {
                                        sValue = sLabel + ": " + sValue;
                                        IsTooltipVisible = false;
                                    }


                                    //in 6.0  J..
                                    ChartTitleInToolTip = sValue;

                                    if (Xaxis.Count > 0)
                                    {
                                        bool isExist = Xaxis.Contains(sLabel);

                                        if (!isExist)
                                        {
                                            Xaxis.Add(sLabel);
                                        }
                                    }
                                    else
                                    {
                                        Xaxis.Add(sLabel);
                                    }

                                    _clSeries.YAxis.Add(dTotal);
                                    if (sGraphType == "PIE")
                                    {
                                        _clPieSeries.category = sLabel;
                                        _clPieSeries.value = dTotal; // dTotal.ToString();
                                        _clPieSeries.SeriesName = sSeriesName;
                                        //SB 12-13-2017 Tckt#2009 Feature - colors in Pie Chart WG_Options if ShowPrimaryColors == true
                                        if (ShowPrimaryColors)
                                        {
                                            _clPieSeries.color = Util.GetChartColorField(i, ref G_ColorIndx);
                                        }
                                        PieSeries.Add(_clPieSeries);
                                        _clPieSeries = new clPieSeries();
                                    }

                                    MaxValue = myMaxValue;
                                    if (MaxValue == 0.0)
                                    {
                                        MaxValue = 1.0;
                                    }

                                    //Used Tooltip
                                    if (XaxisValuesUsedInToolTip.Count > 0)
                                    {
                                        bool isExist = XaxisValuesUsedInToolTip.Contains(sLabel);

                                        if (!isExist)
                                        {
                                            XaxisValuesUsedInToolTip.Add(sLabel);
                                        }
                                    }
                                    else
                                    {
                                        XaxisValuesUsedInToolTip.Add(sLabel);
                                    }

                                    break;
                            }
                            #endregion
                            break;
                    }

                }

                if (sGraphType == "GAUGE" & iGaugePoints == 1)
                    break;
                //goto GetOutForGauge;
                SkipMergedRecord: continue;

            }
            //GetOutForGauge: continue;

            switch (sGraphType)
            {
                case "GAUGE":
                    break;
                default:
                    //Add series into the chart's series collection                       
                    Series.Add(_clSeries);
                    _clSeries = new clSeries();
                    break;
            }

            //if (iGaugePoints == 0)
            //{
            //    gbHideGauge = true;
            //    oChartVisible = false;
            //    lblNoChartText = "No chartable data found.";
            //}


            if (bIsDTX)
            {
                switch (sGraphType)
                {
                    case "GAUGE":
                        if (iGaugePoints == 0)
                        {
                            //gbHideGauge = true;
                            //oChartVisible = false;
                            //lblNoChartText = "No chartable data found.";
                        }
                        break;
                    default:
                        if (iCount > 0 & Series[0].YAxis.Count == 0)
                        {
                            //gbHideGauge = true;
                            //oChartVisible = false;
                            //lblNoChartText = "No chartable data found.";
                        }
                        else if (iCount != Series[0].YAxis.Count)
                        {
                            lblSomeBlankDataVisible = true;
                        }
                        break;
                }
            }

            //if (sGraphType != "GAUGE" & oChartVisible == false)
            //    return;

            GetNext:

            iDataPointLabel = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHSHOWDATAPOINTVALUE", "0", false), "", ref par_iValid, ""));
            iCount = GetChartDataPointCount(sField, iSortField, iGraph1Index, iGraph2Index, iGraph3Index);

            x = x + 1;
            if (sGraphYShow == "STATISTICS" & x <= 4)
            {
                goto RedoThisForStatistics;
            }

            if (sGraphType != "GAUGE")
            {
                if (sGraphYShow == "TOTAL" & x <= 4)
                {
                    if (x == 2)
                        sField = sGraphYField2;
                    if (x == 3)
                        sField = sGraphYField3;
                    if (x == 4)
                        sField = sGraphYField4;
                    if (sField != "NONE" & !string.IsNullOrEmpty(sField))
                        goto RedoThisForMultipleYs;
                }
            }


            #region SetGoalLine

            //int svalid;
            bool IsshowGoleline = false;
            double _GolelineVal = 0;

            //switch (sGraphType)
            //{
            //    case "BAR":
            //    case "STACKEDBAR":
            //    case "PIE":
            //        // No gole line
            //        break;
            //    default:
            //        int iLine = int.Parse(goTR.StrRead(_viewMetaData, "GRAPHGOALLINE", "0", false));
            //        string sTemp = goTR.StrRead(_viewMetaData, "GRAPHGOALSORT" + iSortField, "-2", false);  // '-1 is default value for now
            //        svalid = 0;
            //        var iTemp = goTR.StringToNum(sTemp, iGreatestDec.ToString(), ref svalid);
            //        IsshowGoleline = false;
            //        if (iLine == 1)
            //        {
            //            if (iTemp < -1)
            //            {
            //                string pmsg = "";
            //                IsshowGoleline = true;
            //                iTemp = double.Parse(godata.GetGoalValue(_viewMetaData, goTR.StrRead(_viewMetaData, "FILTER", goTR.StrRead(_viewMetaData, "CONDITION", "")), Strings.Replace(Strings.Replace(Strings.Split(Sorts[iSortField].ToString(), Strings.Chr(1).ToString())[0], "<%", ""), "%>", ""), "VIEW", ref pmsg).ToString());
            //            }
            //            else if (iTemp == -1)
            //            {
            //                IsshowGoleline = false;
            //                //don't display goleline
            //            }
            //            else if (iTemp >= 0)
            //            {
            //                IsshowGoleline = true;
            //            }

            //        }
            //        _GolelineVal = iTemp;
            //        break;
            //}
            //ShowGoleLine = IsshowGoleline;
            //GoleLineValue = _GolelineVal;


            switch (sGraphType)
            {
                case "BAR":
                case "STACKEDBAR":
                case "PIE":
                    break;
                //NO GOAL                   

                default:

                    //decimals, use the field with the most decimals                  
                    if (iGraph0Decimals == 1)
                    {
                        iGreatestDec = 0;
                    }

                    int iLine = Convert.ToInt32(goTR.StrRead(_viewMetaData, "GRAPHGOALLINE", "0", false));

                    string sTemp = goTR.StrRead(_viewMetaData, "GRAPHGOALSORT" + iSortField, "-2", false);
                    //-1 is default value for now
                    double iTemp = Convert.ToInt32(goTR.StringToNum(sTemp, iGreatestDec.ToString(), ref par_iValid));
                    IsshowGoleline = false;
                    if (iLine == 1)
                    {
                        if (Convert.ToInt32(iTemp) < -1)
                        {
                            string sGoalInfoMessage = "";

                            // goal line is not placed in correct place issue fixed - RN
                            //iTemp = Convert.ToDouble(godata.GetGoalValue(_viewMetaData, goTR.StrRead(_viewMetaData, "FILTER", goTR.StrRead(_viewMetaData, "CONDITION", "")), Strings.Replace(Strings.Replace(Strings.Split(Sorts[iSortField].ToString(), Strings.Chr(1).ToString())[0], "<%", ""), "%>", ""), "VIEW", ref sGoalInfoMessage));
                            iTemp = Convert.ToDouble(godata.GetGoalValue(_viewMetaData, UpdatedViewCondition, Strings.Replace(Strings.Replace(Strings.Split(Sorts[iSortField].ToString(), Strings.Chr(1).ToString())[0], "<%", ""), "%>", ""), "VIEW", ref sGoalInfoMessage));

                            iTemp = Math.Round(iTemp, 0);

                            //VS 07082015 : Display Goal filter message
                            if (iTemp >= 0)
                            {
                                if (!string.IsNullOrEmpty(sGoalInfoMessage))
                                {
                                    IsshowGoleline = true;
                                    //imgGoalFilterInfo.ToolTip = sGoalInfoMessage;
                                    lblGoalFilterInfoToolTip = sGoalInfoMessage;
                                    lblGoalFilterInfoText = "Some view filters have been ignored in Goal calculation";
                                }
                            }
                        }
                        else if (Convert.ToInt32(iTemp) == -1)
                        {
                            IsshowGoleline = false;
                            goto GetOutGoalLine;
                        }
                        else if (Convert.ToInt32(iTemp) == 0)
                        {
                            IsshowGoleline = true;
                            iTemp = iTemp;
                        }

                    }

                    //set Goalsort field also when it is selected in filter properties..J
                    if (iTemp >= 0)
                    {
                        IsshowGoleline = true;
                    }

                    if (iTemp > -1)
                    {

                        if (iTemp > myMaxValue)
                            myMaxValue = iTemp;

                        MaxValue = myMaxValue;
                        if (MaxValue == 0.0)
                        {
                            MaxValue = 1.0;
                        }

                        switch (sGraphType)
                        {
                            case "GAUGE":
                                break;
                            default:
                                IsLegendEnabled = true;
                                //oChart.ChartAreas("ChartArea1").AxisX.IsMarginVisible = false;
                                break;
                        }

                        string sValue = "";
                        if (gbAllCurr)
                        {
                            sValue = goTR.CurrToString(Convert.ToDecimal(iTemp), iGreatestDec.ToString(), ref par_iValid);
                        }
                        else
                        {
                            switch (iGreatestDec)
                            {
                                case 0:
                                    if (iTemp >= 1000)
                                    {
                                        sValue = string.Format("{0:0,0}", iTemp);
                                    }
                                    else
                                    {
                                        sValue = Convert.ToInt32(iTemp).ToString();
                                    }
                                    break;
                                default:
                                    sValue = string.Format("{0:n}", iTemp);
                                    break;
                            }
                        }

                        //plot value
                        string sPlotValue = "0";
                        switch (iGreatestDec)
                        {
                            case 0:
                                if (iTemp >= 1000)
                                {
                                    sPlotValue = string.Format("{0:0,0}", iTemp);
                                }
                                else
                                {
                                    sPlotValue = Convert.ToInt32(iTemp).ToString();
                                }
                                break;
                            default:
                                sPlotValue = string.Format("{0:n}", iTemp);
                                break;
                        }

                        switch (sGraphType)
                        {
                            case "GAUGE":
                                gsGaugeQS = gsGaugeQS + "&Goal=" + goTR.StringToNum(sPlotValue, "", ref par_iValid);
                                break;
                            default:

                                //Add line series
                                Series oGoalLine = new Series("Goal");
                                oGoalLine.ChartType = SeriesChartType.Line;
                                oGoalLine.Color = System.Drawing.Color.DarkRed;
                                oGoalLine.BorderDashStyle = ChartDashStyle.Dash;

                                if (bIsDTX)
                                {
                                    if (Information.IsDate(sStart))
                                    {
                                        oGoalLine.Points.AddXY(Convert.ToDateTime(sStart).ToOADate(), goTR.StringToNum(sPlotValue, "", ref par_iValid));
                                    }
                                    else
                                    {
                                        //double dStart = oChart.Series[0].Points[0].XValue;
                                        //oGoalLine.Points.AddXY(dStart, goTR.StringToNum(sPlotValue,"",ref par_iValid));
                                    }
                                    if (Information.IsDate(sEnd))
                                    {
                                        oGoalLine.Points.AddXY(Convert.ToDateTime(sEnd).ToOADate(), goTR.StringToNum(sPlotValue, "", ref par_iValid));
                                    }
                                    else
                                    {
                                        //double dEnd = oChart.Series[0].Points(oChart.Series[0].Points.Count - 1).XValue;
                                        //oGoalLine.Points.AddXY(dEnd, goTR.StringToNum(sPlotValue,"",ref par_iValid));
                                    }

                                    oGoalLine.XValueType = ChartValueType.DateTime;

                                }
                                else
                                {

                                    DataPoint oPoint = new DataPoint(0, goTR.StringToNum(sPlotValue, "", ref par_iValid));
                                    oPoint.AxisLabel = " ";
                                    //if (Session["SA_DEVICE"] != "IPAD")
                                    //    oPoint.MapAreaAttributes = "onmouseover=\"DisplayTooltip('" + sToolTip + "','" + this.ClientID + "');\" onmouseout=\"DisplayTooltip('','');\"";
                                    if (iDataPointLabel == 1)
                                    {
                                        oPoint.IsValueShownAsLabel = true;
                                        string sLabel = "";
                                        sLabel = "Goal: " + sValue;
                                        int iLen = Strings.Len(sLabel);
                                        int val = Convert.ToInt32((iLen * 2) + 15);
                                        sLabel = sLabel.PadLeft(val);
                                        oPoint.Font = new System.Drawing.Font("Verdana", (giBaseFontSize - 1), System.Drawing.FontStyle.Bold);
                                        oPoint.Label = sLabel;
                                    }

                                    oGoalLine.Points.Add(oPoint);

                                    switch (sGraphType)
                                    {
                                        case "AREA":
                                        case "STACKEDAREA":
                                            oPoint = new DataPoint(iCount + 1, goTR.StringToNum(sPlotValue, "", ref par_iValid));
                                            break;
                                        default:
                                            //oPoint = new DataPoint(oChart.Series[0].Points.Count + 1, goTR.StringToNum(sPlotValue,"",ref par_iValid));
                                            break;
                                    }
                                    oPoint.AxisLabel = " ";

                                    oGoalLine.Points.Add(oPoint);

                                }

                                oGoalLine.SmartLabelStyle.Enabled = true;
                                oGoalLine.IsValueShownAsLabel = false;

                                break;

                        }
                    }
                    _GolelineVal = iTemp;

                    break;
            }

            ShowGoleLine = IsshowGoleline;
            GoleLineValue = _GolelineVal;

            if (ShowGoleLine)
            {
                if (sGraphType != "GAUGE")
                {
                    clSeries _newGoalSeries = new clSeries();
                    _newGoalSeries.ChartType = ChartType.Line;
                    _newGoalSeries.Color = "#8B0000";
                    _newGoalSeries.SeriesName = "Goal";
                    //_newGoalSeries.YAxis = GoleLineValue;

                    int _count = Series[0].YAxis.Count;
                    _newGoalSeries.YAxis = new ArrayList();
                    for (int k = 0; k < _count; k++)
                    {
                        _newGoalSeries.YAxis.Add(GoleLineValue);
                    }
                    Series.Add(_newGoalSeries);
                }
            }

            GetOutGoalLine:

            //iDataPointLabel = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHSHOWDATAPOINTVALUE", "0", false), "", ref par_iValid, ""));                



            #endregion



            switch (sGraphType)
            {
                case "GAUGE":
                    //lblInstruct.Visible = false;
                    if (gbHideGauge == false)
                    {
                        if (iDataPointLabel == 1)
                        {
                            gsGaugeQS = gsGaugeQS + "&ShowDatapointLabels=true";
                        }
                        if (gbAllCurr)
                        {
                            gsGaugeQS = gsGaugeQS + "&Currency=true";
                            sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName.ToUpper(), sField.ToUpper() + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                            gsGaugeQS = gsGaugeQS + "&Format=" + sFormat;
                        }
                        if (iDec != 0)
                        {
                            gsGaugeQS = gsGaugeQS + "&Dec=" + iDec;
                        }
                        //System.Web.UI.WebControls.Image oImage = new System.Web.UI.WebControls.Image();
                        //oImage.ImageUrl = "NeedleGauge.aspx" + gsGaugeQS;


                        //pnlGauge.Visible = true;
                        //pnlGauge.Controls.Add(oImage);

                    }
                    //GetGaugeProperties();

                    int iTickLength = 20;
                    int iArchWidth = 20;
                    if (ChartHeight < 75)
                    {
                        iTickLength = 6;
                        iArchWidth = 10;
                    }
                    else if (ChartHeight < 100)
                    {
                        iTickLength = 12;
                        iArchWidth = 20;
                    }
                    else if (ChartHeight < 200)
                    {
                        iTickLength = 30;
                        iArchWidth = 40;
                    }
                    else if (ChartHeight < 300)
                    {
                        iTickLength = 50;
                        iArchWidth = 60;
                    }
                    GuageTickLength = iTickLength;
                    GuageArchWidth = iArchWidth;

                    //if (GaugePointerValue == 0)
                    //{
                    //    GaugeRange = 5;
                    //}
                    //else
                    //{
                    //    GaugeRange = GaugePointerValue * 2;
                    //}
                    //GaugeRange = Math.Round(GaugeRange, 0, MidpointRounding.ToEven);

                    int iTickDivisor = 10;
                    if (_GolelineVal > 0)
                    {
                        if (GaugePointerValue > _GolelineVal)
                        {
                            GaugeRange = GaugePointerValue + (GaugePointerValue * (10 / 100));
                        }
                        else
                        {
                            GaugeRange = _GolelineVal + (_GolelineVal * ((10 * 2) / 100));
                        }
                    }
                    else
                    {
                        if (GaugePointerValue == 0)
                        {
                            GaugeRange = 5;
                        }
                        else
                        {
                            GaugeRange = GaugePointerValue * 2;
                        }
                    }
                    GaugeRange = Math.Round(GaugeRange, 0, MidpointRounding.ToEven);

                    if (GaugeRange < 5)
                        iTickDivisor = 1;
                    else if (GaugeRange < 10)
                        iTickDivisor = 5;
                    else if (GaugeRange < 100)
                        iTickDivisor = 10;
                    else if (GaugeRange < 500)
                        iTickDivisor = 50;
                    else if (GaugeRange < 1000)
                        iTickDivisor = 100;
                    else if (GaugeRange < 5000)
                        iTickDivisor = 500;
                    else if (GaugeRange < 10000)
                        iTickDivisor = 1000;
                    else if (GaugeRange < 50000)
                        iTickDivisor = 5000;
                    else if (GaugeRange < 100000)
                        iTickDivisor = 10000;
                    else if (GaugeRange < 500000)
                        iTickDivisor = 50000;
                    else if (GaugeRange < 1000000)
                        iTickDivisor = 100000;
                    else if (GaugeRange < 10000000)
                        iTickDivisor = 1000000;
                    else if (GaugeRange < 1000000000)
                        iTickDivisor = 100000000;
                    else if (GaugeRange < 10000000000)
                        iTickDivisor = 1000000000;
                    else
                        iTickDivisor = 1000000000;


                    GaugeRange = Math.Round(GaugeRange / iTickDivisor) * iTickDivisor;
                    if (GaugePointerValue > _GolelineVal)
                    {
                        if (GaugeRange <= GaugePointerValue)
                            GaugeRange = GaugeRange + iTickDivisor;
                    }
                    else
                    {
                        if (GaugeRange <= _GolelineVal)
                            GaugeRange = GaugeRange + iTickDivisor;
                    }


                    GaugeMajorUnit = Convert.ToDouble(iTickDivisor);

                    //scale colors when goal line is exists..
                    if (_GolelineVal > 0)
                    {
                        //double gdSplit = 50;
                        //double dSection1 = _GolelineVal * (gdSplit / 100);
                        //double dSection2 = _GolelineVal * ((100 - gdSplit) / 100);
                        //double dSection3 = GaugeRange - _GolelineVal;

                        double dSection1 = _GolelineVal / 2;
                        double dSection2 = _GolelineVal;
                        double dSection3 = GaugeRange - _GolelineVal;


                        GaugeSection1 = dSection1;
                        GaugeSection2 = dSection2;
                        GaugeSection3 = dSection3;
                    }

                    break;

                default:
                    //FORMAT Y AXIS HASH LABELS IN CASE OF NO DECIMAL FIELDS
                    switch (sGraphYShow)
                    {
                        case "STATISTICS":
                            if (myMaxValue < 5)
                            {
                                switch (Strings.Left(sGraphYField1, 4))
                                {
                                    case "LI__":
                                    case "SI__":
                                    case "BI__":
                                    case "INT_":
                                        //oChart.ChartAreas("ChartArea1").AxisY.Interval = 1;
                                        YaxisInterval = 1;
                                        break;
                                    default:
                                        break;
                                        //NO CHANGE
                                }
                            }
                            break;
                        case "COUNT":
                            if (myMaxValue < 5)
                            {
                                //oChart.ChartAreas("ChartArea1").AxisY.Interval = 1;
                                YaxisInterval = 1;
                            }
                            break;
                        default:
                            //TOTAL
                            if (myMaxValue < 5)
                            {
                                bool bInt1 = false;
                                bool bInt2 = false;
                                bool bInt3 = false;
                                bool bInt4 = false;
                                if ((Strings.Left(sGraphYField1, 4) == "LI__" | Strings.Left(sGraphYField1, 4) == "SI__" | Strings.Left(sGraphYField1, 4) == "BI__" | Strings.Left(sGraphYField1, 4) == "INT_"))
                                {
                                    bInt1 = true;
                                }
                                else if (sGraphYField1 == "NONE")
                                {
                                    bInt1 = true;
                                }
                                if ((Strings.Left(sGraphYField2, 4) == "LI__" | Strings.Left(sGraphYField2, 4) == "SI__" | Strings.Left(sGraphYField2, 4) == "BI__" | Strings.Left(sGraphYField2, 4) == "INT_"))
                                {
                                    bInt2 = true;
                                }
                                else if (sGraphYField2 == "NONE")
                                {
                                    bInt2 = true;
                                }
                                if ((Strings.Left(sGraphYField3, 4) == "LI__" | Strings.Left(sGraphYField3, 4) == "SI__" | Strings.Left(sGraphYField3, 4) == "BI__" | Strings.Left(sGraphYField3, 4) == "INT_"))
                                {
                                    bInt3 = true;
                                }
                                else if (sGraphYField3 == "NONE")
                                {
                                    bInt3 = true;
                                }
                                if ((Strings.Left(sGraphYField4, 4) == "LI__" | Strings.Left(sGraphYField4, 4) == "SI__" | Strings.Left(sGraphYField4, 4) == "BI__" | Strings.Left(sGraphYField4, 4) == "INT_"))
                                {
                                    bInt4 = true;
                                }
                                else if (sGraphYField4 == "NONE")
                                {
                                    bInt4 = true;
                                }
                                if (bInt1 & bInt2 & bInt3 & bInt4)
                                {
                                    //oChart.ChartAreas("ChartArea1").AxisY.Interval = 1;
                                    YaxisInterval = 1;
                                }
                            }
                            break;
                    }
                    break;
            }


            ViewMetaData = _viewMetaData;

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, , sProc);
            //    }
            //}
        }

        public void GetGaugeProperties()
        {
            bool gbShowLabels = true, gbShowDatapointLabels, gbShowPercentage;
            string gsTitle = "";
            int iNeedleThickness = 4, iNeedleBaseWidth = 100;
            double iNeedleLabelPctg = 0.19, iNeedleLengthPtg = 0.28;

            int iArcDifference;
            double iArcWidthPtg = 0.075;

            int iTickFont = 16, iTickLength = 20, iTicksDesired = 10;
            double iTickLabelPctg = 0.12;

            if (ChartWidth <= 100)
            {
                gbShowLabels = false;
                gbShowDatapointLabels = false;
                gbShowPercentage = false;

                gsTitle = "";

                iNeedleThickness = 3;
                iNeedleBaseWidth = 15;
                iNeedleLabelPctg = 0.09;
                iNeedleLengthPtg = 0.36;

                iArcDifference = 0;
                iArcWidthPtg = 0.2;
            }
            else if (ChartWidth <= 125)
            {
                iTickFont = 8;
                iTickLength = 3;
                iTicksDesired = 5;
                iTickLabelPctg = 0.02;

                iNeedleThickness = 5;
                iNeedleBaseWidth = 20;
                iNeedleLabelPctg = 0.09;
                iNeedleLengthPtg = 0.36;

                iArcDifference = 0;
                iArcWidthPtg = 0.1;
            }
            else if (ChartWidth <= 150)
            {
                iTickFont = 8;
                iTickLength = 5;
                iTicksDesired = 5;
                iTickLabelPctg = 0.02;

                iNeedleThickness = 6;
                iNeedleBaseWidth = 20;
                iNeedleLabelPctg = 0.09;
                iNeedleLengthPtg = 0.36;

                iArcDifference = 0;
                iArcWidthPtg = 0.1;
            }
            else if (ChartWidth <= 175)
            {
                iTickFont = 10;
                iTickLength = 6;
                iTicksDesired = 5;
                iTickLabelPctg = 0.02;

                iNeedleThickness = 6;
                iNeedleBaseWidth = 30;
                iNeedleLabelPctg = 0.09;
                iNeedleLengthPtg = 0.36;

                iArcDifference = 0;
                iArcWidthPtg = 0.1;
            }
            else if (ChartWidth <= 200)
            {
                iTickFont = 10;
                iTickLength = 8;
                iTicksDesired = 5;
                iTickLabelPctg = 0.02;

                iNeedleThickness = 7;
                iNeedleBaseWidth = 40;
                iNeedleLabelPctg = 0.09;
                iNeedleLengthPtg = 0.32;

                iArcDifference = 0;
                iArcWidthPtg = 0.075;
            }
            else if (ChartWidth <= 250)
            {

                iTickFont = 10;
                iTickLength = 8;
                iTicksDesired = 5;
                iTickLabelPctg = 0.02;

                iNeedleThickness = 7;
                iNeedleBaseWidth = 40;
                iNeedleLabelPctg = 0.09;
                iNeedleLengthPtg = 0.32;

                iArcDifference = 0;
                iArcWidthPtg = 0.075;
            }

            else if (ChartWidth <= 300)
            {

                iTickFont = 12;
                iTickLength = 10;
                iTicksDesired = 5;
                iTickLabelPctg = 0.05;

                iNeedleThickness = 7;
                iNeedleBaseWidth = 40;
                iNeedleLabelPctg = 0.09;
                iNeedleLengthPtg = 0.32;

                iArcDifference = 0;
                iArcWidthPtg = 0.075;
            }
            else if (ChartWidth <= 350)
            {

                iTickFont = 12;
                iTickLength = 12;
                iTicksDesired = 5;
                iTickLabelPctg = 0.05;

                iNeedleThickness = 7;
                iNeedleBaseWidth = 40;
                iNeedleLabelPctg = 0.09;
                iNeedleLengthPtg = 0.32;

                iArcDifference = 0;
                iArcWidthPtg = 0.075;

            }
            else if (ChartWidth <= 400)
            {
                iTickFont = 12;
                iTickLength = 15;
                iTicksDesired = 5;
                iTickLabelPctg = 0.07;

                iNeedleThickness = 8;
                iNeedleBaseWidth = 50;
                iNeedleLabelPctg = 0.14;
                iNeedleLengthPtg = 0.3;

                iArcDifference = 0;
                iArcWidthPtg = 0.075;

            }
            else if (ChartWidth <= 450)
            {
                iTickFont = 14;
                iTickLength = 15;
                iTicksDesired = 5;
                iTickLabelPctg = 0.07;

                iNeedleThickness = 8;
                iNeedleBaseWidth = 50;
                iNeedleLabelPctg = 0.14;
                iNeedleLengthPtg = 0.3;

                iArcDifference = 0;
                iArcWidthPtg = 0.075;
            }

            else if (ChartWidth <= 550)
            {
                iTickFont = 14;
                iTickLength = 15;
                iTicksDesired = 5;
                iTickLabelPctg = 0.07;

                iNeedleThickness = 8;
                iNeedleBaseWidth = 50;
                iNeedleLabelPctg = 0.14;
                iNeedleLengthPtg = 0.3;

                iArcDifference = 0;
                iArcWidthPtg = 0.075;

            }
            else if (ChartWidth <= 600)
            {

                iTickFont = 14;
                iTickLength = 15;
                iTicksDesired = 10;
                iTickLabelPctg = 0.1;

                iNeedleBaseWidth = 75;
                iNeedleThickness = 10;
                iNeedleLabelPctg = 0.19;
                iNeedleLengthPtg = 0.29;

                iArcDifference = 2;
                iArcWidthPtg = 0.075;

            }
            else if (ChartWidth <= 800)
            {

                iTickFont = 16;
                iTickLength = 20;
                iTicksDesired = 10;
                iTickLabelPctg = 0.12;

                iNeedleBaseWidth = 100;
                iNeedleThickness = 12;
                iNeedleLabelPctg = 0.19;
                iNeedleLengthPtg = 0.29;

                iArcDifference = 4;
                iArcWidthPtg = 0.075;
            }

            else if (ChartWidth <= 1500)
            {

                iTickFont = 16;
                iTickLength = 20;
                iTicksDesired = 10;
                iTickLabelPctg = 0.12;

                iNeedleBaseWidth = 100;
                iNeedleThickness = 14;
                iNeedleLabelPctg = 0.19;
                iNeedleLengthPtg = 0.28;

                iArcDifference = 6;
                iArcWidthPtg = 0.075;

            }
            else
            {
                iTickFont = 24;
                iTickLength = 40;
                iTicksDesired = 10;
                iTickLabelPctg = 0.12;

                iNeedleBaseWidth = 200;
                iNeedleThickness = 16;
                iNeedleLabelPctg = 0.23;
                iNeedleLengthPtg = 0.28;
            }
        }


        public string GetAxisLabel(double dDate, string sSortField)
        {
            string sLabel = "";
            System.DateTime dtDate = System.DateTime.FromOADate(dDate);
            int par_iValid = 4;

            switch (Strings.Left(sSortField, 4))
            {
                case "DTD_":
                    sLabel = goTR.DateToString(dtDate, "", ref par_iValid);
                    break;
                case "DTM_":
                    sLabel = goTR.DateToMonthString(dtDate, "", ref par_iValid);
                    break;
                case "DTQ_":
                    sLabel = goTR.DateToQuarterString(dtDate, "", ref par_iValid);
                    break;
                case "DTY_":
                    sLabel = goTR.DateToYearString(dtDate, "", ref par_iValid);
                    break;
                default:

                    break;
            }

            return sLabel;
        }

        public void GetChartSize(ref int iHeight, ref int iWidth, string ViewId, string ViewMetaData, string DesktopMetaData)
        {
            //try
            //{
            int iWinH = 664;
            int iWinW = 1242;
            bool bDRS = false;

            bool gbIsMaximized = false;
            //if (goDesktop.DateRangeSelectorEnabled)
            //    bDRS = true;

            string DRSENABLED = goTR.StrRead(DesktopMetaData, "DRSENABLED", "", false);
            if (DRSENABLED == "1")
            {
                bDRS = true;
            }

            //TAKE OFF WHAT'S AT THE TOP
            //drs
            if (bDRS)
                iWinH = iWinH - 25;
            //view menu
            iWinH = iWinH - 25;

            //this is the height the views have to share
            iHeight = iWinH;

            int par_iValid = 4;
            if (gbIsMaximized)
            {
                //title bar
                iHeight = iHeight - 20;

                //back panel
                int iGraphXSortFields = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(ViewMetaData, "GRAPHXSORTFIELDS", "0"), "", ref par_iValid));
                if (iGraphXSortFields > 1)
                    iHeight = iHeight - 20;

                //bottom scroll bar
                iHeight = iHeight - 20;

                //padding
                iHeight = iHeight - 5;

                int iChangeType = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(ViewMetaData, "ALLOWCHANGINGTYPE", "0"), "", ref par_iValid));
                int iChangeSort = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(ViewMetaData, "ALLOWCHANGINGSORT", "0"), "", ref par_iValid));
                if (iChangeType == 1 | iChangeSort == 1)
                    iHeight = iHeight - 20;

                ////click here to load view
                //if (pnlDoNotAutoLoad.Visible)
                //    iHeight = iHeight - 20;

                //width
                iWidth = iWinW;

                //side scroll bar
                iWidth = iWidth - 20;

                //padding
                iWidth = iWidth - 7;

            }
            else
            {

                int i = 0;
                int iViewCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(DesktopMetaData, "VIEWCOUNT", 0), "", ref par_iValid));
                for (i = 1; i <= iViewCount; i++)
                {
                    if (ViewId.ToString().Replace(" ", "") == goTR.StrRead(DesktopMetaData, "VIEW" + i + "ID", "1").ToString().Replace(" ", ""))
                    {
                        int iCol = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(DesktopMetaData, "VIEW" + i + "COLUMN", "1"), "", ref par_iValid));
                        int iViewHeight = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(DesktopMetaData, "VIEW" + i + "HEIGHT", "100"), "", ref par_iValid));
                        int iCol2Width = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(DesktopMetaData, "COLUMN2WIDTH", "0"), "", ref par_iValid));

                        int iTabCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(DesktopMetaData, "TABCOUNT", "0"), "", ref par_iValid));
                        int iTabHeight = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(DesktopMetaData, "TABHEIGHT", "0"), "", ref par_iValid));

                        //get approx view height
                        if (iTabCount > 0)
                            iHeight = iHeight * (100 - iTabHeight) / 100;
                        iHeight = iHeight * iViewHeight / 100;

                        //title bar
                        iHeight = iHeight - 20;

                        //back panel
                        int iGraphXSortFields = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(ViewMetaData, "GRAPHXSORTFIELDS", "0"), "", ref par_iValid));
                        if (iGraphXSortFields > 1)
                            iHeight = iHeight - 20;

                        //toolbar
                        int iChangeType = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(ViewMetaData, "ALLOWCHANGINGTYPE", "0"), "", ref par_iValid));
                        int iChangeSort = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(ViewMetaData, "ALLOWCHANGINGSORT", "0"), "", ref par_iValid));
                        if (iChangeType == 1 | iChangeSort == 1)
                            iHeight = iHeight - 20;

                        ////click here to load view
                        //if (pnlDoNotAutoLoad.Visible)
                        //    iHeight = iHeight - 20;

                        //bottom scroll bar
                        iHeight = iHeight - 20;

                        //padding
                        //iHeight = iHeight - 7
                        iHeight = iHeight - 8;

                        //get approx view width
                        switch (iCol)
                        {
                            case 1:
                                if (iCol2Width > 0)
                                {
                                    iWidth = iWinW * iCol2Width / 100;
                                    iWidth = iWinW - iWidth;
                                }
                                else
                                {
                                    iWidth = iWinW;
                                }
                                break;
                            case 2:
                                if (iCol2Width > 0)
                                {
                                    iWidth = iWinW * iCol2Width / 100;
                                }
                                break;
                        }

                        //side scroll bar
                        iWidth = iWidth - 20;

                        //padding
                        iWidth = iWidth - 5;

                        goto ReturnValues;
                    }
                }

                iViewCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(DesktopMetaData, "TABCOUNT", 0), "", ref par_iValid));
                for (i = 1; i <= iViewCount; i++)
                {
                    if (ViewId.ToString().Replace(" ", "") == goTR.StrRead(DesktopMetaData, "TAB" + i + "VIEWID", "1").ToString().Replace(" ", ""))
                    {

                        int iTabHeight = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(DesktopMetaData, "TABHEIGHT", "0"), "", ref par_iValid));

                        iWidth = iWinW;

                        //side scroll bar
                        iWidth = iWidth - 20;

                        //padding
                        iWidth = iWidth - 10;

                        iHeight = iHeight * iTabHeight / 100;

                        //back panel
                        int iGraphXSortFields = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(ViewMetaData, "GRAPHXSORTFIELDS", "0"), "", ref par_iValid));
                        if (iGraphXSortFields > 1)
                            iHeight = iHeight - 20;

                        //toolbar
                        int iChangeType = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(ViewMetaData, "ALLOWCHANGINGTYPE", "0"), "", ref par_iValid));
                        int iChangeSort = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(ViewMetaData, "ALLOWCHANGINGSORT", "0"), "", ref par_iValid));
                        if (iChangeType == 1 | iChangeSort == 1)
                            iHeight = iHeight - 20;

                        ////click here to load view
                        //if (pnlDoNotAutoLoad.Visible)
                        //    iHeight = iHeight - 20;

                        //bottom scroll bar
                        iHeight = iHeight - 20;

                        //title bar
                        iHeight = iHeight - 20;

                        //tab buttons
                        iHeight = iHeight - 30;

                        //padding
                        iHeight = iHeight - 10;

                        goto ReturnValues;
                    }
                }
                ReturnValues:


                if (iHeight < 75)
                {
                    iHeight = 75;
                }

                if (iWidth < 100)
                {
                    iWidth = 100;
                }

                //If iHeight < 75 Or iWidth < 100 Then
                //    iHeight = 75
                //    iWidth = 100
                //End If

            }

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {

            //    }
            //}
        }
        private void CustomizeYAxisLabels(bool gbAllCurr, ref int par_iValid, ref string sFormat, ref string Label)
        {
            if (string.IsNullOrEmpty(Label))
            {
                Label = "0";
            }

            if (Information.IsNumeric(Label))
            {

                double dLabel = goTR.StringToNum(Label, "", ref par_iValid);
                dLabel = Math.Round(dLabel, 0, MidpointRounding.ToEven);

                if (dLabel / 1000000000 >= 1 | dLabel / 1000000000 <= -1)
                {
                    dLabel = dLabel / 1000000000;

                    int iDecl = 0;
                    string sTemp = dLabel.ToString();
                    string[] aTemp = Strings.Split(sTemp, ".");
                    if (aTemp.Length > 1)
                    {
                        sTemp = aTemp[1];
                        for (int j = 0; j <= sTemp.Length - 1; j++)
                        {
                            string cTemp = Strings.Left(sTemp, 1);
                            if (goTR.StringToNum(cTemp, "", ref par_iValid) > 0)
                            {
                                iDecl = iDecl + 1;
                            }
                            else
                            {
                                break; // TODO: might not be correct. Was : Exit For
                            }
                            sTemp = Strings.Mid(sTemp, 2);
                        }
                        if (Strings.InStr(sFormat, "|") > 0)
                            sFormat = iDecl + Strings.Mid(sFormat, Strings.InStr(sFormat, "|"));
                    }
                    else
                    {
                        if (Strings.InStr(sFormat, "|") > 0)
                            sFormat = "0" + Strings.Mid(sFormat, Strings.InStr(sFormat, "|"));
                    }

                    if (gbAllCurr)
                    {
                        Label = goTR.CurrToString(Convert.ToDecimal(dLabel), sFormat, ref par_iValid) + "B";
                    }
                    else
                    {
                        Label = goTR.NumToString(dLabel, sFormat) + "B";
                    }

                }
                else if (dLabel / 1000000 >= 1 | dLabel / 1000000 <= -1)
                {
                    dLabel = dLabel / 1000000;

                    int iDecl = 0;
                    string sTemp = dLabel.ToString();
                    string[] aTemp = Strings.Split(sTemp, ".");
                    if (aTemp.Length > 1)
                    {
                        sTemp = aTemp[1];
                        for (int j = 0; j <= sTemp.Length - 1; j++)
                        {
                            string cTemp = Strings.Left(sTemp, 1);
                            if (goTR.StringToNum(cTemp, "", ref par_iValid) > 0)
                            {
                                iDecl = iDecl + 1;
                            }
                            else
                            {
                                break; // TODO: might not be correct. Was : Exit For
                            }
                            sTemp = Strings.Mid(sTemp, 2);
                        }
                        if (Strings.InStr(sFormat, "|") > 0)
                            sFormat = iDecl + Strings.Mid(sFormat, Strings.InStr(sFormat, "|"));
                    }
                    else
                    {
                        if (Strings.InStr(sFormat, "|") > 0)
                            sFormat = "0" + Strings.Mid(sFormat, Strings.InStr(sFormat, "|"));
                    }

                    if (gbAllCurr)
                    {
                        Label = goTR.CurrToString(Convert.ToDecimal(dLabel), sFormat, ref par_iValid) + "M";
                    }
                    else
                    {
                        Label = goTR.NumToString(dLabel, sFormat) + "M";
                    }

                }
                else if (dLabel / 1000 >= 1 | dLabel / 1000 <= -1)
                {
                    dLabel = dLabel / 1000;

                    int iDecl = 0;
                    string sTemp = dLabel.ToString();
                    string[] aTemp = Strings.Split(sTemp, ".");
                    if (aTemp.Length > 1)
                    {
                        sTemp = aTemp[1];
                        for (int j = 0; j <= sTemp.Length - 1; j++)
                        {
                            string cTemp = Strings.Left(sTemp, 1);
                            if (goTR.StringToNum(cTemp, "", ref par_iValid) > 0)
                            {
                                iDecl = iDecl + 1;
                            }
                            else
                            {
                                break; // TODO: might not be correct. Was : Exit For
                            }
                            sTemp = Strings.Mid(sTemp, 2);
                        }
                        if (Strings.InStr(sFormat, "|") > 0)
                            sFormat = iDecl + Strings.Mid(sFormat, Strings.InStr(sFormat, "|"));
                    }
                    else
                    {
                        if (Strings.InStr(sFormat, "|") > 0)
                            sFormat = "0" + Strings.Mid(sFormat, Strings.InStr(sFormat, "|"));
                    }

                    if (gbAllCurr)
                    {
                        Label = goTR.CurrToString(Convert.ToDecimal(dLabel), sFormat, ref par_iValid) + "k";
                    }
                    else
                    {
                        Label = goTR.NumToString(dLabel, sFormat) + "k";
                    }
                }
                else if (dLabel / 100 >= 1 | dLabel / 100 <= -1)
                {
                    int iDec1 = 0;
                    dLabel = dLabel / 1000;

                    if (Strings.InStr(sFormat, "|") > 0)
                        sFormat = iDec1 + Strings.Mid(sFormat, Strings.InStr(sFormat, "|"));

                    if (gbAllCurr)
                    {
                        Label = goTR.CurrToString(Convert.ToDecimal(dLabel), sFormat, ref par_iValid) + "k";
                    }
                    else
                    {
                        Label = goTR.NumToString(dLabel, sFormat) + "k";
                    }
                }
                else
                {

                    int iDec1 = 0;
                    string sTemp = dLabel.ToString();
                    string[] aTemp = Strings.Split(sTemp, ".");
                    if (aTemp.Length > 1)
                    {
                        sTemp = aTemp[1];
                        for (int j = 0; j <= sTemp.Length - 1; j++)
                        {
                            string cTemp = Strings.Left(sTemp, 1);
                            if (goTR.StringToNum(cTemp, "", ref par_iValid) > 0)
                            {
                                iDec1 = iDec1 + 1;
                            }
                            else
                            {
                                break; // TODO: might not be correct. Was : Exit For
                            }
                            sTemp = Strings.Mid(sTemp, 2);
                        }
                        if (Strings.InStr(sFormat, "|") > 0)
                            sFormat = iDec1 + Strings.Mid(sFormat, Strings.InStr(sFormat, "|"));
                    }
                    else
                    {
                        if (Strings.InStr(sFormat, "|") > 0)
                            sFormat = "0" + Strings.Mid(sFormat, Strings.InStr(sFormat, "|"));
                    }

                    if (gbAllCurr)
                    {
                        Label = goTR.CurrToString(Convert.ToDecimal(dLabel), sFormat, ref par_iValid);
                    }
                    else
                    {
                        Label = goTR.NumToString(dLabel, sFormat);
                    }
                }

                if (dLabel < 0)
                {
                    //yAxisLabels(i).ForeColor = Drawing.Color.Red;
                }
                //_clSeries.YAxis[i] = Label;                                               

            }
        }

        public void AddChartHistory(string ViewMetaData, int iIndex, string sParentIndices, string sLabel)
        {
            //try
            //{
            switch (iIndex)
            {
                case 1:
                    goTR.StrWrite(ref ViewMetaData, "GRAPHHISTORY1", sParentIndices + Constants.vbCrLf + sLabel);
                    goTR.StrDelete(ref ViewMetaData, "GRAPHHISTORY2");
                    goTR.StrDelete(ref ViewMetaData, "GRAPHHISTORY3");
                    goTR.StrDelete(ref ViewMetaData, "GRAPHHISTORY4");
                    break;
                case 2:
                    goTR.StrWrite(ref ViewMetaData, "GRAPHHISTORY2", sParentIndices + Constants.vbCrLf + sLabel);
                    goTR.StrDelete(ref ViewMetaData, "GRAPHHISTORY3");
                    goTR.StrDelete(ref ViewMetaData, "GRAPHHISTORY4");
                    break;
                case 3:
                    goTR.StrWrite(ref ViewMetaData, "GRAPHHISTORY3", sParentIndices + Constants.vbCrLf + sLabel);
                    goTR.StrDelete(ref ViewMetaData, "GRAPHHISTORY4");
                    break;
                case 4:
                    goTR.StrWrite(ref ViewMetaData, "GRAPHHISTORY4", sParentIndices + Constants.vbCrLf + sLabel);
                    break;
            }
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, , sProc);
            //    }
            //}
        }
        public string GetSortFieldDirection(string sField, Collection gcSortFields)
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //try
            //{
            int i = 0;
            string sSortField = null;
            string sSortDirection = null;
            for (i = 1; i <= gcSortFields.Count; i++)
            {
                sSortField = gcSortFields[i].ToString();
                sSortField = goTR.ExtractString(sSortField, 1, "|");
                if (Strings.UCase(sSortField) == Strings.UCase(sField))
                {
                    sSortDirection = goTR.ExtractString(gcSortFields[i].ToString(), 2, "|");
                    sSortDirection = Strings.UCase(sSortDirection);
                    if (string.IsNullOrEmpty(sSortDirection))
                        sSortDirection = "ASC";
                    if (sSortDirection == "A")
                        sSortDirection = "ASC";
                    return sSortDirection;
                }
            }
            return "";
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, , sProc);
            //    }
            //    return "";
            //}
        }
        public bool GetChartDataPoint(ref string sLabel, ref double dCount, ref double dTotal, ref double dAverage, ref double dMin, ref double dMax, ref double dMed, ref double dDate, string sField, int iSortField, int iParent1Index, int iParent2Index, int iParent3Index, int iIndex)
        {

            try
            {
                Selltis.Core.Chart.ChartData oPoint = default(Selltis.Core.Chart.ChartData);
                int i = 0;
                int iCount = 0;
                string thisField = "";
                int thisSortField = -1;
                int thisParentGraph1Index = 0;
                int thisParentGraph2Index = 0;
                int thisParentGraph3Index = 0;

                for (i = 1; i <= gcChartData.Count; i++)
                {
                    oPoint = (Selltis.Core.Chart.ChartData)gcChartData[i];
                    thisField = oPoint.sField;
                    thisSortField = oPoint.iSortField;
                    thisParentGraph1Index = oPoint.iParent1Index;
                    thisParentGraph2Index = oPoint.iParent2Index;
                    thisParentGraph3Index = oPoint.iParent3Index;

                    if (thisField == sField & thisSortField == iSortField & thisParentGraph1Index == iParent1Index & thisParentGraph2Index == iParent2Index & thisParentGraph3Index == iParent3Index)
                        iCount = iCount + 1;

                    if (iCount == iIndex)
                    {
                        sLabel = oPoint.sLabel;
                        dCount = oPoint.dCount;
                        dTotal = oPoint.dTotal;
                        dAverage = oPoint.dAverage;
                        dMin = oPoint.dMin;
                        dMax = oPoint.dMax;
                        dMed = oPoint.dMed;
                        dDate = oPoint.dDate;
                        return true;
                    }
                }
                return false;

            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, , sProc);
                //}
                return false;
            }
        }
        public int GetChartDataPointCount(string sField, int iSortField, int iParent1Index = 0, int iParent2Index = 0, int iParent3Index = 0)
        {
            try
            {
                Selltis.Core.Chart.ChartData oData = default(Selltis.Core.Chart.ChartData);
                int i = 0;
                int iCount = 0;
                string thisField = "";
                int thisSortField = -1;
                int thisParentGraph1Index = 0;
                int thisParentGraph2Index = 0;
                int thisParentGraph3Index = 0;

                for (i = 1; i <= gcChartData.Count; i++)
                {
                    oData = (Selltis.Core.Chart.ChartData)gcChartData[i];
                    thisField = oData.sField;
                    thisSortField = oData.iSortField;
                    thisParentGraph1Index = oData.iParent1Index;
                    thisParentGraph2Index = oData.iParent2Index;
                    thisParentGraph3Index = oData.iParent3Index;

                    if (thisField == sField & thisSortField == iSortField & thisParentGraph1Index == iParent1Index & thisParentGraph2Index == iParent2Index & thisParentGraph3Index == iParent3Index)
                        iCount = iCount + 1;
                }

                return iCount;
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, , sProc);
                //}
                return 0;
            }
        }

        public double GetMinMaxDate(string sMode, string sXField, double dDate, bool bDesc = false)
        {
            //sMode = MIN/MAX
            //sXField is the sort field value for current x axis
            //dDate is the date as double
            //bDesc is true if the sort order is DESC for the current x axis

            //12/31/9999 is the max date that a date object can handle....
            System.DateTime theDate = System.DateTime.FromOADate(dDate);
            if (theDate.Year >= 9999)
            {
                return new System.DateTime(9999, 12, 31).ToOADate();
            }

            double dNewDate = dDate;
            int iMultiplier = 1;

            if (sMode == "MIN")
                iMultiplier = -1;
            if (bDesc)
                iMultiplier = iMultiplier * -1;

            switch (Strings.Left(sXField, 4))
            {
                case "DTY_":
                    dNewDate = System.DateTime.FromOADate(dDate).AddMonths(iMultiplier * 11).ToOADate();
                    break;
                case "DTQ_":
                    dNewDate = System.DateTime.FromOADate(dDate).AddMonths(iMultiplier * 2).ToOADate();
                    break;
                case "DTM_":
                    dNewDate = System.DateTime.FromOADate(dDate).AddDays(iMultiplier * 28).ToOADate();
                    break;
                case "DTD_":
                    if (sMode == "MIN")
                        dNewDate = System.DateTime.FromOADate(dDate).AddHours(iMultiplier * 23).ToOADate();
                    break;
                default:
                    break;
                    //no change
            }

            return dNewDate;

        }
        public System.Collections.Generic.List<double> GetDTxMissingValues(string sSortField, double dStart = 0, double dEnd = 0, string StartDate = "", string EndDate = "")
        {
            //let's get missing dates
            bool bIsDTX = false;
            System.Collections.Generic.List<double> lDates = new System.Collections.Generic.List<double>();

            switch (Strings.Left(sSortField, 4))
            {
                case "DTY_":
                case "DTQ_":
                case "DTM_":
                case "DTD_":
                    bIsDTX = true;
                    break;
                default:
                    bIsDTX = false;
                    break;
            }

            if (bIsDTX)
            {
                if (dStart == 0)
                    dStart = Convert.ToDateTime(StartDate).ToOADate();
                if (dEnd == 0)
                    dEnd = Convert.ToDateTime(EndDate).ToOADate();

                DateTime dtStart = DateTime.FromOADate(dStart);
                DateTime dtEnd = DateTime.FromOADate(dEnd);


                DateTime dStrartDate = dtStart.Date;
                DateTime dEndDate = dtEnd.Date;

                //neutralize the start date
                switch (Strings.Left(sSortField, 4))
                {
                    case "DTD_":
                        dStrartDate = dStrartDate;
                        break;
                    case "DTM_":
                        dStrartDate = new System.DateTime(dStrartDate.Year, dStrartDate.Month, 1);
                        break;
                    case "DTQ_":
                        dStrartDate = goTR.GetQuarterDate(dStrartDate);
                        break;
                    case "DTY_":
                        dStrartDate = new System.DateTime(dStrartDate.Year, 1, 1);
                        break;
                    default:

                        break;
                }

                int iMultiplier = 1;

                //descending sort?
                //If GetSortFieldDirection(sSortField) <> "ASC" Then iMultiplier = -1

                switch (Strings.Left(sSortField, 4))
                {
                    case "DTD_":
                        System.DateTime dNew = dStrartDate.AddDays(iMultiplier);
                        //dtStart
                        while (dNew < dEndDate)
                        {
                            lDates.Add(dNew.ToOADate());
                            dNew = dNew.AddDays(iMultiplier);
                        }
                        break;
                    case "DTM_":
                        dNew = dStrartDate.AddMonths(iMultiplier);
                        //dtStart
                        while (dNew < dEndDate)
                        {
                            lDates.Add(dNew.ToOADate());
                            dNew = dNew.AddMonths(iMultiplier);
                        }
                        break;
                    case "DTQ_":
                        dStrartDate = goTR.GetQuarterDate(dStrartDate);
                        dNew = dStrartDate.AddMonths(iMultiplier * 3);
                        //dtStart
                        while (dNew < dEndDate)
                        {
                            lDates.Add(dNew.ToOADate());
                            dNew = dNew.AddMonths(iMultiplier * 3);
                        }
                        break;
                    case "DTY_":
                        dNew = dStrartDate.AddYears(iMultiplier);
                        //dtStart
                        while (dNew < dEndDate)
                        {
                            lDates.Add(dNew.ToOADate());
                            dNew = dNew.AddYears(iMultiplier);
                        }
                        break;
                    default:

                        break;
                }

            }

            return lDates;

        }
        public double GetDtxGhostPointValue(double par_dCurrentValue, double par_dOffset, string par_sDTxFieldname)
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            try
            {
                System.DateTime dDate = System.DateTime.FromOADate(par_dCurrentValue);
                switch (Strings.Left(par_sDTxFieldname, 4))
                {
                    case "DTY_":
                        //11
                        return dDate.AddMonths(Convert.ToInt32(par_dOffset * 6)).ToOADate();
                    case "DTQ_":
                        //2
                        return dDate.AddMonths(Convert.ToInt32(par_dOffset * 1)).ToOADate();
                    case "DTM_":
                        //29
                        return dDate.AddDays(par_dOffset * 15).ToOADate();
                    case "DTD_":
                        //23
                        return dDate.AddHours(par_dOffset * 12).ToOADate();
                    default:
                        return par_dCurrentValue;
                }
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, , sProc);
                //}
                return par_dCurrentValue;
            }
        }



        public void FillChangeYAxisDdl()
        {

            string sOmit = "";
            int y = 0;
            for (y = 2; y <= 4; y++)
            {
                string YFieldOther = goTR.StrRead(_viewMetaData, "GRAPHYFIELD" + y, "", false);
                if (!string.IsNullOrEmpty(YFieldOther))
                {
                    switch (sOmit)
                    {
                        case "":
                            sOmit = YFieldOther;
                            break;
                        default:
                            sOmit = sOmit + "|" + YFieldOther;
                            break;
                    }
                }
            }


            string OmitFields = sOmit;
            //string OmitFields = "Ravi|v";
            clArray clFields = new clArray();
            clArray clLinks = new clArray();
            clTable cltable = FillDropDownsLists(ref OmitFields, ref clFields, TableName, true, false, false, false, false, "CUR_|INT_|SI__|LI__|BI__|SR__|DR__", false, clLinks, false, true, true, true, true);

            //Populate the control 
            List<GraphYFieldsItems> par_oControl = new List<GraphYFieldsItems>();
            DataTable dtn = cltable.dt;

            foreach (DataRow item in dtn.Rows)
            {
                GraphYFieldsItems graphYFieldsItems = new GraphYFieldsItems();
                graphYFieldsItems.index = Convert.ToInt32(item["index"].ToString());
                graphYFieldsItems.value = item["value"].ToString();
                graphYFieldsItems.text = item["text"].ToString();

                par_oControl.Add(graphYFieldsItems);
            }
            GraphYField = par_oControl;
        }

        public void FillChangeXAxisDdl()
        {
            int par_iValid = 4;
            int iSortField = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHSORTFIELD", "1"), "", ref par_iValid));
            string sSortfield = Sorts[iSortField].ToString();
            string sSorts = "";
            for (int i = 1; i <= Sorts.Count; i++)
            {
                if (string.IsNullOrEmpty(sSorts))
                {
                    string s = Strings.Replace(Strings.Replace(goTR.ExtractString(Sorts[i].ToString(), 1, Strings.Chr(1).ToString()), "<%", ""), "%>", "");
                    if (s != sSortfield)
                        sSorts = s;
                }
                else
                {
                    string s = Strings.Replace(Strings.Replace(goTR.ExtractString(Sorts[i].ToString(), 1, Strings.Chr(1).ToString()), "<%", ""), "%>", "");
                    if (s != sSortfield)
                        sSorts = sSorts + "|" + s;
                }
            }

            string sDTT = "";
            clArray aFields = godata.GetFields(TableName);
            for (int i = 1; i <= aFields.GetDimension(); i++)
            {
                string theField = aFields.GetItem(i);
                if (Strings.Left(theField, 4) == "DTT_")
                {
                    if (string.IsNullOrEmpty(sDTT))
                    {
                        sDTT = theField;
                    }
                    else
                    {
                        sDTT = sDTT + "|" + theField;
                    }
                }
            }

            string sOmit = sSorts;
            if (!string.IsNullOrEmpty(sOmit))
            {
                sOmit = sOmit + "|" + sDTT;
            }
            else
            {
                sOmit = sDTT;
            }
            clArray clFields1 = new clArray();
            clArray clLinks1 = new clArray();
            clTable cltable1 = FillDropDownsLists(ref sOmit, ref clFields1, TableName, true, true, true, true, true, "", false, null, false, true, true, true, true);

            //Populate the control 
            List<GraphYFieldsItems> par_oControl1 = new List<GraphYFieldsItems>();
            DataTable dtn1 = cltable1.dt;

            string sXField = Sorts[iSortField].ToString();
            sXField = Strings.Replace(Strings.Replace(goTR.ExtractString(sXField, 1, " "), "<%", ""), "%>", "");

            foreach (DataRow item in dtn1.Rows)
            {
                GraphYFieldsItems graphXFieldsItems = new GraphYFieldsItems();
                graphXFieldsItems.index = Convert.ToInt32(item["index"].ToString());
                graphXFieldsItems.value = item["value"].ToString();
                graphXFieldsItems.text = item["text"].ToString();

                if (item["value"].ToString() != "<%NONE%>")
                {
                    par_oControl1.Add(graphXFieldsItems);
                }

            }
            //check sort filter is different from list(i.e., if it is combined fields then need to add it to the list)..J
            if (sXField.Contains("%%"))
            {
                string sTemp = Microsoft.VisualBasic.Strings.Left(sXField, Microsoft.VisualBasic.Strings.InStr(sXField, "%%") - 1);

                GraphYFieldsItems graphXFieldsItems = new GraphYFieldsItems();
                graphXFieldsItems.index = par_oControl1.Count + 1;
                graphXFieldsItems.value = sXField;
                graphXFieldsItems.text = godata.GetFieldFullLabelFromName(TableName, sXField, ref par_iValid).ToString();

                int iIndex = par_oControl1.IndexOf(par_oControl1.Find(m => m.value == sTemp));
                if (iIndex > 0)
                {
                    par_oControl1.Insert(iIndex + 1, graphXFieldsItems);
                }
            }


            GraphXField = par_oControl1;
        }

        private clTable FillDropDownsLists(ref string OmitFields, ref clArray clFields, string FileName = "", bool IncludeNone = true, bool IncludeLinks = true, bool OmitDTEandTME = false, bool OmitMemos = false, bool OmitNLinks = false, string OnlyPrefix = "", bool OmitDTYDTQDTMDTD = false, clArray clLinks = null, bool OmitDTT = false, bool OmitMMR = true, bool OmitMMP = true, bool OmitADR = true, bool OmitADV = true)
        {
            DataTable dt = new DataTable();
            string Meta;
            string FieldName;
            string FieldLabel;
            string Prefix;
            int j;
            string Temp = "";
            string PrefixToTest = "";
            bool PrefixFound = false;

            clTable cltable = new clTable(ref dt);
            goTR = (clTransform)Util.GetInstance("tr");
            godata = (clData)Util.GetInstance("data");
            goMeta = (clMetaData)Util.GetInstance("meta");

            if (IncludeNone)
            {
                cltable.Insert(par_lRowNo: 1);
                cltable.SetVal("Value", 1, "<%NONE%>");
                cltable.SetVal("Text", 1, " (none)");
            }

            clFields = godata.GetFields(FileName);
            Meta = goMeta.PageRead("GLOBAL", "FLD_" + FileName);

            for (int i = 1; i <= clFields.GetDimension(); i++)
            {
                FieldName = (clFields.GetItem(i)).ToUpper();
                if (OmitDTEandTME)
                {
                    Prefix = goTR.GetPrefix(FieldName);
                    switch (Prefix)
                    {
                        case "DTE_":
                        case "TME_":
                        case "TML_":
                            goto SkipThisField;
                    }
                }
                if (OmitDTYDTQDTMDTD)
                {
                    Prefix = goTR.GetPrefix(FieldName);
                    switch (Prefix)
                    {
                        case "DTY_":
                        case "DTQ_":
                        case "DTM_":
                        case "DTD_":
                            goto SkipThisField;
                    }
                }
                if (OmitDTT)
                {
                    Prefix = goTR.GetPrefix(FieldName);
                    switch (Prefix)
                    {
                        case "DTT_":
                            goto SkipThisField;
                    }
                }
                if (OmitMemos)
                {
                    Prefix = goTR.GetPrefix(FieldName);
                    switch (Prefix)
                    {
                        case "MMO_":
                        case "MMR_":
                        case "FIL_":
                        case "BIN_":
                        case "URL_":
                        case "EML_":
                            goto SkipThisField;
                    }
                }
                if (OmitMMR)
                {
                    Prefix = goTR.GetPrefix(FieldName);
                    switch (Prefix)
                    {
                        case "MMR_":
                            goto SkipThisField;
                    }
                }
                if (OmitMMP)
                {
                    Prefix = goTR.GetPrefix(FieldName);
                    switch (Prefix)
                    {
                        case "MMP_":
                            goto SkipThisField;
                    }
                }
                if (OmitADR)
                {
                    Prefix = goTR.GetPrefix(FieldName);
                    switch (Prefix)
                    {
                        case "ADR_":
                            goto SkipThisField;
                    }
                }
                if (OmitADV)
                {
                    Prefix = goTR.GetPrefix(FieldName);
                    switch (Prefix)
                    {
                        case "ADV_":
                            goto SkipThisField;
                    }
                }

                if (!string.IsNullOrEmpty(OnlyPrefix))
                {
                    j = 1;
                    PrefixFound = false;
                    do
                    {
                        PrefixToTest = goTR.ExtractString(OnlyPrefix, j, "|");
                        if (PrefixToTest == (clC.EOT).ToString())
                            break;
                        if (string.IsNullOrEmpty(PrefixToTest))
                            goto NextDoElement;
                        Prefix = goTR.GetPrefix(FieldName);
                        if (Prefix.ToUpper() == PrefixToTest.ToUpper())
                        {
                            PrefixFound = true;
                            break;
                        }
                        NextDoElement:
                        j = j + 1;
                    } while (true);
                    if (!PrefixFound)
                        goto SkipThisField;
                }


                if (!string.IsNullOrEmpty(OmitFields))
                {
                    if (OmitFields.IndexOf("|") != OmitFields.Length + 1)
                    {
                        OmitFields += "|";
                    }
                    if (goTR.Position(OmitFields, FieldName + "|") > 0)
                    {
                        goto SkipThisField;
                    }
                }

                cltable.Insert("", par_lRowNo: 1);
                cltable.SetVal("Value", 1, FieldName);
                FieldLabel = godata.GetFieldLabel(FileName, FieldName);
                if (FieldLabel == "")
                {
                    FieldLabel = FieldName;
                }
                cltable.SetVal("Text", 1, FieldLabel);

                SkipThisField: continue;
            }

            //Links
            if (IncludeLinks)
            {
                clFields = new clArray();
                if (clLinks == null)
                {
                    clFields = godata.GetLinks(FileName);
                }
                else
                {
                    clLinks.Clone(ref clFields);
                }

                for (int i = 0; i <= clFields.GetDimension(); i++)
                {
                    FieldName = clFields.GetItem(i).ToUpper();
                    if (OmitNLinks)
                    {
                        Temp = godata.LKGetType(FileName, FieldName);
                        if (goTR.FromTo(Temp, 2, 2) == "N")
                        {
                            goto SkipThisLink;
                        }
                    }

                    if (!string.IsNullOrEmpty(OmitFields))
                    {
                        if (OmitFields.IndexOf("|") != OmitFields.Length + 1)
                        {
                            OmitFields += "|";
                        }
                        if (goTR.Position(OmitFields, FieldName + "|") > 0)
                        {
                            goto SkipThisLink;
                        }
                    }

                    cltable.Insert("", par_lRowNo: 1);
                    cltable.SetVal("Value", 1, FieldName);
                    FieldLabel = godata.GetFieldLabel(FileName, FieldName);
                    if (FieldLabel == "")
                    {
                        FieldLabel = FieldName;
                    }
                    cltable.SetVal("Text", 1, FieldLabel);

                    SkipThisLink: continue;
                }
            }

            //Sort 
            cltable.Sort("Text ASC");
            return cltable;
        }

        public class clSeries
        {
            public ArrayList YAxis { get; set; }
            public ChartType ChartType { get; set; }
            public string Group { get; set; }
            public string Color { get; set; }
            public string SeriesName { get; set; }
            public List<clPieSeries> PieSeries { get; set; } //use this only for Donut chart
        }

        public class GraphYFieldsItems
        {
            public int index { get; set; }
            public string text { get; set; }
            public string value { get; set; }
        }

        public class clPieSeries
        {
            public string category { get; set; }
            public dynamic value { get; set; }
            public string color { get; set; }
            public string SeriesName { get; set; }
        }

        public enum ChartType
        {
            Area,
            Bar,
            Line,
            Column,
            Gauge,
            StackedBar,
            StackedColumn,
            StackedArea,
            Pie,
            Donut
        }

        public Collection GetColumnCollection(string ViewMetaData, string Fields, string OrderBy)
        {
            goDef = (clDefaults)Util.GetInstance("def");
            string invalidFields;
            string fieldFormatted;
            string FieldsDef;
            Collection cFieldDefs = new Collection();
            string Field;
            string Label;
            int cWidth;
            string Allignment;

            int DisplayAsIcon;
            int DisplayAsLink;
            int ColTextLength;
            int Ellipsis;

            int total;
            int average = 0;
            int minimum = 0;
            int maximum = 0;
            int median = 0;
            int percent;

            string UseOneLine = goTR.StrRead(ViewMetaData, "LIST_ONELINEPERRECORD", "1");
            int ColCount = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COLCOUNT", 0));
            int TotalWidth = 0;
            for (int i = 1; i <= ColCount; i++)
            {
                switch (HttpContext.Current.Request.Browser.Type)
                {
                    case "IE7":
                    case "IE8":
                        TotalWidth = TotalWidth + (Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + i + "WIDTH", "0")) * 6);
                        break;
                    default:
                        TotalWidth = TotalWidth + (Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + i + "WIDTH", "0")) * 6) + 3;
                        break;

                }
            }

            if (ViewType == "CHART")
            {
                TotalWidth = TotalWidth + 300;
            }
            //else
            //{
            //switch (HttpContext.Current.Request.Browser.Type)
            //{
            //    case "IE7":
            //    case "IE8":
            //        TotalWidth = 0;
            //        break;
            //    default:
            //        TotalWidth = TotalWidth + 1;
            //        break;

            //}
            //}

            for (int i = 1; i <= SortFields.Count; i++)
            {
                string sortField = SortFields[i].ToString();
                sortField = goTR.ExtractString(sortField, 1, "|");
                sortField = "<%" + sortField + "%>";

                invalidFields = goTR.GetFieldsFromLine(TableName, sortField, true, false);

                fieldFormatted = goTR.GetFieldsFromLine(TableName, sortField);

                if (Fields == "")
                {
                    Fields = fieldFormatted;
                }
                else
                {
                    Fields = Fields + ", " + fieldFormatted;
                }

                string heading = "0";
                string count = "0";
                string percentl = "0";

                heading = goTR.StrRead(ViewMetaData, "HEADING" + i.ToString(), "0");
                if (heading == "1")
                    count = goTR.StrRead(ViewMetaData, "COUNT" + i.ToString(), "1");

                if (ViewType == "CHART")
                {
                    if (i <= Convert.ToInt32(goTR.StrRead(ViewMetaData, "GRAPHXSORTFIELDS", "0")))
                    {
                        heading = "1";
                        count = "1";
                    }
                }

                sortField = sortField + " " + heading + " " + count + " " + percentl;
                Sorts.Add(sortField);
            }

            if (ViewType == "REPORT" || ViewType == "CHART")
            {
                TotalWidth = TotalWidth + 15;
                for (int i = 1; i <= Sorts.Count; i++)
                {
                    string def = Sorts[i].ToString();
                    int group = Convert.ToInt32(goTR.ExtractString(def, 2, " "));
                    if (group == 1)
                    {
                        TotalWidth = TotalWidth + 15;
                    }
                }
            }


            if (ViewType == "CHART")
            {
                string graphShow = goTR.StrRead(ViewMetaData, "GRAPHYSHOW", "COUNT", false);
                if (graphShow != "COUNT")
                {
                    int yFields = 4;
                    if (graphShow == "STATISTICS")
                        yFields = 1;

                    for (int i = 1; i <= yFields; i++)
                    {
                        string graphYField = (goTR.StrRead(ViewMetaData, "GRAPHYFIELD" + i.ToString(), "NONE", false)).ToUpper();
                        if (graphYField == "NONE" || graphYField == "")
                        {

                        }
                        else
                        {
                            string defualts = goDef.GetFieldPropertiesByPrfx((graphYField.Substring(0, 4)));
                            Label = godata.GetFieldLabel(TableName, graphYField);
                            Allignment = goTR.ExtractString(defualts, 1);
                            cWidth = Convert.ToInt32(goTR.ExtractString(defualts, 2)) * 6;

                            int ClientW = Convert.ToInt32(goTR.StrRead(ViewMetaData, "CLIENT_COL" + i.ToString() + "WIDTH", "-1"));
                            if (ClientW > -1)
                                Width = ClientW * 6;

                            graphYField = "<%" + graphYField + "%>";
                            invalidFields = goTR.GetFieldsFromLine(TableName, graphYField, true, false);

                            if (invalidFields != "")
                            {
                                //error page
                            }
                            else
                            {
                                fieldFormatted = goTR.GetFieldsFromLine(TableName, graphYField);
                                if (Fields == "")
                                    Fields = fieldFormatted;
                                else
                                    Fields = Fields + ", " + fieldFormatted;

                                switch (Allignment)
                                {
                                    case "L":
                                        Allignment = "Left";
                                        break;
                                    case "R":
                                        Allignment = "Right";
                                        break;
                                    case "C":
                                        Allignment = "Center";
                                        break;
                                }

                                if (graphYField == "STATISTICS")
                                {
                                    average = Convert.ToInt32(goTR.StrRead(ViewMetaData, "GRAPHYAVG", "1", false));
                                    minimum = Convert.ToInt32(goTR.StrRead(ViewMetaData, "GRAPHYMIN", "0", false));
                                    maximum = Convert.ToInt32(goTR.StrRead(ViewMetaData, "GRAPHYMAX", "0", false));
                                    median = Convert.ToInt32(goTR.StrRead(ViewMetaData, "GRAPHYMED", "0", false));
                                }

                                FieldsDef = fieldFormatted + " " + Label + " " + Allignment + " " + cWidth.ToString() + " " + "0" + " " + "0" + " " + graphYField + " " + "1" + " " + "-1" + " " + "0" + " " +
                                                        average.ToString() + " " +
                                                        minimum.ToString() + " " +
                                                        maximum.ToString() + " " +
                                                        median.ToString();

                                cFieldDefs.Add(FieldsDef, fieldFormatted);
                            }
                        }
                    }

                }

            }

            for (int j = 1; j <= ColCount; j++)
            {
                Field = goTR.StrRead(ViewMetaData, "COL" + j + "FIELD", 0);
                Label = goTR.StrRead(ViewMetaData, "COL" + j + "LABEL", 0);
                cWidth = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + j + "WIDTH", 0)) * 6;
                Allignment = goTR.StrRead(ViewMetaData, "COL" + j + "ALIGNMENT", 0);

                DisplayAsIcon = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + j + "DISPLAYASICON", 0));
                DisplayAsLink = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + j + "DISPLAYASLINK", 0));
                ColTextLength = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + j + "TEXTLENGTH", -1));
                Ellipsis = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + j + "ELLIPSIS", 0));

                total = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + j + "TOTAL", 0));
                average = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + j + "AVERAGE", 0));
                median = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + j + "MEDIAN", 0));
                minimum = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + j + "MINIMUM", 0));
                maximum = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + j + "MAXIMUM", 0));
                percent = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + j + "PERCENT", 0));

                int ColWidth = Convert.ToInt32(goTR.StrRead(ViewMetaData, "COL" + j + "WIDTH", 0)) - 1;

                if (ColWidth < ColTextLength)
                {
                    ColTextLength = ColWidth;
                    Ellipsis = 0;
                }
                else if (ColTextLength == -1)
                {
                    ColTextLength = ColWidth;
                }

                invalidFields = goTR.GetFieldsFromLine(TableName, Field, true, false);
                if (invalidFields != "")
                {
                    //error page
                }
                else
                {
                    fieldFormatted = (goTR.GetFieldsFromLine(TableName, Field)).ToUpper();

                    if (!cFieldDefs.Contains(fieldFormatted))
                    {
                        if (Fields == "")
                        {
                            Fields = fieldFormatted;
                        }
                        else
                        {
                            Fields = Fields + ", " + fieldFormatted;
                        }

                        if (fieldFormatted.Substring(0, 4).ToUpper() == "TEL_")
                        {

                        }

                        if (DisplayAsIcon == 1)
                        {
                            switch (fieldFormatted.Substring(0, 4).ToUpper())
                            {
                                case "CHK_":
                                case "MLS_":
                                case "LNK_":
                                    break;
                                default:
                                    if (fieldFormatted.ToUpper() != "SI__SHARESTATE")
                                    {
                                        DisplayAsIcon = 0;
                                    }
                                    break;

                            }
                        }

                        if (DisplayAsIcon == 1 || DisplayAsLink == 1)
                        {
                            string Link = fieldFormatted.ToUpper();
                            if (fieldFormatted.Substring(0, 4) == "LNK_" && goTR.ExtractString(Link, 2, "%%") != "GID_ID")
                            {
                                Field = Field + "#$%^&*<%" + goTR.ExtractString(Link, 1, "%%") + "%%GID_ID%>";
                                Fields = Fields + ", " + goTR.ExtractString(Link, 1, "%%") + "%%GID_ID";
                            }
                        }


                        switch (Allignment)
                        {
                            case "L":
                                Allignment = "Left";
                                break;
                            case "R":
                                Allignment = "Right";
                                break;
                            case "C":
                                Allignment = "Center";
                                break;
                        }

                        FieldsDef = fieldFormatted + " " +
                            Label + " " +
                            Allignment + " " +
                            cWidth.ToString() + " " +
                            DisplayAsIcon.ToString() + " " +
                            DisplayAsLink.ToString() + " " +
                            Field + " " +
                            total.ToString() + " " +
                            ColTextLength.ToString() + " " +
                            Ellipsis.ToString() + " " +
                            average.ToString() + " " +
                            minimum.ToString() + " " +
                            maximum.ToString() + " " +
                            median.ToString() + " " +
                            percent.ToString();

                        cFieldDefs.Add(FieldsDef, fieldFormatted);
                    }
                }
            }

            return cFieldDefs;
        }

        public void GetChartData(string TableName, string Filter, string OrderBy, string _viewId, string _key)
        {
            string sGraphYShow = goTR.StrRead(_viewMetaData, "GRAPHYSHOW", "COUNT", false);
            string sGenFieldList = "";

            clRowSet _clRowSet;

            int graphAverage = 0;
            int graphMin = 0;
            int graphMax = 0;
            int graphMed = 0;

            int iYFields = 0;

            if (Columns.Count > 0 || sGraphYShow == "COUNT")
            {
                for (int i = 1; i <= 4; i++)
                {
                    bool go = false;
                    string field = Strings.UCase(goTR.StrRead(_viewMetaData, "GRAPHYFIELD" + i.ToString(), "NONE", false));
                    if (field == "NONE" | string.IsNullOrEmpty(field) | !godata.IsFieldValid(TableName, field))
                    {
                        //now we are searching additional fields...need to pick only the aggregate ones for chart.
                        go = false;
                    }
                    else
                    {
                        iYFields = iYFields + 1;
                        go = true;
                    }

                    if (!go)
                        goto GetNextYField;
                    if (go)
                    {
                        string sNewField = "";
                        switch (sGraphYShow)
                        {
                            case "COUNT":
                                //do nothing yet

                                sNewField = field + "|SUM";
                                if (string.IsNullOrEmpty(sGenFieldList))
                                {
                                    sGenFieldList = sNewField;
                                }
                                else
                                {
                                    sGenFieldList = sGenFieldList + ", " + sNewField;
                                }

                                break;
                            case "STATISTICS":

                                if (i > 1)
                                    goto GetNextYField;

                                //ALWAYS GIVE THEM TOTAL
                                sNewField = field + "|SUM";
                                if (string.IsNullOrEmpty(sGenFieldList))
                                {
                                    sGenFieldList = sNewField;
                                }
                                else
                                {
                                    sGenFieldList = sGenFieldList + ", " + sNewField;
                                }
                                int par_iValid = 4;
                                graphAverage = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHYAVG", "1", false), "", ref par_iValid, ""));
                                graphMin = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHYMIN", "0", false), "", ref par_iValid, ""));
                                graphMax = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHYMAX", "0", false), "", ref par_iValid, ""));
                                graphMed = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(_viewMetaData, "GRAPHYMED", "0", false), "", ref par_iValid, ""));

                                if (graphAverage == 1)
                                    sNewField = field + "|AVG";
                                if (string.IsNullOrEmpty(sGenFieldList))
                                {
                                    sGenFieldList = sNewField;
                                }
                                else
                                {
                                    sGenFieldList = sGenFieldList + ", " + sNewField;
                                }

                                if (graphMin == 1)
                                    sNewField = field + "|MIN";
                                if (string.IsNullOrEmpty(sGenFieldList))
                                {
                                    sGenFieldList = sNewField;
                                }
                                else
                                {
                                    sGenFieldList = sGenFieldList + ", " + sNewField;
                                }

                                if (graphMax == 1)
                                    sNewField = field + "|MAX";
                                if (string.IsNullOrEmpty(sGenFieldList))
                                {
                                    sGenFieldList = sNewField;
                                }
                                else
                                {
                                    sGenFieldList = sGenFieldList + ", " + sNewField;
                                }

                                if (graphMed == 1)
                                    sNewField = field + "|MED";
                                if (string.IsNullOrEmpty(sGenFieldList))
                                {
                                    sGenFieldList = sNewField;
                                }
                                else
                                {
                                    sGenFieldList = sGenFieldList + ", " + sNewField;
                                }

                                break;
                            case "TOTAL":
                                sNewField = field + "|SUM";
                                if (string.IsNullOrEmpty(sGenFieldList))
                                {
                                    sGenFieldList = sNewField;
                                }
                                else
                                {
                                    sGenFieldList = sGenFieldList + ", " + sNewField;
                                }

                                break;
                            default:
                                sNewField = field + "|SUM";
                                if (string.IsNullOrEmpty(sGenFieldList))
                                {
                                    sGenFieldList = sNewField;
                                }
                                else
                                {
                                    sGenFieldList = sGenFieldList + ", " + sNewField;
                                }

                                break;
                        }
                    }

                    GetNextYField: continue;


                }

                for (int j = (iYFields + 1); j <= Columns.Count; j++)
                {
                    bool bGo = false;

                    //now we are searching additional fields...need to pick only the aggregate ones for chart.

                    string sField = Columns[j].ToString();
                    sField = goTR.ExtractString(sField, 1, " ");

                    //int iTotal = Convert.ToInt32(goTR.ExtractString(Columns[j].ToString(), 8, " "));
                    //graphAverage = Convert.ToInt32(goTR.ExtractString(Columns[j].ToString(), 11, " "));
                    //graphMin = Convert.ToInt32(goTR.ExtractString(Columns[j].ToString(), 12, " "));
                    //graphMax = Convert.ToInt32(goTR.ExtractString(Columns[j].ToString(), 13, " "));
                    //graphMed = Convert.ToInt32(goTR.ExtractString(Columns[j].ToString(), 14, " "));

                    int par_iValid = 4;
                    int iTotal = Convert.ToInt32(goTR.StringToNum(goTR.ExtractString(Columns[j].ToString(), 8, " "), "", ref par_iValid, ""));
                    graphAverage = Convert.ToInt32(goTR.StringToNum(goTR.ExtractString(Columns[j].ToString(), 11, " "), "", ref par_iValid, ""));
                    graphMin = Convert.ToInt32(goTR.StringToNum(goTR.ExtractString(Columns[j].ToString(), 12, " "), "", ref par_iValid, ""));
                    graphMax = Convert.ToInt32(goTR.StringToNum(goTR.ExtractString(Columns[j].ToString(), 13, " "), "", ref par_iValid, ""));
                    graphMed = Convert.ToInt32(goTR.StringToNum(goTR.ExtractString(Columns[j].ToString(), 14, " "), "", ref par_iValid, ""));


                    string sNewField = "";

                    if (iTotal > 1)                               //need check it one more time
                    {
                        sNewField = sField + "|SUM";
                        if (string.IsNullOrEmpty(sGenFieldList))
                        {
                            sGenFieldList = sNewField;
                        }
                        else
                        {
                            sGenFieldList = sGenFieldList + ", " + sNewField;
                        }
                    }

                    if (graphAverage == 1)
                    {
                        sNewField = sField + "|AVG";
                        if (string.IsNullOrEmpty(sGenFieldList))
                        {
                            sGenFieldList = sNewField;
                        }
                        else
                        {
                            sGenFieldList = sGenFieldList + ", " + sNewField;
                        }
                    }

                    if (graphMin == 1)
                    {
                        sNewField = sField + "|MIN";
                        if (string.IsNullOrEmpty(sGenFieldList))
                        {
                            sGenFieldList = sNewField;
                        }
                        else
                        {
                            sGenFieldList = sGenFieldList + ", " + sNewField;
                        }
                    }

                    if (graphMax == 1)
                    {
                        sNewField = sField + "|MAX";
                        if (string.IsNullOrEmpty(sGenFieldList))
                        {
                            sGenFieldList = sNewField;
                        }
                        else
                        {
                            sGenFieldList = sGenFieldList + ", " + sNewField;
                        }
                    }

                    if (graphMed == 1)
                    {
                        sNewField = sField + "|MED";
                        if (string.IsNullOrEmpty(sGenFieldList))
                        {
                            sGenFieldList = sNewField;
                        }
                        else
                        {
                            sGenFieldList = sGenFieldList + ", " + sNewField;
                        }
                    }

                }

                //commented out in attempt to support aggregate only view
                if (string.IsNullOrEmpty(sGenFieldList))
                {
                    sGenFieldList = "BI__GROUPBYCOUNT";
                }
                else
                {
                    sGenFieldList = sGenFieldList + ", BI__GROUPBYCOUNT";
                }

                string sRollupSort = "";
                string sSendOnlyGroupedSorts = "1";

                if (sSendOnlyGroupedSorts == "1")
                {
                    for (int x = 1; x <= Sorts.Count; x++)
                    {
                        string sDef = Sorts[x].ToString();
                        string sField = goTR.GetFieldsFromLine(TableName, goTR.ExtractString(sDef, 1, " "));
                        int iGraphXSortFields = Convert.ToInt32(goTR.StrRead(_viewMetaData, "GRAPHXSORTFIELDS", "0"));
                        if (iGraphXSortFields >= x)
                        {
                            switch (sRollupSort)
                            {
                                case "":
                                    sRollupSort = sField + " " + GetSortFieldDirection(sField);
                                    break;
                                default:
                                    sRollupSort = sRollupSort + ", " + sField + " " + GetSortFieldDirection(sField);
                                    break;
                            }
                        }
                    }
                    if (sRollupSort == "")
                    {
                        sRollupSort = godata.GetDefaultSort(TableName);
                    }
                }
                else
                {
                    sRollupSort = OrderBy;
                }

                //is there a record to delete?  if so, delete it and set next record as selectedrecord
                string sRecToDelete = "";
                string sDelete = goTR.StrRead(_viewMetaData, "DELETE", "False");
                if (sDelete == "True")
                    sRecToDelete = goTR.StrRead(_viewMetaData, "SELECTEDRECORDID", "");
                if (!string.IsNullOrEmpty(sRecToDelete))
                {
                    switch (Filter)
                    {
                        case "":
                            Filter = "GID_ID<>'" + sRecToDelete + "'";
                            break;
                        default:
                            Filter = "(" + Filter + ")" + " AND GID_ID<>'" + sRecToDelete + "'";
                            break;
                    }
                }

                _clRowSet = new clRowSet(TableName, clC.SELL_GROUPBYWITHROLLUP, Filter, sRollupSort, sGenFieldList, -1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 300);
                LoadTime = _clRowSet.lSQLLoadTime / 1000;
                RecordViewExcessiveLoadTime(LoadTime);

                //let's first check the rowcount
                if (_clRowSet.Count() > clC.SELL_CHARTVIEW_SHOWTOPMAX)
                {
                    ChartReportTooLong = true;
                    LblIssueText = "Can't display the view because it has more than " + goTR.NumToString(clC.SELL_CHARTVIEW_SHOWTOPMAX) + " records. Modify the filter to return less data.";
                    return;
                }

                _clRowSet.ToTable();
                GroupDataTable = _clRowSet.dtTransTable;

                GetChartData2(1, GroupDataTable, sGraphYShow, sRollupSort);

                //if (bGroupbedBars)
                //{
                //    Util.SetSessionValue(Key + "_" + ViewKey + "_dt", GroupDataTable);
                //}
            }
        }

        public string GetSortFieldDirection(string sField)
        {
            int i = 0;
            string sSortField = null;
            string sSortDirection = null;
            for (i = 1; i <= SortFields.Count; i++)
            {
                sSortField = SortFields[i].ToString();
                sSortField = goTR.ExtractString(sSortField, 1, "|");
                if ((sSortField).ToUpper() == Strings.UCase(sField))
                {
                    sSortDirection = goTR.ExtractString(SortFields[i].ToString(), 2, "|");
                    sSortDirection = Strings.UCase(sSortDirection);
                    if (string.IsNullOrEmpty(sSortDirection))
                        sSortDirection = "ASC";
                    if (sSortDirection == "A")
                        sSortDirection = "ASC";
                    return sSortDirection;
                }
            }
            return "";
        }

        public void RecordViewExcessiveLoadTime(double dLoadTime)
        {
            if (dLoadTime > 60)
            {
                string sIniString = "";
                string sMeta = goTR.GetFilterSortMetadata(_viewMetaData, "", "SORT", true);
                string[] aMeta = Strings.Split(sMeta, Constants.vbCrLf);
                int i = 0;
                string sValue = null;
                //string sProperties = "";
                for (i = 0; i <= aMeta.Length - 1; i++)
                {
                    if (Strings.InStr(aMeta[i], "=") > 0)
                    {
                        string sProp = goTR.ExtractString(aMeta[i], 1, "=");
                        sValue = goTR.ExtractString(aMeta[i], 2, "=");
                        goTR.StrWrite(ref sIniString, sProp, sValue);
                    }
                }
                string sReturn = "";
                goTR.StrWrite(ref sReturn, "Load Time", LoadTime);

            }
        }


        public void GetChartData2(int iIteration, DataTable oDT, string sGraphYShow, string sSort, string sSort1Value = "", string sSort2Value = "", string sSort3Value = "", int iParent1Index = 0, int iParent2Index = 0, int iParent3Index = 0)
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //try
            //{
            string sField = null;
            int iGraphAverage = 0;
            int iGraphMin = 0;
            int iGraphMax = 0;
            int iGraphMed = 0;
            int iGraphXSortFields = Convert.ToInt32(goTR.StrRead(_viewMetaData, "GRAPHXSORTFIELDS", "0"));

            int iSortFields = Sorts.Count;

            //string sSendOnlyGroupedSorts = goDesktop.GetPOPValue("SENDONLYGROUPEDSORTS");
            //sSendOnlyGroupedSorts = "1";
            //if (sSendOnlyGroupedSorts == "1") {
            //    iSortFields = iGraphXSortFields;
            //    //iGroupCount
            //}

            iSortFields = iGraphXSortFields;

            string sSortField = GetSortFieldByIndex(sSort, iIteration);
            bool bIsDTX = false;
            switch (Strings.Left(sSortField, 4))
            {
                case "DTY_":
                case "DTQ_":
                case "DTM_":
                case "DTD_":
                    bIsDTX = true;
                    break;
                default:
                    bIsDTX = false;
                    break;
            }


            if (iGraphXSortFields > 0)
            {
                string sSelect = "";

                switch (iIteration)
                {
                    case 1:

                        RollupTotal = GetRollupTotalCount(oDT, sSort);

                        sSelect = "[INT_" + GetSortFieldByIndex(sSort, 1) + "_GROUPING]=0";
                        if (iSortFields > 1)
                            sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 2) + "_GROUPING]=1";
                        if (iSortFields > 2)
                            sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 3) + "_GROUPING]=1";
                        if (iSortFields > 3)
                            sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 4) + "_GROUPING]=1";
                        break;
                    case 2:
                        sSelect = "[" + GetSortFieldByIndex(sSort, 1) + "]='" + sSort1Value.Replace("'", "''") + "'";
                        sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 1) + "_GROUPING]=0";
                        sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 2) + "_GROUPING]=0";
                        if (iSortFields > 2)
                            sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 3) + "_GROUPING]=1";
                        if (iSortFields > 3)
                            sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 4) + "_GROUPING]=1";

                        break;
                    case 3:
                        sSelect = "[" + GetSortFieldByIndex(sSort, 1) + "]='" + sSort1Value.Replace("'", "''") + "'";
                        sSelect = sSelect + " AND [" + GetSortFieldByIndex(sSort, 2) + "]='" + sSort2Value.Replace("'", "''") + "'";
                        sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 1) + "_GROUPING]=0";
                        sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 2) + "_GROUPING]=0";
                        sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 3) + "_GROUPING]=0";
                        if (iSortFields > 3)
                            sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 4) + "_GROUPING]=1";

                        break;
                    case 4:
                        sSelect = "[" + GetSortFieldByIndex(sSort, 1) + "]='" + sSort1Value.Replace("'", "''") + "'";
                        sSelect = sSelect + " AND [" + GetSortFieldByIndex(sSort, 2) + "]='" + sSort2Value.Replace("'", "''") + "'";
                        sSelect = sSelect + " AND [" + GetSortFieldByIndex(sSort, 3) + "]='" + sSort3Value.Replace("'", "''") + "'";
                        sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 1) + "_GROUPING]=0";
                        sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 2) + "_GROUPING]=0";
                        sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 3) + "_GROUPING]=0";
                        sSelect = sSelect + " AND [INT_" + GetSortFieldByIndex(sSort, 4) + "_GROUPING]=0";

                        break;
                }

                AddChartFilter(iParent1Index, iParent2Index, iParent3Index, GetSortFieldByIndex(sSort, iIteration), sSelect);

                DataRow[] oRows = oDT.Select(sSelect);
                //DataRow oRow = default(DataRow);
                string sValue = "";
                string sLabel = "";
                int iRowCount = 0;


                foreach (DataRow oRow in oRows)
                {
                    iRowCount = iRowCount + 1;


                    if (sGraphYShow == "COUNT")
                    {
                        //if (goTR.StrRead(_viewMetaData, "GRAPHSHOWDATAPOINTVALUE", "0", false) == "1")
                        //{
                        //    ShowDataPoints = true;
                        //}

                        //string sPercent = "";
                        //if (ShowDataPoints)
                        //{
                        //    //lPercent = Convert.ToDecimal((dCount / _chart.RollupTotal) * 100);
                        //    //sPercent = " (" + goTR.NumToString(lPercent, "1") + "%" + ")";
                        //    sPercent = " (" + oRow[1].ToString() + "%" + ")";
                        //    sPercent = sPercent.Replace("$", "");

                        //    sLabel = oRow.Field<string>(sSortField) + ": " + sPercent;
                        //}
                        //else
                        //{
                        //    sLabel = oRow.Field<string>(sSortField);
                        //}
                        //sValue = oRow.Field<string>("BI__GROUPBYCOUNT") + " " + sPercent;
                        sValue = oRow.Field<string>("BI__GROUPBYCOUNT");
                        sLabel = oRow.Field<string>(sSortField);

                        double dDate = 0.0;
                        if (bIsDTX)
                        {
                            if (!string.IsNullOrEmpty(sLabel))
                            {
                                string sLastDate = sLabel;
                                //if (sLastDate.Contains(":"))
                                //{
                                //    sLastDate = sLastDate.Substring(0, sLastDate.IndexOf(':'));
                                //}
                                //dLastDate = Convert.ToDateTime(sLastDate).ToOADate();
                                DateTime oDTXasDate = goTR.GetDateTimeFromDTxLabel(sLastDate, sSortField, ref a1);
                                dDate = oDTXasDate.Add(DateTime.Now.TimeOfDay).ToOADate();
                            }
                            else
                            {
                                //this is not a valid date, chart control will not display
                            }
                        }

                        switch (iIteration)
                        {
                            case 1:
                                AddChartData("", 1, 0, 0, 0, sLabel, Convert.ToDouble(sValue), 0, 0, 0, 0, 0, dDate);
                                if (iGraphXSortFields > 1)
                                {
                                    GetChartData2(2, oDT, sGraphYShow, sSort, sLabel, "", "", iRowCount);
                                }
                                break;
                            case 2:
                                AddChartData("", 2, iParent1Index, 0, 0, sLabel, Convert.ToDouble(sValue), 0, 0, 0,
                                0, 0, dDate);
                                if (iGraphXSortFields > 2)
                                {
                                    GetChartData2(3, oDT, sGraphYShow, sSort, sSort1Value, sLabel, "", iParent1Index, iRowCount);
                                }
                                break;
                            case 3:
                                AddChartData("", 3, iParent1Index, iParent2Index, 0, sLabel, Convert.ToDouble(sValue), 0, 0, 0,
                                0, 0, dDate);
                                if (iGraphXSortFields > 3)
                                {
                                    GetChartData2(4, oDT, sGraphYShow, sSort, sSort1Value, sSort2Value, sLabel, iParent1Index, iParent2Index, iRowCount);
                                }
                                break;
                            case 4:
                                AddChartData("", 4, iParent1Index, iParent2Index, iParent3Index, sLabel, Convert.ToDouble(sValue), 0, 0, 0,
                                0, 0, dDate);
                                break;
                        }


                    }
                    else if (sGraphYShow == "STATISTICS")
                    {
                        iGraphAverage = Convert.ToInt32(goTR.StrRead(_viewMetaData, "GRAPHYAVG", "1", false));
                        iGraphMin = Convert.ToInt32(goTR.StrRead(_viewMetaData, "GRAPHYMIN", "0", false));
                        iGraphMax = Convert.ToInt32(goTR.StrRead(_viewMetaData, "GRAPHYMAX", "0", false));
                        iGraphMed = Convert.ToInt32(goTR.StrRead(_viewMetaData, "GRAPHYMED", "0", false));

                        string sAvg = "0";
                        string sMin = "0";
                        string sMax = "0";
                        string sMed = "0";

                        sField = Strings.UCase(goTR.StrRead(_viewMetaData, "GRAPHYFIELD1", "NONE", false));

                        if (sField == "NONE" | string.IsNullOrEmpty(sField) | !godata.IsFieldValid(TableName, sField))
                        {

                        }
                        else
                        {
                            if (iGraphAverage == 1)
                                sAvg = oRow.Field<string>(sField + "|AVG");
                            if (iGraphMin == 1)
                                sMin = oRow.Field<string>(sField + "|MIN");
                            if (iGraphMax == 1)
                                sMax = oRow.Field<string>(sField + "|MAX");
                            if (iGraphMed == 1)
                                sMed = oRow.Field<string>(sField + "|MED");
                            sLabel = oRow.Field<string>(GetSortFieldByIndex(sSort, iIteration));

                            double dDate = 0.0;
                            if (bIsDTX)
                            {
                                if (!string.IsNullOrEmpty(sLabel))
                                {
                                    DateTime oDTXasDate = goTR.GetDateTimeFromDTxLabel(sLabel, sSortField, ref a1);
                                    dDate = oDTXasDate.Add(DateTime.Now.TimeOfDay).ToOADate();
                                }
                                else
                                {
                                    //this is not a valid date, chart control will not display
                                }
                            }

                            string sFormat = "";
                            switch (Strings.Left(sField, 4))
                            {
                                case "CUR_":
                                    sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                    break;
                                case "SR__":
                                    sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                    break;
                                case "DR__":
                                    sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                    break;
                            }

                            switch (iIteration)
                            {
                                case 1:
                                    AddChartData(sField, 1, 0, 0, 0, sLabel, 0, 0, Convert.ToDouble(goTR.StringToCurr(sAvg, sFormat, ref a1)), Convert.ToDouble(goTR.StringToCurr(sMin, sFormat, ref a1)),
                                    Convert.ToDouble(goTR.StringToCurr(sMax, sFormat, ref a1)), Convert.ToDouble(goTR.StringToCurr(sMed, sFormat, ref a1)), dDate);
                                    if (iGraphXSortFields > 1)
                                    {
                                        GetChartData2(2, oDT, sGraphYShow, sSort, sLabel, "", "", iRowCount);
                                    }
                                    break;
                                case 2:
                                    AddChartData(sField, 2, iParent1Index, 0, 0, sLabel, 0, 0, Convert.ToDouble(goTR.StringToCurr(sAvg, sFormat, ref a1)), Convert.ToDouble(goTR.StringToCurr(sMin, sFormat, ref a1)),
                                    Convert.ToDouble(goTR.StringToCurr(sMax, sFormat, ref a1)), Convert.ToDouble(goTR.StringToCurr(sMed, sFormat, ref a1)), dDate);
                                    if (iGraphXSortFields > 2)
                                    {
                                        GetChartData2(3, oDT, sGraphYShow, sSort, sSort1Value, sLabel, "", iParent1Index, iRowCount);
                                    }
                                    break;
                                case 3:
                                    AddChartData(sField, 3, iParent1Index, iParent2Index, 0, sLabel, 0, 0, Convert.ToDouble(goTR.StringToCurr(sAvg, sFormat, ref a1)), Convert.ToDouble(goTR.StringToCurr(sMin, sFormat, ref a1)),
                                    Convert.ToDouble(goTR.StringToCurr(sMax, sFormat, ref a1)), Convert.ToDouble(goTR.StringToCurr(sMed, sFormat, ref a1)), dDate);
                                    if (iGraphXSortFields > 3)
                                    {
                                        GetChartData2(4, oDT, sGraphYShow, sSort, sSort1Value, sSort2Value, sLabel, iParent1Index, iParent2Index, iRowCount);
                                    }
                                    break;
                                case 4:
                                    AddChartData(sField, 4, iParent1Index, iParent2Index, iParent3Index, sLabel, 0, 0, Convert.ToDouble(goTR.StringToCurr(sAvg, sFormat, ref a1)), Convert.ToDouble(goTR.StringToCurr(sMin, sFormat, ref a1)),
                                    Convert.ToDouble(goTR.StringToCurr(sMax, sFormat, ref a1)), Convert.ToDouble(goTR.StringToCurr(sMed, sFormat, ref a1)), dDate);
                                    break;
                            }
                        }


                    }
                    else
                    {
                        switch (iIteration)
                        {
                            case 1:


                                for (int j = 1; j <= 4; j++)
                                {
                                    sField = Strings.UCase(goTR.StrRead(_viewMetaData, "GRAPHYFIELD" + j, "NONE", false));

                                    if (sField == "NONE" | string.IsNullOrEmpty(sField) | !godata.IsFieldValid(TableName, sField))
                                    {

                                    }
                                    else
                                    {
                                        string sFormat = "";
                                        switch (Strings.Left(sField, 4))
                                        {
                                            case "CUR_":
                                                sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                                break;
                                            case "SR__":
                                                sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                                break;
                                            case "DR__":
                                                sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                                break;
                                        }

                                        sValue = oRow.Field<string>(sField + "|SUM");
                                        double dValue = Convert.ToDouble(goTR.StringToCurr(sValue, sFormat, ref a1));

                                        sLabel = oRow.Field<string>(GetSortFieldByIndex(sSort, iIteration)); //oRow(GetSortFieldByIndex(sSort, iIteration));

                                        double dDate = 0.0;
                                        if (bIsDTX)
                                        {
                                            if (!string.IsNullOrEmpty(sLabel))
                                            {
                                                DateTime oDTXasDate = goTR.GetDateTimeFromDTxLabel(sLabel, sSortField, ref a1);
                                                dDate = oDTXasDate.Add(DateTime.Now.TimeOfDay).ToOADate();
                                            }
                                            else
                                            {
                                                //this is not a valid date, chart control will not display
                                            }
                                        }

                                        AddChartData(sField, 1, 0, 0, 0, sLabel, 0, dValue, 0, 0,
                                        0, 0, dDate);
                                    }
                                }

                                if (iGraphXSortFields > 1)
                                {
                                    GetChartData2(2, oDT, sGraphYShow, sSort, sLabel, "", "", iRowCount);
                                }
                                break;
                            case 2:

                                for (int j = 1; j <= 4; j++)
                                {
                                    sField = Strings.UCase(goTR.StrRead(_viewMetaData, "GRAPHYFIELD" + j, "NONE", false));

                                    if (sField == "NONE" | string.IsNullOrEmpty(sField) | !godata.IsFieldValid(TableName, sField))
                                    {

                                    }
                                    else
                                    {
                                        string sFormat = "";
                                        switch (Strings.Left(sField, 4))
                                        {
                                            case "CUR_":
                                                sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                                break;
                                            case "SR__":
                                                sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                                break;
                                            case "DR__":
                                                sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                                break;
                                        }

                                        sValue = oRow.Field<string>(sField + "|SUM");//  (sField + "|SUM");
                                        double dValue = Convert.ToDouble(goTR.StringToCurr(sValue, sFormat, ref a1));
                                        sLabel = oRow.Field<string>(GetSortFieldByIndex(sSort, iIteration));

                                        double dDate = 0.0;
                                        if (bIsDTX)
                                        {
                                            if (!string.IsNullOrEmpty(sLabel))
                                            {
                                                DateTime oDTXasDate = goTR.GetDateTimeFromDTxLabel(sLabel, sSortField, ref a1);
                                                dDate = oDTXasDate.Add(DateTime.Now.TimeOfDay).ToOADate();
                                            }
                                            else
                                            {
                                                //this is not a valid date, chart control will not display
                                            }
                                        }

                                        AddChartData(sField, 2, iParent1Index, 0, 0, sLabel, 0, dValue, 0, 0,
                                        0, 0, dDate);
                                    }
                                }

                                if (iGraphXSortFields > 2)
                                {
                                    GetChartData2(3, oDT, sGraphYShow, sSort, sSort1Value, sLabel, "", iParent1Index, iRowCount);
                                }
                                break;
                            case 3:

                                for (int j = 1; j <= 4; j++)
                                {
                                    sField = Strings.UCase(goTR.StrRead(_viewMetaData, "GRAPHYFIELD" + j, "NONE", false));

                                    if (sField == "NONE" | string.IsNullOrEmpty(sField) | !godata.IsFieldValid(TableName, sField))
                                    {

                                    }
                                    else
                                    {
                                        string sFormat = "";
                                        switch (Strings.Left(sField, 4))
                                        {
                                            case "CUR_":
                                                sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                                break;
                                            case "SR__":
                                                sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                                break;
                                            case "DR__":
                                                sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                                break;
                                        }
                                        int a = 0;


                                        sValue = oRow.Field<string>(sField + "|SUM");
                                        double dValue = Convert.ToDouble(goTR.StringToCurr(sValue, sFormat, ref a, ""));
                                        sLabel = oRow.Field<string>(GetSortFieldByIndex(sSort, iIteration));

                                        double dDate = 0.0;
                                        if (bIsDTX)
                                        {
                                            if (!string.IsNullOrEmpty(sLabel))
                                            {

                                                DateTime oDTXasDate = goTR.GetDateTimeFromDTxLabel(sLabel, sSortField, ref a);
                                                dDate = oDTXasDate.Add(DateTime.Now.TimeOfDay).ToOADate();
                                            }
                                            else
                                            {
                                                //this is not a valid date, chart control will not display
                                            }
                                        }

                                        AddChartData(sField, 3, iParent1Index, iParent2Index, 0, sLabel, 0, dValue, 0, 0,
                                        0, 0, dDate);
                                    }
                                }

                                if (iGraphXSortFields > 3)
                                {
                                    GetChartData2(4, oDT, sGraphYShow, sSort, sSort1Value, sSort2Value, sLabel, iParent1Index, iParent2Index, iRowCount);
                                }
                                break;
                            case 4:

                                for (int j = 1; j <= 4; j++)
                                {
                                    sField = Strings.UCase(goTR.StrRead(_viewMetaData, "GRAPHYFIELD" + j, "NONE", false));

                                    if (sField == "NONE" | string.IsNullOrEmpty(sField) | !godata.IsFieldValid(TableName, sField))
                                    {

                                    }
                                    else
                                    {
                                        string sFormat = "";
                                        switch (Strings.Left(sField, 4))
                                        {
                                            case "CUR_":
                                                sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                                break;
                                            case "SR__":
                                                sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                                break;
                                            case "DR__":
                                                sFormat = goMeta.LineRead("GLOBAL", "FLD_" + TableName, sField + "_FRM", "Def|Def|Def|Def|Def|Def|Def");
                                                break;
                                        }

                                        sValue = oRow.Field<string>(sField + "|SUM");
                                        double dValue = Convert.ToDouble(goTR.StringToCurr(sValue, sFormat, ref a1));
                                        sLabel = oRow.Field<string>(GetSortFieldByIndex(sSort, iIteration));

                                        double dDate = 0.0;
                                        if (bIsDTX)
                                        {
                                            if (!string.IsNullOrEmpty(sLabel))
                                            {
                                                DateTime oDTXasDate = goTR.GetDateTimeFromDTxLabel(sLabel, sSortField, ref a1);
                                                dDate = oDTXasDate.Add(DateTime.Now.TimeOfDay).ToOADate();
                                            }
                                            else
                                            {
                                                //this is not a valid date, chart control will not display
                                            }
                                        }

                                        AddChartData(sField, 4, iParent1Index, iParent2Index, iParent3Index, sLabel, 0, Convert.ToDouble(sValue), 0, 0,
                                        0, 0, dDate);
                                    }
                                }

                                break;
                        }

                    }
                    //goto GetNext;

                }

            }

            //}
            //catch (Exception ex)
            //{
            //    return;
            //}
        }

        public string GetSortFieldByIndex(string sOrder, int iIndex)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //try
            //{
            Array aOrder = default(Array);
            int iPos = 0;
            aOrder = Strings.Split(sOrder, ", ");
            if (aOrder.Length < iIndex)
                return "";
            //sOrder = Convert.ToString(Array.IndexOf(aOrder, iIndex - 1));
            sOrder = ((string[])(aOrder))[iIndex - 1];

            iPos = Strings.InStr(Strings.UCase(sOrder), " A");
            if (iPos > 0)
            {
                sOrder = goTR.FromTo(sOrder, 1, iPos - 1);
            }
            iPos = Strings.InStr(Strings.UCase(sOrder), " D");
            if (iPos > 0)
            {
                sOrder = goTR.FromTo(sOrder, 1, iPos - 1);
            }
            return sOrder;
            //}
            //catch (Exception ex)
            //{

            //    return "";
            //}
        }

        public bool AddChartData(string sField, int iSortField, int iParent1Index, int iParent2Index, int iParent3Index, string sLabel, double dCount, double dTotal, double dAverage, double dMin,
            double dMax, double dMed, double dDate = 0, bool bFiller = false)
        {

            try
            {
                ChartData oData = new ChartData();
                oData.sField = sField;
                oData.iSortField = iSortField;
                oData.iParent1Index = iParent1Index;
                oData.iParent2Index = iParent2Index;
                oData.iParent3Index = iParent3Index;
                oData.sLabel = sLabel;
                oData.dCount = dCount;
                oData.dTotal = dTotal;
                oData.dAverage = dAverage;
                oData.dMin = dMin;
                oData.dMax = dMax;
                oData.dMed = dMed;
                oData.dDate = dDate;
                gcChartData.Add(oData);

                return true;
            }
            catch (Exception ex)
            {

                return false;
            }
        }


        public struct ChartData
        {
            public string sField;
            public int iSortField;
            public int iParent1Index;
            public int iParent2Index;
            public int iParent3Index;
            public string sLabel;
            public double dCount;
            public double dTotal;
            public double dAverage;
            public double dMin;
            public double dMax;
            public double dMed;
            public double dDate;
        }

        public struct ChartFilters
        {
            public string sSortField;
            public string sFilter;
        }

        public int GetRollupTotalCount(DataTable oDT, string sSort)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            try
            {
                int iReturn = 0;
                string sSelect = "[INT_" + GetSortFieldByIndex(sSort, 1) + "_GROUPING]=1";
                DataRow[] oRows = oDT.Select(sSelect);
                foreach (DataRow oRow in oRows)
                {
                    string sTotal = oRow.Field<string>("BI__GROUPBYCOUNT");
                    iReturn = Convert.ToInt32(sTotal);
                    break; // TODO: might not be correct. Was : Exit For
                }
                return iReturn;
            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        public bool AddChartFilter(int iParent1Index, int iParent2Index, int iParent3Index, string sSortField, string sSelect)
        {
            string sKey = iParent1Index + "-" + iParent2Index + "-" + iParent3Index;
            ChartFilters oFilter = new ChartFilters();
            oFilter.sSortField = sSortField;
            oFilter.sFilter = sSelect;
            gcChartFilters.Add(oFilter, sKey);

            return true;
        }

    }


}

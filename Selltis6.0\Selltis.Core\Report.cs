﻿using Microsoft.VisualBasic;
using Selltis.BusinessLogic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Selltis.Core
{
    public class Report : View
    {
        private clTransform goTR;
        private clData godata;
        private clMetaData goMeta;
        private clDefaults goDef;
        private bool isLoadFromSession = true;
        private string _viewMetaData = string.Empty;
        public string GroupedColumns { get; set; }
        public string PercentColumns { get; set; }
        public string AggregateColumns { get; set; }
        public string CURSymbols { get; set; }
        public Dictionary<string, string> HashContainedColumns { get; set; }
        public int GroupByFieldsCount { get; set; }
        private bool scrollable;
        public bool IsNotOverFlow { get; set; }
        public bool IsInvalid { get; set; }
        public bool Scrollable
        {
            get
            {
                scrollable = true;
                if (Height == 0)
                    scrollable = false;
                return scrollable;
            }
            set
            {
                scrollable = value;
            }
        }
        public int DISPLAYSECTIONS { get; set; }
        public int FirstTdPossibleWidth { get; set; }
        private bool StopCalculatingFirstColPossibleWidth = false;
        public Report(string _viewId, bool _isTabView, string _section, string _desktopmetadata, int _index, bool _isLoadFromSession = true, int _TotalIndex = 0, string _key = "")
            : base(_viewId, _isTabView, _section, _desktopmetadata, _index, _isLoadFromSession, _TotalIndex, _key)
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            godata = (clData)Util.GetInstance("data");
            goTR = (clTransform)Util.GetInstance("tr");
            GroupByFieldsCount = 0;
            isLoadFromSession = _isLoadFromSession;
            _viewMetaData = ViewMetaData;

            //if (System.Web.HttpContext.Current.Session[ViewKey] != null)
            //{
            //    SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(ViewKey);
            //    SelectedRecord = _SessionViewInfo.LastSelectedRecord;
            //    if (isLoadFromSession == true)
            //    {
            //        _viewMetaData = _SessionViewInfo.ViewMetaData;
            //    }
            //}

            if (Util.GetSessionValue(Key + "_" + ViewKey) != null && isLoadFromSession == true)
            {
                SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + ViewKey);
                SelectedRecord = _SessionViewInfo.LastSelectedRecord;
                SelectedRowIndex = _SessionViewInfo.LastSelectedRowIndex;
                _viewMetaData = _SessionViewInfo.ViewMetaData;

                //To show report too long message when report has more than 10000 records..J
                ReportTooLong = Util.IsReportTooLong(ViewKey, _SessionViewInfo.TableName, _SessionViewInfo.Fields, _SessionViewInfo.ViewMetaData, _SessionViewInfo.ViewCondition, Key);
            }            
            var gsDisplaySections = goTR.StrRead(_viewMetaData, "DISPLAYSECTIONS", "1");
            if (gsDisplaySections == "2") //FULLY COLLAPSED
            {
                DISPLAYSECTIONS = int.Parse(gsDisplaySections);

            }
            else if (gsDisplaySections == "3")//EXPANDED NO DETAIL
            {
                DISPLAYSECTIONS = int.Parse(gsDisplaySections);
            }
            else
            {
                DISPLAYSECTIONS = 1;//FULLY EXPANDED
            }

            GetColumnsCollection();
            GetSortColumnsCollection();
            if(GroupedColumns !=null)
                GroupedColumns = GroupedColumns.TrimStart(',');
            if (AggregateColumns != null)
                AggregateColumns = AggregateColumns.TrimStart(',');
            if (!string.IsNullOrEmpty(CURSymbols))
                CURSymbols = CURSymbols.TrimStart(',');

            LinksTop = Convert.ToInt32(goTR.StrRead(_viewMetaData, "LINKSTOP", "5"));
            PageSize = 10000;
            IDColumn = "GID_ID";
        }

        private string GetCurSymbolFromMeta(string strMeta)
        {
            string retval = "$";

            if (!string.IsNullOrEmpty(strMeta))
            {
                var strMetaarr = strMeta.Split('|');
                if (strMetaarr.Length > 6)
                {
                    retval = strMetaarr[6];
                }
            }

            return retval;
        }
        private void GetColumnsCollection()
        {
            goTR = (clTransform)Util.GetInstance("tr");
            int ColumnCount = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COLCOUNT", 0));
            var fldmata = goMeta.PageRead("GLOBAL", "fld_" + TableName, "", true);
            //goTR.StrRead(fldmata, "VIEWCOUNT", "0")
            Columns = new List<GridColumn>();
            IsShowSaveButton = false;
            FirstTdPossibleWidth = 0;

            //RN #1873 Multiselect functionality added.
            if (ViewMultiSelect == "1")
            {
                GridColumn _reportcolumn = new GridColumn();
                _reportcolumn.Name = "CheckBoxColumn";
                _reportcolumn.Alignment = "L";
                _reportcolumn.IsSortable = false;
                _reportcolumn.Title = " ";
                _reportcolumn.Width = 20;
                _reportcolumn.IsVisible = true;
                _reportcolumn.NameOrg = "CheckBoxColumn";
                Columns.Add(_reportcolumn);
            }
            //SB #2571 Merge Utility multiselect.
            else if (MergeMultiSelect == "1")
            {
                GridColumn _reportcolumn = new GridColumn();
                _reportcolumn.Name = "CheckBoxColumn";
                _reportcolumn.Alignment = "L";
                _reportcolumn.IsSortable = false;
                _reportcolumn.Title = " ";
                _reportcolumn.Width = 20;
                _reportcolumn.IsVisible = true;
                _reportcolumn.NameOrg = "CheckBoxColumn";
                Columns.Add(_reportcolumn);
            }

            if (ViewRecordOpen != "1" || System.Web.HttpContext.Current.Request.Browser.IsMobileDevice)
            {
                GridColumn _reportcolumn = new GridColumn();
                _reportcolumn.Name = "OpenLink";
                _reportcolumn.Alignment = "L";
                _reportcolumn.IsSortable = false;
                _reportcolumn.Title = " ";
                _reportcolumn.Width = 90;
                if (ViewMultiSelect == "1")
                {
                    _reportcolumn.Width = 40;
                }
                else if (MergeMultiSelect == "1")
                {
                    _reportcolumn.Width = 40;
                }
                _reportcolumn.IsVisible = true;
                _reportcolumn.NameOrg = "OpenLink";
                Columns.Add(_reportcolumn);
                FirstTdPossibleWidth = 90;
            }

            for (int i = 1; i <= ColumnCount; i++)//starting from 1
            {
                GridColumn _reportcolumn = new GridColumn();
                _reportcolumn.Name = goTR.StrRead(_viewMetaData, "COL" + i + "FIELD", "").ToUpper();
                _reportcolumn.NameOrg = goTR.StrRead(_viewMetaData, "COL" + i + "FIELD", "").ToUpper();
                _reportcolumn.Name =(string.IsNullOrEmpty(goTR.GetFieldsFromLine(TableName, _reportcolumn.NameOrg).ToUpper())) ? _reportcolumn.Name : goTR.GetFieldsFromLine(TableName, _reportcolumn.NameOrg).ToUpper();
                _reportcolumn.Name = (string.IsNullOrEmpty(_reportcolumn.Name.Replace("<%", string.Empty).Replace("%>", string.Empty).Replace("%%", "__")))? _reportcolumn.Name : _reportcolumn.Name.Replace("<%", string.Empty).Replace("%>", string.Empty).Replace("%%", "__");
                _reportcolumn.AllowEdit = (goTR.StrRead(_viewMetaData, "COL" + i + "MLSISEDIT", "").ToUpper() == "1" ? true : false);

                if (_reportcolumn.AllowEdit)
                {
                    IsShowSaveButton = true;
                }

              

                if (_reportcolumn.Name.Contains("#"))
                {
                    if (HashContainedColumns == null)
                    HashContainedColumns = new Dictionary<string, string>();

                    HashContainedColumns.Add(_reportcolumn.NameOrg.Replace("#", ""), _reportcolumn.NameOrg);
                    _reportcolumn.Name = _reportcolumn.Name.Replace("#", "");
                    _reportcolumn.NameOrg = _reportcolumn.NameOrg.Replace("#", "");
                }

                if( _reportcolumn.Name.Substring(0,4).Equals("CUR_"))
                {
                    var CURSymbol = _reportcolumn.Name +"~" + GetCurSymbolFromMeta(goTR.StrRead(fldmata, _reportcolumn.Name + "_FRM", ""));
                    CURSymbols = CURSymbols + ","+ CURSymbol;
                }                

                _reportcolumn.Title = goTR.StrRead(_viewMetaData, "COL" + i + "LABEL", "");
                _reportcolumn.Alignment = goTR.StrRead(_viewMetaData, "COL" + i + "ALIGNMENT", "");
                var _Width = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "WIDTH", 0) == "" ? 0 : Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "WIDTH", 0)));
                //if (i == 1 && _Width < 18)
                //{
                //    _Width = 18;
                //}
                _reportcolumn.Width = _Width * 6;

                bool _isSortable = false;
                if (!goTR.IsFieldCombined(_reportcolumn.NameOrg) && godata.IsFieldSortable(_reportcolumn.NameOrg, TableName))
                {
                    _isSortable = true;
                }
                else
                {
                    _isSortable = false;
                }

                //bool _isSortable = godata.IsFieldSortable(_gridcolumn.Name, TableName);
                _reportcolumn.IsSortable = _isSortable;

                int _isLink = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "DISPLAYASLINK", 0));
                int _isIcon = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "DISPLAYASICON", 0));
                int _isButton = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "DISPLAYASBUTTON", 0));   //To set bustton style for MLS columns..S1
                int _isIconButton = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "DISPLAYASICONBUTTON", 0));   //To set bustton style for MLS columns..S1
                _reportcolumn.IsLink = _isLink == 1 ? true : false;
                _reportcolumn.IsIcon = _isIcon == 1 ? true : false;
                _reportcolumn.IsButton = _isButton == 1 ? true : false;
                _reportcolumn.IsIconButton = _isIconButton == 1 ? true : false;
                _reportcolumn.IsVisible = true;

                //set link fields with their GID_IDs..J
                if (_isLink == 1)
                {
                    string sLink = _reportcolumn.Name;
                    if (Strings.Left(sLink, 4) == "LNK_" & goTR.ExtractString(sLink, 2, "%%") != "GID_ID")
                    {
                        //"#$%^&*"
                        //modify then gen field
                        string sFieldFormatted = goTR.StrRead(_viewMetaData, "COL" + i + "FIELD", "").ToUpper();
                        sFieldFormatted = goTR.GetFieldsFromLine(TableName, _reportcolumn.NameOrg).ToUpper();
                        _reportcolumn.NameOrg = _reportcolumn.NameOrg + "#$%^&*<%" + goTR.ExtractString(sFieldFormatted, 1, "%%") + "%%GID_ID%>";
                    }
                }

                var Total = goTR.StrRead(_viewMetaData, "COL" + i + "TOTAL", 0);
                if (Total.Equals("1"))
                {
                    AggregateColumns = AggregateColumns + "," + "SUM_"+_reportcolumn.Name;
                    _reportcolumn.IsTotal = true;
                }
                var Average = goTR.StrRead(_viewMetaData, "COL" + i + "AVERAGE", 0);
                if (Average.Equals("1"))
                {
                    AggregateColumns = AggregateColumns + "," + "AVG_" + _reportcolumn.Name;
                    _reportcolumn.IsAverage = true;
                }

                var Median = goTR.StrRead(_viewMetaData, "COL" + i + "MEDIAN", 0);
                if (Median.Equals("1"))
                {
                    AggregateColumns = AggregateColumns + "," + "MED_" + _reportcolumn.Name;
                    _reportcolumn.IsMedian = true;
                }
                var Minimum = goTR.StrRead(_viewMetaData, "COL" + i + "MINIMUM", 0);
                if (Minimum.Equals("1"))
                {
                    AggregateColumns = AggregateColumns + "," + "MIN_" + _reportcolumn.Name;
                    _reportcolumn.IsMinimum = true;
                }
                var Maximum = goTR.StrRead(_viewMetaData, "COL" + i + "MAXIMUM", 0);
                if (Maximum.Equals("1"))
                {
                    AggregateColumns = AggregateColumns + "," + "MAX_" + _reportcolumn.Name;
                    _reportcolumn.IsMaximum = true;
                }
                var Percent = goTR.StrRead(_viewMetaData, "COL" + i + "PERCENT", 0);
                if (Percent.Equals("1"))
                {
                    AggregateColumns = AggregateColumns + "," + "PER_" + _reportcolumn.Name;
                    _reportcolumn.IsPercent = true;
                }

                //To find which column has to be overlapped..
                if ((_reportcolumn.IsPercent || _reportcolumn.IsMaximum || _reportcolumn.IsMinimum || _reportcolumn.IsMedian || _reportcolumn.IsAverage || _reportcolumn.IsTotal))
                {
                    IsNotOverFlow = true;
                    StopCalculatingFirstColPossibleWidth = true;
                }
                else
                {
                    if (StopCalculatingFirstColPossibleWidth == false)
                    {
                        FirstTdPossibleWidth = FirstTdPossibleWidth + _reportcolumn.Width;
                    }                    
                }

                //if ( _reportcolumn.IsPercent || _reportcolumn.IsMaximum || _reportcolumn.IsMinimum || _reportcolumn.IsMedian || _reportcolumn.IsAverage || _reportcolumn.IsTotal)
                //{
                //    if (CurrentColumn == 0)
                //    {
                //        CurrentColumn = i;
                //    }                    
                //}
                //if (i == 2 || i < CurrentColumn)
                //{
                //    IsNotOverFlow = true;
                //}
                var rptcolumnCompar = _reportcolumn.Name;
                if (_reportcolumn.Name.Substring(0, 4).Equals("LNK_"))
                {
                    //rptcolumnCompar = rptcolumnCompar.Replace("__", "%%");
                    int index = rptcolumnCompar.IndexOf("__");
                    if (index != -1)
                    {
                        rptcolumnCompar = rptcolumnCompar.Substring(0, index) + "%%" + rptcolumnCompar.Substring(index + 2);
                    }
                }
                if (Fields.IndexOf(rptcolumnCompar) > -1)
                {
                    if (string.IsNullOrEmpty(_reportcolumn.Title))
                    {
                        _reportcolumn.Title = " ";
                    }
                    Columns.Add(_reportcolumn);
                }
            }

            string _enableBulkSave = goTR.StrRead(_viewMetaData, "ENABLEBULKSAVE", "false");

            if (_enableBulkSave == "1")
            {
                IsShowSaveButton = true;
            }
            else
            {
                IsShowSaveButton = false;
            }

            if (ViewType.ToUpper() == "CHART")
            {
                //graph y fields, go first in view before regular fields
                goDef=(clDefaults)Util.GetInstance("def");
                string gsTable=goTR.StrRead(_viewMetaData, "FILE", "");
                string sLabel = string.Empty;
                string sAlignment = string.Empty;
                int iWidth=0;
                int par_iValid=4;
                string sInvalidFields=string.Empty;
                string sFieldFormatted=string.Empty;

                string gsViewState= string.Empty;
                    string sGraphYShow = goTR.StrRead(_viewMetaData, "GRAPHYSHOW", "COUNT", false);
                    if (sGraphYShow != "COUNT")
                    {
                        int iYFields = 4;
                        if (sGraphYShow == "STATISTICS")
                            iYFields = 1;
                        for (int i = 1; i <= iYFields; i++)
                        {
                            string sGraphYField = Strings.UCase(goTR.StrRead(_viewMetaData, "GRAPHYFIELD" + i.ToString(), "NONE", false));
                            // Or Not goData.IsFieldValid(gsTable, sGraphYField) Then
                            if (sGraphYField == "NONE" | string.IsNullOrEmpty(sGraphYField))
                            {

                            }
                            else
                            {
                                 GridColumn _reportcolumn = new GridColumn();
                                string sDefaults = goDef.GetFieldPropertiesByPrfx(Strings.Left(sGraphYField, 4));
                                sLabel = godata.GetFieldLabel(gsTable, sGraphYField);
                                _reportcolumn.Title = sLabel;
                                sAlignment = goTR.ExtractString(sDefaults, 1);
                                _reportcolumn.Alignment = sAlignment;
                                iWidth =Convert.ToInt32( goTR.StringToNum(goTR.ExtractString(sDefaults, 2),"",ref par_iValid) * 6);

                                //was it modified on the client
                                int iClientW =Convert.ToInt32(goTR.StrRead(gsViewState, "CLIENT_COL" + i.ToString() + "WIDTH", "-1"));
                                if (iClientW > -1)
                                    iWidth = iClientW * 6;

                                _reportcolumn.Width = iWidth;
                                sGraphYField = "<%" + sGraphYField + "%>";

                                //is this field valid?
                                sInvalidFields = goTR.GetFieldsFromLine(gsTable, sGraphYField, true, false);

                                if (!string.IsNullOrEmpty(sInvalidFields))
                                {
                                    IsInvalid = true;
                                    //goErr.SetWarning(45105, sProc, "", "Invalid fieldcode in view: " + sGraphYField);
                                    break;
                                }
                                else
                                {
                                    //get field list
                                    //for example, from this line: COL1FIELD=<%TXT_COMPANYNAME LENGTH=80 ELLIPSIS=1%>
                                    //extracts: TXT_COMPANYNAME and adds it to fieldlist for rowset

                                    sFieldFormatted = goTR.GetFieldsFromLine(gsTable, sGraphYField);
                                    _reportcolumn.Name = sFieldFormatted;
                                    _reportcolumn.NameOrg = sGraphYField;
                                    _reportcolumn.IsVisible = true;

                                    if (_reportcolumn.Name.Substring(0, 4).Equals("CUR_"))
                                    {
                                        var CURSymbol = _reportcolumn.Name + "~" + GetCurSymbolFromMeta(goTR.StrRead(fldmata, _reportcolumn.Name + "_FRM", ""));
                                        CURSymbols = CURSymbols + "," + CURSymbol;
                                    } 
 
                                    //metadata stores alignment information as L, R, & C for left, right, and center respectively
                                    //need to translate to value gridview control recognizes...
                                    
                                    if (sGraphYShow == "STATISTICS")
                                    {
                                        AggregateColumns = AggregateColumns + "," + "AVG_" + _reportcolumn.Name;
                                        _reportcolumn.IsAverage = true;
                                    }
                                    else if (sGraphYShow == "TOTAL")
                                    {
                                        AggregateColumns = AggregateColumns + "," + "SUM_" + _reportcolumn.Name;
                                        _reportcolumn.IsTotal = true;
                                    }

                                    //create delimited field definition string   
                                    //sFieldDef = sFieldFormatted + Strings.Chr(1) + sLabel + Strings.Chr(1) + sAlignment + Strings.Chr(1) + iWidth.ToString() + Strings.Chr(1) + "0" + Strings.Chr(1) + "0" + Strings.Chr(1) + sGraphYField + Strings.Chr(1) + "1" + Strings.Chr(1) + "-1" + Strings.Chr(1) + "0" + Strings.Chr(1) + iAverage.ToString + Strings.Chr(1) + iMinimum.ToString + Strings.Chr(1) + iMaximum.ToString + Strings.Chr(1) + iMedian.ToString;

                                    //add field definition to collection
                                   // cFieldDefs.Add(sFieldDef, sFieldFormatted);  
                                    if (string.IsNullOrEmpty(_reportcolumn.Title))
                                    {
                                        _reportcolumn.Title = " ";
                                    }
                                    Columns.Add(_reportcolumn);
                            }
                        }
                    }
                }
            }

            GridColumn _Tgridcolumn = new GridColumn();
            _Tgridcolumn.Name = "GID_ID";
            _Tgridcolumn.NameOrg = "<%GID_ID%>";
            _Tgridcolumn.Title = "GID_ID";
            _Tgridcolumn.Alignment = "left";
            _Tgridcolumn.Width = 20 * 6;
            _Tgridcolumn.IsLink = false;
            _Tgridcolumn.IsVisible = false;
            _Tgridcolumn.AllowEdit = false;
            Columns.Add(_Tgridcolumn);
        }

        private void GetSortColumnsCollection()
        {
            goTR = (clTransform)Util.GetInstance("tr");
            Microsoft.VisualBasic.Collection gcSortFields = godata.GetFilterSortFields("SORT=" + goTR.StrRead(_viewMetaData, "SORT", ""), false);
            Sorts = new List<string>();
            for (int i = 1; i <= gcSortFields.Count; i++)
            {
                
                string sSortField = gcSortFields[i].ToString();
                sSortField = goTR.ExtractString(sSortField, 1, "|");

                if (string.IsNullOrEmpty(DefaultSortField))
                {
                    DefaultSortField = sSortField;
                    DefaultSortDirection = goTR.ExtractString(gcSortFields[i].ToString(), 2, "|");
                }

                sSortField = "<%" + sSortField + "%>";
                string sFieldFormatted = goTR.GetFieldsFromLine(TableName, sSortField);
                var Heading = goTR.StrRead(_viewMetaData, "HEADING" + i.ToString(), "0");
                if (Heading == "1")
                {
                    GroupByFieldsCount++;
                    GroupedColumns = GroupedColumns + "," + sFieldFormatted.Replace("<%", string.Empty).Replace("%>", string.Empty).Replace("%%", "__"); 
                }
                else if (ViewType.ToUpper() == "CHART")
                {
                    GroupByFieldsCount++;
                    GroupedColumns = GroupedColumns + "," + sFieldFormatted.Replace("<%", string.Empty).Replace("%>", string.Empty).Replace("%%", "__"); 
                }
                Sorts.Add(sSortField);
                // to fix the issue of having percentages in reports
                if (string.IsNullOrEmpty(PercentColumns))
                {
                    PercentColumns = sFieldFormatted + "|" + goTR.StrRead(_viewMetaData, "PERCENT" + i, "1", false);
                }
                else 
                {
                    PercentColumns =PercentColumns + ","+ sFieldFormatted + "|" + goTR.StrRead(_viewMetaData, "PERCENT" + i, "1", false);
                }
            }
        }

        public IList<GridColumn> Columns { get; set; }
        public IList<string> Sorts { get; set; }
        public int PageSize { get; set; }
        public string IDColumn { get; set; }
        public int LinksTop { get; set; }
        public string DefaultSortField { get; set; }
        public string DefaultSortDirection { get; set; }
        

    }
}

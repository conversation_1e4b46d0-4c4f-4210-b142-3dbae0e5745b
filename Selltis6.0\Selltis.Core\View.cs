﻿using Microsoft.VisualBasic;
using Selltis.BusinessLogic;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Selltis.Core
{
    public class View
    {
        public string ViewId { get; set; }
        public string ViewTitle { get; set; }
        public string DefaultViewCondition { get; set; }
        public string ViewType { get; set; } //List, Chart , Report & Calender
        public int Width { get; set; }
        public int Height { get; set; }
        public string TableName { get; set; } //db table name
        public DataTable DataSource { get; set; }
        public string ViewMetaData { get; set; }
        public bool IsTabView { get; set; }
        public int ColumnNumber { get; set; }
        public int OrderIndex { get; set; }
        public int DisplayIndex { get; set; }
        public bool IsMasterView { get; set; }
        public bool IsMaster_And_Client_View { get; set; }
        public string DependencyViewIds { get; set; }
        public string DependencyViewIdsOnFocus { get; set; }
        public string ParentViewId { get; set; } 
        public int Index { get; set; }
        public string TabLabel { get; set; } //only for tab views
        public string Fields { get; set; }
        public string DefaultSortCondition { get; set; }
        public int CurrentPageNumber { get; set; }
        public int LastPageNumber { get; set; }
        public string BrowseType { get; set; } //First,Last,Next & Previous
        public string ChangedSortCondition { get; set; }
        public string ChangedViewCondition { get; set; }
        public string ViewKey { get; set; }
        public int PageSize { get; set; }
        public int TotalViews { get; set; }
        public bool IsActive { get; set; }
        public bool IsDRSEnabled { get; set; }
        public string AutoCount { get; set; }
        public string AutoLoad { get; set; }
        public string EnableBulkRecordsSaveOnView { get; set; }
        public string UseHeadings { get; set; }
        public string OneLinePerRecord { get; set; }
        public string StoredProcedureName { get; set; }
        public string Name { get; set; }
        public string TabLabelName { get; set; }
        public string ViewRecordOpen { get; set; }

        public string SelectedRecord { get; set; }
        public int SelectedRowIndex { get; set; }

        //ChartView properties
        public string Filter { get; set; }
        public string Field { get; set; }
        public string NewSort { get; set; }
        public string OrderBy { get; set; }
        public string ImageMap { get; set; }
        public string Title { get; set; }

        public string GraphXLabel { get; set; }
        public string MasterSelID { get; set; }  //Using for report rebind
        public int LinksTop { get; set; }

        public DataTable GroupDataTable = new DataTable();
        public bool QuickSelect { get; set; }
        public int RollupTotal { get; set; }
        public string ViewTooltipText { get; set; }

        public bool IsParent { get; set; }

        public double LoadTime = 0;
        public string FilterBeforeQuickSelect { get; set; }
        public string RowClick { get; set; }//speific to report view
        public string DisplayNegativeValuesInRed { get; set; }

        public Collection SortFields = new Collection();
        public Collection Sorts = new Collection();
        public Collection Columns = new Collection();

        public int a1 = 0;
        private bool isLoadFromSession = true;
        public string Section;
        public string DesktopMetaData;
        public string DefaultSelectedViewTableName;

        private clMetaData goMeta;
        private clTransform goTR;
        private clData godata;

        public Collection gcSorts = new Collection();
        public Collection gcChartData = new Collection();
        public Collection gcChartFilters = new Collection();

        public DataTable dtData { get; set; }
        public string SiteId { get; set; }

        public bool FirstLoad { get; set; }


        //Specific to drag and drop..J
        public string AllowCreateRecordInDragAndDrop { get; set; }
        public string ResourceFile { get; set; }
        public string ResourceFields { get; set; }
        public string ResourceCondition { get; set; }
        public string ActionName { get; set; }

        public string Key { get; set; }
        public bool IsSelectSpecificRecord { get; set; }
        public string SelectSpecificRecordId { get; set; }

        public bool ReportTooLong { get; set; }
        //RN #1873 Multiselect functionality added.
        public string ViewMultiSelect { get; set; }

        //SB #2571 Merge Utility multiselect.
        public string MergeMultiSelect { get; set; }

        public bool IsShowSaveButton { get; set; }

        public bool ShowProfileIcon { get; set; }

        public View()
        { 
        
        }
        public View(string _viewId, bool _isTabView, string _section, string _desktopmetadata, int _index, bool _isLoadFromSession = true, int _TotalIndex = 0,string _key = "")
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            godata = (clData)Util.GetInstance("data");
            isLoadFromSession = _isLoadFromSession;
            ViewKey = Util.GetViewKey(_viewId.Replace(" ", ""));

            OrderIndex = _index;
            ViewId = _viewId;
            IsTabView = _isTabView;
            Section = _section;
            DesktopMetaData = _desktopmetadata;
            Index = _index;
            Key = _key;
            if (isLoadFromSession == true)
            {
                ViewMetaData = Util.SessionViewInfo(Key + "_" + ViewKey).ViewMetaData;

                //temp fix, remove this check once the calender view is ready
                if (string.IsNullOrEmpty(ViewMetaData))
                {
                    ViewMetaData = goMeta.PageRead(Section, ViewId);
                }
            }
            else
            {
                //ClUI goUI = new ClUI();
                //if (goUI.IsQueueLoaded() && goUI.NavigateType() == "DESKTOP" && goUI.NavigateItem() != null)
                //{
                //    ViewMetaData = Util.SessionViewInfo(Key + "_" + ViewKey).ViewMetaData;
                //    if (string.IsNullOrEmpty(ViewMetaData))
                //    {
                //        ViewMetaData = goMeta.PageRead(Section, ViewId);
                //    }
                //}
                //else
                //{
                //    ViewMetaData = goMeta.PageRead(Section, ViewId);
                //}

                ViewMetaData = goMeta.PageRead(Section, ViewId);
                
            }

            ColumnNumber = Convert.ToInt32(goTR.StrRead(DesktopMetaData, "VIEW" + (Index).ToString() + "COLUMN", "1"));
            ViewType = goTR.StrRead(ViewMetaData, "TYPE", "");
            DefaultViewCondition = goTR.StrRead(ViewMetaData, "CONDITION", "");
            AutoCount = goTR.StrRead(ViewMetaData, "AUTOCOUNT", "0");
            AutoLoad = goTR.StrRead(ViewMetaData, "AUTOLOADVIEWDATA", "1");
            EnableBulkRecordsSaveOnView = goTR.StrRead(ViewMetaData, "ENABLEBULKSAVE", "false");
            UseHeadings = goTR.StrRead(ViewMetaData, "LIST_USEHEADINGS", "1", false);
            OneLinePerRecord = goTR.StrRead(ViewMetaData, "LIST_ONELINEPERRECORD", "1", false);
            //ViewTitle = goTR.StrRead(ViewMetaData, "DESCRIPTION", "");
            ViewTitle = goTR.StrRead(ViewMetaData, "NAME", "").Replace("(<%startdate%> to <%enddate%>)","") ;
            TableName = goTR.StrRead(ViewMetaData, "FILE");
            string sPageSize = goTR.StrRead(ViewMetaData, "SHOWTOP", "10");
            //RN #1873 Multiselect functionality added.
            ViewMultiSelect = goTR.StrRead(ViewMetaData, "SELECTMULTIPLERECORDS", "0");
            string sWGVals = "";
            Util.Get_WG_Options_MetaData(ref sWGVals);
            MergeMultiSelect = goTR.StrRead(sWGVals, "ENABLE_MERGE_RECORDS", "0");

            int _PageSize = 10;
            int.TryParse(sPageSize, out _PageSize);
            PageSize = _PageSize;

            //Specific to drag and drop..J
            AllowCreateRecordInDragAndDrop = goTR.StrRead(ViewMetaData, "ALLOWCREATERECORDINDRAGANDDROP", "0");
            ResourceFile = goTR.StrRead(ViewMetaData, "RESOURCEFILE", "");
            ResourceFields = goTR.StrRead(ViewMetaData, "RESOURCEFIELDS", "");
            ResourceCondition = goTR.StrRead(ViewMetaData, "RESOURCECONDITION", "");

            ActionName = goTR.StrRead(ViewMetaData, "CREATERECORDINDRAGANDDROPACTION", "");

            //check drs enabled or not..J
            string DRSEnabledOrNot = goTR.StrRead(ViewMetaData, "C1DATERANGE", "");
            if (DRSEnabledOrNot != "" && DRSEnabledOrNot == "DRSDATES")
            {
                IsDRSEnabled = true;
            }
            else
            {
                string sCondition = goTR.StrRead(ViewMetaData, "CONDITION", "");
                if (sCondition.ToLower().Contains("<%startdate%>") && sCondition.ToLower().Contains("<%enddate%>"))
                {
                    IsDRSEnabled = true;
                }
                else
                { 
                    IsDRSEnabled = false; 
                }               
            }

            IsActive = false;
            //always the master view should be in the top views so no need to check for master views when IsTabview=true
            if (IsTabView == false)
            {
                //1st view is always master view
                //if (Index == 1 && _TotalIndex == 0)//0 changed to 1
                //{
                //    DefaultSelectedViewTableName = TableName;
                //    IsMasterView = true;
                //    IsActive = true;

                //    //if (DefaultViewCondition.ToLower().Contains("<%selectedrecordid file=") == false || DefaultViewCondition.ToLower().Contains("<%selectedviewrecordid file=") == false)
                //    //{
                //    //    IsMasterView = false;
                //    //    IsActive = false;
                //    //}
                //}

                string sCondition = goTR.StrRead(ViewMetaData, "CONDITION", "");
                if (sCondition.ToLower().Contains("<%selectedrecordid file=") == false && sCondition.ToLower().Contains("<%selectedviewrecordid file=") == false)
                {
                    IsMasterView = true;
                    IsActive = true;
                }

                //if (TableName == DefaultSelectedViewTableName)
                //{
                //    //this is a master view but dependent on 1st view so we need to add this view in client view Ids list
                //    IsMaster_And_Client_View = true;
                //}
            }

            if (IsTabView)
            {
                string sCondition = goTR.StrRead(ViewMetaData, "CONDITION", "");
                if (sCondition.ToLower().Contains("<%selectedrecordid file=") == false && sCondition.ToLower().Contains("<%selectedviewrecordid file=") == false)
                {
                    IsMasterView = true;
                    IsActive = true;
                }
                TabLabel = goTR.StrRead(ViewMetaData, "TABLABEL", "");
                Height = 200;
            }

            Fields = GetFields(ViewMetaData);
            DefaultSortCondition = goTR.StrRead(ViewMetaData, "SORT", "");

            if (ViewType.ToUpper() == "CALDAY" || ViewType.ToUpper() == "CALWEEK" || ViewType.ToUpper() == "CALMONTH" || ViewType.ToUpper() == "CALYEAR")
            {
                string sDateDefaults = goTR.StrRead(ViewMetaData, "CAL_DATETIMEFIELDSDEF");
                if (string.IsNullOrEmpty(sDateDefaults))
                    sDateDefaults = godata.GetDefaultCalDateTimeFields(TableName);
                DefaultSortCondition = goTR.ExtractString(sDateDefaults, 1, "|");
            }
            
            if (!string.IsNullOrEmpty(ViewMetaData))
                ViewTooltipText = Util.GetViewTooltip(ViewMetaData);

            if (Util.GetSessionValue("PersonalOptions") != null)
            {
                string sPersonalOptions = Util.GetSessionValue("PersonalOptions").ToString();
                ViewRecordOpen = goTR.StrRead(sPersonalOptions, "VIEWRECORDOPEN", "3", false);
                DisplayNegativeValuesInRed = goTR.StrRead(sPersonalOptions, "DISPLAYNEGATIVEVALUESINRED", "0", false);
            }

            ShowProfileIcon = Util.ShowProfilePageIcon(TableName);

            ImageMap = Util.GetImageMap(TableName);
            SiteId = Util.GetSiteId();
        }

        private string GetFields(string sMeta)
        {
            try
            {
                int i = 0;
                int iColCount = 0;
                string sField = null;
                string sFieldFormatted = null;
                string sFileName = goTR.StrRead(sMeta, "FILE");
                string sFieldList = null;

                iColCount = Convert.ToInt32(goTR.StrRead(sMeta, "COLCOUNT", 0));

                for (i = 1; i <= iColCount; i++)
                {
                    sField = goTR.StrRead(sMeta, "COL" + i + "FIELD", 0);
                    sFieldFormatted = goTR.GetFieldsFromLine(sFileName, sField).ToUpper();

                    if (string.IsNullOrEmpty(sFieldList))
                    {
                        sFieldList = sFieldFormatted;
                    }
                    else
                    {
                        sFieldList = sFieldList + ", " + sFieldFormatted;
                    }
                }

                return sFieldList;

            }
            catch (Exception ex)
            {
                return null;
            }
        }


    }

    public class TempView
    {
        public string ViewId {get; set;}
        public string TableName {get;set;}
        public string ParentViewId{get;set;}
        public int index { get; set; }
        public string Condition { get; set; }
        public bool isTabView { get; set; }
    }
}

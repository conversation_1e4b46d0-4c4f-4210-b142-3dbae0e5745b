﻿  
  CREATE PROCEDURE [dbo].[Proc_OutsideSalesManager_Dashboard]              
  (@UserId Uniqueidentifier, @GMT_Time varchar(1000), @LoginGID Uniqueidentifier)                  
  AS                  
  BEGIN                  
  Set nocount on                  
  --[dbo].[Proc_OutsideSalesManager_Dashboard] @UserId = '2E06F68A-F997-456F-5553-AE86008119C8',@GMT_Time ='(GMT-06:00) Central America',@LoginGID= 'F0272467-3EC5-48F7-5553-987900B57A11'  
  
  Declare @MyPrimaryAccounts int = 0                  
  Declare @MyNewAccounts int = 0                  
  Declare @MyTeamSellAccounts int = 0                  
  Declare @MyOverserveAccounts int = 0                  
  Declare @MyUnderservedAccounts int = 0                  
  Declare @MyOverduePrimaryAccounts int = 0                  
  Declare @AccountsWithoutContacts int = 0                  
  Declare @AccountsWithoutActivities int = 0                  
                  
  Declare @MyContacts int = 0                  
  Declare @MyNewContacts int = 0                  
  Declare @MyKeyContacts int = 0                  
  Declare @MyOverdueContacts int = 0                  
                                           
  Declare @MyTasksDue int = 0                  
  Declare @MyTasksOverdue int = 0                  
                  
  Declare @MyActivities int = 0                  
  Declare @MyAcTeamSell int = 0                  
                  
  Declare @MySalesThisMonth int = 0                  
  Declare @MySalesLastonth int = 0                  
                    
  Declare @OthersAcThisMonth int = 0                  
  Declare @OthersAcLastMonth int = 0                  
                  
  Declare @MyLeads int = 0                  
  Declare @MyLeadsExptedVal money = 0.0                  
  Declare @MyLeadsWeightedVal money = 0.0                  
                    
  Declare @MyTeamSellLeads int = 0                  
  Declare @MyTeamSellLeadsExptedVal money = 0.0                  
  Declare @MyTeamSellLeadsWeightedVal money = 0.0                  
                  
  Declare @MyNewLeads int = 0                  
  Declare @MyNewLeadsExptedVal money = 0.0                  
  Declare @MyNewLeadsWeightedVal money = 0.0                  
                  
  Declare @MyOverdueLeads int = 0                  
  Declare @MyOverdueLeadsExptedVal money = 0.0                  
  Declare @MyOverdueLeadsWeightedVal money = 0.0                  
                  
  Declare @MyOpenOppsqty int = 0                  
  Declare @MyOpenOppsExptedVal money = 0.0                  
  Declare @MyOpenOppsWeightedVal money = 0.0                  
                                        
  Declare @MyTeamSellOpps_count int = 0                  
  Declare @MyTeamSellOpps_expval money = 0                  
  Declare @MyTeamSellOpps_wghtval money = 0                  
                  
  Declare @MyDueNext30DaysOpps_count int = 0                  
  Declare @MyDueNext30DaysOpps_expval money = 0                  
  Declare @MyDueNext30DaysOpps_wghtval money = 0                  
                  
  Declare @MyOverdueOpps_count int = 0                  
  Declare @MyOverdueOpps_expval money = 0                  
  Declare @MyOverdueOpps_wghtval money = 0                  
                  
  Declare @MyOpps_won_Qty int = 0                  
  Declare @MyOpps_won_Tot money = 0                  
  Declare @MyOpps_won_Rate int = 0                  
                    
  Declare @MyOpps_Lost_Qty int = 0                  
  Declare @MyOpps_Lost_Tot money = 0                  
  Declare @MyOpps_Lost_Rate int = 0                  
                    
  Declare @MyOpps_Cancelled_Qty int = 0                  
  Declare @MyOpps_Cancelled_Tot money = 0                  
  Declare @MyOpps_Cancelled_Rate int = 0                  
                   
  Declare @OpenRFQsQty int = 0                  
  Declare @OpenRFQstotal money = 0                  
  Declare @OpenRFQsmargin int = 0                  
                    
  Declare @AllOpenQuotesQty int = 0                  
  Declare @AllOpenQuotesTotal money = 0                  
  Declare @AllOpenQuotesMargin int = 0                  
                  
  Declare @OpenStrategicQuotesQty int = 0                  
  Declare @OpenStrategicQuotesTotal money = 0                  
  Declare @OpenStrategicQuotesMargin int = 0                  
                  
  Declare @NewStrategicQuotesQty int = 0                  
  Declare @NewStrategicQuotesTotal money = 0                  
  Declare @NewStrategicQuotesMargin int = 0                  
                  
  Declare @OverdueStrategicQuotesQty int = 0                  
  Declare @OverdueStrategicQuotesTotal money = 0                  
  Declare @OverdueStrategicQuotesMargin int = 0                  
                  
  Declare @StrategicQuotesWonQty int = 0                  
  Declare @StrategicQuotesWonTotal money = 0                  
  Declare @StrategicQuotesWonRate int = 0                  
                  
  Declare @StrategicQuotesLostQty int = 0                  
  Declare @StrategicQuotesLostTotal money = 0                  
  Declare @StrategicQuotesLostRate int = 0                  
                  
  Declare @StrategicQuotesCancelledQty int = 0                  
  Declare @StrategicQuotesCancelledTotal money = 0                  
  Declare @StrategicQuotesCancelledRate int = 0                  
                  
  Declare @MyQuotePipeline int = 0                        
  Declare @MyTaskstile int = 0                     
                  
  DECLARE @DTT_LASTREFRESHEDDATE DATETIME;                        
  SET @DTT_LASTREFRESHEDDATE  = GETDATE();                        
--                  
  Declare @MyStrategicQuotesOpe int = 0                                                          
  Declare @MyStrategicQuotesNew int = 0                                                          
  Declare @MyStrategicQuotesOverdue int = 0                    
  Declare @MyRecentOrders int = 0                                                          
  Declare @count_of_my_recent_orders_this_month int = 0                                                 
  Declare @total_amount_of_my_recent_order_this_month money = 0.0                                       
  Declare @gross_profit_of_my_recent_order_this_month int = 0                                                 
  Declare @count_of_my_recent_orders_last_month int = 0                                                          
  Declare @total_amount_of_my_recent_order_last_month money = 0.0                                                 
  Declare @gross_profit_of_my_recent_order_last_month int = 0                       
  Declare @count_of_my_open_orders int = 0                                                          
  Declare @total_amount_of_my_open_order money = 0.0                                                          
  Declare @gross_profit_of_my_open_order int = 0                                                          
  Declare @MyQuotes int = 0                      
  Declare @sum_of_my_open_expected_value money = 0.0                                                          
  Declare @count_of_my_open_expected_value_weighted int = 0                                                          
  Declare @SalesThisMonthQty  int =0                                                
  Declare @SalesThisMonthTotal int = 0                                                     
  Declare @SalesThisMonthMargin int = 0                                                     
  Declare @SalesLastMonthQty int =0                                                    
  Declare @SalesLastMonthTotal int = 0                                                     
  Declare @SalesLastMonthMargin int = 0                    
    
  DECLARE @GMTOffset INT;  
  DECLARE @SignValue INT;  
  
  -- Get the sign of the offset  
  SELECT @GMTOffset = CURRENT_OFFSET_FROM_GMT, @SignValue = SIGN(CURRENT_OFFSET_FROM_GMT)  
  FROM GMTOffset  
  WHERE [TEXT] = @GMT_Time;  
  
  Declare @LastMonthStartDate Datetime = DATEADD(HH,-(CAST(@GMTOffset AS INT)),CAST(DATEADD(DD,1,EOMONTH(DATEADD(MM,-2,GETUTCDATE()))) AS DATETIME))   
  Declare @TodayDate Datetime = DATEADD(HH,-CAST(@GMTOffset AS INT),CAST(CAST(DATEADD(HH,CAST(@GMTOffset AS INT),GETUTCDATE()) AS DATE) AS DATETIME))   
  Declare @MonthAgoDate Datetime = DATEADD(HH,-CAST(@GMTOffset AS INT),DATEADD(DD,1,DATEADD(MM,-1,CAST(CAST(DATEADD(HH,CAST(@GMTOffset AS INT),GETUTCDATE()) AS DATE) AS DATETIME))))  
  Declare @TomorrowDate Datetime = DATEADD(HH,-CAST(@GMTOffset AS INT),DATEADD(DD,1,CAST(CAST(DATEADD(HH,CAST(@GMTOffset AS INT),GETUTCDATE()) AS DATE) AS DATETIME)))  
  set  @GMT_Time = @GMTOffset  
  
  DECLARE @TODAY DATETIME               
  SET @TODAY = CAST(DATEADD(HH,CAST(@GMT_TIME AS INT),GETUTCDATE()) AS DATE)               
  set @GMT_Time= abs(@GMT_Time)              
   
  Declare @StartDateOfNextTwoWeeks Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),DATEADD(DAY,14,DATEADD(DAY,-((DATEPART(WEEKDAY, @TODAY) + 5) % 7),@TODAY)))  
  Declare @EndDateOfNextTwoWeeks Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),DATEADD(DAY,28,DATEADD(DAY,-((DATEPART(WEEKDAY, @TODAY) + 5) % 7),@TODAY)))    
  Declare @ThisMonthStartDate Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),CAST(DATEADD(DD,1,EOMONTH(DATEADD(MM,-1,@TODAY))) AS DATETIME))                
  Declare @NextMonthStartDate Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),CAST(DATEADD(DD,1,EOMONTH(@TODAY)) AS DATETIME))              
  Declare @OneMonthAgoDate Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),DATEADD(MM,-1,DATEADD(DD,1,@TODAY)))              
  Declare @EndDateOfLastTwoWeeks Datetime = DATEADD(DAY, -((DATEPART(WEEKDAY, GETUTCDATE()) + 5) % 7), DATEADD(HH,-(CAST(@GMT_Time AS INT)),CAST(CAST(GETUTCDATE() AS DATE) AS DATETIME)))  
  Declare @StartDateOfLastTwoWeeks Datetime = DATEADD(HH,-(CAST(@GMT_Time AS INT)),DATEADD(HH,CAST(@GMT_Time AS INT),DATEADD(DAY, -14, DATEADD(DAY, -((DATEPART(WEEKDAY, GETUTCDATE()) + 5) % 7), DATEADD(HH,-(CAST(@GMT_Time AS INT)),CAST(CAST(GETUTCDATE() A
S DATE) AS DATETIME))))) )   
  Declare @ThreeMonthsAgoDate Datetime = DATEADD(DD,1,DATEADD(MM,-3,@TodayDate))               
  Declare @FiveDaysAgoDate Datetime = DATEADD(DD,-5, @TodayDate)              
  Declare @NintyOneDaysAgoDate Datetime = DATEADD(MM,-3,DATEADD(SS,1,@TomorrowDate))              
  
 Declare @IsAdmin Int = 0      
 Set @IsAdmin = (SELECT TXT_VALUE FROM XP WHERE GID_SECTION IN(SELECT GID_ID FROM XU WHERE GID_USERID=@LoginGID) AND TXT_PROPERTY ='ADMIN' AND TXT_PAGE ='ROLES')      
      
--My New Accounts         
IF @IsAdmin = 1      
BEGIN       
Select @MyNewAccounts = IsNull(Count(Distinct(CO.GID_ID)),0)                  
FROM [CO]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
WHERE ([CO].[DTT_CREATIONTIME] >= @MonthAgoDate AND --'2025-04-12 11:00:00.000' AND   
([CO].[DTT_CREATIONTIME] <= @TomorrowDate OR --'2025-05-12 10:59:59.000' OR   
([CO].[DTT_CREATIONTIME] IS NULL))) AND   
([US00001].[GID_ID] = @UserId OR  
[US00003].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId)  
 END      
ELSE      
 BEGIN       
 Select @MyNewAccounts = IsNull(Count(Distinct(CO.GID_ID)),0)                          
 FROM [CO]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [CO].[GID_CreatedBy_US]      
 WHERE (([CO].[DTT_CREATIONTIME] >= @MonthAgoDate AND      
 ([CO].[DTT_CREATIONTIME] <= @TomorrowDate)) AND       
 ([US00001].[GID_ID] = @UserId OR      
 [US00003].[GID_ID] = @UserId OR      
 [US00002].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId)) AND       
 ([CO].[SI__ShareState] = 2 OR       
 (([CO].[SI__ShareState] < 2 OR      
 ([CO].[SI__ShareState] IS NULL)) AND       
 [US00005].[GID_ID] = @LoginGID))           
 END      
  
--My Team Sell Accounts       
 IF @IsAdmin = 1      
 BEGIN       
 Select @MyTeamSellAccounts = IsNull(Count(Distinct(CO.GID_ID)),0)                  
 FROM [CO]        
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]      
 LEFT JOIN [CO_INVOLVES_US] ON [CO].[GID_ID] = [CO_INVOLVES_US].[GID_CO]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [CO_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
 WHERE [CO].[CHK_ACTIVEFIELD] = 1 AND       
 ([US00002].[GID_ID]  = @UserId OR       
 [US00005].[GID_ID]  = @UserId OR       
 [US00003].[GID_ID]  = @UserId OR       
 [US00004].[GID_ID]  = @UserId OR       
 [US00001].[GID_ID]  = @UserId OR       
 [US00007].[GID_ID]  = @UserId OR       
 [US00006].[GID_ID]  = @UserId OR       
 [US00008].[GID_ID]  = @UserId) AND       
 [CO].[CHK_TEAMSELL] = 1      
 END      
ELSE      
 BEGIN      
 Select @MyTeamSellAccounts = IsNull(Count(Distinct(CO.GID_ID)),0)                  
 FROM [CO]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]      
 LEFT JOIN [CO_INVOLVES_US] ON [CO].[GID_ID] = [CO_INVOLVES_US].[GID_CO]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [CO_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [CO].[GID_CreatedBy_US]      
 WHERE ([CO].[CHK_ACTIVEFIELD] = 1 AND      
 ([US00002].[GID_ID]  = @UserId OR      
 [US00005].[GID_ID]  = @UserId OR      
 [US00003].[GID_ID]  = @UserId OR       
 [US00004].[GID_ID]  = @UserId OR       
 [US00001].[GID_ID]  = @UserId OR      
 [US00007].[GID_ID]  = @UserId OR       
 [US00006].[GID_ID]  = @UserId OR       
 [US00008].[GID_ID]  = @UserId) AND      
 [CO].[CHK_TEAMSELL] = 1) AND       
 ([CO].[SI__ShareState] = 2 OR       
 (([CO].[SI__ShareState] < 2 OR       
 ([CO].[SI__ShareState] IS NULL)) AND       
 [US00009].[GID_ID] = @LoginGID))                  
 END      
      
--My Overserve accounts        
 IF @IsAdmin = 1      
 BEGIN      
 Select @MyOverserveAccounts = IsNull(Count(Distinct(CO.GID_ID)),0)          
 FROM [CO]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 WHERE [CO].[CHK_OVERSERVE] = 1 AND       
 ([US00001].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId)      
 END      
ELSE      
 BEGIN       
 Select @MyOverserveAccounts = IsNull(Count(Distinct(CO.GID_ID)),0)                  
 FROM [CO]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [CO].[GID_CreatedBy_US]      
 WHERE ([CO].[CHK_OVERSERVE] = 1 AND       
 ([US00001].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR      
 [US00003].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId)) AND       
 ([CO].[SI__ShareState] = 2 OR       
 (([CO].[SI__ShareState] < 2 OR       
 ([CO].[SI__ShareState] IS NULL)) AND      
 [US00005].[GID_ID] = @LoginGID))       
  END      
  
 --MyUnderservedAccounts      
 IF @IsAdmin = 1      
 BEGIN       
 Select @MyUnderservedAccounts = IsNull(Count(Distinct(CO.GID_ID)),0)      
FROM [CO]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
WHERE ([US00001].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId) AND   
[CO].[CHK_OVERSERVE] = 1 AND   
(([CO].[DTT_LASTACSALES] <= @LastMonthStartDate OR --'2025-04-01 05:00:00.000' OR   
([CO].[DTT_LASTACSALES] IS NULL)) AND   
([CO].[DTT_LASTOP] <= @LastMonthStartDate OR --'2025-04-01 05:00:00.000' OR   
([CO].[DTT_LASTOP] IS NULL)) AND   
([CO].[DTT_LASTQT] <= @LastMonthStartDate OR --'2025-04-01 05:00:00.000' OR   
([CO].[DTT_LASTQT] IS NULL)))  
 END      
ELSE      
 BEGIN      
 Select @MyUnderservedAccounts = IsNull(Count(Distinct(CO.GID_ID)),0)                     
FROM [CO]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [CO].[GID_CreatedBy_US]  
WHERE (([US00001].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId) AND   
[CO].[CHK_OVERSERVE] = 1 AND   
(([CO].[DTT_LASTACSALES] <= @LastMonthStartDate OR --'2025-04-01 04:00:00.000' OR   
([CO].[DTT_LASTACSALES] IS NULL)) AND   
([CO].[DTT_LASTOP] <= @LastMonthStartDate OR --'2025-04-01 04:00:00.000' OR   
([CO].[DTT_LASTOP] IS NULL)) AND   
([CO].[DTT_LASTQT] <= @LastMonthStartDate OR --'2025-04-01 04:00:00.000' OR   
([CO].[DTT_LASTQT] IS NULL)))) AND   
([CO].[SI__ShareState] = 2 OR   
(([CO].[SI__ShareState] < 2 OR   
([CO].[SI__ShareState] IS NULL)) AND   
[US00005].[GID_ID] = @LoginGID))  
 END      
  
--@MyOverduePrimaryAccounts      
IF @IsAdmin = 1      
BEGIN       
Select @MyOverduePrimaryAccounts =  IsNull(Count(Distinct(CO.GID_ID)),0)       
FROM [CO]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]  
LEFT JOIN [CO_INVOLVES_US] ON [CO].[GID_ID] = [CO_INVOLVES_US].[GID_CO]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [CO_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00004].[GID_SUPERVISOR_US]  
WHERE [CO].[CHK_REVIEW] = 1 AND   
([CO].[DTT_NEXTREVIEWDATE] <= @TodayDate OR   
([CO].[DTT_NEXTREVIEWDATE] IS NULL)) AND  
[CO].[CHK_PRIMARY] = 1 AND  
([US00001].[GID_ID]  = @UserId OR   
[US00002].[GID_ID]  = @UserId OR  
[US00003].[GID_ID]  = @UserId OR  
[US00004].[GID_ID]  = @UserId OR   
[US00006].[GID_ID]  = @UserId OR  
[US00007].[GID_ID]  = @UserId OR  
[US00008].[GID_ID]  = @UserId OR   
[US00005].[GID_ID]  = @UserId)     
 END      
ELSE      
BEGIN            
Select @MyOverduePrimaryAccounts =  IsNull(Count(Distinct(CO.GID_ID)),0)                    
FROM [CO]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]  
LEFT JOIN [CO_INVOLVES_US] ON [CO].[GID_ID] = [CO_INVOLVES_US].[GID_CO]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [CO_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00004].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [CO].[GID_CreatedBy_US]  
WHERE ([CO].[CHK_REVIEW] = 1 AND  
([CO].[DTT_NEXTREVIEWDATE] <= @TodayDate OR  
([CO].[DTT_NEXTREVIEWDATE] IS NULL)) AND  
[CO].[CHK_PRIMARY] = 1 AND  
([US00001].[GID_ID] = @UserId OR  
[US00002].[GID_ID] = @UserId OR  
[US00003].[GID_ID] = @UserId OR  
[US00004].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR  
[US00008].[GID_ID] = @UserId OR   
[US00005].[GID_ID] = @UserId)) AND  
([CO].[SI__ShareState] = 2 OR  
(([CO].[SI__ShareState] < 2 OR  
([CO].[SI__ShareState] IS NULL)) AND  
[US00009].[GID_ID] = @LoginGID))         
 END      
  
--@AccountsWithoutContacts      
 IF @IsAdmin = 1      
 BEGIN       
 SELECT @AccountsWithoutContacts = IsNull(Count(Distinct(CO.GID_ID)),0)        
 FROM [CO]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [CN_RELATED_CO] ON [CO].[GID_ID] = [CN_RELATED_CO].[GID_CO]      
 LEFT JOIN [CN] [CN00005] ON [CN00005].[GID_ID] = [CN_RELATED_CO].[GID_CN]      
 WHERE [CO].[CHK_ACTIVEFIELD] = 1 AND       
 [CO].[CHK_PRIMARY] = 1 AND       
 ([US00001].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId) AND       
 ([CN00005].[BI__ID] < 1 OR       
 ([CN00005].[BI__ID] IS NULL))      
 END      
ELSE      
 BEGIN                     
 SELECT @AccountsWithoutContacts = IsNull(Count(Distinct(CO.GID_ID)),0)                    
 FROM [CO]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]          
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]          
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]   
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]            
 LEFT JOIN [CN_RELATED_CO] ON [CO].[GID_ID] = [CN_RELATED_CO].[GID_CO]          
 LEFT JOIN [CN] [CN00004] ON [CN00004].[GID_ID] = [CN_RELATED_CO].[GID_CN]          
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [CO].[GID_CreatedBy_US]          
 WHERE ([CO].[CHK_ACTIVEFIELD] = 1 AND          
 [CO].[CHK_PRIMARY] = 1 AND          
 ([US00001].[GID_ID] = @UserId OR          
 [US00003].[GID_ID] = @UserId OR          
 [US00002].[GID_ID] = @UserId OR          
 [US00004].[GID_ID]= @UserId) AND          
 ([CN00004].[BI__ID] < 1 OR          
 ([CN00004].[BI__ID] IS NULL))) AND          
 ([CO].[SI__ShareState] = 2 OR          
 (([CO].[SI__ShareState] < 2 OR           
 ([CO].[SI__ShareState] IS NULL)) AND          
 [US00005].[GID_ID] = @LoginGID))          
 END      
  
--@AccountsWithoutActivities       
 IF @IsAdmin = 1      
 BEGIN       
 SELECT @AccountsWithoutActivities = IsNull(Count(Distinct(CO.GID_ID)),0)        
 FROM [CO]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [AC_RELATED_CO] ON [CO].[GID_ID] = [AC_RELATED_CO].[GID_CO]      
 LEFT JOIN [AC] [AC00005] ON [AC00005].[GID_ID] = [AC_RELATED_CO].[GID_AC]      
 WHERE [CO].[CHK_ACTIVEFIELD] = 1 AND       
 [CO].[CHK_PRIMARY] = 1 AND       
 ([US00001].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId) AND       
 ([AC00005].[BI__ID] < 1 OR       
 ([AC00005].[BI__ID] IS NULL))      
 END      
ELSE      
 BEGIN                   
 SELECT @AccountsWithoutActivities = IsNull(Count(Distinct(CO.GID_ID)),0)                    
 FROM [CO]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [AC_RELATED_CO] ON [CO].[GID_ID] = [AC_RELATED_CO].[GID_CO]      
 LEFT JOIN [AC] [AC00005] ON [AC00005].[GID_ID] = [AC_RELATED_CO].[GID_AC]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [CO].[GID_CreatedBy_US]      
 WHERE ([CO].[CHK_ACTIVEFIELD] = 1 AND       
 [CO].[CHK_PRIMARY] = 1 AND      
 ([US00001].[GID_ID] = @UserId OR      
 [US00004].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId) AND       
 ([AC00005].[BI__ID] < 1 OR       
 ([AC00005].[BI__ID] IS NULL))) AND       
 ([CO].[SI__ShareState] = 2 OR       
 (([CO].[SI__ShareState] < 2 OR      
 ([CO].[SI__ShareState] IS NULL)) AND       
 [US00006].[GID_ID] =  @LoginGID))         
 END      
      
--MyNewContacts      
IF @IsAdmin = 1      
BEGIN       
SELECT @MyNewContacts = ISNULL(Count(Distinct(CN.GID_ID)),0)         
FROM [CN]  
LEFT JOIN [CN_RELATED_US] ON [CN].[GID_ID] = [CN_RELATED_US].[GID_CN]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CN_RELATED_US].[GID_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [CN_RELATED_CO] ON [CN].[GID_ID] = [CN_RELATED_CO].[GID_CN]  
LEFT JOIN [CO] [CO00005] ON [CO00005].[GID_ID] = [CN_RELATED_CO].[GID_CO]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [CO00005].[GID_TEAMLEADER_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
WHERE [CN].[CHK_ACTIVEFIELD] = 1 AND   
([CN].[DTT_CREATIONTIME] >= @OneMonthAgoDate AND --'2025-04-13 05:00:00.000' AND   
([CN].[DTT_CREATIONTIME] <= DATEADD(HH,-1,@TomorrowDate) OR --'2025-05-13 04:59:59.000' OR   
([CN].[DTT_CREATIONTIME] IS NULL))) AND   
([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId)  
 END      
ELSE      
BEGIN       
SELECT @MyNewContacts = ISNULL(Count(Distinct(CN.GID_ID)),0)                      
FROM [CN]  
LEFT JOIN [CN_RELATED_US] ON [CN].[GID_ID] = [CN_RELATED_US].[GID_CN]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CN_RELATED_US].[GID_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [CN_RELATED_CO] ON [CN].[GID_ID] = [CN_RELATED_CO].[GID_CN]  
LEFT JOIN [CO] [CO00005] ON [CO00005].[GID_ID] = [CN_RELATED_CO].[GID_CO]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [CO00005].[GID_TEAMLEADER_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [CN].[GID_CreatedBy_US]  
WHERE ([CN].[CHK_ACTIVEFIELD] = 1 AND   
([CN].[DTT_CREATIONTIME] >= @OneMonthAgoDate AND --'2025-04-13 00:00:00.000' AND   
([CN].[DTT_CREATIONTIME] <= DATEADD(HH,-1,@TomorrowDate) OR --'2025-05-12 23:59:59.000' OR   
([CN].[DTT_CREATIONTIME] IS NULL))) AND   
([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId)) AND   
([CN].[SI__ShareState] = 2 OR   
(([CN].[SI__ShareState] < 2 OR  
([CN].[SI__ShareState] IS NULL)) AND   
[US00009].[GID_ID] = @LoginGID))  
 END      
      
--MyKeyContacts      
IF @IsAdmin = 1      
BEGIN       
Select @MyKeyContacts = ISNULL(Count(Distinct(CN.GID_ID)),0)       
FROM [CN]  
LEFT JOIN [CN_RELATED_US] ON [CN].[GID_ID] = [CN_RELATED_US].[GID_CN]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CN_RELATED_US].[GID_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [CN_RELATED_CO] ON [CN].[GID_ID] = [CN_RELATED_CO].[GID_CN]  
LEFT JOIN [CO] [CO00005] ON [CO00005].[GID_ID] = [CN_RELATED_CO].[GID_CO]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [CO00005].[GID_TEAMLEADER_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
WHERE [CN].[CHK_KEY] = 1 AND   
[CN].[CHK_ACTIVEFIELD] = 1 AND   
([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId)  
END      
ELSE      
BEGIN              
Select @MyKeyContacts = ISNULL(Count(Distinct(CN.GID_ID)),0)                                                          
FROM [CN]  
LEFT JOIN [CN_RELATED_US] ON [CN].[GID_ID] = [CN_RELATED_US].[GID_CN]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CN_RELATED_US].[GID_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [CN_RELATED_CO] ON [CN].[GID_ID] = [CN_RELATED_CO].[GID_CN]  
LEFT JOIN [CO] [CO00005] ON [CO00005].[GID_ID] = [CN_RELATED_CO].[GID_CO]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [CO00005].[GID_TEAMLEADER_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [CN].[GID_CreatedBy_US]  
WHERE ([CN].[CHK_KEY] = 1 AND   
[CN].[CHK_ACTIVEFIELD] = 1 AND   
([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId)) AND   
([CN].[SI__ShareState] = 2 OR   
(([CN].[SI__ShareState] < 2 OR   
([CN].[SI__ShareState] IS NULL)) AND   
[US00009].[GID_ID] = @LoginGID))  
 END      
      
--MyOverdueContacts        
IF @IsAdmin = 1      
BEGIN      
Select @MyOverdueContacts= ISNULL(Count(Distinct(CN.GID_ID)),0)      FROM [CN]  
LEFT JOIN [CN_RELATED_US] ON [CN].[GID_ID] = [CN_RELATED_US].[GID_CN]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CN_RELATED_US].[GID_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [CN_RELATED_CO] ON [CN].[GID_ID] = [CN_RELATED_CO].[GID_CN]  
LEFT JOIN [CO] [CO00005] ON [CO00005].[GID_ID] = [CN_RELATED_CO].[GID_CO]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [CO00005].[GID_TEAMLEADER_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
WHERE [CN].[CHK_ACTIVEFIELD] = 1 AND   
[CN].[CHK_REVIEW] = 1 AND   
([CN].[DTT_NEXTCONTACTDATE] <= @TodayDate OR --'2025-05-12 05:00:00.000' OR   
([CN].[DTT_NEXTCONTACTDATE] IS NULL)) AND   
([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId)  
END   
ELSE   
BEGIN                
Select @MyOverdueContacts= ISNULL(Count(Distinct(CN.GID_ID)),0)                     
FROM [CN]  
LEFT JOIN [CN_RELATED_US] ON [CN].[GID_ID] = [CN_RELATED_US].[GID_CN]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CN_RELATED_US].[GID_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [CN_RELATED_CO] ON [CN].[GID_ID] = [CN_RELATED_CO].[GID_CN]  
LEFT JOIN [CO] [CO00005] ON [CO00005].[GID_ID] = [CN_RELATED_CO].[GID_CO]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [CO00005].[GID_TEAMLEADER_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [CN].[GID_CreatedBy_US]  
WHERE ([CN].[CHK_ACTIVEFIELD] = 1 AND   
[CN].[CHK_REVIEW] = 1 AND   
([CN].[DTT_NEXTCONTACTDATE] <= @TodayDate OR --'2025-05-12 00:00:00.000' OR   
([CN].[DTT_NEXTCONTACTDATE] IS NULL)) AND   
([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId)) AND   
([CN].[SI__ShareState] = 2 OR   
(([CN].[SI__ShareState] < 2 OR   
([CN].[SI__ShareState] IS NULL)) AND   
[US00009].[GID_ID] = @LoginGID))  
 END      
      
--MyTasksDue      
IF @IsAdmin = 1      
BEGIN       
SELECT @MyTasksDue = ISNULL(COUNT(Distinct([TD].[GID_ID])),0)      
FROM [TD]      
LEFT JOIN [TD_ASSIGNEDTO_US] ON [TD].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_TD]      
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_US]      
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
WHERE ([US00001].[GID_ID]  = @UserId  OR       
[US00002].[GID_ID]  = @UserId  OR       
[US00004].[GID_ID]  = @UserId  OR       
[US00003].[GID_ID]  = @UserId ) AND       
([TD].[MLS_STATUS] <> '4' OR       
([TD].[MLS_STATUS] IS NULL)) AND       
([TD].[DTT_DUETIME] >= @StartDateOfNextTwoWeeks AND --'2025-05-05 05:00:00.000' AND       
([TD].[DTT_DUETIME] <= DATEADD(SS,-1,DATEADD(HH,-1,@EndDateOfNextTwoWeeks)) OR --'2025-05-19 04:59:59.000' OR       
([TD].[DTT_DUETIME] IS NULL)))      
END      
ELSE      
BEGIN               
SELECT @MyTasksDue = ISNULL(COUNT(Distinct([TD].[GID_ID])),0)                                                          
FROM [TD]                
LEFT JOIN [TD_ASSIGNEDTO_US] ON [TD].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_TD]      
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_US]      
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [TD].[GID_CreatedBy_US]      
WHERE (([US00001].[GID_ID] = @UserId OR       
[US00002].[GID_ID] = @UserId OR       
[US00004].[GID_ID] = @UserId OR      
[US00003].[GID_ID] = @UserId) AND       
([TD].[MLS_STATUS] <> '4' OR       
([TD].[MLS_STATUS] IS NULL)) AND      
(([TD].[DTT_DUETIME] >= @StartDateOfNextTwoWeeks OR      
([TD].[DTT_DUETIME] IS NULL)) AND      
([TD].[DTT_DUETIME] <= DATEADD(SS,-1,DATEADD(HH,-1,@EndDateOfNextTwoWeeks)) OR       
([TD].[DTT_DUETIME] IS NULL)))) AND      
([TD].[SI__ShareState] = 2 OR      
(([TD].[SI__ShareState] < 2 OR      
([TD].[SI__ShareState] IS NULL)) AND       
[US00005].[GID_ID] = @LoginGID))         
END      
      
--MyTasksOverdue        
 IF @IsAdmin = 1      
 BEGIN       
 SELECT @MyTasksOverdue = ISNULL(COUNT(Distinct([TD].[GID_ID])),0)       
 FROM [TD]      
 LEFT JOIN [TD_ASSIGNEDTO_US] ON [TD].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_TD]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 WHERE ([US00001].[GID_ID] = @UserId  OR       
 [US00002].[GID_ID] = @UserId  OR       
 [US00004].[GID_ID] = @UserId  OR       
 [US00003].[GID_ID] = @UserId) AND       
 ([TD].[MLS_STATUS] <> '4' OR       
 ([TD].[MLS_STATUS] IS NULL)) AND       
 ([TD].[DTT_DUETIME] <= @TodayDate OR --'2025-04-22 05:00:00.000' OR       
 ([TD].[DTT_DUETIME] IS NULL))      
 END      
ELSE      
 BEGIN       
 IF (SELECT GID_InLoginGroup FROM XU WHERE GID_USERID =@LoginGID) = 'D3088A7E-50BA-4434-5855-AEA1013AF75A'      
 BEGIN  
 SELECT @MyTasksOverdue = ISNULL(COUNT(Distinct([TD].[GID_ID])),0)                    
 FROM [TD]                
 LEFT JOIN [TD_ASSIGNEDTO_US] ON [TD].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_TD]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [TD].[GID_CreatedBy_US]      
 WHERE (([US00001].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId) AND       
 ([TD].[MLS_STATUS] <> '4' OR       
 ([TD].[MLS_STATUS] IS NULL)) AND       
 ([TD].[DTT_DUETIME] <= @TodayDate OR       
 ([TD].[DTT_DUETIME] IS NULL))) AND       
 ([TD].[SI__ShareState] = 2 OR       
 (([TD].[SI__ShareState] < 2 OR       
 ([TD].[SI__ShareState] IS NULL)) AND       
 [US00005].[GID_ID] = @LoginGID))      
END      
ELSE      
BEGIN      
 SELECT @MyTasksOverdue = ISNULL(COUNT(Distinct([TD].[GID_ID])),0)       
FROM [TD]      
LEFT JOIN [TD_ASSIGNEDTO_US] ON [TD].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_TD]      
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_US]      
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [TD].[GID_CreatedBy_US]      
WHERE ((([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId) AND       
([TD].[MLS_STATUS] <> '4' OR   
([TD].[MLS_STATUS] IS NULL)) AND       
([TD].[DTT_DUETIME] <=  @TodayDate OR   
([TD].[DTT_DUETIME] IS NULL))) AND       
([US00005].[GID_ID] = @LoginGID)) AND       
([TD].[SI__ShareState] = 2 OR       
(([TD].[SI__ShareState] < 2 OR       
([TD].[SI__ShareState] IS NULL)) AND       
[US00005].[GID_ID] = @LoginGID))      
 END      
 END              
      
--@MyAcTeamSell      
IF @IsAdmin = 1      
BEGIN       
SELECT @MyAcTeamSell = ISNULL(COUNT(Distinct([AC].[GID_ID])),0)            
FROM [AC]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]  
LEFT JOIN [CO] [CO00002] ON [CO00002].[GID_ID] = [AC].[GID_BILL_CO]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]  
LEFT JOIN [AC_INVOLVES_US] ON [AC].[GID_ID] = [AC_INVOLVES_US].[GID_AC]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [AC_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [US00008].[GID_SUPERVISOR_US]  
LEFT JOIN [AC_RELATED_CO] ON [AC].[GID_ID] = [AC_RELATED_CO].[GID_AC]  
LEFT JOIN [CO] [CO00010] ON [CO00010].[GID_ID] = [AC_RELATED_CO].[GID_CO]  
LEFT JOIN [AC_RELATED_OP] ON [AC].[GID_ID] = [AC_RELATED_OP].[GID_AC]  
LEFT JOIN [OP] [OP00011] ON [OP00011].[GID_ID] = [AC_RELATED_OP].[GID_OP]  
LEFT JOIN [SO] [SO00012] ON [SO00012].[GID_ID] = [OP00011].[GID_FROM_SO]  
WHERE ([US00001].[GID_ID] = @UserId OR   
[US00005].[GID_ID] = @UserId OR   
[US00009].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId) AND   
([CO00010].[CHK_TEAMSELL] = 1 AND   
[SO00012].[GID_ID] = '38626532-6232-3836-534f-372f31312f32')  
 END      
ELSE      
 BEGIN       
 SELECT @MyAcTeamSell = ISNULL(COUNT(Distinct([AC].[GID_ID])),0)                                                          
 FROM [AC]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]      
 LEFT JOIN [CO] [CO00002] ON [CO00002].[GID_ID] = [AC].[GID_BILL_CO]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]      
 LEFT JOIN [AC_INVOLVES_US] ON [AC].[GID_ID] = [AC_INVOLVES_US].[GID_AC]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [AC_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [US00008].[GID_SUPERVISOR_US]      
 LEFT JOIN [AC_RELATED_CO] ON [AC].[GID_ID] = [AC_RELATED_CO].[GID_AC]      
 LEFT JOIN [CO] [CO00010] ON [CO00010].[GID_ID] = [AC_RELATED_CO].[GID_CO]      
 LEFT JOIN [AC_RELATED_OP] ON [AC].[GID_ID] = [AC_RELATED_OP].[GID_AC]      
 LEFT JOIN [OP] [OP00011] ON [OP00011].[GID_ID] = [AC_RELATED_OP].[GID_OP]      
 LEFT JOIN [SO] [SO00012] ON [SO00012].[GID_ID] = [OP00011].[GID_FROM_SO]      
 LEFT JOIN [BC] [BC00013] ON [BC00013].[GID_ID] = [AC].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00013].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00014] ON [US00014].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [US] [US00015] ON [US00015].[GID_ID] = [AC].[GID_CreatedBy_US]      
 WHERE ((([US00001].[GID_ID] = @UserId OR      
 [US00003].[GID_ID] = @UserId OR      
 [US00004].[GID_ID] = @UserId OR       
 [US00005].[GID_ID] = @UserId OR       
 [US00008].[GID_ID] = @UserId OR       
 [US00007].[GID_ID] = @UserId OR       
 [US00009].[GID_ID] = @UserId OR       
 [US00006].[GID_ID] = @UserId) AND       
 ([CO00010].[CHK_TEAMSELL] = 1 OR       
 [SO00012].[GID_ID] = '38626532-6232-3836-534f-372f31312f32')) AND      
 ([US00014].[GID_ID] = @LoginGID)) AND       
 ([AC].[SI__ShareState] = 2 OR       
 (([AC].[SI__ShareState] < 2 OR       
([AC].[SI__ShareState] IS NULL)) AND       
 [US00015].[GID_ID]= @LoginGID))           
 END      
      
--@MySalesThisMonth      
IF @IsAdmin = 1      
 BEGIN       
 SELECT @MySalesThisMonth= ISNULL(COUNT(Distinct([AC].[GID_ID])),0)         
 FROM [AC]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 WHERE ([AC].[DTT_STARTTIME] >=  @ThisMonthStartDate AND --'2025-04-01 05:00:00.000' AND       
 ([AC].[DTT_STARTTIME] <=  DATEADD(SS,-1,@NextMonthStartDate) OR --'2025-05-01 04:59:59.000' OR       
 ([AC].[DTT_STARTTIME] IS NULL))) AND       
 [AC].[MLS_TYPE] = '11' AND       
 ([US00001].[GID_ID] = @UserId  OR       
 [US00002].[GID_ID]  = @UserId  OR       
 [US00004].[GID_ID]  = @UserId  OR       
 [US00003].[GID_ID]  = @UserId )      
 END      
ELSE      
 BEGIN       
 SELECT @MySalesThisMonth= ISNULL(COUNT(Distinct([AC].[GID_ID])),0)                    
 FROM [AC]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00005] ON [BC00005].[GID_ID] = [AC].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00005].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [AC].[GID_CreatedBy_US]      
 WHERE (((([AC].[DTT_STARTTIME] >= @ThisMonthStartDate OR      
 ([AC].[DTT_STARTTIME] IS NULL)) AND      
 ([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@NextMonthStartDate) OR      
 ([AC].[DTT_STARTTIME] IS NULL))) AND       
 [AC].[MLS_TYPE] = '11' AND       
 ([US00001].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId)) AND       
 ([US00006].[GID_ID] =  @LoginGID)) AND      
 ([AC].[SI__ShareState] = 2 OR       
 (([AC].[SI__ShareState] < 2 OR       
 ([AC].[SI__ShareState] IS NULL)) AND       
 [US00007].[GID_ID] = @LoginGID))         
 END      
       
--@MySalesLastMonth       
IF @IsAdmin = 1      
 BEGIN       
    SELECT  @MySalesLastonth=ISNULL(COUNT(Distinct([AC].[GID_ID])),0)         
 FROM [AC]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 WHERE ([AC].[DTT_STARTTIME] >= @LastMonthStartDate AND --'2025-03-01 06:00:00.000' AND       
 ([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@ThisMonthStartDate) OR --'2025-04-01 04:59:59.000' OR       
 ([AC].[DTT_STARTTIME] IS NULL))) AND       
 [AC].[MLS_TYPE] = '11' AND       
 ([US00001].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId)      
 END      
ELSE      
 BEGIN       
    SELECT  @MySalesLastonth=ISNULL(COUNT(Distinct([AC].[GID_ID])),0)                    
 FROM [AC]          
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00005] ON [BC00005].[GID_ID] = [AC].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00005].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [AC].[GID_CreatedBy_US]      
 WHERE (((([AC].[DTT_STARTTIME] >= @LastMonthStartDate OR      
 ([AC].[DTT_STARTTIME] IS NULL)) AND      
 ([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@ThisMonthStartDate) OR      
 ([AC].[DTT_STARTTIME] IS NULL))) AND       
 [AC].[MLS_TYPE] = '11' AND       
 ([US00001].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId)) AND       
 ([US00006].[GID_ID] =  @LoginGID)) AND      
 ([AC].[SI__ShareState] = 2 OR       
 (([AC].[SI__ShareState] < 2 OR       
 ([AC].[SI__ShareState] IS NULL)) AND       
 [US00007].[GID_ID] = @LoginGID))      
 END      
      
--@OthersAcThisMonth      
IF @IsAdmin = 1      
 BEGIN      
SELECT @OthersAcThisMonth = ISNULL(COUNT(Distinct([AC].[GID_ID])),0)          
FROM [AC]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]  
LEFT JOIN [AC_RELATED_CO] ON [AC].[GID_ID] = [AC_RELATED_CO].[GID_AC]  
LEFT JOIN [CO] [CO00002] ON [CO00002].[GID_ID] = [AC_RELATED_CO].[GID_CO]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [CO00002].[GID_TEAMLEADER_US]  
LEFT JOIN [CO_INVOLVES_US] ON [CO00002].[GID_ID] = [CO_INVOLVES_US].[GID_CO]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [CO_INVOLVES_US].[GID_US]  
WHERE   
([AC].[DTT_STARTTIME] >=  @ThisMonthStartDate AND --'2025-05-01 06:00:00.000' AND   
([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@NextMonthStartDate) OR --'2025-06-01 05:59:59.000' OR   
([AC].[DTT_STARTTIME] IS NULL))) AND   
[AC].[MLS_TYPE] = '11' AND   
[US00001].[GID_ID] IS NOT NULL AND   
([US00003].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId)  
  
--FROM [AC]      
-- LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]      
-- LEFT JOIN [AC_RELATED_CO] ON [AC].[GID_ID] = [AC_RELATED_CO].[GID_AC]      
-- LEFT JOIN [CO] [CO00002] ON [CO00002].[GID_ID] = [AC_RELATED_CO].[GID_CO]      
-- LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [CO00002].[GID_TEAMLEADER_US]      
-- LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
-- LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]      
-- LEFT JOIN [CO_INVOLVES_US] ON [CO00002].[GID_ID] = [CO_INVOLVES_US].[GID_CO]      
-- LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [CO_INVOLVES_US].[GID_US]      
-- LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
-- LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
-- WHERE ([AC].[DTT_STARTTIME] >=  @ThisMonthStartDate AND --'2025-04-01 05:00:00.000' AND       
-- ([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@NextMonthStartDate) OR --'2025-05-01 04:59:59.000' OR       
-- ([AC].[DTT_STARTTIME] IS NULL))) AND       
-- [AC].[MLS_TYPE] = '11' AND       
-- [US00001].[GID_ID] IS NOT NULL AND       
-- ([US00003].[GID_ID] = @UserId OR       
-- [US00005].[GID_ID] = @UserId OR       
-- [US00008].[GID_ID] = @UserId OR       
-- [US00006].[GID_ID] = @UserId)     
  END      
ELSE      
 BEGIN                 
 SELECT @OthersAcThisMonth = ISNULL(COUNT(Distinct([AC].[GID_ID])),0)                
 FROM [AC]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]      
 LEFT JOIN [AC_RELATED_CO] ON [AC].[GID_ID] = [AC_RELATED_CO].[GID_AC]      
 LEFT JOIN [CO] [CO00002] ON [CO00002].[GID_ID] = [AC_RELATED_CO].[GID_CO]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [CO00002].[GID_TEAMLEADER_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]      
 LEFT JOIN [CO_INVOLVES_US] ON [CO00002].[GID_ID] = [CO_INVOLVES_US].[GID_CO]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [CO_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00009] ON [BC00009].[GID_ID] = [AC].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00009].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00010] ON [US00010].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [US] [US00011] ON [US00011].[GID_ID] = [AC].[GID_CreatedBy_US]      
 WHERE (((([AC].[DTT_STARTTIME] >= @ThisMonthStartDate OR       
 ([AC].[DTT_STARTTIME] IS NULL)) AND       
 ([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@NextMonthStartDate) OR      
 ([AC].[DTT_STARTTIME] IS NULL))) AND       
 [AC].[MLS_TYPE] = '11' AND      
 [US00001].[GID_ID] = @UserId AND       
 ([US00003].[GID_ID] = @UserId OR       
 [US00005].[GID_ID] = @UserId OR      
 [US00008].[GID_ID] = @UserId OR       
 [US00006].[GID_ID] = @UserId)) AND      
 ([US00010].[GID_ID] = @LoginGID)) AND       
 ([AC].[SI__ShareState] = 2 OR      
 (([AC].[SI__ShareState] < 2 OR       
 ([AC].[SI__ShareState] IS NULL)) AND       
 [US00011].[GID_ID] = @LoginGID))        
 END      
      
--@OthersAcLastMonth       
IF @IsAdmin = 1      
 BEGIN       
 SELECT @OthersAcLastMonth = ISNULL(COUNT(Distinct([AC].[GID_ID])),0)       
FROM [AC]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]  
LEFT JOIN [AC_RELATED_CO] ON [AC].[GID_ID] = [AC_RELATED_CO].[GID_AC]  
LEFT JOIN [CO] [CO00002] ON [CO00002].[GID_ID] = [AC_RELATED_CO].[GID_CO]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [CO00002].[GID_TEAMLEADER_US]  
LEFT JOIN [CO_INVOLVES_US] ON [CO00002].[GID_ID] = [CO_INVOLVES_US].[GID_CO]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [CO_INVOLVES_US].[GID_US]  
WHERE   
([AC].[DTT_STARTTIME] >= @LastMonthStartDate AND --'2025-04-01 06:00:00.000' AND   
([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@ThisMonthStartDate) OR --'2025-05-01 05:59:59.000' OR   
([AC].[DTT_STARTTIME] IS NULL))) AND   
[AC].[MLS_TYPE] = '11' AND   
[US00001].[GID_ID] IS NOT NULL AND   
([US00003].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId)  
  
--FROM [AC]      
--LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]      
--LEFT JOIN [AC_RELATED_CO] ON [AC].[GID_ID] = [AC_RELATED_CO].[GID_AC]      
--LEFT JOIN [CO] [CO00002] ON [CO00002].[GID_ID] = [AC_RELATED_CO].[GID_CO]      
--LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [CO00002].[GID_TEAMLEADER_US]      
--LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
--LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]      
--LEFT JOIN [CO_INVOLVES_US] ON [CO00002].[GID_ID] = [CO_INVOLVES_US].[GID_CO]      
--LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [CO_INVOLVES_US].[GID_US]      
--LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
--LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
--WHERE ([AC].[DTT_STARTTIME] >= @LastMonthStartDate AND --'2025-03-01 06:00:00.000' AND       
--([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@ThisMonthStartDate) OR --'2025-04-01 04:59:59.000' OR       
--([AC].[DTT_STARTTIME] IS NULL))) AND       
--[AC].[MLS_TYPE] = '11' AND       
--[US00001].[GID_ID] IS NOT NULL AND       
--([US00003].[GID_ID] = @UserId OR       
--[US00005].[GID_ID] = @UserId OR       
--[US00008].[GID_ID] = @UserId OR       
--[US00006].[GID_ID]  = @UserId)      
 END      
ELSE      
 BEGIN       
 SELECT @OthersAcLastMonth = ISNULL(COUNT(Distinct([AC].[GID_ID])),0)                  
 FROM [AC]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]      
 LEFT JOIN [AC_RELATED_CO] ON [AC].[GID_ID] = [AC_RELATED_CO].[GID_AC]      
 LEFT JOIN [CO] [CO00002] ON [CO00002].[GID_ID] = [AC_RELATED_CO].[GID_CO]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [CO00002].[GID_TEAMLEADER_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]      
 LEFT JOIN [CO_INVOLVES_US] ON [CO00002].[GID_ID] = [CO_INVOLVES_US].[GID_CO]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [CO_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00009] ON [BC00009].[GID_ID] = [AC].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00009].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00010] ON [US00010].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [US] [US00011] ON [US00011].[GID_ID] = [AC].[GID_CreatedBy_US]      
 WHERE (((([AC].[DTT_STARTTIME] >= @LastMonthStartDate OR       
 ([AC].[DTT_STARTTIME] IS NULL)) AND       
 ([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@ThisMonthStartDate) OR      
 ([AC].[DTT_STARTTIME] IS NULL))) AND       
 [AC].[MLS_TYPE] = '11' AND      
 [US00001].[GID_ID] = @UserId AND       
 ([US00003].[GID_ID] = @UserId OR       
 [US00005].[GID_ID] = @UserId OR      
 [US00008].[GID_ID] = @UserId OR       
 [US00006].[GID_ID] = @UserId)) AND      
 ([US00010].[GID_ID] = @LoginGID)) AND       
 ([AC].[SI__ShareState] = 2 OR      
 (([AC].[SI__ShareState] < 2 OR       
 ([AC].[SI__ShareState] IS NULL)) AND       
 [US00011].[GID_ID] = @LoginGID))            
 END             
      
--MyLeads       
IF @IsAdmin = 1      
 BEGIN      
  ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE]         
 FROM [OP]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 WHERE ([US00003].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00001].[GID_ID] = @UserId) AND       
 ([OP].[MLS_STATUS] = '0' OR       
 ([OP].[MLS_STATUS] IS NULL)) AND       
 [OP].[MLS_SALESPROCESSSTAGE] = '1'      
)                  
 SELECT @MyLeads = ISNULL(COUNT([GID_ID]),0),                    
        @MyLeadsExptedVal = ISNULL(Sum([CUR_EXPECTEDVALUE]),0)                  
 FROM CTE       
END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                  
  SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE]                  
 FROM [OP]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00005] ON [BC00005].[GID_ID] = [OP].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00005].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [OP_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [OP].[GID_CreatedBy_US]      
 WHERE ((([US00003].[GID_ID] = @UserId OR      
 [US00004].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR      
 [US00001].[GID_ID] = @UserId) AND      
 ([OP].[MLS_STATUS] = '0' OR       
 ([OP].[MLS_STATUS] IS NULL)) AND      
 [OP].[MLS_SALESPROCESSSTAGE] = '1') AND      
 ([US00006].[GID_ID]= @LoginGID OR      
 [US00007].[GID_ID] = @LoginGID)) AND      
 ([OP].[SI__ShareState] = 2 OR (([OP].[SI__ShareState] < 2 OR      
 ([OP].[SI__ShareState] IS NULL)) AND      
 [US00008].[GID_ID] = @LoginGID))         
)                  
 SELECT @MyLeads = ISNULL(COUNT([GID_ID]),0),                    
        @MyLeadsExptedVal = ISNULL(Sum([CUR_EXPECTEDVALUE]),0)                  
 FROM CTE           
 END      
      
--MyTeamSellLeads      
IF @IsAdmin = 1      
 BEGIN       
 ; WITH CTE AS(                  
  SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE]        
  FROM [OP]      
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
LEFT JOIN [SO] [SO00005] ON [SO00005].[GID_ID] = [OP].[GID_FROM_SO]      
WHERE ([US00003].[GID_ID] = @UserId  OR       
[US00004].[GID_ID]= @UserId OR       
[US00002].[GID_ID] = @UserId  OR       
[US00001].[GID_ID] = @UserId ) AND       
([OP].[MLS_STATUS] = '0' OR       
([OP].[MLS_STATUS] IS NULL)) AND       
[OP].[MLS_SALESPROCESSSTAGE] = '1' AND       
[SO00005].[GID_ID] = '38626532-6232-3836-534f-372f31312f32'      
 )                  
 SELECT @MyTeamSellLeads = ISNULL(COUNT([GID_ID]),0),                                        
     @MyTeamSellLeadsExptedVal=ISNULL(Sum([CUR_EXPECTEDVALUE]),0)                   
 FROM CTE                  
 END      
      
ELSE      
 BEGIN                                         
 ; WITH CTE AS(                  
  SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE]                  
 FROM [OP]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [SO] [SO00005] ON [SO00005].[GID_ID] = [OP].[GID_FROM_SO]      
 LEFT JOIN [BC] [BC00006] ON [BC00006].[GID_ID] = [OP].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00006].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [OP_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [OP].[GID_CreatedBy_US]      
 WHERE ((([US00003].[GID_ID] = @UserId OR      
 [US00004].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00001].[GID_ID] = @UserId) AND       
 ([OP].[MLS_STATUS] = '0' OR       
 ([OP].[MLS_STATUS] IS NULL)) AND       
 [OP].[MLS_SALESPROCESSSTAGE] = '1' AND       
 [SO00005].[GID_ID] = '38626532-6232-3836-534f-372f31312f32') AND       
 ([US00007].[GID_ID] = @LoginGID OR      
 [US00008].[GID_ID] = @LoginGID)) AND      
 ([OP].[SI__ShareState] = 2 OR       
 (([OP].[SI__ShareState] < 2 OR       
 ([OP].[SI__ShareState] IS NULL)) AND      
 [US00009].[GID_ID] = @LoginGID))      
 )                  
 SELECT @MyTeamSellLeads = ISNULL(COUNT([GID_ID]),0),                                        
     @MyTeamSellLeadsExptedVal=ISNULL(Sum([CUR_EXPECTEDVALUE]),0)                   
 FROM CTE                  
 END      
      
--MyNewLeads      
IF @IsAdmin = 1      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE]      
 FROM [OP]      
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [OP].[GID_CREATEDBY_US]      
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]      
WHERE [US00002].[GID_ID] IS NOT NULL AND       
([US00004].[GID_ID] = @UserId OR      
[US00005].[GID_ID] = @UserId OR       
[US00003].[GID_ID] = @UserId OR      
[US00001].[GID_ID] = @UserId) AND       
([OP].[MLS_STATUS] = '0' OR       
([OP].[MLS_STATUS] IS NULL)) AND       
[OP].[MLS_SALESPROCESSSTAGE] = '1' AND       
([OP].[DTT_CREATIONTIME] >= @StartDateOfLastTwoWeeks AND --'2025-04-07 05:00:00.000' AND       
([OP].[DTT_CREATIONTIME] <=  DATEADD(SS,-1,@EndDateOfLastTwoWeeks) OR --'2025-04-21 04:59:59.000' OR      
([OP].[DTT_CREATIONTIME] IS NULL)))      
 )                   
 SELECT @MyNewLeads = ISNULL(COUNT([GID_ID]),0),                                        
 @MyNewLeadsExptedVal=ISNULL(Sum([CUR_EXPECTEDVALUE]),0)                  
 FROM CTE         
 END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE]                  
 FROM [OP]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [OP].[GID_CREATEDBY_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00006] ON [BC00006].[GID_ID] = [OP].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00006].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [OP_INVOLVES_US].[GID_US]      
 WHERE (([US00002].[GID_ID] IS NOT NULL AND       
 ([US00004].[GID_ID] = @UserId OR       
 [US00005].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId OR       
 [US00001].[GID_ID] = @UserId) AND       
 ([OP].[MLS_STATUS] = '0' OR       
 ([OP].[MLS_STATUS] IS NULL)) AND      
 [OP].[MLS_SALESPROCESSSTAGE] = '1' AND      
 ([OP].[DTT_CREATIONTIME] >= @StartDateOfLastTwoWeeks AND      
 ([OP].[DTT_CREATIONTIME] <= DATEADD(SS,-1,@EndDateOfLastTwoWeeks) OR       
 ([OP].[DTT_CREATIONTIME] IS NULL)))) AND       
 ([US00007].[GID_ID] = @LoginGID OR       
 [US00008].[GID_ID] = @LoginGID))AND      
 ([OP].[SI__ShareState] = 2 OR (([OP].[SI__ShareState] < 2 OR       
 ([OP].[SI__ShareState] IS NULL)) AND       
 [US00002].[GID_ID] = @LoginGID))      
 )                  
                   
 SELECT @MyNewLeads = ISNULL(COUNT([GID_ID]),0),                                        
 @MyNewLeadsExptedVal=ISNULL(Sum([CUR_EXPECTEDVALUE]),0)                  
 FROM CTE                  
 END      
      
--MyOverdueLeads                                              
--Overdue Leads      
IF @IsAdmin = 1      
 BEGIN       ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE]       
 FROM [OP]      
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
WHERE [OP].[MLS_SALESPROCESSSTAGE] = '1' AND      
([OP].[DTT_NEXTACTIONDATE] <= @TodayDate OR --'2025-04-22 05:00:00.000' OR       
([OP].[DTT_NEXTACTIONDATE] IS NULL)) AND       
([OP].[MLS_STATUS] = '0' OR      
([OP].[MLS_STATUS] IS NULL)) AND      
([US00001].[GID_ID] = @UserId OR       
[US00002].[GID_ID] = @UserId OR       
[US00004].[GID_ID] = @UserId OR      
[US00003].[GID_ID] = @UserId)      
 )                
 SELECT @MyOverdueLeads = ISNULL(COUNT([GID_ID]),0),                                        
 @MyOverdueLeadsExptedVal = ISNULL(Sum([CUR_EXPECTEDVALUE]),0)                    
 FROM CTE         
 END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE]                  
 FROM [OP]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00005] ON [BC00005].[GID_ID] = [OP].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00005].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [OP_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [OP].[GID_CreatedBy_US]      
 WHERE (([OP].[MLS_SALESPROCESSSTAGE] = '1' AND       
 ([OP].[DTT_NEXTACTIONDATE] <= @TodayDate OR      
 ([OP].[DTT_NEXTACTIONDATE] IS NULL)) AND      
 ([OP].[MLS_STATUS] = '0' OR       
 ([OP].[MLS_STATUS] IS NULL)) AND      
 ([US00001].[GID_ID] = @UserId OR      
 [US00002].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId)) AND       
 ([US00006].[GID_ID] = @LoginGID OR       
 [US00007].[GID_ID] = @LoginGID)) AND       
 ([OP].[SI__ShareState] = 2 OR       
 (([OP].[SI__ShareState] < 2 OR      
 ([OP].[SI__ShareState] IS NULL)) AND       
 [US00008].[GID_ID] = @LoginGID))         
 )                
       
 SELECT @MyOverdueLeads = ISNULL(COUNT([GID_ID]),0),              
 @MyOverdueLeadsExptedVal = ISNULL(Sum([CUR_EXPECTEDVALUE]),0)                    
 FROM CTE                  
 END      
      
--OpenOpps       
IF @IsAdmin = 1      
 BEGIN       
    ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE], [OP].[CUR_WEIGHTEDOPPLINEVALUE]         
 FROM [OP]      
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
WHERE ([US00003].[GID_ID] = @UserId OR       
[US00004].[GID_ID] = @UserId OR       
[US00002].[GID_ID] = @UserId OR       
[US00001].[GID_ID] = @UserId) AND      
([OP].[MLS_STATUS] = '0' OR       
([OP].[MLS_STATUS] IS NULL)) AND      
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR       
([OP].[MLS_SALESPROCESSSTAGE] IS NULL))      
 )                  
 SELECT @MyOpenOppsqty = ISNULL(COUNT([GID_ID]),0),                                  
 @MyOpenOppsExptedVal=ISNULL(Sum([CUR_EXPECTEDVALUE]),0),                                        
 @MyOpenOppsWeightedVal=ISNULL(Sum([CUR_WEIGHTEDOPPLINEVALUE]),0)                    
 FROM CTE          
 END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE], [OP].[CUR_WEIGHTEDOPPLINEVALUE]                  
 FROM [OP]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00005] ON [BC00005].[GID_ID] = [OP].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00005].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [OP_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [OP].[GID_CreatedBy_US]      
 WHERE ((([US00003].[GID_ID] = @UserId OR      
 [US00004].[GID_ID] = @UserId OR      
 [US00002].[GID_ID] = @UserId OR   
 [US00001].[GID_ID] = @UserId) AND      
 ([OP].[MLS_STATUS] = '0' OR       
 ([OP].[MLS_STATUS] IS NULL)) AND       
 ([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR       
 ([OP].[MLS_SALESPROCESSSTAGE] IS NULL))) AND      
 ([US00006].[GID_ID] = @LoginGID OR      
 [US00007].[GID_ID] = @LoginGID)) AND       
 ([OP].[SI__ShareState] = 2 OR       
 (([OP].[SI__ShareState] < 2 OR       
 ([OP].[SI__ShareState] IS NULL)) AND       
 [US00008].[GID_ID] = @LoginGID))            
 )                  
 SELECT @MyOpenOppsqty = ISNULL(COUNT([GID_ID]),0),                                  
 @MyOpenOppsExptedVal=ISNULL(Sum([CUR_EXPECTEDVALUE]),0),                                        
 @MyOpenOppsWeightedVal=ISNULL(Sum([CUR_WEIGHTEDOPPLINEVALUE]),0)                    
 FROM CTE                  
 END      
      
--My Opps - Team Sell        
IF @IsAdmin = 1      
 BEGIN       
 ;WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE], [OP].[CUR_WEIGHTEDEXPECTEDVALUE]           
 FROM [OP]      
LEFT JOIN [SO] [SO00001] ON [SO00001].[GID_ID] = [OP].[GID_FROM_SO]      
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]      
WHERE ([OP].[MLS_STATUS] = '0' OR       
([OP].[MLS_STATUS] IS NULL)) AND       
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR       
([OP].[MLS_SALESPROCESSSTAGE] IS NULL)) AND       
[SO00001].[GID_ID] = '38626532-6232-3836-534f-372f31312f32' AND       
([US00002].[GID_ID]  = @UserId OR       
[US00003].[GID_ID]  = @UserId OR      
[US00004].[GID_ID]  = @UserId OR       
[US00005].[GID_ID]  = @UserId)  
)                  
 SELECT @MyTeamSellOpps_count = ISNULL(COUNT([GID_ID]),0),           
 @MyTeamSellOpps_expval = ISNULL(Sum([CUR_EXPECTEDVALUE]),0),                                          
 @MyTeamSellOpps_wghtval = ISNULL(Sum([CUR_WEIGHTEDEXPECTEDVALUE]),0)                  
 FROM CTE          
END      
ELSE      
 BEGIN       
 ;WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE], [OP].[CUR_WEIGHTEDEXPECTEDVALUE]                   
 FROM [OP]                
 LEFT JOIN [SO] [SO00001] ON [SO00001].[GID_ID] = [OP].[GID_FROM_SO]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00005] ON [BC00005].[GID_ID] = [OP].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00005].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [OP_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [OP].[GID_CreatedBy_US]      
 WHERE ((([OP].[MLS_STATUS] = '0' OR      
 ([OP].[MLS_STATUS] IS NULL)) AND      
 ([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR      
 ([OP].[MLS_SALESPROCESSSTAGE] IS NULL)) AND      
 [SO00001].[GID_ID] = '38626532-6232-3836-534f-372f31312f32' AND      
 ([US00002].[GID_ID]  = @UserId OR       
 [US00003].[GID_ID]  = @UserId OR      
 [US00004].[GID_ID]  = @UserId)) AND       
 ([US00006].[GID_ID] = @LoginGID OR       
 [US00007].[GID_ID] = @LoginGID)) AND       
 ([OP].[SI__ShareState] = 2 OR       
 (([OP].[SI__ShareState] < 2 OR       
 ([OP].[SI__ShareState] IS NULL)) AND      
 [US00008].[GID_ID] = @LoginGID))      
 )                  
 SELECT @MyTeamSellOpps_count = ISNULL(COUNT([GID_ID]),0),                               
 @MyTeamSellOpps_expval = ISNULL(Sum([CUR_EXPECTEDVALUE]),0),                                          
 @MyTeamSellOpps_wghtval = ISNULL(Sum([CUR_WEIGHTEDEXPECTEDVALUE]),0)             
 FROM CTE                  
 END      
      
--MyDueNext30DaysOpps      
IF @IsAdmin = 1      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE], [OP].[CUR_WEIGHTEDEXPECTEDVALUE]        
 FROM [OP]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
WHERE ([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId) AND   
([OP].[MLS_STATUS] = '0' OR   
([OP].[MLS_STATUS] IS NULL)) OR   
(([OP].[DTT_EXPCLOSEDATE] >= @TodayDate AND  --'2025-05-12 05:00:00.000' AND SELECT DATEADD(SS,-1,DATEADD(MONTH,1, '2025-05-12 05:00:00.000'))  
([OP].[DTT_EXPCLOSEDATE] <= DATEADD(SS,-1,DATEADD(MONTH,1, @TodayDate)) OR --'2025-06-12 04:59:59.000' OR   
([OP].[DTT_EXPCLOSEDATE] IS NULL))) AND   
([OP].[DTT_NEXTACTIONDATE] >= @TodayDate AND  --'2025-05-12 05:00:00.000' AND   
([OP].[DTT_NEXTACTIONDATE] <= DATEADD(SS,-1,DATEADD(MONTH,1, @TodayDate)) OR --'2025-06-12 04:59:59.000' OR   
([OP].[DTT_NEXTACTIONDATE] IS NULL)))) AND   
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR   
([OP].[MLS_SALESPROCESSSTAGE] IS NULL))   
)  
 SELECT @MyDueNext30DaysOpps_count = ISNULL(COUNT([GID_ID]),0),                                          
 @MyDueNext30DaysOpps_expval = ISNULL(Sum([CUR_EXPECTEDVALUE]),0),                                          
 @MyDueNext30DaysOpps_wghtval = ISNULL(Sum([CUR_WEIGHTEDEXPECTEDVALUE]),0)                   
 FROM CTE        
 END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE], [OP].[CUR_WEIGHTEDEXPECTEDVALUE]                   
 FROM [OP]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00004] ON [BC00004].[GID_ID] = [OP].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00004].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [OP_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [OP].[GID_CreatedBy_US]      
 WHERE ((([US00001].[GID_ID] = @UserId OR      
 [US00002].[GID_ID] = @UserId OR      
 [US00003].[GID_ID] = @UserId) AND      
 ([OP].[MLS_STATUS] = '0' OR      
 ([OP].[MLS_STATUS] IS NULL)) AND      
 (([OP].[DTT_EXPCLOSEDATE] >= @Today AND      
 ([OP].[DTT_EXPCLOSEDATE] <= DATEADD(DAY, 30, CAST(GETDATE()As Date)) OR      
 ([OP].[DTT_EXPCLOSEDATE] IS NULL))) OR      
 ([OP].[DTT_NEXTACTIONDATE] >= @Today AND       
 ([OP].[DTT_NEXTACTIONDATE] <= DATEADD(DAY, 30, CAST(GETDATE()As Date)) OR      
 ([OP].[DTT_NEXTACTIONDATE] IS NULL)))) AND       
 ([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR       
 ([OP].[MLS_SALESPROCESSSTAGE] IS NULL))) AND      
 ([US00005].[GID_ID] = @LoginGID OR      
 [US00006].[GID_ID] = @LoginGID)) AND       
 ([OP].[SI__ShareState] = 2 OR       
 (([OP].[SI__ShareState] < 2 OR      
 ([OP].[SI__ShareState] IS NULL)) AND      
 [US00007].[GID_ID] = @LoginGID))              
 )      
 SELECT @MyDueNext30DaysOpps_count = ISNULL(COUNT([GID_ID]),0),                                          
 @MyDueNext30DaysOpps_expval = ISNULL(Sum([CUR_EXPECTEDVALUE]),0),                                          
 @MyDueNext30DaysOpps_wghtval = ISNULL(Sum([CUR_WEIGHTEDEXPECTEDVALUE]),0)                   
 FROM CTE                  
 END      
      
--MyOverdueOpps      
IF @IsAdmin = 1      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE], [OP].[CUR_WEIGHTEDEXPECTEDVALUE]         
 FROM [OP]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
WHERE ([US00001].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId) AND   
([OP].[MLS_STATUS] = '0' OR   
([OP].[MLS_STATUS] IS NULL)) AND   
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR   
([OP].[MLS_SALESPROCESSSTAGE] IS NULL)) AND   
(([OP].[DTT_EXPCLOSEDATE] <=  @TodayDate OR --'2025-05-12 05:00:00.000' OR   
([OP].[DTT_EXPCLOSEDATE] IS NULL)) AND   
([OP].[DTT_NEXTACTIONDATE] <=  @TodayDate OR --'2025-05-12 05:00:00.000' OR   
([OP].[DTT_NEXTACTIONDATE] IS NULL)))  
 )      
 SELECT @MyOverdueOpps_count = ISNULL(COUNT([GID_ID]),0),                                          
 @MyOverdueOpps_expval = ISNULL(Sum([CUR_EXPECTEDVALUE]),0),                                          
 @MyOverdueOpps_wghtval = ISNULL(Sum([CUR_WEIGHTEDEXPECTEDVALUE]),0)                  
 FROM CTE         
  END      
ELSE      
 BEGIN       
; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE], [OP].[CUR_WEIGHTEDEXPECTEDVALUE]                    
FROM [OP]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [BC] [BC00005] ON [BC00005].[GID_ID] = [OP].[GID_RELATED_BC]  
LEFT JOIN [US_RELATED_BC] ON [BC00005].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [OP_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [OP].[GID_CreatedBy_US]  
WHERE ((([US00001].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId) AND   
([OP].[MLS_STATUS] = '0' OR   
([OP].[MLS_STATUS] IS NULL)) AND   
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR   
([OP].[MLS_SALESPROCESSSTAGE] IS NULL)) AND   
(([OP].[DTT_EXPCLOSEDATE] <= @TodayDate OR --'2025-05-12 04:00:00.000' OR   
([OP].[DTT_EXPCLOSEDATE] IS NULL)) AND   
([OP].[DTT_NEXTACTIONDATE] <= @TodayDate OR --'2025-05-12 04:00:00.000' OR   
([OP].[DTT_NEXTACTIONDATE] IS NULL)))) AND   
([US00006].[GID_ID] =@LoginGID OR   
[US00007].[GID_ID] = @LoginGID)) AND   
([OP].[SI__ShareState] = 2 OR   
(([OP].[SI__ShareState] < 2 OR   
([OP].[SI__ShareState] IS NULL)) AND   
[US00008].[GID_ID] = @LoginGID))  
)      
 SELECT @MyOverdueOpps_count = ISNULL(COUNT([GID_ID]),0),                                          
 @MyOverdueOpps_expval = ISNULL(Sum([CUR_EXPECTEDVALUE]),0),                                          
 @MyOverdueOpps_wghtval = ISNULL(Sum([CUR_WEIGHTEDEXPECTEDVALUE]),0)                  
 FROM CTE                  
 END      
      
--Won Opportunities      
IF @IsAdmin = 1      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_OPPLINEVALUE]                
 FROM [OP]      
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
WHERE ([OP].[DTT_DATECLOSED] >= dateadd(hh,1,@ThreeMonthsAgoDate) AND --'2025-02-13 06:00:00.000' AND   
([OP].[DTT_DATECLOSED] <= DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR --'2025-05-13 04:59:59.000' OR   ([OP].[DTT_DATECLOSED] IS NULL))) AND   
([US00003].[GID_ID] = @UserId OR       
[US00004].[GID_ID] = @UserId OR       
[US00002].[GID_ID] = @UserId OR       
[US00001].[GID_ID] = @UserId) AND       
[OP].[MLS_STATUS] = '3' AND       
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR       
([OP].[MLS_SALESPROCESSSTAGE] IS NULL))      
 )                  
SELECT @MyOpps_won_Qty = ISNULL(COUNT([GID_ID]),0),                                          
 @MyOpps_won_Tot = ISNULL(Sum([CUR_OPPLINEVALUE]),0)                    
 FROM CTE        
 END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_OPPLINEVALUE]                  
 FROM [OP]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00005] ON [BC00005].[GID_ID] = [OP].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00005].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [OP_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [OP].[GID_CreatedBy_US]      
 WHERE (((([OP].[DTT_DATECLOSED] >= @ThreeMonthsAgoDate OR      
 ([OP].[DTT_DATECLOSED] IS NULL)) AND      
 ([OP].[DTT_DATECLOSED] <= DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR      
 ([OP].[DTT_DATECLOSED] IS NULL))) AND      
 ([US00003].[GID_ID] = @UserId OR      
 [US00004].[GID_ID] = @UserId OR      
 [US00002].[GID_ID] = @UserId OR       
 [US00001].[GID_ID] = @UserId) AND       
 [OP].[MLS_STATUS] = '3' AND       
 ([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR      
 ([OP].[MLS_SALESPROCESSSTAGE] IS NULL))) AND       
 ([US00006].[GID_ID] = @LoginGID OR      
 [US00007].[GID_ID] = @LoginGID)) AND       
 ([OP].[SI__ShareState] = 2 OR       
 (([OP].[SI__ShareState] < 2 OR      
 ([OP].[SI__ShareState] IS NULL)) AND      
 [US00008].[GID_ID] = @LoginGID))        
 )                  
 SELECT @MyOpps_won_Qty = ISNULL(COUNT([GID_ID]),0),                                          
 @MyOpps_won_Tot = ISNULL(Sum([CUR_OPPLINEVALUE]),0)                    
 FROM CTE                  
--last 90 days                  
 END      
   
 --Lost Opportunities      
IF @IsAdmin = 1      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_OPPLINEVALUE]                  
FROM [OP]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
WHERE ([OP].[DTT_DATECLOSED] >= dateadd(hh,1,@ThreeMonthsAgoDate) AND --'2025-02-13 06:00:00.000' AND   
([OP].[DTT_DATECLOSED] <= DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR --'2025-05-13 04:59:59.000' OR   
([OP].[DTT_DATECLOSED] IS NULL))) AND   
([US00003].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00001].[GID_ID] = @UserId) AND   
[OP].[MLS_STATUS] = '4' AND   
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR   
([OP].[MLS_SALESPROCESSSTAGE] IS NULL))  
)  
 SELECT @MyOpps_Lost_Qty = ISNULL(COUNT([GID_ID]),0.0),                                          
 @MyOpps_Lost_Tot = ISNULL(Sum([CUR_OPPLINEVALUE]),0.0)                  
 FROM CTE          
END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_OPPLINEVALUE]                  
 FROM [OP]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00005] ON [BC00005].[GID_ID] = [OP].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00005].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [OP_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [OP].[GID_CreatedBy_US]      
 WHERE (((([OP].[DTT_DATECLOSED] >= @ThreeMonthsAgoDate OR      
 ([OP].[DTT_DATECLOSED] IS NULL)) AND       
 ([OP].[DTT_DATECLOSED] <= DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR      
 ([OP].[DTT_DATECLOSED] IS NULL))) AND       
 ([US00003].[GID_ID] = @UserId OR      
 [US00004].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00001].[GID_ID] = @UserId) AND       
 [OP].[MLS_STATUS] = '4' AND       
 ([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR       
 ([OP].[MLS_SALESPROCESSSTAGE] IS NULL))) AND       
 ([US00006].[GID_ID] = @LoginGID OR       
 [US00007].[GID_ID] = @LoginGID)) AND       
 ([OP].[SI__ShareState] = 2 OR       
 (([OP].[SI__ShareState] < 2 OR       
 ([OP].[SI__ShareState] IS NULL)) AND       
 [US00008].[GID_ID] = @LoginGID))          
  )      
SELECT @MyOpps_Lost_Qty = ISNULL(COUNT([GID_ID]),0.0),                                          
 @MyOpps_Lost_Tot = ISNULL(Sum([CUR_OPPLINEVALUE]),0.0)                  
 FROM CTE                  
  END      
  
 --Cancelled Opportunities  
IF @IsAdmin = 1      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_OPPLINEVALUE]        
 FROM [OP]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
WHERE ([OP].[DTT_DATECLOSED] >= dateadd(hh,1,@ThreeMonthsAgoDate) AND --'2025-02-13 06:00:00.000' AND   
([OP].[DTT_DATECLOSED] <= DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR --'2025-05-13 04:59:59.000' OR   
([OP].[DTT_DATECLOSED] IS NULL))) AND   
([US00003].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId) AND   
[OP].[MLS_STATUS] = '5' AND   
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR   
([OP].[MLS_SALESPROCESSSTAGE] IS NULL))  
)  
 SELECT @MyOpps_Cancelled_Qty = ISNULL(COUNT([GID_ID]),0),                                          
 @MyOpps_Cancelled_Tot = ISNULL(Sum([CUR_OPPLINEVALUE]),0)                    
 FROM CTE        
 END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_OPPLINEVALUE]                  
 FROM [OP]                  
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00005] ON [BC00005].[GID_ID] = [OP].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00005].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [OP_INVOLVES_US].[GID_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [OP].[GID_CreatedBy_US]      
 WHERE (((([OP].[DTT_DATECLOSED] >= @ThreeMonthsAgoDate OR      
 ([OP].[DTT_DATECLOSED] IS NULL)) AND      
 ([OP].[DTT_DATECLOSED] <= DATEADD(SS,-1,DATEADD(DD,1,@TodayDate))  OR       
 ([OP].[DTT_DATECLOSED] IS NULL))) AND       
 ([US00003].[GID_ID] = @UserId OR      
 [US00004].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00001].[GID_ID] = @LoginGID) AND      
 [OP].[MLS_STATUS] = '5' AND       
 ([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR       
 ([OP].[MLS_SALESPROCESSSTAGE] IS NULL))) AND       
 ([US00006].[GID_ID] = @LoginGID OR      
 [US00007].[GID_ID] = @LoginGID)) AND      
 ([OP].[SI__ShareState] = 2 OR       
 (([OP].[SI__ShareState] < 2 OR       
 ([OP].[SI__ShareState] IS NULL)) AND [US00008].[GID_ID] = @LoginGID))      
 )                  
 SELECT @MyOpps_Cancelled_Qty = ISNULL(COUNT([GID_ID]),0),                                          
 @MyOpps_Cancelled_Tot = ISNULL(Sum([CUR_OPPLINEVALUE]),0)                    
 FROM CTE       
  END      
-- Calculate the won rate, ensuring no division by zero                                  
SET @MyOpps_won_Rate =                                   
    CASE                                   
        WHEN CAST(@MyOpps_won_Qty AS MONEY) + CAST(@MyOpps_Lost_Qty AS MONEY) = 0 THEN 0  -- Return 0 if the total is zero                                  
        ELSE (CAST(@MyOpps_won_Qty AS MONEY)/(CAST(@MyOpps_won_Qty AS MONEY) + CAST(@MyOpps_Lost_Qty AS MONEY))) *100                               
    END                                  
                    
-- Calculate the lost rate, ensuring no division by zero                                  
SET @MyOpps_Lost_Rate =                                   
    CASE                                   
        WHEN CAST(@MyOpps_won_Qty AS MONEY) + CAST(@MyOpps_Lost_Qty AS MONEY) = 0 THEN 0  -- Return 0 if the total is zero                                  
        ELSE (CAST(@MyOpps_Lost_Qty AS MONEY)/(CAST(@MyOpps_won_Qty AS MONEY) + CAST(@MyOpps_Lost_Qty AS MONEY))) *100                  
    END                                  
                                  
-- Calculate the cancelled rate, ensuring no division by zero                                  
SET @MyOpps_Cancelled_Rate =                                   
    CASE                                   
        WHEN CAST(@MyOpps_won_Qty AS MONEY) + CAST(@MyOpps_Lost_Qty AS MONEY) + CAST(@MyOpps_Cancelled_Qty AS MONEY) = 0 THEN 0  -- Return 0 if the total is zero                                  
        ELSE (CAST(@MyOpps_Cancelled_Qty AS MONEY)/(CAST(@MyOpps_won_Qty AS MONEY) + CAST(@MyOpps_Lost_Qty AS MONEY) + CAST(@MyOpps_Cancelled_Qty AS MONEY))) *100                              
    END                     
      
--MY RFQ's Quotes      
IF @IsAdmin = 1      
 BEGIN       
 ; WITH CTE AS(                
 SELECT Distinct [QT].[GID_ID],                 
 [QT].[CUR_LINETOTALOPEN],                 
 [QT].SI__GROSSMARGIN         
  FROM [QT]       
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]      
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [QT].[GID_PEER_US]      
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
WHERE ([US00001].[GID_ID] = @UserId OR      
[US00004].[GID_ID] = @UserId OR      
[US00002].[GID_ID] = @UserId OR      
[US00003].[GID_ID] = @UserId OR       
[US00008].[GID_ID] = @UserId OR      
[US00007].[GID_ID] = @UserId OR       
[US00006].[GID_ID] = @UserId OR       
[US00005].[GID_ID] = @UserId) AND       
([QT].[MLS_STATUS] = '0' OR       
([QT].[MLS_STATUS] IS NULL)) AND       
(([QT].[MLS_STAGE] <> '6' OR       
([QT].[MLS_STAGE] IS NULL)) AND       
([QT].[MLS_STAGE] <> '7' OR       
([QT].[MLS_STAGE] IS NULL)) AND       
([QT].[MLS_STAGE] <> '0' AND       
[QT].[MLS_STAGE] IS NOT NULL) AND       
([QT].[MLS_STAGE] <> '8' OR       ([QT].[MLS_STAGE] IS NULL)))      
 )                
SELECT @OpenRFQsQty = ISNULL(COUNT([GID_ID]),0),                  
 @OpenRFQstotal = ISNULL(sum([CUR_LINETOTALOPEN]),0),                    
 @OpenRFQsmargin = ISNULL(AVG(SI__GROSSMARGIN),0)                
 FROM CTE        
 END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                
 SELECT Distinct [QT].[GID_ID],                 
 [QT].[CUR_LINETOTALOPEN],                 
 [QT].SI__GROSSMARGIN                  
 FROM [QT]       
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [QT].[GID_PEER_US]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00009] ON [BC00009].[GID_ID] = [QT].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00009].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00010] ON [US00010].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [US] [US00011] ON [US00011].[GID_ID] = [QT].[GID_CreatedBy_US]      
 WHERE ((([US00001].[GID_ID]= @UserId OR      
 [US00004].[GID_ID] = @UserId OR      
 [US00002].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId OR      
 [US00008].[GID_ID] = @UserId OR       
 [US00007].[GID_ID] = @UserId OR       
 [US00006].[GID_ID] = @UserId OR       
 [US00005].[GID_ID] = @UserId) AND       
 ([QT].[MLS_STATUS] = '0' OR       
 ([QT].[MLS_STATUS] IS NULL)) AND      
 (([QT].[MLS_STAGE] <> '6' OR      
 ([QT].[MLS_STAGE] IS NULL)) AND       
 ([QT].[MLS_STAGE] <> '7' OR       
 ([QT].[MLS_STAGE] IS NULL)) AND      
 ([QT].[MLS_STAGE] <> '0' AND       
 [QT].[MLS_STAGE] IS NOT NULL) AND       
 ([QT].[MLS_STAGE] <> '8' OR       
 ([QT].[MLS_STAGE] IS NULL)))) AND      
 ([US00010].[GID_ID] = @LoginGID)) AND      
 ([QT].[SI__ShareState] = 2 OR       
 (([QT].[SI__ShareState] < 2 OR       
 ([QT].[SI__ShareState] IS NULL)) AND       
 [US00011].[GID_ID] = @LoginGID))      
 )                
 SELECT @OpenRFQsQty = ISNULL(COUNT([GID_ID]),0),                  
 @OpenRFQstotal = ISNULL(sum([CUR_LINETOTALOPEN]),0),                    
 @OpenRFQsmargin = ISNULL(AVG(SI__GROSSMARGIN),0)                
 FROM CTE                
 END      
      
--All OpenQuotes       
IF @IsAdmin = 1      
 BEGIN       
 ; WITH CTE AS(                
 SELECT Distinct [QT].[GID_ID],               
 [QT].[CUR_LINETOTALOPEN],                 
 [QT].SI__GROSSMARGIN                
 FROM [QT]           
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]      
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [QT].[GID_PEER_US]      
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
WHERE ([US00001].[GID_ID] = @UserId OR      
[US00005].[GID_ID] = @UserId OR       
[US00008].[GID_ID] = @UserId OR      
[US00006].[GID_ID] = @UserId OR      
[US00007].[GID_ID] = @UserId OR       
[US00004].[GID_ID] = @UserId OR       
[US00003].[GID_ID] = @UserId OR       
[US00002].[GID_ID] = @UserId) AND      
([QT].[MLS_STATUS] = '0' OR      
([QT].[MLS_STATUS] IS NULL))      
 )                
 SELECT @AllOpenQuotesQty = ISNULL(COUNT([GID_ID]),0),                  
 @AllOpenQuotesTotal = ISNULL(sum([CUR_LINETOTALOPEN]),0),   
 @MyQuotePipeline = ISNULL(sum([CUR_LINETOTALOPEN]),0),  
 @AllOpenQuotesMargin = ISNULL(AVG(SI__GROSSMARGIN),0)                
 FROM CTE        
 END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                
 SELECT Distinct [QT].[GID_ID],               
 [QT].[CUR_LINETOTALOPEN],                 
 [QT].SI__GROSSMARGIN                
 FROM [QT]                
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [QT].[GID_PEER_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
 LEFT JOIN [BC] [BC00009] ON [BC00009].[GID_ID] = [QT].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00009].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00010] ON [US00010].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [US] [US00011] ON [US00011].[GID_ID] = [QT].[GID_CreatedBy_US]      
 WHERE ((([US00001].[GID_ID] = @UserId OR       
 [US00005].[GID_ID] = @UserId OR       
 [US00008].[GID_ID] = @UserId OR       
 [US00006].[GID_ID] = @UserId OR       
 [US00007].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId) AND      
 ([QT].[MLS_STATUS] = '0' OR       
 ([QT].[MLS_STATUS] IS NULL))) AND       
 ([US00010].[GID_ID] = @LoginGID)) AND      
 ([QT].[SI__ShareState] = 2 OR      
 (([QT].[SI__ShareState] < 2 OR       
 ([QT].[SI__ShareState] IS NULL)) AND      
 [US00011].[GID_ID] = @LoginGID))      
 )                
 SELECT @AllOpenQuotesQty = ISNULL(COUNT([GID_ID]),0),                  
 @AllOpenQuotesTotal = ISNULL(sum([CUR_LINETOTALOPEN]),0),    
 @MyQuotePipeline = ISNULL(sum([CUR_LINETOTALOPEN]),0),  
 @AllOpenQuotesMargin = ISNULL(AVG(SI__GROSSMARGIN),0)                
 FROM CTE                
 END      
      
--OpenStrategicQuotes      
IF @IsAdmin = 1      
 BEGIN       
       
 ; WITH CTE AS(                
 SELECT Distinct [QT].[GID_ID],                 
 [QT].[CUR_LINETOTALOPEN],                
 [QT].CUR_ExpectedValue,                
 [QT].SI__GROSSMARGIN                 
 FROM [QT]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [QT].[GID_PEER_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
LEFT JOIN [BU] [BU00009] ON [BU00009].[GID_ID] = [QT].[GID_RELATED_BU]  
LEFT JOIN [QL] [QL00010] ON [QL00010].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00011] ON [PF00011].[GID_ID] = [QL00010].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00012] ON [PC00012].[GID_ID] = [PF00011].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00013] ON [VE00013].[GID_ID] = [QL00010].[GID_FOR_VE]  
WHERE ([QT].[CHK_BUDGET] = 0 OR   
([QT].[CHK_BUDGET] IS NULL)) AND   
([QT].[MLS_STATUS] = '0' OR   
([QT].[MLS_STATUS] IS NULL)) AND   
([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId OR   
[US00005].[GID_ID] = @UserId) AND   
(([BU00009].[GID_ID] = '35643030-3932-3466-4255-312f362f3230' AND   
[QT].[MLS_SOURCE] = '1') OR   
(([BU00009].[GID_ID] = '34636139-3864-6333-4255-312f362f3230' OR   
[BU00009].[GID_ID] = '36343933-3238-3530-4255-312f362f3230') AND   
([QT].[CUR_LINETOTALOPEN] >= '20000' OR   
([QT].[CUR_LINETOTALOPEN] >= '5000' AND   
([PC00012].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR   
[PF00011].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
[PF00011].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR   
[VE00013].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
[VE00013].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR   
[VE00013].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))))  
)  
 SELECT @OpenStrategicQuotesQty = ISNULL(COUNT([GID_ID]),0),                                  
 @OpenStrategicQuotesTotal = ISNULL(SUM([CUR_LINETOTALOPEN]),0),    
 @OpenStrategicQuotesMargin = ISNULL(AVG(SI__GROSSMARGIN),0)                
 FROM CTE       
 END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                
 SELECT Distinct [QT].[GID_ID],                 
 [QT].[CUR_LINETOTALOPEN],                
 [QT].CUR_ExpectedValue,                
 [QT].SI__GROSSMARGIN                 
  FROM [QT]          
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]      
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [QT].[GID_PEER_US]      
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
 LEFT JOIN [QL] [QL00009] ON [QL00009].[GID_IN_QT] = [QT].[GID_ID]      
 LEFT JOIN [PF] [PF00010] ON [PF00010].[GID_ID] = [QL00009].[GID_RELATED_PF]      
 LEFT JOIN [PC] [PC00011] ON [PC00011].[GID_ID] = [PF00010].[GID_RELATED_PC]      
 LEFT JOIN [VE] [VE00012] ON [VE00012].[GID_ID] = [QL00009].[GID_FOR_VE]      
 LEFT JOIN [BC] [BC00013] ON [BC00013].[GID_ID] = [QT].[GID_RELATED_BC]      
 LEFT JOIN [US_RELATED_BC] ON [BC00013].[GID_ID] = [US_RELATED_BC].[GID_BC]      
 LEFT JOIN [US] [US00014] ON [US00014].[GID_ID] = [US_RELATED_BC].[GID_US]      
 LEFT JOIN [US] [US00015] ON [US00015].[GID_ID] = [QT].[GID_CreatedBy_US]      
 WHERE ((([QT].[CHK_BUDGET] = 0 OR       
 ([QT].[CHK_BUDGET] IS NULL)) AND       
 ([QT].[MLS_STATUS] = '0' OR       
 ([QT].[MLS_STATUS] IS NULL)) AND      
 ([US00001].[GID_ID] = @UserId OR      
 [US00004].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId OR      
 [US00007].[GID_ID] = @UserId OR       
 [US00008].[GID_ID] = @UserId OR       
 [US00006].[GID_ID] = @UserId OR       
 [US00005].[GID_ID] = @UserId) AND       
 ([QT].[CUR_LINETOTALOPEN] >= '20000' OR       
 ([QT].[CUR_LINETOTALOPEN] >= '5000' AND       
 ([PC00011].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR      
 [QT].[MLS_TYPE] = '6' OR      
 [PF00010].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR      
 [PF00010].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR       
 [VE00012].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR       
 [VE00012].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR      
 [VE00012].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))) AND      
 ([US00014].[GID_ID]  = @LoginGID)) AND       
 ([QT].[SI__ShareState] = 2 OR       
 (([QT].[SI__ShareState] < 2 OR      
 ([QT].[SI__ShareState] IS NULL)) AND       
 [US00015].[GID_ID] = @LoginGID))        
 )                
 SELECT @OpenStrategicQuotesQty = ISNULL(COUNT([GID_ID]),0),                                  
 @OpenStrategicQuotesTotal = ISNULL(SUM([CUR_LINETOTALOPEN]),0),                 
 @OpenStrategicQuotesMargin = ISNULL(AVG(SI__GROSSMARGIN),0)         
 FROM CTE     
 END      
      
--NewStrategicQuotes       
IF @IsAdmin = 1      
 BEGIN       
 ; WITH CTE AS(                
 SELECT Distinct [QT].[GID_ID],                 
 [QT].[CUR_LINETOTALOPEN],                
 [QT].CUR_ExpectedValue,                
 [QT].SI__GROSSMARGIN                 
 FROM [QT]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [QT].[GID_PEER_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]  
LEFT JOIN [QL] [QL00007] ON [QL00007].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00008] ON [PF00008].[GID_ID] = [QL00007].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00009] ON [PC00009].[GID_ID] = [PF00008].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00010] ON [VE00010].[GID_ID] = [QL00007].[GID_FOR_VE]  
WHERE ([QT].[DTT_CREATIONTIME] >= @StartDateOfLastTwoWeeks AND --'2025-04-27 11:00:00.000' AND   
([QT].[DTT_CREATIONTIME] <= DATEADD(SS,-1,@EndDateOfLastTwoWeeks) OR --'2025-05-11 10:59:59.000' OR   
([QT].[DTT_CREATIONTIME] IS NULL))) AND   
([QT].[CHK_BUDGET] = 0 OR   
([QT].[CHK_BUDGET] IS NULL)) AND   
([QT].[MLS_STATUS] = '0' OR   
([QT].[MLS_STATUS] IS NULL)) AND   
([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId OR   
[US00005].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId) AND   
([QT].[CUR_LINETOTALOPEN] >= '20000' OR   
([QT].[CUR_LINETOTALOPEN] >= '5000' AND   
([PC00009].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR   
[PF00008].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
[PF00008].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR   
[VE00010].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
[VE00010].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR   
[VE00010].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))  
)  
SELECT @NewStrategicQuotesQty = ISNULL(COUNT([GID_ID]),0),                                         
 @NewStrategicQuotesTotal = ISNULL(SUM([CUR_LINETOTALOPEN]),0),                 
 @NewStrategicQuotesMargin = ISNULL(AVG(SI__GROSSMARGIN),0)                
 FROM CTE        
 END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                
 SELECT Distinct [QT].[GID_ID],                 
 [QT].[CUR_LINETOTALOPEN],                
 [QT].CUR_ExpectedValue,                
 [QT].SI__GROSSMARGIN                 
 FROM [QT]  
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [QT].[GID_PEER_US]  
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]  
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]  
 LEFT JOIN [QL] [QL00007] ON [QL00007].[GID_IN_QT] = [QT].[GID_ID]  
 LEFT JOIN [PF] [PF00008] ON [PF00008].[GID_ID] = [QL00007].[GID_RELATED_PF]  
 LEFT JOIN [PC] [PC00009] ON [PC00009].[GID_ID] = [PF00008].[GID_RELATED_PC]  
 LEFT JOIN [VE] [VE00010] ON [VE00010].[GID_ID] = [QL00007].[GID_FOR_VE]  
 LEFT JOIN [BC] [BC00011] ON [BC00011].[GID_ID] = [QT].[GID_RELATED_BC]  
 LEFT JOIN [US_RELATED_BC] ON [BC00011].[GID_ID] = [US_RELATED_BC].[GID_BC]  
 LEFT JOIN [US] [US00012] ON [US00012].[GID_ID] = [US_RELATED_BC].[GID_US]  
 LEFT JOIN [US] [US00013] ON [US00013].[GID_ID] = [QT].[GID_CreatedBy_US]  
 WHERE ((([QT].[DTT_CREATIONTIME] >= @StartDateOfLastTwoWeeks AND --'2025-04-27 05:00:00.000' AND   
 ([QT].[DTT_CREATIONTIME] <= DATEADD(SS,-1,@EndDateOfLastTwoWeeks) OR --'2025-05-11 04:59:59.000' OR   
 ([QT].[DTT_CREATIONTIME] IS NULL))) AND   
 ([QT].[CHK_BUDGET] = 0 OR   
 ([QT].[CHK_BUDGET] IS NULL)) AND   
 ([QT].[MLS_STATUS] = '0' OR   
 ([QT].[MLS_STATUS] IS NULL)) AND   
 ([US00001].[GID_ID] = @UserId OR   
 [US00002].[GID_ID] = @UserId OR   
 [US00003].[GID_ID] = @UserId OR   
 [US00006].[GID_ID] = @UserId OR   
 [US00005].[GID_ID] = @UserId OR   
 [US00004].[GID_ID] = @UserId) AND   
 ([QT].[CUR_LINETOTALOPEN] >= '20000' OR   
 ([QT].[CUR_LINETOTALOPEN] >= '5000' AND   
 ([PC00009].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
 [QT].[MLS_TYPE] = '6' OR   
 [PF00008].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
 [PF00008].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR   
 [VE00010].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
 [VE00010].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR   
 [VE00010].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))) AND   
 ([US00012].[GID_ID] = @LoginGID)) AND   
 ([QT].[SI__ShareState] = 2 OR   
 (([QT].[SI__ShareState] < 2 OR   
 ([QT].[SI__ShareState] IS NULL)) AND   
 [US00013].[GID_ID] = @LoginGID))  
 )                
 SELECT @NewStrategicQuotesQty = ISNULL(COUNT([GID_ID]),0),                                         
 @NewStrategicQuotesTotal = ISNULL(SUM([CUR_LINETOTALOPEN]),0),                 
 @NewStrategicQuotesMargin = ISNULL(AVG(SI__GROSSMARGIN),0)                
 FROM CTE                
--last 2 weeks                
  END       
      
--OverdueStrategicQuotes        
IF @IsAdmin = 1      
 BEGIN       
 ;WITH CTE AS(                
 SELECT Distinct [QT].[GID_ID],                 
 [QT].[CUR_LINETOTALOPEN],                 
 [QT].SI__GROSSMARGIN      
 FROM [QT]      
 LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]     
 LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [QT].[GID_PEER_US]      
 LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]    
 LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]     
 LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00001].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]      
 LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]      
 LEFT JOIN [QL] [QL00009] ON [QL00009].[GID_IN_QT] = [QT].[GID_ID]      
 LEFT JOIN [PF] [PF00010] ON [PF00010].[GID_ID] = [QL00009].[GID_RELATED_PF]     
 LEFT JOIN [PC] [PC00011] ON [PC00011].[GID_ID] = [PF00010].[GID_RELATED_PC]     
 LEFT JOIN [VE] [VE00012] ON [VE00012].[GID_ID] = [QL00009].[GID_FOR_VE]     
 WHERE ([QT].[CHK_BUDGET] = 0 OR       
 ([QT].[CHK_BUDGET] IS NULL)) AND       
 ([QT].[MLS_STATUS] = '0' OR       
 ([QT].[MLS_STATUS] IS NULL)) AND       
 ([QT].[DTT_CREATIONTIME] < @FiveDaysAgoDate OR   --'2025-04-18 06:00:00.000' OR     '2025-05-06 11:00:00.000'  
 ([QT].[DTT_CREATIONTIME] IS NULL)) AND       
 ([US00001].[GID_ID] = @UserId OR       
 [US00005].[GID_ID] = @UserId OR       
 [US00008].[GID_ID] = @UserId OR       
 [US00006].[GID_ID] = @UserId OR       
 [US00007].[GID_ID] = @UserId OR       
 [US00004].[GID_ID] = @UserId OR       
 [US00003].[GID_ID] = @UserId OR       
 [US00002].[GID_ID] = @UserId) AND       
 ((([QT].[DTT_EXPCLOSEDATE] IS NULL) OR       
 [QT].[DTT_EXPCLOSEDATE] = '1753-01-02 23:59:59.000') OR       
 ([QT].[DTT_EXPCLOSEDATE] <=   @TodayDate OR  --'2025-04-23 06:00:00.000' OR     '2025-05-11 11:00:00.000'  
 ([QT].[DTT_EXPCLOSEDATE] IS NULL)) OR       
 ([QT].[DTT_NEXTACTIONDATE] <=   @TodayDate OR  --'2025-04-23 06:00:00.000' OR    '2025-05-11 11:00:00.000'   
 ([QT].[DTT_NEXTACTIONDATE] IS NULL))) AND       
 ([QT].[CUR_LINETOTALOPEN] >= '20000' OR       
 ([QT].[CUR_LINETOTALOPEN] >= '5000' AND       
 ([PC00011].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR       
 [QT].[MLS_TYPE] = '6' OR       
 [PF00010].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR       
 [VE00012].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR       
 [VE00012].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR       
 [VE00012].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))      
 )  
 SELECT @OverdueStrategicQuotesQty = ISNULL(COUNT([GID_ID]),0),                                         
 @OverdueStrategicQuotesTotal = ISNULL(SUM([CUR_LINETOTALOPEN]),0),                  
 @OverdueStrategicQuotesMargin = ISNULL(AVG([SI__GROSSMARGIN]),0)                  
 FROM CTE        
 END      
ELSE      
 BEGIN       
 ;WITH CTE AS(                
 SELECT Distinct [QT].[GID_ID],                 
 [QT].[CUR_LINETOTALOPEN],                 
 [QT].SI__GROSSMARGIN                
FROM [QT]    
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]    
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [QT].[GID_PEER_US]    
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]    
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]     
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]    
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00001].[GID_SUPERVISOR_US]     
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]    
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]    
LEFT JOIN [QL] [QL00009] ON [QL00009].[GID_IN_QT] = [QT].[GID_ID]    
LEFT JOIN [PF] [PF00010] ON [PF00010].[GID_ID] = [QL00009].[GID_RELATED_PF]     
LEFT JOIN [PC] [PC00011] ON [PC00011].[GID_ID] = [PF00010].[GID_RELATED_PC]     
LEFT JOIN [VE] [VE00012] ON [VE00012].[GID_ID] = [QL00009].[GID_FOR_VE]     
LEFT JOIN [BC] [BC00013] ON [BC00013].[GID_ID] = [QT].[GID_RELATED_BC]    
LEFT JOIN [US_RELATED_BC] ON [BC00013].[GID_ID] = [US_RELATED_BC].[GID_BC]    
LEFT JOIN [US] [US00014] ON [US00014].[GID_ID] = [US_RELATED_BC].[GID_US]     
LEFT JOIN [US] [US00015] ON [US00015].[GID_ID] = [QT].[GID_CreatedBy_US]   
WHERE ((([QT].[CHK_BUDGET] = 0 OR       
([QT].[CHK_BUDGET] IS NULL)) AND       
([QT].[MLS_STATUS] = '0' OR       
([QT].[MLS_STATUS] IS NULL)) AND       
([QT].[DTT_CREATIONTIME] <  @FiveDaysAgoDate OR --'2025-04-18 00:00:00.000' OR       
([QT].[DTT_CREATIONTIME] IS NULL)) AND       
([US00001].[GID_ID] = @UserId OR       
[US00005].[GID_ID] = @UserId OR       
[US00008].[GID_ID] = @UserId OR       
[US00006].[GID_ID] = @UserId OR       
[US00007].[GID_ID] = @UserId OR       
[US00004].[GID_ID] = @UserId OR       
[US00003].[GID_ID] = @UserId OR       
[US00002].[GID_ID] = @UserId) AND       
((([QT].[DTT_EXPCLOSEDATE] IS NULL) OR       
[QT].[DTT_EXPCLOSEDATE] = '1753-01-02 23:59:59.000') OR       
([QT].[DTT_EXPCLOSEDATE] <= @TodayDate OR  --'2025-04-23 00:00:00.000' OR       
([QT].[DTT_EXPCLOSEDATE] IS NULL)) OR       
([QT].[DTT_NEXTACTIONDATE] <= @TodayDate OR  --'2025-04-23 00:00:00.000' OR       
([QT].[DTT_NEXTACTIONDATE] IS NULL))) AND       
([QT].[CUR_LINETOTALOPEN] >= '20000' OR       
([QT].[CUR_LINETOTALOPEN] >= '5000' AND       
([PC00011].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR       
[QT].[MLS_TYPE] = '6' OR       
[PF00010].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR       
[VE00012].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR       
[VE00012].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR       
[VE00012].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))) AND       
([US00014].[GID_ID] = @LoginGID)) AND       
([QT].[SI__ShareState] = 2 OR       
(([QT].[SI__ShareState] < 2 OR       
([QT].[SI__ShareState] IS NULL)) AND       
[US00015].[GID_ID] = @LoginGID))      
 )      
 SELECT @OverdueStrategicQuotesQty = ISNULL(COUNT([GID_ID]),0),                                         
 @OverdueStrategicQuotesTotal = ISNULL(SUM([CUR_LINETOTALOPEN]),0),                  
 @OverdueStrategicQuotesMargin = ISNULL(AVG([SI__GROSSMARGIN]),0)                  
 FROM CTE             
 END      
  
 --@StrategicQuotesWon  
IF @IsAdmin = 1      
 BEGIN       
  ; WITH CTE AS(                 
 SELECT Distinct [QT].[GID_ID],              
 [QT].CUR_LineTotalWon,                
 [QT].SI__GROSSMARGIN                 
 FROM [QT]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [QT].[GID_PEER_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
LEFT JOIN [QL] [QL00009] ON [QL00009].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00010] ON [PF00010].[GID_ID] = [QL00009].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00011] ON [PC00011].[GID_ID] = [PF00010].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00012] ON [VE00012].[GID_ID] = [QL00009].[GID_FOR_VE]  
WHERE   
--([QT].[DTT_DATECLOSED] >= @NintyOneDaysAgoDate AND --'2025-02-13 06:00:00.000' AND   
--([QT].[DTT_DATECLOSED] <=  @TomorrowDate OR -- '2025-05-13 04:59:59.000' OR  
([QT].[DTT_DATECLOSED] >= dateadd(hh,1,@ThreeMonthsAgoDate) AND --'2025-02-13 06:00:00.000' AND   
([QT].[DTT_DATECLOSED] <=  DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR -- '2025-05-13 04:59:59.000' OR  
([QT].[DTT_DATECLOSED] IS NULL))) AND    
[QT].[MLS_STATUS] = '2' AND   
([US00001].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId OR   
[US00005].[GID_ID] = @UserId) AND   
([QT].[CUR_TOTALAMOUNT] >= '20000' OR   
([QT].[CUR_TOTALAMOUNT] >= '5000' AND   
([PC00011].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR   
[PF00010].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
[PF00010].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR   
[VE00012].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
[VE00012].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR   
[VE00012].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))  
)  
 SELECT @StrategicQuotesWonQty = ISNULL(COUNT([GID_ID]),0),                
 @StrategicQuotesWonTotal = ISNULL(sum(CUR_LineTotalWon),0)                
 FROM CTE          
 END      
ELSE      
 BEGIN       
  ; WITH CTE AS(                 
 SELECT Distinct [QT].[GID_ID],              
 [QT].CUR_LineTotalWon,                
 [QT].SI__GROSSMARGIN                 
 FROM [QT]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [QT].[GID_PEER_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
LEFT JOIN [QL] [QL00009] ON [QL00009].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00010] ON [PF00010].[GID_ID] = [QL00009].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00011] ON [PC00011].[GID_ID] = [PF00010].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00012] ON [VE00012].[GID_ID] = [QL00009].[GID_FOR_VE]  
LEFT JOIN [BC] [BC00013] ON [BC00013].[GID_ID] = [QT].[GID_RELATED_BC]  
LEFT JOIN [US_RELATED_BC] ON [BC00013].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00014] ON [US00014].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00015] ON [US00015].[GID_ID] = [QT].[GID_CreatedBy_US]  
WHERE ((([QT].[DTT_DATECLOSED] >= dateadd(hh,1,@ThreeMonthsAgoDate) AND --'2025-02-13 05:00:00.000' AND   
([QT].[DTT_DATECLOSED] <= DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR -- '2025-05-13 03:59:59.000' OR   
([QT].[DTT_DATECLOSED] IS NULL))) AND   
[QT].[MLS_STATUS] = '2' AND   
([US00001].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId OR   
[US00005].[GID_ID] = @UserId) AND   
([QT].[CUR_TOTALAMOUNT] >= '20000' OR   
([QT].[CUR_TOTALAMOUNT] >= '5000' AND   
([PC00011].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR   
[PF00010].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
[PF00010].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR   
[VE00012].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
[VE00012].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR   
[VE00012].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))) AND   
([US00014].[GID_ID] = @LoginGID)) AND   
([QT].[SI__ShareState] = 2 OR   
(([QT].[SI__ShareState] < 2 OR   
([QT].[SI__ShareState] IS NULL)) AND   
[US00015].[GID_ID] = @LoginGID))  
 )                
SELECT @StrategicQuotesWonQty = ISNULL(COUNT([GID_ID]),0),                
 @StrategicQuotesWonTotal = ISNULL(sum(CUR_LineTotalWon),0)                
 FROM CTE                
--last 90 days                
 END      
      
--Strategic Quotes - Lost      
IF @IsAdmin = 1      
 BEGIN       
 ; WITH CTE AS(                 
 SELECT DISTINCT [QT].[GID_ID], [QT].CUR_LineTotalLost,[QT].SI__GROSSMARGIN              
FROM [QT]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [QT].[GID_PEER_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
LEFT JOIN [QL] [QL00009] ON [QL00009].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00010] ON [PF00010].[GID_ID] = [QL00009].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00011] ON [PC00011].[GID_ID] = [PF00010].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00012] ON [VE00012].[GID_ID] = [QL00009].[GID_FOR_VE]  
WHERE   
--([QT].[DTT_DATECLOSED] >= @NintyOneDaysAgoDate AND --'2025-02-13 06:00:00.000' AND   
--([QT].[DTT_DATECLOSED] <=  @TomorrowDate OR -- '2025-05-13 04:59:59.000' OR  
([QT].[DTT_DATECLOSED] >= dateadd(hh,1,@ThreeMonthsAgoDate) AND --'2025-02-13 06:00:00.000' AND   
([QT].[DTT_DATECLOSED] <=  DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR -- '2025-05-13 04:59:59.000' OR  
([QT].[DTT_DATECLOSED] IS NULL))) AND   
[QT].[MLS_STATUS] = '3' AND   
([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId OR   
[US00005].[GID_ID] = @UserId) AND   
([QT].[CUR_TOTALAMOUNT] >= '20000' OR   
([QT].[CUR_TOTALAMOUNT] >= '5000' AND   
([PC00011].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR   
[PF00010].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
[PF00010].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR   
[VE00012].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
[VE00012].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR   
[VE00012].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))  
 )  
 SELECT @StrategicQuotesLostQty = ISNULL(COUNT([GID_ID]),0),                
 @StrategicQuotesLostTotal = ISNULL(SUM(CUR_LineTotalLost),0)                 
 FROM CTE        
 END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                 
 SELECT DISTINCT [QT].[GID_ID], [QT].CUR_LineTotalLost,[QT].SI__GROSSMARGIN              
 FROM [QT]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [QT].[GID_PEER_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
LEFT JOIN [QL] [QL00009] ON [QL00009].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00010] ON [PF00010].[GID_ID] = [QL00009].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00011] ON [PC00011].[GID_ID] = [PF00010].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00012] ON [VE00012].[GID_ID] = [QL00009].[GID_FOR_VE]  
LEFT JOIN [BC] [BC00013] ON [BC00013].[GID_ID] = [QT].[GID_RELATED_BC]  
LEFT JOIN [US_RELATED_BC] ON [BC00013].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00014] ON [US00014].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00015] ON [US00015].[GID_ID] = [QT].[GID_CreatedBy_US]  
WHERE ((([QT].[DTT_DATECLOSED] >= dateadd(hh,1,@ThreeMonthsAgoDate) AND --'2025'2025-02-13 05:00:00.000' AND   
([QT].[DTT_DATECLOSED] <=   DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR --'2025-05-13 03:59:59.000' OR   
([QT].[DTT_DATECLOSED] IS NULL))) AND   
[QT].[MLS_STATUS] = '3' AND   
([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId OR   
[US00005].[GID_ID] = @UserId) AND   
([QT].[CUR_TOTALAMOUNT] >= '20000' OR   
([QT].[CUR_TOTALAMOUNT] >= '5000' AND   
([PC00011].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR   
[PF00010].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
[PF00010].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR   
[VE00012].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
[VE00012].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR   
[VE00012].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))) AND   
([US00014].[GID_ID] = @LoginGID)) AND   
([QT].[SI__ShareState] = 2 OR   
(([QT].[SI__ShareState] < 2 OR   
([QT].[SI__ShareState] IS NULL)) AND   
[US00015].[GID_ID] = @LoginGID))  
 )      
SELECT @StrategicQuotesLostQty = ISNULL(COUNT([GID_ID]),0),                
 @StrategicQuotesLostTotal = ISNULL(SUM(CUR_LineTotalLost),0)                 
 FROM CTE                
 END             
      
--Strategic Quotes - Cancelled       
IF @IsAdmin = 1      
 BEGIN       
; WITH CTE AS(                 
 SELECT Distinct [QT].[GID_ID],      
 [QT].CUR_LineTotalCancelled,                
 [QT].SI__GROSSMARGIN                  
FROM [QT]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [QT].[GID_PEER_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
LEFT JOIN [QL] [QL00009] ON [QL00009].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00010] ON [PF00010].[GID_ID] = [QL00009].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00011] ON [PC00011].[GID_ID] = [PF00010].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00012] ON [VE00012].[GID_ID] = [QL00009].[GID_FOR_VE]  
WHERE   
--([QT].[DTT_DATECLOSED] >= @NintyOneDaysAgoDate AND --'2025-02-13 06:00:00.000' AND   
--([QT].[DTT_DATECLOSED] <=  @TomorrowDate OR -- '2025-05-13 04:59:59.000' OR  
([QT].[DTT_DATECLOSED] >= dateadd(hh,1,@ThreeMonthsAgoDate) AND --'2025-02-13 06:00:00.000' AND   
([QT].[DTT_DATECLOSED] <=  DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR -- '2025-05-13 04:59:59.000' OR  
([QT].[DTT_DATECLOSED] IS NULL))) AND   
[QT].[MLS_STATUS] = '4' AND   
([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId OR   
[US00005].[GID_ID] = @UserId) AND   
([QT].[CUR_TOTALAMOUNT] >= '20000' OR   
([QT].[CUR_TOTALAMOUNT] >= '5000' AND   
([PC00011].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR   
[PF00010].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
[PF00010].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR   
[VE00012].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
[VE00012].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR   
[VE00012].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))  
 )                
 SELECT @StrategicQuotesCancelledQty= ISNULL(COUNT([GID_ID]),0),                
 @StrategicQuotesCancelledTotal=ISNULL(SUM(CUR_LineTotalCancelled),0)                
 FROM CTE          
 END      
ELSE      
 BEGIN       
 ; WITH CTE AS(                 
 SELECT Distinct [QT].[GID_ID],      
 [QT].CUR_LineTotalCancelled,                
 [QT].SI__GROSSMARGIN                  
FROM [QT]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [QT].[GID_PEER_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US00007].[GID_SUPERVISOR_US]  
LEFT JOIN [QL] [QL00009] ON [QL00009].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00010] ON [PF00010].[GID_ID] = [QL00009].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00011] ON [PC00011].[GID_ID] = [PF00010].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00012] ON [VE00012].[GID_ID] = [QL00009].[GID_FOR_VE]  
LEFT JOIN [BC] [BC00013] ON [BC00013].[GID_ID] = [QT].[GID_RELATED_BC]  
LEFT JOIN [US_RELATED_BC] ON [BC00013].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00014] ON [US00014].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00015] ON [US00015].[GID_ID] = [QT].[GID_CreatedBy_US]  
WHERE ((([QT].[DTT_DATECLOSED] >=  dateadd(hh,1,@ThreeMonthsAgoDate) AND --'2025-02-13 05:00:00.000' AND   
([QT].[DTT_DATECLOSED] <=    DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR --'2025-05-13 03:59:59.000' OR   
([QT].[DTT_DATECLOSED] IS NULL))) AND   
[QT].[MLS_STATUS] = '4' AND   
([US00001].[GID_ID] = @UserId OR   
[US00002].[GID_ID] = @UserId OR   
[US00004].[GID_ID] = @UserId OR   
[US00003].[GID_ID] = @UserId OR   
[US00007].[GID_ID] = @UserId OR   
[US00008].[GID_ID] = @UserId OR   
[US00006].[GID_ID] = @UserId OR   
[US00005].[GID_ID] = @UserId) AND   
([QT].[CUR_TOTALAMOUNT] >= '20000' OR   
([QT].[CUR_TOTALAMOUNT] >= '5000' AND   
([PC00011].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR   
[PF00010].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
[PF00010].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR   
[VE00012].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
[VE00012].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR   
[VE00012].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))) AND   
([US00014].[GID_ID] = @LoginGID)) AND   
([QT].[SI__ShareState] = 2 OR   
(([QT].[SI__ShareState] < 2 OR   
([QT].[SI__ShareState] IS NULL)) AND   
[US00015].[GID_ID] = @LoginGID))  
 )                
 SELECT @StrategicQuotesCancelledQty= ISNULL(COUNT([GID_ID]),0),                
 @StrategicQuotesCancelledTotal=ISNULL(SUM(CUR_LineTotalCancelled),0)                
 FROM CTE           
 END      
                 
-- Calculate the won rate, ensuring no division by zero                                  
SELECT @StrategicQuotesWonRate =                                   
    CASE                                   
        WHEN @StrategicQuotesWonQty + @StrategicQuotesLostQty = 0 THEN 0  -- Return 0 if the total is zero                                  
        ELSE (cast(@StrategicQuotesWonQty as money)/ (cast(@StrategicQuotesWonQty as money) + cast(@StrategicQuotesLostQty as money))) *100                           
    END,                                  
 @StrategicQuotesLostRate =                                   
    CASE                                   
        WHEN @StrategicQuotesWonQty + @StrategicQuotesLostQty = 0 THEN 0  -- Return 0 if the total is zero                                  
        ELSE (cast(@StrategicQuotesLostQty as money) / (cast(@StrategicQuotesWonQty as money) + cast(@StrategicQuotesLostQty as money))) *100                                  
    END,                    
 @StrategicQuotesCancelledRate =                                   
    CASE                                   
        WHEN @StrategicQuotesWonQty + @StrategicQuotesLostQty + @StrategicQuotesCancelledQty = 0 THEN 0  -- Return 0 if the total is zero                                  
        ELSE (cast(@StrategicQuotesCancelledQty as money) / (cast(@StrategicQuotesWonQty as money) + cast(@StrategicQuotesLostQty as money) + cast(@StrategicQuotesCancelledQty as money)))* 100   
 END                   
    
    
 --; WITH CTE AS(                   
 --SELECT Distinct [QT].[GID_ID], [QT].[CUR_TOTALAMOUNT]                  
 --FROM [QT]                  
 --LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]                  
 --LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]                  
 --LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]                  
 --LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [QT].[GID_PEER_US]                  
 --LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]                  
 --LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]                  
 --WHERE (([QT].[MLS_STATUS] = '0' OR                   
 --([QT].[MLS_STATUS] IS NULL)) OR                   
 --[QT].[MLS_STATUS] = '1') AND                   
 --([US00001].[GID_ID] = @USERID OR                   
 --[US00002].[GID_ID] = @USERID OR                   
 --[US00003].[GID_ID] = @USERID OR                   
 --[US00004].[GID_ID] = @USERID OR                   
 --[US00005].[GID_ID] = @USERID OR                   
 --[US00006].[GID_ID] = @USERID)                  
 --)                  
 --SELECT @MyQuotePipeline = ISNULL(sum([CUR_TOTALAMOUNT]),0) FROM CTE                  
  
Select                  
@MyPrimaryAccounts As 'MyPrimaryAccounts',                    
@MyNewAccounts As 'MyNewAccounts',                    
@MyTeamSellAccounts As 'MyTeamSellAccounts',                    
@MyOverserveAccounts As 'MyOverserveAccounts',                    
@MyUnderservedAccounts As 'MyUnderservedAccounts',                    
@MyOverduePrimaryAccounts As 'MyOverduePrimaryAccounts',                  
@AccountsWithoutContacts 'AccountsWithoutContacts',                    
@AccountsWithoutActivities 'AccountsWithoutActivities',                  
  
@MyContacts As 'MyContacts',                    
@MyNewContacts As 'MyNewContacts',                    
@MyKeyContacts As 'MyKeyContacts',                    
@MyOverdueContacts As 'MyOverdueContacts',                  
  
@MyTasksDue As 'MyTasksDue',                  
@MyTasksOverdue As 'MyTasks',                    
  
@MyActivities As 'MyActivities',                  
@MyAcTeamSell As 'MyAcTeamSell',                  
  
@MySalesThisMonth As 'SalesThisMonthQty',                    
@MySalesLastonth As 'SalesLastMonthQty',                  
  
@OthersAcThisMonth As 'OthersAcThisMonth',             
@OthersAcLastMonth As 'OthersAcLastMonth',         
  
@MyLeads As'Leads',                    
@MyLeadsExptedVal As 'LeadsExpVal',                    
@MyLeadsWeightedVal As 'LeadWeightedVal',                  
@MyTeamSellLeads As 'MyTeamSellLeads',                    
@MyTeamSellLeadsExptedVal As 'MyTeamSellLeadsExptedValue',                    
@MyTeamSellLeadsWeightedVal As 'MyTeamSellLeadsWeightedVal',                    
@MyNewLeads As 'MyNewLeads',                    
@MyNewLeadsExptedVal As 'MyNewLeadsExptedVal',                    
@MyNewLeadsWeightedVal As 'MyNewLeadsWeightedVal',                    
@MyOverdueLeads As 'MyOverdueLeads',                    
@MyOverdueLeadsExptedVal As 'MyOverdueLeadsExptedVal',                    
@MyOverdueLeadsWeightedVal As 'MyOverdueLeadsWeightedVal',                  
  
@MyOpenOppsqty As 'MyOpenOppsqty',                    
@MyOpenOppsExptedVal As 'MyOpenOppsExptedVal',                    
@MyOpenOppsWeightedVal As 'MyOpenOppsWeightedVal',                    
  
@MyTeamSellOpps_count As 'MyTeamSellOpps_count',                    
@MyTeamSellOpps_expval As 'MyTeamSellOpps_expval',                  
@MyTeamSellOpps_wghtval As 'MyTeamSellOpps_wghtval',                  
  
@MyDueNext30DaysOpps_count As 'MyDueNext30DaysOpps_count',                  
@MyDueNext30DaysOpps_expval As 'MyDueNext30DaysOpps_expval',                  
@MyDueNext30DaysOpps_wghtval As 'MyDueNext30DaysOpps_wghtval',                  
  
@MyOverdueOpps_count As 'MyOverdueOpps_count',                    
@MyOverdueOpps_expval As 'MyOverdueOpps_expval',                  
@MyOverdueOpps_wghtval As 'MyOverdueOpps_wghtval',                  
  
@MyOpps_won_Qty As 'MyOpps_won_Qty',                  
@MyOpps_won_Tot As 'MyOpps_won_Tot',                  
@MyOpps_won_Rate As 'MyOpps_won_Rate',                  
  
@MyOpps_Lost_Qty As 'MyOpps_Lost_Qty',                  
@MyOpps_Lost_Tot As 'MyOpps_Lost_Tot',                    
@MyOpps_Lost_Rate As 'MyOpps_Lost_Rate',                  
  
@MyOpps_Cancelled_Qty As 'MyOpps_Cancelled_Qty',                  
@MyOpps_Cancelled_Tot As 'MyOpps_Cancelled_Tot',                    
@MyOpps_Cancelled_Rate As 'MyOpps_Cancelled_Rate',                  
  
@OpenRFQsQty As 'OpenRFQsQty',                    
@OpenRFQstotal As 'OpenRFQstotal',                    
@OpenRFQsmargin As 'OpenRFQsmargin',                    
  
@OpenStrategicQuotesQty As 'OpenStrategicQuotesQty',                   
@OpenStrategicQuotesTotal As 'OpenStrategicQuotesTotal',                    
@OpenStrategicQuotesMargin As 'OpenStrategicQuotesMargin',                   
  
@NewStrategicQuotesQty As 'NewStrategicQuotesQty',                   
@NewStrategicQuotesTotal As 'NewStrategicQuotesTotal',                   
@NewStrategicQuotesMargin As 'NewStrategicQuotesMargin',                  
  
@OverdueStrategicQuotesQty As 'OverdueStrategicQuotesQty',                    
@OverdueStrategicQuotesTotal As 'OverdueStrategicQuotesTotal',                    
@OverdueStrategicQuotesMargin As 'OverdueStrategicQuotesMargin',                    
  
@StrategicQuotesWonQty As 'StrategicQuotesWonQty',                   
@StrategicQuotesWonTotal As 'StrategicQuotesWonTotal',                   
@StrategicQuotesWonRate As 'StrategicQuotesWonRate',                  
  
@StrategicQuotesLostQty  As 'StrategicQuotesLostQty',                  
@StrategicQuotesLostTotal As 'StrategicQuotesLostTotal',                   
@StrategicQuotesLostRate As 'StrategicQuotesLostRate',                  
  
@StrategicQuotesCancelledQty As 'StrategicQuotesCancelledQty',                   
@StrategicQuotesCancelledTotal As 'StrategicQuotesCancelledTotal',                  
@StrategicQuotesCancelledRate As 'StrategicQuotesCancelledRate',                   
  
@AllOpenQuotesQty 'AllOpenQuotesQty',                  
@AllOpenQuotesTotal 'AllOpenQuotesTotal' ,                  
@AllOpenQuotesMargin 'AllOpenQuotesMargin',                    
  
@MyQuotePipeline As 'MyQuotePipeline',                  
@MyTaskstile As 'MyTaskstile',                  
@DTT_LASTREFRESHEDDATE As 'LastRefreshedDate',                  
--                  
@MyStrategicQuotesOpe As 'MyStrategicQuotesOpe',                    
@MyStrategicQuotesNew As 'MyStrategicQuotesNew',                    
@MyStrategicQuotesOverdue As 'MyStrategicQuotesOverdue',                  
@MyRecentOrders  As 'MyRecentOrders',                    
@count_of_my_recent_orders_this_month As 'Count_of_my_recent_orders_this_month',                    
@total_amount_of_my_recent_order_this_month As 'Total_amount_of_my_recent_order_this_month',                    
@gross_profit_of_my_recent_order_this_month As 'Gross_profit_of_my_recent_order_this_month',                    
@count_of_my_recent_orders_last_month As 'Count_of_my_recent_orders_last_month',                    
@total_amount_of_my_recent_order_last_month As 'Total_amount_of_my_recent_order_last_month',                    
@gross_profit_of_my_recent_order_last_month As 'Gross_profit_of_my_recent_order_last_month',                    
@count_of_my_open_orders As 'Count_of_my_open_orders',                    
@total_amount_of_my_open_order As 'Total_amount_of_my_open_order',                    
@gross_profit_of_my_open_order As 'Gross_profit_of_my_open_order',                    
@MyQuotes As 'MyQuotes',                    
@sum_of_my_open_expected_value As 'Sum_of_my_open_expected_value',                    
@count_of_my_open_expected_value_weighted As 'Count_of_my_open_expected_value_weighted',                    
@SalesThisMonthQty  As 'MySalesThisMonth',                    
@SalesThisMonthTotal As 'SalesThisMonthTotal',                    
@SalesThisMonthMargin As 'SalesThisMonthMargin',                    
@SalesLastMonthQty As 'MySalesLastMonth',                    
@SalesLastMonthTotal As 'SalesLastMonthTotal',                    
@SalesLastMonthMargin As 'SalesLastMonthMargin'                    
  
--FOR MINI VIEWS                                            
--PrimaryCoDetails                                                               
SELECT DISTINCT TOP 3 [CO].[GID_ID],[CO].[TXT_CompanyName],[CO].[Txt_AddrMailing],[CO].[Txt_CityMailing],[CO].[Txt_StateMailing],[CO].[txt_ZipMailing]  
From CO                                               
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]                  
LEFT JOIN [CO_INVOLVES_US] ON [CO].[GID_ID] = [CO_INVOLVES_US].[GID_CO]                  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [CO_INVOLVES_US].[GID_US]                  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00001].[GID_SUPERVISOR_US]                  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US00003].[GID_SUPERVISOR_US]                  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00002].[GID_SUPERVISOR_US]                  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]                  
WHERE [CO].[CHK_REVIEW] = 1 AND                   
([CO].[DTT_NEXTREVIEWDATE] <= @TodayDate OR                   
([CO].[DTT_NEXTREVIEWDATE] IS NULL)) AND                   
[CO].[CHK_PRIMARY] = 1 AND                   
([US00001].[GID_ID] = @UserId OR                   
[US00002].[GID_ID] = @UserId OR                   
[US00003].[GID_ID] = @UserId OR                   
[US00004].[GID_ID] = @UserId OR                   
[US00006].[GID_ID] = @UserId OR                   
[US00005].[GID_ID] = @UserId)                  
ORDER BY [CO].[TXT_COMPANYNAME] ASC                  
                    
-- Second result set: Overdue Contacts                                                    
SELECT DISTINCT TOP 3 [CN].[GID_ID],[CN].[Txt_NameFirst],[CN].[Txt_NameLast],[CN].[TXT_COMPANYNAMETEXT],[CN].[Txt_AddrMailing],[CN].[Txt_MailingCity],[CN].[Txt_MailingState],  
[CN].[txt_MailingZip]                              
FROM [CN]                                                        
LEFT JOIN [CN_RELATED_US] ON [CN].[GID_ID] = [CN_RELATED_US].[GID_CN]                  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CN_RELATED_US].[GID_US]                  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]                  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]                  
LEFT JOIN [CN_RELATED_CO] ON [CN].[GID_ID] = [CN_RELATED_CO].[GID_CN]                  
LEFT JOIN [CO] [CO00004] ON [CO00004].[GID_ID] = [CN_RELATED_CO].[GID_CO]                  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [CO00004].[GID_TEAMLEADER_US]                  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]                  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US00006].[GID_SUPERVISOR_US]                  
WHERE [CN].[CHK_ACTIVEFIELD] = 1 AND                     
[CN].[CHK_REVIEW] = 1 AND                     
([CN].[DTT_NEXTCONTACTDATE] <= @TodayDate OR                   
([CN].[DTT_NEXTCONTACTDATE] IS NULL)) AND                     
([US00001].[GID_ID] = @UserId OR                     
[US00002].[GID_ID] = @UserId OR                     
[US00003].[GID_ID] = @UserId OR                     
[US00007].[GID_ID] = @UserId OR                     
[US00006].[GID_ID] = @UserId OR                     
[US00005].[GID_ID] = @UserId)                  
ORDER BY [CN].[Txt_NameFirst] ASC                  
                  
-- Third result set: Overdue Tasks                                                  
SELECT DISTINCT TOP 3 [TD].GID_ID,[TD].TXT_DESCRIPTION,[US00003].TXT_FULLNAME,[TD].DTT_STARTDATE,[TD].DTT_DUETIME,                                
CASE                                               
   WHEN [TD].[MLS_TYPE]= 21 THEN 'Contacts Management'                                                      
   WHEN [TD].[MLS_TYPE]= 22 THEN 'Account Development'                                                      
   WHEN [TD].[MLS_TYPE]= 23 THEN 'Opportunity Advancement'                                                      
   WHEN [TD].[MLS_TYPE]= 24 THEN 'Quote Advancement'                                                      
   WHEN [TD].[MLS_TYPE]= 25 THEN 'Personal'                                                      
   WHEN [TD].[MLS_TYPE]= 99 THEN 'Others'                                                      
   ELSE ''                                                 
END AS MLS_TYPE                                                      
From [TD]                                                    
LEFT JOIN [TD_ASSIGNEDTO_US] ON [TD].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_TD]                  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_US]                  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]                  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]                  
WHERE ([US00001].[GID_ID] = @UserId OR                     
[US00002].[GID_ID] = @UserId OR                     
[US00003].[GID_ID] = @UserId) AND                   
([TD].[MLS_STATUS] <> '4' OR                     
([TD].[MLS_STATUS] IS NULL)) AND                     
([TD].[DTT_DUETIME] <= @TodayDate OR                     
([TD].[DTT_DUETIME] IS NULL))                    
                                          
-- Fourth result set: Overdue Activities                                           
SELECT DISTINCT TOP 3 [AC].[GID_ID],                                          
CASE                                          
WHEN [AC].[MLS_TYPE]=0  THEN '<Make selection>'                                          
WHEN [AC].[MLS_TYPE]=1  THEN 'Call Received'                                          
WHEN [AC].[MLS_TYPE]=11 THEN 'Sales Visit'                                          
WHEN [AC].[MLS_TYPE]=12 THEN 'Task Work'                                          
WHEN [AC].[MLS_TYPE]=13 THEN 'Meeting'                      
WHEN [AC].[MLS_TYPE]=14 THEN 'Demo'                                   
WHEN [AC].[MLS_TYPE]=15 THEN 'Training'                                          
WHEN [AC].[MLS_TYPE]=16 THEN 'Service Call'                                          
WHEN [AC].[MLS_TYPE]=17 THEN 'Vendor Visit'                                          
WHEN [AC].[MLS_TYPE]=2  THEN 'Call Placed'                                          
WHEN [AC].[MLS_TYPE]=31 THEN 'Journal'                                          
WHEN [AC].[MLS_TYPE]=5  THEN 'E-mail Received'                                          
WHEN [AC].[MLS_TYPE]=6  THEN 'E-mail Sent'                                          
WHEN [AC].[MLS_TYPE]=82 THEN 'WP Access'                                          
WHEN [AC].[MLS_TYPE]=99 THEN 'Other'                                          
Else ''                                                
  End AS [MLS_TYPE],                            
[AC].[DTT_STARTTIME],SUBSTRING(CAST([AC].[MMO_History] AS NVARCHAR(4000)), 1, 45) AS MMO_History,[US00001].[TXT_FULLNAME],[CO00002].[TXT_COMPANYNAME]                                          
FROM [AC]                                                
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]                                              
LEFT JOIN [AC_RELATED_CO] ON [AC].[GID_ID] = [AC_RELATED_CO].[GID_AC]                                              
LEFT JOIN [CO] [CO00002] ON [CO00002].[GID_ID] = [AC_RELATED_CO].[GID_CO]                                              
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [CO00002].[GID_TEAMLEADER_US]                                              
LEFT JOIN [CO_INVOLVES_US] ON [CO00002].[GID_ID] = [CO_INVOLVES_US].[GID_CO]                                              
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [CO_INVOLVES_US].[GID_US]        
WHERE (([AC].[DTT_STARTTIME] >= @ThisMonthStartDate OR--@StartOfCurrentMonth OR                             
([AC].[DTT_STARTTIME] IS NULL)) AND                                              
([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@NextMonthStartDate) OR --@EndOfCurrentMonth OR                                              
([AC].[DTT_STARTTIME] IS NULL))) AND                                              
[AC].[MLS_TYPE] = '11' AND                                              
([US00001].[GID_ID] <> @UserId OR                                             
([US00001].[GID_ID] IS NULL)) AND                                              
([US00003].[GID_ID] = @UserId OR                                              
[US00004].[GID_ID] = @UserId)                        
  
SELECT DISTINCT TOP 3 [AC].[GID_ID],                                          
CASE                                          
 WHEN [AC].[MLS_TYPE]=0  THEN '<Make selection>'                                          
 WHEN [AC].[MLS_TYPE]=1  THEN 'Call Received'                                          
 WHEN [AC].[MLS_TYPE]=11 THEN 'Sales Visit'                                          
 WHEN [AC].[MLS_TYPE]=12 THEN 'Task Work'                                          
 WHEN [AC].[MLS_TYPE]=13 THEN 'Meeting'                                          
 WHEN [AC].[MLS_TYPE]=14 THEN 'Demo'                                          
 WHEN [AC].[MLS_TYPE]=15 THEN 'Training'                                          
 WHEN [AC].[MLS_TYPE]=16 THEN 'Service Call'                                          
 WHEN [AC].[MLS_TYPE]=17 THEN 'Vendor Visit'                                          
 WHEN [AC].[MLS_TYPE]=2  THEN 'Call Placed'               
 WHEN [AC].[MLS_TYPE]=31 THEN 'Journal'                                          
 WHEN [AC].[MLS_TYPE]=5  THEN 'E-mail Received'                                          
 WHEN [AC].[MLS_TYPE]=6  THEN 'E-mail Sent'                                          
 WHEN [AC].[MLS_TYPE]=82 THEN 'WP Access'                                          
 WHEN [AC].[MLS_TYPE]=99 THEN 'Other'                                          
Else ''                            
  End AS [MLS_TYPE],                            
[AC].[DTT_STARTTIME],                            
SUBSTRING(CAST([AC].[MMO_History] AS NVARCHAR(4000)), 1, 45) AS MMO_History,                            
[US00002].[TXT_FULLNAME]                               
FROM [AC]                    
LEFT JOIN [AC_INVOLVES_US] ON [AC].[GID_ID] = [AC_INVOLVES_US].[GID_AC]                                            
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC_INVOLVES_US].[GID_US]                                           
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [AC].[GID_CREDITEDTO_US]                                           
WHERE (([AC].[DTT_STARTTIME] >= @LastMonthStartDate OR                                
([AC].[DTT_STARTTIME] IS NULL)) AND                                
([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@ThisMonthStartDate)  OR                                
([AC].[DTT_STARTTIME] IS NULL))) AND                                
[US00001].[GID_ID] = @UserId AND                                
[AC].[MLS_TYPE] = '11'                                 
                  
--Overdue Opportunities M.V                                              
SELECT DISTINCT TOP 3 [OP].[GID_ID],[US00001].[TXT_FULLNAME],[CO00004].[TXT_COMPANYNAME],[OP].[TXT_OPPORTUNITYNAME],[OP].[DTT_EXPCLOSEDATE],                                            
Case                                                 
 When  [OP].[MLS_OPPORTUNITYTYPE] = 1 Then 'Buy Resale'                                                
 When  [OP].[MLS_OPPORTUNITYTYPE] = 2 Then 'Rep Order'                                                
 When  [OP].[MLS_OPPORTUNITYTYPE] = 3 Then 'Service/Repair'                                                
 When  [OP].[MLS_OPPORTUNITYTYPE] = 4 Then 'Distributed System'                                                
 When  [OP].[MLS_OPPORTUNITYTYPE] = 5 Then 'Engineered System'                                                
 When  [OP].[MLS_OPPORTUNITYTYPE] = 6 Then 'Filter'                                                
Else ''                                                
  End AS [MLS_OPPORTUNITYTYPE],                                            
Case                                            
 When [OP].[MLS_SALESPROCESSSTAGE]= 0 Then '<Make Selection>'                                            
 When [OP].[MLS_SALESPROCESSSTAGE]= 1 Then 'Lead'                                            
 When [OP].[MLS_SALESPROCESSSTAGE]= 2 Then 'Qualified Opportunity'                                            
 When [OP].[MLS_SALESPROCESSSTAGE]= 3 Then 'Gain Attention'                                            
 When [OP].[MLS_SALESPROCESSSTAGE]= 4 Then 'Earn Interest'                                            
 When [OP].[MLS_SALESPROCESSSTAGE]= 5 Then 'Conduct Discovery'                                            
 When [OP].[MLS_SALESPROCESSSTAGE]= 6 Then 'Present/Propose'                                            
 When [OP].[MLS_SALESPROCESSSTAGE]= 7 Then 'Close Deal'                                            
Else ''                                                
  End AS [MLS_SALESPROCESSSTAGE],                            
[OP].[DTT_CREATIONTIME] ,[OP].[CUR_OPPLINEVALUE]                                                 
FROM [OP]                                                
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]                  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]                  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]                  
LEFT JOIN [CO] [CO00004] ON [OP].[GID_For_CO]  = [CO00004].[GID_ID]                  
WHERE ([US00001].[GID_ID] = @UserId OR                       
[US00002].[GID_ID] = @UserId OR                       
[US00003].[GID_ID] = @UserId ) AND                       
([OP].[MLS_STATUS] = '0' OR                       
([OP].[MLS_STATUS] IS NULL)) AND                       
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR                       
([OP].[MLS_SALESPROCESSSTAGE] IS NULL)) AND                       
(([OP].[DTT_EXPCLOSEDATE] <= @TodayDate OR                      
([OP].[DTT_EXPCLOSEDATE] IS NULL)) OR                       
([OP].[DTT_NEXTACTIONDATE] <= @TodayDate OR                     
([OP].[DTT_NEXTACTIONDATE] IS NULL)))                     
                  
-- Sixth result set: Overdue Quotes                                                        
SELECT DISTINCT TOP 3 [QT].[GID_ID],[QT].[TXT_QUOTENO], [QT].[TXT_DESCRIPTION],[CO00004].[TXT_COMPANYNAME],[US00003].[TXT_FULLNAME],                                            
CASE                                            
When [QT].[MLS_STATUS] = 0 THEN '10 Open'                                                
When [QT].[MLS_STATUS] = 1 THEN '20 On Hold'                                                
When [QT].[MLS_STATUS] = 2 THEN '30 Won'                                                
When [QT].[MLS_STATUS] = 3 THEN '40 Lost'                                                
When [QT].[MLS_STATUS] = 4 THEN '70 Cancelled'                                                
When [QT].[MLS_STATUS] = 5 THEN '50 Delete'                                                
When [QT].[MLS_STATUS] = 6 THEN '60 Revised'                                                
When [QT].[MLS_STATUS] = 7 THEN '05 In Process'                                             
ELse '' End AS [MLS_STATUS],                            
[QT].[CUR_TOTALAMOUNT],                            
[QT].[DTT_EXPCLOSEDATE],                            
[QT].[DTT_VALIDUNTILDATE]                                            
FROM [QT]                                              
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]                  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [US00001].[GID_SUPERVISOR_US]                  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US00002].[GID_SUPERVISOR_US]                  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [QT].[GID_PEER_US]                  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US00004].[GID_SUPERVISOR_US]                  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [US00005].[GID_SUPERVISOR_US]                  
LEFT JOIN [QL] [QL00007] ON [QL00007].[GID_IN_QT] = [QT].[GID_ID]                  
LEFT JOIN [PF] [PF00008] ON [PF00008].[GID_ID] = [QL00007].[GID_RELATED_PF]                  
LEFT JOIN [PC] [PC00009] ON [PC00009].[GID_ID] = [PF00008].[GID_RELATED_PC]                  
LEFT JOIN [VE] [VE00010] ON [VE00010].[GID_ID] = [QL00007].[GID_FOR_VE]                  
LEFT JOIN [CO] [CO00004] ON [QT].[GID_For_CO]  = [CO00004].[GID_ID]                  
WHERE ([QT].[CHK_BUDGET] = 0 OR                     
([QT].[CHK_BUDGET] IS NULL)) AND                     
([QT].[MLS_STATUS] = '0' OR ([QT].[MLS_STATUS] IS NULL)) AND                     
([QT].[DTT_CREATIONTIME] < @FiveDaysAgoDate OR                     
([QT].[DTT_CREATIONTIME] IS NULL)) AND                     
([US00001].[GID_ID] = @UserId OR                     
[US00002].[GID_ID] = @UserId OR                     
[US00003].[GID_ID] = @UserId OR                     
[US00006].[GID_ID] = @UserId OR                     
[US00005].[GID_ID] = @UserId OR                     
[US00004].[GID_ID] = @UserId) AND                     
((([QT].[DTT_EXPCLOSEDATE] IS NULL) OR                     
[QT].[DTT_EXPCLOSEDATE] = '1753-01-02 23:59:59.000') OR                     
([QT].[DTT_EXPCLOSEDATE] <= @TodayDate OR                     
([QT].[DTT_EXPCLOSEDATE] IS NULL)) OR                     
([QT].[DTT_NEXTACTIONDATE] <= @TodayDate OR                     
([QT].[DTT_NEXTACTIONDATE] IS NULL))) AND                     
([QT].[CUR_LINETOTALOPEN] >= '20000' OR                     
([QT].[CUR_LINETOTALOPEN] >= '5000' AND                     
([PC00009].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR                     
[QT].[MLS_TYPE] = '6' OR                     
[PF00008].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR                     
[VE00010].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR                     
[VE00010].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR                     
[VE00010].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))                                               
                                                        
END   
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;

        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();

            Util.SetSessionValue("SkipQLSpecificLogic", "Y");



        }
        public ScriptsCustom()
        {
            Initialize();
        }


        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }


        public bool AC_FormControlOnChange_BTN_NOTICEOFLEAD_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/8/07 Changed time stamp to local time, no label.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            clRowSet doACRS; // new AC of type email sent
            clArray doContacts = new clArray(); // holds contacts
            clArray doCompanies = new clArray(); // holds companies
            string sLetter = "";
            string sObjectPassed = par_doCallingObject.GetType().ToString();
            string sID = "";
            clSend oMySend = new clSend();
            clSend oSend;
            // Dim bNoAddPerm As Boolean = False
            string sSubject = "";
            int i = 0;
            string sContacts = "";
            string sCompanies = "";
            bool bNoCN = false;
            string sToCN = "";
            string sInquiryNo = "";
            string sUSEmail = "";

            // TLD 12/16/2010 Creates AC of type E-mail Sent for Leads and sends

            // TLD 5/16/2012 bNoAddPerm NOT necessary, not used elsewhere.....
            // Check for add permissions
            if (goData.GetAddPermission("AC") == false)
            {
                // bNoAddPerm = True
                doForm.MessageBox("You cannot create a Notice of Lead Email because you don't have permission to create new Activities.");
                return false;
            }

            // TLD 5/16/2012 bNoAddPerm NOT necessary, not used elsewhere.....
            // If bNoAddPerm = True Then
            // doForm.MessageBox("You cannot create a Notice of Lead Email because you don't have permission to create new Activities.")
            // Return False
            // End If

            // Saves AC before sending AC email
            if (doForm.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
            {
                return false;
            }
            if (sObjectPassed == "clForm")
            {
                sID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));
            }

            // Make sure POP set with email client
            if (goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EMAILPROG", "<%NONE%>", true) == "<%NONE%>")
            {
                doForm.MessageBox("You must select your e-mail program in Personal Options before you can send.");
                return true;
            }

            // Get Credited to User's Is Contact record to set to LNK_RELATED_CN
            // Create RS of first one to see if there is an email address
            clArray doLink = new clArray();
            doLink = doForm.doRS.GetLinkVal("LNK_CREDITEDTO_US%%LNK_IS_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
            if (doLink.GetDimension() > 0)
            {
                if (goData.GetFieldValueFromRec(doLink.GetItem(1), "EML_EMAIL") == "")
                {
                    bNoCN = true;
                }
                else
                {
                    sToCN = doLink.GetItem(1);

                    sUSEmail = Convert.ToString(goData.GetFieldValueFromRec((doLink.GetItem(1)), "EML_EMAIL"));
                }
            }
            else
            {
                bNoCN = true;
            }

            // Give user message
            if (bNoCN == true)
            {
                doForm.MessageBox("The Credited to User on this Lead either does NOT have a contact record, or their contact record does NOT contain an email address.");
                return true;
            }

            // Gets Contacts from LNK_RELATED_CN
            doContacts = doForm.doRS.GetLinkVal("LNK_RELATED_CN", ref doContacts, true, 0, -1, "A_a", ref oTable);
            for (i = 1; i <= doContacts.GetDimension(); i++)
            {
                clRowSet doNewRS = new clRowSet("CN", 3, "GID_ID='" + doContacts.GetItem(i) + "'", null/* Conversion error: Set to default value for this argument */, "TXT_NameFirst, TXT_NameLast");
                if (doNewRS.GetFirst() == 0)
                {
                    do
                    {
                        if (sContacts == "")
                        {
                            sContacts = doNewRS.GetFieldVal("TXT_NameFirst") + " " + doNewRS.GetFieldVal("TXT_NameLast");
                        }
                        else
                        {
                            sContacts = sContacts + ", " + doNewRS.GetFieldVal("TXT_NameFirst") + " " + doNewRS.GetFieldVal("TXT_NameLast");
                        }
                        if (doNewRS.GetNext() == 0)
                        {
                            break;
                        }
                    }
                    while (true);
                }
                doNewRS = null/* TODO Change to default(_) if this is not a reference type */;
            }

            // Gets Companies from LNK_RELATED_CO
            doCompanies = doForm.doRS.GetLinkVal("LNK_RELATED_CO", ref doCompanies, true, 0, -1, "A_a", ref oTable);
            for (i = 1; i <= doCompanies.GetDimension(); i++)
            {
                clRowSet doNewRS = new clRowSet("CO", 3, "GID_ID='" + doCompanies.GetItem(i) + "'", null/* Conversion error: Set to default value for this argument */, "TXT_CompanyName");
                if (doNewRS.GetFirst() == 0)
                {
                    do
                    {
                        if (sCompanies == "")
                        {
                            sCompanies = Convert.ToString(doNewRS.GetFieldVal("TXT_CompanyName"));
                        }
                        else

                        {
                            sCompanies = sCompanies + ", " + doNewRS.GetFieldVal("TXT_CompanyName");
                        }
                        if (doNewRS.GetNext() == 0)
                        {
                            break;
                        }
                    }
                    while (true);
                }
                doNewRS = null/* TODO Change to default(_) if this is not a reference type */;
            }

            // Sets subject field
            sInquiryNo = Convert.ToString(doForm.doRS.GetFieldVal("TXT_INQUIRY"));
            sSubject = "NOL: (" + sInquiryNo + ")";

            // Builds Letter
            sLetter = sLetter + "This lead has been assigned to you.  If you are not the correct person to follow up on this lead, please forward to the appropriate person and CC Jennifer Rossnagle, so the information can be changed in Selltis." + Constants.vbCrLf + Constants.vbCrLf;
            sLetter = sLetter + "Inquiry #: " + sInquiryNo + Constants.vbCrLf;
            sLetter = sLetter + "Company: " + sCompanies + Constants.vbCrLf;
            sLetter = sLetter + "Contact: " + sContacts + Constants.vbCrLf;
            sLetter = sLetter + "Journal: " + doForm.doRS.GetFieldVal("MMO_Journal") + Constants.vbCrLf + Constants.vbCrLf;
            // TLD 1/17/2011 They wanted BOTH Next Action Date & Next action
            // sLetter = sLetter & "Next Action Date: " & vbCrLf
            // sLetter = sLetter & "Next Action: " & doForm.doRS.GetFieldVal("MMO_NextAction") & vbCrLf
            sLetter = sLetter + "Next Action Date: " + doForm.doRS.GetFieldVal("DTE_NextActionDate") + Constants.vbCrLf;
            sLetter = sLetter + "Next Action: " + doForm.doRS.GetFieldVal("MMO_NextAction") + Constants.vbCrLf;

            // Creates new AC
            doACRS = new clRowSet("AC", 2, "", "", "CRL_AC:EMAILSENT", -1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 1800);

            // Set values in linked AC
            doACRS.SetFieldVal("LNK_INVOLVES_CO", doCompanies);
            doACRS.SetFieldVal("LNK_INVOLVES_CN", doContacts);
            doACRS.SetFieldVal("LNK_INVOLVES_CN", sToCN);

            doACRS.ClearLinkAll("LNK_RELATED_CN");
            doACRS.SetFieldVal("LNK_RELATED_CN", sToCN);

            doACRS.ClearLinkAll("LNK_RELATED_CO");
            doACRS.SetFieldVal("LNK_RELATED_CO", goData.GetFieldValueFromRec(sToCN, "LNK_Related_CO"));

            // Sets Letter field
            doACRS.SetFieldVal("MMO_LETTER", sLetter);

            // Sets email field
            doACRS.SetFieldVal("EML_EMAIL", sUSEmail);

            // Sets Subject field
            doACRS.SetFieldVal("TXT_SUBJ", sSubject);

            // Saves new AC record
            if (doACRS.Commit() != 1)
            {
                doForm.MessageBox("Cannot create an email Activity.");
                return true;
            }

            // Sends AC to PC Link
            if (oMySend.AddSendJob("Notice of Email Lead: " + doForm.doRS.GetFieldVal("SYS_NAME"), Convert.ToString(doACRS.GetFieldVal("GID_ID")), "cus_Text Email.txt", "CORR", "EMAIL", "AC", false, true) != "")
            {
                oSend = new clSend();

                oSend.MarkRecordAsSent(Convert.ToString(doACRS.GetFieldVal("GID_ID")), "CORR");
                goUI.ExecuteSendPopup = true;
            }
            else
            {
                doForm.MessageBox("Cannot create send job.");
                return true;
            }
            par_doCallingObject = doForm;
            return true;
        }


        public bool AC_FormControlOnChange_MLS_PURPOSE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            scriptManager.RunScript("Activity_ManageControlState", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            par_doCallingObject = doForm;
            return true;
        }


        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sInquiryNo = "";
            string sMode = doForm.GetOpeningMode();

            // TLD 5/17/2013 Changed to Lead tab, in ManageControlState
            // 'TLD 3/10/2009 Defaults to Journal Tab if purpose is Lead
            // If doForm.doRS.GetFieldVal("MLS_PURPOSE", 2) = 8 Then
            // doForm.MoveToTab(3) 'Journal
            // End If

            // TLD 4/22/2009 Call to generate Inquiry # if new Opp
            if (doForm.GetMode() == "CREATION" & sMode == "LEAD")
            {
                // TLD 7/22/2013 Change to call from form & record
                scriptManager.RunScript("GenerateInquiryNo", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                doForm.doRS.SetFieldVal("TXT_INQUIRY", sInquiryNo);
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 11/12/2010 Prevent Source from being required in main
            goTR.StrWrite(ref par_sSections, "EnforceSourceIfPurposeIsLead", "0");

            // TLD 11/12/2010 Prevent Product from being required in main
            goTR.StrWrite(ref par_sSections, "EnforceProductIfPurposeIsInquiry", "0");

            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            int iACSeqID = 0;
            // TLD 8/24/2009 Updated to read for Product XX
            // Read product because Inquiry #s should only be
            // stored in one place
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "", false);
            string sPOP = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS");

            int iPurpose = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_PURPOSE", 2));

            // -------------- For Custom Inquiry #s
            // TLD 3/6/2009 For custom Inquiry #s
            // Make sure the Inquiry # does not already exist.
            if (doForm.doRS.GetFieldVal("TXT_INQUIRY") != "" & iPurpose == 8)
            {
                clRowSet doRSQuote = new clRowSet("AC", 3, "TXT_INQUIRY='" + doForm.doRS.GetFieldVal("TXT_INQUIRY") + "' AND GID_ID <> " + doForm.doRS.GetFieldVal("GID_ID") + "", "", "", -1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 1800);
                if (doRSQuote.GetFirst() == 1)
                {
                    // Message user
                    doForm.MessageBox("This Inquiry Number already exists. Please make the Inquiry Number is unique by appending an A, B, or C to the number.");
                    return false;
                }
            }

            // Update OTH MD for next Inquiry # if we are saving a new AC that has a inquiry #
            if (doForm.oVar.GetVar("AC_UpdateInquiryNo_Ran") != "1")
            {
                doForm.oVar.SetVar("AC_UpdateInquiryNo_Ran", "1");
                if (doForm.GetMode() == "CREATION" & doForm.doRS.GetFieldVal("TXT_INQUIRY") != "" & iPurpose == 8)
                {
                    // Update OTH MD for next Inquir #
                    // update Inquiry Seq ID

                    doForm.doRS.SetFieldVal("LNK_RELATED_DM", goTR.StrRead(sPOP, "QUOTE_DELIVERY", "", true));
                    iACSeqID = Convert.ToInt32(goTR.StrRead(sWOP, "AC_INQUIRYNO", "", true));
                    // update Inquiry Seq ID
                    // TLD 8/24/2009 Updated to write to Product XX
                    goTR.StrWrite(ref sWOP, "AC_INQUIRYNO", iACSeqID + 1);
                    // goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP)
                    goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "XX");
                }
            }
            // -------------- For Custom Inquiry #s
            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sInquiryNO = "";

            // TLD 9/5/2010 Fille LNK_PRIMARY_CO with first LNK_RELATED_CO
            if (doRS.IsLinkEmpty("LNK_RELATED_CO") == false)
            {
                clArray doLink = new clArray();
                doLink = doRS.GetLinkVal("LNK_RELATED_CO", ref doLink, true, 0, 1, "A_a", ref oTable);
                doRS.SetLinkVal("LNK_PRIMARY_CO", doLink);
            }

            // TLD 7/22/2013 Added for imported records
            // Call to generate Inquiry # if new Opp
            if (doRS.iRSType == 2 & Convert.ToInt32(doRS.GetFieldVal("MLS_Purpose", 2)) == 8)
            {
                if (doRS.GetFieldVal("TXT_Inquiry") == "")
                {
                    // TLD 7/22/2013 Change to call from form & record
                    scriptManager.RunScript("GenerateInquiryNo", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                    doRS.SetFieldVal("TXT_INQUIRY", sInquiryNO);
                }
            }

            // VS 04182016 TKT#1049 : Save first listed Related Company to single select link for sorting view
            // Not for this Client for HSI - commented out code
            // doRS.SetFieldVal("LNK_RELATEDFIRST_CO", doRS.GetFieldVal("LNK_RELATED_CO", 2, , , 1), 2)
            par_doCallingObject = doRS;
            return true;
        }

        public bool Activity_ManageControlState_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                Form doForm = (Form)par_doCallingObject;

                int lPurpose = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_PURPOSE", 2));

                // Hide Notice of Lead Email by default
                doForm.SetControlState("BTN_NoticeOfLead", 2); // hide

                // TLD 12/16/2010 Hide/Show BTN_NoticeOfLead if 
                if (lPurpose == 8)
                {
                    doForm.SetControlState("BTN_NoticeOfLead", 0); // show
                                                                   // TLD 5/17/2013 Open to New Lead tab
                    doForm.MoveToTab(13); // Lead
                }
                par_doCallingObject = doForm;
                return true;
            }
            catch (Exception ex)
            {

                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                {
                    goErr.SetError(ex, 45105, sProc);
                }
            }

            return true;
        }

        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/10/2012 Disable Merged checkbox
            doForm.SetControlState("CHK_Merged", 4);
            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/10/2012 Click cancel on merge
            if (doForm.oVar.GetVar("CancelSave") == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/10/2012 Merge Functionality - run at end of CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (doForm.oVar.GetVar("CN_Merge") != "1")
                    {
                        // TLD 5/17/2013 Updated for generic messagebox
                        // Don't allow merge of contact to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID"))
                        {
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CN", "MergeFail");

                        }
                        else
                        {
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CN", "Merge");

                        }
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/10/2012 Disable Merged checkbox
            doForm.SetControlState("CHK_Merged", 4);
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/10/2012 Click cancel on merge
            if (doForm.oVar.GetVar("CancelSave") == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/10/2012 Merge Functionality - run at end of CO_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (doForm.oVar.GetVar("CO_Merge") != "1")
                    {
                        // TLD 5/17/2013 Updated for generic messagebox
                        // Don't allow merge of company to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID"))
                        {
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CO", "MergeFail");

                        }
                        else
                        {
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CO", "Merge");

                        }
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool GenerateInquiryNo_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 10/1/07 Changed now to goTR.NowUTC().
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 7/22/2013 Changed to _Pre
            // call from form & record
            par_bRunNext = false;

            // PURPOSE:
            // Generate OP and AC of type Lead Inquiry Numbers (7 digits)
            // On 10/1/07, MI changed 'now' to the UTC value, goTR.UTC_ServerToUTC(Now).
            // This may cause the duplication of some ID numbers in existing databases.
            // RETURNS:
            // Code number via return parameter.

            // Dim doForm As clForm = par_doCallingObject
            Form doForm = (Form)par_doCallingObject;
            string s = "";
            // Dim dtBase As DateTime
            // Dim lDiff As Long
            // Dim doUSRS As clRowSet
            // Dim dtDateTime As DateTime
            // Dim sDiv As String = "" 'User's division
            // TLD 8/24/2009 Updated to read for Product XX
            // Read for Product XX because Inquiry #s should ONLY
            // be stored in 1 place
            // Dim sWOP As String = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS")
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "", false);
            // Dim i As Integer = 0
            string sSeq = ""; // Sequential quote number
                              // Dim sYear As String = ""
            string sFile = par_s1;

            // TLD 4/22/2009 Customizes Inquiry Numbers for AC, type Lead and OP

            try
            {
                // DateSerial gives us a serial number of the starting point in time
                // dtBase = DateSerial(1997, 1, 1)         'DO NOT change the year number after the DB is in use!
                // lDiff = DateDiff("s", dtBase, goTR.NowUTC())       'difference between dtBase and Now
                // s = goTR.Pad(lDiff.ToString, 10, "0", "L") & goP.GetUserCode()      'US.TXT_Code

                // Gets sequence # from WOP
                switch (Strings.UCase(par_s1))
                {
                    case "AC":
                        {
                            sSeq = goTR.StrRead(sWOP, "AC_INQUIRYNO");
                            break;
                        }

                    case "OP":
                        {
                            sSeq = goTR.StrRead(sWOP, "OP_INQUIRYNO");
                            break;
                        }
                }

                // Ensures at least a 7 digit number
                s = goTR.Pad(sSeq, 7, "0", "L");
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                {
                    goErr.SetError(ex, 45105, sProc);
                    return false;
                }
            }

            par_oReturn = s;
            par_doCallingObject = doForm;
            return true;
        }

        public bool GenerateSysName_Pre(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // CS 8/25/08 Case US: Added Title text
            // CS 8/20/08 Case CN: added Title Text
            // MI 11/14/07 Case CN: added TXT_ContactCode.
            // MI 10/15/07 Appended ' UTC' to all datetimes
            // MI 6/22/07 Changes to QT, OP, PR, PD, MO
            // MI 4/17/07 Added Phone Co to CN; Phone to CO ; removed padding from and to to 4 in MS; Status to OP, PR, QT, TD(?)
            // MI 3/9/07 Removed ellipsis from Co name in AC name.
            // MI 3/1/07 Added Contact, Company to AC Name.
            // MI 2/1/07 Added Date, Originator to Project name
            // MI 9/15/06 Updated QL, WT SYS_Name formats.
            // MI 7/25/06 Added raising error when field is not in the rowset.
            // MI 7/24/06 Mods. 
            // MI 7/20/06 Created in clScripts.
            // MI 7/20/06 Finished, tested, moved from clData to clScripts and renamed from GetCurrentRecordName to GetSysName.
            // MI 7/17/06 Started making this work in SellSQL.

            // AUTHOR: MI
            // PURPOSE:
            // Send back via the par_oReturn parameter the 'User Friendly' Name of the current record in par_oRowset.
            // This is to be called from each RecordOnSave script, but can be called
            // from any other code to generate a SYS_Name value. Do NOT set the name in 
            // the par_doCallingObject rowset or the script won't be usable simply for evaluating the returned
            // string.
            // IMPORTANT: Keep this "in sync" with clScripts.GetDefaultSort(), which is called from
            // clData.GetDefaultSort.
            // PARAMETERS:
            // par_doCallingObject: rowset object (for example, 'doRS'). The rowset must contain
            // all links and fields being referenced in the code below or error 45163 will be
            // raised. This can be achieved by putting '**' in the FIELDS parameter of the rowset, but 
            // avoid this when possible for performance reasons. The object, declared ByRef to conserve
            // resources by avoiding duplicating the object in memory, should not be altered directly
            // by this method (the purpose of the method is to return the name, not set it), but check
            // the code below to be sure.
            // par_doArray: not used
            // par_s1 - 5: not used
            // par_oReturn: String containing the generated SysName.
            // RETURNS:
            // True as a result. Returns friendly name or an empty string if the
            // filename is invalid via par_oReturn parameter.
            // EXAMPLE:
            // 'From a RecordOnSave script (not tested):
            // Dim sName as string = goScr.RunScript("GenerateSysName", doRS)
            // NOTES:
            // When a "Name" that is built with this method
            // is displayed in a View or linkbox and the same Name field is used
            // to sort the View or linkbox, at least the first field should match
            // the first field defined in clData::LKGetSortValue(). Otherwise what's
            // displayed will appear to be sorted arbitrarily.
            // NOTE 2:  
            // Links will not be tested because they are loaded automatically, but 
            // currently there is a bug in clRowset. RH working on this.

            string sProc = "clScripts:GenerateSysName";
            // goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

            // TLD 5/17/2013 Convert to _Pre

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";

            // We assume that sFileName is valid. If this is a problem, test it here and SetError.

            // TLD 5/17/2013 Added Try
            try
            {
                switch (Strings.UCase(sFileName))
                {
                    case "BI"     // BMT Industry	TXT_BMTINDUSTRYNAME	
                   :
                        {
                            if (!doRS.IsLoaded("TXT_BMTINDUSTRYNAME"))
                            {
                                goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_BMTINDUSTRYNAME");
                                sResult = Convert.ToString(doRS.GetFieldVal("TXT_BMTINDUSTRYNAME", 0, -1, true, -1, "CR", "A_a"));
                                // TLD 5/17/2013 Added
                                // par_oReturn = sResult
                                par_bRunNext = false;
                            }
                            break;
                        }

                    case "BP":    // BMT Product	TXT_BMTPRODUCTNAME	

                        {
                            if (!doRS.IsLoaded("TXT_BMTPRODUCTNAME"))
                            {
                                goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_BMTPRODUCTNAME");
                                sResult = Convert.ToString(doRS.GetFieldVal("TXT_BMTPRODUCTNAME", 0, -1, true, -1, "CR", "A_a"));
                                // TLD 5/17/2013 Added
                                // par_oReturn = sResult
                                par_bRunNext = false;
                            }
                            break;
                        }

                    case "US"         // USER		TXT_NameLast, TXT_Name First 
             :
                        {
                            if (!doRS.IsLoaded("TXT_NameFirst"))
                            {
                                goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_NameFirst");

                            }
                            if (!doRS.IsLoaded("TXT_NameLast"))
                            {
                                goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_NameLast");
                            }
                            sTemp = Convert.ToString(doRS.GetFieldVal("TXT_NameFirst"));
                            sResult = Convert.ToString(doRS.GetFieldVal("TXT_NameLast"));

                            if (sTemp != "" & sResult != "")
                            {
                                sResult = sResult + ", " + sTemp;
                            }
                            else
                            {
                                sResult = sResult + sTemp;

                                // TLD 5/17/2013 Added
                                // par_oReturn = sResult
                                par_bRunNext = false;
                            }
                            break;
                        }
                }

                // TLD 5/17/2013 Added
                if (!par_bRunNext)
                {
                    sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
                    sResult = goTR.Replace(sResult, Constants.vbTab, " ");
                    par_oReturn = sResult;
                }
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                {
                    goErr.SetError(ex, 45105, sProc);
                }
            }
            par_doCallingObject = doRS;
            return true;
        }

        public bool LeadCreateRecordsFromDatatable_Pre(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/29/2013 Tasks: Utility Import Leads
            // Match CO, CN, AC by TXT_RefNo
            // If no match, ADD record
            // If match, do nothing
            par_bRunNext = false;

            try
            {
                DataSet ds = (DataSet)par_doCallingObject;
                DataTable dt = new DataTable();
                string sWork = "";
                bool bAddCO = false;
                string sUSID = "";
                string sWorkDate = ""; // Temp Date
                string sNotes = "";
                string sLeadID = ""; // Lead Record ID
                string sAddress = "";
                string sCity = "";
                string sState = "";
                string sZip = "";
                string sCountry = "";
                string sPhone = "";
                string sIndustryID = ""; // BMT Industry ID
                int iCOCount = 0;
                int iACCount = 0;
                string sCOName = ""; // Plant CO Name
                clRowSet doCORS = null/* TODO Change to default(_) if this is not a reference type */;
                string sURL = "";
                string sPlantOwnerName = ""; // Plant Owner Name
                string sPlantOwnerID = ""; // Plant Owner Record ID

                // Check for records?
                if (dt.Rows.Count == 0 | dt.Columns.Count == 0)
                {
                    // No records return true to timeout?
                    // Return True
                    goMeta.LineWrite(goP.GetMe("ID"), "OTH_Lead_Upload_ImportCompleteFlag", "Message", "The import failed because Selltis could not read records from the excel spreadsheet.  Make sure pcLink is running, the Excel workbook is open, there is a sheet named Selltis in the open workbook, and that you have saved the Excel workbook.", ref par_oConnection, "", "");
                    goMeta.LineWrite(goP.GetMe("ID"), "OTH_Lead_Upload_ImportCompleteFlag", "ImportComplete", "True", ref par_oConnection, "", "");
                    return false;
                }

                // loop through import dt and add/update CO, CN, AC Leads
                foreach (DataRow dr in dt.Rows)
                {
                    // Clear vars
                    sWork = "";
                    // TLD 7/10/2013 Reset add var, getting read only error
                    bAddCO = false;

                    // Check table for columns
                    if (dt.Columns.Contains("P_ULT_NAME"))
                    {
                        // Does CO exist -- only try for 1?
                        sPlantOwnerName = dr["P_ULT_NAME"].ToString();
                        doCORS = new clRowSet("CO", 3, "TXT_CompanyName='" + goTR.PrepareForSQL(sPlantOwnerName) + "'", "", "", -1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 1800);
                        if (doCORS.Count() == 0)
                        {
                            bAddCO = true;
                            doCORS = new clRowSet("CO", 2, "", "", "", -1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 1800);
                        }
                        sPlantOwnerID = Convert.ToString(doCORS.GetFieldVal("GID_ID", 0, -1, true, -1, "CR", "A_a"));
                    }

                    // Get BMT Industry for AC & CO
                    if (dt.Columns.Contains("IND_DESC"))
                    {
                        // Find BMT Industry Match
                        clRowSet doBIRS = new clRowSet("BI", 3, "TXT_BMTIndustryName='" + goTR.PrepareForSQL(dr["IND_DESC"].ToString()) + "'", "", "", -1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 1800);
                        if (doBIRS.GetFirst() == 0)
                        {
                            sIndustryID = Convert.ToString(doBIRS.GetFieldVal("GID_ID", 0, -1, true, -1, "CR", "A_a"));
                        }
                    }

                    // Get User
                    if (dt.Columns.Contains("Credited to"))
                    {
                        // Find User ID
                        clRowSet doUSRS = new clRowSet("US", 3, "SYS_Name='" + goTR.PrepareForSQL(dr["Credited to"].ToString()) + "'", "", "", -1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 1800);
                        if (doUSRS.GetFirst() == 0)
                        {
                            sUSID = Convert.ToString(doUSRS.GetFieldVal("GID_ID", 0, -1, true, -1, "CR", "A_a"));
                        }
                    }

                    // Get URL
                    if (dt.Columns.Contains("REPORT_LINK"))
                    // Find User ID
                    {
                        sURL = goTR.PrepareForSQL(dr["REPORT_LINK"].ToString());
                    }

                    // Get Address
                    if (dt.Columns.Contains("PLANT_ADDR"))
                    {
                        sAddress = dr["PLANT_ADDR"].ToString();
                    }

                    // Get City
                    if (dt.Columns.Contains("PLANT_CITY"))
                    {
                        sCity = dr["PLANT_CITY"].ToString();
                    }

                    // Get State
                    if (dt.Columns.Contains("PLANT_ST"))
                    {
                        sState = dr["PLANT_ST"].ToString();
                    }

                    // Get Zip
                    if (dt.Columns.Contains("PLANT_ZIP"))
                    {
                        sZip = dr["PLANT_ZIP"].ToString();
                    }

                    // Set Phone
                    if (dt.Columns.Contains("PL_PHONE"))
                    {
                        sPhone = dr["PL_PHONE"].ToString();
                    }

                    if (bAddCO & doCORS != null)
                    {

                        // Set New Plant Owner Name
                        if (sPlantOwnerName != "")
                        {
                            doCORS.SetFieldVal("TXT_CompanyName", sPlantOwnerName);
                        }

                        // Set BMT Industry
                        if (sIndustryID != "")
                        {
                            doCORS.SetFieldVal("LNK_Connected_BI", sIndustryID);
                        }

                        // Set Team Leader
                        if (sUSID != "")
                        {
                            doCORS.SetFieldVal("LNK_TeamLeader_US", sUSID);
                        }

                        // Set URL
                        if (sURL != "")
                        {
                            doCORS.SetFieldVal("URL_URLS", sURL);
                        }

                        // Save
                        if (doCORS.Commit() == 0)
                        // write message to log with error
                        {
                            goLog.Log(sProc, "Plant Owner CO add from lead import failed for Ref #'" + sPlantOwnerName + " with error " + goErr.GetLastError("NUMBER") + "'", -1, false, false, 0, 0);
                        }
                        else
                        {
                            iCOCount += 1;
                        }
                    }

                    // Create Lead
                    clRowSet doACRS = new clRowSet("AC", 2, "", "", "", -1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 1800);

                    if (doACRS.GetFirst() == 0)
                    {

                        // Set BMT Industry
                        if (sIndustryID != "")
                        {
                            doACRS.SetFieldVal("LNK_Connected_BI", sIndustryID);
                        }

                        // Set Project Value
                        if (dt.Columns.Contains("PROJ_TIV"))
                        {
                            sWork = dr["PROJ_TIV"].ToString();
                            doACRS.SetFieldVal("CUR_ProjectValue", goTR.RoundCurr(Strings.Len(sWork)), -1);
                        }

                        // Set IIR Id #
                        if (dt.Columns.Contains("PROJECT_NO"))
                        {
                            sWork = dr["PROJECT_NO"].ToString();
                            doACRS.SetFieldVal("TXT_IIRIDNo", Strings.Left(sWork, 20));
                        }

                        // Set Project Name
                        if (dt.Columns.Contains("PROJ_NAME"))
                        {
                            sWork = dr["PROJ_NAME"].ToString();
                            doACRS.SetFieldVal("TXT_ProjectName", Strings.Left(sWork, 50));
                        }

                        // Set Plant Name
                        if (dt.Columns.Contains("PLANT_NAME"))
                        {
                            sWork = dr["PLANT_NAME"].ToString();
                            doACRS.SetFieldVal("TXT_PlantName", Strings.Left(sWork, 20));
                        }

                        // Set Plant Owner Link
                        if (sPlantOwnerID != "")
                        {
                            // Make sure it exists?
                            clRowSet doPlantRS = new clRowSet("CO", 6, "GID_ID='" + sPlantOwnerID + "'");
                            if (Convert.ToInt32(doPlantRS.GetFieldVal("BI__Count")) == 1)
                            {
                                doACRS.SetFieldVal("LNK_PlantOwner_CO", sPlantOwnerID);
                            }
                        }

                        // Set Plant Address
                        if (sAddress != "")
                        {
                            doACRS.SetFieldVal("TXT_PlantAddress", sAddress);
                        }

                        // Set Plant City
                        if (sCity != "")
                        {
                            doACRS.SetFieldVal("TXT_PlantCity", sCity);
                        }
                        // Set Plant State
                        if (sState != "")
                        {
                            doACRS.SetFieldVal("TXT_PlantState", sState);
                        }

                        // Set Plant Zip
                        if (sZip != "")
                        {
                            doACRS.SetFieldVal("TXT_PlantZip", sZip);
                        }

                        // 
                        // Set Plant Phone
                        if (sPhone != "")
                        {
                            doACRS.SetFieldVal("TEL_PlantPhone", sPhone);
                        }

                        // Set Project Stage
                        if (dt.Columns.Contains("ACT_DESC"))
                        {
                            clList goList = new clList();
                            sWork = dr["ACT_DESC"].ToString();
                            string sStage = goList.LReadSeek("AC:PROJECTSTAGE", "VALUE", sWork); // Find index number of web submission status in Ac type list
                                                                                                 // If type looking for does not exist, the default MLS value is returned so need to 
                                                                                                 // check if returned that error and if so, skip this.
                            if (sStage != "" & goErr.GetLastError("NUMBER") != "E30035")
                            {
                                doACRS.SetFieldVal("MLS_ProjectStage", goTR.StringToNum(sStage, "", ref par_iValid, ""), 2);
                            }
                        }

                        // NOTE:  Dates in spreadsheet are formatted for yyyymm
                        // Add to it to be the first day of month

                        // Set AFE Date
                        if (dt.Columns.Contains("AFE_DATE"))
                        {
                            sWorkDate = dr["AFE_DATE"].ToString(); // Sample format in spreadsheet 201304
                            if (sWorkDate != "")
                            {
                                if (Strings.Len(sWorkDate) == 6)
                                {
                                    sWorkDate = Strings.Left(sWorkDate, 4) + "-" + Strings.Right(sWorkDate, 2) + "-01";
                                    doACRS.SetFieldVal("DTT_AFE", sWorkDate + "|Now");
                                }
                            }
                        }

                        // Set RFQ Date
                        if (dt.Columns.Contains("RFQ_DATE"))
                        {
                            sWorkDate = dr["RFQ_DATE"].ToString(); // Sample format in spreadsheet 201304
                            if (sWorkDate != "")
                            {
                                if (Strings.Len(sWorkDate) == 6)
                                {
                                    sWorkDate = Strings.Left(sWorkDate, 4) + "-" + Strings.Right(sWorkDate, 2) + "-01";
                                    doACRS.SetFieldVal("DTT_RFQ", sWorkDate + "|Now");
                                }
                            }
                        }

                        // Set Bid Doc Date
                        if (dt.Columns.Contains("BID_DOC"))
                        {
                            sWorkDate = dr["BID_DOC"].ToString(); // Sample format in spreadsheet 201304
                            if (sWorkDate != "")
                            {
                                if (Strings.Len(sWorkDate) == 6)
                                {
                                    sWorkDate = Strings.Left(sWorkDate, 4) + "-" + Strings.Right(sWorkDate, 2) + "-01";
                                    doACRS.SetFieldVal("DTT_BidDoc", sWorkDate + "|Now");
                                }
                            }
                        }

                        // Set Project Kickoff Date
                        if (dt.Columns.Contains("KICKOFF"))
                        {
                            sWorkDate = dr["KICKOFF"].ToString(); // Sample format in spreadsheet 201304
                            if (sWorkDate != "")
                            {
                                if (Strings.Len(sWorkDate) == 6)
                                {
                                    sWorkDate = Strings.Left(sWorkDate, 4) + "-" + Strings.Right(sWorkDate, 2) + "-01";
                                    doACRS.SetFieldVal("DTT_ProjectKickoff", sWorkDate + "|Now");
                                }
                            }
                        }

                        // Set Completion Date
                        if (dt.Columns.Contains("COMPLETION"))
                        {
                            sWorkDate = dr["COMPLETION"].ToString(); // Sample format in spreadsheet 201304
                            if (sWorkDate != "")
                            {
                                if (Strings.Len(sWorkDate) == 6)
                                {
                                    sWorkDate = Strings.Left(sWorkDate, 4) + "-" + Strings.Right(sWorkDate, 2) + "-01";
                                    doACRS.SetFieldVal("DTT_Completion", sWorkDate + "|Now");
                                }
                            }
                        }

                        // Set Scope
                        if (dt.Columns.Contains("SCOPE"))
                        {
                            sWork = dr["SCOPE"].ToString(); // Sample format in spreadsheet 201304
                            if (sWork != "")
                            {
                                doACRS.SetFieldVal("MMO_Scope", sWork);
                            }
                        }

                        // Set Schedule
                        if (dt.Columns.Contains("SCHEDULE"))
                        {
                            sWork = dr["SCHEDULE"].ToString(); // Sample format in spreadsheet 201304
                            if (sWork != "")
                            {
                                doACRS.SetFieldVal("MMO_Schedule", sWork);
                            }
                        }

                        // Set URL
                        if (sURL != "")
                        {
                            doACRS.SetFieldVal("URL_URLS", sURL);
                        }

                        // Set LNK_CreditedTo_US
                        if (sUSID != "")
                        {
                            doACRS.SetFieldVal("LNK_CreditedTo_US", sUSID);
                        }

                        // TLD 7/23/2013 Added Go %, Get %, Ballpark Price, GM columns
                        // Go %
                        if (dt.Columns.Contains("GO %"))
                        {
                            sWork = dr["GO %"].ToString();
                            if (goTR.IsNum(sWork, "") & Strings.Len(sWork) >= 0 & Strings.Len(sWork) <= 256 & Strings.InStr(sWork, ".") < 1)
                            {
                                doACRS.SetFieldVal("SI__Margin", goTR.StringToNum(sWork, "", ref par_iValid, ""));
                            }
                        }

                        // Get %
                        if (dt.Columns.Contains("GET %"))
                        {
                            sWork = dr["GET %"].ToString();
                            if (goTR.IsNum(sWork) & Strings.Len(sWork) >= 0 & Strings.Len(sWork) <= 256 & Strings.InStr(sWork, ".") < 1)
                            {
                                doACRS.SetFieldVal("SI__Probability", goTR.StringToNum(sWork, "", ref par_iValid, ""));
                            }
                        }

                        // Ballpark Price
                        if (dt.Columns.Contains("BALLPARK PRICE"))
                        {
                            sWork = dr["BALLPARK PRICE"].ToString();
                            if (goTR.IsNum(sWork))
                            {
                                doACRS.SetFieldVal("CUR_Ballpark", goTR.StringToNum(sWork, "", ref par_iValid, ""));
                            }
                        }

                        // Ballpark Price
                        if (dt.Columns.Contains("GM"))
                        {
                            sWork = dr["GM"].ToString();
                            if (goTR.IsNum(sWork))
                            {
                                doACRS.SetFieldVal("SR__GM", goTR.StringToNum(sWork, "", ref par_iValid, ""));
                            }
                        }

                        // Set Other Fields
                        doACRS.SetFieldVal("MLS_Purpose", 8, 2); // Lead
                        doACRS.SetFieldVal("MLS_Status", 0, 2); // Open

                        // Save
                        if (doACRS.Commit() == 0)
                        // write message to log with error
                        {
                            goLog.Log(sProc, "Lead import failed with error " + goErr.GetLastError("NUMBER") + "'", 1, false, false, 0, 0);
                        }
                        else
                        {
                            iACCount += 1;
                        }
                    }
                }

                // Successful?
                // Send alert that done?
                // Opens Models - My Created Today
                if (iACCount == 0 & iCOCount == 0)
                {
                    // no records to import
                    goMeta.LineWrite(goP.GetMe("ID"), "OTH_Lead_Upload_ImportCompleteFlag", "Message", "There were no new records to import.", ref par_oConnection, "", "");
                    goMeta.LineWrite(goP.GetMe("ID"), "OTH_Lead_Upload_ImportCompleteFlag", "ImportComplete", "True", ref par_oConnection, "", "");
                }
                else
                {
                    // records imported
                    goMeta.LineWrite(goP.GetMe("ID"), "OTH_Lead_Upload_ImportCompleteFlag", "Message", "The lead import job is complete.  You should receive an alert to open a desktop to verify import.  The following records were created:  Companies: " + iCOCount + " Leads: " + iACCount + ".", ref par_oConnection, "", "");
                    goMeta.LineWrite(goP.GetMe("ID"), "OTH_Lead_Upload_ImportCompleteFlag", "ImportComplete", "True", ref par_oConnection, "", "");
                    // TLD 5/29/2013 Opens "Lead Created Today By Me" desktop
                    goUI.AddAlert("Lead Import Done", clC.SELL_ALT_OPENDESKTOP, "DSK_6215E43B-5C96-4F13-5858-A1C6011E44DF", goP.GetMe("ID"), "Lead16.gif");
                }
            }
            catch (Exception ex)
            {
                // goUI.NewWorkareaMessage("Lead import failed.  Make sure the excel lead import file is open.")
                goMeta.LineWrite(goP.GetMe("ID"), "OTH_Lead_Upload_ImportCompleteFlag", "Message", "The import failed.  Make sure pcLink is running, the Excel workbook is open, there is a sheet named Selltis in the open workbook, and that you have saved the Excel workbook.", ref par_oConnection, "", "");
                goMeta.LineWrite(goP.GetMe("ID"), "OTH_Lead_Upload_ImportCompleteFlag", "ImportComplete", "Error", ref par_oConnection, "", "");
                return true;
            }

            return true;
        }

        public bool Leads_OpenWOP_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/29/2013 For import leads from open spreadsheet

            try
            {
                bool bPerms = true; // Assume perms

                // Check feature perms for import from excel?
                if (goPerm.GetUserPermission(goP.GetMe("ID"), "IMPORT") != "1")
                {
                    bPerms = false;
                }

                // Perm to create Leads?
                if (goData.GetAddPermission("AC") == false)
                {
                    bPerms = false;
                }

                // Perm to create companies?
                if (goData.GetAddPermission("CO") == false)
                {
                    bPerms = false;
                }

                // 'Perm to create companies?
                // If goData.GetAddPermission("CN") = False Then
                // bPerms = False
                // End If

                if (bPerms)
                // open custom Expense page
                {
                    goUI.OpenURLExternal("../Pages/cus_wopLeads.aspx", "", "height=500,width=600,left=100,top=100,status=yes,location=no,toolbar=no,resizable=yes,titlebar=no,dependent=yes");
                }
                else
                // Message to user
                {

                    goUI.NewWorkareaMessage("You do not have permission to (1) import excel data and/or (2) to create Leads, Contacts or Companies.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, par_doArray = null, "", "", "", "", "");

                }

            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                {
                    goErr.SetError(ex, 45105, sProc);
                }
            }

            return true;
        }

        public bool MergeRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/17/2013 Changed to _Pre
            par_bRunNext = false;
            // TLD 8/10/2012 Added for merge

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName()) + "'", "", "", -1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 1800);
                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Microsoft.VisualBasic.Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField, 0, -1, true, -1, "CR", "A_a") == "")
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    }
                                    break;
                                }

                            case "MMO":
                                {
                                    // Append
                                    if (doRSMergeTo.GetFieldVal(sField, 0, -1, true, -1, "CR", "A_a") == "")
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    }
                                    else
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMergeTo.GetFieldVal(sField) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + doRSMerge.GetFieldVal("SYS_Name") + " ==" + Constants.vbCrLf + doRSMerge.GetFieldVal(sField));

                                    }
                                    break;
                                }

                            case "CHK":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField, 2, -1, true, -1, "CR", "A_a") == "")
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    }
                                    break;
                                }

                            case "MLS":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField, 2, -1, true, -1, "CR", "A_a") == "")
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    }
                                    break;
                                }
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Convert.ToString(Strings.Chr(9)));
                        if (sLinkType[4] == "NN" || Convert.ToInt32(sLinkType[1]) == 2)
                        {
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref par_doArray, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (doRSMergeTo.GetFieldVal(aLinks.GetItem(i)) == "")
                        {
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref par_doArray, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);
                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;
            par_doCallingObject = doRSMerge;
            return true;
        }

        public bool MessageBoxEvent_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sJournal = "";
            string sWork = "";

            switch (Strings.UCase(par_s5))
            {
                case "MERGE":
                    {
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // run merge script, continue save
                                    scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
                                    break;
                                }

                            case "NO":
                                {
                                    // Clear merged to co linkbox, continue save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    break;
                                }

                            case "CANCEL":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "MERGEFAIL":
                    {
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }
            }

            return true;
        }

        public bool OpCalcCompetitors(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 10/1/07 Changed now to goTR.NowUTC().
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PURPOSE: TLD 4/22/2009
            // Calculates # of competitors from Competing Vendor field
            // Called from OP_FormonTabClick and OP_FormOnSave
            // RETURNS:
            // Number vendors via return parameter.

            Form doForm = (Form)par_doCallingObject;
            int i = 0;

            try
            {
                i = Convert.ToInt32(doForm.doRS.GetLinkCount("LNK_COMPETING_VE"));
            }
            catch (Exception ex)
            {
                if (ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
                {
                    goErr.SetError(ex, 45105, sProc);
                    return false;
                }
            }

            par_oReturn = i;
            par_doCallingObject = doForm;

            return true;
        }

        public bool OP_FormControlOnChange_BTN_NOTICEOFEMAIL_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/8/07 Changed time stamp to local time, no label.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS 06272016 TKT#1140 : New changes as per customer request. Commented a lot of the Letter

            Form doForm = (Form)par_doCallingObject;
            clRowSet doACRS; // new AC of type email sent
            clArray doContacts = new clArray(); // holds contacts of users
            string sLetter = "";
            string sObjectPassed = par_doCallingObject.GetType().ToString();
            string sID = "";
            clSend oMySend = new clSend();
            clSend oSend;
            // Dim bNoAddPerm As Boolean = False
            string sSubject = "";
            // TLD 8/5/2014 Commented, not longer used
            // Dim sWork As String = ""
            // TLD 3/28/2014 For list of contacts
            string sContact = "";

            // TLD 4/15/2009 Modified to send job straight through to PC Link
            // TLD 4/14/2009 Creates AC of type E-mail Sent and sends

            // TLD 5/16/2012 Don't need bNoAddPerm, not used elsewhere
            // Check for add permissions
            if (goData.GetAddPermission("AC") == false)
            {
                // bNoAddPerm = True
                doForm.MessageBox("You cannot create a Notice of Bid Email because you don't have permission to create new Activities.");
                return false;
            }

            // TLD 5/16/2012 Don't need bNoAddPerm, not used elsewhere
            // If bNoAddPerm = True Then
            // doForm.MessageBox("You cannot create a Notice of Bid Email because you don't have permission to create new Activities.")
            // Return False
            // End If

            // TLD 3/31/2014 ONLY save if changed
            // Saves OP before sending AC email
            if (doForm.IsDirty)
            {
                if (doForm.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                {
                    return false;
                }
            }

            if (sObjectPassed == "clForm")
                sID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

            // Make sure POP set with email client
            if (goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EMAILPROG", "<%NONE%>", true) == "<%NONE%>")
            {
                doForm.MessageBox("You must select your e-mail program in Personal Options before you can send.");
                return true;
            }

            // TLD 5/12/2009 dolink has been fixed, so modified to just use 1 dolink array
            // TLD 4/15/2009 -- this does not appear to build the list, but clears it
            // Sent to Christy to check me, but in the meantime created individuals
            // Gets Users from 6 fields on Inquiry Flow tab
            doContacts = doForm.doRS.GetLinkVal("LNK_SALESCOORD_US%%LNK_IS_CN", ref par_doArray, false, 0, -1, "A_a", ref oTable);
            doContacts = doForm.doRS.GetLinkVal("LNK_SALESPRIMARY_US%%LNK_IS_CN", ref par_doArray, false, 0, -1, "A_a", ref oTable);
            doContacts = doForm.doRS.GetLinkVal("LNK_ESTCOORD_US%%LNK_IS_CN", ref par_doArray, false, 0, -1, "A_a", ref oTable);
            doContacts = doForm.doRS.GetLinkVal("LNK_ESTPRIMARY_US%%LNK_IS_CN", ref par_doArray, false, 0, -1, "A_a", ref oTable);
            doContacts = doForm.doRS.GetLinkVal("LNK_ENGCOORD_US%%LNK_IS_CN", ref par_doArray, false, 0, -1, "A_a", ref oTable);
            doContacts = doForm.doRS.GetLinkVal("LNK_ENGPRIMARY_US%%LNK_IS_CN", ref par_doArray, false, 0, -1, "A_a", ref oTable);
            // TLD 5/12/2009 dolink has been fixed, so modified to just use 1 dolink array
            // Dim doContacts1 As New clArray()
            // Dim doContacts2 As New clArray()
            // Dim doContacts3 As New clArray()
            // Dim doContacts4 As New clArray()
            // Dim doContacts5 As New clArray()
            // doContacts = doForm.doRS.GetLinkVal("LNK_SALESCOORD_US%%LNK_IS_CN", doContacts, False)
            // doContacts1 = doForm.doRS.GetLinkVal("LNK_SALESPRIMARY_US%%LNK_IS_CN", doContacts1, False)
            // doContacts2 = doForm.doRS.GetLinkVal("LNK_ESTCOORD_US%%LNK_IS_CN", doContacts2, False)
            // doContacts3 = doForm.doRS.GetLinkVal("LNK_ESTPRIMARY_US%%LNK_IS_CN", doContacts3, False)
            // doContacts4 = doForm.doRS.GetLinkVal("LNK_ENGCOORD_US%%LNK_IS_CN", doContacts4, False)
            // doContacts5 = doForm.doRS.GetLinkVal("LNK_ENGPRIMARY_US%%LNK_IS_CN", doContacts5, False)

            // TLD 4/22/2010 Changed "Opportunity" to "NOB:"
            // TLD 5/15/2009 Sets subject field
            // sSubject = "Opportunity: " & doForm.doRS.GetFieldVal("TXT_INQUIRY") & " " & doForm.doRS.GetFieldVal("LNK_FOR_CO%%TXT_COMPANYNAME")
            sSubject = "NOB: " + doForm.doRS.GetFieldVal("TXT_INQUIRY") + " " + doForm.doRS.GetFieldVal("LNK_FOR_CO%%TXT_COMPANYNAME");

            // Build text for Letter field
            if (Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2)) != goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, "", "", ref par_iValid, "|", false))
            // not blank, Gets Bid Date
            {
                sLetter = "Bid Date: " + doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1) + Constants.vbCrLf;
            }
            else
            {
                sLetter = "Bid Date: " + Constants.vbCrLf;
            }

            if (Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_PREBIDMEETING", 2)) != goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, "", "", ref par_iValid, "|", false))
            // not blank, Gets PreBid Date
            {
                sLetter = sLetter + "PreBid: " + doForm.doRS.GetFieldVal("DTE_PREBIDMEETING", 1) + Constants.vbCrLf;
            }
            else
            {
                sLetter = sLetter + "PreBid: " + Constants.vbCrLf;
            }

            // TLD 5/15/2009 Added Mandatory, but only if checked
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_PreBidMandatory", 2)) == 1)
            {
                sLetter = sLetter + "Mandatory" + Constants.vbCrLf;

                // TLD 5/15/2009 Added 2 spaces before the entries under Coordinator
                // TLD 5/15/2009 Added Coatings after Technical Services
                sLetter = sLetter + "Owner: " + doForm.doRS.GetFieldVal("LNK_FOR_CO%%TXT_COMPANYNAME") + Constants.vbCrLf;
                sLetter = sLetter + "Description: " + doForm.doRS.GetFieldVal("TXT_DESCRIPTION") + Constants.vbCrLf;
                // TLD 6/10/2009 Modified to non-static Location -- pulls from
                // city and state job site location fields
                sLetter = sLetter + "Location: " + doForm.doRS.GetFieldVal("TXT_CITYJOBSITE") + ", " + doForm.doRS.GetFieldVal("TXT_STATEJOBSITE") + Constants.vbCrLf + Constants.vbCrLf;

                // TLD 8/5/2014 Customer changed this to MLS_PreBidReq
                // TLD 8/16/2011 -------Added 2 lines
                // If doForm.doRS.GetFieldVal("CHK_PreBidReq", 2) = 1 Then
                // sWork = "Yes"
                // Else
                // sWork = "No"
                // End If
                // TLD 8/5/2014 Customer changed this to MLS_PreBidReq
                sLetter = sLetter + "Pre-bid Design Required: " + doForm.doRS.GetFieldVal("MLS_PreBidReq", 1) + Constants.vbCrLf + Constants.vbCrLf;
                // sLetter = sLetter & "Pre-bid Design Required: " & sWork & vbCrLf & vbCrLf
                // TLD 8/5/2014 Customer changed this to MLS_TBRReq
                // If doForm.doRS.GetFieldVal("CHK_TBRReq", 2) = 1 Then
                // sWork = "Yes"
                // Else
                // sWork = "No"
                // End If
                // TLD 8/5/2014 Customer changed this to MLS_TBRReq
                // VS 06272016 TKT#1140
                // sLetter = sLetter & "TBR Required: " & doForm.doRS.GetFieldVal("MLS_TBRReq", 1) & vbCrLf & vbCrLf
                sLetter = sLetter + "Pre-Bid Design Required by: " + doForm.doRS.GetFieldVal("DTE_DESIGNDATE", 1) + Constants.vbCrLf + Constants.vbCrLf;

                // sLetter = sLetter & "TBR Required: " & sWork & vbCrLf & vbCrLf
                // TLD 8/16/2011 -------End added 2 lines

                sLetter = sLetter + "Coordinators: " + Constants.vbCrLf;
                sLetter = sLetter + "  Sales - " + doForm.doRS.GetFieldVal("LNK_SALESCOORD_US%%TXT_NAMEFIRST") + " " + doForm.doRS.GetFieldVal("LNK_SALESCOORD_US%%TXT_NAMELAST") + Constants.vbCrLf;
                sLetter = sLetter + "  Estimating - " + doForm.doRS.GetFieldVal("LNK_ESTCOORD_US%%TXT_NAMEFIRST") + " " + doForm.doRS.GetFieldVal("LNK_ESTCOORD_US%%TXT_NAMELAST") + Constants.vbCrLf;
                sLetter = sLetter + "  Technical Services (Engineer) - " + doForm.doRS.GetFieldVal("LNK_ENGCOORD_US%%TXT_NAMEFIRST") + " " + doForm.doRS.GetFieldVal("LNK_ENGCOORD_US%%TXT_NAMELAST") + Constants.vbCrLf;
                sLetter = sLetter + "  Coatings - " + doForm.doRS.GetFieldVal("LNK_CODINGCOORD_US%%TXT_NAMEFIRST") + " " + doForm.doRS.GetFieldVal("LNK_CODINGCOORD_US%%TXT_NAMELAST") + Constants.vbCrLf;
                // VS 06272016 TKT#1140
                sLetter = sLetter + "  Civil - " + doForm.doRS.GetFieldVal("LNK_CIVILCOORDINATORIS_US%%TXT_NAMEFIRST") + " " + doForm.doRS.GetFieldVal("LNK_CIVILCOORDINATORIS_US%%TXT_NAMELAST") + Constants.vbCrLf + Constants.vbCrLf;
                // TLD 5/15/2009 Added 2 spaces before the entries under Primaries Assigned
                // TLD 5/15/2009 Added Estimating, Technical Services and Coatings sections
                sLetter = sLetter + "Primaries Assigned: " + Constants.vbCrLf;
            }
            if (doForm.doRS.GetLinkCount("LNK_SALESPRIMARY_US") != 0)
            {
                sLetter = sLetter + "  Sales - " + doForm.doRS.GetFieldVal("LNK_SALESPRIMARY_US%%TXT_NAMEFIRST") + " " + doForm.doRS.GetFieldVal("LNK_SALESPRIMARY_US%%TXT_NAMELAST") + Constants.vbCrLf;
            }
            else
            {
                sLetter = sLetter + "  Sales - Not Assigned" + Constants.vbCrLf;

            }
            if (doForm.doRS.GetLinkCount("LNK_ESTPRIMARY_US") != 0)
            {
                sLetter = sLetter + "  Estimating - " + doForm.doRS.GetFieldVal("LNK_ESTPRIMARY_US%%TXT_NAMEFIRST") + " " + doForm.doRS.GetFieldVal("LNK_ESTPRIMARY_US%%TXT_NAMELAST") + Constants.vbCrLf;
            }
            else
            {
                sLetter = sLetter + "  Estimating - Not Assigned" + Constants.vbCrLf;
            }

            if (doForm.doRS.GetLinkCount("LNK_ENGPRIMARY_US") != 0)
            {
                sLetter = sLetter + "  Technical Services (Engineer) - " + doForm.doRS.GetFieldVal("LNK_ENGPRIMARY_US%%TXT_NAMEFIRST") + " " + doForm.doRS.GetFieldVal("LNK_ENGPRIMARY_US%%TXT_NAMELAST") + Constants.vbCrLf;

            }
            else
            {
                sLetter = sLetter + "  Technical Services (Engineer) - Not Assigned" + Constants.vbCrLf;
            }

            // TLD 8/25/2011 Changed LNK_CODINGPRIMARY_US (Mftr. Rep) to
            // LNK_MFGRep_CO
            // If doForm.doRS.GetLinkCount("LNK_CODINGPRIMARY_US") <> 0 Then
            // sLetter = sLetter & "  Coatings - " & doForm.doRS.GetFieldVal("LNK_CODINGPRIMARY_US%%TXT_NAMEFIRST") & " " & doForm.doRS.GetFieldVal("LNK_CODINGPRIMARY_US%%TXT_NAMELAST") & vbCrLf
            // Else
            // sLetter = sLetter & "  Coatings - Not Assigned" & vbCrLf
            // End If
            if (doForm.doRS.GetLinkCount("LNK_MFGREP_CO") != 0)
            {
                sLetter = sLetter + "  Coatings - " + doForm.doRS.GetFieldVal("LNK_MFGREP_CO%%TXT_CompanyName") + Constants.vbCrLf;
            }
            else
            {
                sLetter = sLetter + "  Coatings - Not Assigned" + Constants.vbCrLf;
            }
            // VS 06272016 TKT#1140
            if (doForm.doRS.GetLinkCount("LNK_CIVILPRIMARYIS_US") != 0)
            {
                sLetter = sLetter + "  Civil - " + doForm.doRS.GetFieldVal("LNK_CIVILPRIMARYIS_US%%TXT_NAMEFIRST") + " " + doForm.doRS.GetFieldVal("LNK_CIVILPRIMARYIS_US%%TXT_NAMELAST") + Constants.vbCrLf + Constants.vbCrLf;

            }
            else
            {
                sLetter = sLetter + "  Civil - Not Assigned" + Constants.vbCrLf + Constants.vbCrLf;
            }

            // VS 06272016 TKT#1140
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_CSTBOLTED", 2)) == 1)
            {
                sLetter = sLetter + "Bolted Tank - " + " Yes" + Constants.vbCrLf + Constants.vbCrLf;
            }
            else
            {
                sLetter = sLetter + "Bolted Tank - " + " No" + Constants.vbCrLf + Constants.vbCrLf;
            }

            // TLD 3/28/2014 Added more fields
            // Commercial Contacts
            if (doForm.doRS.GetLinkCount("LNK_Involves_CN") != 0)
            {
                clRowSet doCNRS = new clRowSet("CN", 3, "LNK_InvolvedIn_OP='" + doForm.doRS.GetFieldVal("GID_ID") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_NameFirst, TXT_NameLast");
                if (doCNRS.GetFirst() == 0)
                {
                    do
                    {
                        if (sContact == "")
                        {
                            sContact += doCNRS.GetFieldVal("TXT_NameFirst") + " " + doCNRS.GetFieldVal("TXT_NameLast");
                        }
                        else
                        {
                            sContact += ", " + doCNRS.GetFieldVal("TXT_NameFirst") + " " + doCNRS.GetFieldVal("TXT_NameLast");
                        }
                        if (doCNRS.GetNext() == 0)
                        {
                            break;
                        }
                    }
                    while (true);
                }
                sLetter += "Commercial Contact - " + sContact + Constants.vbCrLf + Constants.vbCrLf;
            }
            else
            {
                sLetter += "Commercial Contact - Not Assigned" + Constants.vbCrLf + Constants.vbCrLf;
            }

            // TLD 3/31/2014 Changed to new LNK_Technical_CN field
            // TLD 3/28/2014 Comment for now, there is no Engineer CN on Involves tab
            // Technical Contacts
            sContact = "";
            if (doForm.doRS.GetLinkCount("LNK_Technical_CN") != 0)
            {
                clRowSet doCNRS = new clRowSet("CN", 3, "LNK_TechnicalFor_OP='" + doForm.doRS.GetFieldVal("GID_ID") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_NameFirst, TXT_NameLast");
                if (doCNRS.GetFirst() == 0)
                {
                    do
                    {
                        if (sContact == "")
                        {
                            sContact += doCNRS.GetFieldVal("TXT_NameFirst") + " " + doCNRS.GetFieldVal("TXT_NameLast");
                        }
                        else
                        {
                            sContact += ", " + doCNRS.GetFieldVal("TXT_NameFirst") + " " + doCNRS.GetFieldVal("TXT_NameLast");
                        }
                        if (doCNRS.GetNext() == 0)
                        {
                            break;
                        }
                    }
                    while (true);
                }
                sLetter += "Technical Contact - " + sContact + Constants.vbCrLf + Constants.vbCrLf;
            }
            else
            {
                sLetter += "Technical Contact - Not Assigned" + Constants.vbCrLf + Constants.vbCrLf;


                // VS 06272016 TKT#1140
                // 'Kickoff Date
                // If doForm.doRS.GetFieldVal("DTE_PROJECTKICKOFF", 2) <> goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, , , , " ") Then
                // 'not blank, Gets Bid Date
                // sLetter &= "Kickoff Date: " & doForm.doRS.GetFieldVal("DTE_PROJECTKICKOFF", 1) & vbCrLf & vbCrLf
                // Else
                // sLetter &= "Kickoff Date: " & vbCrLf & vbCrLf
                // End If

                // Ballpark
                sLetter += "Ballpark Price: " + doForm.doRS.GetFieldVal("CUR_EstPrice") + Constants.vbCrLf + Constants.vbCrLf;

                // Quoted
                sLetter += "Quoted Price: " + doForm.doRS.GetFieldVal("CUR_ActualPrice") + Constants.vbCrLf + Constants.vbCrLf;

                // TLD 4/25/2014 Moved from below Get %
                // GM
                sLetter += "GM %: " + doForm.doRS.GetFieldVal("SR__GM") + Constants.vbCrLf + Constants.vbCrLf;

                // TLD 4/25/2014 combined Go %, Get %, Backlog Prob Value to one line
                // Go %, Get %, Backlog Prob Value
                // VS 06272016 TKT#1140
                // sLetter &= "Go %: " & doForm.doRS.GetFieldVal("SI__Margin") & ", Get %: " & doForm.doRS.GetFieldVal("SI__Probability") & ", Backlog Prob Value: " & doForm.doRS.GetFieldVal("CUR_BacklogProb") & vbCrLf & vbCrLf
                // 'Go %
                // sLetter &= "Go %: " & doForm.doRS.GetFieldVal("SI__Margin") & vbCrLf & vbCrLf

                // 'Get %
                // sLetter &= "Get %: " & doForm.doRS.GetFieldVal("SI__Probability") & vbCrLf & vbCrLf

                // 'TLD 4/25/2014 Moved to below Quoted Price
                // 'GM
                // sLetter &= "GM: " & doForm.doRS.GetFieldVal("SR__GM") & vbCrLf & vbCrLf

                // 'Backlog Prob Value
                // sLetter &= "Backlog Prob Value: " & doForm.doRS.GetFieldVal("CUR_BacklogProb") & vbCrLf & vbCrLf

                // TLD 4/23/2014 Added new Date fields
                sLetter += "On Site Date: " + doForm.doRS.GetFieldVal("DTE_OnSite", 1) + Constants.vbCrLf + Constants.vbCrLf;

                sLetter += "Completion Date: " + doForm.doRS.GetFieldVal("DTE_Completion", 1); // & vbCrLf & vbCrLf - New lines added below at "Strategy".

                // TLD 4/24/2014 Added new Date fields
                // VS 06272016 TKT#1140
                // sLetter &= "Drawing List: " & doForm.doRS.GetFieldVal("TXT_DrawingList") & vbCrLf & vbCrLf

                // sLetter &= "Spec List: " & doForm.doRS.GetFieldVal("TXT_SpecList") & vbCrLf & vbCrLf

                // sLetter &= "Reason Won/Lost: " & doForm.doRS.GetFieldVal("MLS_ReasonWonLost", 1) & vbCrLf & vbCrLf

                // 'TLD 4/25/2014 Converted "--" to ", "
                // '---------First Data Row
                // sLetter &= "*******************"
                // sLetter &= vbCrLf & "Diameter 1:  " & doForm.doRS.GetFieldVal("TXT_Dia") & ", Height 1:  " & doForm.doRS.GetFieldVal("TXT_Height") & ", Qty 1:  " & doForm.doRS.GetFieldVal("TXT_Qty") & ", Roof Type 1:  " & doForm.doRS.GetFieldVal("TXT_RoofType") & ", Material 1:  " & doForm.doRS.GetFieldVal("TXT_Material") & ", Base/Alt 1:  " & doForm.doRS.GetFieldVal("MLS_BaseAlt", 1) & vbCrLf & vbCrLf
                // '---------First Data Row

                // 'TLD 4/25/2014 Converted "--" to ", "
                // '---------Data Rows 2 - 5
                // For i As Integer = 2 To 5
                // sLetter &= "*******************"
                // sLetter &= vbCrLf & "Diameter " & i & ":  " & doForm.doRS.GetFieldVal("TXT_Dia" & i) & ", Height " & i & ":  " & doForm.doRS.GetFieldVal("TXT_Height" & i) & ", Qty " & i & ":  " & doForm.doRS.GetFieldVal("TXT_Qty" & i) & ", Roof Type " & i & ":  " & doForm.doRS.GetFieldVal("TXT_RoofType" & i) & ", Material " & i & ":  " & doForm.doRS.GetFieldVal("TXT_Material" & i) & ", Base/Alt " & i & ":  " & doForm.doRS.GetFieldVal("MLS_BaseAlt" & i, 1) & vbCrLf & vbCrLf
                // Next
                // '---------Data Rows 2 - 5

                // sLetter &= "*******************" & vbCrLf & vbCrLf


                // ''Dia
                // 'sLetter &= "Diameter: " & doForm.doRS.GetFieldVal("TXT_Dia") & vbCrLf & vbCrLf

                // ''Qty
                // 'sLetter &= "Qty: " & doForm.doRS.GetFieldVal("TXT_Qty") & vbCrLf & vbCrLf

                // ''Height
                // 'sLetter &= "Height: " & doForm.doRS.GetFieldVal("TXT_Height") & vbCrLf & vbCrLf

                // ''Roof Type
                // 'sLetter &= "Roof Type: " & doForm.doRS.GetFieldVal("TXT_RoofType") & vbCrLf & vbCrLf

                // ''TLD 4/23/2014 removed product
                // ''Product
                // ''sLetter &= "Product: " & doForm.doRS.GetFieldVal("LNK_For_PD%%TXT_ProductName") & vbCrLf & vbCrLf

                // ''Material
                // 'sLetter &= "Material: " & doForm.doRS.GetFieldVal("TXT_Material") & vbCrLf & vbCrLf

                // ''Base/Alt
                // 'sLetter &= "Base/Alt: " & doForm.doRS.GetFieldVal("MLS_BaseAlt", 1) & vbCrLf & vbCrLf

                // 'TLD 4/25/2014 Changed "--" to ", "
                // 'TLD 4/24/2014 Added fields at bottom of Details tab
                // '---------Payment Terms
                // sLetter &= "Payment Terms:  "
                // If doForm.doRS.GetFieldVal("CHK_PayBMTStd", 2) = 1 Then
                // sLetter &= "BMT Std:  " & "YES"
                // Else
                // sLetter &= "BMT Std:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_Payother", 2) = 1 Then
                // sLetter &= ", Other:  " & "YES"
                // Else
                // sLetter &= ", Other:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_PayCashFlow", 2) = 1 Then
                // sLetter &= ", Cashflow Approved:  " & "YES"
                // Else
                // sLetter &= ", Cashflow Approved:  " & "NO"
                // End If
                // sLetter &= vbCrLf & "Comments:  " & doForm.doRS.GetFieldVal("MMO_PayComments")

                // '---------Retainage
                // sLetter &= vbCrLf & vbCrLf & "Retainage:  "
                // If doForm.doRS.GetFieldVal("CHK_RetainageNone", 2) = 1 Then
                // sLetter &= "None:  " & "YES"
                // Else
                // sLetter &= "None:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_Retainage5Perc", 2) = 1 Then
                // sLetter &= ", 5%:  " & "YES"
                // Else
                // sLetter &= ", 5%:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_Retainage10Perc", 2) = 1 Then
                // sLetter &= ", 10%:  " & "YES"
                // Else
                // sLetter &= ", 10%:  " & "NO"
                // End If
                // sLetter &= vbCrLf & "Comments:  " & doForm.doRS.GetFieldVal("MMO_RetainageComments")

                // '---------Bonds
                // sLetter &= vbCrLf & vbCrLf & "Bonds:  "
                // If doForm.doRS.GetFieldVal("CHK_BondsNotReqd", 2) = 1 Then
                // sLetter &= "Not Req'd:  " & "YES"
                // Else
                // sLetter &= "Not Req'd:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_BondsRequested", 2) = 1 Then
                // sLetter &= ", Requested:  " & "YES"
                // Else
                // sLetter &= ", Requested:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_BondsIncluding", 2) = 1 Then
                // sLetter &= ", Including:  " & "YES"
                // Else
                // sLetter &= ", Including:  " & "NO"
                // End If
                // sLetter &= vbCrLf & "Comments:  " & doForm.doRS.GetFieldVal("MMO_BondsComments")

                // '---------Indemnification
                // sLetter &= vbCrLf & vbCrLf & "Indemnification:  "
                // If doForm.doRS.GetFieldVal("CHk_IndemAcceptable", 2) = 1 Then
                // sLetter &= "Acceptable:  " & "YES"
                // Else
                // sLetter &= "Acceptable:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_IndemException", 2) = 1 Then
                // sLetter &= ", Exception:  " & "YES"
                // Else
                // sLetter &= ", Exception:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_IndemNotReview", 2) = 1 Then
                // sLetter &= ", Not Reviewed:  " & "YES"
                // Else
                // sLetter &= ", Not Reviewed:  " & "NO"
                // End If
                // sLetter &= vbCrLf & "Comments:  " & doForm.doRS.GetFieldVal("MMO_IndemComments")

                // '---------Taxes
                // sLetter &= vbCrLf & vbCrLf & "Taxes:  "
                // If doForm.doRS.GetFieldVal("CHK_TaxesAcceptable", 2) = 1 Then
                // sLetter &= "Acceptable:  " & "YES"
                // Else
                // sLetter &= "Acceptable:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_TaxesException", 2) = 1 Then
                // sLetter &= ", Exception:  " & "YES"
                // Else
                // sLetter &= ", Exception:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_TaxesNotReviewed", 2) = 1 Then
                // sLetter &= ", Not Reviewed:  " & "YES"
                // Else
                // sLetter &= ", Not Reviewed:  " & "NO"
                // End If
                // sLetter &= vbCrLf & "Comments:  " & doForm.doRS.GetFieldVal("MMO_TaxesComments")

                // '---------Warranty
                // sLetter &= vbCrLf & vbCrLf & "Warranty:  "
                // If doForm.doRS.GetFieldVal("CHK_WarrantyAcceptable", 2) = 1 Then
                // sLetter &= "Acceptable:  " & "YES"
                // Else
                // sLetter &= "Acceptable:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_WarrantyException", 2) = 1 Then
                // sLetter &= ", Exception:  " & "YES"
                // Else
                // sLetter &= ", Exception:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_WarrantyNotReviewed", 2) = 1 Then
                // sLetter &= ", Not Reviewed:  " & "YES"
                // Else
                // sLetter &= ", Not Reviewed:  " & "NO"
                // End If
                // sLetter &= vbCrLf & "Comments:  " & doForm.doRS.GetFieldVal("MMO_WarrantyComments")

                // '---------Extended Warranty
                // sLetter &= vbCrLf & vbCrLf & "Extended Warranty:  "
                // If doForm.doRS.GetFieldVal("CHK_ExtWarrAcceptable", 2) = 1 Then
                // sLetter &= "Acceptable:  " & "YES"
                // Else
                // sLetter &= "Acceptable:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_ExtWarrException", 2) = 1 Then
                // sLetter &= ", Exception:  " & "YES"
                // Else
                // sLetter &= ", Exception:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_ExtWarrNotReviewed", 2) = 1 Then
                // sLetter &= ", Not Reviewed:  " & "YES"
                // Else
                // sLetter &= ", Not Reviewed:  " & "NO"
                // End If
                // sLetter &= vbCrLf & "Comments:  " & doForm.doRS.GetFieldVal("MMO_ExtWarrComments")

                // '---------Schedule
                // sLetter &= vbCrLf & vbCrLf & "Schedule:  "
                // If doForm.doRS.GetFieldVal("CHK_SchedAcceptable", 2) = 1 Then
                // sLetter &= "Acceptable:  " & "YES"
                // Else
                // sLetter &= "Acceptable:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_SchedException", 2) = 1 Then
                // sLetter &= ", Exception:  " & "YES"
                // Else
                // sLetter &= ", Exception:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_SchedNotReviewed", 2) = 1 Then
                // sLetter &= ", Not Reviewed:  " & "YES"
                // Else
                // sLetter &= ", Not Reviewed:  " & "NO"
                // End If
                // sLetter &= vbCrLf & "Comments:  " & doForm.doRS.GetFieldVal("MMO_SchedComments")

                // '---------Site Conditions
                // sLetter &= vbCrLf & vbCrLf & "Site Conditions:  "
                // If doForm.doRS.GetFieldVal("CHK_SiteCondAcceptable", 2) = 1 Then
                // sLetter &= "Acceptable:  " & "YES"
                // Else
                // sLetter &= "Acceptable:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_SitecondException", 2) = 1 Then
                // sLetter &= ", Exception:  " & "YES"
                // Else
                // sLetter &= ", Exception:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_SiteCondNotReviewed", 2) = 1 Then
                // sLetter &= ", Not Reviewed:  " & "YES"
                // Else
                // sLetter &= ", Not Reviewed:  " & "NO"
                // End If
                // sLetter &= vbCrLf & "Comments:  " & doForm.doRS.GetFieldVal("MMO_SiteCondComments")

                // '---------T&C's
                // sLetter &= vbCrLf & vbCrLf & "T&C's:  "
                // If doForm.doRS.GetFieldVal("CHK_TCAcceptable", 2) = 1 Then
                // sLetter &= "Acceptable:  " & "YES"
                // Else
                // sLetter &= "Acceptable:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_TCException", 2) = 1 Then
                // sLetter &= ", Exception:  " & "YES"
                // Else
                // sLetter &= ", Exception:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_TCNotReviewed", 2) = 1 Then
                // sLetter &= ", Not Reviewed:  " & "YES"
                // Else
                // sLetter &= ", Not Reviewed:  " & "NO"
                // End If
                // sLetter &= vbCrLf & "Comments:  " & doForm.doRS.GetFieldVal("MMO_TCComments")

                // '---------LD's
                // sLetter &= vbCrLf & vbCrLf & "T&C's:  "
                // If doForm.doRS.GetFieldVal("CHK_LDAcceptable", 2) = 1 Then
                // sLetter &= "Acceptable:  " & "YES"
                // Else
                // sLetter &= "Acceptable:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_LDException", 2) = 1 Then
                // sLetter &= ", Exception:  " & "YES"
                // Else
                // sLetter &= ", Exception:  " & "NO"
                // End If
                // If doForm.doRS.GetFieldVal("CHK_LDNotReviewed", 2) = 1 Then
                // sLetter &= ", Not Reviewed:  " & "YES"
                // Else
                // sLetter &= ", Not Reviewed:  " & "NO"
                // End If
                // sLetter &= vbCrLf & "Comments:  " & doForm.doRS.GetFieldVal("MMO_LDComments")


                sLetter += Constants.vbCrLf + Constants.vbCrLf + "Strategy Notes:  " + doForm.doRS.GetFieldVal("MMO_STRATEGY");

                // ---------Subcontractors
                sLetter += Constants.vbCrLf + Constants.vbCrLf + "Subcontractors:  Comments:  " + doForm.doRS.GetFieldVal("MMO_LDComments");


                // VS 06272016 TKT#1140
                sLetter = sLetter + Constants.vbCrLf + "--------------------"; // & vbCrLf & vbCrLf & "Notice of Bid Notes:  " & doForm.doRS.GetFieldVal("MMO_BIDNOTES") & vbCrLf & vbCrLf
            }

            // Creates new AC
            doACRS = new clRowSet("AC", 2, "", "", "", -1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 1800);

            // TLD 5/12/2009 dolink has been fixed, so modified to just use 1 dolink array
            // Set values in linked AC
            doACRS.ClearLinkAll("LNK_RELATED_CN");
            doACRS.SetLinkVal("LNK_RELATED_CN", doContacts);
            // doACRS.SetLinkVal("LNK_RELATED_CN", doContacts1)
            // doACRS.SetLinkVal("LNK_RELATED_CN", doContacts2)
            // doACRS.SetLinkVal("LNK_RELATED_CN", doContacts3)
            // doACRS.SetLinkVal("LNK_RELATED_CN", doContacts4)
            // doACRS.SetLinkVal("LNK_RELATED_CN", doContacts5)

            // Sets Letter field
            doACRS.SetFieldVal("MMO_LETTER", sLetter);

            // TLD 5/15/2009 Sets Subject field
            doACRS.SetFieldVal("TXT_SUBJ", sSubject);

            // TLD 5/16/2012 Clear vars
            sLetter = "";
            sSubject = "";
            doContacts = null/* TODO Change to default(_) if this is not a reference type */;

            // Saves new AC record
            if (doACRS.Commit() != 1)
            {
                doForm.MessageBox("Cannot create an email Activity.");
                return true;
            }

            // Sends AC to PC Link
            if (oMySend.AddSendJob("Email Activity from Opportunity: " + doForm.doRS.GetFieldVal("SYS_NAME"), Convert.ToString(doACRS.GetFieldVal("GID_ID")), "cus_OP_Text Email.txt", "CORR", "EMAIL", "AC", false, true) != "")
            {
                oSend = new clSend();
                oSend.MarkRecordAsSent(Convert.ToString(doACRS.GetFieldVal("GID_ID")), "CORR");
                goUI.ExecuteSendPopup = true;
            }
            else
            {
                doForm.MessageBox("Cannot create send job.");
                return true;
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormControlOnChange_LNK_CUSTCONTACT_CN_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/8/07 Changed time stamp to local time, no label.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/10/2009 Auto fills Customer/GC (LNK_SELECTEDGC_CO)
            doForm.doRS.ClearLinkAll("LNK_SELECTEDGC_CO");
            scriptManager.RunScript("ConnectCompanies", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sColor = "#000000";
            string sInquiryNo = "";
            string sID = doForm.GetCreateLinkedSourceRecSUID;
            string sFile = goTR.GetFileFromSUID(sID);
            string sMandatory = Convert.ToString(goP.GetVar("sMandatoryFieldColor"));

            // TLD 8/9/2010 Converted to currency
            // TLD 8/5/2010 Disable calculated fields
            // doForm.SetControlState("SR__GOPOTENTIAL", 4)
            // doForm.SetControlState("SR__BACKLOGPROB", 4)
            doForm.SetControlState("CUR_GOPOTENTIAL", 4);
            doForm.SetControlState("CUR_BACKLOGPROB", 4);

            // TLD 6/4/2009 Uncommented to NOT require these two fields
            // 'TLD 4/14/2009 Overrides main to NOT require Unit Price and Qty fields
            doForm.SetFieldProperty("SR__Qty", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("CUR_UnitValue", "LABELCOLOR", sColor);
            // TLD 8/24/2012 Set fonts color to red
            doForm.SetFieldProperty("SI__Margin", "LABELCOLOR", sMandatory); // Go%
            doForm.SetFieldProperty("SI__Probability", "LABELCOLOR", sMandatory); // Get%

            // TLD 4/14/2009 Hides Notes tab
            // Defaults to Inquiry Flow tab
            doForm.SetControlState("TAB_FORM[3]", 2);
            doForm.MoveToTab(13);

            // TLD 4/23/2014 hide Sales Process tab
            doForm.SetControlState("TAB_FORM[4]", 2);

            // TLD 4/22/2009 Call to generate Inquiry # if new Opp
            if (doForm.GetMode() == "CREATION")
            {
                if (sID != "" & sID != null & sFile == "AC")
                {
                    clRowSet doACRS = new clRowSet("AC", 3, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "TXT_INQUIRY, MLS_PURPOSE");
                    if (doACRS.GetFirst() == 1)
                    {
                        if (Convert.ToInt32(doACRS.GetFieldVal("MLS_PURPOSE", 2)) == 8)
                        {
                            doForm.doRS.SetFieldVal("TXT_INQUIRY", doACRS.GetFieldVal("TXT_INQUIRY") + "-LD");
                            // Sets variable so FormOnSave knows not to increment inquiry#
                            goP.SetVar("OP_IncrementInquiry", "0");
                        }
                    }
                }
                else
                {
                }

                // TLD 9/10/2010 Else, if TXT_Inquiry is still blank for new op, generate
                if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_INQUIRY")) == "")
                {
                    // TLD 7/22/2013 Change to call from form & record
                    scriptManager.RunScript("GenerateInquiryNo", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
                    doForm.doRS.SetFieldVal("TXT_INQUIRY", sInquiryNo);
                    // Sets variable so FormOnSave knows to increment inquiry#
                    goP.SetVar("OP_IncrementInquiry", "1");
                }
            }

            // SKO 11102016 TKT1312: When creating an Opp and inputting pre-bid date and if its mandatory, can we have a field for pre-bid time
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_PreBidMandatory", 2)) == 1)
            {
                doForm.SetControlState("TME_PREBIDMEETING", 0); // show
            }
            else
            {
                doForm.SetControlState("TME_PREBIDMEETING", 2);// hide
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormControlOnChange_CHK_PREBIDMANDATORY_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // SKO 11102016 TKT1312: When creating an Opp and inputting pre-bid date and if its mandatory, can we have a field for pre-bid time

            Form doForm = (Form)par_doCallingObject;

            // SKO 11102016 TKT1312: When creating an Opp and inputting pre-bid date and if its mandatory, can we have a field for pre-bid time
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_PreBidMandatory", 2)) == 1)
            {
                doForm.SetControlState("TME_PREBIDMEETING", 0); // show
            }
            else
            {
                doForm.SetControlState("TME_PREBIDMEETING", 2);// hide
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__QTY", "LABELCOLOR", color);


            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            long lStatusVal;
            string sCreatedDate;
            string sCloseDate;

            // TLD 6/4/2009 Prevents main from running, then modifies here to
            // NOT require Unit Price (CUR_UNITVALUE) and Qty (SR__Qty)
            goTR.StrWrite(ref par_sSections, "EnforceAndFillStatusRelatedFields", "0");

            lStatusVal = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2));

            switch (lStatusVal)
            {
                case 0        // Open
               :
                    {
                        // Make sure the Expected Close Date field is not earlier than Date Created
                        // First test whether either of the two fields is empty

                        if (goTR.IsDate(Convert.ToString(doForm.doRS.GetFieldVal("DTE_TIME", 1))) || Convert.ToString(doForm.doRS.GetFieldVal("DTE_TIME", 1)) == "")
                        {
                            doForm.doRS.SetFieldVal("DTE_TIME", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                        }
                        else
                        {
                            sCreatedDate = Convert.ToString(doForm.doRS.GetFieldVal("DTE_TIME", 2));
                        }

                        if (goTR.IsDate((doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1) == null) ? "" : doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1).ToString()) != true)
                        {
                            doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                        }
                        else
                        {
                            sCloseDate = Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2));
                        }

                        // If CloseDate < CreatedDate
                        // ***Review this line - same as above***

                        if (goTR.IsDate((doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2) == null) ? "" : doForm.doRS.GetFieldVal("DTE_TIME", 2).ToString()) != true)
                        {
                            sCloseDate = "";
                            // doForm.SetFieldVal("DTE_EXPCLOSEDATE", sCloseDate, 2)  'RAH: I commented this out as it was a little confusing and unneccessary.  
                            // Additionally, knowing what the older date is has some value

                            // doForm.MoveToTab(2)    'RAH:There is no need to move to a tab as the field is on the top of the form.
                            doForm.MoveToField("DTE_EXPCLOSEDATE");
                            goErr.SetWarning(30200, sProc, "", "The Expected Close Date can't be earlier than the Date Created.", "", "", "", "", "", "", "", "", "DTE_ExpCloseDate");
                            return false;
                        }
                        else
                        {
                        }
                        break;
                    }

                case 6       // Budget Price
         :
                    {
                        // First test whether either of the two fields is empty
                        if (goTR.IsDate((doForm.doRS.GetFieldVal("DTE_TIME", 1) == null) ? "" : doForm.doRS.GetFieldVal("DTE_TIME", 1).ToString()) != true)
                        {
                            doForm.doRS.SetFieldVal("DTE_TIME", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                        }
                        else
                        {
                            sCreatedDate = Convert.ToString(doForm.doRS.GetFieldVal("DTE_TIME", 2));
                        }

                        if (goTR.IsDate((doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1) == null) ? "" : doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1).ToString()) != true)
                        {
                            doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                        }
                        else
                        {
                            sCloseDate = Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2));
                        }
                        break;
                    }

                case 2:
                case 3   // Won, Lost
         :
                    {
                        if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_REASONWONLOST", 2)) == 0)
                        {
                            doForm.MoveToTab(2);
                            doForm.MoveToField("MLS_REASONWONLOST");
                            goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "MLS_REASONWONLOST"), "", "", "", "", "", "", "", "", "MLS_REASONWONLOST");
                            return false;
                        }

                        // Fill 'Date Won' field if empty
                        if (goTR.IsDate((doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1) == null) ? "" : doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1).ToString()) != true)
                        {
                            doForm.doRS.SetFieldVal("DTE_DATECLOSED", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                        }

                        // Fill Expected Close Date field if empty
                        if (goTR.IsDate((doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1) == null) ? "" : doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1).ToString()) != true)
                        {
                            doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                        }
                        break;
                    }

                case 1:      // On Hold
                    {
                        break;
                    }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            int iOpSeqID = 0;
            // TLD 8/24/2009 Updated to read for Product XX
            // Read for Product XX because Inquiry #s
            // should only be stored in 1 place
            // Dim sWOP As String = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS")
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "", false);
            int iNoComp = 0;
            // TLD 5/27/2011 Added var for multiple use below
            string sCurrentInquiry = Convert.ToString(doForm.doRS.GetFieldVal("TXT_INQUIRY"));
            string sNextInquiry = goTR.StrRead(sWOP, "OP_INQUIRYNO", "1", false);
            // Ensures at least a 7 digit number
            sNextInquiry = goTR.Pad(sNextInquiry, 7, "0", "L");

            if (sCurrentInquiry == sNextInquiry)
                // OP Inquiry # is equal to WOP number, need to increment WOP
                goP.SetVar("OP_IncrementInquiry", "1");

            // -------------- For Custom Inquiry #s
            // TLD 3/6/2009 For custom Inquiry #s
            // Make sure the Inquiry # does not already exist.
            // If doForm.doRS.GetFieldVal("TXT_INQUIRY") <> "" Then
            if (sCurrentInquiry != "")
            {
                // Dim doRSQuote As New clRowSet("OP", 3, "TXT_INQUIRY='" & doForm.doRS.GetFieldVal("TXT_INQUIRY") & "' AND GID_ID <> " & doForm.doRS.GetFieldVal("GID_ID") & "", , "GID_ID", , , , , , 1)
                clRowSet doRSQuote = new clRowSet("OP", 3, "TXT_INQUIRY='" + sCurrentInquiry + "' AND GID_ID <> " + doForm.doRS.GetFieldVal("GID_ID") + "", "", "", -1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 1800);
                if (doRSQuote.GetFirst() == 1)
                {
                    // TLD 5/27/2011 There is a duplicate number that does NOT match WOP next #.
                    // Probably created because multiple users created an OP, and incremented 
                    // the number manually.  
                    // Give user message with next available number
                    // Dup # but is not same as next WOP number
                    // do NOT increment WOP #
                    goP.SetVar("OP_IncrementInquiry", "");
                    doForm.MessageBox("This Inquiry Number already exists. If another user has not just saved a new opportunity, the next available number is " + sNextInquiry + ".");
                    return false;
                }
            }

            // Update OTH MD for next Inquiry # if we are saving a new OP that has a inquiry #
            // If doForm.oVar.GetVar("OP_UpdateInquiryNo_Ran") <> "1" Then
            // doForm.oVar.SetVar("OP_UpdateInquiryNo_Ran", "1")
            // If doForm.GetMode = "CREATION" And doForm.doRS.GetFieldVal("TXT_INQUIRY") <> "" Then
            if (doForm.GetMode() == "CREATION" & sCurrentInquiry != "")
            {
                // Update OTH MD for next Inquir #
                // update Inquiry Seq ID
                // Checks var on whether to update inquiry no
                // If this OP was created from a Lead, it uses the Lead Inquiry Number
                // instead of creating a new one
                if (Convert.ToString(goP.GetVar("OP_IncrementInquiry")) == "1")
                {
                    iOpSeqID = Convert.ToInt32(goTR.StrRead(sWOP, "OP_INQUIRYNO", "1", false));
                    // update Inquiry Seq ID
                    goTR.StrWrite(ref sWOP, "OP_INQUIRYNO", iOpSeqID + 1);
                    // TLD 8/24/2009 Updated to write to Product XX
                    // goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP)
                    goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "XX");
                }
            }
            // End If
            // -------------- For Custom Inquiry #s

            // TLD 4/22/2009 Calls script to Calculate # of competitors field on Sales Process
            scriptManager.RunScript("OPCalcCompetitors", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            doForm.doRS.SetFieldVal("SI__NOCOMPETITORS", iNoComp);
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormTabOnClick_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            int iNoComp = 0;

            // TLD 4/22/2009 Calls script to Calculate # of competitors field on Sales Process
            scriptManager.RunScript("OPCalcCompetitors", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, "", "", "", "", "", "");
            doForm.doRS.SetFieldVal("SI__NOCOMPETITORS", iNoComp);
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // VS 06052015 TKT#560: Including Pre-Bid, Pending Bid for CHK_Open Checked
            switch (doRS.GetFieldVal("MLS_STATUS", 2))
            {
                case 0:
                case 1:
                case 6:
                case 4:
                case 5        // Open, On Hold, Quoted, Pre-Bid, Pending Bid
               :
                    {
                        doRS.SetFieldVal("CHK_Open", 1, 2);
                        break;
                    }

                default:
                    {
                        doRS.SetFieldVal("CHK_Open", 0, 2);
                        break;
                    }
            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UNITPRICE|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UNITPRICE|SUM", 2));

                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doRS.SetFieldVal("CUR_UnitValue", curValue);
            }

            par_doCallingObject = doRS;

            return true;

        }

        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_sSalesProcess: 1 or 0 (default): if 1, use checkboxes in the Sales Process tab
            // to calculate probability %, else just calculate value and value index.
            // 2 to calc both
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/10/2009 Prevents main from running
            // Modified for custom statuses and pretty much
            // disable the calculations
            par_bRunNext = false;

            Form doForm = null;
            clRowSet doRS1 = null;

            // Check if we passed doForm or doRs to the script. We pass doRs from OP_RecordOnsave. This 
            // allows calculating value/prob on save of the form. In all other cases it is a form (clicking Calculate
            // buttons for example).
            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
            }

            // goP.TraceLine("", "", sProc)

            long lStatus;
            double rProb;
            decimal cValueFld;
            double rQty;
            double rNewValue;
            // TLD 8/5/2010 Added for new field calcs
            decimal cActualPrice;
            double rMargin;
            decimal cBallParkPrice;

            #region
            // Dim rQ03Worth As Double = 6.25  ' Leverage                      CHK_Q01
            // Dim rQ07Worth As Double = 6.25  ' Exec Buy-In             CHK_Q02
            // Dim rQ10Worth As Double = 12.5  ' CEO Contacted           CHK_Q03
            // Dim rQ20Worth As Double = 6.25  ' Front-End Buy In        CHK_Q04
            // Dim rQ30Worth As Double = 6.25  ' Influences IDd          CHK_Q05
            // Dim rQ35Worth As Double = 6.25  ' Needs Assessed          CHK_Q06
            // Dim rQ37Worth As Double = 6.25  ' Approved/Funded         CHK_Q07
            // Dim rQ40Worth As Double = 6.25  ' Competition IDd         CHK_Q08
            // Dim rQ50Worth As Double = 6.25  ' Champion Built          CHK_Q09
            // Dim rQ60Worth As Double = 6.25  ' Decision Process        CHK_Q10
            // Dim rQ65Worth As Double = 6.25  ' Timing Estimate         CHK_Q11
            // Dim rQ70Worth As Double = 6.25  ' Key Questions           CHK_Q12
            // Dim rQ75Worth As Double = 6.25  ' Present/Demo                  CHK_Q13
            // Dim rQ80Worth As Double = 6.25  ' Quote                   CHK_Q14
            // Dim rQ85Worth As Double = 6.25  ' Mark Status             CHK_Q15

            // Dim bQ03 As Boolean
            // Dim bQ07 As Boolean
            // Dim bQ10 As Boolean
            // Dim bQ20 As Boolean
            // Dim bQ30 As Boolean
            // Dim bQ35 As Boolean
            // Dim bQ37 As Boolean
            // Dim bQ40 As Boolean
            // Dim bQ50 As Boolean
            // Dim bQ60 As Boolean
            // Dim bQ65 As Boolean
            // Dim bQ70 As Boolean
            // Dim bQ75 As Boolean
            // Dim bQ80 As Boolean
            // Dim bQ85 As Boolean
            #endregion
            if (par_s2 == "doRS")
            {
                // -----      Calculate Value and Value Index

                rProb = Convert.ToDouble(doRS1.GetFieldVal("SI__PROBABILITY", 2));
                cValueFld = Convert.ToDecimal(doRS1.GetFieldVal("CUR_UNITVALUE", 2));
                // TLD 8/5/2010 Added for new fields
                cActualPrice = Convert.ToDecimal(doRS1.GetFieldVal("CUR_ACTUALPRICE", 2));
                // TLD 9/10/2010 Added for ballpark price
                cBallParkPrice = Convert.ToDecimal(doRS1.GetFieldVal("CUR_ESTPRICE", 2));
                rMargin = Convert.ToDouble(doRS1.GetFieldVal("SI__MARGIN", 2));
                if (!goTR.IsNumber(cValueFld.ToString()))
                {
                    cValueFld = 0;
                }
                rQty = Convert.ToInt32(doRS1.GetFieldVal("SR__QTY", 2));

                // CS 10/13/08: Changed to IsNumber because the qty could be set to a varying number of decimals such as 2.15 and then
                // IsNumeric returns False
                // If Not goTR.IsNumeric(rQty) Then rQty = 0
                if (!goTR.IsNumber(rQty.ToString()))
                {
                    rQty = 0;
                    rNewValue = Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty);
                    doRS1.SetFieldVal("CUR_VALUE", rNewValue, 2);
                    doRS1.SetFieldVal("CUR_VALUEINDEX", Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty) * (Math.Round(rProb, 0) / 100), 2);   // Value Index
                                                                                                                                                   // TLD 8/9/2010 Converted to currency fields
                }
                // TLD 8/5/2010 Added to calc Go Potential (SR__GOPOTENTIAL) & Backlog Prob. Value (SR__BACKLOGPROB)
                // doRS1.SetFieldVal("SR__GOPOTENTIAL", (cActualPrice * rMargin) / 100, 2)
                // doRS1.SetFieldVal("SR__BACKLOGPROB", (doRS1.GetFieldVal("SR__GOPOTENTIAL", 2) * rProb), 2)

                // TLD 9/10/2010 If Actual Price is 0, use BallPark Price
                if (cActualPrice == 0)
                {
                    doRS1.SetFieldVal("CUR_GOPOTENTIAL", (Convert.ToDouble(cBallParkPrice) * Convert.ToDouble(rMargin)) / 100, 2);
                }
                else
                {
                    doRS1.SetFieldVal("CUR_GOPOTENTIAL", (Convert.ToDouble(cActualPrice) * Convert.ToDouble(rMargin)) / 100, 2);
                }

                // TLD 8/13/2010 They decided it should be /100 not 10
                // doRS1.SetFieldVal("CUR_BACKLOGPROB", (doRS1.GetFieldVal("CUR_GOPOTENTIAL", 2) * rProb) / 10, 2)
                doRS1.SetFieldVal("CUR_BACKLOGPROB", Convert.ToDouble(doRS1.GetFieldVal("CUR_GOPOTENTIAL", 2)) * rProb / (double)100, 2);
            }
            else
            {
                // Working on a form
                // Get Probability % value from form
                rProb = Convert.ToInt32(doForm.doRS.GetFieldVal("SI__PROBABILITY", 2));
                // Calc probability and value index
                lStatus = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2));
                // goP.TraceLine("lStatus is " & lStatus, "", sProc)
                switch (lStatus)
                {
                    case 2      // Status = Won
                   :
                        {
                            // Set probability to 100%
                            rProb = 100;
                            break;
                        }

                    case 3:
                    case 7:
                    case 8:
                    case 9:    // Status = Lost, No Bid, Cancelled, Deleted

                        {
                            rProb = 0;
                            break;
                        }
                }
                // goP.TraceLine("Value of SI__PROBABILITY: '" & doForm.doRS.GetFieldVal("SI__PROBABILITY", 2) & "'", "", sProc)
                // goP.TraceLine("rProb: '" & Math.Round(rProb, 0) & "'", "", sProc)
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__PROBABILITY", 2)) != Math.Round(rProb, 0))
                // goP.TraceLine("Setting SI__PROBABILITY to '" & Math.Round(rProb, 0) & "'", "", sProc)
                {
                    doForm.doRS.SetFieldVal("SI__PROBABILITY", Math.Round(rProb, 0), 2);
                }

                // -----      Calculate Value and Value Index
                cValueFld = Convert.ToInt32(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2));
                if (!goTR.IsNumber(cValueFld.ToString()))
                {
                    cValueFld = 0;
                }
                rQty = Convert.ToInt32(doForm.doRS.GetFieldVal("SR__QTY", 2));
                // TLD 8/5/2010 Added for new fields
                cActualPrice = Convert.ToInt32(doForm.doRS.GetFieldVal("CUR_ACTUALPRICE", 2));
                // TLD 9/10/2010 Added for ballpark price
                cBallParkPrice = Convert.ToInt32(doForm.doRS.GetFieldVal("CUR_ESTPRICE", 2));
                rMargin = Convert.ToInt32(doForm.doRS.GetFieldVal("SI__MARGIN", 2));
                // CS: 10/13/08: Changed to IsNumber b/c Qty could contain decimals
                // If Not goTR.IsNumeric(rQty) Then rQty = 0
                if (!goTR.IsNumber(rQty.ToString()))
                {
                    rQty = 0;
                    rNewValue = Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty);
                    doForm.doRS.SetFieldVal("CUR_VALUE", rNewValue, 2);
                    doForm.doRS.SetFieldVal("CUR_VALUEINDEX", Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty) * (Math.Round(rProb, 0) / 100), 2);
                }                                                                                             // Value Index
                                                                                                              // TLD 8/9/2010 Converted to currncey fields
                                                                                                              // TLD 8/5/2010 Added to calc Go Potential (SR__GOPOTENTIAL) & Backlog Prob. Value (SR__BACKLOGPROB)
                                                                                                              // doForm.doRS.SetFieldVal("SR__GOPOTENTIAL", (cActualPrice * rMargin) / 100, 2)
                                                                                                              // doForm.doRS.SetFieldVal("SR__BACKLOGPROB", (doForm.doRS.GetFieldVal("SR__GOPOTENTIAL", 2) * rProb), 2)

                // TLD 9/10/2010 If Actual Price is 0, use BallPark Price
                if (cActualPrice == 0)
                {
                    doForm.doRS.SetFieldVal("CUR_GOPOTENTIAL", Convert.ToDouble(cBallParkPrice) * Convert.ToDouble(rMargin) / 100, 2);
                }
                else
                {
                    doForm.doRS.SetFieldVal("CUR_GOPOTENTIAL", Convert.ToDouble(cActualPrice) * Convert.ToDouble(rMargin) / 100, 2);
                }

                // TLD 8/13/2010 They decided it should be /100 not 10
                // doForm.doRS.SetFieldVal("CUR_BACKLOGPROB", (doForm.doRS.GetFieldVal("CUR_GOPOTENTIAL", 2) * rProb) / 10, 2)
                doForm.doRS.SetFieldVal("CUR_BACKLOGPROB", Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_GOPOTENTIAL", 2)) * Convert.ToDouble(rProb) / ((double)100), 2);
            }
            if (par_s1 == "doRS")
            {
                par_doCallingObject = doRS1;
            }
            else
            {
                par_doCallingObject = doForm;
            }
            return true;
        }

        public bool XW_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = "Script::XW_FormOnLoadREcord_Pre";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                return false;
            }

            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS");
            // Note that we did not read using the 'Product' parameter

            // Read values from MD and set in the form fields
            par_doCallingObject = doForm;
            return true;
        }

        public bool XW_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = "Script::XW_FormOnSave_PRE";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;

            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                return false;
            }

            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS");

            // Write values to MD
            // goTR.StrWrite(sWOP, "ACTIVITY_TRAVELRATE", doForm.doRS.GetFieldVal("CUR_TRAVELRATE"))
            // goTR.StrWrite(sWOP, "ACTIVITY_LABORRATE", doForm.doRS.GetFieldVal("CUR_LABORRATE"))

            goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "XX");
            // Write MD page setting Product parameter to "XX"

            doForm.CloseOnReturn = true;
            doForm.CancelSave();
            par_doCallingObject = doForm;
            return true;
        }
        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }
        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //} 
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));



            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_FOR_MO,LNK_RELATED_PD,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);

            //LineNo for mobile oppLines
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool OL_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.oVar.SetVar("OppID", doRS.GetFieldVal("LNK_IN_OP"));

            par_doCallingObject = doRS;

            return true;
        }

        public bool OL_RecordAfterDelete_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_s2 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sID = doRS.oVar.GetVar("OppID").ToString();

            if (!string.IsNullOrEmpty(sID))
            {
                clRowSet doOPLines = default(clRowSet);

                //Create a rowset of quote lines linked to the deleted quote.
                doOPLines = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo,Gid_id", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    StringBuilder sbQuery = new StringBuilder();
                    for (int i = 1; i <= doOPLines.Count(); i++)
                    {
                        double iLineno = Convert.ToDouble(doOPLines.GetFieldVal("SR__LineNo"));
                        string sGid_id = Convert.ToString(doOPLines.GetFieldVal("Gid_ID"));
                        string sTemp = "#" + goTR.Pad(i.ToString(), 6, " ", "L", true, "R");
                        sbQuery.AppendLine("Update OL Set SR__LineNo='" + i.ToString() + "',sys_name='" + sTemp + "' where Gid_id='" + sGid_id + "' AND SR__LineNo = '" + iLineno.ToString() + "'");

                        if (doOPLines.GetNext() == 0)
                        {
                            break;
                        }

                    }
                    if (!string.IsNullOrEmpty(sbQuery.ToString()))
                    {
                        bool bretval = goData.RunSQLQuery(sbQuery.ToString());
                    }

                }

                doOPLines = null;

            }

            par_doCallingObject = doRS;

            return true;
        }

        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);


            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }

        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            //double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));
            //string sMODescription = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%MMO_SPECIFICATIONS"));
            string sModelDesc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_DESCRIPTION"));
            string sModelText = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_MODELNAME"));
            //string sModelDesc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%MMO_SPECIFICATIONS"));


            //if (curUnitPrice <= 0)
            //{
            //    goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
            //    doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
            //    par_doCallingObject = doForm;
            //    return false;
            //}

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,MMO_DETAILS,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);

            rsQL.SetFieldVal("SR__Qty", dQty);
            //rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);
            //rsQL.SetFieldVal("MMO_DETAILS", sMODescription);
            rsQL.SetFieldVal("MMO_DETAILS", sModelDesc);
            rsQL.SetFieldVal("TXT_Model", sModelText);
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}

            Refresh_QouteTotal(doForm.doRS);
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");


            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote_draft.docx";
                }
                
            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote.docx";
                }
                
            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }
            if (doForm.Save(3) != 1)
            {
                goLog.SetErrorMsg("Save failed for QT PDF Generation ");
                //return false;
            }
            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                       
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", true);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }

        public bool QT_MoveQuoteLine_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            string sDirection = "";
            string sLtGt = "";
            string sFirstLast = "";
            string sMoveSortDir = "";

            string sRecID = "";
            string sQuoteID = "";
            string sSelLineNO = "";
            string sMoveLineNO = "";
            double dSelLineNO = 0;
            double dMoveLineNO = 0;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot edit Quote Lines.");
                return true;
            }

            if (par_s3.ToUpper() == "UP")
            {
                sDirection = "up";
                sLtGt = "<";
                sFirstLast = "first";
                sMoveSortDir = "desc";
            }
            else if (par_s3.ToUpper() == "DOWN")
            {
                sDirection = "down";
                sLtGt = ">";
                sFirstLast = "last";
                sMoveSortDir = "asc";
            }
            else
            {
                return false;
            }

            sQuoteID = doForm.doRS.GetFieldVal("GID_ID").ToString();
            sRecID = doForm.GetLinkSelection("LNK_Connected_QL");

            if (string.IsNullOrEmpty(sRecID))
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }

            clRowSet doQLSelRS = new clRowSet("QL", clC.SELL_EDIT, "GID_ID=" + sRecID, "", "*,**", 1, par_bBypassValidation: true);
            if (doQLSelRS.GetFirst() == 0)
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }
            sSelLineNO = doQLSelRS.GetFieldVal("SR__LineNo").ToString();
            dSelLineNO = Convert.ToDouble(doQLSelRS.GetFieldVal("SR__LineNo"));

            clRowSet doQLMoveRS = new clRowSet("QL", clC.SELL_EDIT, ("LNK_IN_QT=" + sQuoteID + " AND SR__LineNo" + sLtGt + sSelLineNO), "SR__LineNo " + sMoveSortDir, "*,**", 1, par_bBypassValidation: true);
            if (doQLMoveRS.GetFirst() == 0)
            {
                doForm.MessageBox("This is the " + sFirstLast + " Qoute Line. Please select another Qoute Line to move " + sDirection + ".");
                par_doCallingObject = doForm;
                return false;
            }
            sMoveLineNO = doQLMoveRS.GetFieldVal("SR__LineNo").ToString();
            dMoveLineNO = Convert.ToDouble(doQLMoveRS.GetFieldVal("SR__LineNo"));

            doQLSelRS.SetFieldVal("SR__LineNo", dMoveLineNO, 2);
            doQLMoveRS.SetFieldVal("SR__LineNo", dSelLineNO, 2);

            if (doQLSelRS.Commit() == 0 || doQLMoveRS.Commit() == 0)
            {
                doForm.MessageBox("Could not save the Quote Lines. Please reopen the quote and try again.");
                par_doCallingObject = doForm;
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");
            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }

            return true;

        }
        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_ENDUSER_CO", rsOP.GetFieldVal("LNK_ENGINEERING_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_DISTREP_CO", rsOP.GetFieldVal("LNK_DISTRIBUTOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECOMPLETED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }
        public bool OP_FormControlOnChange_BTN_CALCPROBABILITY_1_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_OPPTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            par_doCallingObject = doForm;
            return true;

        }
        private static void Refresh_OPPTotal(clRowSet doForm)
        {
            string sGidId = Convert.ToString(doForm.GetFieldVal("Gid_id"));

            clRowSet rsOL = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + sGidId + "' ", "LNK_IN_OP", "CUR_VALUE|SUM");

            double curTotalAmt = 0.0;

            if ((rsOL.GetFirst() == 1))
            {

                curTotalAmt = Convert.ToDouble(rsOL.GetFieldVal("CUR_VALUE|SUM", 2));


                doForm.SetFieldVal("CUR_VALUE", curTotalAmt, 2);
                //doForm.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);

            }
            else
            {
                doForm.SetFieldVal("CUR_VALUE", 0.0);
                //doForm.SetFieldVal("CUR_TOTAL", 0.0);

            }


        }
        private static void Refresh_QouteTotal(clRowSet doQuote)
        {
            string sGidId = Convert.ToString(doQuote.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_GROUPBY, "LNK_IN_QT='" + sGidId + "' ", "LNK_IN_QT", "CUR_SUBTOTAL|SUM");

            double curTotalAmt = 0.0;

            if ((rsQL.GetFirst() == 1))
            {

                curTotalAmt = Convert.ToDouble(rsQL.GetFieldVal("CUR_SUBTOTAL|SUM", 2));


                doQuote.SetFieldVal("CUR_SUBTOTAL", curTotalAmt, 2);
                doQuote.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);

            }
            else
            {
                doQuote.SetFieldVal("CUR_SUBTOTAL", 0.0);
                doQuote.SetFieldVal("CUR_TOTAL", 0.0);

            }


        }
        public bool QT_FormControlOnChange_BTN_RECALC_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_QouteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            par_doCallingObject = doForm;
            return true;

        }
    }
}

﻿    
    
CREATE PROCEDURE [dbo].[proc_overduestrategicQuotes_Segment]     
(@SegmentId varchar(200),    
@UserId Varchar(200) = '',    
@LoginId Varchar(200)='',    
@GMTTime Varchar(200)= 6,    
@GroupedData int =0    
)    
    
AS     
    
Begin    
    
 If(@SegmentId ='')    
 Begin    
     SET @SegmentId = '36343933-3238-3530-4255-312F362F3230'    
 End    
    
 --EXEC [proc_overduestrategicQuotes_Segment] @SegmentId='35643030-3932-3466-4255-312F362F3230',    
 --@UserId='',@LoginId='F0272467-3EC5-48F7-5553-987900B57A11',@GMTTime='5'    
 --select * from bu    
 --select * from us where txt_fullname like '%system%'    
 --Declare  @SegmentId Uniqueidentifier = '64303264-3339-3535-4255-312F362F3230'    
 -- Declare @LoginId Uniqueidentifier ='F0272467-3EC5-48F7-5553-987900B57A11'    
 --declare @gmttime varchar(10)='5'    
    
 --EXEC [proc_overduestrategicQuotes_Segment] @SegmentId='36343933-3238-3530-4255-312F362F3230',    
 --@UserId='',@LoginId='F0272467-3EC5-48F7-5553-987900B57A11',@GMTTime='5',@GroupedData = 1    
    
 --declare @SegmentId varchar(200)='36343933-3238-3530-4255-312F362F3230',  --@LoginId  Varchar(200) = 'F0272467-3EC5-48F7-5553-987900B57A11',  --@GMTTime  Varchar(200)='5',@GroupedData int = 1    
    
    
 DECLARE @TODAY DATETIME     
    
 SET @GMTTime = CAST(CAST(@GMTTime AS INT)+1 AS VARCHAR(100))    
 SET @TODAY = CAST(DATEADD(HH,CAST(@GMTTime AS INT),GETUTCDATE()) AS DATE)     
 set @GMTTime= abs(@GMTTime)    
    
 Declare @TodayDate DATETIME = DATEADD(HH,CAST(@GMTTime AS INT),@TODAY)    
 Declare @FiveDaysAgoDate Datetime = DATEADD(DD,-5, @TodayDate)    
    
 --select @TodayDate,@FiveDaysAgoDate    
    
    
-- Write the session data. Only @par_sTZOffset makes a difference for the query below.    
    
EXEC pInitSession    
@par_uUserID = '11111111-1111-1111-1111-111111111111',    
@par_sUserCode = 'AAAA',    
@par_sProduct = 'SA',    
@par_sTZOffset = '-300'     
    
--DECLARE @tManyLinkList TABLE(LinkName nvarchar(100))    
DECLARE @tIDTable TABLE(    
[QT_GID_ID] uniqueidentifier,    
[QT_SYS_NAME] nvarchar(300),    
[US00001_MLS_TYPE] smallint,    
[US00001_TXT_SUPERVISORNAME] nvarchar(300),    
[US00001_SYS_NAME] nvarchar(300),    
[QT_DTT_EXPCLOSEDATE] datetime,    
[QT_BI__ID] bigint)     
    
INSERT INTO @tIDTable([QT_GID_ID], [QT_SYS_NAME], [US00001_MLS_TYPE], [US00001_TXT_SUPERVISORNAME], [US00001_SYS_NAME], [QT_DTT_EXPCLOSEDATE], [QT_BI__ID])    
SELECT DISTINCT     
[QT].[GID_ID],    
[QT].[SYS_NAME],    
[US00001].[MLS_TYPE],    
[US00001].[TXT_SUPERVISORNAME],    
[US00001].[SYS_NAME],    
CASE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [QT].[DTT_EXPCLOSEDATE])), 0) WHEN '1753-01-03' THEN '1753-01-01' ELSE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [QT].[DTT_EXPCLOSEDATE]))
  
, 0) END,    
[QT].[BI__ID]    
FROM [QT]    
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]    
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [QT].[GID_RELATED_BU]    
LEFT JOIN [QL] [QL00003] ON [QL00003].[GID_IN_QT] = [QT].[GID_ID]    
LEFT JOIN [PF] [PF00004] ON [PF00004].[GID_ID] = [QL00003].[GID_RELATED_PF]    
LEFT JOIN [PC] [PC00005] ON [PC00005].[GID_ID] = [PF00004].[GID_RELATED_PC]    
LEFT JOIN [VE] [VE00006] ON [VE00006].[GID_ID] = [QL00003].[GID_FOR_VE]    
--    
LEFT JOIN [BC] [BC00007] ON [BC00007].[GID_ID] = [QT].[GID_RELATED_BC]    
LEFT JOIN [US_RELATED_BC] ON [BC00007].[GID_ID] = [US_RELATED_BC].[GID_BC]    
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US_RELATED_BC].[GID_US]    
LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [QT].[GID_CreatedBy_US]    
    
    
WHERE     
((([QT].[CHK_BUDGET] = 0 OR     
([QT].[CHK_BUDGET] IS NULL)) AND     
([QT].[MLS_STATUS] = '0' OR    
([QT].[MLS_STATUS] IS NULL)) AND     
([QT].[DTT_CREATIONTIME] < @FiveDaysAgoDate or --'2025-04-03 06:00:00.000' OR    
([QT].[DTT_CREATIONTIME] IS NULL)) AND    
[BU00002].[GID_ID] = @SegmentId AND    
((([QT].[DTT_EXPCLOSEDATE] IS NULL) OR    
[QT].[DTT_EXPCLOSEDATE] = '1753-01-02 23:59:59.000') OR     
([QT].[DTT_EXPCLOSEDATE] <= @TodayDate or ----'2025-04-08 06:00:00.000' OR    
([QT].[DTT_EXPCLOSEDATE] IS NULL)) OR     
([QT].[DTT_NEXTACTIONDATE] <= @TodayDate or --'2025-04-08 06:00:00.000' OR    
([QT].[DTT_NEXTACTIONDATE] IS NULL))) AND     
([QT].[CUR_LINETOTALOPEN] >= '20000' OR    
([QT].[CUR_LINETOTALOPEN] >= '5000' AND     
([PC00005].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR    
[QT].[MLS_TYPE] = '6' OR     
[PF00004].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR    
[VE00006].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR    
[VE00006].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR     
[VE00006].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025'))))AND    
 ([US00008].[GID_ID] = @LoginId)) AND    
 ([QT].[SI__ShareState] = 2 OR     
(([QT].[SI__ShareState] < 2 OR     
([QT].[SI__ShareState] IS NULL)) AND    
 [US00009].[GID_ID] = @LoginId))    
    
ORDER BY     
[US00001].[MLS_TYPE] ASC,     
[US00001].[TXT_SUPERVISORNAME] ASC,     
[US00001].[SYS_NAME] ASC,     
CASE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [QT].[DTT_EXPCLOSEDATE])), 0) WHEN '1753-01-03' THEN '1753-01-01' ELSE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [QT].[DTT_EXPCLOSEDATE])
)  
, 0) END DESC,     
[QT].[BI__ID] ASC    
    
     
Drop Table IF Exists #T1    
    
SELECT     
Cast('OpenLink' as Nvarchar(Max)) As 'OpenLink',    
--'<a id="OpendLinkPO" style="cursor: pointer;" class="AnchorOpen" onclick="OpenLinkClick('''+CAST(qt.GID_ID AS NVARCHAR(MAX))+''',''QT'',''VIE_8DDDC0E5-9D2D-41BC-5858-B2AC003EBA65'')"><i class="fa fa-edit" title="Edit Record"></i></a>'As 'OpenLink',    
    
--CASE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [QT].[DTT_ExpCloseDate])), 0) WHEN '1753-01-03' THEN '1753-01-01' ELSE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [QT].[DTT_ExpCloseDate]
)), 0) END AS 'DTD_ExpCloseDate',    
--CASE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [QT].[DTT_CreationTime])), 0) WHEN '1753-01-03' THEN '1753-01-01' ELSE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60 AS 'DTD_ExpCloseDate',  
  
   
    
[QT].[DTT_ExpCloseDate] AS 'DTD_ExpCloseDate',    
    
[QT].[DTT_CreationTime] AS 'DTD_CreationTime',    
[QT].[MLS_SOURCE] AS 'MLS_SOURCE',    
[QT].[TXT_SXOrderNo] AS 'TXT_SXOrderNo',    
[QT].[TXT_QUOTENO] AS 'TXT_QUOTENO',    
[CO00007].[TXT_COMPANYNAME] AS 'LNK_FOR_CO__TXT_COMPANYNAME',    
[QT].[CUR_LineTotalOpen] AS 'CUR_LineTotalOpen',    
[QT].[SI__GROSSMARGIN] AS 'SI__GROSSMARGIN',    
[QT].[CUR_GROSSPROFIT] AS 'CUR_GROSSPROFIT',    
[QT].[MLS_STATUS] AS 'MLS_STATUS',    
[BC00008].[SYS_NAME] AS 'LNK_RELATED_BC__SYS_NAME',    
[US00001].[TXT_FULLNAME] AS 'LNK_CREDITEDTO_US__TXT_FULLNAME',    
[US00009].[TXT_CODE] AS 'LNK_PEER_US__TXT_CODE',    
    
[OP00012].[SYS_NAME] AS 'LNK_RELATED_OP__SYS_NAME',    
    
--CASE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [QT].[DTT_NextActionDate])), 0) WHEN '1753-01-03' THEN '1753-01-01' ELSE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [QT].[DTT_NextActionD
ate])), 0) END AS 'DTD_NextActionDate',    
    
[QT].[DTT_NextActionDate] AS 'DTT_NextActionDate',    
    
--[QT].[MMO_NextAction] AS 'MMO_NextAction',    
--[QT].[MMO_Journal] AS 'MMO_Journal',    
    
dbo.StripHTML([QT].[MMO_NextAction]) AS 'MMO_NextAction',    
dbo.StripHTML([QT].[MMO_Journal]) AS 'MMO_Journal',    
    
[QT].[GID_ID] AS 'GEN_16',    
[US00001].[MLS_TYPE] AS 'LNK_CREDITEDTO_US__MLS_TYPE',    
[US00001].[TXT_SUPERVISORNAME] AS 'LNK_CREDITEDTO_US__TXT_SUPERVISORNAME',    
[US00001].[SYS_NAME] AS 'LNK_CREDITEDTO_US__SYS_NAME',    
[QT].[DTT_ExpCloseDate] AS 'DTT_EXPCLOSEDATE',    
[QT].[GID_ID] AS 'GID_ID',    
NewID() As ID,    
Cast(Null AS uniqueidentifier) As ParentId    
into #T1    
FROM [QT]    
JOIN @tIDTable idt ON idt.[QT_GID_ID] = [QT].[GID_ID]    
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]    
LEFT JOIN [CO] [CO00007] ON [CO00007].[GID_ID] = [QT].[GID_FOR_CO]    
LEFT JOIN [BC] [BC00008] ON [BC00008].[GID_ID] = [QT].[GID_RELATED_BC]    
LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [QT].[GID_PEER_US]    
LEFT JOIN [OP] [OP00012] ON [OP00012].[GID_ID] = [QT].[GID_RELATED_OP]    
    
ORDER BY [US00001].[MLS_TYPE] ASC, [US00001].[TXT_SUPERVISORNAME] ASC,    
[US00001].[SYS_NAME] ASC, [QT].[DTT_EXPCLOSEDATE] DESC, [QT].[BI__ID] ASC    
    
     
Drop Table IF Exists #T2    
    
SELECT    
DISTINCT STRING_AGG(isnull([CN].[TXT_FULLNAME],''),', ') AS 'LNK_TO_CN__TXT_FULLNAME',  idt.[QT_GID_ID] AS 'BASE__GID_ID'    
into #T2    
FROM [CN]    
JOIN [QT_TO_CN] ON [CN].[GID_ID] = [QT_TO_CN].[GID_CN]    
JOIN @tIDTable idt ON idt.[QT_GID_ID] = [QT_TO_CN].[GID_QT]    
GROUP BY idt.[QT_GID_ID]     
    
Drop Table IF Exists #T3    
    
Select     
 A.*,    
 B.[LNK_TO_CN__TXT_FULLNAME],    
 D.TXT_Value AS 'MLSSOURCE',     
    C.TXT_Value AS 'MLSSTATUS',     
 E.TXT_Value AS [LNKCREDITEDTO_US__MLS_TYPE]    
 Into #T3    
from #T1 A    
Left Join #T2 b ON A.GID_ID = B.[BASE__GID_ID]     
LEFT JOIN MD c ON c.TXT_Property = CAST(A.MLS_STATUS  AS nvarchar(MAX)) and c.TXT_Page = 'LST_QT:STATUS'    
LEFT JOIN MD d ON d.TXT_Property = CAST(A.MLS_SOURCE  AS nvarchar(MAX)) and d.TXT_Page = 'LST_QT:SOURCE'    
LEFT JOIN MD e ON e.TXT_Property = CAST(A.[LNK_CREDITEDTO_US__MLS_TYPE]  AS nvarchar(MAX)) and e.TXT_Page = 'LST_US:TYPE'    
    
If(@GroupedData = 0)    
Begin    
    
    
    
  Select     
   --OpenLink +isNull(CountString ,'') As OpenLink,     
   Cast('OpenLink' AS nvarchar(Max)) As OpenLink,    
  Replace(FORMAT(DTD_ExpCloseDate, 'yyyy-MM-dd'),'1753-01-02','') AS 'DTD_EXPCLOSEDATE',    
  Replace(FORMAT(DTD_CreationTime, 'yyyy-MM-dd'),'1753-01-02','') AS 'DTD_CREATIONTIME',     
  IsNull(MLSSOURCE,'') AS 'MLS_SOURCE',     
  --C.TXT_Value AS 'MLS_SOURCE',     
  IsNull(TXT_SXORDERNO,'') AS 'TXT_SXORDERNO',     
  IsNull(TXT_QUOTENO,'') AS 'TXT_QUOTENO',     
  IsNull(LNK_FOR_CO__TXT_COMPANYNAME,'') AS 'LNK_FOR_CO__TXT_COMPANYNAME',    
  --IsNull(LNK_FOR_CO__TXT_COMPANYNAME,'') AS 'GEN_6',    
  IsNull(LNK_TO_CN__TXT_FULLNAME,'') AS 'LNK_TO_CN__TXT_FULLNAME',    
 -- IsNull(LNK_TO_CN__TXT_FULLNAME,'') AS 'GEN_8',    
 --- '$'+Cast(CAST(CUR_LineTotalOpen AS DECIMAL(18,2))  as Varchar(100)) 'CUR_LINETOTALOPEN',    
 FORMAT(CUR_LineTotalOpen, 'C', 'en-US') AS CUR_LINETOTALOPEN,    
 Cast(IsNull(SI__GROSSMARGIN,'') AS nvarchar(200)) AS 'SI__GROSSMARGIN',     
  --'$'+Cast(CUR_GROSSPROFIT as Varchar(100)) 'CUR_GROSSPROFIT',    
   FORMAT(CUR_GROSSPROFIT, 'C', 'en-US') AS CUR_GROSSPROFIT,    
  IsNull(MLSSTATUS,'') AS 'MLS_STATUS',    
 -- B.TXT_Value AS 'MLS_STATUS',     
  IsNull(LNK_RELATED_BC__SYS_NAME,'') AS 'LNK_RELATED_BC__SYS_NAME',     
  --IsNull(LNK_RELATED_BC__SYS_NAME,'') AS 'GEN_14',     
  IsNull(LNK_CREDITEDTO_US__TXT_FULLNAME,'') AS 'LNK_CREDITEDTO_US__TXT_FULLNAME',     
  --IsNull(LNK_CREDITEDTO_US__TXT_FULLNAME,'') AS 'GEN_16',    
  IsNull(LNK_PEER_US__TXT_CODE,'') AS 'LNK_PEER_US__TXT_CODE',    
  ISNULL(LNK_RELATED_OP__SYS_NAME,'') AS 'LNK_RELATED_OP__SYS_NAME',    
 --IsNull(LNK_PEER_US__TXT_CODE,'') AS 'GEN_18',    
 Replace(FORMAT(DTT_NextActionDate, 'yyyy-MM-dd'),'1753-01-02','') AS 'DTD_NEXTACTIONDATE',     
  IsNull(MMO_NEXTACTION,'') AS 'MMO_NEXTACTION',     
    IsNull(MMO_Journal,'') AS 'MMO_Journal',   
  IsNull(Cast(GEN_16 as Nvarchar(200)),'') AS 'GEN_16',     
  IsNull([LNKCREDITEDTO_US__MLS_TYPE],'') AS [LNK_CREDITEDTO_US__MLS_TYPE],    
  --D.TXT_Value AS [LNK_CREDITEDTO_US__MLS_TYPE],    
  IsNull(LNK_CREDITEDTO_US__TXT_SUPERVISORNAME,'') AS 'LNK_CREDITEDTO_US__TXT_SUPERVISORNAME',     
  IsNull(LNK_CREDITEDTO_US__SYS_NAME,'') AS 'LNK_CREDITEDTO_US__SYS_NAME',     
  Replace(FORMAT(DTT_EXPCLOSEDATE, 'yyyy-MM-dd|HH:mm'),'1753-01-02|23:59','') AS 'DTT_EXPCLOSEDATE',     
  IsNull(Cast(GID_ID as Nvarchar(200)),'') AS 'GID_ID'    
  FROM #T3 Order By DTT_EXPCLOSEDATE Desc    
    
   End    
    
Else    
  Begin    
--select * from #t3    
Alter Table #t3 Alter Column GID_ID UNIQUEIDENTIFIER  null    
Alter Table #t3 Alter Column GEN_16 UNIQUEIDENTIFIER  null    
    
    
  ALTER TABLE #T3 ADD [CountString] Nvarchar(300)    
  ALTER TABLE #T3 ADD TempMLS INT    
    
  Declare @TopGID UniQUEIDENTIFIER = NEWID()    
    
Insert Into #T3 (OpenLink, Cur_LineTotalOpen, CUR_GROSSPROFIT, [LNK_TO_CN__TXT_FULLNAME], ID)    
Select     
'All ('+ Cast(Count(*) as Nvarchar(200))+') Totals:',     
Sum(cur_linetotalopen),     
Sum(CUR_GROSSPROFIT),    
'',    
@TopGID    
 From #T3     
    
    
     
    
   INSERT INTO #T3(OpenLink,CUR_LineTotalOpen,CUR_GROSSPROFIT,Id, [LNK_TO_CN__TXT_FULLNAME],ParentId,[CountString]) Select  isnull(b.TXT_Value,'(Blank)'), Sum(CUR_LineTotalOpen) as Total, Sum(CUR_GROSSPROFIT) as GrossProfit, NewID() , '', @TopGID, ' ('+C
ast(Count(*) as Nvarchar(20))+') Totals:' from #T3 a  left Join MD b ON b.TXT_Property = Cast(a.[LNK_CREDITEDTO_US__MLS_TYPE]  AS Nvarchar(50))and b.TXT_Page = 'LST_US:TYPE' Where a.GID_ID is not null Group By isnull(b.TXT_Value,'(Blank)')       
    
    
--INSERT INTO #T3(OpenLink,CUR_LineTotalOpen,CUR_GROSSPROFIT,Id, [LNK_TO_CN__TXT_FULLNAME],ParentId,[CountString])    
--  Select isnull(b.TXT_Value,'(Blank)'),Sum(CUR_LineTotalOpen) as Total ,Sum(CUR_GROSSPROFIT) as GrossProfit,NewID() ,'',@TopGID,' ('+Cast(Count(*) as Nvarchar(20))+') Totals:' from #T3 a     
--  Join MD b ON b.TXT_Property = Cast(a.[LNK_CREDITEDTO_US__MLS_TYPE]  AS Nvarchar(50))and b.TXT_Page = 'LST_US:TYPE'    
--  Group By isnull(b.TXT_Value,'(Blank)')    
    
  --Insert #T3(OpenLink,CUR_LineTotalOpen,CUR_GROSSPROFIT,Id, [LNK_TO_CN__TXT_FULLNAME],ParentId)    
  --Select IsNull(a.[LNK_CREDITEDTO_US__TXT_SUPERVISORNAME],'Blank'),Sum(a.CUR_LineTotalOpen) as Total ,Sum(a.CUR_GROSSPROFIT) as GrossProfit,NewID() ,'',c.id from #T3 a     
  --Join MD b ON b.TXT_Property = Cast(a.[LNK_CREDITEDTO_US__MLS_TYPE]  AS Nvarchar(50))and b.TXT_Page = 'LST_US:TYPE'     
  --JOIN #T3 c ON c.OpenLink = isnull(b.TXT_Value,'Blank')    
  --Group By b.TXT_Value,IsNull(a.[LNK_CREDITEDTO_US__TXT_SUPERVISORNAME],'Blank'),c.id    
    
INSERT INTO #T3(OpenLink, CUR_LineTotalOpen, CUR_GROSSPROFIT, Id, TempMLS, ParentId,[CountString])    
SELECT ISNULL(a.[LNK_CREDITEDTO_US__TXT_SUPERVISORNAME], '(Blank)') AS OpenLink, SUM(a.CUR_LineTotalOpen) AS Total, SUM(a.CUR_GROSSPROFIT) AS GrossProfit, NEWID(), CAST(a.[LNK_CREDITEDTO_US__MLS_TYPE] AS NVARCHAR(50)) AS TempMLS, c.id,' ('+Cast(Count(*) a
s Nvarchar(20))+') Totals:'    
FROM #T3 a    
LEFT JOIN MD b ON b.TXT_Property = CAST(a.[LNK_CREDITEDTO_US__MLS_TYPE] AS NVARCHAR(50)) AND b.TXT_Page = 'LST_US:TYPE'    
LEFT JOIN #T3 c ON c.OpenLink = ISNULL(b.TXT_Value, '(Blank)')    
Where a.GID_ID is not null    
GROUP BY     
b.TXT_Value,     
ISNULL(a.[LNK_CREDITEDTO_US__TXT_SUPERVISORNAME], '(Blank)'),     
CAST(a.[LNK_CREDITEDTO_US__MLS_TYPE] AS NVARCHAR(50)),     
c.id    
    
    
  --Insert #T3(OpenLink,CUR_LineTotalOpen,CUR_GROSSPROFIT,Id, [LNK_TO_CN__TXT_FULLNAME],ParentId)    
  -- Select Distinct isnull(a.[LNK_CREDITEDTO_US__SYS_NAME],'Blank') AS OpenLink,Sum(a.CUR_LineTotalOpen) as Total ,Sum(a.CUR_GROSSPROFIT) as GrossProfit,NewID() ,'',d.id from #T3 a     
  --Join MD b ON b.TXT_Property = Cast(a.[LNK_CREDITEDTO_US__MLS_TYPE]  AS Nvarchar(50))and b.TXT_Page = 'LST_US:TYPE'      
  --JOIN #T3 c ON c.OpenLink = b.TXT_Value    
  --JOIN #T3 d On d.OpenLink = IsNull(a.[LNK_CREDITEDTO_US__TXT_SUPERVISORNAME],'Blank')    
  --Group By IsNull(a.[LNK_CREDITEDTO_US__TXT_SUPERVISORNAME],'Blank'),isnull(a.[LNK_CREDITEDTO_US__SYS_NAME],'Blank'),d.id order By OpenLink    
    
  INSERT INTO #T3(OpenLink, CUR_LineTotalOpen, CUR_GROSSPROFIT, Id, [LNK_TO_CN__TXT_FULLNAME], ParentId,[CountString])    
SELECT DISTINCT ISNULL(a.[LNK_CREDITEDTO_US__SYS_NAME], '(Blank)') AS OpenLink,SUM(a.CUR_LineTotalOpen) AS Total, SUM(a.CUR_GROSSPROFIT) AS GrossProfit, NEWID(), '', d.id,' ('+Cast(Count(*) as Nvarchar(20))+') Total: Total:'    
FROM #T3 a    
Left JOIN MD b ON b.TXT_Property = CAST(a.[LNK_CREDITEDTO_US__MLS_TYPE] AS NVARCHAR(50)) AND b.TXT_Page = 'LST_US:TYPE'    
Left JOIN #T3 c ON c.OpenLink = b.TXT_Value    
Left JOIN #T3 d ON d.OpenLink = ISNULL(a.[LNK_CREDITEDTO_US__TXT_SUPERVISORNAME], '(Blank)') AND d.TempMLS = CAST(a.[LNK_CREDITEDTO_US__MLS_TYPE] AS NVARCHAR(50))    
Where a.GID_ID is not null    
GROUP BY ISNULL(a.[LNK_CREDITEDTO_US__TXT_SUPERVISORNAME], '(Blank)'),ISNULL(a.[LNK_CREDITEDTO_US__SYS_NAME], '(Blank)'), d.id    
    
    
  --Select  a.ParentId,b.ID,a.[LNK_CREDITEDTO_US__SYS_NAME]    
  Update a SET a.ParentId = b.id    
   From #T3 a     
  JOIN  #T3 b On b.OpenLink = isnull(a.[LNK_CREDITEDTO_US__SYS_NAME] ,'(Blank)')    
   Where a.ParentId is null and a.openLink Not Like 'ALL%'    
    
       
 --select distinct OpenLink from #t3 order by OpenLink     
    
    
--   SELECT  *    
--FROM #T3;    
    
    
  Select     
   OpenLink +isNull(CountString ,'') As OpenLink,     
  Replace(FORMAT(DTD_ExpCloseDate, 'yyyy-MM-dd'),'1753-01-02','') AS 'DTD_EXPCLOSEDATE',    
  Replace(FORMAT(DTD_CreationTime, 'yyyy-MM-dd'),'1753-01-02','') AS 'DTD_CREATIONTIME',     
  IsNull(MLSSOURCE,'') AS 'MLS_SOURCE',     
  --C.TXT_Value AS 'MLS_SOURCE',     
  IsNull(TXT_SXORDERNO,'') AS 'TXT_SXORDERNO',     
  IsNull(TXT_QUOTENO,'') AS 'TXT_QUOTENO',     
  IsNull(LNK_FOR_CO__TXT_COMPANYNAME,'') AS 'LNK_FOR_CO__TXT_COMPANYNAME',    
  IsNull(LNK_FOR_CO__TXT_COMPANYNAME,'') AS 'GEN_6',    
  IsNull(LNK_TO_CN__TXT_FULLNAME,'') AS 'LNK_TO_CN__TXT_FULLNAME',    
  IsNull(LNK_TO_CN__TXT_FULLNAME,'') AS 'GEN_8',    
  '$'+Cast(CAST(CUR_LineTotalOpen AS DECIMAL(18,2))  as Varchar(100)) 'CUR_LINETOTALOPEN',     
 IsNull(Cast(SI__GROSSMARGIN AS nvarchar(300)),'') AS 'SI__GROSSMARGIN',     
  '$'+Cast(CUR_GROSSPROFIT as Varchar(100)) 'CUR_GROSSPROFIT',     
  IsNull(MLSSTATUS,'') AS 'MLS_STATUS',    
 -- B.TXT_Value AS 'MLS_STATUS',     
  IsNull(LNK_RELATED_BC__SYS_NAME,'') AS 'LNK_RELATED_BC__SYS_NAME',     
  IsNull(LNK_RELATED_BC__SYS_NAME,'') AS 'GEN_14',     
  IsNull(LNK_CREDITEDTO_US__TXT_FULLNAME,'') AS 'LNK_CREDITEDTO_US__TXT_FULLNAME',     
  IsNull(LNK_CREDITEDTO_US__TXT_FULLNAME,'') AS 'GEN_16',    
  IsNull(LNK_PEER_US__TXT_CODE,'') AS 'LNK_PEER_US__TXT_CODE',     
 IsNull(LNK_PEER_US__TXT_CODE,'') AS 'GEN_18',    
 Replace(FORMAT(DTT_NextActionDate, 'yyyy-MM-dd'),'1753-01-02','') AS 'DTD_NEXTACTIONDATE',     
  IsNull(MMO_NEXTACTION,'') AS 'MMO_NEXTACTION',   
    IsNull(MMO_Journal,'') AS 'MMO_Journal',   
    
  IsNull(Cast(GEN_16 as Nvarchar(200)),'') AS 'GEN_21',     
  IsNull([LNKCREDITEDTO_US__MLS_TYPE],'') AS [LNK_CREDITEDTO_US__MLS_TYPE],    
  --D.TXT_Value AS [LNK_CREDITEDTO_US__MLS_TYPE],    
  IsNull(LNK_CREDITEDTO_US__TXT_SUPERVISORNAME,'') AS 'LNK_CREDITEDTO_US__TXT_SUPERVISORNAME',     
  IsNull(LNK_CREDITEDTO_US__SYS_NAME,'') AS 'LNK_CREDITEDTO_US__SYS_NAME',     
  Replace(FORMAT(DTT_EXPCLOSEDATE, 'yyyy-MM-dd|HH:mm'),'1753-01-02|23:59','') AS 'DTT_EXPCLOSEDATE',     
  IsNull(Cast(GID_ID as Nvarchar(200)),'') AS 'GID_ID',     
  id,     
  parentid    
  FROM #T3 Order By DTT_EXPCLOSEDATE Desc    
  --LEFT JOIN MD B ON B.TXT_Property = CAST(A.MLS_STATUS  AS nvarchar(MAX))    
  --LEFT JOIN MD C ON C.TXT_Property = CAST(A.MLS_SOURCE  AS nvarchar(MAX))    
  --LEFT JOIN MD D ON D.TXT_Property = CAST(A.[LNK_CREDITEDTO_US__MLS_TYPE]  AS nvarchar(MAX))    
  --WHERE B.TXT_Page = 'LST_QT:STATUS' AND C.TXT_Page = 'LST_QT:SOURCE' AND   D.TXT_Page = 'LST_US:TYPE'    
    
  END    
    
    
    
  END    
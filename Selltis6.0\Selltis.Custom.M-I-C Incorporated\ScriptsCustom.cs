﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;
        //private clScrMng goScr;
        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();

        }
        public ScriptsCustom()
        {
            Initialize();
        }


        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }

        public bool AutoModelDuplicate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 2/7/2014 Duplicates selected
            par_bRunNext = false;
            // MI 4/25/07 CREATED BY MI 4/22/07
            // PURPOSE:
            // Duplicate an existing Model allowing the user to connect a different
            // Contact, Company, etc.
            // Duplicates from Base Products desktop, Tasks menu or MO form

            string sID;
            clRowSet doRowset= default(clRowSet); ;
            string sFileName;
            Form doNewForm = default(Form); // new dup form
            string sOrigModelName;
            string sOrigModelID;
            string sObjectPassed;
            Form doObject = (Form)par_doCallingObject;

            // Sets var to "NOTHING" if called from Tasks menu
            if (par_doCallingObject == null)
            {
                sObjectPassed = "Nothing";
            }
            else
            // Converts to string for use below in how to open new form
            {
                sObjectPassed = par_doCallingObject.GetType().ToString();
            }

            // Check selected record
            sID = goUI.GetLastSelected("SELECTEDRECORDID");
            // goP.TraceLine("sID: " & sID & "'", "", sProc)
            sFileName = Strings.UCase(goTR.GetFileFromSUID(sID));
            if (sFileName != "MO")
            {
                goUI.NewWorkareaMessage("Please select a Model first.",0,"Selltis","","","","","","","",ref par_doCallingObject,null,"","","","","");
                return true;
            }

            // Check if have permissions
            if (goData.GetAddPermission("MO") == false)
            {
                goUI.NewWorkareaMessage("You cannot duplicate the selected Model because you don't have permissions to create Models.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                return true;
            }

            // TLD 7/2/2014 Only copy direct fields
            // as it causes disconnect between single-select
            // QL --> MO
            // Get doRowset of current record
            doRowset = new clRowSet(sFileName, 3, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "**", 1);
            if (doRowset.Count() < 1)
            {
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user, or may be a new model that has not yet been saved. Select a different record and start again or save the new model before duplicating.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                return true;
            }
            else
            {
                sOrigModelName = Convert.ToString(doRowset.GetFieldVal("SYS_Name"));
                sOrigModelID = Convert.ToString(doRowset.GetFieldVal("GID_ID"));
            }

            // Create the new Model form
            doNewForm = new Form(sFileName, "", "CRU_" + sFileName);

            // 'TLD 7/2/2014 Can't copy as it copies
            // 'the single-select link from QL --> MO
            // 'and disconnects the original MO from the QL
            // 'Copy this model to the new form's rowset
            // If Not goData.CopyRecord(doRowset, doNewForm.doRS) Then
            // goErr.SetError(35000, sProc, "Copying the selected Model '" & sID & "' failed.")
            // Return False
            // End If

            // Prepend TXT_ModelName with "*New "
            doNewForm.doRS.SetFieldVal("TXT_ModelName", "*New " + doRowset.GetFieldVal("TXT_ModelName"));
            doNewForm.doRS.SetFieldVal("LNK_CreatedBy_US", goP.GetMe("ID"));
            doNewForm.doRS.SetFieldVal("CHK_ActiveField", doRowset.GetFieldVal("CHK_ActiveField", 2), 2);
            doNewForm.doRS.SetFieldVal("TXT_UnitText", doRowset.GetFieldVal("TXT_UnitText"));
            doNewForm.doRS.SetFieldVal("LNK_Of_PD", doRowset.GetFieldVal("LNK_Of_PD", 2), 2);
            doNewForm.doRS.SetFieldVal("TXT_Description", doRowset.GetFieldVal("TXT_Description"));
            doNewForm.doRS.SetFieldVal("CUR_PricePurch", doRowset.GetFieldVal("CUR_PricePurch", 2), 2);
            doNewForm.doRS.SetFieldVal("LNK_Related_VE", doRowset.GetFieldVal("LNK_Related_VE", 2), 2);
            doNewForm.doRS.SetFieldVal("CUR_Cost", doRowset.GetFieldVal("CUR_Cost", 2), 2);
            doNewForm.doRS.SetFieldVal("TXT_ManufCode", doRowset.GetFieldVal("TXT_ManufCode"));
            doNewForm.doRS.SetFieldVal("CUR_PriceRetail", doRowset.GetFieldVal("CUR_PriceRetail", 2), 2);
            doNewForm.doRS.SetFieldVal("CHK_Taxable", doRowset.GetFieldVal("CHK_Taxable", 2), 2);
            doNewForm.doRS.SetFieldVal("CHK_Obsolete", doRowset.GetFieldVal("CHK_Obsolete", 2), 2);
            doNewForm.doRS.SetFieldVal("CUR_Price", doRowset.GetFieldVal("CUR_Price", 2), 2);
            doNewForm.doRS.SetFieldVal("LI__SKUNo", doRowset.GetFieldVal("LI__SKUNo", 2), 2);
            doNewForm.doRS.SetFieldVal("SR__Weight", doRowset.GetFieldVal("SR__Weight", 2), 2);
            doNewForm.doRS.SetFieldVal("SR__Margin", doRowset.GetFieldVal("SR__Margin", 2), 2);
            doNewForm.doRS.SetFieldVal("TXT_Attribute1", doRowset.GetFieldVal("TXT_Attribute1"));
            doNewForm.doRS.SetFieldVal("TXT_Attribute2", doRowset.GetFieldVal("TXT_Attribute2"));
            doNewForm.doRS.SetFieldVal("SR__DiscFromRetail", doRowset.GetFieldVal("SR__DiscFromRetail", 2), 2);
            doNewForm.doRS.SetFieldVal("MMO_Specifications", doRowset.GetFieldVal("MMO_Specifications"));
            doNewForm.doRS.SetFieldVal("MMO_Notes", doRowset.GetFieldVal("MMO_Notes"));
            doNewForm.doRS.SetFieldVal("FIL_Attachments", doRowset.GetFieldVal("FIL_Attachments"));
            doNewForm.doRS.SetFieldVal("URL_URLs", doRowset.GetFieldVal("URL_URLs"));
            doNewForm.doRS.SetFieldVal("FIL_Inclusions", doRowset.GetFieldVal("FIL_Inclusions"));
            doNewForm.doRS.SetFieldVal("LNK_Competing_VE", doRowset.GetFieldVal("LNK_Competing_VE", 2), 2);
            doNewForm.doRS.SetFieldVal("MMO_CompetitionNotes", doRowset.GetFieldVal("MMO_CompetitionNotes"));
            doNewForm.doRS.SetFieldVal("LNK_ComponentOf_MO", doRowset.GetFieldVal("LNK_ComponentOf_MO", 2), 2);
            doNewForm.doRS.SetFieldVal("LNK_GroupFor_MO", doRowset.GetFieldVal("LNK_GroupFor_MO", 2), 2);

            // TLD 2/7/2014 should ONLY be form........
            // Checks to see what called this script
            switch (sObjectPassed)
            {
                case "Nothing" // Tasks menu
               :
                    {
                        // do nothing
                        doNewForm.MessagePanel("This is a duplicate of the Model '" + sOrigModelName + "'." + Constants.vbCrLf + "Fill out the form and click Save.", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Info.gif");
                        goUI.Queue("FORM", doNewForm);
                        break;
                    }

                case "clForm" // MO form
         :
                    {
                        // Close original model without saving original
                        doObject.CloseOnReturn = true;
                        doNewForm.MessagePanel("This is a duplicate of the Model '" + sOrigModelName + "'." + Constants.vbCrLf + "Fill out the form and click Save.", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Info.gif");
                        goUI.Queue("FORM", doNewForm);
                        break;
                    }

                case "clDesktop" // Base Products desktop
         :
                    {
                        doNewForm.MessagePanel("This is a duplicate of the Model '" + sOrigModelName + "'." + Constants.vbCrLf + "Fill out the form and click Save.", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Info.gif");
                        goUI.Queue("FORM", doNewForm);
                        //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
                        goUI.Queue("", "");
                        break;
                    }
            }

            // Clean up objects
            doRowset = null/* TODO Change to default(_) if this is not a reference type */;
            par_doCallingObject = doObject;
            return true;
        }

        //public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn , ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "0", string par_s4 = "0", string par_s5 = "0")
        //{
        //    // par_doCallingObject: Optional: editable clRowset of the Quote header record. If passed, 
        //    // this rowset is updated and the record on disk is not touched. If not passed,
        //    // the record on disk is updated. in this case par_s1 (Quote ID) is mandatory.
        //    // par_doArray: Unused.
        //    // par_s1: GID_ID of the Quote to calculate. Mandatory even when par_doCallingObject is passed.
        //    // par_s2: Optional: ID of the Quote Line for which to take amounts from par_s3 and par_s4
        //    // instead of from disk. This is to allow recalculating the quote from a Quote Line
        //    // which hasn't been saved yet. If "", all quote lines are read from disk and par_s3 and par_s4
        //    // are ignored.
        //    // par_s3: Mandatory only if par_s2 <> "". CHK_TAXABLE value (1 or 0) from Quote Line (ID passed via par_s2). 
        //    // Use goTr.CheckboxToString(doRS.GetFieldVal("CHK_TAXABLE", 2)) to get it in this format.
        //    // par_s4: mandatory only is par_s2 <> "". CUR_SUBTOTAL as unformatted number from Quote Line (ID passed via par_s2).
        //    // Use goTr.CurrToString(doRS.GetFieldVal("CUR_SUBTOTAL", 2)) to get it in this format.
        //    // par_s5: Unused.

        //    // Old NGP parameters:
        //    // par_s1: ID of the Quote. If blank, all parameters are read from global variables named QUOTE_<FieldName>
        //    // and the Quote is saved on disk.
        //    // par_s2: System-format value of Quote's CHK_INCLUDETAXCHARGES as boolean: 1 (default) or 0.
        //    // par_s3: System-format value of Quote's SR__SALESTAXPERCENT as single real. Default is '0'.
        //    // par_s4: System-format value of Quote's CUR_OTHERCHARGE as currency. Default is '0'.
        //    // par_s5: System-format value of Quote's CUR_SHIPPING as currency. Default is '0'.
        //    // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        //    // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        //    // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        //    string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    // TLD 1/28/2014 Added
        //    par_bRunNext = false;
        //    // MI 2/23/07 Mods
        //    // PURPOSE:
        //    // Calculate totals of a Quote from Quote Lines independent of the form context.
        //    // RETURNS:
        //    // True if successful, False if not with SetError. Returns calculation results
        //    // via gop:SetVar() in the following variables:
        //    // CUR_SUBTOTAL
        //    // CUR_SUBTOTALT
        //    // CUR_SALESTAX
        //    // CUR_TOTAL	

        //    clRowSet doLines;   // 
        //    int lI;
        //    decimal cWork= default(decimal);        // Non-taxable subtotals
        //    decimal cWorkT = default(decimal);       // Taxable subtotals only
        //    double rTaxPercent;
        //    decimal cTaxAmount = default(decimal);
        //    decimal cOtherCharge = default(decimal);
        //    decimal cTotalAmount = default(decimal);
        //    decimal cShipAmount = default(decimal);
        //    // Dim s1 As String = par_s1   'Quote GID_ID
        //    // Dim s2 As String = par_s2   'Quote Line GID_ID 
        //    // Dim s3 As String = par_s3
        //    // Dim s4 As String = par_s4
        //    // Dim s5 As String = par_s5
        //    clRowSet doRS = (clRowSet)par_doCallingObject;      // Quote rowset
        //    bool bCommit = false;
        //    bool bQLTaxable= true;
        //    decimal cQLSubtotal = default(decimal);
        //    bool bQLFound = false;
        //    string sFiles = ""; // FIL_INCLUSIONS from Quote Line Models
        //    bool bUpdInclusions = false;
        //    // TLD 1/28/2014 Added
        //    decimal cCost = default(decimal); // SUM QL.CUR_TotalCost

        //    // Vars are:
        //    // s1 = QT GID_ID
        //    // s2 = QL GID_ID
        //    // s3 = QL CHK_TAXABLE
        //    // s4 = QL CUR_Subtotal

        //    // Vars used to be:
        //    // s1 = goP.GetVar("QUOTE_GID_ID")
        //    // s2 = goP.GetVar("QUOTE_CHK_INCLUDETAXCHARGES")
        //    // s3 = goP.GetVar("QUOTE_SR__SALESTAXPERCENT")
        //    // s4 = goP.GetVar("QUOTE_CUR_OTHERCHARGE")
        //    // s5 = goP.GetVar("QUOTE_CUR_SHIPPING")
        //    // CS Debug
        //    // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)



        //    // ------------- Validate parameters ----------------
        //    if (!goData.IsFileValid(goTR.GetFileFromSUID(par_s1.ToString())))
        //    {
        //        goErr.SetError(10100, sProc, null/* Conversion error: Set to default value for this argument */, goTR.GetFileFromSUID(par_s1), "File extracted from SUID in par_s1: '" + par_s1 + "'. Be sure to pass the GID_ID value of the Quote to recalculate.");
        //        // 10100: Invalid file name '[1]'. [2]
        //        return false;
        //    }
        //    if (par_s2.ToString() == "")
        //    {
        //        // Override QL ID not provided - ignore the rest of QL parameters
        //        par_s3 = "";
        //        par_s4 = "";
        //    }
        //    else
        //    {
        //        // Quote Line GID_ID was passed
        //        // QL's CHK_Taxable value
        //        if (par_s3.ToString() == "")
        //        {
        //            goErr.SetError(35000, sProc, "par_s3 is blank. QL's CHK_Taxable value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
        //            return false;
        //        }
        //        else
        //            bQLTaxable = goTR.StringToCheckbox(par_s3,false,ref par_iValid);
        //        // QL's CUR_Subtotal value
        //        if (par_s4.ToString() == "")
        //        {
        //            goErr.SetError(35000, sProc, "par_s4 is blank. QL's CUR_Subtotal value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
        //            return false;
        //        }
        //        else
        //            cQLSubtotal =Convert.ToDecimal( par_s4);
        //    }


        //    // -------------- Read Lines and calculate their amounts ------------
        //    // TLD 3/17/2014 Added CUR_TotalTocst
        //    // TLD 1/28/2014 Added CUR_Cost
        //    // CS 12/2/08: Check if MO.FIL_INCLUSIONS exists. If so need to get it in the QL RS below
        //    if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
        //    // doLines = New clRowSet("QL", 3, "LNK_IN_QT='" & par_s1.ToString & "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS")
        //    {
        //        doLines = new clRowSet("QL", 3, "LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS, CUR_Cost, CUR_TotalCost");

        //    }
        //    else
        //        // doLines = New clRowSet("QL", 3, "LNK_IN_QT='" & par_s1.ToString & "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL")
        //        doLines = new clRowSet("QL", 3, "LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, CUR_Cost, CUR_TotalCost");

            
        //    // Browse through the rowset
        //    lI = 1;
        //    if (Convert.ToUInt32(doLines.GetFirst()) == 1)
        //    {
        //        do
        //        {
        //            // Add up Quote Lines. Skip the one for which GID_ID is passed via par_s2
        //            if (par_s2.ToString() == "" | Strings.UCase(par_s2) !=Strings.UCase(Convert.ToString(doLines.GetFieldVal("GID_ID", 1))))
        //            {
        //                if (Convert.ToInt32(doLines.GetFieldVal("CHK_TAXABLE", 2)) == 1)
        //                {
        //                    cWorkT += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));
        //                }
        //                else
        //                {
        //                    cWork += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));
        //                }
        //                // TLD 3/17/2014 Added CUR_TotalCost to calc CUR_Cost * SR__Qty
        //                // TLD 1/28/2014 Added for cost, regardless of tax?
        //                // cCost += doLines.GetfieldVal("CUR_Cost", 2)
        //                cCost += Convert.ToDecimal(doLines.GetFieldVal("CUR_TotalCost", 2));
        //                // CS 12/2/08: Get value from QL%%MO FIL_INCLUSIONS field
        //                if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
        //                {
        //                    // Check if the file has already been added
        //                    if (goTR.Position(sFiles, Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"))) == 0)
        //                    {
        //                        // If this is the first file don't add a vbcrlf in front of it
        //                        if (sFiles == "")
        //                        {
        //                            sFiles = Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"));
        //                        }
        //                        else
        //                        {
        //                            sFiles = sFiles + Constants.vbCrLf + doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS");
        //                        }
        //                    }
        //                }
        //            }

        //            if (doLines.GetNext() == 0)
        //                break;
        //            lI += 1;
        //        }
        //        while (true);
        //    }
        //    // delete(doLines)
        //    doLines = null;

        //    // Add the Quote Line passed via parameters
        //    if (par_s2 != "")
        //    {
        //        // CS 12/31/08: Get Fil_Inclusions of QL passed via parameter
        //        // This code needs to be run if you open a QL directly from
        //        // a QL view and it has file inclusions
        //        // CS 1/8/09: Check if FIL_INCLUSIONS exist in db first.
        //        if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
        //        {
        //            doLines = new clRowSet("QL", 3, "GID_ID='" + par_s2.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS");
        //            if (doLines.GetFirst()==1)
        //            {
        //                // If goData.IsFieldValid("MO", "FIL_INCLUSIONS") Then
        //                // Check if the file has already been added
        //                if (goTR.Position(sFiles, Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"))) == 0)
        //                {
        //                    // If this is the first file don't add a vbcrlf in front of it
        //                    if (sFiles == "")
        //                    {
        //                        sFiles = Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"));
        //                    }
        //                    else
        //                    {
        //                        sFiles = sFiles + Constants.vbCrLf + doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS");
        //                    }
        //                }
        //            }
        //        }

        //        if (bQLTaxable == true)
        //        {
        //            cWorkT += cQLSubtotal;
        //        }
        //        else
        //        {
        //            cWork += cQLSubtotal;
        //        }
        //    }

        //    // Subtotal = cWork + cWorkT
        //    // SubtotalT = cWorkT

        //    // ---------- Pull up the Quote -----------
        //    if (doRS == null)
        //    {
        //        // Get the quote from disk
        //        bCommit = true;
        //        // doRS = New clRowSet("QT", 1, _
        //        // "GID_ID='" & par_s1 & "'", _
        //        // "DTT_TIME ASC", _
        //        // "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL")
        //        // CS 7/26/07: Currently if you open a QT that has a required field missing
        //        // such as NA date, edit a QL and save the QL you get an error. Trying to avoid
        //        // that by bypassing validation.
        //        doRS = new clRowSet("QT", 1, "GID_ID='" + par_s1 + "'", "DTT_TIME ASC", "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL",-1,"","","","","",false,false,false,false,-1,"",false,false,1800);
        //    }
        //    else
        //        // Validate the passed Rowset object
        //        if (Strings.UCase(doRS.GetFileName()) != "QT")
        //    {
        //        goErr.SetError(35000, sProc, "The file of the rowset in par_doCallingObject parameter is not QT (Quote). Either pass a Quote rowset or only pass the Quote GID_ID in par_s1 parameter.");
        //        return false;
        //    }

        //    // ----------- Read Quote data and calculate -------------
        //    if (doRS.GetFirst()==1)
        //    {
        //        // CS 12/2/08: Get the value of the 'Do not update inclusions' on QT
        //        // If checked, do not update FIL_INCLUSIONS field on QT
        //        if (goData.IsFieldValid("QT", "CHK_NOUPDINCLUSIONS"))
        //        {
        //            if (Convert.ToInt32(doRS.GetFieldVal("CHK_NOUPDINCLUSIONS", 2)) == 0)
        //            {
        //                bUpdInclusions = true;
        //            }
        //        }
        //        rTaxPercent = Convert.ToDouble(doRS.GetFieldVal("SR__SALESTAXPERCENT", clC.SELL_SYSTEM));    // s3
        //        cTaxAmount = cWorkT * Convert.ToDecimal(rTaxPercent) / 100;     // cTaxAmount goes into CUR_SALESTAX
        //                                                     // If the 'Include Tax/Charges' check-box is not checked, do not add tax,
        //                                                     // other charge and shipping to Total. 
        //        if (Convert.ToDecimal(doRS.GetFieldVal("CHK_INCLUDETAXCHARGES", clC.SELL_SYSTEM)) == 1)
        //        {
        //            cOtherCharge = Convert.ToDecimal(doRS.GetFieldVal("CUR_OTHERCHARGE", clC.SELL_SYSTEM));   // s4
        //            cShipAmount = Convert.ToDecimal(doRS.GetFieldVal("CUR_SHIPPING", clC.SELL_SYSTEM));   // s5
        //                                                                               // cTotalAmount goes to CUR_TOTAL
        //            cTotalAmount = cWork + cWorkT + cTaxAmount + cOtherCharge + cShipAmount;
        //        }
        //        else
        //        // cTotalAmount goes to CUR_TOTAL
        //        {
        //            cTotalAmount = cWork + cWorkT;
        //        }
        //    }
        //    else
        //    {
        //        // goP.TraceLine("doRS GetFirst not found", "", sProc)
        //        doRS = null/* TODO Change to default(_) if this is not a reference type */;
        //        goErr.SetError(30032, sProc, "", "Quote");
        //        // The linked [1] can't be updated because it can't be found. 
        //        // goP.TraceLine("Return False", "", sProc)
        //        return false;
        //    }

        //    // --------------- Update calculated fields ----------------
        //    doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cWork + cWorkT), clC.SELL_SYSTEM);
        //    doRS.SetFieldVal("CUR_SUBTOTALT", goTR.RoundCurr(cWorkT), clC.SELL_SYSTEM);
        //    doRS.SetFieldVal("CUR_SALESTAX", goTR.RoundCurr(cTaxAmount), clC.SELL_SYSTEM);
        //    doRS.SetFieldVal("CUR_TOTAL", goTR.RoundCurr(cTotalAmount), clC.SELL_SYSTEM);
        //    // TLD 2/5/2014 Added for sum of QL.CUR_Cost
        //    doRS.SetFieldVal("CUR_MCost", goTR.RoundCurr(cCost), 2);

        //    // TLD 2/7/2014 Subtract result from 1
        //    // TLD 1/28/2014 Added for SR__GM_Perc
        //    if (cWork != 0 | cWorkT != 0)
        //    // doRS.SetFieldVal("SR__GMPerc", (cCost / (cWork + cWorkT)) * 100, 2)
        //    {
        //        doRS.SetFieldVal("SR__GMPerc", (1 - (cCost / (decimal)(cWork + cWorkT))) * 100, 2);
        //    }
        //    else
        //    {
        //        doRS.SetFieldVal("SR__GMPerc", 0, 2);
        //    }

        //    // --------------Update FIL_Inclusions
        //    if (bUpdInclusions == true)
        //    {
        //        doRS.SetFieldVal("FIL_INCLUSIONS", sFiles);
        //    }

        //    // --------------- Save the Quote ---------------
        //    if (bCommit)
        //    {
        //        goP.SetVar("bDoNotUpdateQuoteLines", "1");
        //        // CS 11/5/09: Setting a variable here to let me know NOT to try to update the Qt total again in QT_RecOnSave. Issue was that if
        //        // you open a QT, add a Quote Line and then cancel out of the QT, the QT total did not reflect the actual total. This
        //        // was because CalcQuotetotal was being called here and then again in QT_RecordOnSave. We do NOT want it to be called in QT
        //        // RecOnSave if it was called here.
        //        goP.SetVar("bDoNotRecalcQuoteTotal", "1");
        //        // Save to disk
        //        if (doRS.Commit() != 1)
        //        {
        //            // goP.TraceLine("Commit failed, raising error", "", sProc)
        //            goP.DeleteVar("bDoNotUpdateQuoteLines");
        //            // CS 11/6/09: Set in Ql_RecordOnSave
        //            goP.DeleteVar("bDoNotRecalcQuoteTotal");
        //            doRS = null/* TODO Change to default(_) if this is not a reference type */;
        //            goErr.SetError(35000, sProc, "Error updating the Quote '" + par_s1 + "'."); // CS
        //                                                                                        // goP.TraceLine("Return False", "", sProc)
        //            return false;
        //        }
        //        else
        //        {
        //            goP.DeleteVar("bDoNotUpdateQuoteLines");
        //            goP.DeleteVar("bDoNotRecalcQuoteTotal"); // CS 11/6/09
        //            if (!(doRS == null))
        //                doRS = null/* TODO Change to default(_) if this is not a reference type */;
        //        }
        //    }

        //    // CS Debug
        //    // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)
        //    par_doCallingObject = doRS;
        //    return true;
        //}

        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn , ref bool par_bRunNext , ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 1/21/2014 Disabed Merged
            doForm.SetControlState("CHK_Merged", 4);
            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 1/21/2014 Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 1/21/2014 Merge Functionality - run at end of CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (doForm.oVar.GetVar("CN_Merge") != "1")
                    {
                        // Don't allow merge of contact to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID"))
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CN", "MergeFail");
                        else
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CN", "Merge");
                    }
                }
            }

            return true;
        }

        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 1/21/2014 Disabed Merged
            doForm.SetControlState("CHK_Merged", 4);
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 1/21/2014 Click cancel on merge
            if (doForm.oVar.GetVar("CancelSave") == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 1/21/2014 Merge Functionality - run at end of CO_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (doForm.oVar.GetVar("CO_Merge") != "1")
                    {
                        // Don't allow merge of company to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID"))
                        {
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CO", "MergeFail");

                        }
                        else
                        {
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CO", "Merge");
                        }
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext , ref string par_sSections , clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }

        public bool MergeRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/21/2014 Added for merge
            par_bRunNext = false;

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName()) + "'");
                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Microsoft.VisualBasic.Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField).ToString() == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    break;
                                }

                            case "MMO":
                                {
                                    // Append
                                    if (doRSMergeTo.GetFieldVal(sField) == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    else
                                        doRSMergeTo.SetFieldVal(sField, doRSMergeTo.GetFieldVal(sField) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + doRSMerge.GetFieldVal("SYS_Name") + " ==" + Constants.vbCrLf + doRSMerge.GetFieldVal(sField));
                                    break;
                                }

                            case "CHK":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }

                            case "MLS":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Convert.ToString(Strings.Chr(9)));
                        if (sLinkType[4] == "NN" | Convert.ToInt32(sLinkType[1]) == 2)
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i),ref doLink,true,0,-1,"A_a",ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (doRSMergeTo.GetFieldVal(aLinks.GetItem(i)) == "")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i),ref doLink,true,0,-1,"A_a",ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    // Check active if exists
                    if (goData.IsFieldValid(doRSMerge.GetFileName(), "CHK_ACTIVEFIELD") == true)
                        doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);

                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;
            par_doCallingObject = doRSMerge;
            return true;
        }

        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sJournal = "";
            string sWork = "";

            switch (Strings.UCase(par_s5))
            {
                case "MERGE":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // run merge script, continue save
                                    scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
                                    break;
                                }

                            case "NO":
                                {
                                    // Clear merged to co linkbox, continue save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    break;
                                }

                            case "CANCEL":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "MERGEFAIL":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool MO_FormControlOnChange_BTN_DuplicateModel_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 2/7/2014 Added for dup model
            // Sets par_s4 to 1 to designate from form button
            scriptManager.RunScript("AutoModelDuplicate", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            par_doCallingObject = doForm;
            return true;
        }

        public bool QL_FilterLinks_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sPD;
            string sVE;


            // ----------TLD 1/24/2014 Filter LNK_For_MO by LNK_For_PD
            sPD = Convert.ToString(doForm.doRS.GetFieldVal("LNK_For_PD", 1));

            string sFilterIni = "";
            if (sPD != "")
            {
                goTR.StrWrite(ref sFilterIni, "CONDITION", "LNK_Of_PD=" + sPD);
                goTR.StrWrite(ref sFilterIni, "C1CONDITION", "0");
                goTR.StrWrite(ref sFilterIni, "C1FIELDNAME", "<%LNK_Of_PD%>");
                goTR.StrWrite(ref sFilterIni, "C1VALUE1", sPD);
                goTR.StrWrite(ref sFilterIni, "CCOUNT", "1");
                goTR.StrWrite(ref sFilterIni, "FILE", "MO");
                goTR.StrWrite(ref sFilterIni, "SORT", "SYS_NAME ASC");
                goTR.StrWrite(ref sFilterIni, "SORT1", "SYS_NAME");
                goTR.StrWrite(ref sFilterIni, "DIRECTION1", "1");
                goTR.StrWrite(ref sFilterIni, "TABLENAME", "MO");
                goTR.StrWrite(ref sFilterIni, "ACTIVE", "1");
            }

            doForm.SetFilterINI("LNK_For_MO", sFilterIni);


            // -----------TLD 1/24/2014 Filter LNK_For_PD by LNK_Related_VE
            sVE = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_VE", 1));

            sFilterIni = "";
            if (sVE != "")
            {
                goTR.StrWrite(ref sFilterIni, "CONDITION", "LNK_Related_VE=" + sVE);
                goTR.StrWrite(ref sFilterIni, "C1CONDITION", "0");
                goTR.StrWrite(ref sFilterIni, "C1FIELDNAME", "<%LNK_Related_VE%>");
                goTR.StrWrite(ref sFilterIni, "C1VALUE1", sVE);
                goTR.StrWrite(ref sFilterIni, "CCOUNT", "1");
                goTR.StrWrite(ref sFilterIni, "FILE", "PD");
                goTR.StrWrite(ref sFilterIni, "SORT", "SYS_NAME ASC");
                goTR.StrWrite(ref sFilterIni, "SORT1", "SYS_NAME");
                goTR.StrWrite(ref sFilterIni, "DIRECTION1", "1");
                goTR.StrWrite(ref sFilterIni, "TABLENAME", "PD");
                goTR.StrWrite(ref sFilterIni, "ACTIVE", "1");
            }

            doForm.SetFilterINI("LNK_For_PD", sFilterIni);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QL_FormControlOnChange_LNK_For_PD_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 1/24/2014 Call script to filter links
            scriptManager.RunScript("QL_FilterLinks", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            par_doCallingObject = doForm;
            return true;
        }

        public bool QL_FormControlOnChange_LNK_Related_VE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/1/2014 Don't connect vendors here, changed to _Pre
            par_bRunNext = false;

            // TLD 1/24/2014 Call script to filter links
            scriptManager.RunScript("QL_FilterLinks", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");

            return true;
        }

        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 1/24/2014 Unlock fields
            // locked
            doForm.SetControlState("lnk_for_pd", 0);
            doForm.SetControlState("lnk_related_ve", 0);

            // TLD 1/28/2014 -- disable from main Gray out CUR_Cost field on the QL. The Cost will be calculated based on Model Cost * Qty
            // If a customer needs to manually enter costs, this can be customized to ungray the field
            doForm.SetControlState("CUR_COST", 0);
            // TLD 3/17/2014 Added to calc CUR_Cost * SR__Qty
            doForm.SetControlState("CUR_TotalCost", 4);

            // TLD 1/28/2014 Grays Gross Profit field, calculated
            doForm.SetControlState("CUR_GROSSPROFIT", 4);

            // TLD 1/24/2014 Call script to filter links
            scriptManager.RunScript("QL_FilterLinks", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            par_doCallingObject = doForm;
            return true;
        }

        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Rowset object containing the record to be saved.
            // par_doArray: Unused.
            // par_sMode: 'CREATION' or 'MODIF'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/24/2014 Don't autofill LNK_For_PD & LNK_Related_VE
            par_bRunNext = false;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

            // MI 2/25/07 Added bDoNotUpdateQuoteLines

            decimal cResult= default(decimal);

            clArray doLink;
            // Dim cResult as decimal
            // Dim doQuote as object 
            // Dim lActionNo as integer
            string sFileName = "QL";
            string sSysName = "";
            DateTime dtDateTime = default(DateTime);
            // Get QuoteInfo variable set in QT_RecOnSave
            string sQuoteInfo = Convert.ToString(goP.GetVar("QuoteInfo_" + Convert.ToString(doRS.GetFieldVal("LNK_IN_QT"))));

            // If bDoNotUpdateQuoteLines remained 1 earlier due to an error, reset it
            goP.SetVar("bDoNotUpdateQuoteLines", "0");

            // --------- AUTO-FILLED FIELDS ---------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "SetQuoteOpenAndQLTimeCompleted",true))
            {
                // Fill CHK_OPEN when Status is Open or On Hold
                switch (doRS.GetFieldVal("MLS_STATUS", 2))
                {
                    case 0:
                    case 1     // Open, On Hold (was using 0 and 20)
                   :
                        {
                            doRS.SetFieldVal("CHK_OPEN", 1, 2);
                            break;
                        }

                    case 2:
                    case 3 // Won, Lost
             :
                        {
                            doRS.SetFieldVal("CHK_OPEN", 0, 2);
                            // CS 1/11/10 Fill QL.DTE_TimeCompleted with QT.DTE_DateClosed if blank
                            if (Convert.ToString(doRS.GetFieldVal("DTT_TimeCompleted", 1)) == "")
                            {
                                // If don't manage quote line status independent of qt set to same as QT; otherwise, set to now.
                                if (Convert.ToString(doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_STATUSMGMT")) == "0")
                                {
                                    doRS.SetFieldVal("DTT_TimeCompleted", doRS.GetFieldVal("LNK_IN_QT%%DTT_DATECLOSED"));
                                }
                                else
                                // QL status mgmt is on. 
                                {
                                    doRS.SetFieldVal("DTT_TimeCompleted", goTR.NowLocal());
                                }
                            }

                            break;
                        }

                    default:
                        {
                            doRS.SetFieldVal("CHK_OPEN", 0, 2);
                            break;
                        }
                }
            }


            // Fill Quote Date and Time from the linked Quote
            if (sQuoteInfo == "")
                doRS.SetFieldVal("DTT_QTETIME", doRS.GetFieldVal("LNK_IN_QT%%DTT_TIME"));
            else
                doRS.SetFieldVal("DTT_QTETIME", goTR.StrRead(sQuoteInfo, "QT_DTT_TIME", null/* Conversion error: Set to default value for this argument */, false));

            // Automatically fill Expected Close Date if blank
            if (Convert.ToString(doRS.GetFieldVal("DTE_ExpCloseDate", 1)) == "")
                doRS.SetFieldVal("DTE_ExpCloseDate", "Today");

            // Old non-UTC-aware code
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillYearMonthDayFields",true))
            {
                // 'Fill Month/Year fields used for grouping/totaling in reports
                // doRS.SetFieldVal("TXT_YEAR", goTR.GetYear(doRS.GetFieldVal("DTT_TIME", 2)))
                // doRS.SetFieldVal("SI__MONTH", goTR.StringToNum(goTR.GetMonth(doRS.GetFieldVal("DTT_TIME", 2))))
                // 'Close Month/Year fields used for grouping/totaling in reports
                // doRS.SetFieldVal("TXT_YEARCLOSE", goTR.GetYear(doRS.GetFieldVal("DTT_EXPCLOSEDATE", 2)))
                // doRS.SetFieldVal("SI__MONTHCLOSE", goTR.StringToNum(goTR.GetMonth(doRS.GetFieldVal("DTT_EXPCLOSEDATE", 2))))

                /////////////////////////////////////////////
                //DateTime par_dtDateTime =Convert.ToDateTime(doRS.GetFieldVal("DTT_TIME", 2));
                //dtDateTime = goTR.UTC_LocalToUTC(ref par_dtDateTime);
                DateTime _dt = Convert.ToDateTime(doRS.GetFieldVal("DTT_TIME", 2));
                dtDateTime = goTR.UTC_LocalToUTC(ref _dt);
                doRS.SetFieldVal("TXT_YEAR", goTR.GetYear(dtDateTime));
                doRS.SetFieldVal("SI__MONTH", goTR.StringToNum(goTR.GetMonth(dtDateTime),"",ref par_iValid,""));
                if (goData.IsFieldValid("QL", "SI__Day"))
                {
                    doRS.SetFieldVal("SI__Day", goTR.StringToNum(goTR.GetDay(dtDateTime), "", ref par_iValid, ""));
                }

                DateTime _dt1 = Convert.ToDateTime(doRS.GetFieldVal("DTT_EXPCLOSEDATE", 2));
                dtDateTime = goTR.UTC_LocalToUTC(ref _dt1);
                doRS.SetFieldVal("TXT_YEARCLOSE", goTR.GetYear(dtDateTime));
                doRS.SetFieldVal("SI__MONTHCLOSE", goTR.StringToNum(goTR.GetMonth(dtDateTime), "", ref par_iValid, ""));
                if (goData.IsFieldValid("QL", "SI__DayClose"))
                {
                    doRS.SetFieldVal("SI__DayClose", goTR.StringToNum(goTR.GetDay(dtDateTime), "", ref par_iValid, ""));
                }
            }

            // CS 6/3/09 Store QL Model info in var to use instead of creating rowsets throughout.
            string sWork = "";

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before Model rowset" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

            clRowSet doMO = new clRowSet("MO", 3, "GID_ID='" + doRS.GetFieldVal("LNK_FOR_MO") + "'", null/* Conversion error: Set to default value for this argument */, "CUR_COST,LNK_OF_PD,LNK_OF_PD%%LNK_RELATED_DV,LNK_RELATED_VE,TXT_UNITTEXT");
            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("After model rowset" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

            // TLD 1/28/2014 Overrides variables for QL Cost, user enters
            // NOT pulled from Model
            if (doMO.GetFirst() == 1)
            {
                goTR.StrWrite(ref sWork, "MO_TXT_UNITTEXT", doMO.GetFieldVal("TXT_UNITTEXT"));
                goTR.StrWrite(ref sWork, "MO_LNK_OF_PD", doMO.GetFieldVal("LNK_OF_PD"));
                goTR.StrWrite(ref sWork, "MO_LNK_RELATED_VE", doMO.GetFieldVal("LNK_RELATED_VE"));
                goTR.StrWrite(ref sWork, "MO_LNK_OF_PD_LNK_RELATED_DV", doMO.GetFieldVal("LNK_OF_PD%%LNK_RELATED_DV"));
                // goTR.StrWrite(sWork, "MO_CUR_COST", doMO.GetFieldVal("CUR_COST"))
                goTR.StrWrite(ref sWork, "MO_CUR_COST", doRS.GetFieldVal("CUR_COST"));
                goP.SetVar("QuoteLineInfo_" + doRS.GetFieldVal("GID_ID"), sWork);
            }

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillUnitFromModel",true))
            {
                if (Convert.ToString(doRS.GetFieldVal("TXT_UNIT")) == "")
                {
                    if (sWork == "")
                    {
                        doRS.SetFieldVal("TXT_UNIT", doRS.GetFieldVal("LNK_FOR_MO%%TXT_UNITTEXT"));
                    }
                    else
                    {
                        doRS.SetFieldVal("TXT_UNIT", goTR.StrRead(sWork, "MO_TXT_UNITTEXT", null, false));
                    }
                }
            }


            // ---------- Set field values -----------
            if (goP.GetRunMode() != "Import")
            {
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillRedundantLinksForReporting",true))
                {

                    // CS Debug
                    // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("BEfore fill redundant links" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                    // '==> AFTER GETFIELDVAL FROM 2ND HOP LINKS AND CLEARLINKALL ARE FIXED, ENABLE THIS SECTION
                    // 'AND REMOVE IT FROM FORMONSAVE SCRIPT.
                    // TraceLine("Setting links to values from the Quote and Model","",sProc)
                    doRS.ClearLinkAll("LNK_TO_CO");

                    // TLD 1/24/2014 Don't clear
                    // doRS.ClearLinkAll("LNK_FOR_PD")

                    // CS 6/2/09: Get values from values set in QT_RecOnSave if it exists
                    if (sQuoteInfo == "")
                    {
                        doRS.SetFieldVal("LNK_TO_CO", doRS.GetFieldVal("LNK_IN_QT%%LNK_TO_CO", 2), 2);
                    }
                    else
                    {
                        doRS.SetFieldVal("LNK_TO_CO", goTR.StrRead(sQuoteInfo, "QT_LNK_TO_CO", null, false), 1);
                    }
                    // TLD 1/24/2014 don't fill
                    // If sWork = "" Then
                    // doRS.SetFieldVal("LNK_FOR_PD", doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2)
                    // Else
                    // doRS.SetFieldVal("LNK_FOR_PD", goTR.StrRead(sWork, "MO_LNK_OF_PD", , False))
                    // End If

                    // TraceLine("LNK_FOR_MO%%LNK_OF_PD: '" & doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD") & "'", "", sProc)


                    // CS 8/27/08: Only set Cred/Peer User if WOP for managing Peer/Credited To User independent of 
                    // Qt is off
                    if (Convert.ToString(doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_USERMGMT")) != "1")
                    {
                        doRS.ClearLinkAll("LNK_PEER_US");
                        doRS.ClearLinkAll("LNK_CREDITEDTO_US");
                        if (sQuoteInfo == "")
                        {
                            doRS.SetFieldVal("LNK_PEER_US", doRS.GetFieldVal("LNK_IN_QT%%LNK_PEER_US", 2), 2);
                            doRS.SetFieldVal("LNK_CREDITEDTO_US", doRS.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2), 2);
                        }
                        else
                        {
                            doRS.SetFieldVal("LNK_PEER_US", goTR.StrRead(sQuoteInfo, "QT_LNK_PEER_US", null, false), 1);
                            doRS.SetFieldVal("LNK_CREDITEDTO_US", goTR.StrRead(sQuoteInfo, "QT_LNK_CREDITEDTO_US", null, false), 1);
                        }
                    }

                    doRS.ClearLinkAll("LNK_RELATED_PR");
                    doRS.ClearLinkAll("LNK_RELATED_TE");
                    if (sQuoteInfo == "")
                    {
                        doRS.SetFieldVal("LNK_RELATED_PR", doRS.GetFieldVal("LNK_IN_QT%%LNK_RELATED_PR", 2), 2);
                        doRS.SetFieldVal("LNK_RELATED_TE", doRS.GetFieldVal("LNK_TO_CO%%LNK_IN_TE", 2), 2);
                    }
                    else
                    {
                        doRS.SetFieldVal("LNK_RELATED_PR", goTR.StrRead(sQuoteInfo, "QT_LNK_RELATED_PR", null, false), 1);
                        doRS.SetFieldVal("LNK_RELATED_TE", goTR.StrRead(sQuoteInfo, "CO_LNK_IN_TE", null, false), 1);
                    }
                }
            }

            if (goP.GetRunMode() != "Import")
            {
                // LNK_INVOLVES_USER
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillInvolvesUser",true))
                {
                    doLink = new clArray();
                    oTable = null;
                    doLink = doRS.GetLinkVal("LNK_CREDITEDTO_US",ref  doLink, false,0,-1,"A_a",ref oTable);
                    oTable = null;
                    doLink = doRS.GetLinkVal("LNK_PEER_US", ref doLink, false, 0, -1, "A_a", ref oTable);
                    // If doLink <> vbNull Then
                    doRS.SetLinkVal("LNK_INVOLVES_US", doLink);
                    // delete(doLink)
                    doLink = null;
                }
            }


            // Fill Division
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillDivisionFromModel",true))
            {
                // TraceLine("Setting Model's Product's Division into Related Division","",sProc)
                doRS.ClearLinkAll("LNK_RELATED_DV");
                // TraceLine("LNK_FOR_MODEL: '" & doForm.GetFieldVal("LNK_FOR_MO") & "'","",sProc)
                // TraceLine("LNK_FOR_MO%%LNK_OF_PD: '" & doForm.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD") & "'","",sProc)
                // TraceLine("LNK_FOR_MO%%LNK_OF_PD%%LNK_RELATED_DV: '" & doForm.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD%%LNK_RELATED_DV") & "'","",sProc)
                // Cs commented doRS.SetFieldVal("LNK_RELATED_DV", doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD%%LNK_RELATED_DV"))
                // CS: 3 hops do not work...change to below
                if (sWork == "")
                {
                    string sProduct = Convert.ToString(doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 1));
                    // Get Products division
                    clRowSet doProduct = new clRowSet("PD", 3, "GID_ID='" + sProduct + "'", "", "LNK_Related_DV");
                    if (doProduct.GetFirst() == 1)
                        doRS.SetFieldVal("LNK_RELATED_DV", doProduct.GetFieldVal("LNK_RELATED_DV", 2), 2);
                }
                else
                {
                    doRS.SetFieldVal("LNK_RELATED_DV", goTR.StrRead(sWork, "MO_LNK_OF_PD_LNK_RELATED_DV", null, false));
                }
            }


            // CS Note: CUR_PriceUnit is filled in FormOnSave based on the linked Model's price. I would think that
            // code may need to be in RecordONSave too. For now I am assuming we always have a value in CUR_Price.

            // ----------- Calc Sales Tax Rate -------------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CalcSalesTaxRate"))
            {
                // goP.TraceLine("Calculating CUR_SalesTax", "", sProc)
                // Formula: CUR_SalesTax=([Qty] * [PriceUnitNoDisc] - [Qty] * [PriceUnitNoDisc] * [DiscPercent] / 100) * [SalesTaxPercent] / 100
                cResult = Convert.ToInt32(doRS.GetFieldVal("CUR_PriceUnit", 2)) * Convert.ToInt32(doRS.GetFieldVal("SR__Qty", 2));
                // goP.TraceLine("CUR_PriceUnit: '" & Convert.ToString(doRS.GetFieldVal("CUR_PriceUnit", 2)) & "'", "", sProc)
                // goP.TraceLine("SR__Qty: '" & Convert.ToString(doRS.GetFieldVal("SR__Qty", 2)) & "'", "", sProc)
                // goP.TraceLine("cResult after PriceUnit * Qty: '" & Convert.ToString(cResult) & "'", "", sProc)
                cResult = (cResult - (Convert.ToDecimal(cResult) * Convert.ToDecimal(doRS.GetFieldVal("SR__DiscPercent", 2)) /100) * Convert.ToDecimal(doRS.GetFieldVal("SR__SalesTaxPercent", 2)) /100);
                // goP.TraceLine("SR__SalesTaxPercent: '" & Convert.ToString(doRS.GetFieldVal("SR__SalesTaxPercent", 2)) & "'", "", sProc)
                // goP.TraceLine("cResult after full calc: '" & Convert.ToString(cResult) & "'", "", sProc)
                // goP.TraceLine("Setting Sales Tax field: '" & Convert.ToString(cResult) & "'", "", sProc)
                // goP.TraceLine("Setting in CUR_SalesTax: '" & cResult & "'", "", sProc)
                doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cResult), 2);
            }

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CalcTotal"))
                // CS Adding to calculate Quote Line totals here. Will need to calculate Quote totals
                // when they are linked together.
                // CS Debug
                // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("BEfore QL CalcTotal-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                scriptManager.RunScript("Quotline_CalcTotal",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections,null,"","","","","");

            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before ConnectVendors-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

            // TLD 1/24/2014 Do NOT Fill Vendor
            // If goScr.IsSectionEnabled(sProc, par_sSections, "ConnectVendors") Then
            // goScr.RunScript("Quotline_ConnectVendors", doRS)
            // End If

            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("After connectvendors-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


            // ----------- Recalc Quote totals -------------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "RecalcQuoteTotals"))
            {
                // Recalc quote only if this isn't running as a result of Quote updating Quote Lines
                if (Convert.ToString(goP.GetVar("bDoNotUpdateQuote")) != "1" & doRS.GetLinkCount("LNK_IN_QT") > 0)
                    // goP.SetVar("bDoNotUpdateQuoteLines", "1")  'not needed because now coded in CalcQuoteTotal
                    // CS Debug
                    // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before CalcQuoteTotal-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                    scriptManager.RunScript("CalcQuoteTotal",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections,null,"","","","","");
            }

            goP.DeleteVar("QuoteLineInfo_" + doRS.GetFieldVal("GID_ID"));
            par_doCallingObject = doRS;
            return true;
        }

        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // TLD 4/28/2014 This no longer runs, set in _Pre
            // 'TLD 1/28/2014 Overrides variables for QL Cost, user enters
            // 'NOT pulled from Model
            // 'CS 6/3/09 Store QL Model info in var to use instead of creating rowsets throughout.
            // Dim sWork As String = ""
            // Dim doMO As New clRowSet("MO", 3, "GID_ID='" & doRS.GetFieldVal("LNK_FOR_MO") & "'", , _
            // "GID_ID, SYS_NAME, CUR_COST, LNK_OF_PD, LNK_OF_PD%%LNK_RELATED_DV, LNK_RELATED_VE, TXT_UNITTEXT")
            // If doMO.GetFirst = 1 Then
            // goTR.StrWrite(sWork, "MO_TXT_UNITTEXT", doMO.GetFieldVal("TXT_UNITTEXT"))
            // goTR.StrWrite(sWork, "MO_LNK_OF_PD", doMO.GetFieldVal("LNK_OF_PD"))
            // goTR.StrWrite(sWork, "MO_LNK_RELATED_VE", doMO.GetFieldVal("LNK_RELATED_VE"))
            // goTR.StrWrite(sWork, "MO_LNK_OF_PD_LNK_RELATED_DV", doMO.GetFieldVal("LNK_OF_PD%%LNK_RELATED_DV"))
            // 'goTR.StrWrite(sWork, "MO_CUR_COST", doMO.GetFieldVal("CUR_COST"))
            // goTR.StrWrite(sWork, "MO_CUR_COST", doRS.GetFieldVal("CUR_COST"))
            // goP.SetVar("QuoteLineInfo_" & doRS.GetFieldVal("GID_ID"), sWork)
            // End If
            // doMO = Nothing
            par_doCallingObject = doRS;
            return true;
        }

        //public bool Quote_FillAddress_Pre(ref object par_doCallingObject, ref object par_oReturn , ref bool par_bRunNext , ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //{
        //    // par_doCallingObject: Unused.
        //    // par_doArray: Unused.
        //    // par_s1: 
        //    // par_s2: 
        //    // par_s3: 
        //    // par_s4: 
        //    // par_s5: 
        //    // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        //    // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        //    // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        //    string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    // TLD 1/24/2014 also fill TXT_ShipTo
        //    par_bRunNext = false;

        //    Form doForm = (Form)par_doCallingObject;

        //    // goP.TraceLine("", "", sProc)

        //    // PURPOSE:
        //    // Fill the address field, first checking whether it is empty.
        //    // RETURNS:
        //    // True

        //    string sContactName = "";
        //    string sMailingAddr;
        //    string sFirstName;
        //    string sLastName;
        //    // Dim sCompName as string
        //    string sAddrMail;
        //    string sCityMail;
        //    string sStateMail;
        //    string sZipMail;
        //    string sCountryMail;

        //    // PJ 5/19/14: Changed TXT_BillTo and TXT_ShipTo to 7 individual lines for each address.
        //    // TLD 1/24/2014 If here, need to fill ALWAYS
        //    // If doForm.doRS.GetFieldVal("TXT_ADDRESSMAILING") <> "" And doForm.doRS.GetFieldVal("TXT_ShipTo") <> "" Then Return True

        //    // PJ 5/19/14 Removed this
        //    // If doForm.doRS.GetLinkCount("LNK_ORIGINATEDBY_CN") < 1 Then Return True

        //    // CS 6/22/09: Create CN rowset to get CN fields
        //    clRowSet doRSContact = new clRowSet("CN", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_NAMEFIRST,TXT_NAMELAST,TXT_ADDRBUSINESS,TXT_CITYBUSINESS,TXT_STATEBUSINESS,TXT_ZIPBUSINESS,TXT_COUNTRYBUSINESS");
        //    if (doRSContact.GetFirst() == 1)
        //    {
        //        sFirstName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMEFIRST"));
        //        sLastName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMELAST"));
        //        if (sFirstName != "")
        //            sContactName = sFirstName + " ";
        //        sContactName += sLastName;

        //        sAddrMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ADDRBUSINESS"));
        //        sCityMail = Convert.ToString(doRSContact.GetFieldVal("TXT_CITYBUSINESS"));
        //        sStateMail = Convert.ToString(doRSContact.GetFieldVal("TXT_STATEBUSINESS"));
        //        sZipMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ZIPBUSINESS"));
        //        sCountryMail = Convert.ToString(doRSContact.GetFieldVal("TXT_COUNTRYBUSINESS"));

        //        // PJ 5/19/14 New fields
        //        clRowSet doRSCompany = new clRowSet("CO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_To_CO") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_CompanyName");
        //        if (doRSCompany.GetFirst() == 1)
        //            doForm.doRS.SetFieldVal("TXT_ShipToComp", doRSCompany.GetFieldVal("TXT_CompanyName"));

        //        doForm.doRS.SetFieldVal("TXT_ShipToContact", sContactName);

        //        string[] aAddr = Strings.Split(sAddrMail, Constants.vbCrLf);
        //        if (aAddr.GetLength(0) > 0)
        //        {
        //            doForm.doRS.SetFieldVal("TXT_ShipToAddr1", aAddr.GetValue(0));
        //            if (aAddr.GetLength(0) > 1)
        //            {
        //                doForm.doRS.SetFieldVal("TXT_ShipToAddr2", aAddr.GetValue(1));
        //                if (aAddr.GetLength(0) > 2)
        //                    doForm.doRS.SetFieldVal("TXT_ShipToAddr3", aAddr.GetValue(2));
        //            }
        //        }
        //        doForm.doRS.SetFieldVal("TXT_ShipToCity", sCityMail);
        //        doForm.doRS.SetFieldVal("TXT_ShipToState", sStateMail);
        //        doForm.doRS.SetFieldVal("TXT_ShipToZip", sZipMail);


        //        // Start building the mailing address
        //        sMailingAddr = sContactName;
        //        if (sAddrMail != "")
        //        {
        //            sMailingAddr = sMailingAddr + Constants.vbCrLf + sAddrMail;
        //        }
        //        if (sCityMail != "")
        //        {
        //            sMailingAddr = sMailingAddr + Constants.vbCrLf + sCityMail;
        //        }
        //        if (sStateMail != "")
        //        {
        //            sMailingAddr = sMailingAddr + ", " + sStateMail;
        //        }
        //        if (sZipMail != "")
        //        {
        //            sMailingAddr = sMailingAddr + " " + sZipMail;
        //        }
        //        if (sCountryMail != "")
        //        {
        //            sMailingAddr = sMailingAddr + Constants.vbCrLf + sCountryMail;
        //        }

        //        // TLD 1/24/2014 Always fill if here
        //        if (doForm.doRS.GetFieldVal("TXT_AddressMailing") == "")
        //        {
        //            doForm.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailingAddr);
        //        }
        //    }

        //    return true;
        //}

        public bool Quote_FillBillTo_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PJ 5/19/14: Replaced TXT_Billto with 7 address fields
            // TLD 1/24/2014 also fill TXT_BillTo
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;

            // goP.TraceLine("", "", sProc)

            // PURPOSE:
            // Fill the address field, first checking whether it is empty.
            // RETURNS:
            // True

            string sMailingAddr = "";
            string sAddrMail = "";
            string sCityMail = "";
            string sStateMail = "";
            string sZipMail = "";
            string sCountryMail = "";
            string sCOName = "";



            if (doForm.doRS.GetLinkCount("LNK_To_CO") < 1)
                return true;

            // Create CO rowset to get CO fields
            clRowSet doRSCO = new clRowSet("CO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_To_CO") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_CompanyName, TXT_ADDRBILLING,TXT_CITYBILLING,TXT_STATEBILLING,TXT_ZIPBILLING,TXT_COUNTRYBILLING");
            if (doRSCO.GetFirst() == 1)
            {
                sCOName = Convert.ToString(doRSCO.GetFieldVal("TXT_CompanyName"));
                sAddrMail = Convert.ToString(doRSCO.GetFieldVal("TXT_ADDRMailing"));
                sCityMail = Convert.ToString(doRSCO.GetFieldVal("TXT_CITYMailing"));
                sStateMail = Convert.ToString(doRSCO.GetFieldVal("TXT_STATEMailing"));
                sZipMail = Convert.ToString(doRSCO.GetFieldVal("TXT_ZIPMailing"));
                sCountryMail = Convert.ToString(doRSCO.GetFieldVal("TXT_COUNTRYMailing"));

                // 'Start building the mailing address
                // sMailingAddr = sCOName
                // If sAddrMail <> "" Then
                // sMailingAddr = sMailingAddr & vbCrLf & sAddrMail
                // End If
                // If sCityMail <> "" Then
                // sMailingAddr = sMailingAddr & vbCrLf & sCityMail
                // End If
                // If sStateMail <> "" Then
                // sMailingAddr = sMailingAddr & ", " & sStateMail
                // End If
                // If sZipMail <> "" Then
                // sMailingAddr = sMailingAddr & " " & sZipMail
                // End If
                // If sCountryMail <> "" Then
                // sMailingAddr = sMailingAddr & vbCrLf & sCountryMail
                // End If
                // doForm.doRS.SetFieldVal("TXT_BillTo", sMailingAddr)

                // PJ 5/19/14:
                string[] aAddr = Strings.Split(sAddrMail, Constants.vbCrLf);

                doForm.doRS.SetFieldVal("TXT_BillToComp", sCOName);
                if (aAddr.GetLength(0) > 0)
                {
                    doForm.doRS.SetFieldVal("TXT_BillToAddr1", aAddr.GetValue(0));
                    if (aAddr.GetLength(0) > 1)
                    {
                        doForm.doRS.SetFieldVal("TXT_BillToAddr2", aAddr.GetValue(1));
                        if (aAddr.GetLength(0) > 2)
                            doForm.doRS.SetFieldVal("TXT_BillToAddr3", aAddr.GetValue(2));
                    }
                }
                doForm.doRS.SetFieldVal("TXT_BillToCity", sCityMail);
                doForm.doRS.SetFieldVal("TXT_BillToState", sStateMail);
                doForm.doRS.SetFieldVal("TXT_BillToZip", sZipMail);
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QTConvertToPDF_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
                // AUTHOR: WT
                // MI 9/23/09 Added page sizes, made other mods.
                // PARAMETERS:
                // par_sPageSize = page size. Supported: A4, LEGAL, LETTER. Unsupported sizes are processed as LETTER.
                // par_sFilename = name of file, including .pdf extension
                // par_sHTML = complete HTML to convert
                // par_sOrientation = can be LANDSCAPE or PORTRAIT
                // par_bShowHeader = header on, True.  header off, False.
                // par_sHeaderText = header text
                // par_sHeaderAlignment = header text alignment, possible values are CENTER, LEFT, RIGHT
                // par_iLeftMargin = 50
                // par_iRightMargin = 50
                // par_iTopMargin = 50
                // par_iBottomMargin = 50

                string sProc = "clPDF::HTMLToPDF";

                // TLD 1/14/2013 Added custom pdf convert for quote formatting
                // Called from QT_FormControlOnChange_BTN_PrintRoutingSheet_Pre
                par_bRunNext = false;
                clPDF oPDF = new clPDF();
                string sPath = HttpContext.Current.Server.MapPath("~/Temp/");
                //HtmlToPdfArea oArea = new HtmlToPdfArea(par_s3, "");
                if (!oPDF.HTMLToPDF(sPath, par_s3, "LETTER"))
                {
                    goErr.SetWarning(30029, sProc, "Could not generate Pdf document, please try again.", "", "", "", "", "", "", "", "", "", "");
                    return false;
                }
            
            //try
            //{
            //    ExpertPdf.HtmlToPdf.PdfConverter PdfConverter = new ExpertPdf.HtmlToPdf.PdfConverter();
            //    PdfConverter.PdfDocumentOptions.PdfPageSize = PdfPageSize.Letter;
            //    PdfConverter.PdfDocumentOptions.PdfPageOrientation = PDFPageOrientation.Portrait;
            //    // Set compression level to max (can be set from 0 to 100, with 0 being wors)
            //    // it appears to effect the logo colors?
            //    PdfConverter.PdfDocumentOptions.JpegCompressionLevel = 0;
            //    PdfConverter.PdfDocumentOptions.JpegCompressionEnabled = false;
            //    // Added to see if helps with logo colors
            //    PdfConverter.PdfDocumentOptions.PdfCompressionLevel = PdfCompressionLevel.Normal;

            //    HtmlToPdfArea oArea = new HtmlToPdfArea(par_s3, "");

            //    // Select Case UCase("LETTER")
            //    // Case "A4"
            //    // PdfConverter.PdfDocumentOptions.PdfPageSize = PdfPageSize.A4
            //    // Case "LEGAL"
            //    // PdfConverter.PdfDocumentOptions.PdfPageSize = PdfPageSize.Legal
            //    // Case Else
            //    // 'LETTER and other unsupported sizes
            //    // PdfConverter.PdfDocumentOptions.PdfPageSize = PdfPageSize.Letter
            //    // End Select
            //    // Select Case "PORTRAIT"
            //    // Case "LANDSCAPE"
            //    // PdfConverter.PdfDocumentOptions.PdfPageOrientation = PDFPageOrientation.Landscape
            //    // Case Else
            //    // 'PORTRAIT
            //    // PdfConverter.PdfDocumentOptions.PdfPageOrientation = PDFPageOrientation.Portrait
            //    // End Select
            //    // PdfConverter.PdfDocumentOptions.PdfCompressionLevel = PdfCompressionLevel.Normal
            //    PdfConverter.PdfDocumentOptions.LeftMargin = 30;
            //    PdfConverter.PdfDocumentOptions.RightMargin = 30;
            //    PdfConverter.PdfDocumentOptions.TopMargin = 20;
            //    PdfConverter.PdfDocumentOptions.BottomMargin = 20;
            //    PdfConverter.PdfDocumentOptions.GenerateSelectablePdf = true;

            //    // MI 9/24/09 Per WT: FitWidth won't work until you know the width of the desktops largest table.
            //    // then you set pdfconverter.PageWidth  to that number, then the html would fit.
            //    // PdfConverter.PdfDocumentOptions.FitWidth = True
            //    // PdfConverter.PdfDocumentOptions.FitHeight = True
            //    PdfConverter.AvoidImageBreak = true;
            //    PdfConverter.AvoidTextBreak = true;

            //    // header
            //    // PdfConverter.PdfHeaderOptions.ImageArea.DestHeight = 55
            //    // PdfConverter.PdfHeaderOptions.ImageArea.DestWidth = 1000
            //    PdfConverter.PdfHeaderOptions.HtmlToPdfArea = oArea;
            //    PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Center;
            //    PdfConverter.PdfDocumentOptions.ShowHeader = true;
            //    PdfConverter.PdfHeaderOptions.DrawHeaderLine = false;
            //    // PdfConverter.PdfHeaderOptions.HeaderText = par_s3
            //    // Select Case "CENTER"
            //    // Case "LEFT"
            //    // PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Left
            //    // Case "RIGHT"
            //    // PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Right
            //    // Case "CENTER"
            //    // PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Center
            //    // Case Else
            //    // PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Left
            //    // End Select

            //    // TLD 11/6/2013 Changed to 100 due to font size
            //    // PdfConverter.PdfHeaderOptions.HeaderHeight = 90
            //    PdfConverter.PdfHeaderOptions.HeaderHeight = 20;
            //    // PdfConverter.PdfHeaderOptions.HeaderTextFontName = "Verdana"
            //    // PdfConverter.PdfHeaderOptions.HeaderTextFontSize = 4

            //    // Enabled footer
            //    PdfConverter.PdfDocumentOptions.ShowFooter = true;
            //    PdfConverter.PdfFooterOptions.DrawFooterLine = false;
            //    PdfConverter.PdfFooterOptions.PageNumberingFormatString = "Page &p; of &P;";
            //    PdfConverter.PdfFooterOptions.ShowPageNumber = true;
            //    PdfConverter.PdfFooterOptions.FooterHeight = 20;
            //    // PdfConverter.PdfFooterOptions.FooterTextFontName = "Verdana"
            //    // PdfConverter.PdfFooterOptions.FooterTextFontSize = 5
            //    PdfConverter.PdfFooterOptions.PageNumberTextFontType = PdfFontType.Helvetica;
            //    PdfConverter.PdfFooterOptions.PageNumberTextFontSize = 5;

            //    string sPath = HttpContext.Current.Server.MapPath("~/Temp/");

            //    PdfConverter.LicenseKey = "CSI7KTEpODo/KT8nOSk6OCc4OycwMDAw";
            //    byte[] downloadBytes = PdfConverter.GetPdfBytesFromHtmlString(par_s2);

            //    // let's test for directory
            //    if (System.IO.Directory.Exists(HttpContext.Current.Server.MapPath(@"~\Temp\")))
            //    {
            //    }
            //    else
            //        try
            //        {
            //            // try to create directory
            //            System.IO.Directory.CreateDirectory(HttpContext.Current.Server.MapPath("..") + @"\Temp");
            //        }
            //        catch (Exception ex)
            //        {
            //            goErr.SetError(ex, 45105, sProc);
            //            return "";
            //        }

            //    // Delete the file if it exists.
            //    if (System.IO.File.Exists(sPath + par_s1))
            //        System.IO.File.Delete(sPath + par_s1);

            //    // create the new file
            //    System.IO.FileStream fs = System.IO.File.Create(sPath + par_s1);
            //    fs.Write(downloadBytes, 0, downloadBytes.Length);
            //    fs.Close();

            //    return true;
            ////}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //        goErr.SetError(ex, 45105, sProc);
            //    return false;
            //}
            return true;
        }

        public bool QTPDF_GetLetterHead_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 2/10/2014 Creates Letterhead HTML for PDF
            // Called from QT_FormControlOnChange_BTN_PrintPO
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sLetterHead = "";
            string sLOAddress = "";
            string sLOCity = "";
            string sLOState = "";
            string sLOZip = "";
            // Dim sLOCO As String = ""
            string sLOPhone = "";
            string sLOFax = "";
            string sLOEmail = "";
            string sLOURL = "";

            // ------------Get location data
            clRowSet doLORS = new clRowSet("LO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_TAKENAT_LO") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_ADDRESS, TXT_CITYBUSINESS, TXT_STATEBUSINESS, TXT_ZIPBUSINESS, TXT_LOCATIONCOUNTRY, TEL_PHONE1, TEL_FAX, EML_EMAIL, URL_HTTP");
            if (doLORS.GetFirst() == 1)
            {
                sLOAddress = Convert.ToString(doLORS.GetFieldVal("TXT_ADDRESS"));
                sLOCity = Convert.ToString(doLORS.GetFieldVal("TXT_CITYBUSINESS"));
                sLOState = Convert.ToString(doLORS.GetFieldVal("TXT_STATEBUSINESS"));
                sLOZip = Convert.ToString(doLORS.GetFieldVal("TXT_ZIPBUSINESS"));
                // sLOCO = doLORS.GetFieldVal("TXT_LOCATIONCOUNTRY")
                sLOPhone = Convert.ToString(doLORS.GetFieldVal("TEL_PHONE1"));
                sLOFax = Convert.ToString(doLORS.GetFieldVal("TEL_FAX"));
                sLOURL = Convert.ToString(doLORS.GetFieldVal("URL_HTTP"));
                sLOEmail = Convert.ToString(doLORS.GetFieldVal("EML_EMAIL"));
            }
            // ------------End Quote Location Address, phone & fax
            // Build letterhead that repeats at top of each page
            // sLetterHead = "<LTRHEAD>"
            sLetterHead += "<TABLE style=\"Width:100%; page-break-inside:avoid;\">";
            sLetterHead += "<TR>";
            sLetterHead += "<TD rowspan=1 valign=top style=\"font-size:35; font-weight: bold;\">M-I-C, Inc.</TD>";
            sLetterHead += "<TD rowspan=5 style=\"font-size:18; text-align: right;\">";
            sLetterHead += sLOAddress + "<BR>";
            sLetterHead += sLOCity + ", " + sLOState + "  " + sLOZip + "<BR>";
            sLetterHead += "PH: " + sLOPhone + "<BR>";
            sLetterHead += "FAX: " + sLOFax + "<BR>";
            sLetterHead += "</TD></TR>";
            sLetterHead += "<TR>";
            sLetterHead += "<TD rowspan=1 valign=top style=\"font-size:25; text-align: left;\">";
            sLetterHead += "Metering-Instrumentation-Controls</TD></TR></TABLE>";

            par_oReturn = sLetterHead;
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormAfterSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/20/2014 Added
            par_bRunNext = false;

            // MI 4/26/07

            Form doForm = (Form)par_doCallingObject;
            string sID;
            clRowSet doRS;
            // Dim doOrigQL As clRowSet
            clRowSet doNewQL;
            clRowSet doOrigQuote;
            int i;
            bool bQLNotFound = false;
            string sQuoteOpeningMode;
            string sMessage;

            if (doForm.GetMode() == "CREATION")
            {
                if (doForm.oVar.GetVar("QT_AddQuoteLine").ToString() == "1")
                {
                    // Redisplay the same form so the user thinks the form never went away
                    Form doFormSame = new Form("QT", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), "");
                    goUI.Queue("FORM", doFormSame);
                }
                sQuoteOpeningMode = Convert.ToString(doForm.oVar.GetVar("QuoteOpeningMode"));
                switch (sQuoteOpeningMode)
                {
                    case "Duplicate":
                    case "Revision":
                        {
                            // Create Quote Lines by copying the original ones
                            sID = Convert.ToString(doForm.oVar.GetVar("QuoteOrinalQuoteID"));
                            doRS = new clRowSet("QL", clC.SELL_EDIT, "LNK_In_QT='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "*");
                            // For each quote line found create a new one, linked to the new Quote
                            goP.SetVar("bDoNotUpdateQuote", "1");
                            for (i = 1; i <= doRS.Count(); i++)
                            {
                                switch (sQuoteOpeningMode)
                                {
                                    case "Duplicate":
                                        {
                                            // ---- Technique 1: copy only model-related fields ----
                                            doNewQL = new clRowSet("QL", clC.SELL_ADD, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, 0/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CRL_QL", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), null/* Conversion error: Set to default value for this argument */, true); // bBypassValidation
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       // doNewQL.SetFieldVal("LNK_in_QT", doForm.doRS.GetFieldVal("GID_ID"))
                                            doNewQL.SetFieldVal("LNK_For_MO", doRS.GetFieldVal("LNK_For_MO", 2), 2);
                                            if (doNewQL.GetLinkCount("LNK_CreditedTo_US") < 1)
                                                doNewQL.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));
                                            // doNewQL.SetFieldVal("LNK_CreatedBy_US", goP.GetUserTID())      'System linked - filled automatically
                                            if (doNewQL.GetLinkCount("LNK_Peer_US") < 1)
                                                doNewQL.SetFieldVal("LNK_Peer_US", doNewQL.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
                                            doNewQL.SetFieldVal("SR__LineNo", doRS.GetFieldVal("SR__LineNo", 2), 2);
                                            doNewQL.SetFieldVal("SR__Qty", doRS.GetFieldVal("SR__Qty", 2), 2);
                                            doNewQL.SetFieldVal("TXT_Unit", doRS.GetFieldVal("TXT_Unit"));
                                            doNewQL.SetFieldVal("CUR_PriceUnit", doRS.GetFieldVal("CUR_PriceUnit", 2), 2);
                                            doNewQL.SetFieldVal("SR__DiscPercent", doRS.GetFieldVal("SR__DiscPercent", 2), 2);
                                            doNewQL.SetFieldVal("CUR_Subtotal", doRS.GetFieldVal("CUR_Subtotal", 2), 2);
                                            doNewQL.SetFieldVal("CUR_DiscAddlAmt", doRS.GetFieldVal("CUR_DiscAddlAmt", 2), 2);
                                            doNewQL.SetFieldVal("CUR_PriceUnitAfterDisc", doRS.GetFieldVal("CUR_PriceUnitAfterDisc", 2), 2);
                                            doNewQL.SetFieldVal("CUR_Cost", doRS.GetFieldVal("CUR_Cost", 2), 2);
                                            doNewQL.SetFieldVal("CUR_GrossProfit", doRS.GetFieldVal("CUR_GrossProfit", 2), 2);
                                            doNewQL.SetFieldVal("CHK_Taxable", doRS.GetFieldVal("CHK_Taxable", 2), 2);
                                            doNewQL.SetFieldVal("CHK_Report", doRS.GetFieldVal("CHK_Report", 2), 2);
                                            doNewQL.SetFieldVal("CHK_Include", doRS.GetFieldVal("CHK_Include", 2), 2);
                                            doNewQL.SetFieldVal("TXT_Model", doRS.GetFieldVal("TXT_Model"));
                                            doNewQL.SetFieldVal("MMO_Details", doRS.GetFieldVal("MMO_Details"));

                                            // 'Fields filled in QL_FormOnLoadRecord:
                                            // doForm.doRS.SetFieldVal("DTT_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTT_TIME"))
                                            // doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_EXPCLOSEDATE"))
                                            // doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2), 2)
                                            // doForm.doRS.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_PEER_US", 2), 2)
                                            // doForm.doRS.SetFieldVal("LNK_RELATED_PR", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_RELATED_PR", 2), 2)
                                            // doForm.doRS.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_TO_CO", 2), 2)
                                            // doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"))

                                            // TLD 6/20/2014 Added
                                            doNewQL.SetFieldVal("LNK_Related_VE", doRS.GetFieldVal("LNK_Related_VE", 2), 2);
                                            doNewQL.SetFieldVal("LNK_For_PD", doRS.GetFieldVal("LNK_For_PD", 2), 2);

                                            // 'Fields filled in CRL_QL:
                                            // LNK_IN_QT=<%GID_ID%>
                                            // LNK_Related_PR=<%LNK_Related_PR%>
                                            // MLS_REASONWONLOST=<%MLS_REASONWONLOST%>
                                            // MLS_Status=<%MLS_Status%>

                                            if (doNewQL.Commit() != 1)
                                            {
                                                goP.DeleteVar("bDoNotUpdateQuote");
                                                // CS 6/23/08 Reset var
                                                goP.SetVar("USEQTSTATUS", "");
                                                // CS 8/27/08 Reset var
                                                goP.SetVar("USEQTUSERS", "");
                                                // MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                                                goErr.SetError(35000, sProc, "Error committing an add rowset for the new Quote Line.");
                                                return false;
                                            }

                                            break;
                                        }

                                    case "Revision":
                                        {
                                            // ---- Copy all fields, then reset them as needed ------
                                            // doOrigQL = New clRowSet("QL", clC.SELL_EDIT, "GID_ID='" & doRS.GetFieldVal("GID_ID") & "'")
                                            // If doOrigQL.Count < 1 Then
                                            // 'Record not found - skip it and tell the user later
                                            // bQLNotFound = True
                                            // Else
                                            doNewQL = new clRowSet("QL", clC.SELL_ADD, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, 0/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CRU_QL", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true); // True: bBypassValidation
                                            if (!goData.CopyRecord(ref doRS,ref doNewQL))
                                            {
                                                goP.DeleteVar("bDoNotUpdateQuote");
                                                // CS 6/23/08 Reset var
                                                goP.SetVar("USEQTSTATUS", "");
                                                // CS 8/27/08 Reset var
                                                goP.SetVar("USEQTUSERS", "");
                                                goErr.SetError(35000, sProc, "Error running goData.CopyRecord(). Are both rowsets in clc.SELL_EDIT mode?");
                                                return false;
                                            }
                                            else
                                            {
                                                // Link the line to the new quote
                                                doNewQL.ClearLinkAll("LNK_In_QT");
                                                doNewQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("GID_ID"), clC.SELL_SYSTEM);
                                                // Reset datetime, quote datetime
                                                doNewQL.SetFieldVal("DTT_Time", "Today|Now");
                                                doNewQL.SetFieldVal("DTT_QTETIME", doForm.doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM), clC.SELL_SYSTEM);
                                                // Reset Status, Reason, and Completed
                                                // CS 8/3/07: Per PJ, statuses/reasons should remain as they were in original QT
                                                // doNewQL.SetFieldVal("MLS_Status", 0, clC.SELL_SYSTEM)           'Open
                                                // doNewQL.SetFieldVal("MLS_ReasonWonLost", 0, clC.SELL_SYSTEM)    '<Make selection>
                                                doNewQL.SetFieldVal("DTT_TimeCompleted", "");
                                                if (doNewQL.Commit() != 1)
                                                {
                                                    goP.DeleteVar("bDoNotUpdateQuote");
                                                    // CS 6/23/08 Reset var
                                                    goP.SetVar("USEQTSTATUS", "");
                                                    // CS 8/27/08 Reset var
                                                    goP.SetVar("USEQTUSERS", "");
                                                    // MI 3/31/09 added 35000, sproc. The string was in the first parameter.
                                                    goErr.SetError(35000, sProc, "Error committing an add rowset for the new Quote Line.");
                                                    return false;
                                                }
                                            }

                                            break;
                                        }
                                }
                                if (doRS.GetNext() != 1)
                                    break;
                            }
                            goP.DeleteVar("bDoNotUpdateQuote");

                            if (sQuoteOpeningMode == "Revision")
                            {
                                // Set Status of the original quote to Revised (6)
                                doOrigQuote = new clRowSet("QT", clC.SELL_EDIT, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "*");
                                if (doOrigQuote.Count() > 0)
                                {
                                    doOrigQuote.SetFieldVal("MLS_Status", 6, clC.SELL_SYSTEM);
                                    if (doOrigQuote.Commit() != 1)
                                    {
                                        sMessage = "The Status of the original Quote can't be changed to 'Revised'. It will be reported in totals as a duplicate of the quote you just created. Please contact your Selltis administrrator. Quote Name: '" + doOrigQuote.GetFieldVal("SYS_Name") + "'. Quote ID: '" + sID + "'.";

                                    }
                                    // Change the status of all linked QLs to 'revised (6)'
                                    // Have this above: doRS = New clRowSet("QL", clC.SELL_EDIT, "LNK_In_QT='" & sID & "'")
                                    if (doRS.GetFirst()==1)
                                    {
                                        do
                                        {
                                            doRS.SetFieldVal("MLS_STATUS", 6, 2);
                                            if (doRS.Commit() != 1)
                                            {
                                                sMessage = "The Status of one or more of the original Quote lines can't be changed to 'Revised'. Please contact your Selltis administrator. Quote Name: '" + doOrigQuote.GetFieldVal("SYS_NAME") + "'. Quote ID: '" + sID + "'.";

                                            }
                                            if (doRS.GetNext() == 0)
                                            {
                                                break;
                                            }
                                        }
                                        while (true);
                                    }
                                }
                            }

                            // Recalc the new quote
                            scriptManager.RunScript("CalcQuoteTotal", ref par_doCallingObject, ref par_oReturn,ref par_bRunNext, ref par_sSections, null, doForm.doRS.GetFieldVal("GID_ID").ToString());

                            if (doForm.oVar.GetVar("QuoteDuplicateManageLines").ToString() == "1")
                            {
                                // Redisplay the Quote form
                                Form doFormSame = new Form("QT", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), "");
                                if (bQLNotFound)
                                    doFormSame.MessageBox("One or more Quote Lines from the original Quote couldn't be created because they don't exist. They may have been deleted by another user.");
                                doFormSame.MessagePanel("Click the buttons next to the Lines linkbox to create, edit, or remove the lines.", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "info.gif");
                                goUI.Queue("FORM", doFormSame);
                            }

                            break;
                        }
                }
            }

            // CS 6/23/08 Reset var
            goP.SetVar("USEQTSTATUS", "");
            // CS 8/27/08
            goP.SetVar("USEQTUSERS", "");

            // CS 7/29/09 Reset variable that told us we had opened a QT in a form. This variable is set in QT_formOnloadrecord and is used in
            // QT_RecordOnSave to tell us that we need to call RecalcTotals if all we did was edit a QT via rowset (not from a form), and not edit QLS
            goP.SetVar("OpenQTForm", "");

            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;


            string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sGidId + "' And LNK_FOR_MO%%BI__ID<1 ", "LNK_IN_QT", "LNK_FOR_MO");

            if ((rsQL.GetFirst() == 1))
            {
                goErr.SetWarning(35000, sProc, "Please fill Model before saving the Quote  ");
                doForm.FieldInFocus = "LNK_FOR_MO";
                par_doCallingObject = doForm;
                return false;
            }
            if (doForm.doRS.IsLinkEmpty("LNK_PEER_US"))
            {
                goErr.SetWarning(35000, sProc, "Please fill out the field 'Inside Rep'");
                doForm.FieldInFocus = "LNK_PEER_US";
                par_doCallingObject = doForm;
                return false;
            }
            doForm.MoveToTab(0);

            par_doCallingObject = doForm;
            return true;


        }
        public bool QT_FormControlOnChange_BTN_CORR_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 2/5/2014 Default to File
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            // Dim doNewForm As Object

            string sURL;
            string sName;

            if (goP.GetProduct() == "MB")
                sURL = "../Pages/Mobile_DiaSend.aspx";
            else
                sURL = "../Pages/diaSend.aspx";
            // objectotsend: VIEW, RECORD, CORR
            goTR.URLWrite(ref sURL, "objecttosend", "CORR");
            // TLD 2/5/2014 Default to file
            goTR.URLWrite(ref sURL, "sendtype", "LETTER");

            // We don't know how the user wants to send a Quote 
            // Send type: EMAIL, FAX, LETTER, or WPRESPONSE
            // goTR.URLWrite(sURL, "sendtype", "EMAIL")

            goTR.URLWrite(ref sURL, "objectid",doForm.doRS.GetFieldVal("GID_ID").ToString());
            sName = Convert.ToString(doForm.doRS.GetFieldVal("SYS_Name"));
            // CS: Always update sys name in case one of the affected fields changed.
            // If sName = "" Then
            scriptManager.RunScript("GenerateSysName", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, sName);
            // End If
            goTR.URLWrite(ref sURL, "objectname", sName);
            // goTR.URLWrite(sURL, "returnto", "MI_Test.aspx")



            // If doForm.Save(1, , System.Reflection.MethodInfo.GetCurrentMethod().Name) = 0 Then
            // Return False
            // End If

            // CS 6/28/07:Changing to doForm.save(2) so that the _post script will be called if there is any. When
            // set to doform.save(1) the code never returned here after save so the function never
            // returned true and the post script would never be called. So now the post script can be called and I
            // just set a boolean that tells the form to close so that the Send dialog displays.
            if (doForm.Save(2,true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                return false;

            // CS 9/13/08 Added
            //HttpContext.Current.Response.Redirect(goUI.Navigate("DIALOG", sURL));
            goUI.Queue("DIALOG", sURL);
            // CS 9/13/08: Commenting out below. 
            // Reason: Open an existing QT; edit its contents; Create linked correspondence
            // and send it. When the send dialog opens, both the corr activity AND the Quote are
            // closed and thus changes are lost.
            // goUI.SetNext("DIALOG", sURL) 'CS: Moved to after save call. Was before
            // doForm.CloseOnReturn = True 'flag       
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_FILLFROMCN_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext , ref string par_sSections , clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/24/2014 Fill other fields
            //par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;

            // TLD 1/24/2014 Fill Always clear these and refill on change of contact?
            // If doForm.doRS.GetFieldVal("TXT_AddressMailing") = "" Then
            doForm.doRS.SetFieldVal("TXT_Addressmailing", "");
            doForm.doRS.SetFieldVal("TXT_ShipTo", "");        // If doForm.doRS.GetFieldVal("TXT_AddressMailing") = "" Then
                                                              // If doForm.doRS.GetFieldVal("TXT_AddressMailing") = "" Or doForm.doRS.GetFieldVal("TXT_ShipTo") = "" Then
            scriptManager.RunScript("Quote_FillAddress", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            // End If

            if (Convert.ToString(doForm.doRS.GetFieldVal("Eml_email")) == "")
            {
                scriptManager.RunScript("Quote_FillEmail", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            }
            if (Convert.ToString(doForm.doRS.GetFieldVal("TEL_FAX")) == "")
            {
                scriptManager.RunScript("Quote_FillFax", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            }
            
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_PrintPO_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/30/2014 Print PDF Purchase Order
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sID = Convert.ToString(doForm.doRS.GetCurrentRecID());
            DateTime dtDate; // DTE_DateWon
            string sDate = ""; // DTE_DateWon alpha
            double rQty = 0; // Total of QL SR__Qty where Include is checked
                             // TLD 2/5/2014 Removed this unused
                             // Dim dCost As Decimal = 0 'Total of QL CUR_Cost where Include is checked
            string sHTML = "<html><body>";
            string sFile = "";
            string sContact = ""; // Vendor string
            string sContactName = ""; // Vendor first & last name
            string sCompany = ""; // Vendor contact's company name
            string sNewOrder = ""; // Status of CHK_NewOrder
            string sChangeOrder = ""; // Status of CHK_ChangeOrder
            string sNotes = ""; // Notes section after quote lines
            string sPhone = ""; // vendor contact phone
            string sEmail = ""; // vendor contact email
                                // Quote Line Table
            int i = 0;
            string sDetails = "";
            string sUnitPrice = "";
            string sNetPrice = "";
            string sQLTableHeader = "";
            string sDescription = "";
            string sFirstPage = "";
            string sTerms = "";
            decimal cTotal = default(decimal); // total of Price (CUR_UnitPrice) field?
                                // TLD 2/10/2014 Added for first page letterhead
            string sLetterhead = "";
            // TLD 3/17/2014 Changed to CUR_TotalCost
            // TLD 2/26/2014 New var
            decimal cCost = default(decimal); // CUR_Cost

            try
            {
                // Capture QT ID
                sID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                // -----------Save quote just in case
                // ONLY save if user has permissions,
                // otherwise they can still print
                if (goData.GetRecordPermission(sID, "E") == true & doForm.IsDirty)
                {
                    // Check Sent checkbox
                    doForm.doRS.SetFieldVal("CHK_Sent", 1, 2);
                    // Save quote
                    if (doForm.Save(3,true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                        // failed save, write to log
                        goLog.Log(sProc, "QT update for Print PDF failed from QT '" + sID + " with error " + goErr.GetLastError("NUMBER") + "'");
                }
                // ----------End save quote

                // ---------Create file name for PDF
                if (doForm.doRS.IsLinkEmpty("LNK_TO_CO") == false)
                {
                    sFile = doForm.doRS.GetFieldVal("LNK_TO_CO%%TXT_COMPANYNAME",0,-1,true, 25) + "QT" + doForm.doRS.GetFieldVal("TXT_QUOTENO") + goTR.NowUTC();

                }
                else
                {
                    sFile = "QT" + doForm.doRS.GetFieldVal("TXT_QUOTENO") + goTR.NowUTC();
                }
                // Strips illegal characters from file name
                // so no error when creating temporary file
                sFile = goTR.StripIllegalChars(sFile, "REMOVE") + ".PDF";
                // ---------End Create file name for PDF

                // -----------Get Common fields for each Vendor order
                // TLD 2/25/2014 Now they want to pul the local date for right now
                // Get longdate
                // dtDate = doForm.doRS.GetFieldVal("DTT_Time", 2)
                dtDate = goTR.NowLocal().Date;
                sDate = goTR.GetMonthAlpha(dtDate) + " " + goTR.GetDay(dtDate) + ", " + goTR.GetYear(dtDate);

                // Get Vendor Contact string
                clRowSet doCNRS = new clRowSet("CN", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_Vendor_CN%%GID_ID") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_NameFirst, TXT_NameLast, TXT_CompanyNameText, TEL_BusPhone, EML_Email", 1);
                if (doCNRS.GetFirst()==1)
                {
                    sContactName = "ATTN:&nbsp&nbsp" + doCNRS.GetFieldVal("TXT_NameFirst") + " " + doCNRS.GetFieldVal("TXT_NameLast");
                    sCompany = "TO:&nbsp&nbsp&nbsp&nbsp" + doCNRS.GetFieldVal("TXT_CompanyNameText");
                    sPhone = Convert.ToString(doCNRS.GetFieldVal("TEL_BusPhone"));
                    sEmail = Convert.ToString(doCNRS.GetFieldVal("EML_Email"));
                    // TLD 1/10/2014 changed order
                    if (sContact == "")
                    {
                        if (sCompany != "")
                        {
                            sContact = sCompany;
                        }
                    }
                    if (sContact == "")
                    {
                        if (sContactName != "")
                        {
                            sContact = sContactName;
                        }
                    }
                    else if (sContactName != "")
                    {
                        sContact += "<BR>" + sContactName;
                    }
                    // TLD 2/10/2014 Changed Order
                    // If sContact = "" Then
                    // If sCompany <> "" Then
                    // sContact = sCompany
                    // End If
                    // Else
                    // If sCompany <> "" Then
                    // sContact &= "<BR>" & sCompany
                    // End If
                    // End If
                    if (sContact == "")
                    {
                        if (sPhone != "")
                        {
                            sContact = sPhone;
                        }
                    }
                    else if (sPhone != "")
                    {
                        sContact += "<BR>" + sPhone;
                    }
                    if (sContact == "")
                    {
                        if (sEmail != "")
                        {
                            sContact = sEmail;
                        }
                    }
                    else if (sEmail != "")
                    {
                        sContact += "<BR>" + sEmail;
                    }
                }

                // Get New Order check mark symbol
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_NewOrder", 2)) == 1)
                {
                    sNewOrder = "&#10004";
                }

                // Get Change Order check mark symbol
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_ChangeOrder", 2)) == 1)
                {
                    sNewOrder = "&#10004";
                }

                // TLD Get Letterhead for first page
                scriptManager.RunScript("QTPDF_GetLetterhead", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, sLetterhead);

                sHTML += sLetterhead;

                // Heading
                sHTML += "<DIV style=\"text-align:center; Width:100%; margin:0 auto;\">";
                // sHTML &= "<TABLE style=""border:0px solid black; Width:100%; page-break-inside:avoid; font-size:23; background-color:lightgray;"">"
                // sHTML &= "<TR><TD style=""text-align:center;"">"
                // sHTML &= "M-I-C, Inc. Order Approval/Routing Sheet"
                // sHTML &= "</TD></TR></TABLE><BR><BR>"

                // Vendor
                sHTML += "<BR><BR><TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18;\">";
                sHTML += "<TR>";
                // TLD 2/11/2014 Set PURCHASE ORDER to bol
                // TLD 2/10/2014 Changed Vendor: to Purchase Order
                sHTML += "<TD rowspan=\"6\" style=\"text-align:left; vertical-align:top; Width:50%; font-weight:bold;\">PURCHASE ORDER<BR><BR><Font style=\"font-weight:normal;\">" + sContact + "</TD>";
                sHTML += "<TD rowspan=\"1\" style=\"vertical-align:top; Width:50%;\">Date:  " + sDate + "</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                // TLD 2/11/2014 Changed MIC PO# to M-I-C, INC PO#:
                // sHTML &= "<TD style=""text-align:left; vertical-align:top; Width:50%;"">MIC PO#:  " & doForm.doRS.GetFieldVal("TXT_POToVendor") & "</TD>"
                sHTML += "<TD style=\"text-align:left; vertical-align:top; Width:50%;\">M-I-C, INC PO#:  " + doForm.doRS.GetFieldVal("TXT_POToVendor") + "</TD>";
                sHTML += "<TD style=\"vertical-align:top; Width:50%;\">&nbsp</TD>";
                sHTML += "</TR></TABLE>";

                sHTML += "<BR><BR>";

                // Ship To
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18;\">";
                sHTML += "<TR>";
                sHTML += "<TD rowspan=\"6\" style=\"text-align:left; vertical-align:top; Width:50%;\">Ship To:<BR><BR>" + goTR.Replace(Convert.ToString(doForm.doRS.GetFieldVal("TXT_ShipTo")), Constants.vbCrLf, "<BR>") + "</TD>";

                // TLD 2/14/2014 Changed from TXT_BillTo to TXT_POBillTo
                // TLD 2/4/2014 Added Bill To
                // Bill To
                // sHTML &= "<TD rowspan=""8"" style=""vertical-align:top;"">Bill To:<BR><BR>" & goTR.Replace(doForm.doRS.GetFieldVal("TXT_BillTo"), vbCrLf, "<BR>") & "</TD>"
                sHTML += "<TD rowspan=\"8\" style=\"vertical-align:top;\">Bill To:<BR><BR>" + goTR.Replace(Convert.ToString(doForm.doRS.GetFieldVal("TXT_POBillTo")), Constants.vbCrLf, "<BR>") + "</TD>";
                sHTML += "</TR></TABLE>";

                sHTML += "<BR><BR>";

                // Total Items table
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18; border-collapse:collapse; border:1px solid black;\">";
                sHTML += "<TR>";
                // TLD 2/5/2014 Default Terms to Net 30 -- users will change on PDF if necessary
                // TLD 2/4/2014 Changed Total Items to Terms
                // sHTML &= "<TD style=""text-align:left; vertical-align:top; border:1px solid black;"">Total Items:  </TD>"
                // sHTML &= "<TD style=""text-align:left; vertical-align:top; border:1px solid black;"">Terms:  " & doForm.doRS.GetFieldVal("LNK_Related_TR%%TXT_TermsName", 1) & "</TD>"
                sHTML += "<TD style=\"text-align:left; vertical-align:top; border:1px solid black;\">Terms:  Net 30</TD>";
                // TLD 6/17/2014 Changed the #
                // TLD 2/7/2014 Added another cell:
                // sHTML &= "<TD style=""text-align:left; vertical-align:top; border:1px solid black;"">For Resale: 46-4336472</TD>"
                sHTML += "<TD style=\"text-align:left; vertical-align:top; border:1px solid black;\">For Resale: SR CH 102-551277</TD>";
                // TLD 2/5/2014 Change to pull from new TXT_AccountNumber field
                // sHTML &= "<TD style=""text-align:left; vertical-align:top; border:1px solid black;"">Account #:  " & doForm.doRS.GetFieldVal("MLS_ShippingPayment", 1) & "</TD>"
                sHTML += "<TD style=\"text-align:left; vertical-align:top; border:1px solid black;\">Account #:  " + doForm.doRS.GetFieldVal("TXT_AccountNumber") + "</TD>";
                sHTML += "</TR></TABLE>";

                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18; border-collapse:collapse; border:1px solid black;\">";
                sHTML += "<TR>";
                // TLD 2/4/2014 Replaced Ship Date with Requested Ship Date
                // sHTML &= "<TD style=""text-align:left; vertical-align:top; border:1px solid black;"">Ship Date:  " & doForm.doRS.GetFieldVal("DTE_RequestedShip") & "</TD>"
                sHTML += "<TD style=\"text-align:left; vertical-align:top; border:1px solid black;\">Requested Ship Date:  " + doForm.doRS.GetFieldVal("DTE_RequestedShip") + "</TD>";
                // TLD 2/5/2014 Ship Via should pull from LNK_Related_DM
                // TLD 2/4/2014 Replaces Promised Date with Ship Via
                // sHTML &= "<TD style=""text-align:left; vertical-align:top; border:1px solid black;"">Promised Date:  </TD>"
                // sHTML &= "<TD style=""text-align:left; vertical-align:top; border:1px solid black;"">Ship Via:  " & doForm.doRS.GetFieldVal("TXT_FOB") & "</TD>"
                sHTML += "<TD style=\"text-align:left; vertical-align:top; border:1px solid black;\">Ship Via:  " + doForm.doRS.GetFieldVal("LNK_Related_DM%%TXT_DELMETHNAME") + "</TD>";
                sHTML += "</TR></TABLE>";

                // TLD 3/17/2014 Changed Price to pull CUR_TotalCost
                // TLD 2/26/2014 Changed Unit column to Unit Price (CUR_Cost)
                // Price is CUR_Cost * Qty
                // Quote Line Table
                // Add column headers to table
                // sQLTableHeader = "Item,Qty,Description,Unit,Price"
                sQLTableHeader = "Item,Qty,Description,Unit Price,Price";

                string[] aQLTableHeader = Strings.Split(sQLTableHeader, ",");
                string sHeader = "";
                //TableRow oTableRow = new TableRow();

                // Format table columns
                sHTML += "<TABLE style=\"Width:100%; font-size:18; border:1px solid black; border-collapse: collapse;\">";
                sHTML += "<THEAD style=\"display: table-header-group;\"><TR style=\"text-align:center; page-break-inside:avoid; vertical-align:top; border:1px solid black;\">";
                for (i = 0; i <= aQLTableHeader.GetUpperBound(0); i++)
                {
                    sHeader = aQLTableHeader[i];
                    sHTML += "<TH style=\"border:1px solid black;\">" + sHeader + "</TH>";
                }
                sHTML += "</TR></THEAD>";

                // TLD 3/17/2014 Added CUR_TotalCost
                // TLD 2/26/2014 Removed TXT_Unit from Rowset
                // TLD 2/14/2014 Replaced CUR_PriceUnit with CUR_Cost
                // TLD 2/7/2014 Added LNK_Related_VE
                // TLD 11/8/2013 Increased fontsize for table entries
                // from 19 to 23 below
                // Loop through QLs, add to table
                // Dim oRS As New clRowSet("QL", 3, "CHK_INCLUDE=1 AND LNK_IN_QT='" & sID & "'", , _
                // "SR__LINENO,TXT_MODEL,MMO_Details,SR__Qty,CUR_PriceUnit,TXT_Unit")
                // Dim oRS As New clRowSet("QL", 3, "CHK_INCLUDE=1 AND LNK_IN_QT='" & sID & "'", , _
                // "SR__LINENO,TXT_MODEL,MMO_Details,SR__Qty,CUR_PriceUnit,TXT_Unit,LNK_Related_VE%%TXT_VendorName")
                // Dim oRS As New clRowSet("QL", 3, "CHK_INCLUDE=1 AND LNK_IN_QT='" & sID & "'", , _
                // "SR__LINENO,TXT_MODEL,MMO_Details,SR__Qty,CUR_Cost,TXT_Unit,LNK_Related_VE%%TXT_VendorName")
                // Dim oRS As New clRowSet("QL", 3, "CHK_INCLUDE=1 AND LNK_IN_QT='" & sID & "'", , _
                // "SR__LINENO,TXT_MODEL,MMO_Details,SR__Qty,CUR_Cost,LNK_Related_VE%%TXT_VendorName")
                clRowSet oRS = new clRowSet("QL", 3, "CHK_INCLUDE=1 AND LNK_IN_QT='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "SR__LINENO,TXT_MODEL,MMO_Details,SR__Qty,CUR_TotalCost,CUR_Cost,LNK_Related_VE%%TXT_VendorName");
                if (oRS.GetFirst() == 1)
                {
                    oRS.ToTable();
                    DataTable oDT = new DataTable();
                    oDT = oRS.dtTransTable;
                    oRS = null/* TODO Change to default(_) if this is not a reference type */;
                    //oTableRow = new TableRow();

                   // DataRow oRow;

                    foreach (DataRow oRow in oDT.Rows)
                    {
                        sHTML += "<TR style=\"page-break-inside:avoid;\"><TD style=\"text-align:center; vertical-align:top; border:1px solid black;\">";

                        // QL Line Number
                        string sLineNoDecimal = oRow["SR__LINENO"].ToString();
                        var iPosDecimal = Strings.InStr(sLineNoDecimal, ".");
                        if (iPosDecimal > 0)
                            sLineNoDecimal = Strings.Left(sLineNoDecimal, iPosDecimal - 1);
                        sHTML += sLineNoDecimal + "</TD>";

                        // Qty
                        sHTML += "<TD style=\"text-align:center; vertical-align:top; border:1px solid black;\">";
                        sHTML += oRow["SR__Qty"].ToString() + "</TD>";

                        // QL Description Column
                        sHTML += "<TD style=\"text-align:left; vertical-align:top; border:1px solid black; word-wrap:break-word;\">";

                        // TLD 2/7/2014 Added for LNK_Related_VE
                        sDescription = oRow["LNK_Related_VE%%TXT_VendorName"].ToString();
                        // Model
                        if (oRow["TXT_Model"].ToString() != "")
                        {
                            if (sDescription != "")
                            {
                                sDescription += "<BR>" + oRow["TXT_Model"].ToString();
                            }
                            else
                            {
                                sDescription = oRow["TXT_Model"].ToString();
                            }
                        }
                        // Details
                        if (oRow["MMO_Details"].ToString() != "")
                        {
                            // TLD 2/7/2014 Added return
                            if (sDescription != "")
                            {
                                sDescription += "<BR>" + oRow["MMO_Details"].ToString();
                            }
                            else
                            {
                                sDescription = oRow["MMO_Details"].ToString();
                            }
                        }
                        sDescription = Strings.Replace(sDescription, Strings.Chr(10) + Strings.Chr(13).ToString(), "<BR><BR>");
                        sDescription = Strings.Replace(sDescription, Constants.vbCrLf, "<BR>");
                        sHTML += sDescription + "</TD>";

                        // TLD 2/26/2014 Unit is now Unit Price (CUR_Cost)
                        // Unit
                        sHTML += "<TD style=\"text-align:center; vertical-align:top; border:1px solid black;\">";
                        // sHTML &= oRow.Item("TXT_Unit").ToString & "</TD>"
                        cCost = Convert.ToDecimal(oRow["CUR_Cost"].ToString());
                        sHTML += goTR.FormatCurrField(Convert.ToString(cCost)) + "</TD>";

                        // TLD 3/17/2014 Changed to grab CUR_TotalCost
                        // TLD 2/14/2014 Changed to CUR_Cost, assume Total now totals CUR_Cost?
                        // Price
                        sHTML += "<TD style=\"text-align:center; vertical-align:top; border:1px solid black;\">";
                        // sHTML &= oRow.Item("CUR_PriceUnit").ToString & "</TD>"
                        // cTotal += oRow.Item("CUR_PriceUnit")
                        // TLD 2/26/2014 Added cost to var above
                        // TLD 2/25/2014 Not stored on form, just print -- cost * qty
                        // sHTML &= goTR.FormatCurrField(oRow.Item("CUR_Cost").ToString * oRow.Item("SR__Qty").ToString) & "</TD>"
                        // sHTML &= goTR.FormatCurrField(cCost * oRow.Item("SR__Qty").ToString) & "</TD>"
                        // cTotal += oRow.Item("CUR_Cost") * oRow.Item("SR__Qty")
                        sHTML += goTR.FormatCurrField(oRow["CUR_TotalCost"].ToString()) + "</TD>";
                        cTotal += Convert.ToDecimal(oRow["CUR_TotalCost"]);

                        sHTML += "</TR>";
                    }

                    // Add row for total
                    // QL cell blank placeholder
                    sHTML += "<TR style=\"page-break-inside:avoid;\"><TD style=\"text-align:center; vertical-align:top;\">&nbsp</TD>";

                    // Qty cell blank placeholder
                    sHTML += "<TD style=\"text-align:center; vertical-align:top;\">&nbsp</TD>";

                    // Description cell blank placeholder
                    sHTML += "<TD style=\"text-align:center; vertical-align:top;\">&nbsp</TD>";


                    // Total in Unit column
                    sHTML += "<TD style=\"text-align:right; vertical-align:top;\">Total:&nbsp</TD>";

                    // Total of Price column?
                    sHTML += "<TD style=\"text-align:right; vertical-align:top;\">" + goTR.FormatCurrField(cTotal.ToString()) + "</TD>";

                    sHTML += "</TR></TABLE>";
                }

                // Notes
                // TLD 2/7/2014 Added borders
                sNotes += "<BR><BR><TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18; border-collapse:collapse; border:1px solid black;\">";
                sNotes += "<TR>";
                sNotes += "<TD style=\"text-align:left; vertical-align:top; border:1px solid black;\">Notes:<BR><BR>" + goTR.Replace(Convert.ToString(doForm.doRS.GetFieldVal("MMO_MiscNotesToVendor")), Constants.vbCrLf, "<BR>") + "</TD>";
                sNotes += "</TR><TABLE><BR>";

                // New Order, Change Order
                sNotes += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18;\">";
                sNotes += "<TR>";
                sNotes += "<TD style=\"text-align:left; vertical-align:top;\">New Order:   " + sNewOrder + "</TD>";
                sNotes += "<TD style=\"text-align:left; vertical-align:top;\">Change Order:   " + sChangeOrder + "</TD>";
                sNotes += "</TR><TABLE>";

                // Table HTML to PDF
                sHTML += sNotes + "</div></body></html>";

                // Convert to PDF
                scriptManager.RunScript("QTConvertToPDF", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, sFile, sHTML);

                // Send to PC Link
                string sPage = "SNQ_" + goData.GenerateID("XX");
                string sMisc = "";
                string sSNQ = "";
                goTR.StrWrite(ref sMisc, "SourceFolder", "temp");
                goTR.StrWrite(ref sMisc, "ref ", "True");
                goTR.StrWrite(ref sMisc, "Open", "True");
                goTR.StrWrite(ref sSNQ, "MISCINFO", sMisc);
                goTR.StrWrite(ref sSNQ, "MODE", "DOWNLOADFILE");
                goTR.StrWrite(ref sSNQ, "FILE", sFile);
                goTR.StrWrite(ref sSNQ, "US_NAME", "Send to PDF queue item");
                goTR.StrWrite(ref sSNQ, "OBJECTTOSEND", "None");
                goTR.StrWrite(ref sSNQ, "SENDNAME", "Purchase Order To PDF '" + sFile + "'");
                goMeta.PageWrite(goP.GetUserTID(), sPage, sSNQ);
                goUI.ExecuteSendPopup = true;
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    goErr.SetError(ex, 45105, sProc);
                    return false;
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_PrintRoutingSheet_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/24/2014 Print PDF Routing Sheet
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sID = Convert.ToString(doForm.doRS.GetCurrentRecID());
            DateTime dtDate; // DTE_DateWon
            string sDate = ""; // DTE_DateWon alpha
            double rQty = 0; // Total of QL SR__Qty where Include is checked
            decimal dCost = 0; // Total of QL CUR_TotalCost where Include is checked
            string sHTML = "<html><body>";
            string sFile = "";

            try
            {
                // Capture QT ID
                sID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                // -----------Save quote just in case
                // ONLY save if user has permissions,
                // otherwise they can still print
                if (goData.GetRecordPermission(sID, "E") == true & doForm.IsDirty)
                {
                    // Check Sent checkbox
                    doForm.doRS.SetFieldVal("CHK_Sent", 1, 2);
                    // Save quote
                    if (doForm.Save(3,true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                        // failed save, write to log
                        goLog.Log(sProc, "QT update for Print PDF failed from QT '" + sID + " with error " + goErr.GetLastError("NUMBER") + "'",1,false,true);
                }
                // ----------End save quote

                // ---------Create file name for PDF
                if (doForm.doRS.IsLinkEmpty("LNK_TO_CO") == false)
                {
                    sFile = doForm.doRS.GetFieldVal("LNK_TO_CO%%TXT_COMPANYNAME", 0, -1, true, -1, "CR", "A_a") + "QT" + doForm.doRS.GetFieldVal("TXT_QUOTENO") + goTR.NowUTC();
                }
                else
                {
                    sFile = "QT" + doForm.doRS.GetFieldVal("TXT_QUOTENO") + goTR.NowUTC();
                }
                // Strips illegal characters from file name
                // so no error when creating temporary file
                sFile = goTR.StripIllegalChars(sFile, "REMOVE") + ".PDF";
                // ---------End Create file name for PDF

                // -----------Get Common fields for each Vendor order
                // Get longdate
                if (Convert.ToString(doForm.doRS.GetFieldVal("DTT_DateWon", clC.SELL_FRIENDLY)) != "")
                {
                    dtDate = Convert.ToDateTime(doForm.doRS.GetFieldVal("DTT_DateWon", 2));
                    sDate = goTR.GetMonthAlpha(dtDate) + " " + goTR.GetDay(dtDate) + ", " + goTR.GetYear(dtDate);
                }

                // TLD 3/17/2014 Changed to total CUR_TotalCost, already includes CUR_Cost * Qty
                // TLD 2/4/2014 Added to SUM SR__Qty and CUR_Cost from QLs where Include is checked
                // Dim doQLRS As New clRowSet("QL", clC.SELL_GROUPBY, "LNK_In_QT='" & sID & "' AND CHK_Include=1", _
                // "SR__LineNo", "SR__Qty|SUM, CUR_Cost|SUM")
                clRowSet doQLRS = new clRowSet("QL", clC.SELL_GROUPBY, "LNK_In_QT='" + sID + "' AND CHK_Include=1", "DTY_Time", "SR__Qty|SUM, CUR_TotalCost|SUM");
                if (doQLRS.GetFirst()==1)
                {
                    // TLD 3/17/2014 Need to Grab CUR_TotalCost, which includes CUR_Cost * Qty
                    // Also SUM does not appear to work, it appears to grab only the 1st one....
                    // dCost = doQLRS.GetFieldVal("CUR_Cost|SUM", 2)
                    dCost = Convert.ToDecimal(doQLRS.GetFieldVal("CUR_TotalCost|SUM", 2));
                    rQty = Convert.ToDouble(doQLRS.GetFieldVal("SR__Qty|SUM", 2));
                }

                // Heading
                sHTML += "<DIV style=\"text-align:center; Width:100%; margin:0 auto;\">";
                sHTML += "<TABLE style=\"border:0px solid black; Width:100%; page-break-inside:avoid; font-size:23; background-color:lightgray;\">";
                sHTML += "<TR><TD style=\"text-align:center;\">";
                sHTML += "M-I-C, Inc. Order Approval/Routing Sheet";
                sHTML += "</TD></TR></TABLE><BR><BR>";

                // Date, etc. Table
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18;\">";
                sHTML += "<TR>";
                sHTML += "<TD style=\"text-align:left; Width:15%;\">Date:</TD>";
                sHTML += "<TD style=\"text-align:left; Width:35%;\">" + sDate + "</TD>";
                sHTML += "<TD style=\"text-align:left; Width:15%;\">&nbsp</TD>";
                sHTML += "<TD style=\"text-align:left; Width:35%;\">&nbsp</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD style=\"text-align:left; Width:15%;\">Customer</TD>";
                sHTML += "<TD style=\"text-align:left; Width:35%;\">" + doForm.doRS.GetFieldVal("LNK_To_CO%%TXT_CompanyName") + "</TD>";
                sHTML += "<TD style=\"text-align:left; Width:15%;\"></TD>";
                sHTML += "<TD style=\"text-align:left; Width:35%;\"></TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD style=\"text-align:left; Width:15%;\">PO #:</TD>";
                sHTML += "<TD style=\"text-align:left; Width:35%;\">" + doForm.doRS.GetFieldVal("TXT_POQuoteCust") + "</TD>";
                sHTML += "<TD style=\"text-align:left; Width:15%;\"></TD>";
                sHTML += "<TD style=\"text-align:left; Width:35%;\"></TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD style=\"text-align:left; Width:15%;\">Quote Sent:</TD>";
                sHTML += "<TD style=\"text-align:left; Width:35%;\">YES/NO</TD>";
                sHTML += "<TD style=\"text-align:left; Width:15%;\">Quote #:</TD>";
                sHTML += "<TD style=\"text-align:left; Width:35%;\">" + doForm.doRS.GetFieldVal("TXT_QuoteNo") + "</TD>";
                sHTML += "</TR></TABLE>";

                sHTML += "<BR><BR>";

                // Note, Initial/Date Table
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18; border-collapse:collapse;\">";
                sHTML += "<TR>";
                sHTML += "<TD>NOTE:</TD>";
                sHTML += "<TD>&nbsp</TD>";
                sHTML += "<TD>&nbsp</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD style=\"text-align:left;\">Initial/Date:</TD>";
                sHTML += "<TD style=\"text-align:left;\">______________________________</TD>";
                sHTML += "</TR><TABLE>";

                sHTML += "<BR><BR>";

                // 1) Review Table
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18; border-collapse:collapse;\">";
                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD style=\"text-align:left;\">1) REVIEW:</TD>";
                sHTML += "<TD style=\"text-align:left;\">MS</TD>";
                sHTML += "<TD style=\"text-align:left;\">__________</TD>";
                sHTML += "<TD style=\"text-align:left;\">ST</TD>";
                sHTML += "<TD style=\"text-align:left;\">__________</TD>";
                sHTML += "<TD style=\"text-align:left;\">PC</TD>";
                sHTML += "<TD style=\"text-align:left;\">__________</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD style=\"text-align:left;\">Billing</TD>";
                sHTML += "<TD style=\"text-align:left;\">M-I-C, Inc</TD>";
                sHTML += "<TD style=\"text-align:left;\">__________</TD>";
                sHTML += "<TD style=\"text-align:left;\">Factory</TD>";
                sHTML += "<TD style=\"text-align:left;\">__________</TD>";
                sHTML += "<TD style=\"text-align:left;\">Drop Ship</TD>";
                sHTML += "<TD style=\"text-align:left;\">__________</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD>&nbsp</TD>";
                sHTML += "<TD style=\"text-align:left;\">Bring into M-I-C</TD>";
                sHTML += "<TD style=\"text-align:left;\">__________</TD>";
                sHTML += "<TD style=\"text-align:left;\">M-Stock</TD>";
                sHTML += "<TD style=\"text-align:left;\">__________</TD>";
                sHTML += "<TD>&nbsp</TD>";
                sHTML += "<TD>&nbsp</TD>";
                sHTML += "</TR></TABLE>";

                sHTML += "<BR><BR>";

                // 2 Order check/Approval By Table
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18; border-collapse:collapse;\">";
                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD style=\"text-align:left;\">2) ORDER CHECK/APPROVAL BY:</TD>";
                sHTML += "<TD style=\"text-align:left;\">__________</TD>";
                sHTML += "<TD style=\"text-align:left;\">Initial/Date</TD>";
                sHTML += "<TD style=\"text-align:left;\">____/____/____</TD>";
                sHTML += "</TR>";

                sHTML += "<TR><TD>&nbsp</TD></TD>";

                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD>&nbsp</TD>";
                sHTML += "<TD style=\"text-align:left;\">Quantity</TD>";
                sHTML += "<TD style=\"text-align:left;\"><U>&nbsp&nbsp" + rQty + "&nbsp&nbsp</U></TD>";
                sHTML += "<TD style=\"text-align:left;\">Pricing</TD>";
                sHTML += "<TD style=\"text-align:left; border-bottom-style:1px solid black;\"><U>&nbsp&nbsp" + doForm.doRS.GetFieldVal("CUR_Total") + "&nbsp&nbsp</U></TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD>&nbsp</TD>";
                sHTML += "<TD style=\"text-align:left;\">M-Cost</TD>";
                sHTML += "<TD style=\"text-align:left; border-bottom-style:1px solid black;\"><U>&nbsp&nbsp" + Strings.FormatCurrency(dCost, 2) + "&nbsp&nbsp</U></TD>";
                sHTML += "<TD style=\"text-align:left;\">GM %</TD>";
                sHTML += "<TD style=\"text-align:left;\"><U>&nbsp&nbsp" + doForm.doRS.GetFieldVal("SR__GMPerc") + "&nbsp&nbsp</U></TD>";
                sHTML += "</TR></TABLE>";

                sHTML += "<BR><BR>";

                // 3 Credit Approval Table(s)
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18; border-collapse:collapse;\">";
                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD style=\"text-align:left;\">3) CREDIT APPROVAL:</TD>";
                sHTML += "</TR></TABLE>";

                sHTML += "<BR><BR>";

                // Acct Dept, etc. Table
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18; border-collapse:collapse;\">";
                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD style=\"text-align:left;\">Acct. Dept</TD>";
                sHTML += "<TD style=\"text-align:left;\">__________</TD>";
                sHTML += "<TD style=\"text-align:left;\">Approved By:</TD>";
                sHTML += "<TD style=\"text-align:left;\">__________</TD>";
                sHTML += "</TR></TABLE>";

                sHTML += "<BR><BR>";

                // Credit Card Table
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18; border-collapse:collapse;\">";
                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD style=\"text-align:left;\">Credit Card</TD>";
                sHTML += "<TD style=\"text-align:left;\">___________________________________ (# & exp)</TD>";
                sHTML += "<TD style=\"text-align:left;\">Initial/Date:</TD>";
                sHTML += "<TD style=\"text-align:left;\">__________</TD>";
                sHTML += "</TR></TABLE>";

                sHTML += "<BR><BR>";

                // Name Table
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18; border-collapse:collapse;\">";
                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD style=\"text-align:left;\">Name</TD>";
                sHTML += "<TD style=\"text-align:left;\">______________________</TD>";
                sHTML += "<TD style=\"text-align:left;\">Processed:</TD>";
                sHTML += "<TD style=\"text-align:left;\">______________________</TD>";
                sHTML += "<TD>&nbsp</TD>";
                sHTML += "</TR></TABLE>";

                sHTML += "<BR><BR>";

                // 4 Order Placement with supplies table
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18; border-collapse:collapse;\">";
                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD style=\"text-align:left; Width:75%\">4) ORDER PLACEMENT WITH SUPPLIERS:</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD style=\"border:1px solid black; text-align:left; rowspan:7; vertical-align:top;\">Assigned To:</TD>";
                sHTML += "<TD style=\"border:1px solid black; text-align:left; rowspan:7;\">M reg# or Factory Reg #: " + doForm.doRS.GetFieldVal("TXT_MICOrderNo") + "<BR><BR>";
                sHTML += "Inv. Coded:<BR><BR></TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD style=\"Width:5%;\"></TD>";
                sHTML += "<TD style=\"border:1px solid black; text-align:left; rowspan:7; vertical-align:top;\">Order(s) Placed by:<BR><BR></TD>";
                sHTML += "<TD style=\"border:1px solid black; text-align:left; rowspan:7; vertical-align:top;\">Factory/PO#: " + doForm.doRS.GetFieldVal("TXT_POToVendor") + "<BR><BR></TD>";
                sHTML += "</TR></TABLE>";
                sHTML += "</DIV>";

                // Get all HTML
                sHTML += "</body></html>";

                // Convert to PDF
                scriptManager.RunScript("QTConvertToPDF", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, sFile, sHTML);

                // Send to PC Link
                string sPage = "SNQ_" + goData.GenerateID("XX");
                string sMisc = "";
                string sSNQ = "";
                goTR.StrWrite(ref sMisc, "SourceFolder", "temp");
                goTR.StrWrite(ref sMisc, "Delete", "True");
                goTR.StrWrite(ref sMisc, "Open", "True");
                goTR.StrWrite(ref sSNQ, "MISCINFO", sMisc);
                goTR.StrWrite(ref sSNQ, "MODE", "DOWNLOADFILE");
                goTR.StrWrite(ref sSNQ, "FILE", sFile);
                goTR.StrWrite(ref sSNQ, "US_NAME", "Send to PDF queue item");
                goTR.StrWrite(ref sSNQ, "OBJECTTOSEND", "None");
                goTR.StrWrite(ref sSNQ, "SENDNAME", "Routing Sheet To PDF '" + sFile + "'");
                goMeta.PageWrite(goP.GetUserTID(), sPage, sSNQ);
                goUI.ExecuteSendPopup = true;
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    goErr.SetError(ex, 45105, sProc);
                    return false;
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_PrintSalesSheet_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/24/2014 Print PDF Sales Sheet
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sID = Convert.ToString(doForm.doRS.GetCurrentRecID());
            DateTime dtDate; // DTE_DateWon
            string sDate = ""; // DTE_DateWon alpha
            double rQty = 0; // Total of QL SR__Qty where Include is checked
                             // Dim dCost As Decimal = 0 'Total of QL CUR_Cost where Include is checked
            string sHTML = "<html><body>";
            string sFile = "";
            string sVendor = ""; // First Quote Line's Vendor
                                 // Quote Line Table
            int i = 0;
            string sDetails = "";
            string sUnitPrice = "";
            string sNetPrice = "";
            string sQLTableHeader = "";
            string sDescription = "";
            string sFirstPage = "";
            string sTerms = "";

            try
            {
                // Capture QT ID
                sID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                // -----------Save quote just in case
                // ONLY save if user has permissions,
                // otherwise they can still print
                if (goData.GetRecordPermission(sID, "E") == true & doForm.IsDirty)
                {
                    // Check Sent checkbox
                    doForm.doRS.SetFieldVal("CHK_Sent", 1, 2);
                    // Save quote
                    if (doForm.Save(3,true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                        // failed save, write to log
                        goLog.Log(sProc, "QT update for Print PDF failed from QT '" + sID + " with error " + goErr.GetLastError("NUMBER") + "'",1,false, true, 0,0);
                }
                // ----------End save quote

                // ---------Create file name for PDF
                if (doForm.doRS.IsLinkEmpty("LNK_TO_CO") == false)
                    sFile = doForm.doRS.GetFieldVal("LNK_TO_CO%%TXT_COMPANYNAME", 25,-1,true,-1,"","") + "QT" + doForm.doRS.GetFieldVal("TXT_QUOTENO") + goTR.NowUTC();
                else
                    sFile = "QT" + doForm.doRS.GetFieldVal("TXT_QUOTENO") + goTR.NowUTC();
                // Strips illegal characters from file name
                // so no error when creating temporary file
                sFile = goTR.StripIllegalChars(sFile, "REMOVE") + ".PDF";
                // ---------End Create file name for PDF

                // -----------Get Common fields for each Vendor order
                // Get longdate
                dtDate = Convert.ToDateTime(doForm.doRS.GetFieldVal("DTT_Time", 2));
                sDate = goTR.GetMonthAlpha(dtDate) + " " + goTR.GetDay(dtDate) + ", " + goTR.GetYear(dtDate);

                // Get First Quote Line's Vendor
                clRowSet doQLRS = new clRowSet("QL", 3, "LNK_In_QT='" + sID + "' AND CHK_Include=1", "SR__LineNo", "LNK_Related_VE%%TXT_VendorName", 1);
                if (doQLRS.GetFirst()==1)
                {
                    sVendor = Convert.ToString(doQLRS.GetFieldVal("LNK_Related_VE%%TXT_VendorName", 1,-1,true,-1,"",""));
                    doQLRS = null/* TODO Change to default(_) if this is not a reference type */;
                }

                // Heading
                sHTML += "<DIV style=\"text-align:center; Width:100%; margin:0 auto;\">";
                sHTML += "<TABLE style=\"border:0px solid black; Width:100%; page-break-inside:avoid; font-size:23; background-color:lightgray;\">";
                sHTML += "<TR><TD style=\"text-align:center;\"><BR>M-I-C, Inc.<BR></TD>";
                sHTML += "<TD><TD style=\"text-align:right;\"><BR>486 Lindbergh Ave. Livermore, Ca  94551<BR></TD>";
                sHTML += "</TR></TABLE><BR><BR>";

                // Invoice To
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18;border-collapse:collapse; border:1px solid black;\">";
                sHTML += "<TR>";
                sHTML += "<TD rowspan=\"8\" style=\"text-align:left; vertical-align:top;\">Invoice To:  <BR><BR>" + goTR.Replace(Convert.ToString(doForm.doRS.GetFieldVal("TXT_BillTo")), Constants.vbCrLf, "<BR>") + "</TD>";
                sHTML += "<TD rowspan=\"1\" style=\"vertical-align:top; border:1px solid black;\">Inv. Code:  </TD>";
                sHTML += "<TD rowspan=\"1\" style=\"vertical-align:top; border:1px solid black;\">Date: " + sDate + "</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                // TLD 2/4/2014 changed to TXT_POQuoteCust
                // sHTML &= "<TD rowspan=""1"" style=""text-align:left; vertical-align:top; border:1px solid black;"">P.O.# :" & doForm.doRS.GetFieldVal("TXT_PONO") & "</TD>"
                sHTML += "<TD rowspan=\"1\" style=\"text-align:left; vertical-align:top; border:1px solid black;\">P.O.#:  " + doForm.doRS.GetFieldVal("TXT_POQuoteCust") + "</TD>";
                sHTML += "<TD rowspan=\"1\" style=\"text-align:left; vertical-align:top; border:1px solid black;\">M-I-C#  : " + doForm.doRS.GetFieldVal("TXT_MICOrderNo") + "</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD colspan=\"2\" style=\"text-align:left; border:1px solid black;\">Placed By: " + doForm.doRS.GetFieldVal("LNK_OriginatedBy_CN%%TXT_NameFirst") + " " + doForm.doRS.GetFieldVal("LNK_OriginatedBy_CN%%TXT_NameLast") + "</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD rowspan=\"4\" colspan=\"2\" style=\"text-align:left; border:1px solid black;\">Phone:  " + doForm.doRS.GetFieldVal("LNK_OriginatedBy_CN%%TEL_BusPhone");
                sHTML += "<BR>Fax:  " + doForm.doRS.GetFieldVal("TEL_Fax");
                sHTML += "<BR>Email:  " + doForm.doRS.GetFieldVal("EML_Email");
                sHTML += "</TD></TR></TABLE>";

                sHTML += "<BR><BR>";

                // Ship To
                sHTML += "<TABLE style=\"Width:100%; page-break-inside:avoid; font-size:18;border-collapse:collapse; border:1px solid black;\">";
                sHTML += "<TR>";
                sHTML += "<TD rowspan=\"6\" style=\"text-align:left; vertical-align:top;\">Ship To:<BR><BR>" + goTR.Replace(doForm.doRS.GetFieldVal("TXT_ShipTo").ToString(), Constants.vbCrLf, "<BR>") + "</TD>";
                sHTML += "<TD rowspan=\"1\" style=\"vertical-align:top; border:1px solid black;\">Ship to Code:  </TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD rowspan=\"1\" style=\"text-align:left; vertical-align:top; border:1px solid black;\">Terms:  " + doForm.doRS.GetFieldVal("LNK_Related_TR%%TXT_TermsName") + "</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD rowspan=\"1\" style=\"text-align:left; vertical-align:top; border:1px solid black;\">FOB:  " + doForm.doRS.GetFieldVal("TXT_FOB") + "</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD rowspan=\"1\" style=\"text-align:left; vertical-align:top; border:1px solid black;\">Shipping Style:  " + doForm.doRS.GetFieldVal("LNK_Related_DM%%TXT_DelMethName") + "</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD rowspan=\"1\" style=\"text-align:left; vertical-align:top; border:1px solid black;\">Taken By:  " + doForm.doRS.GetFieldVal("LNK_Peer_US%%TXT_NameFirst") + " " + doForm.doRS.GetFieldVal("LNK_Peer_US%%TXT_NameLast") + "</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD rowspan=\"1\" style=\"text-align:left; vertical-align:top; border:1px solid black;\">Act Engineer:  " + doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%TXT_NameFirst") + " " + doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%TXT_NameLast") + "</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD rowspan=\"1\" style=\"text-align:left; vertical-align:top; border:1px solid black;\">Vendor:  " + sVendor + "</TD>";
                sHTML += "<TD rowspan=\"1\" style=\"text-align:left; vertical-align:top; border:1px solid black;\">Billing:  " + doForm.doRS.GetFieldVal("LNK_Billing_VE%%TXT_VendorName", 1) + "</TD>";
                sHTML += "</TR>";

                sHTML += "<TR>";
                sHTML += "<TD rowspan=\"1\" style=\"text-align:left; vertical-align:top; border:1px solid black;\">Shipping Payment:  " + doForm.doRS.GetFieldVal("MLS_ShippingPayment", 1) + "</TD>";
                sHTML += "<TD rowspan=\"1\" style=\"text-align:left; vertical-align:top; border:1px solid black;\">Quoted lead time:  " + doForm.doRS.GetFieldVal("TXT_ProposedShipping") + "</TD>";
                sHTML += "</TR></TABLE>";

                sHTML += "<BR><BR>";

                // Quote Line Table
                // Add column headers to table
                sQLTableHeader = "Item,Qty,PC,Description,Unit Price,Logic Notes";

                string[] aQLTableHeader = Strings.Split(sQLTableHeader, ",");
                string sHeader = "";
                //TableRow oTableRow = new TableRow();

                // Format table columns
                sHTML += "<TABLE style=\"Width:100%; font-size:18; border:1px solid black; border-collapse: collapse;\">";
                sHTML += "<THEAD style=\"display: table-header-group;\"><TR style=\"text-align:center; page-break-inside:avoid; vertical-align:top; border:1px solid black;\">";
                for (i = 0; i <= aQLTableHeader.GetUpperBound(0); i++)
                {
                    sHeader = aQLTableHeader[i];
                    if (sHeader != "Logic Notes")
                        sHTML += "<TH style=\"border:1px solid black;\">" + sHeader + "</TH>";
                }
                sHTML += "</TR></THEAD>";

                // ------------Create table column headers for QL print template

                // TLD 2/7/2014 Added LNK_Related_VE
                // TLD 11/8/2013 Increased fontsize for table entries
                // from 19 to 23 below
                // Loop through QLs, add to table
                // Dim oRS As New clRowSet("QL", 3, "CHK_INCLUDE=1 AND LNK_IN_QT='" & sID & "'", , _
                // "SR__LINENO,TXT_MODEL,MMO_Details,SR__Qty,CUR_PriceUnit,TXT_Unit")
                clRowSet oRS = new clRowSet("QL", 3, "CHK_INCLUDE=1 AND LNK_IN_QT='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "SR__LINENO,TXT_MODEL,MMO_Details,SR__Qty,CUR_PriceUnit,TXT_Unit,MMO_LogicNotes,LNK_Related_VE%%TXT_VendorName");
                if (oRS.GetFirst() == 1)
                {
                    oRS.ToTable();
                    DataTable oDT = new DataTable();
                    oDT = oRS.dtTransTable;
                    oRS = null/* TODO Change to default(_) if this is not a reference type */;
                    //oTableRow = new TableRow();

                    

                    foreach (DataRow oRow in oDT.Rows)
                    {
                        sHTML += "<TR style=\"page-break-inside:avoid;\"><TD style=\"text-align:center; vertical-align:top; border:1px solid black;\">";

                        // QL Line Number
                        string sLineNoDecimal = oRow["SR__LINENO"].ToString();
                        var iPosDecimal = Strings.InStr(sLineNoDecimal, ".");
                        if (iPosDecimal > 0)
                            sLineNoDecimal = Strings.Left(sLineNoDecimal, iPosDecimal - 1);
                        sHTML += sLineNoDecimal + "</TD>";

                        // Qty
                        sHTML += "<TD style=\"text-align:center; vertical-align:top; border:1px solid black;\">";
                        sHTML += oRow["SR__Qty"].ToString() + "</TD>";

                        // PC -- don't know what this is yet.....
                        sHTML += "<TD style=\"text-align:center; vertical-align:top; border:1px solid black;\">";
                        sHTML += "&nbsp</TD>";

                        // QL Description Column
                        sHTML += "<TD style=\"text-align:left; vertical-align:top; border:1px solid black;\">";

                        // TLD 2/7/2014 Added for LNK_Related_VE
                        sDescription = oRow["LNK_Related_VE%%TXT_VendorName"].ToString();
                        // Model
                        if (oRow["TXT_Model"].ToString() != "")
                        {
                            if (sDescription != "")
                            {
                                sDescription += "<BR>" + oRow["TXT_Model"].ToString();
                            }
                            else
                            {
                                sDescription = oRow["TXT_Model"].ToString();
                            }
                        }
                        // Details
                        if (oRow["MMO_Details"].ToString() != "")
                        {
                            // TLD 2/7/2014 Added return
                            if (sDescription != "")
                                sDescription += "<BR>" + oRow["MMO_Details"].ToString();
                            else
                                sDescription = oRow["MMO_Details"].ToString();
                        }
                        sDescription = Strings.Replace(sDescription, Strings.Chr(10) + Strings.Chr(13).ToString(), "<BR><BR>");
                        sDescription = Strings.Replace(sDescription, Constants.vbCrLf, "<BR>");
                        sHTML += sDescription + "</TD>";

                        // Unit Price
                        sHTML += "<TD style=\"text-align:left; vertical-align:top; border:1px solid black;\">";
                        sHTML += oRow["CUR_PriceUnit"].ToString() + "</TD>";

                        sHTML += "</TR>";

                        // add QL Logic Notes Row
                        sHTML += "<TR style=\"page-break-inside:avoid;\"><TD colspan=5 style=\"text-align:left; vertical-align:top; border:1px solid black;\">";
                        sHTML += "Price Logic Notes:  " + goTR.Replace(oRow["MMO_LogicNotes"].ToString(), Constants.vbCrLf, "<BR>") + "</TD>";

                        sHTML += "</TR>";
                    }
                    sHTML += "</TABLE>";
                }

                // Table HTML to PDF

                sHTML += "</div></body></html>";

                // Convert to PDF
                scriptManager.RunScript("QTConvertToPDF", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, sFile, sHTML);

                // Send to PC Link
                string sPage = "SNQ_" + goData.GenerateID("XX");
                string sMisc = "";
                string sSNQ = "";
                goTR.StrWrite(ref sMisc, "SourceFolder", "temp");
                goTR.StrWrite(ref sMisc, "Delete", "True");
                goTR.StrWrite(ref sMisc, "Open", "True");
                goTR.StrWrite(ref sSNQ, "MISCINFO", sMisc);
                goTR.StrWrite(ref sSNQ, "MODE", "DOWNLOADFILE");
                goTR.StrWrite(ref sSNQ, "FILE", sFile);
                goTR.StrWrite(ref sSNQ, "US_NAME", "Send to PDF queue item");
                goTR.StrWrite(ref sSNQ, "OBJECTTOSEND", "None");
                goTR.StrWrite(ref sSNQ, "SENDNAME", "Sales Sheet To PDF '" + sFile + "'");
                goMeta.PageWrite(goP.GetUserTID(), sPage, sSNQ);
                goUI.ExecuteSendPopup = true;
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    goErr.SetError(ex, 45105, sProc);
                    return false;
                }
            }

            return true;
        }

        public bool QT_FormControlOnChange_LNK_ORIGINATEDBY_CN_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/24/2014 Custom to fill other fields
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;

            doForm.doRS.ClearLinkAll("LNK_To_CO");

            scriptManager.RunScript("ConnectCompanies", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "LNK_OriginatedBy_CN", "LNK_To_CO");

            // TLD 1/24/2014 Fill Always clear these and refill on change of contact?
            // If doForm.doRS.GetFieldVal("TXT_AddressMailing") = "" Then
            doForm.doRS.SetFieldVal("TXT_Addressmailing", "");
            doForm.doRS.SetFieldVal("TXT_ShipTo", "");
            // If doForm.doRS.GetFieldVal("TXT_AddressMailing") = "" Or doForm.doRS.GetFieldVal("TXT_ShipTo") = "" Then
            scriptManager.RunScript("Quote_FillAddress", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            // End If

            if (Convert.ToString(doForm.doRS.GetFieldVal("Eml_email")) == "")
            {
                scriptManager.RunScript("Quote_FillEmail", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            }
            if (Convert.ToString(doForm.doRS.GetFieldVal("TEL_FAX")) == "")
            {
                scriptManager.RunScript("Quote_FillFax", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            }

            // TLD 1/24/2014 Fill Submitted by if blank
            if (doForm.doRS.IsLinkEmpty("LNK_SubmittedBy_CN"))
                doForm.doRS.SetFieldVal("LNK_SubmittedBy_CN", doForm.doRS.GetFieldVal("LNK_OriginatedBy_CN", 2), 2);
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_LNK_TO_CO_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            // TLD 1/24/2014 Fill Bill To
            scriptManager.RunScript("Quote_FillBillTo", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_SendToQB_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            clRowSet qtRS;
            clRowSet qlRS;
            clRowSet coRS;
            clRowSet cnRS;
            clRowSet moRS;
            clRowSet usRS;
            DataTable dt = new DataTable();
            DataSet ds = new DataSet();
            DataColumn col;
            DataRow row = default(DataRow);
            string sFields;
            string[] aFields;
            string sAddr;
            string[] aAddr;

            try
            {

                // Save form and leave open
                if (doForm.Save(3) == 0)
                {
                }

                // Get Selltis data
                qtRS = doForm.doRS;
                qlRS = new clRowSet("QL", 3, "LNK_In_QT='" + qtRS.GetFieldVal("GID_ID") + "' And MLS_Status=2 And CHK_Include=1", "SR__LineNo", "TXT_Model, MMO_Details, SR__Qty, TXT_Unit, CUR_PriceUnitAfterDisc, CHK_Taxable");
                coRS = new clRowSet("CO", 3, "GID_ID='" + qtRS.GetFieldVal("LNK_To_CO") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_CompanyName, TXT_CustNo, TXT_AddrMailing, " + "TXT_CityMailing, TXT_StateMailing, TXT_ZipMailing, TXT_CountryMailing, " + "TXT_AddrBilling, TXT_CityBilling, TXT_StateBilling, TXT_ZipBilling, TXT_CountryBilling");
                cnRS = new clRowSet("CN", 3, "GID_ID='" + qtRS.GetFieldVal("LNK_OriginatedBy_CN") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_NameFirst, TXT_NameLast, TEL_BusPhone");
                usRS = new clRowSet("US", 3, "GID_ID='" + qtRS.GetFieldVal("LNK_CreditedTo_US") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_Code");

                // Create datatable
                dt.TableName = "QL";
                sFields = "Customer,Transaction Date,RefNumber,PO Number,Terms,To Be Printed,Ship Date," + "BillTo Line1,BillTo Line2,BillTo Line3,BillTo Line4," + "BillTo City,BillTo State,BillTo PostalCode,BillTo Country," + "ShipTo Line1,ShipTo Line2,ShipTo Line3,ShipTo Line4," + "ShipTo City,ShipTo State,ShipTo PostalCode,ShipTo Country," + "Phone,Fax,Email,Contact Name,First Name,Last Name,Rep,Due Date,Ship Method," + "Item,Quantity,Description,Price,FOB,Customer Acct No,Unit of Measure";
                // removed Sales Tax Item

                aFields = Strings.Split(sFields, ",");
                for (int i = 0; i <= aFields.GetUpperBound(0); i++)
                {
                    col = new DataColumn();
                    col.ColumnName = aFields[i];
                    dt.Columns.Add(col);
                }

                // Put data in datatable
                if (qlRS.GetFirst() == 1)
                {
                    do
                    {
                        row = dt.NewRow();
                        row["Customer"] = coRS.GetFieldVal("TXT_CompanyName");
                        row["Transaction Date"] = goTR.DateToString(DateTime.Now.Date,"",ref par_iValid).ToString();
                        row["RefNumber"] = qtRS.GetFieldVal("TXT_MICOrderNo");
                        row["PO Number"] = qtRS.GetFieldVal("TXT_PONo");
                        row["Terms"] = qtRS.GetFieldVal("LNK_Related_TR%%SYS_Name");
                        row["To Be Printed"] = "N";
                        row["Ship Date"] = goTR.DateToString(DateTime.Now.AddDays(5),"",ref par_iValid).ToString();

                        // sAddr = qtRS.GetFieldVal("TXT_BillTo")
                        // aAddr = Split(sAddr, vbCrLf)
                        // If aAddr.GetUpperBound(0) > -1 Then
                        row["BillTo Line1"] = qtRS.GetFieldVal("TXT_BillToComp");
                        // Else
                        // row("BillTo Line1") = ""
                        // End If
                        // If aAddr.GetUpperBound(0) = 1 Then
                        row["BillTo Line2"] = qtRS.GetFieldVal("TXT_BillToAddr1");
                        // Else
                        // row("BillTo Line2") = ""
                        // End If
                        // If aAddr.GetUpperBound(0) = 2 Then
                        row["BillTo Line3"] = qtRS.GetFieldVal("TXT_BillToAddr2");
                        // Else
                        // row("BillTo Line3") = ""
                        // End If
                        // If aAddr.GetUpperBound(0) = 3 Then
                        row["BillTo Line4"] = qtRS.GetFieldVal("TXT_BilltoAddr3");
                        // Else
                        // row("BillTo Line4") = ""
                        // End If
                        row["BillTo City"] = qtRS.GetFieldVal("TXT_BillToCity");
                        row["BillTo State"] = qtRS.GetFieldVal("TXT_BillToState");
                        row["BillTo PostalCode"] = qtRS.GetFieldVal("TXT_BillToZip");
                        row["BillTo Country"] = coRS.GetFieldVal("TXT_CountryBilling");

                        // 'sAddr = coRS.GetFieldVal("TXT_AddrMailing")
                        // sAddr = qtRS.GetFieldVal("TXT_ShipTo")
                        // aAddr = Split(sAddr, vbCrLf)
                        // If aAddr.GetUpperBound(0) > -1 Then
                        row["ShipTo Line1"] = qtRS.GetFieldVal("TXT_ShipToComp");
                        // Else
                        // row("ShipTo Line1") = ""
                        // End If
                        // If aAddr.GetUpperBound(0) = 1 Then
                        row["ShipTo Line2"] = qtRS.GetFieldVal("TXT_ShipToAddr1");
                        // Else
                        // row("ShipTo Line2") = ""
                        // End If
                        // If aAddr.GetUpperBound(0) = 2 Then
                        row["ShipTo Line3"] = qtRS.GetFieldVal("TXT_ShipToAddr2");
                        // Else
                        // row("ShipTo Line3") = ""
                        // End If
                        // If aAddr.GetUpperBound(0) = 3 Then
                        row["ShipTo Line4"] = qtRS.GetFieldVal("TXT_ShipToAddr3");
                        // Else
                        // row("ShipTo Line4") = ""
                        // End If
                        row["ShipTo City"] = qtRS.GetFieldVal("TXT_ShipToCity");
                        row["ShipTo State"] = qtRS.GetFieldVal("TXT_ShipToState");
                        row["ShipTo PostalCode"] = qtRS.GetFieldVal("TXT_ShipToZip");
                        row["ShipTo Country"] = coRS.GetFieldVal("TXT_CountryMailing");

                        // "Phone,Fax,Email,Contact Name,First Name,Last Name,Rep,Due Date,Ship Method," & _
                        // "Item,Quantity,Description,Price,Service Date,FOB,Customer Acct No,Sales Tax Item,To Be E-Mailed,Unit of Measure"
                        row["Phone"] = cnRS.GetFieldVal("TEL_BusPhone"); 
                        row["Fax"] = qtRS.GetFieldVal("TEL_Fax");
                        row["Email"] = qtRS.GetFieldVal("EML_Email");
                        row["Contact Name"] = cnRS.GetFieldVal("TXT_NameFirst") + " " + cnRS.GetFieldVal("TXT_NameLast");
                        row["First Name"] = cnRS.GetFieldVal("TXT_NameFirst");
                        row["Last Name"] = cnRS.GetFieldVal("TXT_NameLast");
                        row["Rep"] = usRS.GetFieldVal("TXT_Code");
                        row["Due Date"] = qtRS.GetFieldVal("DTE_RequestedShip");
                        row["Ship Method"] = qtRS.GetFieldVal("LNK_Related_DM%%SYS_Name");
                        clRowSet rsMO = new clRowSet("MO", 3, "GID_ID='" + qlRS.GetFieldVal("LNK_For_MO") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_ModelName");
                        if (rsMO.GetFirst() == 1)
                            row["Item"] = rsMO.GetFieldVal("TXT_ModelName");
                        else
                            row["Item"] = qlRS.GetFieldVal("TXT_Model");
                        row["Quantity"] = qlRS.GetFieldVal("SR__Qty");
                        row["Description"] = qlRS.GetFieldVal("LNK_Related_VE%%SYS_Name") + " - " + Strings.Replace(Convert.ToString(qlRS.GetFieldVal("MMO_Details")), Constants.vbTab, "  ");
                        row["Price"] = qlRS.GetFieldVal("CUR_PriceUnitAfterDisc");
                        row["FOB"] = qtRS.GetFieldVal("TXT_FOB");
                        row["Customer Acct No"] = coRS.GetFieldVal("TXT_CustNo");
                        // If qlRS.GetFieldVal("CHK_Taxable", 2) = 1 Then
                        // row("Sales Tax Item") = "Y"
                        // Else
                        // row("Sales Tax Item") = "N"
                        // End If
                        // row("Sales Tax Item") = "Non Taxable"
                        row["Unit of Measure"] = qlRS.GetFieldVal("TXT_Unit");
                        dt.Rows.Add(row);
                        if (qlRS.GetNext() == 0)
                            break;
                    }
                    while (true);
                }

                ds.Tables.Add(dt);

                if (goData.DatasetToTextFile(ds,800, "Quote:" + qtRS.GetFieldVal("TXT_QuoteNo")))
                    // goUI.OpenURLExternal("../Pages/diaLoadSend.aspx")
                    doForm.MessageBox("Quote file successfully sent to QuickBooks!");
                else
                    doForm.MessageBox("Error creating the export file for QuickBooks!");
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // TLD 4/28/2014
            bool bDisplayWarning = false;
            string sWarning = "";
            string sID = doForm.GetCreateLinkedSourceRecSUID;
            //Lines specific code
            doForm.MoveToTab(0);
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("LNK_FOR_PD", "LABELCOLOR", color);
            doForm.SetFieldProperty("LNK_RELATED_VE", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode").ToString() == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }
            // TLD 1/24/2014 Fill Bill To if blank
            if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_BillTo")) == "")
            {
                scriptManager.RunScript("Quote_FillBillTo", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            }

            if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_ShipTo")) == "")
            {
                scriptManager.RunScript("Quote_FillAddress", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            }

            // TLD 1/24/2014 Fill Submitted by if blank
            if (doForm.doRS.IsLinkEmpty("LNK_SubmittedBy_CN"))
                doForm.doRS.SetFieldVal("LNK_SubmittedBy_CN", doForm.doRS.GetFieldVal("LNK_OriginatedBy_CN", 2), 2);

            // TLD 1/28/2014 Disable SR__GMPerc
            doForm.SetControlState("SR__GMPerc", 4);
            // TLD 2/5/2014 Disable CUR_MCost
            doForm.SetControlState("CUR_MCost", 4);

            // TLD 4/28/2014 -------------Warnings
            if (doForm.GetMode() == "CREATION")
            {
                // Check purpose for CO Warning message
                if (goTR.GetFileFromSUID(sID) == "AC")
                {
                    var doACRS = new clRowSet("AC", 3, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "MLS_Purpose,LNK_Related_CO%%MMO_Warning");
                    if (doACRS.GetFirst() == 1)
                    {
                        if (Convert.ToInt32(doACRS.GetFieldVal("MLS_Purpose", 2)) == 24)
                        {
                            bDisplayWarning = true;
                            sWarning = Convert.ToString(doACRS.GetFieldVal("LNK_Related_CO%%MMO_Warning", 1));
                        }
                        doACRS = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                }

                // If new created from CN, check CO warning messages
                // Set to display, if necessary
                if (goTR.GetFileFromSUID(sID) == "CN")
                {
                    var doCORS = new clRowSet("CO", 3, "MMO_WARNING<[]>'' AND LNK_Connected_CN='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "MMO_Warning", 1);
                    if (doCORS.GetFirst() == 1)
                    {
                        bDisplayWarning = true;
                        sWarning = Convert.ToString(doCORS.GetFieldVal("MMO_Warning"));
                        doCORS = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                }
            }
            // TLD 4/28/2014 Display CO Warning message if True
            if (bDisplayWarning == true & sWarning != "")
            {
                doForm.MessageBox(sWarning, clC.SELL_MB_OK, "Company Warning");
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/28/2014 copied from main, custom below
            par_bRunNext = false;

            clRowSet doQuote = default(clRowSet);
            clRowSet doRS;
            clArray doLink;
            // Dim sWork As String
            string sSysName = "";
            clRowSet doCO;
            string sTerr;
            clRowSet doMO;
            string sVendor;
            // Dim sWork2 As String
            DateTime dtDateTime;
            // Dim sVend As String
            string sCustCode;

            doQuote = (clRowSet)par_doCallingObject;

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


            // If bDoNotUpdateQuote was not reset earlier due to an error, reset it now
            goP.SetVar("bDoNotUpdateQuote", "0");

            // ------------------ ENFORCE --------------
            // If goP.GetRunMode <> "Import" Then
            if (doQuote.bBypassValidation != true)
            {
                // Enforce mandatory fields
                // CS 7/6/07: In MD
                // If doQuote.GetLinkCount("LNK_CREDITEDTO_US") < 1 Then
                // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "lnk_creditedto_us"), "", "", "", "", "", "", "", "", "lnk_creditedto_us")
                // Return False
                // End If
                // If doQuote.GetLinkCount("LNK_PEER_US") < 1 Then
                // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "lnk_peer_us"), "", "", "", "", "", "", "", "", "lnk_peer_us")
                // Return False
                // End If
                // If doQuote.GetLinkCount("LNK_ORIGINATEDBY_CN") < 1 Then
                // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "lnk_originatedby_cn"), "", "", "", "", "", "", "", "", "lnk_originatedby_cn")
                // Return False
                // End If
                // If doQuote.GetLinkCount("LNK_TO_CO") < 1 Then
                // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "lnk_to_co"), "", "", "", "", "", "", "", "", "lnk_to_co")
                // Return False
                // End If
                // TLD 1/24/2014 Need to set custom DTT_DateWon
                // If goScr.IsSectionEnabled(sProc, par_sSections, "EnforceReasonWonLostAndDateClosed") Then
                switch (doQuote.GetFieldVal("MLS_STATUS", 2))
                {
                    case 2      // Won
                   :
                        {
                            if (Convert.ToInt32(doQuote.GetFieldVal("MLS_REASONWONLOST", 2)) == 0)
                            {
                                goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "mls_reasonwonlost"), "", "", "", "", "", "", "", "", "mls_reasonwonlost");
                                return false;
                            }
                            else
                            {
                                if (goTR.IsDate(Convert.ToString(doQuote.GetFieldVal("DTE_DATECLOSED", 1))) == false | Convert.ToString(doQuote.GetFieldVal("DTE_DateClosed")) == "")
                                {
                                    doQuote.SetFieldVal("DTE_DATECLOSED", goTR.NowLocal(), 2);
                                }
                                // TLD 1/24/2014 Added to set DTT_Datewon
                                if (goTR.IsDate(Convert.ToString(doQuote.GetFieldVal("DTE_DateWon", 1))) == false | Convert.ToString(doQuote.GetFieldVal("DTE_DateWon")) == "")
                                {
                                    doQuote.SetFieldVal("DTT_DateWon", goTR.NowLocal(), 2);
                                }
                            }

                            break;
                        }

                    case 3      // Lost
             :
                        {
                            if (Convert.ToInt32(doQuote.GetFieldVal("MLS_REASONWONLOST", 2)) == 0)
                            {
                                goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "mls_reasonwonlost"), "", "", "", "", "", "", "", "", "mls_reasonwonlost");
                                return false;
                            }
                            else if (goTR.IsDate(Convert.ToString(doQuote.GetFieldVal("DTE_DATECLOSED", 1))) == false | Convert.ToString(doQuote.GetFieldVal("DTE_DateClosed")) == "")
                                doQuote.SetFieldVal("DTE_DATECLOSED", goTR.NowLocal(), 2);
                            break;
                        }
                }
            }


            // ------------ AUTO-FILLED FIELDS ------------
            // Fill CHK_Closed when Status is Open or On Hold
            switch (doQuote.GetFieldVal("MLS_STATUS", 2))
            {
                case 0:
                case 1       // Open, On Hold
               :
                    {
                        doQuote.SetFieldVal("CHK_OPEN", 1, 2);
                        break;
                    }

                default:
                    {
                        doQuote.SetFieldVal("CHK_OPEN", 0, 2);
                        break;
                    }
            }

            // CS: Commented out. Only fill in controlonchange of DM
            // 'Fill Shipping from 'Related Delivery Method' only if something is linked
            // If doQuote.GetLinkCount("LNK_RELATED_DM") > 0 And doQuote.GetFieldVal("CUR_SHIPPING", 2) = 0 Then
            // doQuote.SetFieldVal("CUR_SHIPPING", doQuote.GetFieldVal("LNK_RELATED_DM%%CUR_SHIPPINGCHARGE", 1))
            // End If


            // Fill Month/Year fields used for grouping/totaling in reports
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillYearMonthDayFields",true))
            {
                // 'Old non-UTC-aware code
                // doQuote.SetFieldVal("TXT_YEAR", goTR.GetYear(doQuote.GetFieldVal("DTT_TIME", 2)))
                // doQuote.SetFieldVal("SI__MONTH", goTR.StringToNum(goTR.GetMonth(doQuote.GetFieldVal("DTT_TIME", 2))))
                DateTime _dt = Convert.ToDateTime(doQuote.GetFieldVal("DTT_TIME", 2));
                dtDateTime = goTR.UTC_LocalToUTC(ref _dt);
                doQuote.SetFieldVal("TXT_YEAR", goTR.GetYear(dtDateTime));
                doQuote.SetFieldVal("SI__MONTH", goTR.StringToNum(goTR.GetMonth(dtDateTime),"",ref par_iValid,""));
                if (goData.IsFieldValid("QT", "SI__Day"))
                    doQuote.SetFieldVal("SI__Day", goTR.StringToNum(goTR.GetDay(dtDateTime),"",ref par_iValid,""));
            }


            // ---------------- FILL FIELDS -------------------
            if (goP.GetRunMode() != "Import")
            {
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillSignature"))
                {
                    scriptManager.RunScript("FillSignature", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
                }

                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillSendTo"))
                    scriptManager.RunScript("Quote_FillSendTo", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
                // is below: goScr.RunScript("Quote_FillInvolvesContact", doForm)

                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillRFQNo"))
                {
                    // CS: this was in controlonleave
                    if (doQuote.GetFieldVal("TXT_RFQNO") == "")
                    {
                        doQuote.SetFieldVal("TXT_RFQNO", "Verbal");
                    }
                }

                // CS Debug
                // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before fill involves links" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)



                // LNK_INVOLVES_COMPANY
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillInvolvesCompany"))
                {
                    doLink = new clArray();
                    oTable = null;
                    doLink = doQuote.GetLinkVal("LNK_TO_CO",ref doLink, false,0,-1,"",ref oTable);
                    // If doLink <> vbNull Then
                    doQuote.SetLinkVal("LNK_INVOLVES_CO", doLink);
                    // delete(doLink)
                    doLink = null/* TODO Change to default(_) if this is not a reference type */;
                }

                // LNK_INVOLVES_CONTACT
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillInvolvesContact"))
                {
                    doLink = new clArray();
                    oTable = null;
                    doLink = doQuote.GetLinkVal("LNK_TO_CN", ref doLink, false,0,-1,"",ref oTable);
                    doLink = doQuote.GetLinkVal("LNK_CC_CN", ref doLink, false, 0, -1, "", ref oTable);
                    doLink = doQuote.GetLinkVal("LNK_BC_CN", ref doLink, false, 0, -1, "", ref oTable);
                    // If doLink <> vbNull Then
                    doQuote.SetLinkVal("LNK_INVOLVES_CN", doLink);
                    // delete(doLink)
                    doLink = null/* TODO Change to default(_) if this is not a reference type */;
                }

                // LNK_INVOLVES_USER
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillInvolvesUser"))
                {
                    doLink = new clArray();
                    oTable = null;
                    doLink = doQuote.GetLinkVal("LNK_CREDITEDTO_US",ref doLink, false, 0, -1, "", ref oTable);
                    doLink = doQuote.GetLinkVal("LNK_PEER_US",ref doLink, false, 0, -1, "", ref oTable);
                    // If doLink <> vbNull Then
                    doQuote.SetLinkVal("LNK_INVOLVES_US", doLink);
                    // delete(doLink)
                    doLink = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }


            // --------- UPDATE QUOTE LINES ----------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "UpdateQuoteLines"))
            {
                // Never update Quote Lines in import mode
                if (goP.GetRunMode() != "Import")
                {
                    // Update Quote Lines only if this isn't running as a result of Quote Line recalcing the Quote
                    if (Convert.ToString(goP.GetVar("bDoNotUpdateQuoteLines")) != "1")
                    {
                        // Bypass validation i Quote Lines

                        // CS Debug
                        // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before Quote Line rowset-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)



                        doRS = new clRowSet("QL", 1, "LNK_IN_QT='" + doQuote.GetFieldVal("GID_ID") + "'", "DTT_QTETIME D, SR__LINENO ASC", "*",-1,"","","","","",true,false,false,false,-1,"",false,false,1800);

                        // CS Debug
                        // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("After Quote Line rowset-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                        // CS:See line 12250. This causes an error b/c I can't pass a multi-level link in doQuote rowset.Sent
                        // this as a To Do to MI.

                        if (doRS.GetFirst() == 1)
                        {
                            // CS Debug
                            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before doQuote GetFieldVals-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                            // CS 6/1/09 Get values to set in the QLS once and then set vars in loop below
                            DateTime dtDate = Convert.ToDateTime(doQuote.GetFieldVal("DTE_TIME", 2));
                            DateTime dtTime = Convert.ToDateTime(doQuote.GetFieldVal("TME_TIME", 2));
                            DateTime dtExpClose = Convert.ToDateTime(doQuote.GetFieldVal("DTE_EXPCLOSEDATE", 2));
                            clArray aCompany = (clArray)doQuote.GetFieldVal("LNK_TO_CO", 2);
                            clArray aPeer = (clArray)doQuote.GetFieldVal("LNK_PEER_US", 2);
                            clArray aProject = (clArray)doQuote.GetFieldVal("LNK_RELATED_PR", 2);
                            string sProject = Convert.ToString(doQuote.GetFieldVal("LNK_RELATED_PR", 1)); // get as string to set in project var below
                            clArray aInvolves = (clArray)doQuote.GetFieldVal("LNK_InVOLVES_US", 2);
                            int iStatus = Convert.ToInt32(doQuote.GetFieldVal("MLS_STATUS", 2));
                            clArray aCreditedTo = (clArray)doQuote.GetFieldVal("LNK_CREDITEDTO_US", 2);
                            int iOpen = Convert.ToInt32(doQuote.GetFieldVal("CHK_OPEN", 2));
                            int iReason = Convert.ToInt32(doQuote.GetFieldVal("MLS_REASONWONLOST", 2));

                            // CS Debug
                            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("After doQuote GetfieldVals-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                            // CS 8/8/11: No longer need to get the Company Vendor. It will not be set in linked QLs. QL Vendor will be
                            // filled from MO Vendor only.
                            // doCO = New clRowSet("CO", 3, "GID_ID = '" & doQuote.GetFieldVal("LNK_TO_CO") & "'", , "LNK_IN_TE,LNK_RELATED_VE,TXT_CUSTCODE")
                            doCO = new clRowSet("CO", 3, "GID_ID = '" + doQuote.GetFieldVal("LNK_TO_CO") + "'", null/* Conversion error: Set to default value for this argument */, "LNK_IN_TE,TXT_CUSTCODE");
                            if (doCO.Count() > 0)
                            {
                                sTerr = Convert.ToString(doCO.GetFieldVal("LNK_In_TE"));
                                // CS 6/2/09: Assuming that every QL will have the same 'to company' so can get these values here and use
                                // on all QLs                            
                                // CS 8/8/11 sVend = doCO.GetFieldVal("LNK_Related_VE")
                                sCustCode = Convert.ToString(doCO.GetFieldVal("TXT_CustCode"));
                            }
                            else
                            {
                                sTerr = "";
                                // sVend = ""
                                sCustCode = "";
                            }

                            // CS 6/2/09 Create a string to be used in QL_RecOnSave so that we don't have to get double hops
                            // each time
                            string sWork = "";
                            goTR.StrWrite(ref sWork, "QT_LNK_TO_CO", aCompany.GetItem(1));
                            goTR.StrWrite(ref sWork, "QT_DTT_TIME", dtTime);
                            goTR.StrWrite(ref sWork, "QT_LNK_PEER_US", aPeer.GetItem(1));
                            goTR.StrWrite(ref sWork, "QT_LNK_CREDITEDTO_US", aCreditedTo.GetItem(1));
                            goTR.StrWrite(ref sWork, "QT_LNK_RELATED_PR", sProject);
                            goTR.StrWrite(ref sWork, "CO_LNK_IN_TE", sTerr);
                            // CS 8/8/11: No longer fill QL Related VE with CO's VE. It will be filled
                            // from MO's VE only.
                            // goTR.StrWrite(sWork, "CO_LNK_RELATED_VE", sVend)
                            goTR.StrWrite(ref sWork, "CO_TXT_CUSTCODE", sCustCode);
                            goTR.StrWrite(ref sWork, "QT_SALESTAXPERCENT", doRS.GetFieldVal("SR__SALESTAXPERCENT"));

                            goP.SetVar("QuoteInfo_" + doQuote.GetFieldVal("GID_ID"), sWork);

                            // goP.TraceLine("First found, setting bDoNotUpdateQuote to 1", "", sProc)
                            goP.SetVar("bDoNotUpdateQuote", "1");
                            do
                            {
                                // CS Added: Have to get LNK_TO_CO%%LNK_IN_TE and LNK_FOR_MO%%LNK_RELATED_VE b/c 
                                // multihop links are not allowed in the FIELDS line of doQuote.
                                doRS.SetFieldVal("DTE_QTETIME", dtDate, 2);
                                doRS.SetFieldVal("TME_QTETIME", dtTime, 2);
                                doRS.SetFieldVal("DTE_EXPCLOSEDATE", dtExpClose, 2);
                                doRS.ClearLinkAll("LNK_TO_CO");
                                doRS.SetFieldVal("LNK_TO_CO", aCompany, 2);
                                // doRS.SetFieldVal("LNK_FOR_PD",doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD"))
                                doRS.ClearLinkAll("LNK_PEER_US");
                                doRS.SetFieldVal("LNK_PEER_US", aPeer, 2);
                                doRS.ClearLinkAll("LNK_RELATED_PR");
                                doRS.SetFieldVal("LNK_RELATED_PR", aProject, 2);
                                doRS.ClearLinkAll("LNK_RELATED_TE");
                                doRS.SetFieldVal("LNK_RELATED_TE", sTerr);

                                // TLD 1/28/2014 do NOT clear QL Vendor
                                // doRS.ClearLinkAll("LNK_RELATED_VE")

                                // 'CS Added
                                // 'doMO = New clRowSet("MO", 3, "GID_ID = '" & doRS.GetFieldVal("LNK_FOR_MO") & "'", , "LNK_RELATED_VE")
                                // 'If doMO.Count() > 0 Then
                                // '    sVendor = doMO.GetFieldVal("LNK_Related_VE")
                                // '    doRS.SetFieldVal("LNK_RELATED_VE", sVendor)
                                // 'End If
                                // 'CS 6/2/09: commented above and use variable if set
                                // If goP.GetVar("QuoteInfo_" & doQuote.GetFieldVal("GID_ID")) = "" Then
                                // doMO = New clRowSet("MO", 3, "GID_ID = '" & doRS.GetFieldVal("LNK_FOR_MO") & "'", , "LNK_RELATED_VE")
                                // If doMO.Count() > 0 Then
                                // sVendor = doMO.GetFieldVal("LNK_Related_VE")
                                // doRS.SetFieldVal("LNK_RELATED_VE", sVendor)
                                // End If
                                // 'Else 'CS 8/8/11 commented
                                // '    doRS.SetFieldVal("LNK_RELATED_VE", sVend)
                                // End If


                                // doRS.SetFieldVal("LNK_RELATED_VE", doRS.GetFieldVal("LNK_FOR_MO%%LNK_RELATED_VE"))
                                doRS.ClearLinkAll("LNK_INVOLVES_US");
                                doRS.SetFieldVal("LNK_INVOLVES_US", doRS.GetFieldVal("LNK_CREDITEDTO_US", 2), 2);
                                // doRS.SetFieldVal("LNK_INVOLVES_US",doRS.GetFieldVal("LNK_CREATEDBY_US"))
                                doRS.SetFieldVal("LNK_INVOLVES_US", doRS.GetFieldVal("LNK_PEER_US", 2), 2);
                                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "SetInvolvesUsFromQT"))
                                    // CS 4/20/09
                                    // Set Involves User from QT
                                    // Per DF, this needed b/c users are setting Inv User on QT and this
                                    // is not carried over to the QL and they are using the Inv User
                                    // field for permissions.
                                    doRS.SetFieldVal("LNK_INVOLVES_US", aInvolves, 2);
                                // -------- Status, Reason Won/Lost ----------
                                // In Selltis DB, status and reason won/lost are the same as in the Quote
                                // Remove the following line to manage Status independently from Quote
                                // CS: 7/3/07: Per PJ we are allowing managing QL status independently from QT depending upon
                                // a checkbox.
                                if (goP.GetVar("USEQTSTATUS") == "1")
                                {
                                    doRS.SetFieldVal("MLS_STATUS", iStatus, 2);
                                    doRS.SetFieldVal("MLS_REASONWONLOST", iReason, 2);
                                }
                                // CS 8/27/08: 
                                if (goP.GetVar("USEQTUSERS") == "1")
                                {
                                    doRS.SetFieldVal("LNK_CREDITEDTO_US", aCreditedTo, 2);
                                    doRS.SetFieldVal("LNK_PEER_US", aPeer, 2);
                                }

                                doRS.SetFieldVal("CHK_OPEN", iOpen, 2);
                                // Remove the following line to manage Reason Won/Lost independently from Quote
                                // CS 5/28/10 doRS.SetFieldVal("MLS_REASONWONLOST", iReason, 2)
                                // -------------------------------------------

                                // CS Debug
                                // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before Quote Line commit-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)



                                if (doRS.Commit() != 1)
                                {
                                    goErr.SetWarning(30200, sProc, "", "An error '" + goErr.GetLastError("NUMBER") + "' occurred in procedure '" + goErr.GetLastError("PROCEDURE") + "'." + Constants.vbCrLf + Constants.vbCrLf + "Quote Lines could not be updated." + Constants.vbCrLf + Constants.vbCrLf + goErr.GetLastError("MESSAGE"), "", "", "", "", "", "", "", "", "");
                                    // Exit the script
                                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                                    goP.SetVar("bDoNotUpdateQuote", "0");
                                    return false;
                                }
                                else
                                {
                                }

                                if (doRS.GetNext() == 0)
                                    break;
                            }
                            while (true);
                            goP.SetVar("bDoNotUpdateQuote", "0");
                        }
                        doRS = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                }
            }


            // CS 6/2/09: Reset var
            goP.DeleteVar("QuoteInfo_" + doQuote.GetFieldVal("GID_ID"));


            // CS 7/29/09: SHOULD I RECALC THE QT TOTAL HERE? THIS IS ONLY REALLY NEEDED IN THE CASE THAT A QT ROWSET IS CREATED, A VALUE IS
            // UPDATED ON THE QT AND THE QT IS COMMITTED. IF a QL ROWSET IS CREATED AND EDITED, THEN QT TOTAL DOES GET UPDATED. IF A QL IS 
            // DELETED, QT TOTAL DOES GETS UPDATED. THIS ISSUE IS THAT YOU MIGHT CREATE A QT ROWSET, UPDATE SHIPP CHG AND COMMIT. IN THIS CASE QT TOTAL WILL BE WRONG.
            // IN THE FORM CONTEXT, CALCTOTAL IS CALLED IN QT FORMONSAVE AS WELL AS SOME OF THE BUTTON EVENTS, LIKE LINEDUPLICATE.
            // SETTING A VARIABLE IN QT_FORMONLOADRECORD TO LET ME KNOW THAT WE HIT THAT SCRIPT. IN THAT CASE WE DON'T NEED TO RECALC QT TOTALS HERE AGAIN
            // B/C THEY WILL BE CALCED IN FORMONSAVE.
            // ----------- Recalc Quote totals -------------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "RecalcQuoteTotals"))
            {
                // Cs 11/6/09: Added checking for var(bDoNotRecalcQuoteTotal) set in CalcQuoteTotal. See note there.
                if (Convert.ToString(goP.GetVar("OpenQTForm")) != "1" & Convert.ToString(goP.GetVar("bDoNotRecalcQuoteTotal")) != "1")
                    // CS Debug
                    // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before Recalc Quote Totals-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                    scriptManager.RunScript("CalcQuoteTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, doQuote.GetFieldVal("GID_ID").ToString());
            }


            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


            // Reset var
            // CS 6/23/08 Need to set this in FormAfterSave b/c it is being reset
            // before the Quote is finished all of its processing. Qt saving runs through
            // RecOnSave multiple times.
            // goP.SetVar("USEQTSTATUS", "")
            par_doCallingObject = doQuote;
            return true;
        }

        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/28/2014 Prevents main from running
            // Customizes for Cost and Gross Profit
            par_bRunNext = false;

            Form doForm = null;
            clRowSet doRS1 = null;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3)
            if (par_s1 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
            }

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

            decimal cPriceUnit = default(decimal);
            double rQtyFld = default(double);
            double rDiscPerc = default(double);
            decimal cDiscAddAmt = default(decimal);
            decimal cCostVal = default(decimal);
            decimal cWork = default(decimal);
            decimal cSubtotal = default(decimal);
            double rSalesTaxPerc = default(double);
            // Dim cSKUCost As Decimal
            string sSKUCost;

            // PURPOSE:
            // Calc Subtotal if Include is checked, otherwise enter 0 as subtotal
            // Field 'Price Unit No Disc' = Unit Price before discount
            // Field 'Price Unit' = Unit Price after discount
            // RETURNS:
            // True.

            // goP.TraceLine("", "", sProc)

            // CS Need to check if coming from RecOnSave b/c in that case we are working with a rowset and 
            // otherwise we are on a form.
            if (par_s1 != "doRS")
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_INCLUDE", 2)) != 1)
                {
                    // Set calculated fields to 0
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                    // TLD 1/28/2014 Don't clear cost, user enters unit cost
                    // doForm.doRS.SetFieldVal("CUR_COST", 0, 2)
                    doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_SALESTAX", 0, 2);
                }
                else
                {
                    cPriceUnit = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2));
                    rQtyFld = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));
                    rDiscPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__DISCPERCENT", 2));
                    cDiscAddAmt = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_DISCADDLAMT", 2));
                    // TLD 1/28/2014 NEVER calculate total cost based on model cost
                    // User enters unit cost always
                    // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                    // cCostVal = doForm.doRS.GetFieldVal("CUR_COST", 2)
                    // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                    sSKUCost = Convert.ToString(doForm.doRS.GetFieldVal("CUR_COST"));
                    // If sSKUCost <> "" Then
                    // cSKUCost = goTR.StringToCurr(sSKUCost)
                    // Else
                    // cSKUCost = 0
                    // End If
                    // cCostVal = cSKUCost * rQtyFld
                    // cCostVal = sSKUCost * rQtyFld;
                    sSKUCost = sSKUCost.Replace("$", "");
                    //cSKUPrice = decimal.Parse(sSKUPrice);
                    cCostVal = decimal.Parse(sSKUCost) * Convert.ToDecimal(rQtyFld);
                    // doForm.doRS.SetFieldVal("CUR_COST", cCostVal)
                    // TLD 3/17/2014 Set CUR_TotalCost
                    doForm.doRS.SetFieldVal("CUR_TotalCost", cCostVal);

                    // Copy Cost from SKU and multiply it by Qty if the user edited Qty or if Qty is 0
                    // (we set it to 0 above if it is blank)
                    // bUpdCostVal is set to 1 in ControlOnLeave script for 'For Model'.

                    // Check if form variable has been set otherwise get an error comparing srQtyEnterVal as 
                    // a string ("") to a double (rQtyFld)
                    // Per MI, we can get rid of the form var dealing with changing the qty. We will always
                    // recalc regardless of it.
                    // If doForm.oVar.GetVar("srQtyEnterVal") <> "" Then
                    // If (rQtyFld <> doForm.oVar.GetVar("srQtyEnterVal")) Or (doForm.oVar.GetVar("bUpdCostVal")) = 1 Then
                    // If doForm.oVar.Getvar("bUpdCostVal") <> "" Then
                    // If (doForm.oVar.GetVar("bUpdCostVal")) = 1 Then
                    // CS: Moving this to QL_FormControlOnChange_USEMODELPRICE. When the user clicks this button the linked model's cost
                    // will be used.
                    // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                    // If sSKUCost <> "" Then 'CS added this If b/c if coming from QL_FormOnLoad the value
                    // 'is blank for cost and get error
                    // cSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")

                    // cCostVal = cSKUCost * rQtyFld
                    // doForm.doRS.SetFieldVal("CUR_COST", cCostVal)
                    // End If
                    // End If

                    // Calculate unit price after discount
                    cWork = cPriceUnit - (cPriceUnit * Convert.ToDecimal(rDiscPerc) / 100);
                    doForm.doRS.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                    // Calculate Subtotal
                    cSubtotal = (cPriceUnit * Convert.ToDecimal(rQtyFld)) - (cPriceUnit * Convert.ToDecimal(rQtyFld) * Convert.ToDecimal(rDiscPerc) / 100) + cDiscAddAmt;
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                    // Calc Gross Profit
                    // TLD 2/25/2014 Put this back to normal, I think?  See notes below
                    // TLD 1/28/2014 Unit Price - Unit Cost
                    // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                    // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS 6/13/07: Added rQtyField per DF
                    cWork = cSubtotal - cCostVal;
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));
                    // doForm.dors.SetFieldVal("CUR_GROSSPROFIT", cPriceUnit - sSKUCost)
                    // TLD 2/25/2014 Now they want Unit Price - (cost * Qty) -- cCostVal is cost * qty
                    // TLD 2/25/2014 don't think this is right either -- if we use this, then Unit Price
                    // does NOT include qty, so the Gross Profit is NOT correct, so put this back to normal
                    // doForm.dors.SetFieldVal("CUR_GROSSPROFIT", cPriceUnit - cCostVal)

                    // Sales tax
                    if (Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_TAXABLE", 2)))
                    {
                        // CS 6/2/09: Get value from variable if set
                        if (Convert.ToString(goP.GetVar("QuoteInfo")) == "")
                        {
                            rSalesTaxPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));
                        }
                        else
                        {
                            rSalesTaxPerc = goTR.StringToNum(goTR.StrRead(Convert.ToString(goP.GetVar("QuoteInfo")), "QT_SALESTAXPERCENT", "", false), "", ref par_iValid, "");
                        }


                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", cSubtotal * Convert.ToDecimal(rSalesTaxPerc) / 100);
                    }
                    else
                    {
                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", 0);
                    }
                }
            }
            else if (Convert.ToInt32(doRS1.GetFieldVal("CHK_INCLUDE", 2)) != 1)
            {
                // Set calculated fields to 0
                doRS1.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                doRS1.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                doRS1.SetFieldVal("CUR_COST", 0, 2);
                doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                doRS1.SetFieldVal("CUR_SALESTAX", 0, 2);
            }
            else
            {
                cPriceUnit = Convert.ToDecimal(doRS1.GetFieldVal("CUR_PRICEUNIT", 2));
                rQtyFld = Convert.ToDouble(doRS1.GetFieldVal("SR__QTY", 2));
                rDiscPerc = Convert.ToDouble(doRS1.GetFieldVal("SR__DISCPERCENT", 2));
                cDiscAddAmt = Convert.ToDecimal(doRS1.GetFieldVal("CUR_DISCADDLAMT", 2));
                // cCostVal = doRS1.GetFieldVal("CUR_COST", 2)
                // TLD 1/28/2014 NEVER calculate total cost based on model cost
                // User enters unit cost ALWAYS
                // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                // Dim sModel As String = goP.GetVar("QuoteLineInfo_" & doRS1.getfieldval("GID_ID"))
                // If sModel = "" Then
                // sSKUCost = doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                // Else
                // sSKUCost = goTR.StrRead(sModel, "MO_CUR_COST", , False)
                // End If
                // If sSKUCost <> "" Then
                // cSKUCost = goTR.StringToCurr(sSKUCost)
                // Else
                // cSKUCost = 0
                // End If
                sSKUCost = Convert.ToString(doRS1.GetFieldVal("CUR_COST", 2));
                // cCostVal = cSKUCost * rQtyFld
                cCostVal = Convert.ToDecimal(sSKUCost) * Convert.ToDecimal(rQtyFld);
                // doRS1.SetFieldVal("CUR_COST", cCostVal)

                // 'CS: only set the cost if it is 0
                // If cCostVal = 0 Then
                // cSKUCost = goTR.StringToCurr(doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST"))
                // cCostVal = cSKUCost * rQtyFld
                // doRS1.SetFieldVal("CUR_COST", goTR.RoundCurr(cCostVal))
                // End If

                // Calculate unit price after discount
                cWork = cPriceUnit - (cPriceUnit * Convert.ToDecimal(rDiscPerc) / 100);
                doRS1.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                // Calculate Subtotal
                cSubtotal = (cPriceUnit * Convert.ToDecimal(rQtyFld)) - (cPriceUnit * Convert.ToDecimal(rQtyFld) * Convert.ToDecimal(rDiscPerc) / 100) + cDiscAddAmt;
                doRS1.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                // Calc Gross Profit
                // TLD 2/25/2014 Put this back to normal, I think?  See notes below
                // TLD 1/28/2014 Unit Price - Unit Cost
                // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS 6/13/07: Added rQtyField per DF
                cWork = cSubtotal - cCostVal;
                doRS1.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork), 2);
                // doForm.dors.SetFieldVal("CUR_GROSSPROFIT", cPriceUnit - sSKUCost)
                // TLD 2/25/2014 Now they want Unit Price - (cost * Qty) -- cCostVal is cost * qty
                // TLD 2/25/2014 don't think this is right either -- if we use this, then Unit Price
                // does NOT include qty, so the Gross Profit is NOT correct, so put this back to normal
                // dors1.SetFieldVal("CUR_GROSSPROFIT", cPriceUnit - cCostVal)

                // Sales tax
                if (Convert.ToBoolean(doRS1.GetFieldVal("CHK_TAXABLE", 2)))
                {
                    // CS 6/2/09:
                    if (goP.GetVar("QuoteInfo").ToString() == "")
                    {
                        // CS 10/19/10: Check if have a linked QT. Should always, but causes error if not.
                        if (doRS1.GetLinkCount("LNK_IN_QT") > 0)
                        {
                            rSalesTaxPerc = Convert.ToDouble(doRS1.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));
                        }
                        else
                        {
                            rSalesTaxPerc = 0;
                        }
                    }
                    else
                    {
                        rSalesTaxPerc = goTR.StringToNum(goTR.StrRead(Convert.ToString(goP.GetVar("QuoteInfo")), "QT_SALESTAXPERCENT", null, false), "", ref par_iValid, "");

                    }
                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                    doRS1.SetFieldVal("CUR_SALESTAX", cSubtotal * (Convert.ToDecimal(rSalesTaxPerc)) / 100);
                }
                else
                {
                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0);
                    doRS1.SetFieldVal("CUR_SALESTAX", 0);
                }
            }
            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)
            if (par_s1 == "doRS")
            {
                par_doCallingObject = doRS1;
            }
            else
            {
                par_doCallingObject = doForm;
            }
            return true;
        }

        public bool Quotline_CalcTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool Quotline_ConnectVendors_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext , ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // TLD 5/1/2014 Want to fill it, but ONLY if it is blank
            // so they can overwrite it....
            // TLD 4/25/2014 Now they want to fill it.....
            // TLD 1/24/2014 Don't run
            par_bRunNext = false;

            // CS 8/8/11: No longer fill QL VE from linked Company

            // TLD 5/1/2014 Don't clear, ONLY fill if blank
            // Link Vendors from linked Model 
            // doRS.ClearLinkAll("LNK_RELATED_VE")         'Clear any existing connected Vendors
            // CS 6/2/09: If var set, pull vendors from it
            if (doRS.IsLinkEmpty("LNK_Related_VE"))
            {
                if (goP.GetVar("QuoteLineInfo_" + Convert.ToString(doRS.GetFieldVal("GID_ID"))).ToString() == "")
                    doRS.SetFieldVal("LNK_RELATED_VE", doRS.GetFieldVal("LNK_FOR_MO%%LNK_RELATED_VE", 2), 2);
                else
                    doRS.SetFieldVal("LNK_RELATED_VE", goTR.StrRead(Convert.ToString(goP.GetVar("QuoteLineInfo_" + doRS.GetFieldVal("GID_ID"))), "MO_LNK_RELATED_VE", null, false),0);
            }
            par_doCallingObject = doRS;
            return true;
        }

        public bool Quotline_FillItem_Pre(ref object par_doCallingObject, ref object par_oReturn , ref bool par_bRunNext , ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/28/2014 Prevents main from running
            // Customizes to NOT fill cost from MO
            par_bRunNext = false;

            // goP.TraceLine("", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            // PURPOSE:
            // Fill the TXT_MODEL field
            // RETURNS:
            // True.

            // CS: Original code 10/10/07
            // If Trim(doForm.dors.GetFieldVal("TXT_MOdel")) = "" Then
            // If doForm.dors.GetLinkCount("LNK_FOR_MO") > 0 Then
            // 'PJ 10/12/01 Remmed end of line per BKG request
            // 'MI 4/2/08: MMO_Description is replaced with TXT_Description as of 4/3/08.
            // 'If you reenable this code, you MUST test:
            // If goData.IsFieldValid("MO", "TXT_Description") Then
            // '-> Use TXT_Description field's value 
            // Else
            // '-> Use MMO_Description
            // End If
            // doForm.dors.SetFieldVal("TXT_MODEL", doForm.dors.GetFieldVal("LNK_FOR_MO%%MMO_DESCRIPTION")) '& " - " & doForm.GetFieldVal("LNK_FOR_MO%%TXT_MODELNAME")
            // End If
            // End If

            string sSKUPrice;
            decimal cSKUPrice = default(decimal);
            // Dim sSKUCost As String
            // Dim cSKUCost As Decimal
            // Dim rQtyFld As Decimal
            // Dim cCostVal As Decimal
            string sModel;
            string sLinkedModel = "";
            string sUnit;
            clRowSet doRSModel;


            // If no model selected, return
            if (doForm.doRS.GetLinkCount("LNK_FOR_MO") == 0)
                return true;


            if (goData.IsFieldValid("MO", "TXT_Description"))
            // Cs 6/22/09
            {
                doRSModel = new clRowSet("MO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_FOR_MO") + "'", null, "TXT_Description,txt_unittext,lnk_of_pd,cur_price,cur_cost");

            }
            else
            {
                doRSModel = new clRowSet("MO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_FOR_MO") + "'", null, "MMO_Description,txt_unittext,lnk_of_pd,cur_price,cur_cost");

            }
            if (doRSModel.GetFirst() != 1)
                return true;

            // Set Unit price to linked model's price
            // sSKUPrice = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_PRICE", 1) '6/22/09
            sSKUPrice = Convert.ToString(doRSModel.GetFieldVal("CUR_PRICE", 2));
            if (sSKUPrice != "")
            {
                //cSKUPrice = decimal.Parse(sSKUPrice);
                //cSKUPrice = Convert.ToDecimal(sSKUPrice.Replace("$", "").Trim());
                sSKUPrice = sSKUPrice.Replace("$", "");
                cSKUPrice = Convert.ToDecimal(sSKUPrice);
            }

            doForm.doRS.SetFieldVal("CUR_PRICEUNIT", cSKUPrice, 2);

            // TLD 1/28/2014 User enters Unit Cost, so don't need to set
            // Set Cost to linked model's cost
            // Set Cost to linked model's cost
            // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST", 1) 'Cs 6/22/09
            // sSKUCost = doRSModel.GetFieldVal("CUR_COST", 1)
            // If sSKUCost <> "" Then
            // cSKUCost = sSKUCost
            // rQtyFld = doForm.doRS.GetFieldVal("SR__QTY")
            // cCostVal = cSKUCost * rQtyFld
            // doForm.doRS.SetFieldVal("CUR_COST", goTR.RoundCurr(cCostVal))
            // End If

            // If txt_model is blank set to linked model's description
            sModel = Convert.ToString(doForm.doRS.GetFieldVal("TXT_Model"));
            if (sModel == "")
            {
                // MI 4/2/08 MO.TXT_Description replaces MMO_Description as of 4/3/08, but MMO_Description remains in existing DBs.
                if (goData.IsFieldValid("MO", "TXT_Description"))
                // Cs 6/22/09                
                {
                    sLinkedModel = Convert.ToString(doRSModel.GetFieldVal("TXT_Description", 1));
                }
                else
                // sLinkedModel = doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_DESCRIPTION", 1)
                {
                    sLinkedModel = Convert.ToString(doRSModel.GetFieldVal("MMO_DESCRIPTION", 1));
                }
                doForm.doRS.SetFieldVal("TXT_Model", sLinkedModel);
            }

            // Fill unit with linked model's unit
            // sUnit = doForm.doRS.GetFieldVal("LNK_FOR_MO%%txt_unittext") '6/22/09
            sUnit = Convert.ToString(doRSModel.GetFieldVal("txt_unittext"));
            doForm.doRS.SetFieldVal("TXT_Unit", sUnit);

            scriptManager.RunScript("Quotline_CheckTaxable", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            scriptManager.RunScript("Quotline_ConnectVendors", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);
            scriptManager.RunScript("Quotline_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null);        // runs CalcTotal
                                                                  // goScr.RunScript("Quotline_FillItem", doForm)

            // Set Product link to Model's Product
            doForm.doRS.ClearLinkAll("LNK_FOR_PD");
            // doForm.doRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2) 'cs 6/22/09
            doForm.doRS.SetFieldVal("LNK_FOR_PD", doRSModel.GetFieldVal("LNK_OF_PD", 2), 2);
            par_doCallingObject = doForm;
            return true;
        }

        public bool Utility_RunImportUtility(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                goUI.OpenURLExternal("../Pages/cus_diaImportMan.aspx", "Selltis", "height=840,width=1250,left=100,top=100,status=yes,location=no,toolbar=no,resizable=yes,titlebar=no,dependent=yes");
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote_draft.docx";
                }
            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote.docx";
                }
            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                       
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);

            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_RELATED_PD,SR__QTY,CUR_UNITPRICE,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("CUR_UNITPRICE", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UNITPRICE|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UNITPRICE|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curValue);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";
            Refresh_OPPTotal(doForm.doRS);
            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("CUR_UNITPRICE", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);

            //This will generate line no's in mobile.
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }

        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }

        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }
            if (doForm.doRS.IsLinkEmpty("LNK_RELATED_VE"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Vendor");
                doForm.FieldInFocus = "LNK_RELATED_VE";
                par_doCallingObject = doForm;
                return false;
            }
            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            //double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string VE_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_VE%%GID_ID"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));
            string sMODescription = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%MMO_SPECIFICATIONS"));
            string sModelText = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_DESCRIPTION"));


            //if (curUnitPrice <= 0)
            //{
            //    goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
            //    doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
            //    par_doCallingObject = doForm;
            //    return false;
            //}

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,LNK_FOR_PD,LNK_RELATED_VE,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,MMO_DETAILS,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);
            rsQL.SetFieldVal("LNK_RELATED_VE", VE_Gid);
            rsQL.SetFieldVal("LNK_FOR_PD", PD_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);

            rsQL.SetFieldVal("SR__Qty", dQty);
            //rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);
            rsQL.SetFieldVal("MMO_DETAILS", sMODescription);

            rsQL.SetFieldVal("TXT_Model", sModelText);
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";
            Refresh_QouteTotal(doForm.doRS);
            par_doCallingObject = doForm;
            return true;

        }

        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEUNITPRICE", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);


            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.ClearLinkAll("LNK_RELATED_VE");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }
        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;


            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

            }
            par_doCallingObject = doRS;
            par_bRunNext = false;
            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            clRowSet doRS1 = null;


            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS1.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doRS1.SetFieldVal("CUR_VALUE", curValue);
                    doRS1.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doRS1;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                    doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doForm;
            }

            return true;

        }
        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_ENDUSER_CO", rsOP.GetFieldVal("LNK_ENGINEERING_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_DISTREP_CO", rsOP.GetFieldVal("LNK_DISTRIBUTOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECOMPLETED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            //doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    //doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("CUR_PRICEUNIT", rsOL.GetFieldVal("CUR_UNITPRICE", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }
        public bool GenerateSysName_Post(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            // MI 3/12/10 Added CASE "DF", modified Case Else to generate Name based on TXT_<FileName>Name field if exists.
            // CS 6/2/09 Modified QL case
            // CS 8/25/08 Case US: Added Title text
            // CS 8/20/08 Case CN: added Title Text
            // MI 11/14/07 Case CN: added TXT_ContactCode.
            // MI 10/15/07 Appended ' UTC' to all datetimes
            // MI 6/22/07 Changes to QT, OP, PR, PD, MO
            // MI 4/17/07 Added Phone Co to CN; Phone to CO ; removed padding from and to to 4 in MS; Status to OP, PR, QT, TD(?)
            // MI 3/9/07 Removed ellipsis from Co name in AC name.
            // MI 3/1/07 Added Contact, Company to AC Name.
            // MI 2/1/07 Added Date, Originator to Project name
            // MI 9/15/06 Updated QL, WT SYS_Name formats.
            // MI 7/25/06 Added raising error when field is not in the rowset.
            // MI 7/24/06 Mods. 
            // MI 7/20/06 Created in clScripts.
            // MI 7/20/06 Finished, tested, moved from clData to clScripts and renamed from GetCurrentRecordName to GetSysName.
            // MI 7/17/06 Started making this work in SellSQL.

            // AUTHOR: MI
            // PURPOSE:
            // Send back via the par_oReturn parameter the 'User Friendly' Name of the current record in par_oRowset.
            // This is to be called from each RecordOnSave script, but can be called
            // from any other code to generate a SYS_Name value. Do NOT set the name in 
            // the par_doCallingObject rowset or the script won't be usable simply for evaluating the returned
            // string.
            // IMPORTANT: Keep this "in sync" with clScripts.GetDefaultSort(), which is called from
            // clData.GetDefaultSort.
            // PARAMETERS:
            // par_doCallingObject: rowset object (for example, 'doRS'). The rowset must contain
            // all links and fields being referenced in the code below or error 45163 will be
            // raised. This can be achieved by putting '**' in the FIELDS parameter of the rowset, but 
            // avoid this when possible for performance reasons. The object, declared ByRef to conserve
            // resources by avoiding duplicating the object in memory, should not be altered directly
            // by this method (the purpose of the method is to return the name, not set it), but check
            // the code below to be sure.
            // par_doArray: not used
            // par_s1 - 5: not used
            // par_oReturn: String containing the generated SysName.
            // RETURNS:
            // True as a result. Returns friendly name or an empty string if the
            // filename is invalid via par_oReturn parameter.
            // EXAMPLE:
            // 'From a RecordOnSave script (not tested):
            // Dim sName as string = goScr.RunScript("GenerateSysName", doRS)
            // NOTES:
            // When a "Name" that is built with this method
            // is displayed in a View or linkbox and the same Name field is used
            // to sort the View or linkbox, at least the first field should match
            // the first field defined in clData::LKGetSortValue(). Otherwise what's
            // displayed will appear to be sorted arbitrarily.
            // NOTE 2:  
            // Links will not be tested because they are loaded automatically, but 
            // currently there is a bug in clRowset. RH working on this.

            string sProc = "clScripts:GenerateSysName";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS 11152016
            // par_bRunNext = False
            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";
            clRowSet doLink;
            int iLen;

            // We assume that sFileName is valid. If this is a problem, test it here and SetError.

            switch (Strings.UCase(sFileName))
            {
                case "CT"     // ==> Country
               :
                    {
                        par_bRunNext = false;

                        sTemp = doRS.GetFieldVal("TXT_COUNTRYNAME").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_COUNTRYCODE").ToString();
                        if (sTemp2 == "")
                            sTemp2 = "?";

                        sResult = sTemp + " (" + sTemp2 + ")";
                        break;
                    }

                case "SA"   // ==> State
         :
                    {
                        par_bRunNext = false;

                        sTemp = doRS.GetFieldVal("TXT_STATENAME").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_CODE").ToString();
                        if (sTemp2 == "")
                            sTemp2 = "?";

                        sResult = sTemp + " (" + sTemp2 + ")";
                        break;
                    }

                case "CY"   // ==> Customer Type
         :
                    {
                        par_bRunNext = false;

                        sTemp = doRS.GetFieldVal("TXT_CUSTOMERTYPENAME").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_CODE").ToString();
                        if (sTemp2 == "")
                            sTemp2 = "?";

                        sResult = sTemp + " (" + sTemp2 + ")";
                        break;
                    }

                case "OP":
                    {
                        // ==> OPP NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+LNK_For_Company%%TXT_CompanyName+" "+...
                        // LNK_For_Product%%TXT_ProductName+" "+CUR_Value
                        // OPP		<For - Company - TXT_CompanyName> <For - Product - TXT_ProductName> CUR_ValueIndex (MLS_Status)  
                        // OPP-COMPANY-0						OPP-PRODUCT-0

                        par_bRunNext = false;
                        if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_CreditedTo_US");

                        }
                        if (!doRS.IsLoaded("LNK_For_CO"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_For_CO");

                        }
                        if (!doRS.IsLoaded("LNK_For_PD"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_For_PD");

                        }
                        if (!doRS.IsLoaded("DTT_Time"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".DTT_Time");

                        }
                        if (!doRS.IsLoaded("CUR_Value"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".CUR_Value");

                        }
                        if (!doRS.IsLoaded("MLS_Status"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".MLS_Status");

                        }

                        // LNK_CreditedTo_US%%TXT_Code
                        sTemp = Convert.ToString(doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, true, 1));
                        if (sTemp == "")
                            // No records linked
                            sTemp = "?";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", null, "TXT_Code", 1);
                            if (doLink.Count() > 0)
                                sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                            else
                                sTemp = "?";
                        }

                        // LNK_For_CO%%TXT_CompanyName
                        sTemp2 = Convert.ToString(doRS.GetFieldVal("LNK_For_CO", 1, 1, false, 1));
                        if (sTemp2 == "")
                            // No records linked
                            sTemp2 = "";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", null, "TXT_CompanyName", 1);
                            if (doLink.Count() > 0)
                                sTemp2 = doLink.GetFieldVal("TXT_CompanyName", 1, 22).ToString();
                            else
                                sTemp2 = "";
                        }

                        // LNK_For_Product%%TXT_ProductName
                        sTemp3 = doRS.GetFieldVal("LNK_For_PD", 1, 1, false, 1).ToString();
                        if (sTemp3 == "")
                            // No records linked
                            sTemp3 = "";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("PD", 3, "GID_ID='" + sTemp3 + "'", null, "TXT_ProductName", 1);
                            if (doLink.Count() > 0)
                                sTemp3 = doLink.GetFieldVal("TXT_ProductName", 1, 14).ToString();
                            else
                                sTemp3 = "";
                        }

                        // Company (23)   '25
                        // Date (15)      '11
                        // Credited To User (5)
                        // Product (15)   '17
                        // Value (13)
                        // Status (9)
                        // *** MI 10/4/07 Added LocalToUTC conversion
                        // sResult = sTemp2 & " " & _
                        // goTR.DateToString(doRS.GetFieldVal("DTE_Time", clC.SELL_SYSTEM), "YYYY-MM-DD") & " " & _
                        // sTemp & " " & _
                        // sTemp3 & " " & _
                        // goTR.Pad(doRS.GetFieldVal("CUR_Value"), 11, " ", "L")\                      
                        int par_iValid = 0;
                        string par_sdlim = "";
                        DateTime sDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM));
                        sResult = sTemp2 + " " + Strings.Left(goTR.DateTimeToSysString(goTR.UTC_LocalToUTC(ref sDate), ref par_iValid, ref par_sdlim), 10) + " GMT " + sTemp + " " + sTemp3 + " " + goTR.Pad(doRS.GetFieldVal("CUR_Value").ToString(), 11, " ", "L");

                       

                        sResult += " [" + doRS.GetFieldVal("MLS_STATUS", 1, 8) + "]";
                        break;
                    }
                case "QT":
                    //==> QUOTE NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+
                    //					LNK_To_Company%%TXT_CompanyName+" "+CUR_Total

                    if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("LNK_To_CO"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_To_CO");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("DTT_Time"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".DTT_Time");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_QuoteNo"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_QuoteNo");
                        ///35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("CUR_Total"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".CUR_Total");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("MLS_STATUS"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".MLS_STATUS");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    //LNK_CreditedTo_US%%TXT_Code
                    sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp))
                    {
                        //No records linked
                        sTemp = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                        }
                        else
                        {
                            sTemp = "?";
                        }
                    }

                    //LNK_To_CO%%TXT_CompanyName
                    sTemp2 = doRS.GetFieldVal("LNK_To_CO", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp2))
                    {
                        //No records linked
                        sTemp2 = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CompanyName", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp2 = doLink.GetFieldVal("TXT_CompanyName").ToString();
                        }
                        else
                        {
                            sTemp2 = "?";
                        }
                    }


                    //Company 17 '21
                    //Date 15    '11
                    //Cred User 6
                    //Quote No 16
                    //Total 15
                    //Status 11
                    //Total: 80
                    sResult = goTR.Pad(sTemp2, 16, "", "R", true) + " " + goTR.Pad(sTemp3, 14, "", "R", true) + " " + goTR.Pad(sTemp, 4, "", "R", true) + " [" + goTR.Pad(doRS.GetFieldVal("TXT_QuoteNo").ToString(), 14, "", "R", true) + "] " + goTR.Pad(doRS.GetFieldVal("CUR_Total").ToString(), 13, "", "L", true) + " [" + doRS.GetFieldVal("MLS_STATUS", 0, 10).ToString() + "]";

                    break;
                //case "CN":

                //    sTemp2 = Convert.ToString(doRS.GetFieldVal("TXT_NameLast", 2));
                //    sResult = sTemp2;
                //    par_bRunNext = false;
                //    break;
                case "CN":
                    //==> CONTACT NEW:	TXT_NameLast+", "+TXT_NameFirst+" "+TXT_ContactCode	
                    if (!doRS.IsLoaded("TXT_NameLast"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_NameLast");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_NameFirst"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_NameFirst");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_ContactCode"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_ContactCode");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TEL_BusPhone"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TEL_BusPhone");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("LNK_Related_CO"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_Related_CO");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_TitleText"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_TitleText");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    //Considering that the length of typical contents in these fields, especially
                    //BusPhone and Title, is smaller, we are allowing the total length to exceed 80
                    //and are Left()-ing it to 80 (SYS_Name length).
                    //TXT_NameLast 22+2=24
                    //TXT_NameFirst 15+1=16
                    //TXT_ContactCode 10+1=11
                    //TEL_BusPhone 39+2=41
                    //NEW: TXT_TitleText 50+2=52
                    //LNK_Related_CO 21
                    //TOTAL: 113

                    //Last Name, First Name
                    sResult = doRS.GetFieldVal("TXT_NameLast").ToString();
                    sTemp = doRS.GetFieldVal("TXT_NameFirst").ToString();
                    if (!string.IsNullOrEmpty(sTemp) & !string.IsNullOrEmpty(sResult))
                    {
                        sResult = sResult + ", " + sTemp;
                    }
                    else
                    {
                        sResult = sResult + sTemp;
                    }

                    //*** MI 11/14/07
                    //Contact Code
                    sTemp = doRS.GetFieldVal("TXT_ContactCode").ToString();
                    if (!string.IsNullOrEmpty(sTemp))
                    {
                        sResult = sResult + " " + sTemp;
                    }

                    //Business Phone
                    sTemp2 = doRS.GetFieldVal("TEL_BusPhone").ToString();
                    if (!string.IsNullOrEmpty(sTemp2))
                    {
                        sResult = sResult + " " + sTemp2;
                    }
                    iLen = Microsoft.VisualBasic.Strings.Len(sResult);

                    //CS 8/21/08: Add title to sys name
                    sTemp4 = doRS.GetFieldVal("TXT_TitleText").ToString();
                    if (!string.IsNullOrEmpty(sTemp4))
                    {
                        sResult = sResult + " " + sTemp4;
                    }

                    //Related Company
                    sTemp3 = doRS.GetFieldVal("LNK_Related_CO", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp3))
                    {
                        //No records linked
                        sTemp3 = "";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp3 + "'", "", "TXT_CompanyName", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp3 = "(" + doLink.GetFieldVal("TXT_CompanyName", 0, 20).ToString() + ")";
                        }
                        else
                        {
                            sTemp3 = "(?)";
                        }
                    }
                    sResult = Microsoft.VisualBasic.Strings.Left(sResult + " " + sTemp3, 80).ToString();

                    break;

                case "CO":
                    //==> COMPANY NEW:	TXT_CompanyName+" - "+TXT_CityMailing+", "+TXT_StateMailing
                    if (!doRS.IsLoaded("TXT_CompanyName"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_CompanyName");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_CityMailing"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_CityMailing");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_StateMailing"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_StateMailing");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TEL_PhoneNo"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TEL_PhoneNo");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    sResult = doRS.GetFieldVal("TXT_CompanyName").ToString();
                    sTemp = doRS.GetFieldVal("TXT_CityMailing").ToString();
                    sTemp2 = doRS.GetFieldVal("TXT_StateMailing").ToString();
                    if (!string.IsNullOrEmpty(sTemp) | !string.IsNullOrEmpty(sTemp2))
                    {
                        sResult += " - ";
                    }
                    if (!string.IsNullOrEmpty(sTemp))
                    {
                        sResult += sTemp;
                        if (!string.IsNullOrEmpty(sTemp2))
                        {
                            sResult += ", ";
                        }
                    }
                    if (!string.IsNullOrEmpty(sTemp2))
                    {
                        sResult += sTemp2;
                    }
                    sTemp3 = doRS.GetFieldVal("TEL_PhoneNo").ToString();
                    if (!string.IsNullOrEmpty(sTemp3))
                    {
                        sResult += " " + sTemp3;
                    }
                    sResult = Microsoft.VisualBasic.Strings.Left(sResult, 80);

                    break;
                case "AC":
                    if (!doRS.IsLoaded("DTT_StartTime"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".DTT_StartTime");
                        // 35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    if (!doRS.IsLoaded("MMO_Notes"))
                    {
                        goErr.SetError(35103, sProc, "", (sFileName + ".MMO_Notes"));
                        // 35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    // NOTE: MI 3/6/08 Links will not be tested because they are loaded automatically, but currently there is a bug in clRowset. RH working on this.
                    if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                    {
                        goErr.SetError(35103, sProc, "", (sFileName + ".LNK_CreditedTo_US"));
                        // 35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    if (!doRS.IsLoaded("LNK_Related_CN"))
                    {
                        goErr.SetError(35103, sProc, "", (sFileName + ".LNK_Related_CN"));
                        // 35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    if (!doRS.IsLoaded("LNK_Related_CO"))
                    {
                        goErr.SetError(35103, sProc, "", (sFileName + ".LNK_Related_CO"));
                        // 35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1) == null ? "" : doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
                    if (sTemp == "")
                    {
                        // No records linked
                        sTemp = "?";
                    }
                    else
                    {
                        // Find the field value in the linked record
                        doLink = new clRowSet("US", 3, "GID_ID=\'" + sTemp + "\'", "", "TXT_Code", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp = doLink.GetFieldVal("TXT_Code") == null ? "" : doLink.GetFieldVal("TXT_Code").ToString();
                        }
                        else
                        {
                            sTemp = "?";
                        }

                    }

                    // Contact
                    sTemp2 = doRS.GetFieldVal("LNK_Related_CN", 0, -1, false, 1) == null ? "" : doRS.GetFieldVal("LNK_Related_CN", 0, -1, false, 1).ToString();
                    if ((sTemp2 == ""))
                    {
                        // No records linked
                        sTemp2 = "";
                    }
                    else
                    {
                        // Find the field value in the linked record
                        doLink = new clRowSet("CN", 3, "GID_ID=\'" + sTemp2 + "\'", "", "SYS_Name", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp2 = doLink.GetFieldVal("SYS_Name", 0, 16) == null ? "" : doLink.GetFieldVal("SYS_Name", 0, 16).ToString();
                        }
                        else
                        {
                            sTemp2 = "";
                        }

                    }

                    // Company
                    sTemp3 = doRS.GetFieldVal("LNK_Related_CO", 0, -1, false, 1) == null ? "" : doRS.GetFieldVal("LNK_Related_CO", 0, -1, false, 1).ToString();
                    if (sTemp3 == "")
                    {
                        // No records linked
                        sTemp3 = "";
                    }
                    else
                    {
                        // Find the field value in the linked record
                        doLink = new clRowSet("CO", 3, "GID_ID=\'" + sTemp3 + "\'", "", "TXT_CompanyName", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp3 = doLink.GetFieldVal("TXT_CompanyName", 0, 14, false) == null ? "" : doLink.GetFieldVal("TXT_CompanyName", 0, 14, false).ToString();
                        }
                        else
                        {
                            sTemp3 = "";
                        }

                    }

                    // Concatenate Contact and Company
                    if (sTemp3 == "")
                    {
                        if (sTemp2 != "")
                        {
                            sTemp2 = "(" + sTemp2 + ")";
                        }

                    }
                    else if (sTemp2 == "")
                    {
                        sTemp2 = "(" + sTemp3 + ")";
                    }
                    else
                    {
                        sTemp2 = "(" + sTemp2 + " " + sTemp3 + ")";
                    }

                    iLen = sTemp2.Length;
                    // *** MI 10/4/07 Added LocalToUTC conversion
                    // sResult = goTR.DateToString(doRS.GetFieldVal("DTE_StartTime", clC.SELL_SYSTEM), "YYYY-MM-DD") & " " & _
                    //             goTR.TimeToString(doRS.GetFieldVal("TME_StartTime", "2"), "HH:MM") & " " & _
                    //             goTR.Pad(sTemp, 4) & " " & _
                    //             goTR.ExtractString(doRS.GetFieldVal("MMO_Notes", , (55 - iLen - 1)), 1, vbCrLf) & " " & _
                    //             sTemp2
                    string par_sDelim = " ";
                    DateTime DTT_StartTime_VAR = doRS.GetFieldVal("DTT_StartTime", clC.SELL_SYSTEM) == null ? default(DateTime) : Convert.ToDateTime(doRS.GetFieldVal("DTT_StartTime", clC.SELL_SYSTEM));
                    sResult = (goTR.DateTimeToSysString(goTR.UTC_LocalToUTC(ref DTT_StartTime_VAR), ref par_iValid, ref par_sDelim).Substring(0, 16) + (" GMT "
                                + goTR.Pad(sTemp, 4) + " "
                                + goTR.ExtractString(doRS.GetFieldVal("MMO_Notes", 0, (51 - (iLen - 1))) == null ? "" : doRS.GetFieldVal("MMO_Notes", 0, (51 - (iLen - 1))).ToString(), 1, "\r\n") + " " + sTemp2));
                    //RN #2306 06222018 - Start time does not have to update here.
                    //doRS.SetFieldVal("DTT_StartTime", DTT_StartTime_VAR, 2);
                    break;

            }

            sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
            sResult = goTR.Replace(sResult, Strings.Chr(10).ToString(), " ");
            sResult = goTR.Replace(sResult, Strings.Chr(13).ToString(), " ");
            sResult = goTR.Replace(sResult, Constants.vbTab, " ");

            // 1/28/15 Manmeet added replace for |
            sResult = goTR.Replace(sResult, "|", " ");

            par_oReturn = sResult;
            par_doCallingObject = doRS;
            return true;
        }

        public bool OP_FormControlOnChange_BTN_CALCPROBABILITY_1_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_OPPTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            par_doCallingObject = doForm;
            return true;

        }

        private static void Refresh_OPPTotal(clRowSet doForm)
        {
            string sGidId = Convert.ToString(doForm.GetFieldVal("Gid_id"));

            clRowSet rsOL = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + sGidId + "' ", "LNK_IN_OP", "CUR_VALUE|SUM");

            double curTotalAmt = 0.0;

            if ((rsOL.GetFirst() == 1))
            {

                curTotalAmt = Convert.ToDouble(rsOL.GetFieldVal("CUR_VALUE|SUM", 2));


                doForm.SetFieldVal("CUR_VALUE", curTotalAmt, 2);
                //doForm.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);

            }
            else
            {
                doForm.SetFieldVal("CUR_VALUE", 0.0);
                //doForm.SetFieldVal("CUR_TOTAL", 0.0);

            }
        }
        public bool QT_FormControlOnChange_BTN_RECALC_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_QouteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            par_doCallingObject = doForm;
            return true;

        }
        private static void Refresh_QouteTotal(clRowSet doQuote)
        {
            string sGidId = Convert.ToString(doQuote.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_GROUPBY, "LNK_IN_QT='" + sGidId + "' ", "LNK_IN_QT", "CUR_SUBTOTAL|SUM");

            double curTotalAmt = 0.0;

            if ((rsQL.GetFirst() == 1))
            {

                curTotalAmt = Convert.ToDouble(rsQL.GetFieldVal("CUR_SUBTOTAL|SUM", 2));


                doQuote.SetFieldVal("CUR_SUBTOTAL", curTotalAmt, 2);
                doQuote.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);

            }
            else
            {
                doQuote.SetFieldVal("CUR_SUBTOTAL", 0.0);
                doQuote.SetFieldVal("CUR_TOTAL", 0.0);

            }

        }
        public bool Quote_FillAddress_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //goP.TraceLine("", "", sProc)

            //PURPOSE:
            //		Fill the address field, first checking whether it is empty.
            //RETURNS:
            //		True

            string sContactName = "";
            string sMailingAddr = null;
            string sFirstName = null;
            string sLastName = null;
            //Dim sCompName as string
            string sAddrMail = null;
            string sCityMail = null;
            string sStateMail = null;
            string sZipMail = null;
            string sCountryMail = null;
            string scompany = null;

            //VS 03262018 TKT#2151 : Refill data whenever Contact has changed even if not empty.
            //if (!string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_ADDRESSMAILING").ToString()))
            //    return true;
            if (doForm.doRS.GetLinkCount("LNK_ORIGINATEDBY_CN") < 1)
                return true;

            //CS 6/22/09: Create CN rowset to get CN fields
            clRowSet doRSContact = new clRowSet("CN", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN") + "'", "", "LNK_RELATED_CO,TXT_NAMEFIRST,TXT_NAMELAST,TXT_ADDRBUSINESS,TXT_CITYBUSINESS,TXT_STATEBUSINESS,TXT_ZIPBUSINESS,TXT_COUNTRYBUSINESS");
            if (doRSContact.GetFirst() == 1)
            {
                scompany = Convert.ToString(doRSContact.GetFieldVal("LNK_RELATED_CO%%TXT_COMPANYNAME"));
                sFirstName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMEFIRST"));
                sLastName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMELAST"));


                if (!string.IsNullOrEmpty(sFirstName))
                {
                    sContactName = sFirstName + " ";
                }
                sContactName += sLastName;

                sAddrMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ADDRBUSINESS"));
                sCityMail = Convert.ToString(doRSContact.GetFieldVal("TXT_CITYBUSINESS"));
                sStateMail = Convert.ToString(doRSContact.GetFieldVal("TXT_STATEBUSINESS"));
                sZipMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ZIPBUSINESS"));
                sCountryMail = Convert.ToString(doRSContact.GetFieldVal("TXT_COUNTRYBUSINESS"));

                //Start building the mailing address
                //sMailingAddr = scompany;
                //if (!string.IsNullOrEmpty(scompany))
                //{
                //    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                //}
                //sMailingAddr = scompany;
                sMailingAddr = sContactName;
                if (!sAddrMail.Contains(scompany))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                }
                if (!string.IsNullOrEmpty(sAddrMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sAddrMail;
                }
                if (!string.IsNullOrEmpty(sCityMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCityMail;
                }
                if (!string.IsNullOrEmpty(sStateMail))
                {
                    sMailingAddr = sMailingAddr + ", " + sStateMail;
                }
                if (!string.IsNullOrEmpty(sZipMail))
                {
                    sMailingAddr = sMailingAddr + " " + sZipMail;
                }
                if (!string.IsNullOrEmpty(sCountryMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCountryMail;
                }
                doForm.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailingAddr);
                doForm.doRS.SetFieldVal("MMO_ADDRMAILING", sMailingAddr);
            }

            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;

        }
    }
}

<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="Logon">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sUser" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sPassword" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LogonResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="LogonResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetVar">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sProperty" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sValue" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetVarResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SetVarResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="NewRS">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sFile" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iType" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="sCondition" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sSort" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sFields" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iTop" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="sINI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sGenFieldsDefs" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="NewRSResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="NewRSResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Bypass">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="bBypassValidation" type="s:boolean" />
            <s:element minOccurs="1" maxOccurs="1" name="bNoRecordOnSave" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="BypassResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="BypassResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetFieldVal">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sFieldName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="oValue" />
            <s:element minOccurs="1" maxOccurs="1" name="iFormat" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetFieldValResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SetFieldValResult" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetFieldVals">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIni" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetFieldValsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SetFieldValsResult" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetFieldVal">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sFieldName" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iFormat" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetFieldValResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetFieldValResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Log">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sMethod" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sErrorMessage" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iLevel" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LogResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="LogResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetLinkVal">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sFieldName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDelim" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetLinkValResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetLinkValResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetFieldVals">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sFields" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetFieldValsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetFieldValsResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ProcessingInstructions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sInstructions" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ProcessingInstructionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="ProcessingInstructionsResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ClearLink">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sLinkName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="oLinks" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ClearLinkResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="ClearLinkResult" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ClearLinkAll">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sLinkName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ClearLinkAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="ClearLinkAllResult" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Commit">
        <s:complexType />
      </s:element>
      <s:element name="CommitResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="CommitResult" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Count">
        <s:complexType />
      </s:element>
      <s:element name="CountResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="CountResult" type="s:long" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteRecord">
        <s:complexType />
      </s:element>
      <s:element name="DeleteRecordResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteRecordResult" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteAll">
        <s:complexType />
      </s:element>
      <s:element name="DeleteAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteAllResult" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ToTable">
        <s:complexType />
      </s:element>
      <s:element name="ToTableResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="ToTableResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetTransTable">
        <s:complexType />
      </s:element>
      <s:element name="GetTransTableResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetTransTableResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetTransTableAsXML">
        <s:complexType />
      </s:element>
      <s:element name="GetTransTableAsXMLResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetTransTableAsXMLResult">
              <s:complexType mixed="true">
                <s:sequence>
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetTransTableAsXMLWithCount">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Start" type="s:int" />
            <s:element minOccurs="1" maxOccurs="1" name="RecordCount" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetTransTableAsXMLWithCountResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetTransTableAsXMLWithCountResult">
              <s:complexType mixed="true">
                <s:sequence>
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetTransTableAsXMLString">
        <s:complexType />
      </s:element>
      <s:element name="GetTransTableAsXMLStringResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetTransTableAsXMLStringResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetLastError">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sParam" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetLastErrorResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetLastErrorResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Logoff">
        <s:complexType />
      </s:element>
      <s:element name="LogoffResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="LogoffResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMetaPage">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sSection" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sPage" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMetaPageResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMetaPageResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ResetConduit">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sConduit" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sValue" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ResetConduitResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="ResetConduitResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMe">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sVal" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMeResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetLoginByUserID">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sUserID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sType" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetLoginByUserIDResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetLoginByUserIDResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetQuoteSubject">
        <s:complexType />
      </s:element>
      <s:element name="GetQuoteSubjectResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetQuoteSubjectResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetFieldLabelFromName">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sFileName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sFieldName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetFieldLabelFromNameResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetFieldLabelFromNameResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetFileLabel">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sFile" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetFileLabelResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetFileLabelResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TestIDsForDeletions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIDS" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TestIDsForDeletionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TestIDsForDeletionsResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetFieldList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sFile" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetFieldListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetFieldListResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetLinksList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sFile" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetLinksListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetLinksListResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetFileList">
        <s:complexType />
      </s:element>
      <s:element name="GetFileListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetFileListResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetPermission">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sFile" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sType" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetPermissionResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetPermissionResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ValidateSession">
        <s:complexType />
      </s:element>
      <s:element name="ValidateSessionResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="ValidateSessionResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddRecordsByXML">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="xmlNode">
              <s:complexType mixed="true">
                <s:sequence>
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="1" maxOccurs="1" name="par_bDupCheck" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sDupReturnFields" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddRecordsByXMLResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="AddRecordsByXMLResult">
              <s:complexType mixed="true">
                <s:sequence>
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddRecordsByXMLString">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="xmlString" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="par_bDupCheck" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sDupReturnFields" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddRecordsByXMLStringResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="AddRecordsByXMLStringResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddRecordsByDataset">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_oDataSet">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="1" maxOccurs="1" name="par_bDupCheck" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sDupReturnFields" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddRecordsByDatasetResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="AddRecordsByDatasetResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EditRecordsByXML">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="xmlNode">
              <s:complexType mixed="true">
                <s:sequence>
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EditRecordsByXMLResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EditRecordsByXMLResult">
              <s:complexType mixed="true">
                <s:sequence>
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EditRecordsByXMLString">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="xmlString" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EditRecordsByXMLStringResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EditRecordsByXMLStringResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EditRecordsByDataset">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_oDataSet">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EditRecordsByDatasetResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EditRecordsByDatasetResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpsertRecordsByXML">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="xmlNode">
              <s:complexType mixed="true">
                <s:sequence>
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="0" maxOccurs="1" name="strCompareField" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="strExtSource" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpsertRecordsByXMLResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpsertRecordsByXMLResult">
              <s:complexType mixed="true">
                <s:sequence>
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpsertRecordsByXMLString">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="xmlString" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="strCompareField" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="strExtSource" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpsertRecordsByXMLStringResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpsertRecordsByXMLStringResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpsertRecordsByDataset">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_oDataSet">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="0" maxOccurs="1" name="strCompareField" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="strExtSource" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpsertRecordsByDatasetResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpsertRecordsByDatasetResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sFile" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sField" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetListResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DatasetToTextFile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_oDataset">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="1" maxOccurs="1" name="par_iMaxCharsPerCell" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DatasetToTextFileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DatasetToTextFileResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSchemaAsXML">
        <s:complexType />
      </s:element>
      <s:element name="GetSchemaAsXMLResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetSchemaAsXMLResult">
              <s:complexType mixed="true">
                <s:sequence>
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSchemaAsXMLString">
        <s:complexType />
      </s:element>
      <s:element name="GetSchemaAsXMLStringResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetSchemaAsXMLStringResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteRecordsByXMLString">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="xmlString" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteRecordsByXMLStringResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DeleteRecordsByXMLStringResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteRecordsByXML">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="xNode">
              <s:complexType mixed="true">
                <s:sequence>
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteRecordsByXMLResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DeleteRecordsByXMLResult">
              <s:complexType mixed="true">
                <s:sequence>
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UploadAndProcessDataset">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_oDataSet">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="0" maxOccurs="1" name="par_sScriptName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sReportPage" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UploadAndProcessDatasetResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UploadAndProcessDatasetResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateStatus">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sReportPage" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sStatus" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateStatusResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UpdateStatusResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateS3Status">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sReportPage" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sStatus" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sErrorMessage" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sProgress" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="lBytesTransferred" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="lTotalBytes" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="sType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sFileLastMod" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateS3StatusResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UpdateS3StatusResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSystemCodes">
        <s:complexType />
      </s:element>
      <s:element name="GetSystemCodesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetSystemCodesResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetStandardizedPhoneNumber">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sNumber" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetStandardizedPhoneNumberResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetStandardizedPhoneNumberResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LogError">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sMessage" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sProcedure" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LogErrorResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="LogErrorResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddAlert">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="par_sMessage" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sType" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sExecute" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sUser" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sIcon" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sProduct" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="par_sTooltip" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddAlertResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AddAlertResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="LogonSoapIn">
    <wsdl:part name="parameters" element="tns:Logon" />
  </wsdl:message>
  <wsdl:message name="LogonSoapOut">
    <wsdl:part name="parameters" element="tns:LogonResponse" />
  </wsdl:message>
  <wsdl:message name="SetVarSoapIn">
    <wsdl:part name="parameters" element="tns:SetVar" />
  </wsdl:message>
  <wsdl:message name="SetVarSoapOut">
    <wsdl:part name="parameters" element="tns:SetVarResponse" />
  </wsdl:message>
  <wsdl:message name="NewRSSoapIn">
    <wsdl:part name="parameters" element="tns:NewRS" />
  </wsdl:message>
  <wsdl:message name="NewRSSoapOut">
    <wsdl:part name="parameters" element="tns:NewRSResponse" />
  </wsdl:message>
  <wsdl:message name="BypassSoapIn">
    <wsdl:part name="parameters" element="tns:Bypass" />
  </wsdl:message>
  <wsdl:message name="BypassSoapOut">
    <wsdl:part name="parameters" element="tns:BypassResponse" />
  </wsdl:message>
  <wsdl:message name="SetFieldValSoapIn">
    <wsdl:part name="parameters" element="tns:SetFieldVal" />
  </wsdl:message>
  <wsdl:message name="SetFieldValSoapOut">
    <wsdl:part name="parameters" element="tns:SetFieldValResponse" />
  </wsdl:message>
  <wsdl:message name="SetFieldValsSoapIn">
    <wsdl:part name="parameters" element="tns:SetFieldVals" />
  </wsdl:message>
  <wsdl:message name="SetFieldValsSoapOut">
    <wsdl:part name="parameters" element="tns:SetFieldValsResponse" />
  </wsdl:message>
  <wsdl:message name="GetFieldValSoapIn">
    <wsdl:part name="parameters" element="tns:GetFieldVal" />
  </wsdl:message>
  <wsdl:message name="GetFieldValSoapOut">
    <wsdl:part name="parameters" element="tns:GetFieldValResponse" />
  </wsdl:message>
  <wsdl:message name="LogSoapIn">
    <wsdl:part name="parameters" element="tns:Log" />
  </wsdl:message>
  <wsdl:message name="LogSoapOut">
    <wsdl:part name="parameters" element="tns:LogResponse" />
  </wsdl:message>
  <wsdl:message name="GetLinkValSoapIn">
    <wsdl:part name="parameters" element="tns:GetLinkVal" />
  </wsdl:message>
  <wsdl:message name="GetLinkValSoapOut">
    <wsdl:part name="parameters" element="tns:GetLinkValResponse" />
  </wsdl:message>
  <wsdl:message name="GetFieldValsSoapIn">
    <wsdl:part name="parameters" element="tns:GetFieldVals" />
  </wsdl:message>
  <wsdl:message name="GetFieldValsSoapOut">
    <wsdl:part name="parameters" element="tns:GetFieldValsResponse" />
  </wsdl:message>
  <wsdl:message name="ProcessingInstructionsSoapIn">
    <wsdl:part name="parameters" element="tns:ProcessingInstructions" />
  </wsdl:message>
  <wsdl:message name="ProcessingInstructionsSoapOut">
    <wsdl:part name="parameters" element="tns:ProcessingInstructionsResponse" />
  </wsdl:message>
  <wsdl:message name="ClearLinkSoapIn">
    <wsdl:part name="parameters" element="tns:ClearLink" />
  </wsdl:message>
  <wsdl:message name="ClearLinkSoapOut">
    <wsdl:part name="parameters" element="tns:ClearLinkResponse" />
  </wsdl:message>
  <wsdl:message name="ClearLinkAllSoapIn">
    <wsdl:part name="parameters" element="tns:ClearLinkAll" />
  </wsdl:message>
  <wsdl:message name="ClearLinkAllSoapOut">
    <wsdl:part name="parameters" element="tns:ClearLinkAllResponse" />
  </wsdl:message>
  <wsdl:message name="CommitSoapIn">
    <wsdl:part name="parameters" element="tns:Commit" />
  </wsdl:message>
  <wsdl:message name="CommitSoapOut">
    <wsdl:part name="parameters" element="tns:CommitResponse" />
  </wsdl:message>
  <wsdl:message name="CountSoapIn">
    <wsdl:part name="parameters" element="tns:Count" />
  </wsdl:message>
  <wsdl:message name="CountSoapOut">
    <wsdl:part name="parameters" element="tns:CountResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteRecordSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteRecord" />
  </wsdl:message>
  <wsdl:message name="DeleteRecordSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteRecordResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteAllSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteAll" />
  </wsdl:message>
  <wsdl:message name="DeleteAllSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteAllResponse" />
  </wsdl:message>
  <wsdl:message name="ToTableSoapIn">
    <wsdl:part name="parameters" element="tns:ToTable" />
  </wsdl:message>
  <wsdl:message name="ToTableSoapOut">
    <wsdl:part name="parameters" element="tns:ToTableResponse" />
  </wsdl:message>
  <wsdl:message name="GetTransTableSoapIn">
    <wsdl:part name="parameters" element="tns:GetTransTable" />
  </wsdl:message>
  <wsdl:message name="GetTransTableSoapOut">
    <wsdl:part name="parameters" element="tns:GetTransTableResponse" />
  </wsdl:message>
  <wsdl:message name="GetTransTableAsXMLSoapIn">
    <wsdl:part name="parameters" element="tns:GetTransTableAsXML" />
  </wsdl:message>
  <wsdl:message name="GetTransTableAsXMLSoapOut">
    <wsdl:part name="parameters" element="tns:GetTransTableAsXMLResponse" />
  </wsdl:message>
  <wsdl:message name="GetTransTableAsXMLWithCountSoapIn">
    <wsdl:part name="parameters" element="tns:GetTransTableAsXMLWithCount" />
  </wsdl:message>
  <wsdl:message name="GetTransTableAsXMLWithCountSoapOut">
    <wsdl:part name="parameters" element="tns:GetTransTableAsXMLWithCountResponse" />
  </wsdl:message>
  <wsdl:message name="GetTransTableAsXMLStringSoapIn">
    <wsdl:part name="parameters" element="tns:GetTransTableAsXMLString" />
  </wsdl:message>
  <wsdl:message name="GetTransTableAsXMLStringSoapOut">
    <wsdl:part name="parameters" element="tns:GetTransTableAsXMLStringResponse" />
  </wsdl:message>
  <wsdl:message name="GetLastErrorSoapIn">
    <wsdl:part name="parameters" element="tns:GetLastError" />
  </wsdl:message>
  <wsdl:message name="GetLastErrorSoapOut">
    <wsdl:part name="parameters" element="tns:GetLastErrorResponse" />
  </wsdl:message>
  <wsdl:message name="LogoffSoapIn">
    <wsdl:part name="parameters" element="tns:Logoff" />
  </wsdl:message>
  <wsdl:message name="LogoffSoapOut">
    <wsdl:part name="parameters" element="tns:LogoffResponse" />
  </wsdl:message>
  <wsdl:message name="GetMetaPageSoapIn">
    <wsdl:part name="parameters" element="tns:GetMetaPage" />
  </wsdl:message>
  <wsdl:message name="GetMetaPageSoapOut">
    <wsdl:part name="parameters" element="tns:GetMetaPageResponse" />
  </wsdl:message>
  <wsdl:message name="ResetConduitSoapIn">
    <wsdl:part name="parameters" element="tns:ResetConduit" />
  </wsdl:message>
  <wsdl:message name="ResetConduitSoapOut">
    <wsdl:part name="parameters" element="tns:ResetConduitResponse" />
  </wsdl:message>
  <wsdl:message name="GetMeSoapIn">
    <wsdl:part name="parameters" element="tns:GetMe" />
  </wsdl:message>
  <wsdl:message name="GetMeSoapOut">
    <wsdl:part name="parameters" element="tns:GetMeResponse" />
  </wsdl:message>
  <wsdl:message name="GetLoginByUserIDSoapIn">
    <wsdl:part name="parameters" element="tns:GetLoginByUserID" />
  </wsdl:message>
  <wsdl:message name="GetLoginByUserIDSoapOut">
    <wsdl:part name="parameters" element="tns:GetLoginByUserIDResponse" />
  </wsdl:message>
  <wsdl:message name="GetQuoteSubjectSoapIn">
    <wsdl:part name="parameters" element="tns:GetQuoteSubject" />
  </wsdl:message>
  <wsdl:message name="GetQuoteSubjectSoapOut">
    <wsdl:part name="parameters" element="tns:GetQuoteSubjectResponse" />
  </wsdl:message>
  <wsdl:message name="GetFieldLabelFromNameSoapIn">
    <wsdl:part name="parameters" element="tns:GetFieldLabelFromName" />
  </wsdl:message>
  <wsdl:message name="GetFieldLabelFromNameSoapOut">
    <wsdl:part name="parameters" element="tns:GetFieldLabelFromNameResponse" />
  </wsdl:message>
  <wsdl:message name="GetFileLabelSoapIn">
    <wsdl:part name="parameters" element="tns:GetFileLabel" />
  </wsdl:message>
  <wsdl:message name="GetFileLabelSoapOut">
    <wsdl:part name="parameters" element="tns:GetFileLabelResponse" />
  </wsdl:message>
  <wsdl:message name="TestIDsForDeletionsSoapIn">
    <wsdl:part name="parameters" element="tns:TestIDsForDeletions" />
  </wsdl:message>
  <wsdl:message name="TestIDsForDeletionsSoapOut">
    <wsdl:part name="parameters" element="tns:TestIDsForDeletionsResponse" />
  </wsdl:message>
  <wsdl:message name="GetFieldListSoapIn">
    <wsdl:part name="parameters" element="tns:GetFieldList" />
  </wsdl:message>
  <wsdl:message name="GetFieldListSoapOut">
    <wsdl:part name="parameters" element="tns:GetFieldListResponse" />
  </wsdl:message>
  <wsdl:message name="GetLinksListSoapIn">
    <wsdl:part name="parameters" element="tns:GetLinksList" />
  </wsdl:message>
  <wsdl:message name="GetLinksListSoapOut">
    <wsdl:part name="parameters" element="tns:GetLinksListResponse" />
  </wsdl:message>
  <wsdl:message name="GetFileListSoapIn">
    <wsdl:part name="parameters" element="tns:GetFileList" />
  </wsdl:message>
  <wsdl:message name="GetFileListSoapOut">
    <wsdl:part name="parameters" element="tns:GetFileListResponse" />
  </wsdl:message>
  <wsdl:message name="GetPermissionSoapIn">
    <wsdl:part name="parameters" element="tns:GetPermission" />
  </wsdl:message>
  <wsdl:message name="GetPermissionSoapOut">
    <wsdl:part name="parameters" element="tns:GetPermissionResponse" />
  </wsdl:message>
  <wsdl:message name="ValidateSessionSoapIn">
    <wsdl:part name="parameters" element="tns:ValidateSession" />
  </wsdl:message>
  <wsdl:message name="ValidateSessionSoapOut">
    <wsdl:part name="parameters" element="tns:ValidateSessionResponse" />
  </wsdl:message>
  <wsdl:message name="AddRecordsByXMLSoapIn">
    <wsdl:part name="parameters" element="tns:AddRecordsByXML" />
  </wsdl:message>
  <wsdl:message name="AddRecordsByXMLSoapOut">
    <wsdl:part name="parameters" element="tns:AddRecordsByXMLResponse" />
  </wsdl:message>
  <wsdl:message name="AddRecordsByXMLStringSoapIn">
    <wsdl:part name="parameters" element="tns:AddRecordsByXMLString" />
  </wsdl:message>
  <wsdl:message name="AddRecordsByXMLStringSoapOut">
    <wsdl:part name="parameters" element="tns:AddRecordsByXMLStringResponse" />
  </wsdl:message>
  <wsdl:message name="AddRecordsByDatasetSoapIn">
    <wsdl:part name="parameters" element="tns:AddRecordsByDataset" />
  </wsdl:message>
  <wsdl:message name="AddRecordsByDatasetSoapOut">
    <wsdl:part name="parameters" element="tns:AddRecordsByDatasetResponse" />
  </wsdl:message>
  <wsdl:message name="EditRecordsByXMLSoapIn">
    <wsdl:part name="parameters" element="tns:EditRecordsByXML" />
  </wsdl:message>
  <wsdl:message name="EditRecordsByXMLSoapOut">
    <wsdl:part name="parameters" element="tns:EditRecordsByXMLResponse" />
  </wsdl:message>
  <wsdl:message name="EditRecordsByXMLStringSoapIn">
    <wsdl:part name="parameters" element="tns:EditRecordsByXMLString" />
  </wsdl:message>
  <wsdl:message name="EditRecordsByXMLStringSoapOut">
    <wsdl:part name="parameters" element="tns:EditRecordsByXMLStringResponse" />
  </wsdl:message>
  <wsdl:message name="EditRecordsByDatasetSoapIn">
    <wsdl:part name="parameters" element="tns:EditRecordsByDataset" />
  </wsdl:message>
  <wsdl:message name="EditRecordsByDatasetSoapOut">
    <wsdl:part name="parameters" element="tns:EditRecordsByDatasetResponse" />
  </wsdl:message>
  <wsdl:message name="UpsertRecordsByXMLSoapIn">
    <wsdl:part name="parameters" element="tns:UpsertRecordsByXML" />
  </wsdl:message>
  <wsdl:message name="UpsertRecordsByXMLSoapOut">
    <wsdl:part name="parameters" element="tns:UpsertRecordsByXMLResponse" />
  </wsdl:message>
  <wsdl:message name="UpsertRecordsByXMLStringSoapIn">
    <wsdl:part name="parameters" element="tns:UpsertRecordsByXMLString" />
  </wsdl:message>
  <wsdl:message name="UpsertRecordsByXMLStringSoapOut">
    <wsdl:part name="parameters" element="tns:UpsertRecordsByXMLStringResponse" />
  </wsdl:message>
  <wsdl:message name="UpsertRecordsByDatasetSoapIn">
    <wsdl:part name="parameters" element="tns:UpsertRecordsByDataset" />
  </wsdl:message>
  <wsdl:message name="UpsertRecordsByDatasetSoapOut">
    <wsdl:part name="parameters" element="tns:UpsertRecordsByDatasetResponse" />
  </wsdl:message>
  <wsdl:message name="GetListSoapIn">
    <wsdl:part name="parameters" element="tns:GetList" />
  </wsdl:message>
  <wsdl:message name="GetListSoapOut">
    <wsdl:part name="parameters" element="tns:GetListResponse" />
  </wsdl:message>
  <wsdl:message name="DatasetToTextFileSoapIn">
    <wsdl:part name="parameters" element="tns:DatasetToTextFile" />
  </wsdl:message>
  <wsdl:message name="DatasetToTextFileSoapOut">
    <wsdl:part name="parameters" element="tns:DatasetToTextFileResponse" />
  </wsdl:message>
  <wsdl:message name="GetSchemaAsXMLSoapIn">
    <wsdl:part name="parameters" element="tns:GetSchemaAsXML" />
  </wsdl:message>
  <wsdl:message name="GetSchemaAsXMLSoapOut">
    <wsdl:part name="parameters" element="tns:GetSchemaAsXMLResponse" />
  </wsdl:message>
  <wsdl:message name="GetSchemaAsXMLStringSoapIn">
    <wsdl:part name="parameters" element="tns:GetSchemaAsXMLString" />
  </wsdl:message>
  <wsdl:message name="GetSchemaAsXMLStringSoapOut">
    <wsdl:part name="parameters" element="tns:GetSchemaAsXMLStringResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteRecordsByXMLStringSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteRecordsByXMLString" />
  </wsdl:message>
  <wsdl:message name="DeleteRecordsByXMLStringSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteRecordsByXMLStringResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteRecordsByXMLSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteRecordsByXML" />
  </wsdl:message>
  <wsdl:message name="DeleteRecordsByXMLSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteRecordsByXMLResponse" />
  </wsdl:message>
  <wsdl:message name="UploadAndProcessDatasetSoapIn">
    <wsdl:part name="parameters" element="tns:UploadAndProcessDataset" />
  </wsdl:message>
  <wsdl:message name="UploadAndProcessDatasetSoapOut">
    <wsdl:part name="parameters" element="tns:UploadAndProcessDatasetResponse" />
  </wsdl:message>
  <wsdl:message name="UpdateStatusSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateStatus" />
  </wsdl:message>
  <wsdl:message name="UpdateStatusSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateStatusResponse" />
  </wsdl:message>
  <wsdl:message name="UpdateS3StatusSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateS3Status" />
  </wsdl:message>
  <wsdl:message name="UpdateS3StatusSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateS3StatusResponse" />
  </wsdl:message>
  <wsdl:message name="GetSystemCodesSoapIn">
    <wsdl:part name="parameters" element="tns:GetSystemCodes" />
  </wsdl:message>
  <wsdl:message name="GetSystemCodesSoapOut">
    <wsdl:part name="parameters" element="tns:GetSystemCodesResponse" />
  </wsdl:message>
  <wsdl:message name="GetStandardizedPhoneNumberSoapIn">
    <wsdl:part name="parameters" element="tns:GetStandardizedPhoneNumber" />
  </wsdl:message>
  <wsdl:message name="GetStandardizedPhoneNumberSoapOut">
    <wsdl:part name="parameters" element="tns:GetStandardizedPhoneNumberResponse" />
  </wsdl:message>
  <wsdl:message name="LogErrorSoapIn">
    <wsdl:part name="parameters" element="tns:LogError" />
  </wsdl:message>
  <wsdl:message name="LogErrorSoapOut">
    <wsdl:part name="parameters" element="tns:LogErrorResponse" />
  </wsdl:message>
  <wsdl:message name="AddAlertSoapIn">
    <wsdl:part name="parameters" element="tns:AddAlert" />
  </wsdl:message>
  <wsdl:message name="AddAlertSoapOut">
    <wsdl:part name="parameters" element="tns:AddAlertResponse" />
  </wsdl:message>
  <wsdl:portType name="WebService_RowSetSoap">
    <wsdl:operation name="Logon">
      <wsdl:input message="tns:LogonSoapIn" />
      <wsdl:output message="tns:LogonSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetVar">
      <wsdl:input message="tns:SetVarSoapIn" />
      <wsdl:output message="tns:SetVarSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="NewRS">
      <wsdl:input message="tns:NewRSSoapIn" />
      <wsdl:output message="tns:NewRSSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Bypass">
      <wsdl:input message="tns:BypassSoapIn" />
      <wsdl:output message="tns:BypassSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetFieldVal">
      <wsdl:input message="tns:SetFieldValSoapIn" />
      <wsdl:output message="tns:SetFieldValSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetFieldVals">
      <wsdl:input message="tns:SetFieldValsSoapIn" />
      <wsdl:output message="tns:SetFieldValsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetFieldVal">
      <wsdl:input message="tns:GetFieldValSoapIn" />
      <wsdl:output message="tns:GetFieldValSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Log">
      <wsdl:input message="tns:LogSoapIn" />
      <wsdl:output message="tns:LogSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetLinkVal">
      <wsdl:input message="tns:GetLinkValSoapIn" />
      <wsdl:output message="tns:GetLinkValSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetFieldVals">
      <wsdl:input message="tns:GetFieldValsSoapIn" />
      <wsdl:output message="tns:GetFieldValsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ProcessingInstructions">
      <wsdl:input message="tns:ProcessingInstructionsSoapIn" />
      <wsdl:output message="tns:ProcessingInstructionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ClearLink">
      <wsdl:input message="tns:ClearLinkSoapIn" />
      <wsdl:output message="tns:ClearLinkSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ClearLinkAll">
      <wsdl:input message="tns:ClearLinkAllSoapIn" />
      <wsdl:output message="tns:ClearLinkAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Commit">
      <wsdl:input message="tns:CommitSoapIn" />
      <wsdl:output message="tns:CommitSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Count">
      <wsdl:input message="tns:CountSoapIn" />
      <wsdl:output message="tns:CountSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteRecord">
      <wsdl:input message="tns:DeleteRecordSoapIn" />
      <wsdl:output message="tns:DeleteRecordSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteAll">
      <wsdl:input message="tns:DeleteAllSoapIn" />
      <wsdl:output message="tns:DeleteAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ToTable">
      <wsdl:input message="tns:ToTableSoapIn" />
      <wsdl:output message="tns:ToTableSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetTransTable">
      <wsdl:input message="tns:GetTransTableSoapIn" />
      <wsdl:output message="tns:GetTransTableSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetTransTableAsXML">
      <wsdl:input message="tns:GetTransTableAsXMLSoapIn" />
      <wsdl:output message="tns:GetTransTableAsXMLSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetTransTableAsXMLWithCount">
      <wsdl:input message="tns:GetTransTableAsXMLWithCountSoapIn" />
      <wsdl:output message="tns:GetTransTableAsXMLWithCountSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetTransTableAsXMLString">
      <wsdl:input message="tns:GetTransTableAsXMLStringSoapIn" />
      <wsdl:output message="tns:GetTransTableAsXMLStringSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetLastError">
      <wsdl:input message="tns:GetLastErrorSoapIn" />
      <wsdl:output message="tns:GetLastErrorSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Logoff">
      <wsdl:input message="tns:LogoffSoapIn" />
      <wsdl:output message="tns:LogoffSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMetaPage">
      <wsdl:input message="tns:GetMetaPageSoapIn" />
      <wsdl:output message="tns:GetMetaPageSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ResetConduit">
      <wsdl:input message="tns:ResetConduitSoapIn" />
      <wsdl:output message="tns:ResetConduitSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMe">
      <wsdl:input message="tns:GetMeSoapIn" />
      <wsdl:output message="tns:GetMeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetLoginByUserID">
      <wsdl:input message="tns:GetLoginByUserIDSoapIn" />
      <wsdl:output message="tns:GetLoginByUserIDSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetQuoteSubject">
      <wsdl:input message="tns:GetQuoteSubjectSoapIn" />
      <wsdl:output message="tns:GetQuoteSubjectSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetFieldLabelFromName">
      <wsdl:input message="tns:GetFieldLabelFromNameSoapIn" />
      <wsdl:output message="tns:GetFieldLabelFromNameSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetFileLabel">
      <wsdl:input message="tns:GetFileLabelSoapIn" />
      <wsdl:output message="tns:GetFileLabelSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TestIDsForDeletions">
      <wsdl:input message="tns:TestIDsForDeletionsSoapIn" />
      <wsdl:output message="tns:TestIDsForDeletionsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetFieldList">
      <wsdl:input message="tns:GetFieldListSoapIn" />
      <wsdl:output message="tns:GetFieldListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetLinksList">
      <wsdl:input message="tns:GetLinksListSoapIn" />
      <wsdl:output message="tns:GetLinksListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetFileList">
      <wsdl:input message="tns:GetFileListSoapIn" />
      <wsdl:output message="tns:GetFileListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetPermission">
      <wsdl:input message="tns:GetPermissionSoapIn" />
      <wsdl:output message="tns:GetPermissionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ValidateSession">
      <wsdl:input message="tns:ValidateSessionSoapIn" />
      <wsdl:output message="tns:ValidateSessionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="AddRecordsByXML">
      <wsdl:input message="tns:AddRecordsByXMLSoapIn" />
      <wsdl:output message="tns:AddRecordsByXMLSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="AddRecordsByXMLString">
      <wsdl:input message="tns:AddRecordsByXMLStringSoapIn" />
      <wsdl:output message="tns:AddRecordsByXMLStringSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="AddRecordsByDataset">
      <wsdl:input message="tns:AddRecordsByDatasetSoapIn" />
      <wsdl:output message="tns:AddRecordsByDatasetSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EditRecordsByXML">
      <wsdl:input message="tns:EditRecordsByXMLSoapIn" />
      <wsdl:output message="tns:EditRecordsByXMLSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EditRecordsByXMLString">
      <wsdl:input message="tns:EditRecordsByXMLStringSoapIn" />
      <wsdl:output message="tns:EditRecordsByXMLStringSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EditRecordsByDataset">
      <wsdl:input message="tns:EditRecordsByDatasetSoapIn" />
      <wsdl:output message="tns:EditRecordsByDatasetSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpsertRecordsByXML">
      <wsdl:input message="tns:UpsertRecordsByXMLSoapIn" />
      <wsdl:output message="tns:UpsertRecordsByXMLSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpsertRecordsByXMLString">
      <wsdl:input message="tns:UpsertRecordsByXMLStringSoapIn" />
      <wsdl:output message="tns:UpsertRecordsByXMLStringSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpsertRecordsByDataset">
      <wsdl:input message="tns:UpsertRecordsByDatasetSoapIn" />
      <wsdl:output message="tns:UpsertRecordsByDatasetSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetList">
      <wsdl:input message="tns:GetListSoapIn" />
      <wsdl:output message="tns:GetListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DatasetToTextFile">
      <wsdl:input message="tns:DatasetToTextFileSoapIn" />
      <wsdl:output message="tns:DatasetToTextFileSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetSchemaAsXML">
      <wsdl:input message="tns:GetSchemaAsXMLSoapIn" />
      <wsdl:output message="tns:GetSchemaAsXMLSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetSchemaAsXMLString">
      <wsdl:input message="tns:GetSchemaAsXMLStringSoapIn" />
      <wsdl:output message="tns:GetSchemaAsXMLStringSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteRecordsByXMLString">
      <wsdl:input message="tns:DeleteRecordsByXMLStringSoapIn" />
      <wsdl:output message="tns:DeleteRecordsByXMLStringSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteRecordsByXML">
      <wsdl:input message="tns:DeleteRecordsByXMLSoapIn" />
      <wsdl:output message="tns:DeleteRecordsByXMLSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UploadAndProcessDataset">
      <wsdl:input message="tns:UploadAndProcessDatasetSoapIn" />
      <wsdl:output message="tns:UploadAndProcessDatasetSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpdateStatus">
      <wsdl:input message="tns:UpdateStatusSoapIn" />
      <wsdl:output message="tns:UpdateStatusSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpdateS3Status">
      <wsdl:input message="tns:UpdateS3StatusSoapIn" />
      <wsdl:output message="tns:UpdateS3StatusSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetSystemCodes">
      <wsdl:input message="tns:GetSystemCodesSoapIn" />
      <wsdl:output message="tns:GetSystemCodesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetStandardizedPhoneNumber">
      <wsdl:input message="tns:GetStandardizedPhoneNumberSoapIn" />
      <wsdl:output message="tns:GetStandardizedPhoneNumberSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="LogError">
      <wsdl:input message="tns:LogErrorSoapIn" />
      <wsdl:output message="tns:LogErrorSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="AddAlert">
      <wsdl:input message="tns:AddAlertSoapIn" />
      <wsdl:output message="tns:AddAlertSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="WebService_RowSetSoap" type="tns:WebService_RowSetSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Logon">
      <soap:operation soapAction="http://tempuri.org/Logon" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetVar">
      <soap:operation soapAction="http://tempuri.org/SetVar" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="NewRS">
      <soap:operation soapAction="http://tempuri.org/NewRS" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Bypass">
      <soap:operation soapAction="http://tempuri.org/Bypass" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetFieldVal">
      <soap:operation soapAction="http://tempuri.org/SetFieldVal" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetFieldVals">
      <soap:operation soapAction="http://tempuri.org/SetFieldVals" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFieldVal">
      <soap:operation soapAction="http://tempuri.org/GetFieldVal" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Log">
      <soap:operation soapAction="http://tempuri.org/Log" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLinkVal">
      <soap:operation soapAction="http://tempuri.org/GetLinkVal" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFieldVals">
      <soap:operation soapAction="http://tempuri.org/GetFieldVals" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ProcessingInstructions">
      <soap:operation soapAction="http://tempuri.org/ProcessingInstructions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ClearLink">
      <soap:operation soapAction="http://tempuri.org/ClearLink" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ClearLinkAll">
      <soap:operation soapAction="http://tempuri.org/ClearLinkAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Commit">
      <soap:operation soapAction="http://tempuri.org/Commit" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Count">
      <soap:operation soapAction="http://tempuri.org/Count" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteRecord">
      <soap:operation soapAction="http://tempuri.org/DeleteRecord" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteAll">
      <soap:operation soapAction="http://tempuri.org/DeleteAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ToTable">
      <soap:operation soapAction="http://tempuri.org/ToTable" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTransTable">
      <soap:operation soapAction="http://tempuri.org/GetTransTable" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTransTableAsXML">
      <soap:operation soapAction="http://tempuri.org/GetTransTableAsXML" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTransTableAsXMLWithCount">
      <soap:operation soapAction="http://tempuri.org/GetTransTableAsXMLWithCount" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTransTableAsXMLString">
      <soap:operation soapAction="http://tempuri.org/GetTransTableAsXMLString" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLastError">
      <soap:operation soapAction="http://tempuri.org/GetLastError" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Logoff">
      <soap:operation soapAction="http://tempuri.org/Logoff" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMetaPage">
      <soap:operation soapAction="http://tempuri.org/GetMetaPage" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ResetConduit">
      <soap:operation soapAction="http://tempuri.org/ResetConduit" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMe">
      <soap:operation soapAction="http://tempuri.org/GetMe" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLoginByUserID">
      <soap:operation soapAction="http://tempuri.org/GetLoginByUserID" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetQuoteSubject">
      <soap:operation soapAction="http://tempuri.org/GetQuoteSubject" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFieldLabelFromName">
      <soap:operation soapAction="http://tempuri.org/GetFieldLabelFromName" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFileLabel">
      <soap:operation soapAction="http://tempuri.org/GetFileLabel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TestIDsForDeletions">
      <soap:operation soapAction="http://tempuri.org/TestIDsForDeletions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFieldList">
      <soap:operation soapAction="http://tempuri.org/GetFieldList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLinksList">
      <soap:operation soapAction="http://tempuri.org/GetLinksList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFileList">
      <soap:operation soapAction="http://tempuri.org/GetFileList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPermission">
      <soap:operation soapAction="http://tempuri.org/GetPermission" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidateSession">
      <soap:operation soapAction="http://tempuri.org/ValidateSession" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddRecordsByXML">
      <soap:operation soapAction="http://tempuri.org/AddRecordsByXML" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddRecordsByXMLString">
      <soap:operation soapAction="http://tempuri.org/AddRecordsByXMLString" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddRecordsByDataset">
      <soap:operation soapAction="http://tempuri.org/AddRecordsByDataset" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EditRecordsByXML">
      <soap:operation soapAction="http://tempuri.org/EditRecordsByXML" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EditRecordsByXMLString">
      <soap:operation soapAction="http://tempuri.org/EditRecordsByXMLString" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EditRecordsByDataset">
      <soap:operation soapAction="http://tempuri.org/EditRecordsByDataset" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpsertRecordsByXML">
      <soap:operation soapAction="http://tempuri.org/UpsertRecordsByXML" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpsertRecordsByXMLString">
      <soap:operation soapAction="http://tempuri.org/UpsertRecordsByXMLString" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpsertRecordsByDataset">
      <soap:operation soapAction="http://tempuri.org/UpsertRecordsByDataset" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetList">
      <soap:operation soapAction="http://tempuri.org/GetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DatasetToTextFile">
      <soap:operation soapAction="http://tempuri.org/DatasetToTextFile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSchemaAsXML">
      <soap:operation soapAction="http://tempuri.org/GetSchemaAsXML" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSchemaAsXMLString">
      <soap:operation soapAction="http://tempuri.org/GetSchemaAsXMLString" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteRecordsByXMLString">
      <soap:operation soapAction="http://tempuri.org/DeleteRecordsByXMLString" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteRecordsByXML">
      <soap:operation soapAction="http://tempuri.org/DeleteRecordsByXML" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadAndProcessDataset">
      <soap:operation soapAction="http://tempuri.org/UploadAndProcessDataset" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateStatus">
      <soap:operation soapAction="http://tempuri.org/UpdateStatus" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateS3Status">
      <soap:operation soapAction="http://tempuri.org/UpdateS3Status" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSystemCodes">
      <soap:operation soapAction="http://tempuri.org/GetSystemCodes" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetStandardizedPhoneNumber">
      <soap:operation soapAction="http://tempuri.org/GetStandardizedPhoneNumber" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LogError">
      <soap:operation soapAction="http://tempuri.org/LogError" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddAlert">
      <soap:operation soapAction="http://tempuri.org/AddAlert" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="WebService_RowSetSoap12" type="tns:WebService_RowSetSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Logon">
      <soap12:operation soapAction="http://tempuri.org/Logon" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetVar">
      <soap12:operation soapAction="http://tempuri.org/SetVar" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="NewRS">
      <soap12:operation soapAction="http://tempuri.org/NewRS" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Bypass">
      <soap12:operation soapAction="http://tempuri.org/Bypass" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetFieldVal">
      <soap12:operation soapAction="http://tempuri.org/SetFieldVal" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetFieldVals">
      <soap12:operation soapAction="http://tempuri.org/SetFieldVals" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFieldVal">
      <soap12:operation soapAction="http://tempuri.org/GetFieldVal" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Log">
      <soap12:operation soapAction="http://tempuri.org/Log" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLinkVal">
      <soap12:operation soapAction="http://tempuri.org/GetLinkVal" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFieldVals">
      <soap12:operation soapAction="http://tempuri.org/GetFieldVals" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ProcessingInstructions">
      <soap12:operation soapAction="http://tempuri.org/ProcessingInstructions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ClearLink">
      <soap12:operation soapAction="http://tempuri.org/ClearLink" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ClearLinkAll">
      <soap12:operation soapAction="http://tempuri.org/ClearLinkAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Commit">
      <soap12:operation soapAction="http://tempuri.org/Commit" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Count">
      <soap12:operation soapAction="http://tempuri.org/Count" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteRecord">
      <soap12:operation soapAction="http://tempuri.org/DeleteRecord" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteAll">
      <soap12:operation soapAction="http://tempuri.org/DeleteAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ToTable">
      <soap12:operation soapAction="http://tempuri.org/ToTable" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTransTable">
      <soap12:operation soapAction="http://tempuri.org/GetTransTable" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTransTableAsXML">
      <soap12:operation soapAction="http://tempuri.org/GetTransTableAsXML" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTransTableAsXMLWithCount">
      <soap12:operation soapAction="http://tempuri.org/GetTransTableAsXMLWithCount" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTransTableAsXMLString">
      <soap12:operation soapAction="http://tempuri.org/GetTransTableAsXMLString" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLastError">
      <soap12:operation soapAction="http://tempuri.org/GetLastError" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Logoff">
      <soap12:operation soapAction="http://tempuri.org/Logoff" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMetaPage">
      <soap12:operation soapAction="http://tempuri.org/GetMetaPage" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ResetConduit">
      <soap12:operation soapAction="http://tempuri.org/ResetConduit" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMe">
      <soap12:operation soapAction="http://tempuri.org/GetMe" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLoginByUserID">
      <soap12:operation soapAction="http://tempuri.org/GetLoginByUserID" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetQuoteSubject">
      <soap12:operation soapAction="http://tempuri.org/GetQuoteSubject" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFieldLabelFromName">
      <soap12:operation soapAction="http://tempuri.org/GetFieldLabelFromName" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFileLabel">
      <soap12:operation soapAction="http://tempuri.org/GetFileLabel" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TestIDsForDeletions">
      <soap12:operation soapAction="http://tempuri.org/TestIDsForDeletions" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFieldList">
      <soap12:operation soapAction="http://tempuri.org/GetFieldList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLinksList">
      <soap12:operation soapAction="http://tempuri.org/GetLinksList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFileList">
      <soap12:operation soapAction="http://tempuri.org/GetFileList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPermission">
      <soap12:operation soapAction="http://tempuri.org/GetPermission" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidateSession">
      <soap12:operation soapAction="http://tempuri.org/ValidateSession" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddRecordsByXML">
      <soap12:operation soapAction="http://tempuri.org/AddRecordsByXML" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddRecordsByXMLString">
      <soap12:operation soapAction="http://tempuri.org/AddRecordsByXMLString" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddRecordsByDataset">
      <soap12:operation soapAction="http://tempuri.org/AddRecordsByDataset" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EditRecordsByXML">
      <soap12:operation soapAction="http://tempuri.org/EditRecordsByXML" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EditRecordsByXMLString">
      <soap12:operation soapAction="http://tempuri.org/EditRecordsByXMLString" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EditRecordsByDataset">
      <soap12:operation soapAction="http://tempuri.org/EditRecordsByDataset" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpsertRecordsByXML">
      <soap12:operation soapAction="http://tempuri.org/UpsertRecordsByXML" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpsertRecordsByXMLString">
      <soap12:operation soapAction="http://tempuri.org/UpsertRecordsByXMLString" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpsertRecordsByDataset">
      <soap12:operation soapAction="http://tempuri.org/UpsertRecordsByDataset" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetList">
      <soap12:operation soapAction="http://tempuri.org/GetList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DatasetToTextFile">
      <soap12:operation soapAction="http://tempuri.org/DatasetToTextFile" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSchemaAsXML">
      <soap12:operation soapAction="http://tempuri.org/GetSchemaAsXML" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSchemaAsXMLString">
      <soap12:operation soapAction="http://tempuri.org/GetSchemaAsXMLString" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteRecordsByXMLString">
      <soap12:operation soapAction="http://tempuri.org/DeleteRecordsByXMLString" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteRecordsByXML">
      <soap12:operation soapAction="http://tempuri.org/DeleteRecordsByXML" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadAndProcessDataset">
      <soap12:operation soapAction="http://tempuri.org/UploadAndProcessDataset" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateStatus">
      <soap12:operation soapAction="http://tempuri.org/UpdateStatus" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateS3Status">
      <soap12:operation soapAction="http://tempuri.org/UpdateS3Status" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSystemCodes">
      <soap12:operation soapAction="http://tempuri.org/GetSystemCodes" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetStandardizedPhoneNumber">
      <soap12:operation soapAction="http://tempuri.org/GetStandardizedPhoneNumber" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LogError">
      <soap12:operation soapAction="http://tempuri.org/LogError" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddAlert">
      <soap12:operation soapAction="http://tempuri.org/AddAlert" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="WebService_RowSet">
    <wsdl:port name="WebService_RowSetSoap" binding="tns:WebService_RowSetSoap">
      <soap:address location="https://hyspeco.selltis.com/webservice/rowset.asmx" />
    </wsdl:port>
    <wsdl:port name="WebService_RowSetSoap12" binding="tns:WebService_RowSetSoap12">
      <soap12:address location="https://hyspeco.selltis.com/webservice/rowset.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;
using System.Drawing;
using System.Data;
using System.Data.SqlClient;
using Microsoft.VisualBasic;
using System.Collections;
using System.IO;
using System.Net;
using Newtonsoft.Json;
using System.Web.Script.Serialization;
using System.Xml;
using System.Text.RegularExpressions;
using System.Diagnostics;
using Selltis.BusinessLogic;
using Selltis.Core;
using MailBee.ImapMail;
using System.Threading;

namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clProject goP;
        private clLog goLog;
        private clError goErr;
        private clPerm goPerm;
        private ClUI goUI;
        ScriptManager scriptManager = new ScriptManager();

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        public string sError;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();
        }
        public ScriptsCustom()
        {
            Initialize();
        }



        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //*** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //try
            //{




            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }

            //}

            return true;
        }

        public bool AC_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            clRowSet doRS = (clRowSet)par_doCallingObject;
            //AD 110/2017 TKT#1929 : Update Last Date for Company
            //goScr.RunScript("UpdateLastDateinCO", doRS);
            scriptManager.RunScript("UpdateLastDateinCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            par_doCallingObject = doRS;
            return true;
        }
        public bool AP_FormAfterSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;
            clArray doLink = new clArray();
            //NC #1953 creating an activity whenever CRL_AP  happen.
            if (doForm.GetMode().ToString() == "CREATION" & doForm.RowsetType == "CRL_AP")
            {
                string sID = doForm.GetCreateLinkedSourceRecSUID;
                //If the ID is not blank, find out if coming from an AC
                if (!string.IsNullOrEmpty(sID) & sID != null)
                {

                    clRowSet oRS = new clRowSet("AC", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_AC", sID, "", true, true);
                    oRS.SetFieldVal("DTE_STARTTIME", doForm.doRS.GetFieldVal("DTE_STARTTIME"));
                    oRS.SetFieldVal("TME_STARTTIME", doForm.doRS.GetFieldVal("TME_STARTTIME"));
                    oRS.SetFieldVal("MMO_NOTES", doForm.doRS.GetFieldVal("MMO_NOTES"));
                    oRS.Commit();

                }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CalcQuoteTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 03162018 Disable chk_merged
            doForm.SetControlState("CHK_Merged", 4);

            par_doCallingObject = doForm;

            return true;
        }
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 03162018 Click cancel on merge
            if (doForm.oVar.GetVar("CancelSave") != null && doForm.oVar.GetVar("CancelSave").ToString() == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                par_doCallingObject = doForm;
                return false;
            }

            par_doCallingObject = doForm;

            return true;
        }
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 03162018 Merge Functionality - run at end of CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (doForm.oVar.GetVar("CN_Merge") != null && doForm.oVar.GetVar("CN_Merge").ToString() != "1")
                    {
                        //Don't allow merge of contact to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID"))
                        {
                            //doForm.MessageBox("You cannot merge a contact to itself.  Please select a different merge to contact.", clC.SELL_MB_OK, , , , , , "MessageBoxEvent", , , doForm, , "OK", , , , "MergeCNFail")
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CN", "MergeFail");
                            return false;
                        }
                        else
                        {
                            //doForm.MessageBox("This contact will be merged to the target contact, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") & "'. Blank fields on the target contact will be filled from this contact record and all links will be copied to the target contact. Are you sure you want to merge this contact?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "MergeCN")
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CN", "Merge");
                            return false;
                        }
                    }
                }
            }

            par_doCallingObject = doForm;

            return true;
        }
        public bool CN_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            if (goP.GetVar("SENTFROMP21SIS") != null && goP.GetVar("SENTFROMP21SIS").ToString() != "1")
            {
                //Updated from Website or Mobile, Not from SIS, Need to sync back to P21 - Update MLS_SyncStatus
                doRS.SetFieldVal("MLS_SYNCTOP21STATUS", 2, 2);    //Needs to Sync
            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool CO_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 03162018 Disable chk_merged
            doForm.SetControlState("CHK_Merged", 4);

            // NC Create Activity log on save of journals 2 / 16 / 20
            doForm.oVar.SetVar("lLenJournal_FSAP", Strings.Len(doForm.doRS.GetFieldVal("MMO_FestoSAP")));
            doForm.oVar.SetVar("lLenJournal_MSAP", Strings.Len(doForm.doRS.GetFieldVal("MMO_MitsubishiSAP")));
            doForm.oVar.SetVar("lLenJournal_BSAP", Strings.Len(doForm.doRS.GetFieldVal("MMO_BalluffSAP")));
            doForm.oVar.SetVar("lLenJournal_USAP", Strings.Len(doForm.doRS.GetFieldVal("MMO_UniversalRobotsSAP")));
            doForm.SetControlState("MMO_FestoSAP", 1);
            doForm.SetControlState("MMO_MitsubishiSAP", 1);
            doForm.SetControlState("MMO_BalluffSAP", 1);
            doForm.SetControlState("MMO_UniversalRobotsSAP", 1);

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //AD 06262017 Ticket#1929: Make "TXT_CURANDPOT" Field GrayedOut as it is being Calculated.
            Form doForm = (Form)par_doCallingObject;

            doForm.SetControlState("TXT_CURANDPOT", 4);

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 03162018 Click cancel on merge
            if (doForm.oVar.GetVar("CancelSave").ToString() == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                par_doCallingObject = doForm;
                return false;
            }

            //AD ticket 1929 11082017 : Calculate "TXT_CURANDPOT" by Appending MLS_CurrentVolume & MLS_PotentialVolume.
            string CURRENTANDPOTENTIAL = null;
            string screatelog = "";

            //Ticket 1929
            string MLS_CurrentVolumeVAR = (doForm.doRS.GetFieldVal("MLS_CURVOLUME", 1) == null) ? "" : Strings.Left(doForm.doRS.GetFieldVal("MLS_CURVOLUME", 1).ToString(), 1);
            string MLS_PotentialVolumeVAR = (doForm.doRS.GetFieldVal("MLS_POTVOLUME", 1) == null) ? "" : Strings.Left(doForm.doRS.GetFieldVal("MLS_POTVOLUME", 1).ToString(), 1);

            CURRENTANDPOTENTIAL = MLS_CurrentVolumeVAR + MLS_PotentialVolumeVAR;

            doForm.doRS.SetFieldVal("TXT_CURANDPOT", CURRENTANDPOTENTIAL);
            //NC Create Activity log on save of journals 2/16/2018
            screatelog = doForm.oVar.GetVar("JournalCreateLog").ToString();

            if (screatelog == "1")
            {
                scriptManager.RunScript("CO_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "lLenJournal_FSAP", "JournalWithHardReturns_FSAP");
                scriptManager.RunScript("CO_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "lLenJournal_MSAP", "JournalWithHardReturns_MSAP");
                scriptManager.RunScript("CO_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "lLenJournal_BSAP", "JournalWithHardReturns_BSAP");
                scriptManager.RunScript("CO_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "lLenJournal_USAP", "JournalWithHardReturns_USAP");

            }

            par_doCallingObject = doForm;

            return true;
        }
        public bool CO_CreateActLog_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: doForm.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //TLD 11/28/2012 Add as Type Matrix Update
            //from CO
            //2004/12/22 10:29:34 MAR Edited. SetLinkVals cause an error on line 37 of SetLinkVal: incorrect type.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //PURPOSE:
            //		TLD 11/28/2012 Adds Act Log with new matrix update from CO
            //		Run from enforce only in non-CREATION (MODIF) mode.
            //		If it is run in CREATION mode, the new Activity will not be linked to the original Opp.
            //RETURNS:
            //		1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.
            //NC Create Activity log on save of journals 2/16/2018
            //SKO 01302017 TKT#:1414 create an activity log automatically when a journal is updated on a To-Do
            Form doForm = (Form)par_doCallingObject;
            string sNotes = "";
            string sWork = "";
            int lWork = 0;
            string sMessage = null;

            //'If answered Yes on MB to create a journal, then go directly to script that creates journal.
            //If UCase(par_s1) = "YES" Then
            //    GoTo CREATEACTLOG
            //End If


            //If doForm.doRS.GetFieldVal("SI__SHARESTATE", 2) < 2 Then
            //    sMessage = "A journal Activity cannot be created because this Company is not shared."
            //    doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") & sMessage & vbCrLf)
            //    doForm.oVar.SetVar("CO_CreateActLog_Ran", "1")
            //    'Don't return out of formonsave. This message will be displayed at end of form save.
            //    doForm.oVar.SetVar("ContinueSave", "1")
            //    Return True
            //End If

            doForm.oVar.SetVar("TD_CreateActLog_Ran", "1");
            string coname = doForm.doRS.GetFieldVal("TXT_COMPANYNAME").ToString();
            //sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            //CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            //hard returns in the journal field
            sWork = doForm.oVar.GetVar(par_s3).ToString();
            //CS 2/4/10
            if (string.IsNullOrEmpty(sWork))
                return true;
            //We didn't hit MessageBoxEvent from entering a journal note.

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
            //clRowSet doCO = new clRowSet("CO",3, "TXT_COMPANYNAME='" + coname + "'","", "GID_ID");

            //sWork = doForm.doRS.GetFieldVal("MMO_Journal");
            //'CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            //'hard returns in the journal field
            //sWork = doForm.oVar.GetVar("JournalWithHardReturns")

            if (par_s2 == "lLenJournal_FSAP")
            {
                doNew.SetFieldVal("LNK_RELATED_VE", "38636461-3832-6230-5645-382f32342f32");
            }

            if (par_s2 == "lLenJournal_MSAP")
            {
                //doNew.SetFieldVal("LNK_RELATED_VE", "238f083f-37b7-4dd2-5645-9f9500e88abc");  //Mitsubishi

                //SB 02-27-2018 Tkt#2123 On CO Stratagic action VE selection record has to create targeted AC to 'Mitsubishi Electric Automation' Earlier it was targetted to 'Mitsubishi'.
                doNew.SetFieldVal("LNK_RELATED_VE", "35383161-3161-3264-5645-382f32342f32");    //Mitsubishi Electric Automation
            }

            if (par_s2 == "lLenJournal_BSAP")
            {
                doNew.SetFieldVal("LNK_RELATED_VE", "65f41b80-9ed2-4244-5645-a4020107f7d2");
            }

            if (par_s2 == "lLenJournal_USAP")
            {
                doNew.SetFieldVal("LNK_RELATED_VE", "31396561-3765-3739-5645-392f382f3230");
            }

            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar(par_s2));
            sNotes = Strings.Left(sWork, lWork);
            sNotes = sNotes + "== Created from ToDO '" + doForm.doRS.GetFieldVal("SYS_Name") + "'";
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            doNew.SetFieldVal("MLS_Status", 1, 2);
            //Completed
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            //CS doNew.SetFieldVal("TME_ENDTIME", doForm.doRS.GetFieldVal("TME_Time"))
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");

            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));

            //In the code below Paul added IsObjectAssigned tests because SetLinkVals weren't working in some cases.
            //==> Remove the IsObjectAssigned tests.

            //doLink = doForm.doRS.GetLinkVal("LNK_CONNECTED_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (10).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            //doNew.SetLinkVal("LNK_Related_CN", doLink);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (11).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If


            doNew.SetLinkVal("LNK_RELATED_CO", doForm.doRS.GetFieldVal("GID_ID"));
            oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
            doLink = doForm.doRS.GetLinkVal("LNK_RELATED_GR", ref doLink, true, 0, -1, "A_a", ref oTable);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_Related_GR) is not assigned.", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            doNew.SetLinkVal("LNK_Related_GR", doLink);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_Related_GROUP) is not assigned (2).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If

            doNew.SetFieldVal("MLS_TYPE", 24, 2);
            //US_31=Journal
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(doNew.GetFieldVal("MMO_HISTORY").ToString(), "Created."));
            //doNew.SetFieldVal("LNK_RELATED_CO", doForm.GetRecordID())

            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;
                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);

                goLog.Log("MessageBoxEvent", doForm.oVar.GetVar("ScriptMessages").ToString(), 1, false, true);
                return false;
            }
            doForm.oVar.SetVar("JournalCreateLog", "0");
            doNew = null;
            doLink = null;

            return true;

        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 03162018 Merge Functionality - run at end of CO_FormOnSave_Post
            if (Convert.ToInt32(doForm.doRS.GetLinkCount("LNK_MergedTo_CO")) > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (doForm.oVar.GetVar("CO_Merge").ToString() != "1")
                    {
                        //Don't allow merge of company to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID"))
                        {
                            //doForm.MessageBox("You cannot merge a company to itself.  Please select a different merge to company.", clC.SELL_MB_OK, , , , , , "MessageBoxEvent", , , doForm, , "OK", , , , "MergeCOFail")
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CO", "MergeFail");
                            return false;
                        }
                        else
                        {
                            //doForm.MessageBox("This company will be merged to the target company, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") & "'. Blank fields on the target company will be filled from this company record and all links will be copied to the target company. Are you sure you want to merge this company?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "MergeCO")
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CO", "Merge");
                            return false;
                        }
                    }
                }
            }

            par_doCallingObject = doForm;

            return true;
        }
        public bool CO_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sCurVol = "";
            string sPotVol = "";
            int iCurCount = Convert.ToInt32(doRS.GetLinkCount("LNK_CURRENT_PD"));
            int iPotCount = Convert.ToInt32(doRS.GetLinkCount("LNK_POTENTIAL_PD"));
            //Ticket 1929
            //Target Account Matrix Profiling
            //Copy LNK_Current_PD to LNK_Potential_PD
            doRS.SetFieldVal("LNK_POTENTIAL_PD", doRS.GetFieldVal("LNK_CURRENT_PD"));

            //Record total selections from LNK_Current_PD and LNK_Potential_PD
            doRS.SetFieldVal("INT_CURCOUNT", iCurCount, 2);
            doRS.SetFieldVal("INT_POTCOUNT", iPotCount, 2);

            //Calculate Potential Percentage & Potential Portfolio
            clRowSet doPDRS = new clRowSet("PD", 3, "", "", "GID_ID");
            int iPDCount = Convert.ToInt32(doPDRS.Count());
            if (iPDCount != 0)
            {
                if (iPotCount != 0)
                {
                    doRS.SetFieldVal("SR__POTENTIALPERC", (iCurCount / iPotCount) * 100, 2);
                }
                doRS.SetFieldVal("SR__POTENTIALPORTFOLIO", (iPotCount / iPDCount) * 100, 2);

                doRS.SetFieldVal("SR__ProdLinePot", (iPotCount - iCurCount), 2);
                sCurVol = (doRS.GetFieldVal("MLS_CURVOLUME") == null) ? "" : Strings.Left(doRS.GetFieldVal("MLS_CURVOLUME").ToString(), 1);
                sPotVol = (doRS.GetFieldVal("MLS_POTVOLUME") == null) ? "" : Strings.Left(doRS.GetFieldVal("MLS_POTVOLUME").ToString(), 1);
                //for now record a "Z" if it is make selection
                if (sCurVol == "<")
                {
                    sCurVol = "Z";
                }
                //for now record a "Z" if it is make selection
                if (sPotVol == "<")
                {
                    sPotVol = "Z";
                }

                //set field to cur & pot
                doRS.SetFieldVal("TXT_CURANDPOT", sCurVol + sPotVol);
            }

            //---------- Target Account -----------------
            //Set Product Potential Quadrant
            double rTotalPortfolio = (doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2) == null) ? 0 : Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2));
            double rPotentialProduct = (doRS.GetFieldVal("SR__POTENTIALPERC", 2) == null) ? 0 : Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPERC", 2));

            if (rTotalPortfolio >= 51 & rTotalPortfolio <= 100)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                {
                    //Set to 1
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "1");
                }

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                {
                    //Set to 3
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "3");
                }
            }

            if (rTotalPortfolio >= 0 & rTotalPortfolio <= 50)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                {
                    //Set to 2
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "2");
                }

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                {
                    //Set to 4
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "4");
                }
            }

            //Because COs are updated nightly to set custom
            //date fields, need to write to custom mod time and mod by fields
            //AutoCOUpdate does NOT run recordonsave
            doRS.SetFieldVal("TXT_CusModBy", goP.GetMe("CODE"));
            doRS.SetFieldVal("DTT_CusModTime", "Today|Now");
            //---------End Target Account Matrix Profiling


            //VS ******** : Regenerate Quote's & Opps' SysNames when a company name changes. This is causing failure modes in Linkbox Searchs when a company name changes.
            string sCompID = doRS.GetCurrentRecID(0).ToString();
            clRowSet doCORS = new clRowSet("CO", clC.SELL_READONLY, "GID_ID=" + sCompID, "", "TXT_COMPANYNAME", 1);

            if (doCORS.GetFirst() == 1)
            {
                string sCompNameOld = doCORS.GetFieldVal("TXT_COMPANYNAME").ToString();
                string sCompNameNew = doRS.GetFieldVal("TXT_COMPANYNAME").ToString();

                if (Microsoft.VisualBasic.Strings.Left(sCompNameOld, 15).Trim() != Microsoft.VisualBasic.Strings.Left(sCompNameNew, 15).Trim())
                {
                    //Create a Job in the Metadata and the SysName Regen automator should pick it up.
                    string sMDText = "";
                    goTR.StrWrite(ref sMDText, "COMPANYID", sCompID);
                    goTR.StrWrite(ref sMDText, "REGEN_FILE_1", "OP");
                    goTR.StrWrite(ref sMDText, "REGEN_REVERSELINK_1", "LNK_FOR_CO");
                    goTR.StrWrite(ref sMDText, "REGEN_FILE_2", "QT");
                    goTR.StrWrite(ref sMDText, "REGEN_REVERSELINK_2", "LNK_TO_CO");
                    goTR.StrWrite(ref sMDText, "REGEN_FILE_COUNT", "2");
                    //goMeta.PageWrite("GLOBAL", "SYSREG_" + Guid.NewGuid().ToString(), sMDText, "", goP.GetMe("ID"), "XX");
                    goMeta.PageWrite("GLOBAL", "SYSREG_" + DateTime.UtcNow.ToString("MMddyyyyhhmmssffff"), sMDText, "", goP.GetMe("ID"), "XX");
                }
            }

            par_doCallingObject = doRS;
            return true;
        }
        public bool CO_FormControlOnChange_BTN_FestoSAP(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //goP.TraceLine("", "", sProc)
            //goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)
            Form doForm = (Form)par_doCallingObject;
            //Dim sWork As String
            string sParams = "";
            string sDateStamp = "";

            //GetDateTimeStamp parameters:
            //par_s1: Format of the date and time. Supported values:
            //			NEUTRAL		'Default: Ex: '2004-09-24 16:37 GMT' if par_s4 is 'UTC'.
            //			REGIONAL	'Same as NEUTRAL until regional formatting is implemented.
            //par_s2: Name of the variable in which to return the date/time stamp.
            //par_s3: Format of the user code to include. Supported values:
            //			NONE		'Default. Add no user code.
            //			CODE		'User Code.
            //par_s4: Time zone (UTC (GMT) is the default). Supported values:
            //           UTC         'Default
            //           UTCNOOFFSETLABEL
            //           USER        
            //           USERNOOFFSETLABEL
            //           SERVER  
            //           SERVERNOOFFSETLABEL
            par_oReturn = sDateStamp;
            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "", "CODE", "USERNOOFFSETLABEL");
            sDateStamp = par_oReturn.ToString();
            //returns var sDateStamp
            doForm = (Form)par_doCallingObject;
            doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", "Cancel", "", sDateStamp + " ", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", System.Reflection.MethodInfo.GetCurrentMethod().Name);

            par_doCallingObject = doForm;
            return true;

        }
        public bool CO_FormControlOnChange_BTN_MitsubishiSAP(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //goP.TraceLine("", "", sProc)
            //goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)
            Form doForm = (Form)par_doCallingObject;
            //Dim sWork As String
            string sParams = "";
            string sDateStamp = "";

            //GetDateTimeStamp parameters:
            //par_s1: Format of the date and time. Supported values:
            //			NEUTRAL		'Default: Ex: '2004-09-24 16:37 GMT' if par_s4 is 'UTC'.
            //			REGIONAL	'Same as NEUTRAL until regional formatting is implemented.
            //par_s2: Name of the variable in which to return the date/time stamp.
            //par_s3: Format of the user code to include. Supported values:
            //			NONE		'Default. Add no user code.
            //			CODE		'User Code.
            //par_s4: Time zone (UTC (GMT) is the default). Supported values:
            //           UTC         'Default
            //           UTCNOOFFSETLABEL
            //           USER        
            //           USERNOOFFSETLABEL
            //           SERVER  
            //           SERVERNOOFFSETLABEL
            par_oReturn = sDateStamp;
            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "", "CODE", "USERNOOFFSETLABEL");
            sDateStamp = par_oReturn.ToString();
            //returns var sDateStamp
            doForm = (Form)par_doCallingObject;
            doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", "Cancel", "", sDateStamp + " ", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", System.Reflection.MethodInfo.GetCurrentMethod().Name);

            par_doCallingObject = doForm;
            return true;

        }
        public bool CO_FormControlOnChange_BTN_BalluffSAP(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //goP.TraceLine("", "", sProc)
            //goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)
            Form doForm = (Form)par_doCallingObject;
            //Dim sWork As String
            string sParams = "";
            string sDateStamp = "";

            //GetDateTimeStamp parameters:
            //par_s1: Format of the date and time. Supported values:
            //			NEUTRAL		'Default: Ex: '2004-09-24 16:37 GMT' if par_s4 is 'UTC'.
            //			REGIONAL	'Same as NEUTRAL until regional formatting is implemented.
            //par_s2: Name of the variable in which to return the date/time stamp.
            //par_s3: Format of the user code to include. Supported values:
            //			NONE		'Default. Add no user code.
            //			CODE		'User Code.
            //par_s4: Time zone (UTC (GMT) is the default). Supported values:
            //           UTC         'Default
            //           UTCNOOFFSETLABEL
            //           USER        
            //           USERNOOFFSETLABEL
            //           SERVER  
            //           SERVERNOOFFSETLABEL
            par_oReturn = sDateStamp;
            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "", "CODE", "USERNOOFFSETLABEL");
            sDateStamp = par_oReturn.ToString();
            //returns var sDateStamp
            doForm = (Form)par_doCallingObject;
            doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", "Cancel", "", sDateStamp + " ", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", System.Reflection.MethodInfo.GetCurrentMethod().Name);

            par_doCallingObject = doForm;
            return true;

        }
        public bool CO_FormControlOnChange_BTN_UniversalRobotsSAP(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //goP.TraceLine("", "", sProc)
            //goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)
            Form doForm = (Form)par_doCallingObject;
            //Dim sWork As String
            string sParams = "";
            string sDateStamp = "";

            //GetDateTimeStamp parameters:
            //par_s1: Format of the date and time. Supported values:
            //			NEUTRAL		'Default: Ex: '2004-09-24 16:37 GMT' if par_s4 is 'UTC'.
            //			REGIONAL	'Same as NEUTRAL until regional formatting is implemented.
            //par_s2: Name of the variable in which to return the date/time stamp.
            //par_s3: Format of the user code to include. Supported values:
            //			NONE		'Default. Add no user code.
            //			CODE		'User Code.
            //par_s4: Time zone (UTC (GMT) is the default). Supported values:
            //           UTC         'Default
            //           UTCNOOFFSETLABEL
            //           USER        
            //           USERNOOFFSETLABEL
            //           SERVER  
            //           SERVERNOOFFSETLABEL
            par_oReturn = sDateStamp;
            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "", "CODE", "USERNOOFFSETLABEL");
            sDateStamp = par_oReturn.ToString();
            //returns var sDateStamp
            doForm = (Form)par_doCallingObject;
            doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", "Cancel", "", sDateStamp + " ", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", System.Reflection.MethodInfo.GetCurrentMethod().Name);

            par_doCallingObject = doForm;
            return true;

        }
        public bool SysNameRegenerator_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS ******** : Automator to Regenerate SysNames of OP and QT when their corresponding CO name is updated.

            //Get the first MD page that starts with SYSREG_ 
            string sSelect = "Select TOP 1 TXT_Page as 'Page' FROM MD WHERE TXT_Page LIKE 'SYSREG_%' AND TXT_Page NOT LIKE '%_RESULTS' ORDER BY DTT_CREATIONTIME";

            DataTable dtAge = new DataTable();
            SqlConnection sqlConnection1 = goData.GetConnection();

            SqlCommand cmd = new SqlCommand();
            SqlDataReader reader;

            cmd.CommandText = sSelect;
            cmd.CommandType = CommandType.Text;
            cmd.Connection = sqlConnection1;

            reader = cmd.ExecuteReader();

            //if any rows were returned
            if (reader.HasRows)
            {
                dtAge.Load(reader);
                reader.Close();
                sqlConnection1.Close();
            }
            // if no rows found
            else
            {
                reader.Close();
                sqlConnection1.Close();
                return true;
            }
            //Finish getting any Meta Info

            //If MD Page found read and process the page
            string sPageName = dtAge.Rows[0]["Page"].ToString();
            string sPageNameResult = sPageName + "_RESULTS";
            string sMD = goMeta.PageRead("GLOBAL", sPageName, "", false, "XX", false);

            Int16 iErrorCount = 1;
            System.Data.SqlClient.SqlConnection par_oConnection = goData.GetConnection(); //new System.Data.SqlClient.SqlConnection();

            //Start writing log into a new MD page SYSREG_....._RESULTS
            goMeta.PageWrite("GLOBAL", sPageNameResult, sMD, "", "", "XX");
            goMeta.LineWrite("GLOBAL", sPageNameResult, "ProcessingStarted", DateTime.UtcNow.ToString("MM-dd-yyyy hh:mm:ss") + " UTC", ref par_oConnection, "", "XX");

            //Return if no Metadata
            if (string.IsNullOrEmpty(sMD))
            {
                goMeta.LineWrite("GLOBAL", sPageNameResult, "ERROR" + iErrorCount.ToString(), "No MetaData Found", ref par_oConnection, "", "XX");
                iErrorCount++;
                return true;
            }

            string sCompanyID = goTR.StrRead(sMD, "COMPANYID", "", true);
            string sRegenFileCount = goTR.StrRead(sMD, "REGEN_FILE_COUNT", "0", true);
            int iRegenFileCount;
            int.TryParse(sRegenFileCount, out iRegenFileCount);

            if (string.IsNullOrEmpty(sCompanyID))
            {
                goMeta.LineWrite("GLOBAL", sPageNameResult, "ERROR" + iErrorCount.ToString(), "Company ID Not Found", ref par_oConnection, "", "XX");
                iErrorCount++;
                return true;
            }
            if (iRegenFileCount == 0)
            {
                goMeta.LineWrite("GLOBAL", sPageNameResult, "ERROR", "File Count 0", ref par_oConnection, "", "XX");
                iErrorCount++;
                return true;
            }

            string sFile = "";
            string sLink = "";
            string sFilter = "";
            long lBiID = 0;
            int iCount = 0;
            int iCountFailed = 0;
            int iCountSuccess = 0;
            for (int i = 1; i <= iRegenFileCount; i++)
            {
                sFile = goTR.StrRead(sMD, "REGEN_FILE_" + i.ToString(), "", true);
                sLink = goTR.StrRead(sMD, "REGEN_REVERSELINK_" + i.ToString(), "", true);
                if (string.IsNullOrEmpty(sFile) || string.IsNullOrEmpty(sLink))
                {
                    if (string.IsNullOrEmpty(sFile))
                    {
                        goMeta.LineWrite("GLOBAL", sPageNameResult, "ERROR" + iErrorCount.ToString(), "Reverse Link 1 Empty", ref par_oConnection, "", "XX");
                        iErrorCount++;
                    }
                    else
                    {
                        goMeta.LineWrite("GLOBAL", sPageNameResult, "ERROR" + iErrorCount.ToString(), "File 1 Empty", ref par_oConnection, "", "XX");
                        iErrorCount++;
                    }
                    continue;
                }
                sFilter = sLink + "=" + sCompanyID;

                goMeta.LineWrite("GLOBAL", sPageNameResult, "File_" + i.ToString() + "_Started", DateTime.UtcNow.ToString("MM-dd-yyyy hh:mm:ss") + " UTC", ref par_oConnection, "", "XX");

                //Get Total Record Count and update MD
                clRowSet doRSCount = new clRowSet(sFile, clC.SELL_COUNT, sFilter);
                goMeta.LineWrite("GLOBAL", sPageNameResult, "File_" + i.ToString() + "_TotalRecs", doRSCount.GetFieldVal("BI__COUNT").ToString(), ref par_oConnection, "", "XX");
                do
                {
                    clRowSet doRS = new clRowSet(sFile, clC.SELL_EDIT, sFilter + " AND bi__id>" + lBiID.ToString(), "bi__id asc", "", 50, "", "", "", "", "", true, true);
                    iCount = iCount + Convert.ToInt32(doRS.Count());
                    if (doRS.GetFirst() == 1)
                    {
                        do
                        {
                            if (doRS.Commit() == 0)
                            {
                                iCountFailed = iCountFailed + 1;
                            }
                            else
                            {
                                iCountSuccess = iCountSuccess + 1;
                            }
                        } while (doRS.GetNext() == 1);
                        lBiID = Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                    }
                    else
                    {
                        break;
                    }
                } while (true);
                goMeta.LineWrite("GLOBAL", sPageNameResult, "File_" + i.ToString() + "_SuccessRecs", iCountSuccess, ref par_oConnection, "", "XX");
                goMeta.LineWrite("GLOBAL", sPageNameResult, "File_" + i.ToString() + "_FailedRecs", iCountFailed, ref par_oConnection, "", "XX");

                sFile = "";
                sLink = "";
                sFilter = "";
                iCount = 0;
                iCountSuccess = 0;
                iCountFailed = 0;

                goMeta.LineWrite("GLOBAL", sPageNameResult, "File_" + i.ToString() + "_Completed", DateTime.UtcNow.ToString("MM-dd-yyyy hh:mm:ss") + " UTC", ref par_oConnection, "", "XX");

            }

            goMeta.LineWrite("GLOBAL", sPageNameResult, "ProcessingCompleted", DateTime.UtcNow.ToString("MM-dd-yyyy hh:mm:ss") + " UTC", ref par_oConnection, "", "XX");

            goMeta.PageDelete("GLOBAL", sPageName, "XX");

            return true;
        }


        public bool IV_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.SetFieldVal("LNK_INVOLVES_US", doRS.GetFieldVal("LNK_CREDITEDTO_US").ToString());
            doRS.SetFieldVal("LNK_INVOLVES_US", doRS.GetFieldVal("LNK_PEER_US").ToString());

            par_doCallingObject = doRS;

            return true;
        }

        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_sFileName: file for which to return the sort.
            //par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //PURPOSE:
            //	Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            //       By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            //       add CASEs for them here. To not override the default sort, par_oReturn must be "".
            //       IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            //       with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            //       the sort likely should be ASC.
            //RETURNS:
            //		Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            //Select Case (par_sFileName)
            //    Case "AA"
            //        'This is a reverse sort, typically used for datetime fields
            //        If par_sReverseDirection = "1" Then
            //            sResult = "SYS_NAME ASC"
            //        Else
            //            sResult = "SYS_NAME DESC"
            //        End If
            //    Case "BB"
            //        'Reverse sort on Creation datetime
            //        If par_sReverseDirection = "1" Then
            //            sResult = "DTT_CREATIONTIME ASC"
            //        Else
            //            sResult = "DTT_CREATIONTIME DESC"
            //        End If
            //        'Case Else
            //        '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            //        '    'it is not needed here
            //End Select

            par_oReturn = sResult;

            return true;

        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.GetMode() == "CREATION")
            {
                //SKO 08092016 Ticket1192: 
                //SKO 09192016 Ticket1253: 
                string sID = doForm.GetCreateLinkedSourceRecSUID;
                if (!string.IsNullOrEmpty(sID) & sID != null)
                {
                    if (doForm.RowsetType == "CRL_OP" && goTR.GetFileFromSUID(sID) == "QT")     //SB 04/16/2019 TKT#2779 CRL_OP Stage change to QT
                    {
                        clRowSet doQTRS = new clRowSet("QT", 3, "GID_ID='" + sID + "'", "", "MLS_STATUS");

                        if (doQTRS.GetFirst() == 1)
                        {
                            //NC 1831 Make MLS_STAGE = quote in op when op is linked to quote
                            doForm.doRS.SetFieldVal("MLS_STAGE", "Quote");
                            if (doQTRS.GetFieldVal("MLS_STATUS").ToString() == "30 Won")
                            {
                                doForm.doRS.SetFieldVal("MLS_STATUS", "Won");
                            }
                        }
                        else
                        {
                            //tkt #1931 in selltis70.selltis.com
                            clRowSet doOPRS = new clRowSet("OP", 3, "GID_ID='" + sID + "'");

                            if (doOPRS.GetFirst() == 1)
                            {
                                doForm.doRS.SetFieldVal("MLS_STAGE", "Opportunity");
                            }
                        }
                    }

                    //End If
                }
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool OP_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;

            //NC 1831 Make MLS_STAGE = quote in op when op is linked to quote
            if (doForm.GetMode() == "CREATION")
            {
                //SKO 08092016 Ticket1192: 
                //SKO 09192016 Ticket1253: 
                string sID = doForm.GetCreateLinkedSourceRecSUID;
                if (!string.IsNullOrEmpty(sID) & sID != null)
                {
                    if (doForm.RowsetType == "CRL_OP" && goTR.GetFileFromSUID(sID) == "QT") //SB 04/16/2019 TKT#2779 CRL_OP Stage change to QT
                    {
                        doForm.doRS.SetFieldVal("MLS_STAGE", "Quote");
                        clRowSet doQTRS = new clRowSet("QT", 3, "GID_ID='" + sID + "'", "", "MLS_STATUS");

                        if (doQTRS.GetFirst() == 1)
                        {
                            if (doQTRS.GetFieldVal("MLS_STATUS").ToString() == "30 Won")
                            {
                                doForm.doRS.SetFieldVal("MLS_STATUS", "Won");
                            }
                        }
                    }

                    //End If
                }
            }


            if (doForm.doRS.GetFieldVal("MMO_JOURNAL").ToString() != "")
            {
                clRowSet doACRS = new clRowSet("AC", 2, "", "", "MLS_TYPE", -1, "", "", "", "", "", true, true);
                doACRS.SetFieldVal("MLS_TYPE", doForm.doRS.GetFieldVal("MLS_JOURNALTYPE", 2), 2);
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool OP_FormControlOnChange_BTN_INSERTLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Need to enforce Type BEFORE allowing user to enter
            if (((doForm.doRS.GetFieldVal("MLS_JOURNALTYPE", 2) == null) ? -1 : Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_JOURNALTYPE", 2))) == 0)
            {
                doForm.MessageBox("Please select a  Journal Type.");
                return false;
            }


            return true;
        }
        public bool QT_FormControlOnChange_BTN_INSERTLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Need to enforce Type BEFORE allowing user to enter
            if (((doForm.doRS.GetFieldVal("MLS_JOURNALTYPE", 2) == null) ? -1 : Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_JOURNALTYPE", 2))) == 0)
            {
                doForm.MessageBox("Please select a  Journal Type.");
                return false;
            }


            return true;
        }
        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            //the user's response.
            //Par_s5 will always be the name of the script that called doform.MessageBox
            //Par_s1 will be whatever button the user clicked.
            //Par_s2-Par_s4 can be whatever else you want to pass.
            //In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            //After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc = null;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            Desktop doDesktop = null;

            if (par_s5.ToUpper().Contains("FORM"))
            {
                doForm = (Form)par_doCallingObject;
            }
            else if (par_s5.ToUpper().Contains("VIEW"))
            {
                doDesktop = (Desktop)par_doCallingObject;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
            }


            string sJournal = "";
            string sWork = "";
            string screatelog = "";
            string sSolution = "";
            string sClosenotes = "";
            string sCorrectiveaction = "";

            try
            {
                switch (par_s5.ToUpper())
                {

                    //VS 03162018 For Merge records
                    case "MERGE":
                        par_bRunNext = false;
                        doForm = (Form)par_doCallingObject;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                //run merge script, continue save
                                par_doCallingObject = doForm.doRS;
                                bool runnext = true;
                                scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref runnext, ref par_sSections);
                                doForm.doRS = (clRowSet)par_doCallingObject;
                                break;
                            case "NO":
                                //Clear merged to co linkbox, continue save
                                doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);

                                break;
                            case "CANCEL":
                                //Clear merged to co linkbox, cancel save
                                doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                doForm.oVar.SetVar("CancelSave", "1");
                                break;
                        }
                        par_doCallingObject = doForm;
                        break;
                    //Added for Merge record to itself
                    case "MERGEFAIL":
                        par_bRunNext = false;
                        doForm = (Form)par_doCallingObject;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                //Clear merged to co linkbox, cancel save
                                doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                doForm.oVar.SetVar("CancelSave", "1");
                                break;
                        }
                        par_doCallingObject = doForm;
                        break;


                    //SGR 30012015 TKT#292
                    case "OP_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                par_bRunNext = false;
                                sJournal = (doForm.doRS.GetFieldVal("MMO_Journal") == null) ? "" : doForm.doRS.GetFieldVal("MMO_Journal").ToString();
                                //CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                //Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                sWork = par_s2;
                                doForm.doRS.oVar.SetVar("JournalWithHardReturns", sWork + Constants.vbCrLf + sJournal);
                                if (!string.IsNullOrEmpty(sWork))
                                {
                                    //CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                    //If not defined in WOP, default is 1
                                    string MTA_GLOBALVar = (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS") == null) ? "" : doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS").ToString();
                                    if (MTA_GLOBALVar != "0")
                                    {
                                        sWork = goTR.Replace(sWork, Constants.vbCrLf.ToString(), Strings.Chr(32).ToString() + Strings.Chr(32).ToString() + Strings.Chr(32).ToString());
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                    }
                                    else
                                    {
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                    }
                                    doForm.MoveToField("MMO_JOURNAL");
                                    //doForm.oVar.SetVar("sJournalVal", sWork)
                                    doForm.doRS.oVar.SetVar("OP_JournalType", doForm.doRS.GetFieldVal("MLS_JOURNALTYPE", 1));
                                    doForm.doRS.SetFieldVal("MLS_JOURNALTYPE", 0, 2);
                                }
                                break;
                        }
                        break;
                    case "CO_FORMCONTROLONCHANGE_BTN_FESTOSAP":
                        switch (par_s1.ToUpper())
                        {

                            case "OK":
                                par_bRunNext = false;
                                sJournal = doForm.doRS.GetFieldVal("MMO_FestoSAP").ToString();
                                //CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                //Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                sWork = par_s2;
                                doForm.oVar.SetVar("JournalWithHardReturns_FSAP", sWork + Environment.NewLine + sJournal);
                                doForm.oVar.SetVar("JournalCreateLog", 1);
                                if (!string.IsNullOrEmpty(sWork))
                                {
                                    //CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                    //If not defined in WOP, default is 1
                                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS_FSAP").ToString() != "0")
                                    {
                                        sWork = goTR.Replace(sWork, Environment.NewLine, Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString());
                                        doForm.doRS.SetFieldVal("MMO_FestoSAP", sWork + Environment.NewLine + sJournal);
                                    }
                                    else
                                    {
                                        doForm.doRS.SetFieldVal("MMO_FestoSAP", sWork + Environment.NewLine + sJournal);
                                        //doForm.oVar.SetVar("sJournalVal", sWork)
                                    }
                                    doForm.MoveToField("MMO_FestoSAP");
                                }

                                break;
                        }

                        break;
                    case "CO_FORMCONTROLONCHANGE_BTN_MITSUBISHISAP":
                        switch (par_s1.ToUpper())
                        {
                            case "OK":
                                par_bRunNext = false;
                                sJournal = doForm.doRS.GetFieldVal("MMO_MitsubishiSAP").ToString();
                                //CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                //Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                sWork = par_s2;
                                doForm.oVar.SetVar("JournalCreateLog", 1);
                                doForm.oVar.SetVar("JournalWithHardReturns_MSAP", sWork + Environment.NewLine + sJournal);
                                if (!string.IsNullOrEmpty(sWork))
                                {
                                    //CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                    //If not defined in WOP, default is 1
                                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS_MSAP").ToString() != "0")
                                    {
                                        sWork = goTR.Replace(sWork, Environment.NewLine, Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString());
                                        doForm.doRS.SetFieldVal("MMO_MitsubishiSAP", sWork + Environment.NewLine + sJournal);
                                    }
                                    else
                                    {
                                        doForm.doRS.SetFieldVal("MMO_MitsubishiSAP", sWork + Environment.NewLine + sJournal);
                                        //doForm.oVar.SetVar("sJournalVal", sWork)
                                    }
                                    doForm.MoveToField("MMO_MitsubishiSAP");
                                }

                                break;
                        }

                        break;
                    case "CO_FORMCONTROLONCHANGE_BTN_BALLUFFSAP":
                        switch (par_s1.ToUpper())
                        {
                            case "OK":
                                par_bRunNext = false;
                                sJournal = doForm.doRS.GetFieldVal("MMO_BalluffSAP").ToString();
                                //CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                //Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                sWork = par_s2;
                                doForm.oVar.SetVar("JournalCreateLog", 1);
                                doForm.oVar.SetVar("JournalWithHardReturns_BSAP", sWork + Environment.NewLine + sJournal);
                                if (!string.IsNullOrEmpty(sWork))
                                {
                                    //CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                    //If not defined in WOP, default is 1
                                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS_BSAP").ToString() != "0")
                                    {
                                        sWork = goTR.Replace(sWork, Environment.NewLine, Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString());
                                        doForm.doRS.SetFieldVal("MMO_BalluffSAP", sWork + Environment.NewLine + sJournal);
                                    }
                                    else
                                    {
                                        doForm.doRS.SetFieldVal("MMO_BalluffSAP", sWork + Environment.NewLine + sJournal);
                                        //doForm.oVar.SetVar("sJournalVal", sWork)
                                    }
                                    doForm.MoveToField("MMO_BalluffSAP");
                                }

                                break;
                        }

                        break;
                    case "CO_FORMCONTROLONCHANGE_BTN_UNIVERSALROBOTSSAP":
                        switch (par_s1.ToUpper())
                        {
                            case "OK":
                                par_bRunNext = false;
                                sJournal = doForm.doRS.GetFieldVal("MMO_UniversalRobotsSAP").ToString();
                                //CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                //Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                sWork = par_s2;
                                doForm.oVar.SetVar("JournalCreateLog", 1);
                                doForm.oVar.SetVar("JournalWithHardReturns_USAP", sWork + Environment.NewLine + sJournal);
                                if (!string.IsNullOrEmpty(sWork))
                                {
                                    //CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                    //If not defined in WOP, default is 1
                                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNSUSAP").ToString() != "0")
                                    {
                                        sWork = goTR.Replace(sWork, Environment.NewLine, Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString());
                                        doForm.doRS.SetFieldVal("MMO_UniversalRobotsSAP", sWork + Environment.NewLine + sJournal);
                                    }
                                    else
                                    {
                                        doForm.doRS.SetFieldVal("MMO_UniversalRobotsSAP", sWork + Environment.NewLine + sJournal);
                                        //doForm.oVar.SetVar("sJournalVal", sWork)
                                    }
                                    doForm.MoveToField("MMO_UniversalRobotsSAP");
                                }

                                break;
                        }

                        break;
                    case "QT_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                par_bRunNext = false;
                                sJournal = (doForm.doRS.GetFieldVal("MMO_Journal") == null) ? "" : doForm.doRS.GetFieldVal("MMO_Journal").ToString();
                                //CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                //Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                sWork = par_s2;
                                doForm.doRS.oVar.SetVar("JournalWithHardReturns", sWork + Constants.vbCrLf + sJournal);
                                if (!string.IsNullOrEmpty(sWork))
                                {
                                    //CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                    //If not defined in WOP, default is 1
                                    string MTA_GLOBALVar = (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS") == null) ? "" : doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS").ToString();
                                    if (MTA_GLOBALVar != "0")
                                    {
                                        sWork = goTR.Replace(sWork, Constants.vbCrLf.ToString(), Strings.Chr(32).ToString() + Strings.Chr(32).ToString() + Strings.Chr(32).ToString());
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                    }
                                    else
                                    {
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                    }
                                    doForm.MoveToField("MMO_JOURNAL");
                                    //doForm.oVar.SetVar("sJournalVal", sWork)
                                    doForm.doRS.oVar.SetVar("QT_JournalType", doForm.doRS.GetFieldVal("MLS_JOURNALTYPE", 1));
                                    doForm.doRS.SetFieldVal("MLS_JOURNALTYPE", 0, 2);
                                }
                                break;
                        }
                        break;

                    case "GP_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                        //VS 02162018 TKT#2108 : Journal feature
                        switch (par_s1.ToUpper())
                        {
                            case "OK":
                                par_bRunNext = false;
                                sJournal = doForm.doRS.GetFieldVal("MMO_Journal").ToString();
                                //CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal GP.
                                //Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal GP.
                                sWork = par_s2;
                                doForm.oVar.SetVar("JournalWithHardReturns", sWork + Environment.NewLine + sJournal);
                                if (!string.IsNullOrEmpty(sWork))
                                {
                                    //CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                    //If not defined in WOP, default is 1
                                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS").ToString() != "0")
                                    {
                                        sWork = goTR.Replace(sWork, Environment.NewLine, Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString());
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Environment.NewLine + sJournal);
                                    }
                                    else
                                    {
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Environment.NewLine + sJournal);
                                        //doForm.oVar.SetVar("sJournalVal", sWork)
                                    }
                                    doForm.MoveToField("MMO_JOURNAL");
                                }

                                break;
                        }

                        break;

                }
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                  //  goErr.SetError(ex, 45105, sProc);
                    return false;
                }
            }
            // par_doCallingObject = doForm;

            if (par_s5.ToUpper().Contains("FORM"))
            {
                par_doCallingObject = doForm;
            }
            else if (par_s5.ToUpper().Contains("VIEW"))
            {
                par_doCallingObject = doDesktop;
            }
            else
            {
                par_doCallingObject = doForm;
            }

            return true;

        }
        public bool Opp_CreateActLog_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: doForm.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //   12/22/2004 RAH: turned over to MI
            //2004/12/22 10:29:34 MAR Edited. SetLinkVals cause an error on line 37 of SetLinkVal: incorrect type.
            par_bRunNext = false;
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //PURPOSE:
            //		PJ 5/30/02 Adds Act Log with new notes of Opportunity.
            //		Run from enforce only in non-CREATION (MODIF) mode.
            //		If it is run in CREATION mode, the new Activity will not be linked to the original Opp.
            //RETURNS:
            //		1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.

            //VS 11102016 TKT#1275 : Creating activity on Record On save to include saves from mobile App

            //Calling now from RecordOnSave instead of FormOnSave. Changing all doForm references to doRS references

            //Dim doForm As clForm = par_doCallingObject
            Form doForm = (Form)par_doCallingObject;
            string sNotes = "";
            string sWork = "";
            long lWork = 0;
            string sMessage = null;
            clList goACType = new clList();
            //'If answered Yes on MB to create a journal, then go directly to script that creates journal.
            //If UCase(par_s1) = "YES" Then
            //    GoTo CREATEACTLOG
            //End If

            string sJournalType = (doForm.doRS.oVar.GetVar("OP_JournalType") == null) ? "" : doForm.doRS.oVar.GetVar("OP_JournalType").ToString();
            if (string.IsNullOrEmpty(sJournalType))
            {
                //If saved from Website then JournalType is in the variable.
                //If saved from the Mobile App form then the variable is not available. So we use the type on the Mobile App form
                sJournalType = (doForm.doRS.GetFieldVal("MLS_JOURNALTYPE", 1) == null) ? "" : doForm.doRS.GetFieldVal("MLS_JOURNALTYPE", 1).ToString();
                //If Webform this is set to 0 in messagebox event. But if mobile we have to set it here
                doForm.doRS.SetFieldVal("MLS_JOURNALTYPE", 0, 2);
            }
            if (Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL").ToString()) <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))
                return true;
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Opportunity is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Environment.NewLine);
                doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");
                //Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                return true;
            }

            doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");

            //sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            //CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            //hard returns in the journal field
            sWork = doForm.oVar.GetVar("JournalWithHardReturns").ToString();
            //CS 2/4/10
            //if (string.IsNullOrEmpty(sWork))
            //    return true;
            //We didn't hit MessageBoxEvent from entering a journal note.

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);

            //'sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            //'CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            //'hard returns in the journal field
            //sWork = doForm.oVar.GetVar("JournalWithHardReturns")

            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, Convert.ToInt32(lWork)).ToString();
            sNotes = sNotes + "== Created from Opportunity '" + doForm.doRS.GetFieldVal("SYS_Name") + "'";
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            doNew.SetFieldVal("MLS_Status", 1, 2);
            //Completed
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            //CS doNew.SetFieldVal("TME_ENDTIME", doForm.doRS.GetFieldVal("TME_Time"))
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");

            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));


            //In the code below Paul added IsObjectAssigned tests because SetLinkVals weren't working in some cases.
            //==> Remove the IsObjectAssigned tests.
            DataTable oTable = new DataTable();
            doLink = doForm.doRS.GetLinkVal("LNK_ORIGINATEDBY_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (10).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            doNew.SetLinkVal("LNK_Related_CN", doLink);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (11).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If

            oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
            doLink = doForm.doRS.GetLinkVal("LNK_FOR_PD", ref doLink, true, 0, -1, "A_a", ref oTable);
            //PJ Changed link name from 'LNK_RELATED_PRODUCT' to 'LNK_FOR_PRODUCT'
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_Related_PD) is not assigned (12).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            doNew.SetLinkVal("LNK_Related_PD", doLink);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_Related_PD) is not assigned (13).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If

            oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
            doLink = doForm.doRS.GetLinkVal("LNK_Related_GR", ref doLink, true, 0, -1, "A_a", ref oTable);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_Related_GR) is not assigned.", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            doNew.SetLinkVal("LNK_Related_GR", doLink);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_Related_GROUP) is not assigned (2).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If

            oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
            doLink = doForm.doRS.GetLinkVal("LNK_FOR_CO", ref doLink, true, 0, -1, "A_a", ref oTable);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_FOR_CO) is not assigned (3).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            doNew.SetLinkVal("LNK_RELATED_CO", doLink);
            //if goErr.GetLastError()<>"E00000" then
            //	goErr.DisplayLastError()
            //End If

            if (sJournalType != "<Make selection>")
            {
                //returns 0 for default if not there
                if (goACType.LReadSeek("AC:TYPE", "VALUE", sJournalType) != "0")
                {
                    doNew.SetFieldVal("MLS_TYPE", sJournalType, 1);
                    //If sJournalType = "A&E" Or sJournalType = "Lunch and Learn" Or sJournalType = "Trade Show" Or sJournalType = "Customer" Or sJournalType = "Training" Or sJournalType = "Customer Quote" Then
                    //    doNew.SetFieldVal("MLS_PURPOSE", sJournalType, 1)
                    //End If
                    //VS 03302016 TKT#1024: Commented below code Setting JournalType as Purpose for few Journal types above and the rest are set to followup
                    //SGR TKT:# 1161 12072016 added Service Call
                    if (sJournalType == "Service Call" | sJournalType == "A&E" | sJournalType == "Customer/Sales Visit" | sJournalType == "Trade Show" | sJournalType == "Dealer Show" | sJournalType == "Training" | sJournalType == "Lunch and Learn")
                    {
                        doNew.SetFieldVal("MLS_PURPOSE", sJournalType, 1);
                    }
                }
                else
                {
                    doNew.SetFieldVal("MLS_TYPE", 31, 2);
                    //Journal
                }
            }
            else
            {
                doNew.SetFieldVal("MLS_TYPE", 31, 2);
                //Journal
            }
            par_doCallingObject = doNew;
            bool runnext = true;
            //Journal
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(doNew.GetFieldVal("MMO_HISTORY").ToString(), "Created."));
            doNew.SetFieldVal("LNK_RELATED_OP", doForm.GetRecordID());

            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;
                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Environment.NewLine);

                ////goLog.Log("MessageBoxEvent", doForm.oVar.GetVar("ScriptMessages").ToString(),1 ,false , true);
                par_doCallingObject = doForm;
                return false;
            }

            doNew = null;
            doLink = null;

            return true;
            par_doCallingObject = doForm;
            return true;

        }
        public bool Quote_CreateActLog_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: doForm.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //   12/22/2004 RAH: turned over to MI
            //2004/12/22 10:29:34 MAR Edited. SetLinkVals cause an error on line 37 of SetLinkVal: incorrect type.
            par_bRunNext = false;
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //PURPOSE:
            //		PJ 5/30/02 Adds Act Log with new notes of Opportunity.
            //		Run from enforce only in non-CREATION (MODIF) mode.
            //		If it is run in CREATION mode, the new Activity will not be linked to the original Opp.
            //RETURNS:
            //		1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.

            //VS 11102016 TKT#1275 : Creating activity on Record On save to include saves from mobile App

            //Calling now from RecordOnSave instead of FormOnSave. Changing all doForm references to doRS references

            //Dim doForm As clForm = par_doCallingObject
            Form doForm = (Form)par_doCallingObject;
            string sNotes = "";
            string sWork = "";
            long lWork = 0;
            string sMessage = null;
            clList goACType = new clList();
            //'If answered Yes on MB to create a journal, then go directly to script that creates journal.
            //If UCase(par_s1) = "YES" Then
            //    GoTo CREATEACTLOG
            //End If

            string sJournalType = (doForm.doRS.oVar.GetVar("QT_JournalType") == null) ? "" : doForm.doRS.oVar.GetVar("QT_JournalType").ToString();
            if (string.IsNullOrEmpty(sJournalType))
            {
                //If saved from Website then JournalType is in the variable.
                //If saved from the Mobile App form then the variable is not available. So we use the type on the Mobile App form
                sJournalType = (doForm.doRS.GetFieldVal("MLS_JOURNALTYPE", 1) == null) ? "" : doForm.doRS.GetFieldVal("MLS_JOURNALTYPE", 1).ToString();
                //If Webform this is set to 0 in messagebox event. But if mobile we have to set it here
                doForm.doRS.SetFieldVal("MLS_JOURNALTYPE", 0, 2);
            }
            if (Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL").ToString()) <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))
                return true;
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Opportunity is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Environment.NewLine);
                doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");
                //Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                return true;
            }

            doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");

            //sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            //CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            //hard returns in the journal field
            sWork = doForm.doRS.oVar.GetVar("JournalWithHardReturns").ToString();
            //CS 2/4/10
            //if (string.IsNullOrEmpty(sWork))
            //    return true;
            //We didn't hit MessageBoxEvent from entering a journal note.

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);

            //'sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            //'CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            //'hard returns in the journal field
            //sWork = doForm.oVar.GetVar("JournalWithHardReturns")

            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, Convert.ToInt32(lWork)).ToString();
            sNotes = sNotes + "== Created from Quote '" + doForm.doRS.GetFieldVal("SYS_Name") + "'";
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            doNew.SetFieldVal("MLS_Status", 1, 2);
            //Completed
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            //CS doNew.SetFieldVal("TME_ENDTIME", doForm.doRS.GetFieldVal("TME_Time"))
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");

            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));


            //In the code below Paul added IsObjectAssigned tests because SetLinkVals weren't working in some cases.
            //==> Remove the IsObjectAssigned tests.
            DataTable oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
            doLink = doForm.doRS.GetLinkVal("LNK_ORIGINATEDBY_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (10).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            doNew.SetLinkVal("LNK_Related_CN", doLink);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (11).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If

            //doLink = doForm.doRS.GetLinkVal("LNK_FOR_PD", ref doLink, true, 0, -1, "A_a", ref oTable);
            ////PJ Changed link name from 'LNK_RELATED_PRODUCT' to 'LNK_FOR_PRODUCT'
            ////If Not goP.IsObjectAssigned(doLink) Then
            ////    goP.TraceLine("doLink (LNK_Related_PD) is not assigned (12).", "", sProc)
            ////    '	goErr.DisplayLastError()
            ////End If
            //doNew.SetLinkVal("LNK_Related_PD", doLink);
            ////If Not goP.IsObjectAssigned(doLink) Then
            ////    goP.TraceLine("doLink (LNK_Related_PD) is not assigned (13).", "", sProc)
            ////    '	goErr.DisplayLastError()
            ////End If

            //doLink = doForm.doRS.GetLinkVal("LNK_Related_GR", ref doLink, true, 0, -1, "A_a", ref oTable);
            ////If Not goP.IsObjectAssigned(doLink) Then
            ////    goP.TraceLine("doLink (LNK_Related_GR) is not assigned.", "", sProc)
            ////    '	goErr.DisplayLastError()
            ////End If
            //doNew.SetLinkVal("LNK_Related_GR", doLink);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_Related_GROUP) is not assigned (2).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If

            oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
            doLink = doForm.doRS.GetLinkVal("LNK_TO_CO", ref doLink, true, 0, -1, "A_a", ref oTable);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_FOR_CO) is not assigned (3).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            doNew.SetLinkVal("LNK_RELATED_CO", doLink);
            //if goErr.GetLastError()<>"E00000" then
            //	goErr.DisplayLastError()
            //End If

            if (sJournalType != "<Make selection>")
            {
                //returns 0 for default if not there
                if (goACType.LReadSeek("AC:TYPE", "VALUE", sJournalType) != "0")
                {
                    doNew.SetFieldVal("MLS_TYPE", sJournalType, 1);
                    //If sJournalType = "A&E" Or sJournalType = "Lunch and Learn" Or sJournalType = "Trade Show" Or sJournalType = "Customer" Or sJournalType = "Training" Or sJournalType = "Customer Quote" Then
                    //    doNew.SetFieldVal("MLS_PURPOSE", sJournalType, 1)
                    //End If
                    //VS 03302016 TKT#1024: Commented below code Setting JournalType as Purpose for few Journal types above and the rest are set to followup
                    //SGR TKT:# 1161 12072016 added Service Call
                    if (sJournalType == "Service Call" | sJournalType == "A&E" | sJournalType == "Customer/Sales Visit" | sJournalType == "Trade Show" | sJournalType == "Dealer Show" | sJournalType == "Training" | sJournalType == "Lunch and Learn")
                    {
                        doNew.SetFieldVal("MLS_PURPOSE", sJournalType, 1);
                    }
                }
                else
                {
                    doNew.SetFieldVal("MLS_TYPE", 31, 2);
                    //Journal
                }
            }
            else
            {
                doNew.SetFieldVal("MLS_TYPE", 31, 2);
                //Journal
            }
            par_doCallingObject = doNew;
            bool runnext = true;
            //Journal
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(doNew.GetFieldVal("MMO_HISTORY").ToString(), "Created."));
            doNew.SetFieldVal("LNK_RELATED_QT", doForm.GetRecordID());

            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;
                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Environment.NewLine);

                ////goLog.Log("MessageBoxEvent", doForm.oVar.GetVar("ScriptMessages").ToString(),1 ,false , true);
                par_doCallingObject = doForm;
                return false;
            }

            doNew = null;
            doLink = null;

            return true;
            par_doCallingObject = doForm;
            return true;

        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //Refill Vendor for the Product

            doRS.ClearLinkAll("LNK_RELATED_VE");
            doRS.SetFieldVal("LNK_RELATED_VE", doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_VE"));

            //AD 11082017 TKT#1929 : Update Last Date for Company

            scriptManager.RunScript("UpdateLastDateinCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doRS;
            return true;
        }
        public bool OP_FormControlOnChange_LNK_FOR_PD_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Refill Vendor for the Product

            doForm.doRS.ClearLinkAll("LNK_RELATED_VE");
            doForm.doRS.SetFieldVal("LNK_RELATED_VE", doForm.doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_VE"));

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormControlOnChange_LNK_CONNECTED_QT_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            //tkt #1931 in selltis70.selltis.com
            clArray dolink = new clArray();
            oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
            dolink = doForm.doRS.GetLinkVal("LNK_CONNECTED_QT", ref dolink, true, 0, -1, "A_a", ref oTable);

            if (dolink.GetDimension() > 0)
            {
                doForm.doRS.SetFieldVal("MLS_STAGE", "Quote");
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.SetFieldVal("DTT_QUOTEDATE", doRS.GetFieldVal("LNK_IN_QT%%DTT_QUOTEDATE"));
            doRS.SetFieldVal("DTT_BOOKEDDATE", doRS.GetFieldVal("LNK_IN_QT%%DTT_BOOKEDDATE"));

            doRS.ClearLinkAll("LNK_RELATEDSTAGES_GR");

            if (doRS.GetFieldVal("DTT_QUOTEDATE", 1) != null && doRS.GetFieldVal("DTT_QUOTEDATE", 1).ToString() != "")
            {
                doRS.SetFieldVal("LNK_RELATEDSTAGES_GR", "38383834-3861-3165-4752-31302F31372F");
            }

            if (doRS.GetFieldVal("DTT_BOOKEDDATE", 1) != null && doRS.GetFieldVal("DTT_BOOKEDDATE", 1).ToString() != "")
            {
                doRS.SetFieldVal("LNK_RELATEDSTAGES_GR", "36623664-6338-6437-4752-31302F31372F");
            }

            //VS 02022018 : Calculate Match P21 Booking checkbox. If Quote Order Type, Quote Stage AND QuoteLine Detail Type, Included or Qty > 0
            //CONDITION=LNK_IN_QT%%MLS_ORDERTYPE=1 AND INT_DETAILTYPE='0' AND LNK_IN_QT%%MLS_STAGE<>1 AND (CHK_INCLUDE=1 OR SR__QTY<>'0') 
            clRowSet doQTRS = new clRowSet("QT", 3, "GID_ID=" + doRS.GetFieldVal("LNK_IN_QT").ToString(), "MLS_ORDERTYPE,MLS_STAGE");
            doRS.SetFieldVal("CHK_MatchP21Booking", 0, 2);
            if (doQTRS.GetFirst() == 1)
            {
                if (doQTRS.GetFieldVal("MLS_ORDERTYPE", 2).ToString() == "1" && doQTRS.GetFieldVal("MLS_STAGE", 2).ToString() != "1")
                {
                    if (doRS.GetFieldVal("INT_DETAILTYPE", 2).ToString() == "0")
                    {
                        if (doRS.GetFieldVal("CHK_INCLUDE", 2).ToString() == "1" || Convert.ToDecimal(doRS.GetFieldVal("SR__QTY", 2).ToString()) != 0)
                        {
                            doRS.SetFieldVal("CHK_MatchP21Booking", 1, 2);
                        }
                    }
                }
            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool QT_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //1831 NC set mls_stage = quote
            Form doForm = (Form)par_doCallingObject;
            clArray dolink = new clArray();
            int i = 0;
            bool bResult = false;
            oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
            dolink = doForm.doRS.GetLinkVal("LNK_RELATED_OP", ref dolink, true, 0, -1, "A_a", ref oTable);
            for (i = 1; i <= dolink.GetDimension(); i++)
            {
                clRowSet OPRS = new clRowSet("OP", 1, "GID_ID = '" + dolink.GetItem(i) + "'", "", "MLS_STAGE", -1, "", "", "", "", "", true, true);
                OPRS.GetFieldVal("MLS_STAGE");
                OPRS.SetFieldVal("MLS_STAGE", "Quote");

                OPRS.Commit();
            }

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CreateActivity"))
            {
                //check if we already ran this script. If so don't run it again.
                string Opp_CreateActLog_Ran_Var = (doForm.doRS.oVar.GetVar("Opp_CreateActLog_Ran") == null) ? "" : doForm.doRS.oVar.GetVar("Opp_CreateActLog_Ran").ToString();
                if (Opp_CreateActLog_Ran_Var != "1")
                {
                    //Only try to create an AC log if have the option set in workgroup options to do so
                    string MTA_GLOBAL_var = (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEOPP_CREATE_AC") == null) ? "" : doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEOPP_CREATE_AC").ToString();
                    if (MTA_GLOBAL_var == "1")
                    {
                        //bResult = goScr.RunScript("Opp_CreateActLog", doRS);
                        par_doCallingObject = doForm;
                        bool runnext = true;
                        bResult = scriptManager.RunScript("Opp_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref runnext, ref par_sSections);
                        doForm = (Form)par_doCallingObject;

                    }
                }
                //End If
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //AD 11082017 TKT#1929 : Update Last Date for Company
            //goScr.RunScript("UpdateLastDateinCO", doRS);

            scriptManager.RunScript("UpdateLastDateinCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);


            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.ClearLinkAll("LNK_RELATEDSTAGES_GR");

            if (doRS.GetFieldVal("DTT_QUOTEDATE", 1) != null && doRS.GetFieldVal("DTT_QUOTEDATE", 1).ToString() != "")
            {
                doRS.SetFieldVal("LNK_RELATEDSTAGES_GR", "38383834-3861-3165-4752-31302F31372F");
            }

            if (doRS.GetFieldVal("DTT_BOOKEDDATE", 1) != null && doRS.GetFieldVal("DTT_BOOKEDDATE", 1).ToString() != "")
            {
                doRS.SetFieldVal("LNK_RELATEDSTAGES_GR", "36623664-6338-6437-4752-31302F31372F");
            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Quotline_CalcTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }

        //AD TASK 1929 11082017
        public bool UpdateLastDateinCO(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //AD 07072017 TKT#1559 : Update Last Date for Company
            //Purpose Update LastDate for OP, QT, LEAD, AC, AC Sales and also in CO

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sFile = doRS.GetFileName();
            string sField = "";
            //field to update in company/contact
            string sFieldLabel = "";
            DateTime dDate = default(DateTime);
            string sCOLinkFieldName = "";

            //Need to update only for new records created
            //new Record
            if (doRS.GetInfo("TYPE") == "2")
            {

                if (sFile == "AC")
                {
                    sCOLinkFieldName = "LNK_RELATED_CO";
                    //If Activity is of type Sales Visit, update linked Related Company.Last AC Sales date
                    //sales visit
                    if (doRS.GetFieldVal("MLS_TYPE", 2) != null && Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2)) == 11)
                    {
                        sField = "DTT_LASTACSALES";
                    }
                    else
                    {
                        //Activity is not of type sales visit
                        sField = "DTT_LASTAC";
                    }

                    //LEAD Activity
                    if (doRS.GetFieldVal("MLS_PURPOSE", 2) != null && Convert.ToInt32(doRS.GetFieldVal("MLS_PURPOSE", 2)) == 8)
                    {
                        sField = "DTT_LASTLEAD";
                    }

                }
                else if (sFile == "OP")
                {
                    sCOLinkFieldName = "LNK_FOR_CO";
                    sField = "DTT_LASTOP";

                    //ElseIf sFile = "QT" Then
                    //    sCOLinkFieldName = "LNK_TO_CO"
                    //    sField = "DTT_LASTQT"

                }
                else if (sFile == "QT")
                {
                    sCOLinkFieldName = "LNK_ShipTO_CO";
                    sField = "DTT_LASTQT";
                }

                dDate = (doRS.GetFieldVal("DTT_CreationTime", 2) == null) ? DateTime.MinValue : Convert.ToDateTime(doRS.GetFieldVal("DTT_CreationTime", 2));

                if (!string.IsNullOrEmpty(sField))
                {
                    sFieldLabel = goData.GetFieldLabel("CO", sField);
                    //Update CO Record
                    if (doRS.GetLinkCount(sCOLinkFieldName) > 0)
                    {
                        clArray doCompanies = (clArray)doRS.GetFieldVal(sCOLinkFieldName, 2);
                        //Bipass reconsave and validation to speed up AC save
                        for (int i = 1; i <= doCompanies.GetDimension(); i++)
                        {
                            clRowSet doRSCompany = new clRowSet("CO", 1, "GID_ID='" + doCompanies.GetItem(i) + "'", "", sField, -1, "", "", "", "", "", true, true);
                            if (doRSCompany.GetFirst() == 1)
                            {
                                doRSCompany.SetFieldVal(sField, dDate, 2);
                                //log error but proceed
                                if (doRSCompany.Commit() == 0)
                                {
                                    goLog.Log(sProc, "CO update of last " + sFieldLabel + " field failed for CO " + ((doRSCompany.GetFieldVal("TXT_CompanyName") == null) ? "" : doRSCompany.GetFieldVal("TXT_CompanyName").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                }
                            }
                            doRSCompany = null;
                        }
                    }
                }

            }

            par_doCallingObject = doRS;
            return true;

        }
        //AD TASK 1929 11082017
        public bool AutoCOUpdate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.


            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //AD 07072017 TKT#1559 : Update Last Date for Company
            //AGE_D0D32818-BC80-4333-5858-A09800E45CDE
            //PURPOSE:
            //       Called by agent to update 3 custom date fields that
            //       record latest AC of type Sales Visit, latest OP and latest Quote
            //A_01_EXECUTE=AutoCOUpdate
            //A_01_OBJSHARED = 1
            //A_01_TYPE = RUNSCRIPT
            //A_ORDER=1,
            //ACTIONS=1,
            //ACTIVE = 1
            //E_TM_HOUR = 19 '7:00 PM
            //E_TM_INTERVAL = 3 'Every day
            //E_TM_MINUTE = 0 'top of hour
            //EVENT=TIMER
            //US_NAME=AutoCOUpdate
            //US_PURPOSE=Runs daily Update of Company records
            //SORTVALUE1=TIMER_ACTIVE

            long lBiID = 0;
            long lastBiId = 0;
            clRowSet doRS = default(clRowSet);
            clRowSet doNewRS = default(clRowSet);
            long iCount = 0;
            bool bUpdateCO = false;
            string par_sDelim = " ";
            string sNowDate = goTR.DateTimeToSysString(goTR.NowLocal(), ref par_iValid, ref par_sDelim);
            string sRecID = "";
            string sWOP = goMeta.PageRead("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED");
            int iFailedOther = 0;
            int iFailedPerm = 0;
            int iFailedTotal = 0;
            int iSuccess = 0;

            try
            {
                do
                {
                    doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0 OR LNK_SHIPTOFOR_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT, LNK_SHIPTOFOR_QT", 50, "", "", "", "", "", true, true, true);
                    iCount = iCount + doRS.Count();
                    if (doRS.GetFirst() == 1)
                    {
                        lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));

                        do
                        {
                            bUpdateCO = false;
                            //until set to true below
                            sRecID = (doRS.GetCurrentRecID() == null) ? "" : doRS.GetCurrentRecID().ToString();

                            //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                            if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                            {
                                doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            //TLD 5/18/2011 -----------Added to include date of latest OP
                            if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                            {
                                doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            //TLD 5/18/2011 -----------Added to include date of latest QT
                            if (doRS.GetLinkCount("LNK_Received_QT") > 0)
                            {
                                doNewRS = new clRowSet("QT", 3, "LNK_To_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }

                            //VS 12182017 -----------Added to include date of latest QT for ShipTo link
                            if (doRS.GetLinkCount("LNK_SHIPTOFOR_QT") > 0)
                            {
                                doNewRS = new clRowSet("QT", 3, "LNK_ShipTo_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }

                            //Update CO
                            if (bUpdateCO == true)
                            {
                                if (doRS.Commit() == 0)
                                {
                                    if (goErr.GetLastError("NUMBER") == "E47250")
                                    {
                                        //Commit failed b/c user has no permissions to edit record; log it, but proceed
                                        iFailedPerm = iFailedPerm + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " due to permissions.", 1, false, true);
                                        //testing
                                    }
                                    else
                                    {
                                        //Commit failed for some other reason.
                                        iFailedOther = iFailedOther + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                        //testing
                                    }
                                }
                            }
                            if (doRS.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                        lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                        //get last BI__ID processed
                    }
                    else
                    {
                        break; // TODO: might not be correct. Was : Exit Do
                    }
                } while (true);

                //Check once more for any newly added records
                doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0 OR LNK_SHIPTOFOR_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT, LNK_SHIPTOFOR_QT", -1, "", "", "", "", "", true, true, true);
                iCount = iCount + doRS.Count();
                if (doRS.GetFirst() == 1)
                {
                    do
                    {
                        bUpdateCO = false;
                        //until set to true below
                        sRecID = (doRS.GetCurrentRecID() == null) ? "" : doRS.GetCurrentRecID().ToString();

                        //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                        if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                        {
                            doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        //TLD 5/18/2011 -----------Added to include date of latest OP
                        if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                        {
                            doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        //TLD 5/18/2011 -----------Added to include date of latest QT
                        if (doRS.GetLinkCount("LNK_Received_QT") > 0)
                        {
                            doNewRS = new clRowSet("QT", 3, "LNK_To_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }

                        //VS 12182017 -----------Added to include date of latest QT for ShipTo link
                        if (doRS.GetLinkCount("LNK_SHIPTOFOR_QT") > 0)
                        {
                            doNewRS = new clRowSet("QT", 3, "LNK_ShipTo_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }

                        //Update CO
                        if (bUpdateCO == true)
                        {
                            if (doRS.Commit() == 0)
                            {
                                if (goErr.GetLastError("NUMBER") == "E47250")
                                {
                                    //Commit failed b/c user has no permissions to edit record; log it, but proceed
                                    iFailedPerm = iFailedPerm + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " due to permissions.", 1, false, true);
                                    //testing
                                }
                                else
                                {
                                    //Commit failed for some other reason.
                                    iFailedOther = iFailedOther + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                    //testing
                                }
                            }
                        }

                        if (doRS.GetNext() == 0)
                            break; // TODO: might not be correct. Was : Exit Do
                    } while (true);
                    lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                    //get last bi__id processed        
                }
                iFailedTotal = iFailedOther + iFailedPerm;
                iSuccess = Convert.ToInt32(iCount) - iFailedTotal;

                //Write to WOP
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Started " + sNowDate + " and Completed " + goTR.DateTimeToSysString(goTR.NowLocal(), ref par_iValid, ref par_sDelim) + " with " + iSuccess + " successful updates; " + iFailedPerm + " failed updates due to permissions; " + iFailedOther + " total failed updates.");
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);

                iCount = 0;
                iFailedOther = 0;
                iFailedPerm = 0;
                iFailedTotal = 0;
                iSuccess = 0;
                doRS = null;
                lBiID = 0;

            }
            catch (Exception ex)
            {
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Failed at Record " + sRecID + " " + goErr.GetLastError("NUMBER"));
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);
            }

            return true;

        }

        public bool GenerateSysName_Pre(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //CS 8/11/08: Added to Company sys name
            //MI 10/23/07 Added 'UTC' to AT Name.
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            //Array aTemp4 = default(Array);
            string[] aTemp4;
            string sRel = null;
            string sTemp5 = "";
            int i = 0;
            string sFileName = doRS.GetFileName();
            string sResult = "";
            clRowSet doLink = default(clRowSet);

            string sDelim = " _ ";
            clRowSet doMORS = default(clRowSet);
            clRowSet doPDRS = default(clRowSet);
            string sPrice = "";
            string sPrice_Tmp = "";
            string sSubtotal = "";
            string sSubtotal_Tmp = "";
            int iLen1 = 0;
            int iLenRem = 0;
            //TLD 8/15/2013 Added Try, converted to _Pre
            //We assume that sFileName is valid. If this is a problem, test it here and SetError.
            //try
            //{
                switch (Strings.UCase(sFileName))
                {
                    case "CO":
                        //==> COMPANY NEW:	TXT_CompanyName+" - "+TXT_CityMailing+", "+TXT_StateMailing
                        if (!doRS.IsLoaded("TXT_CompanyName"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".TXT_CompanyName");
                            ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                        }
                        if (!doRS.IsLoaded("TXT_CityMailing"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".TXT_CityMailing");
                            ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                        }
                        if (!doRS.IsLoaded("TXT_StateMailing"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".TXT_StateMailing");
                            ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                        }
                        if (!doRS.IsLoaded("TEL_PhoneNo"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".TEL_PhoneNo");
                            ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                        }
                        sResult = doRS.GetFieldVal("TXT_CompanyName").ToString();
                        sTemp = doRS.GetFieldVal("TXT_CityMailing").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_StateMailing").ToString();
                        sTemp4 = doRS.GetFieldVal("TXT_AddrMailing", 2, 14).ToString();
                        if (!string.IsNullOrEmpty(sTemp) | !string.IsNullOrEmpty(sTemp2) | !string.IsNullOrEmpty(sTemp4))
                        {
                            sResult += " - ";
                        }
                        if (!string.IsNullOrEmpty(sTemp4))
                        {
                            sResult += sTemp4;
                            if (!string.IsNullOrEmpty(sTemp))
                            {
                                sResult += ", ";
                            }
                        }
                        if (!string.IsNullOrEmpty(sTemp))
                        {
                            sResult += sTemp;
                            if (!string.IsNullOrEmpty(sTemp2))
                            {
                                sResult += ", ";
                            }
                        }
                        if (!string.IsNullOrEmpty(sTemp2))
                        {
                            sResult += sTemp2;
                        }
                        sTemp3 = doRS.GetFieldVal("TEL_PhoneNo").ToString();
                        if (!string.IsNullOrEmpty(sTemp3))
                        {
                            sResult += " " + sTemp3;
                        }
                        sResult = Microsoft.VisualBasic.Strings.Left(sResult, 80);
                        par_bRunNext = false;
                        break;

                    case "QL":
                        //VS 12202017 : As per new requirement from Kevin, Jordan
                        //Item ID | Item Description  | Extended Description       | Quantity | Price  | Extended Price | Product Family
                        //1463252 | DSBC-32- -PPSA-N3 | 1463252 DSBC-32-35-PPSA-N3 | 2        | 132.15 | 264.30         | Festo Actuators
                        //Adding Line No to Order Quote Lines in link box
                        //QL SysName max length set to 115 with 14 chars for Delimiter " _ "

                        sDelim = " _ ";
                        if (!doRS.IsLoaded("LNK_FOR_MO"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".LNK_FOR_MO");
                        }
                        if (!doRS.IsLoaded("LNK_FOR_PD"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".LNK_FOR_PD");
                        }

                        doMORS = new clRowSet("MO", clC.SELL_READONLY, "GID_ID=" + doRS.GetFieldVal("LNK_FOR_MO"), "", "TXT_ModelName, TXT_Description, TXT_ExtendedDesc");
                        doPDRS = new clRowSet("PD", clC.SELL_READONLY, "GID_ID=" + doRS.GetFieldVal("LNK_FOR_PD"), "", "TXT_CatalogName");

                        //Cant really be truncate number fields SR__LINENO, SR__QTY, CUR_PRICEUNIT, CUR_SUBTOTAL
                        //Should have a max length of 44
                        //Round the decimal to 2 places. If we the data in system format we will miss currency symbol and commas in the numbers
                        sPrice = doRS.GetFieldVal("CUR_PRICEUNIT", 1).ToString();
                        sPrice_Tmp = sPrice;
                        if (sPrice.Contains("."))
                        {
                            sPrice = Strings.Split(sPrice_Tmp, ".")[0];
                            sPrice = sPrice + "." + Strings.Left(Strings.Split(sPrice_Tmp, ".")[1], 2);
                        }
                        sSubtotal = doRS.GetFieldVal("CUR_SUBTOTAL", 1).ToString();
                        sSubtotal_Tmp = sSubtotal;
                        if (sSubtotal.Contains("."))
                        {
                            sSubtotal = Strings.Split(sSubtotal_Tmp, ".")[0];
                            sSubtotal = sSubtotal + "." + Strings.Left(Strings.Split(sSubtotal_Tmp, ".")[1], 2);
                        }
                        sResult = doRS.GetFieldVal("SR__QTY", 1).ToString() + sDelim;
                        sTemp = sPrice + sDelim + sSubtotal;

                        //Product Name capped at 10 chars - Max in Database as of 12212017 is 8
                        if (doPDRS.GetFirst() == 1)
                        {
                            sTemp2 = doPDRS.GetFieldVal("TXT_CatalogName", 1).ToString();
                        }
                        else
                        {
                            sTemp2 = "";
                        }
                        if (sTemp2.Length > 19)
                        {
                            sTemp2 = Strings.Left(sTemp2, 16) + "...";
                        }

                        if (doMORS.GetFirst() == 1)
                        {
                            sTemp3 = doMORS.GetFieldVal("TXT_ModelName", 1).ToString();
                            sTemp4 = doMORS.GetFieldVal("TXT_Description", 1).ToString();
                            sTemp5 = doMORS.GetFieldVal("TXT_ExtendedDesc", 1).ToString();
                        }
                        else
                        {
                            sTemp3 = "";
                            sTemp4 = "";
                            sTemp5 = "";
                        }

                        if (sTemp3.Length > 22)
                        {
                            sTemp3 = Strings.Left(sTemp3, 20) + "...";
                        }
                        //get this length and we will adjust model fields Length 
                        iLen1 = sResult.Length + sTemp.Length + sTemp2.Length + sTemp3.Length;
                        iLenRem = Convert.ToInt16(goData.GetFieldSize("QL", "SYS_NAME")) - iLen1 - 15/*delim len*/;
                        //Remaining length should be more than around 20 chars
                        //Out of the remaining chars, let's give altleast 10 to Description
                        if (sTemp4.Length >= iLenRem / 2 && sTemp5.Length >= iLenRem / 2)
                        {
                            sTemp4 = Strings.Left(sTemp4, iLenRem / 2 - 2) + "..";
                        }
                        else if (sTemp4.Length >= iLenRem / 2 && sTemp5.Length < iLenRem / 2)
                        {
                            sTemp4 = Strings.Left(sTemp4, iLenRem - sTemp5.Length - 2) + "..";
                        }

                        if (sTemp4.Length >= iLenRem / 2 && sTemp5.Length >= iLenRem / 2)
                        {
                            sTemp4 = Strings.Left(sTemp5, iLenRem / 2 - 2) + "..";
                        }
                        else if (sTemp5.Length >= iLenRem / 2 && sTemp4.Length < iLenRem / 2)
                        {
                            sTemp5 = Strings.Left(sTemp5, iLenRem - sTemp4.Length - 2) + "..";
                        }


                        sResult = sResult + sTemp3 + sDelim + sTemp4 + sDelim + sTemp5 + sDelim + sTemp + sDelim + sTemp2;
                        par_bRunNext = false;
                        break;

                    case "IV":
                        //VS 12202017 : As per new requirement from Kevin, Jordan
                        //Item ID | Item Description  | Extended Description       | Quantity | Price  | Extended Price | Product Family
                        //1463252 | DSBC-32- -PPSA-N3 | 1463252 DSBC-32-35-PPSA-N3 | 2        | 132.15 | 264.30         | Festo Actuators
                        //Adding Line No to Order Quote Lines in link box
                        //IV SysName max length set to 115 with 14 chars for Delimiter " _ "

                        sDelim = " _ ";
                        if (!doRS.IsLoaded("LNK_FOR_MO"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".LNK_FOR_MO");
                        }
                        //if (!doRS.IsLoaded("LNK_FOR_PD"))
                        //{
                        //    goErr.SetError(35103, sProc, "", sFileName + ".LNK_FOR_PD");
                        //}

                        doMORS = new clRowSet("MO", clC.SELL_READONLY, "GID_ID=" + doRS.GetFieldVal("LNK_FOR_MO"), "", "TXT_ModelName, TXT_Description, TXT_ExtendedDesc");
                        doPDRS = new clRowSet("PD", clC.SELL_READONLY, "GID_ID=" + doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD"), "", "TXT_CatalogName");

                        //Cant really be truncate number fields SR__LINENO, SR__QTY, CUR_PRICEUNIT, CUR_SUBTOTAL
                        //Should have a max length of 44
                        //Round the decimal to 2 places. If we the data in system format we will miss currency symbol and commas in the numbers
                        sPrice = doRS.GetFieldVal("CUR_UNITPRICE", 1).ToString();
                        sPrice_Tmp = sPrice;
                        if (sPrice.Contains("."))
                        {
                            sPrice = Strings.Split(sPrice_Tmp, ".")[0];
                            sPrice = sPrice + "." + Strings.Left(Strings.Split(sPrice_Tmp, ".")[1], 2);
                        }
                        sSubtotal = doRS.GetFieldVal("CUR_EXTENDEDPRICE", 1).ToString();
                        sSubtotal_Tmp = sSubtotal;
                        if (sSubtotal.Contains("."))
                        {
                            sSubtotal = Strings.Split(sSubtotal_Tmp, ".")[0];
                            sSubtotal = sSubtotal + "." + Strings.Left(Strings.Split(sSubtotal_Tmp, ".")[1], 2);
                        }
                        sResult = doRS.GetFieldVal("SR__QTYSHIPPED", 1).ToString() + sDelim;
                        sTemp = sPrice + sDelim + sSubtotal;

                        //Product Name capped at 10 chars - Max in Database as of 12212017 is 8
                        if (doPDRS.GetFirst() == 1)
                        {
                            sTemp2 = doPDRS.GetFieldVal("TXT_CatalogName", 1).ToString();
                        }
                        else
                        {
                            sTemp2 = "";
                        }
                        if (sTemp2.Length > 19)
                        {
                            sTemp2 = Strings.Left(sTemp2, 16) + "...";
                        }

                        if (doMORS.GetFirst() == 1)
                        {
                            sTemp3 = doMORS.GetFieldVal("TXT_ModelName", 1).ToString();
                            sTemp4 = doMORS.GetFieldVal("TXT_Description", 1).ToString();
                            sTemp5 = doMORS.GetFieldVal("TXT_ExtendedDesc", 1).ToString();
                        }
                        else
                        {
                            sTemp3 = "";
                            sTemp4 = "";
                            sTemp5 = "";
                        }

                        if (sTemp3.Length > 22)
                        {
                            sTemp3 = Strings.Left(sTemp3, 20) + "...";
                        }
                        //get this length and we will adjust model fields Length 
                        iLen1 = sResult.Length + sTemp.Length + sTemp2.Length + sTemp3.Length;
                        iLenRem = Convert.ToInt16(goData.GetFieldSize("IV", "SYS_NAME")) - iLen1 - 15/*delim len*/;
                        //Remaining length should be more than around 20 chars
                        //Out of the remaining chars, let's give altleast 10 to Description
                        if (sTemp4.Length >= iLenRem / 2 && sTemp5.Length >= iLenRem / 2)
                        {
                            sTemp4 = Strings.Left(sTemp4, iLenRem / 2 - 2) + "..";
                        }
                        else if (sTemp4.Length >= iLenRem / 2 && sTemp5.Length < iLenRem / 2)
                        {
                            sTemp4 = Strings.Left(sTemp4, iLenRem - sTemp5.Length - 2) + "..";
                        }

                        if (sTemp4.Length >= iLenRem / 2 && sTemp5.Length >= iLenRem / 2)
                        {
                            sTemp4 = Strings.Left(sTemp5, iLenRem / 2 - 2) + "..";
                        }
                        else if (sTemp5.Length >= iLenRem / 2 && sTemp4.Length < iLenRem / 2)
                        {
                            sTemp5 = Strings.Left(sTemp5, iLenRem - sTemp4.Length - 2) + "..";
                        }


                        sResult = sResult + sTemp3 + sDelim + sTemp4 + sDelim + sTemp5 + sDelim + sTemp + sDelim + sTemp2;
                        par_bRunNext = false;
                        break;

                }

                if (!par_bRunNext)
                {
                    sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
                    sResult = goTR.Replace(sResult, Constants.vbTab, " ");
                    par_oReturn = sResult;
                }

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}

            return true;

        }

        #region Market Share (Demand) Calculation
        //VS TASK 1681 08/07/2017 Make Fields Read-Only for Market Share
        public bool GP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //'VS 08022016 : 
            //doForm.oVar.SetVar("lLenJournal", Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")))
            //doForm.SetControlState("MMO_JOURNAL", 1)
            //doForm.SetControlState("DTE_CALLCAMPAIGNSTARTDATE", 4)
            //doForm.SetControlState("DTE_CALLCAMPAIGNCOMPLETEDATE", 4)

            //'Implement Profile Start and Complete
            //If doForm.doRS.GetFieldVal("CHK_CALLCAMPAIGNSTARTED", 2) = 0 Then
            //    doForm.doRS.oVar.SetVar("CALLCAMPAIGNNotStarted_OnLoad", "1")
            //End If
            //If doForm.doRS.GetFieldVal("CHK_CALLCAMPAIGNCOMPLETED", 2) = 0 Then
            //    doForm.doRS.oVar.SetVar("CALLCAMPAIGNNotCompleted_OnLoad", "1")
            //End If

            //AD TASK 1681 08/03/2017
            doForm.SetControlState("SR__OURPRODUCTINSTALLEDBASEPERCENT1", 4);
            doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT1", 4);
            doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT2", 4);
            doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT3", 4);
            //doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT4", 4)
            //doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT5", 4)
            //doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT6", 4)

            //VS 02162018 TKT#2108 : Journal feature. Set journal/history to not allow direct editing/locked
            doForm.SetControlState("MMO_JOURNAL", 1);
            doForm.oVar.SetVar("lLenJournal", Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")));

            //VS 06122018 TKT#2287 : New fields per Kevin
            doForm.SetControlState("SR__SPECIFIEDPURCHPERCENT", 4);
            doForm.SetControlState("SR__SPECIFIEDCOMPPERCENT1", 4);
            doForm.SetControlState("SR__SPECIFIEDCOMPPERCENT2", 4);
            doForm.SetControlState("SR__SPECIFIEDCOMPPERCENT3", 4);


            par_doCallingObject = doForm;
            return true;
        }

        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__TOTALINSTALLATIONS(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__TOTALINSTALLATIONS");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO1(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO1");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO2(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO2");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO3(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO3");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO4(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO4");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO5(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO5");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO6(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO6");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Fields Read-Only for Market Share
        public bool GP_FormControlOnChange_SR__OURPRODUCTINSTALLEDBASE1(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08072017 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__OURPRODUCTINSTALLEDBASE1");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public void GP_SetInstallPercentages(ref object par_doCallingObject, string par_sPercentField)
        {
            //VS 08012016 Added for Profile Update Journal Entry
            Form doForm = (Form)par_doCallingObject;

            double dOverallInstalls = 0;
            double dInstalls = 0;
            double dInstallPercent = 0;
            dOverallInstalls = (doForm.doRS.GetFieldVal("SR__TOTALINSTALLATIONS", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__TOTALINSTALLATIONS", 2));

            //Our Installation

            if (par_sPercentField == "SR__OURPRODUCTINSTALLEDBASE1" | par_sPercentField == "SR__TOTALINSTALLATIONS")
            {
                dInstalls = (doForm.doRS.GetFieldVal("SR__OURPRODUCTINSTALLEDBASE1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__OURPRODUCTINSTALLEDBASE1", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__OURPRODUCTINSTALLEDBASEPERCENT1", Math.Round(dInstallPercent, 2), 2);

            }
            //Competition 1

            if (par_sPercentField == "SR__COMPETITIONINSTALLEDBASENO1" | par_sPercentField == "SR__TOTALINSTALLATIONS")
            {
                dInstalls = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO1", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT1", Math.Round(dInstallPercent, 2), 2);

            }
            //Competition 2

            if (par_sPercentField == "SR__COMPETITIONINSTALLEDBASENO2" | par_sPercentField == "SR__TOTALINSTALLATIONS")
            {
                dInstalls = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO2", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO2", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT2", Math.Round(dInstallPercent, 2), 2);

            }
            //Competition 3

            if (par_sPercentField == "SR__COMPETITIONINSTALLEDBASENO3" | par_sPercentField == "SR__TOTALINSTALLATIONS")
            {
                dInstalls = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO3", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO3", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT3", Math.Round(dInstallPercent, 2), 2);

            }
            //'Competition 4
            //If par_sPercentField = "SR__COMPETITIONINSTALLEDBASENO4" Or par_sPercentField = "SR__TOTALINSTALLATIONS" Then

            //    dInstalls = doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO4", 2)

            //    If dOverallInstalls = 0 Or dInstalls = 0 Then
            //        dInstallPercent = 0
            //    Else
            //        dInstallPercent = (dInstalls / dOverallInstalls) * 100
            //    End If

            //    doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT4", Math.Round(dInstallPercent, 2), 2)

            //End If
            //'Competition 5
            //If par_sPercentField = "SR__COMPETITIONINSTALLEDBASENO5" Or par_sPercentField = "SR__TOTALINSTALLATIONS" Then

            //    dInstalls = doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO5", 2)

            //    If dOverallInstalls = 0 Or dInstalls = 0 Then
            //        dInstallPercent = 0
            //    Else
            //        dInstallPercent = (dInstalls / dOverallInstalls) * 100
            //    End If

            //    doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT5", Math.Round(dInstallPercent, 2), 2)

            //End If
            //'Competition 6
            //If par_sPercentField = "SR__COMPETITIONINSTALLEDBASENO6" Or par_sPercentField = "SR__TOTALINSTALLATIONS" Then

            //    dInstalls = doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO6", 2)

            //    If dOverallInstalls = 0 Or dInstalls = 0 Then
            //        dInstallPercent = 0
            //    Else
            //        dInstallPercent = (dInstalls / dOverallInstalls) * 100
            //    End If

            //    doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT6", Math.Round(dInstallPercent, 2), 2)

            //End If

            par_doCallingObject = doForm;


        }


        //VS 05152018 TKT#2240 : Customer wants to use Annual Purchase $ instead of Installation counts.
        //VS 06122018 TKT#2287 : New changes and additions on Market Share form.
        public bool GP_FormControlOnChange_CUR_ANNUALPURCHASES_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //GP_SetPurchasePercentages(ref par_doCallingObject, "CUR_ANNUALPURCHASES");
            //VS 06122018 TKT#2287 : New Calculations everything.
            if (GP_SetPurchasePercentages_New(ref par_doCallingObject) == false)
                return false;

            par_doCallingObject = doForm;

            return true;
        }
        public bool GP_FormControlOnChange_CUR_AVAILABLEPURCHASES_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //GP_SetPurchasePercentages(ref par_doCallingObject, "CUR_ANNUALPURCHASES");
            //VS 06122018 TKT#2287 : New Calculations everything.
            if (GP_SetPurchasePercentages_New(ref par_doCallingObject) == false)
                return false;

            par_doCallingObject = doForm;

            return true;
        }
        public bool GP_FormControlOnChange_CUR_OURANNUALPURCHASES_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //GP_SetPurchasePercentages(ref par_doCallingObject, "CUR_OURANNUALPURCHASES");
            //VS 06122018 TKT#2287 : New Calculations everything.
            if (GP_SetPurchasePercentages_New(ref par_doCallingObject) == false)
                return false;

            par_doCallingObject = doForm;

            return true;
        }
        public bool GP_FormControlOnChange_CUR_COMPETITIONANNUALPURCHASES1_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //GP_SetPurchasePercentages(ref par_doCallingObject, "CUR_COMPETITIONANNUALPURCHASES1");
            //VS 06122018 TKT#2287 : New Calculations everything.
            if (GP_SetPurchasePercentages_New(ref par_doCallingObject) == false)
                return false;

            par_doCallingObject = doForm;

            return true;
        }
        public bool GP_FormControlOnChange_CUR_COMPETITIONANNUALPURCHASES2_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //GP_SetPurchasePercentages(ref par_doCallingObject, "CUR_COMPETITIONANNUALPURCHASES2");
            //VS 06122018 TKT#2287 : New Calculations everything.
            if (GP_SetPurchasePercentages_New(ref par_doCallingObject) == false)
                return false;

            par_doCallingObject = doForm;

            return true;
        }
        public bool GP_FormControlOnChange_CUR_COMPETITIONANNUALPURCHASES3_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //GP_SetPurchasePercentages(ref par_doCallingObject, "CUR_COMPETITIONANNUALPURCHASES3");
            //VS 06122018 TKT#2287 : New Calculations everything.
            if (GP_SetPurchasePercentages_New(ref par_doCallingObject) == false)
                return false;

            par_doCallingObject = doForm;

            return true;
        }
        public bool GP_FormControlOnChange_CUR_SPECIFIEDPURCHASES_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 06122018 TKT#2287 : New Calculations everything.
            if (GP_SetPurchasePercentages_New(ref par_doCallingObject) == false)
                return false;

            par_doCallingObject = doForm;

            return true;
        }
        public bool GP_FormControlOnChange_CUR_SPECIFIEDCOMPPURCH1_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 06122018 TKT#2287 : New Calculations everything.
            if (GP_SetPurchasePercentages_New(ref par_doCallingObject) == false)
                return false;

            par_doCallingObject = doForm;

            return true;
        }
        public bool GP_FormControlOnChange_CUR_SPECIFIEDCOMPPURCH2_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 06122018 TKT#2287 : New Calculations everything.
            if (GP_SetPurchasePercentages_New(ref par_doCallingObject) == false)
                return false;

            par_doCallingObject = doForm;

            return true;
        }
        public bool GP_FormControlOnChange_CUR_SPECIFIEDCOMPPURCH3_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 06122018 TKT#2287 : New Calculations everything.
            if (GP_SetPurchasePercentages_New(ref par_doCallingObject) == false)
                return false;

            par_doCallingObject = doForm;

            return true;
        }
        public bool GP_FormControlOnChange_LNK_SPECCOMPCONTACT1_CN_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 06122018 TKT#2287 : Fill Company from Contact
            doForm.doRS.ClearLinkAll("LNK_SPECCOMPCOMPANY1_CO");
            doForm.doRS.SetFieldVal("LNK_SPECCOMPCOMPANY1_CO", doForm.doRS.GetFieldVal("LNK_SPECCOMPCONTACT1_CN%%LNK_RELATED_CO"));

            par_doCallingObject = doForm;

            return true;
        }
        public bool GP_FormControlOnChange_LNK_SPECCOMPCONTACT2_CN_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 06122018 TKT#2287 : Fill Company from Contact
            doForm.doRS.ClearLinkAll("LNK_SPECCOMPCOMPANY2_CO");
            doForm.doRS.SetFieldVal("LNK_SPECCOMPCOMPANY2_CO", doForm.doRS.GetFieldVal("LNK_SPECCOMPCONTACT2_CN%%LNK_RELATED_CO"));

            par_doCallingObject = doForm;

            return true;
        }
        public bool GP_FormControlOnChange_LNK_SPECCOMPCONTACT3_CN_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 06122018 TKT#2287 : Fill Company from Contact
            doForm.doRS.ClearLinkAll("LNK_SPECCOMPCOMPANY3_CO");
            doForm.doRS.SetFieldVal("LNK_SPECCOMPCOMPANY3_CO", doForm.doRS.GetFieldVal("LNK_SPECCOMPCONTACT3_CN%%LNK_RELATED_CO"));

            par_doCallingObject = doForm;

            return true;
        }
      
        //VS 06122018 TKT#2287 : New Calculations everything.
        public bool GP_SetPurchasePercentages_New(ref object par_doCallingObject)
        {
            Form doForm = (Form)par_doCallingObject;

            double dAnnualPurchases = 0;
            double dAvailablePurchases = 0;
            double dSpecifiedPurchases = 0;
            double dSpecCompPurchases1 = 0;
            double dSpecCompPurchases2 = 0;
            double dSpecCompPurchases3 = 0;
            double dPurchases = 0;

            dAnnualPurchases = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_ANNUALPURCHASES", 2));
            dSpecifiedPurchases = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_SPECIFIEDPURCHASES", 2));
            dSpecCompPurchases1 = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_SPECIFIEDCOMPPURCH1", 2));
            dSpecCompPurchases2 = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_SPECIFIEDCOMPPURCH2", 2));
            dSpecCompPurchases3 = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_SPECIFIEDCOMPPURCH3", 2));

            if (dAnnualPurchases < dSpecifiedPurchases)
            {
                doForm.MessageBox("Specified purchases cannot be more than Annual purchases.");
                doForm.MoveToField("CUR_SPECIFIEDPURCHASES");
                return false;
            }

            //Available Purchases
            dAvailablePurchases = dAnnualPurchases - dSpecifiedPurchases;
            doForm.doRS.SetFieldVal("CUR_AVAILABLEPURCHASES", dAvailablePurchases, 2);

            //Our Purchases
            dPurchases = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_OURANNUALPURCHASES", 2));
            GP_CalcPercentage(ref doForm, "SR__OURPRODUCTINSTALLEDBASEPERCENT1", dAvailablePurchases, dPurchases);

            //Competition 1
            dPurchases = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_COMPETITIONANNUALPURCHASES1", 2));
            GP_CalcPercentage(ref doForm, "SR__COMPETITIONINSTALLEDBASEPERCENT1", dAvailablePurchases, dPurchases);

            //Competition 2
            dPurchases = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_COMPETITIONANNUALPURCHASES2", 2));
            GP_CalcPercentage(ref doForm, "SR__COMPETITIONINSTALLEDBASEPERCENT2", dAvailablePurchases, dPurchases);
            
            //Competition 3
            dPurchases = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_COMPETITIONANNUALPURCHASES3", 2));
            GP_CalcPercentage(ref doForm, "SR__COMPETITIONINSTALLEDBASEPERCENT3", dAvailablePurchases, dPurchases);
            
            //Specified Purchases %
            GP_CalcPercentage(ref doForm, "SR__SPECIFIEDPURCHPERCENT", dAnnualPurchases, dSpecifiedPurchases);

            //Specified Competition 1
            GP_CalcPercentage(ref doForm, "SR__SPECIFIEDCOMPPERCENT1", dSpecifiedPurchases, dSpecCompPurchases1);

            //Specified Competition 2
            GP_CalcPercentage(ref doForm, "SR__SPECIFIEDCOMPPERCENT2", dSpecifiedPurchases, dSpecCompPurchases2);

            //Specified Competition 3
            GP_CalcPercentage(ref doForm, "SR__SPECIFIEDCOMPPERCENT3", dSpecifiedPurchases, dSpecCompPurchases3);

            par_doCallingObject = doForm;

            return true;
        }
        public void GP_CalcPercentage(ref Form par_doForm, string par_sPercentField, double par_dTotalPurchases = 0, double par_dPurchases = 0)
        {
            double dPercent = 0;

            if (par_dTotalPurchases == 0 | par_dPurchases == 0)
                dPercent = 0;
            else
                dPercent = (par_dPurchases / par_dTotalPurchases) * 100;

            par_doForm.doRS.SetFieldVal(par_sPercentField, Math.Round(dPercent, 2), 2);
        }
        public void GP_SetPurchasePercentages(ref object par_doCallingObject, string par_sPercentField)
        {
            Form doForm = (Form)par_doCallingObject;

            double dOverallInstalls = 0;
            double dInstalls = 0;
            double dInstallPercent = 0;
            dOverallInstalls = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_ANNUALPURCHASES", 2));

            //Our Installation

            if (par_sPercentField == "CUR_OURANNUALPURCHASES" | par_sPercentField == "CUR_ANNUALPURCHASES")
            {
                dInstalls = (doForm.doRS.GetFieldVal("CUR_OURANNUALPURCHASES", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_OURANNUALPURCHASES", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__OURPRODUCTINSTALLEDBASEPERCENT1", Math.Round(dInstallPercent, 2), 2);

            }
            //Competition 1

            if (par_sPercentField == "CUR_COMPETITIONANNUALPURCHASES1" | par_sPercentField == "CUR_ANNUALPURCHASES")
            {
                dInstalls = (doForm.doRS.GetFieldVal("CUR_COMPETITIONANNUALPURCHASES1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_COMPETITIONANNUALPURCHASES1", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT1", Math.Round(dInstallPercent, 2), 2);

            }
            //Competition 2

            if (par_sPercentField == "CUR_COMPETITIONANNUALPURCHASES2" | par_sPercentField == "CUR_ANNUALPURCHASES")
            {
                dInstalls = (doForm.doRS.GetFieldVal("CUR_COMPETITIONANNUALPURCHASES2", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_COMPETITIONANNUALPURCHASES2", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT2", Math.Round(dInstallPercent, 2), 2);

            }
            //Competition 3

            if (par_sPercentField == "CUR_COMPETITIONANNUALPURCHASES3" | par_sPercentField == "CUR_ANNUALPURCHASES")
            {
                dInstalls = (doForm.doRS.GetFieldVal("CUR_COMPETITIONANNUALPURCHASES3", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_COMPETITIONANNUALPURCHASES3", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT3", Math.Round(dInstallPercent, 2), 2);

            }

            par_doCallingObject = doForm;


        }

        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 06122018 TKT#2287 : New changes
            //Validations
            double dAnnualPurchases = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_ANNUALPURCHASES", 2));
            double dSpecifiedPurchases = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_SPECIFIEDPURCHASES", 2));
            double dSpecCompPurchases1 = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_SPECIFIEDCOMPPURCH1", 2));
            double dSpecCompPurchases2 = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_SPECIFIEDCOMPPURCH2", 2));
            double dSpecCompPurchases3 = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_SPECIFIEDCOMPPURCH3", 2));

            if (Math.Round((dSpecCompPurchases1 + dSpecCompPurchases2 + dSpecCompPurchases3)) != Math.Round(dSpecifiedPurchases))
            {
                doForm.MessageBox("Specified purchases amount should match specified competition purchases total.");
                doForm.MoveToField("CUR_SPECIFIEDPURCHASES");
                return false;
            }

            //VS 06122018 TKT#2287 : New changes
            //VS 08052016 : Calc %s
            //GP_SetInstallPercentages(ref par_doCallingObject, "SR__TOTALINSTALLATIONS");
            if (GP_SetPurchasePercentages_New(ref par_doCallingObject) == false)
                return false;

            doForm = (Form)par_doCallingObject;

            if (doForm.oVar.GetVar("GP_InstallPercent_Save").ToString() != "1")
            {
                double dTotalPercent = 0;
                double SR__OURPRODUCTINSTALLEDBASEPERCENT1_var = (doForm.doRS.GetFieldVal("SR__OURPRODUCTINSTALLEDBASEPERCENT1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__OURPRODUCTINSTALLEDBASEPERCENT1", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT1_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT1", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT2_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT2", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT2", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT3_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT3", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT3", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT4_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT4", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT4", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT5_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT5", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT5", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT6_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT6", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT6", 2));
                dTotalPercent = SR__OURPRODUCTINSTALLEDBASEPERCENT1_var + SR__COMPETITIONINSTALLEDBASEPERCENT1_var + SR__COMPETITIONINSTALLEDBASEPERCENT2_var + SR__COMPETITIONINSTALLEDBASEPERCENT3_var + SR__COMPETITIONINSTALLEDBASEPERCENT4_var + SR__COMPETITIONINSTALLEDBASEPERCENT5_var + SR__COMPETITIONINSTALLEDBASEPERCENT6_var;

                if (dTotalPercent > 100)
                {
                    doForm.oVar.SetVar("GP_FormSaveMode", doForm.MessageBoxFormMode);
                    doForm.MessageBox("The Overall Installation % is more than 100. Do you want to continue saving the record?", clC.SELL_MB_YESNO, "Selltis", "Yes", "No", "", "", "MessageBoxEvent", "", "", null, null, "Yes", "No", "", "", "GP_InstallPercent_Save");
                    par_doCallingObject = doForm;
                    return false;
                }
            }

            //VS 08022016 : Create Activity Log of Journal Entry
            if (doForm.oVar.GetVar("GP_CreateActLog_Ran").ToString() != "1")
            {
                //goScr.RunScript("GP_CreateActLog", doForm);
                par_doCallingObject = doForm;
                scriptManager.RunScript("GP_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                doForm = (Form)par_doCallingObject;
            }

            par_doCallingObject = doForm;

            return true;
        }


        #endregion

        public bool GP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //VS 08022016 : Implement Profile Start and Complete
            //If doRS.oVar.GetVar("CALLCAMPAIGNNotStarted_OnLoad") = "1" And doRS.GetFieldVal("CHK_CALLCAMPAIGNSTARTED", 2) = 1 Then
            //    doRS.SetFieldVal("DTE_CALLCAMPAIGNSTARTDATE", Now, 2)
            //End If
            //If doRS.oVar.GetVar("CALLCAMPAIGNNotCompleted_OnLoad") = "1" And doRS.GetFieldVal("CHK_CALLCAMPAIGNCOMPLETED", 2) = 1 Then
            //    doRS.SetFieldVal("DTE_CALLCAMPAIGNCOMPLETEDATE", Now, 2)
            //End If

            return true;
        }
        public bool GP_FormControlOnChange_BTN_INSERTLINE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 02162018 TKT#2108 : Journal feature

            //goP.TraceLine("", "", sProc)
            //goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)
            Form doForm = (Form)par_doCallingObject;
            //Dim sWork As String
            string sParams = "";
            string sDateStamp = "";


            //GetDateTimeStamp parameters:
            //par_s1: Format of the date and time. Supported values:
            //			NEUTRAL		'Default: Ex: '2004-09-24 16:37 GMT' if par_s4 is 'UTC'.
            //			REGIONAL	'Same as NEUTRAL until regional formatting is implemented.
            //par_s2: Name of the variable in which to return the date/time stamp.
            //par_s3: Format of the user code to include. Supported values:
            //			NONE		'Default. Add no user code.
            //			CODE		'User Code.
            //par_s4: Time zone (UTC (GMT) is the default). Supported values:
            //           UTC         'Default
            //           UTCNOOFFSETLABEL
            //           USER        
            //           USERNOOFFSETLABEL
            //           SERVER  
            //           SERVERNOOFFSETLABEL
            par_oReturn = sDateStamp;
            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "", "CODE", "USERNOOFFSETLABEL");
            sDateStamp = par_oReturn.ToString();
            //returns var sDateStamp
            doForm = (Form)par_doCallingObject;
            doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", "Cancel", "", sDateStamp + " ", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", "GP_FormControlOnChange_BTN_INSERTLINE");

            par_bRunNext = false;

            par_doCallingObject = doForm;
            return true;

        }
        public bool GP_CreateActLog_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sNotes = null;
            string sWork = null;
            int lWork = 0;

            //If answered Yes on MB to create a journal, then go directly to script that creates journal.
            //If UCase(par_s1) = "YES" Then
            //    GoTo CREATEACTLOG
            //End If


            string sMessage = null;

            string MMO_JOURNAL_Var = (doForm.doRS.GetFieldVal("MMO_JOURNAL") == null) ? "" : doForm.doRS.GetFieldVal("MMO_JOURNAL").ToString();
            string lLenJournal_Var = (doForm.oVar.GetVar("lLenJournal") == null) ? "0" : doForm.oVar.GetVar("lLenJournal").ToString();

            if (string.IsNullOrEmpty(lLenJournal_Var))
            {
                lLenJournal_Var = "0";
            }

            if (Strings.Len(MMO_JOURNAL_Var) <= Convert.ToInt32(lLenJournal_Var))
                return true;

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Demand is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Environment.NewLine);
                doForm.oVar.SetVar("GP_CreateActLog_Ran", "1");
                //Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                return true;
            }
            //If doForm.GetMode() = "CREATION" Then Return True		'The record must be in edit mode so we have its ID for links to it.
            doForm.oVar.SetVar("GP_CreateActLog_Ran", "1");

            //sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            //CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            //hard returns in the journal field
            sWork = doForm.oVar.GetVar("JournalWithHardReturns").ToString();
            //CS 2/4/10
            if (string.IsNullOrEmpty(sWork))
                return true;
            //We didn't hit MessageBoxEvent from entering a journal note.

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);

            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, lWork);

            sNotes = sNotes + "== Created from Demand '" + doForm.doRS.GetFieldVal("SYS_Name") + "'";

            doNew.SetFieldVal("MMO_NOTES", sNotes);

            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));

            doNew.SetFieldVal("MLS_Status", 1, 2);
            //Completed

            doNew.SetFieldVal("TME_STARTTIME", "Now");
            doNew.SetFieldVal("TME_ENDTIME", "Now");

            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");

            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));
            DataTable oTable = new DataTable();
            oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
            doLink = (clArray)doForm.doRS.GetLinkVal("LNK_Related_PD", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_PD", doLink);
            oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
            doLink = (clArray)doForm.doRS.GetLinkVal("LNK_Related_PD%%LNK_RELATED_VE", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_VE", doLink);
            oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
            doLink = (clArray)doForm.doRS.GetLinkVal("LNK_Related_CO", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_CO", doLink);

            doNew.SetFieldVal("LNK_CONNECTED_GP", doForm.doRS.GetFieldVal("GID_ID"));

            doNew.SetFieldVal("MLS_TYPE", 25, 2);
            //Journal

            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine("", "Created."));

            if (doNew.Commit() != 1)
            {
                //delete(doNew)
                //delete(doLink)
                doNew = null;
                doLink = null;

                string sError = goErr.GetLastError();
                //Dim sMessage As String
                sMessage = goErr.GetLastError("MESSAGE");
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Environment.NewLine);

                return false;
            }


            doNew = null;
            doLink = null;
            par_doCallingObject = doForm;
            return true;

        }

        public bool OP_ViewControlOnChange_BTN_RECONCILE_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 11292017 : Reconcile Opps and Quotes from Desktop

            QTOP_Reconcile(ref par_doCallingObject);

            return true;
        }
        public bool QT_ViewControlOnChange_BTN_RECONCILE_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //Delete button on Ap view Ext Dev Review DT

            //VS 11292017 : Reconcile Opps and Quotes from Desktop

            QTOP_Reconcile(ref par_doCallingObject);

            return true;
        }

        public void QTOP_Reconcile(ref object par_doCallingObject)
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 11292017 : Reconcile Opps and Quotes from Desktop

            Desktop doDesktop = (Desktop)par_doCallingObject;
            string QuoteSelectedRecord = "";
            string OppSelectedRecord = "";

            //Get Selected RecordIDs
            string Key = "";
            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
            {
                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
            }
            //Opps View
            SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_VIE_E236E428-F525-4643-5858-A83A00AAD94D");
            OppSelectedRecord = _SessionViewInfo.LastSelectedRecord;

            //Quotes View
            _SessionViewInfo = Util.SessionViewInfo(Key + "_VIE_326293B3-BCF6-4F76-5858-A83A00AAFB32");
            QuoteSelectedRecord = _SessionViewInfo.LastSelectedRecord;

            //Validations on Selected Records
            if (string.IsNullOrEmpty(OppSelectedRecord))
            {
                goUI.NewWorkareaMessage("Please select an Opportunity", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                return;
            }
            if (string.IsNullOrEmpty(QuoteSelectedRecord))
            {
                goUI.NewWorkareaMessage("Please select a Quote", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                return;
            }

            clRowSet doQTRS = new clRowSet("QT", clC.SELL_EDIT, "GID_ID=" + QuoteSelectedRecord, "", "", 1, "", "", "", "", "", true, true, false, false, -1, "", false, false, 1800);
            doQTRS.SetFieldVal("LNK_RELATED_OP", OppSelectedRecord, 1);
            if (doQTRS.Commit() == 1)   //Success
            {
                goUI.NewWorkareaMessage("Successfully Linked Opportunity to Quote", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                return;
            }
            else   //Failed
            {
                goUI.NewWorkareaMessage("Failed to Link Opportunity to Quote. Please Contact your Administrator", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                return;
            }

        }

        public bool US_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Rowset object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //VS 12142017 : Create Email Alias for User Email Addresses if Does not exist already
            //Used by Email log Automator
            if (!string.IsNullOrEmpty(doRS.GetFieldVal("EML_EMAIL", 1).ToString().Trim()))
            {
                clRowSet doELCheckRS = new clRowSet("EL", clC.SELL_READONLY, "TXT_EMAILADDRESS='" + doRS.GetFieldVal("EML_EMAIL", 1).ToString() + "' AND LNK_RELATED_US='" + doRS.GetFieldVal("GID_ID", 1).ToString() + "'");
                if (doELCheckRS.GetFirst() == 0)
                {
                    //Does Not Exist
                    clRowSet doELRS = new clRowSet("EL", clC.SELL_ADD, "", "", "", -1, "", "", "", "", "", true, true, false, false, -1, "", false, false, 1800);
                    doELRS.SetFieldVal("TXT_EMAILADDRESS", doRS.GetFieldVal("EML_EMAIL", 1), 1);
                    doELRS.SetFieldVal("LNK_RELATED_US", doRS.GetFieldVal("GID_ID", 1), 1);
                    doELRS.SetFieldVal("CHK_DoNotAutoconnect", 0, 2);
                    if (doELRS.Commit() == 1)
                    {
                        doRS.SetFieldVal("LNK_CONNECTED_EL", doELRS.GetFieldVal("GID_ID").ToString(), 1);
                    }
                }
            }

            par_doCallingObject = doRS;

            return true;
        }

        #region LogEMailAutomator
        public bool AutoLogEmailContent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: In a _Pre script set to False to prevent the main clScripts script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {

                object doForm = par_doCallingObject;

                cljsondata _cljsondata = new cljsondata();
                string suid = ""; //GetFields(_cljsondata, "txt_suid");
                string sfoldername = "Inbox"; // GetFields(_cljsondata, "txt_foldername");
                string sFromEmail = "";
                string sNumOfDaysBack = "1";
                string sSubject = "";

                goMeta = (clMetaData)Util.GetInstance("meta");
                goP = (clProject)Util.GetInstance("p");
                goTR = (clTransform)Util.GetInstance("tr");

                DateTime currentDateTime = DateTime.UtcNow;

                //DateTime LastUpdatedDate = Convert.ToDateTime(goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", "LASTUPDATEDDATE"));
                //TimeSpan ts = currentDateTime - LastUpdatedDate;
                //sNumOfDaysBack = Math.Floor(ts.TotalDays + 1).ToString();
                ////DateTime updatedDateTime = currentDateTime.Add(new TimeSpan(0, -10, 0));
                //LogMail(suid, sfoldername, sNumOfDaysBack, sSubject, sFromEmail);


                //Mailbox Email Folder Rules. We will use those from now on instead of 'Inbox' folder
                //1. If Email Sender does not Contains '@allied-automation.com' then move to 'Received' Folder
                //2. If Email Sender does Contains '@allied-automation.com' then move to 'Sent Items' Folder
                DateTime LastUpdatedDate_Inbox = Convert.ToDateTime(goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", "LASTUPDATEDDATE_INBOX"));
                DateTime LastUpdatedDate_Received = Convert.ToDateTime(goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", "LASTUPDATEDDATE_RECEIVED"));
                DateTime LastUpdatedDate_Sent = Convert.ToDateTime(goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", "LASTUPDATEDDATE_SENT"));

                clServiceOutput objclserviceoutput = new clServiceOutput();

                //Let's run for Inbox also to work with existing emails and any emails that failed outlook rules and fall into this folder
                //Sent Folder
                TimeSpan ts = currentDateTime - LastUpdatedDate_Sent;
                sNumOfDaysBack = Math.Floor(ts.TotalDays + 1).ToString();
                objclserviceoutput = LogMail(suid, "Sent Items", sNumOfDaysBack, sSubject, sFromEmail, "LASTUPDATEDDATE_SENT", "Sent");

                //Received Folder
                if (objclserviceoutput.Status != "Fail")
                {
                    ts = currentDateTime - LastUpdatedDate_Received;
                    sNumOfDaysBack = Math.Floor(ts.TotalDays + 1).ToString();
                    objclserviceoutput = LogMail(suid, "Received", sNumOfDaysBack, sSubject, sFromEmail, "LASTUPDATEDDATE_RECEIVED", "Received");
                }

                //Inbox Folder
                if (objclserviceoutput.Status != "Fail")
                {
                    ts = currentDateTime - LastUpdatedDate_Inbox;
                    sNumOfDaysBack = Math.Floor(ts.TotalDays + 1).ToString();
                    objclserviceoutput = LogMail(suid, "Inbox", sNumOfDaysBack, sSubject, sFromEmail, "LASTUPDATEDDATE_INBOX", "");
                }

                return true;
            }
            catch (Exception ex)
            {
                goLog.Log("LogMail", ex.ToString(), 0, true);
                System.Data.SqlClient.SqlConnection con = goData.GetConnection();
                goMeta.LineWrite("GLOBAL", "PTA_78212E8D-8500-43B9-5858-A82B00B49AEE", "INPROGRESS", "0", ref con);
                return true;
            }
        }

        public string GetFields(cljsondata cldata, string sFieldName)
        {

            dynamic field = (from item in cldata.lstData where item.FieldName.ToLower() == sFieldName select item);

            if (field != null)
            {
                return field.FirstOrDefault().FieldValue;
            }

            return "";
        }

        public clServiceOutput LogMail(string suid, string sFoldername, string sNumOfDaysBack, string sSubject, string sFromEmail = "", string sLastUpdatedDateProperty = "LASTUPDATEDDATE_RECEIVED", string sType = "Received")
        {
            clServiceOutput objclserviceoutput = new clServiceOutput();

            try
            {
                clUtil goUt = new clUtil();
                ImapUtility.ImapUtility.SellMailItem LogData = new ImapUtility.ImapUtility.SellMailItem();


                UserMailSettings currUsermailsettings = GetUserWebMailSettings();

                DataSet ds = GetMails(currUsermailsettings, sFoldername, sNumOfDaysBack, sSubject, sFromEmail, 1, 1000);

                if (ds.Tables.Count != 0)
                {
                    DataTable dtAllMails = ds.Tables[0];

                    PublicDomain.TzTimeZone zone = default(PublicDomain.TzTimeZone);
                    string sCurrentUser = goP.GetMe("ID");
                    zone = goTR.UTC_GetUserTimeZone(sCurrentUser);


                    if (dtAllMails != null && dtAllMails.Rows.Count > 0)
                    {
                        DateTime currentDateTime = DateTime.Now;
                        DateTime updatedDateTime = currentDateTime.Add(new TimeSpan(0, -10, 0));

                        //Get last log mail updated date..J
                        DateTime LastUpdatedDate = Convert.ToDateTime(goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", sLastUpdatedDateProperty));

                        DataView dv = new DataView(dtAllMails);

                        //Sort by email received date to get last updated date and update it in 'OTH_LOGMAILAUTOMATORDATA'..J
                        dv.Sort = "DateReceived ASC";

                        //Filter with emails to log only recent received mails..J
                        dv.RowFilter = "DateReceived > #" + LastUpdatedDate.ToString() + "#";

                        //Set type to add MLS_TYPE when logging email AC..J
                        string Type = "Received";

                        foreach (DataRowView row in dv)
                        {
                            suid = row["UID"].ToString();
                            if (!string.IsNullOrEmpty(suid))
                            {
                                MailBee.Mime.MailMessage msg = new MailBee.Mime.MailMessage();
                                LogData = GetMailContent(ref msg, currUsermailsettings, sFoldername, suid);

                                if (LogData != null)
                                {
                                    DataTable dt = new DataTable();
                                    dt.Columns.Add("Type");
                                    dt.Columns.Add("From");
                                    dt.Columns.Add("To");
                                    dt.Columns.Add("CC");
                                    dt.Columns.Add("BCC");
                                    dt.Columns.Add("Subject");
                                    dt.Columns.Add("Body");
                                    dt.Columns.Add("Attachments");
                                    dt.Columns.Add("Date");
                                    dt.Columns.Add("Time");
                                    dt.Columns.Add("EmailUID");
                                    dt.TableName = "email";

                                    string To_Address = "";
                                    string cc_Address = "";
                                    string Bcc_Address = "";
                                    string Attachments = "";

                                    To_Address = ConvertCollectionToString(LogData.c_To);
                                    cc_Address = ConvertCollectionToString(LogData.c_CC);
                                    Bcc_Address = ConvertCollectionToString(LogData.c_BCC);
                                    //Attachments = ConvertCollectionToString(LogData.c_Attachments);
                                    Attachments = ConvertAttachmentCollectionToString(LogData.c_Attachments);
                                    LogData.s_TextBody = LogData.s_HTMLBody;// goUt.StripHTML(LogData.s_HTMLBody);

                                    //If Inbox folder need to figure out if Received or Sent Item
                                    if (sFoldername == "Inbox")
                                    {
                                        //If From email contains any of Allied domains it is SentItem else Received
                                        if (LogData.s_From.Contains("@allied-automation.com") || LogData.s_From.Contains("@aai.co") || LogData.s_From.Contains("@alliedautomationinc.onmicrosoft.com"))
                                        {
                                            Type = "Sent";
                                        }
                                        else
                                        {
                                            Type = "Received";
                                        }
                                    }
                                    else
                                    {
                                        Type = sType;
                                    }

                                    //dt.Rows.Add(Type, LogData.s_From, LogData.c_To.ToString(), LogData.c_CC.ToString(), LogData.c_BCC.ToString(), LogData.s_Subject, LogData.s_TextBody, LogData.c_Attachments.ToString(), zone.ToLocalTime(LogData.dt_DateReceived), zone.ToLocalTime(LogData.dt_DateReceived).TimeOfDay);
                                    dt.Rows.Add(Type, LogData.s_From, To_Address, cc_Address, Bcc_Address, LogData.s_Subject, LogData.s_TextBody, Attachments, zone.ToLocalTime(LogData.dt_DateReceived), zone.ToLocalTime(LogData.dt_DateReceived).TimeOfDay, suid);

                                    clEmail clE = new clEmail();
                                    //This is here no longer be used as we need gid_id of newly created AC to set attachments path..J
                                    //string result = clE.LogMessage(dt);   

                                    //So we need the custom method here only..J
                                    string gid_id = Guid.NewGuid().ToString();

                                    //S_B checking attachments exists
                                    if (!string.IsNullOrEmpty(Attachments))
                                    {
                                        //Save attachments..J
                                        SaveAttachmentsIntoCloud(msg, gid_id);
                                    }

                                    //Passing email data including attachment related GID_ID to extract it from azure temp folder.
                                    string result = LogMessage(dt, clE, ref gid_id);

                                    if (result == "1")
                                    {
                                        System.Data.SqlClient.SqlConnection con = goData.GetConnection();
                                        goMeta.LineWrite("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", sLastUpdatedDateProperty, Convert.ToDateTime(row["DateReceived"]).ToString(), ref con);
                                        goLog.Log("LogMail", "Log mail done successfully with UID " + suid, 0, true);

                                        objclserviceoutput.Status = "Success";
                                        objclserviceoutput.Data = "Log mail done successfully";

                                        //Add attachments in the selltis 70 specified path..
                                        //if (!string.IsNullOrEmpty(Attachments))
                                        //{
                                        //    //Save attachments..J
                                        //    SaveAttachmentsIntoCloud(msg, gid_id);

                                        //    //string sBody = SaveAttachementsAndGetAttachmentsBody(currUsermailsettings, gid_id, suid, sFoldername);

                                        //    ////Update the MMO_LETTER with html body of the email becasue if the body contains images, then need to convert it into base 64 inorder to shoe in MMO control..J
                                        //    //clRowSet ACrs = new clRowSet("AC", 1, "GID_ID = '" + gid_id + "'", "", "MMO_LETTER", -1, "", "", "", "", "", true, true);
                                        //    //ACrs.GetFieldVal("MMO_LETTER");
                                        //    //ACrs.SetFieldVal("MMO_LETTER", "From: " + clE.GetVal(dt, "From") + Constants.vbCrLf + "Received: " + clE.GetVal(dt, "Date") + " " + clE.GetVal(dt, "Time") + Constants.vbCrLf + "Subject: " + clE.GetVal(dt, "Subject") + Constants.vbCrLf + Constants.vbCrLf + sBody);
                                        //    //ACrs.Commit();
                                        //}
                                    }
                                    else
                                    {
                                        objclserviceoutput.Status = "Fail";
                                        objclserviceoutput.Data = result;
                                        goLog.Log("LogMail", "Log mail failed with UID " + suid, 0, true);
                                        return objclserviceoutput;
                                    }
                                }

                            }
                        }
                    }
                }

                return objclserviceoutput;
            }
            catch (Exception ex)
            {
                if (ex.GetType().ToString() != "System.Threading.ThreadAbortException" && ex.GetType().ToString() != "System.IO.IOException")
                {
                    goLog.Log("LogMail", "Log mail failed with UID " + suid + ex.ToString(), 0, true);
                }
                objclserviceoutput.Status = "Fail";
                return objclserviceoutput;
            }
        }

        public ImapUtility.ImapUtility.SellMailItem GetMailContent(ref MailBee.Mime.MailMessage msg, UserMailSettings currUsermailsettings, string sFolder, string sUid)
        {
            try
            {
                string EmailUsername = null;
                string EmailPwd = null;
                string serverName = null;
                int portNumber = 0;

                //UserMailSettings currUsermailsettings = GetUserWebMailSettings();

                EmailUsername = currUsermailsettings.UserName;
                EmailPwd = currUsermailsettings.Password;
                serverName = currUsermailsettings.ServerName;
                portNumber = currUsermailsettings.PortNumber;

                sFolder = HttpUtility.UrlDecode(sFolder);
                sUid = HttpUtility.UrlDecode(sUid);

                sFolder = (string.IsNullOrEmpty(sFolder) ? "Inbox" : sFolder);

                //ImapUtility.ImapUtility utility = new ImapUtility.ImapUtility();

                //return utility.GetSelectedMailContent(EmailUsername, EmailPwd, serverName, portNumber, sUid, sFolder);
                return GetSelectedMailContent(ref msg, EmailUsername, EmailPwd, serverName, portNumber, sUid, sFolder);
            }
            catch (Exception ex)
            {
                if (ex.GetType().ToString() != "System.Threading.ThreadAbortException" && ex.GetType().ToString() != "System.IO.IOException")
                {
                    goLog.Log("LogMail", "Log mail failed with UID " + sUid + ex.ToString(), 0, true);
                }
                return null;
            }

        }

        public ImapUtility.ImapUtility.SellMailItem GetSelectedMailContent(ref MailBee.Mime.MailMessage msg, string UserName, string Password, string ServerName, int PortNumber, string sUID, string sFolder, string sMID = "")
        {
            string uid = sUID;
            string partID = "2";
            string ofolder = sFolder;
            string url = "";
            string sMailBeeLicense = "MN110-D21AE5AB1BB11AAA1A6F90390211-94CA";
            MailBee.ImapMail.Imap imp = new MailBee.ImapMail.Imap(sMailBeeLicense);
            //MailBee.Mime.MailMessage msg = new MailBee.Mime.MailMessage();
            bool bAlreadyDownloaded = false;

            //if (sMID != "")
            //{
            //    string sEMLFile = "";
            //    if (My.Computer.FileSystem.FileExists(sEMLFile))
            //    {
            //        byte[] msgBytes = null;
            //        BinaryReader br;
            //        try
            //        {
            //            br = new BinaryReader(File.OpenRead(sEMLFile));
            //            FileInfo fi = new FileInfo(sEMLFile);
            //            msgBytes = br.ReadBytes(Convert.ToInt32(fi.Length));
            //        }
            //        finally
            //        {
            //            if (br != null)
            //            {
            //                br.Close();
            //            }
            //        }

            //        msg.LoadMessage(msgBytes);
            //        bAlreadyDownloaded = true;
            //    }
            //}

            if (bAlreadyDownloaded == false)
            {
                if (PortNumber.ToString() == "0")
                {
                    imp.Connect(ServerName);
                }
                else
                {
                    imp.Connect(ServerName, PortNumber);
                }

                imp.Login(UserName, Password);
                imp.SelectFolder(ofolder);
                msg = imp.DownloadEntireMessage(Convert.ToInt64(uid), true);
                imp.Disconnect();
            }

            string sPrivateDirectory = "";
            string htmlBody = "";
            if (msg != null)
            {
                msg.Parser.PlainToHtmlMode = MailBee.Mime.PlainToHtmlAutoConvert.IfNoHtml;
                if (bAlreadyDownloaded == false)
                {
                    htmlBody = msg.GetHtmlWithBase64EncodedRelatedFiles();
                    string s = "<base target=\"_blank\" />";
                    string sBody = htmlBody;
                    int iBody = sBody.IndexOf("<head");
                    if (iBody < 0)
                    {
                        sBody = s + sBody;
                    }
                    else
                    {
                        int iBodyEnd = sBody.IndexOf(">", iBody);
                        sBody = sBody.Insert(iBodyEnd + 1, s);
                    }

                    htmlBody = sBody;

                }
                else
                {
                }
            }
            else
            {
                url = "Inbox is empty or the requested e-mail is no longer on the server (may happen with Gmail/POP3)";
            }

            if (bAlreadyDownloaded == false)
            {
            }

            ImapUtility.ImapUtility.SellMailItem _objMailItem = new ImapUtility.ImapUtility.SellMailItem();
            string sTOs = "";
            string sCCs = "";
            string sBCCs = "";
            foreach (MailBee.Mime.EmailAddress sTO in msg.To)
            {
                sTOs = sTOs + sTO.Email + ",";
            }

            if (Microsoft.VisualBasic.Strings.Len(sTOs) > 0)
            {
                sTOs = Microsoft.VisualBasic.Strings.Left(sTOs, Microsoft.VisualBasic.Strings.Len(sTOs) - 1);
            }

            foreach (MailBee.Mime.EmailAddress sCC in msg.Cc)
            {
                sCCs = sCCs + sCC.Email + ",";
            }

            if (Microsoft.VisualBasic.Strings.Len(sCCs) > 0)
            {
                sCCs = Microsoft.VisualBasic.Strings.Left(sCCs, Microsoft.VisualBasic.Strings.Len(sCCs) - 1);
            }

            foreach (MailBee.Mime.EmailAddress sBCC in msg.Bcc)
            {
                sBCCs = sBCCs + sBCC.Email + ",";
            }

            if (Microsoft.VisualBasic.Strings.Len(sBCCs) > 0)
            {
                sBCCs = Microsoft.VisualBasic.Strings.Left(sBCCs, Microsoft.VisualBasic.Strings.Len(sBCCs) - 1);
            }

            _objMailItem.c_To = ConvertAddressStringToCollection(sTOs);
            _objMailItem.c_CC = ConvertAddressStringToCollection(sCCs);
            _objMailItem.c_BCC = ConvertAddressStringToCollection(sBCCs);
            _objMailItem.s_From = msg.From.Email;
            _objMailItem.s_Subject = msg.Subject;
            if (htmlBody != "")
            {
                _objMailItem.s_HTMLBody = htmlBody;
            }
            else
            {
                _objMailItem.s_HTMLBody = msg.BodyPlainText;
            }

            _objMailItem.s_TextBody = msg.BodyPlainText;
            //_objMailItem.c_Attachments = ConvertAddressStringToCollection(GetAttachmentNamesFromMsg(msg.Attachments));
            _objMailItem.c_Attachments = GetAttachmentCollection(msg.Attachments);
            _objMailItem.s_UID = msg.UidOnServer.ToString();
            _objMailItem.s_MID = getSHA1Hash(msg.MessageID).ToString();
            _objMailItem.s_URL = url;
            if (msg.DateReceived == DateTime.MinValue)
            {
                _objMailItem.dt_DateReceived = msg.Date;
            }
            else
            {
                _objMailItem.dt_DateReceived = msg.DateReceived;
            }

            if (msg.DateSent == DateTime.MinValue)
            {
                _objMailItem.dt_DateSent = msg.Date;
            }
            else
            {
                _objMailItem.dt_DateSent = msg.DateSent;
            }

            return _objMailItem;
        }

        private static void SaveAttachmentsIntoCloud(MailBee.Mime.MailMessage msg, string GID_ID)
        {
            //Take local path for temperaraly to save cloud files..J
            string sPath = System.Web.HttpContext.Current.Server.MapPath("~/Temp/");

            string AttachmentsPath = ((DataTable)Util.GetSessionValue("SiteSettings")).Rows[0]["AttachmentsPath"].ToString();
            string AttachmentsStorageType = System.Web.Configuration.WebConfigurationManager.AppSettings["AttachmentsStorageType"].ToString();

            string sAttachmentsPath = sPath + "AC" + "\\" + GID_ID + "\\" + "ADR_ATTACHMENTS";

            string sAttachmentsTempPath = sAttachmentsPath;

            if (AttachmentsStorageType == "Server")
            {
                sAttachmentsTempPath = AttachmentsPath + "AC" + "\\" + GID_ID + "\\" + "ADR_ATTACHMENTS";
                if (!Directory.Exists(sAttachmentsTempPath))
                {
                    Directory.CreateDirectory(sAttachmentsTempPath);
                }

                //For every attachment...
                foreach (MailBee.Mime.Attachment attach in msg.Attachments)
                {
                    //Save it to selltispublic files folder where we save attachements..J                    
                    attach.SaveToFolder(sAttachmentsTempPath, true);
                }
            }
            else
            {
                if (!Directory.Exists(sAttachmentsPath))
                {
                    Directory.CreateDirectory(sAttachmentsPath);
                }

                //For every attachment...
                foreach (MailBee.Mime.Attachment attach in msg.Attachments)
                {
                    attach.SaveToFolder(sAttachmentsTempPath, true);

                    if (File.Exists(sAttachmentsTempPath + "/" + attach.Filename))
                    {
                        Stream fileStream = File.OpenRead(sAttachmentsTempPath + "\\" + attach.Filename);

                        //Save it to cloud files folder where we save attachements..J
                        var FinalFolder = clAzureFileStorage.GetFinalFolder("AC", GID_ID, "ADR_ATTACHMENTS", "", false);

                        clAzureFileStorage.UploadFromStream(FinalFolder, fileStream, attach.Filename);

                        fileStream.Flush();
                        fileStream.Close();

                        if (File.Exists(sAttachmentsTempPath + "/" + attach.Filename))
                        {
                            File.Delete(sAttachmentsTempPath + "/" + attach.Filename);
                        }
                    }
                }

                Directory.Delete(sPath + "AC", true);
            }
        }
        private string GetAttachmentNamesFromMsg(MailBee.Mime.AttachmentCollection ac)
        {
            string sReturn = "";
            foreach (MailBee.Mime.Attachment attach in ac)
            {
                sReturn = sReturn + attach.Name + ", ";
            }

            if (Microsoft.VisualBasic.Strings.Len(sReturn) > 2)
            {
                sReturn = Microsoft.VisualBasic.Strings.Left(sReturn, Microsoft.VisualBasic.Strings.Len(sReturn) - 2);
            }
            else
            {
                sReturn = "";
            }

            return sReturn;
        }

        public DataSet GetMails(UserMailSettings currUsermailsettings, string sFolder, string sNumOfDaysBack, string sSubject, string sFromEmail, int iFirstItem, int iNumberOfItems)
        {
            string EmailUsername = null;
            string EmailPwd = null;
            string serverName = null;
            int portNumber = 0;

            //UserMailSettings currUsermailsettings = GetUserWebMailSettings();

            EmailUsername = currUsermailsettings.UserName;
            EmailPwd = currUsermailsettings.Password;
            serverName = currUsermailsettings.ServerName;
            portNumber = currUsermailsettings.PortNumber;

            sFolder = HttpUtility.UrlDecode(sFolder);

            sFolder = (string.IsNullOrEmpty(sFolder) ? "Inbox" : sFolder);

            ImapUtility.ImapUtility utility = new ImapUtility.ImapUtility();

            int intdaysback = 0;

            if (!string.IsNullOrEmpty(sNumOfDaysBack))
            {
                int.TryParse(sNumOfDaysBack, out intdaysback);
            }

            //return utility.GetMails(currUsermailsettings.UserName, currUsermailsettings.Password, currUsermailsettings.ServerName, currUsermailsettings.PortNumber, sFolder, 1, iNumberOfItems, intdaysback, sSubject, sFromEmail);
            return GetAllMails(currUsermailsettings.UserName, currUsermailsettings.Password, currUsermailsettings.ServerName, currUsermailsettings.PortNumber, sFolder, iFirstItem, iNumberOfItems, intdaysback, sSubject, sFromEmail);
        }


        public DataSet GetAllMails(string UserName, string Password, string ServerName, int PortNumber, string FolderName, int iFirstItem, int iNumberOfItems, int DaysBack, string Subject, string FromEmailID)
        {
            DataSet ds = new DataSet();
            DataTable dt = new DataTable();
            string sMailBeeLicense = "MN110-D21AE5AB1BB11AAA1A6F90390211-94CA";
            bool bAccountOrLoginError;
            string sAccountOrLoginErrorMessage;
            Imap oIMAP = new Imap(sMailBeeLicense);
            MailBee.SmtpMail.Smtp oSMTP = new MailBee.SmtpMail.Smtp(sMailBeeLicense);
            string server = ServerName;
            int portno = PortNumber;
            string folder = FolderName;
            Imap imp = new Imap();
            try
            {
                try
                {
                    if (imp.Connect(server, portno) == false)
                    {
                        bAccountOrLoginError = true;
                        sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" + server + "). No further details are available.";
                        imp.Disconnect();
                        return ds;
                    }

                    if (imp.Login(UserName, Password) == false)
                    {
                        bAccountOrLoginError = true;
                        sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" + server + "). No further details are available.";
                        imp.Disconnect();
                        return ds;
                    }
                }
                catch (Exception ex)
                {
                    bAccountOrLoginError = true;
                    sAccountOrLoginErrorMessage = ex.Message;
                    return ds;
                }

                try
                {
                    EnvelopeCollection msgs = new EnvelopeCollection();
                    UidCollection uids = null;
                    dt.Columns.Add("HasAttachment", System.Type.GetType("System.Boolean"));
                    dt.Columns.Add("AttachmentList");
                    dt.Columns.Add("From");
                    dt.Columns.Add("To");
                    dt.Columns.Add("CC");
                    dt.Columns.Add("BCC");
                    dt.Columns.Add("Subject");
                    dt.Columns.Add("DateReceived", System.Type.GetType("System.String"));
                    dt.Columns.Add("IsRead", System.Type.GetType("System.Boolean"));
                    dt.Columns.Add("Size");
                    dt.Columns.Add("UID");
                    dt.Columns.Add("MID");
                    DataRow dr;
                    try
                    {
                        if (imp.SelectFolder(folder) != true)
                        {
                            imp.Disconnect();
                            return ds;
                        }
                    }
                    catch (Exception ex)
                    {
                        return ds;
                    }

                    if ((iFirstItem != 0))
                    {
                    }
                    else
                    {
                        iFirstItem = 1;
                    }

                    if ((iNumberOfItems != 0))
                    {
                    }
                    else
                    {
                        iNumberOfItems = 50;
                    }

                    int intFrom = (imp.MessageCount + 1) - iFirstItem;
                    int intTo = (intFrom - iNumberOfItems) + 1;
                    if (intTo < 1)
                        intTo = 1;
                    if (imp.MessageCount == 0)
                    {
                        return ds;
                    }
                    else
                    {
                        msgs = imp.DownloadEnvelopes(intTo + ":" + intFrom, false, EnvelopeParts.MailBeeEnvelope | EnvelopeParts.BodyStructure, 80, null, null);
                    }

                    msgs.Reverse();
                    MailBee.ImapMail.Envelope env;
                    bool bSeen;
                    foreach (MailBee.ImapMail.Envelope env1 in msgs)
                    {
                        dr = dt.NewRow();
                        Collection cCol = ConvertAddressStringToCollection(env1.To.ToString());
                        string sString = ConvertAddressCollectionToString(cCol);
                        var env2 = env1;
                        dr["HasAttachment"] = TestForAttachments(ref env2, true);
                        var env3 = env1;
                        dr["AttachmentList"] = TestForAttachments(ref env3, false);
                        dr["From"] = HttpUtility.HtmlEncode(env1.From.ToString());
                        dr["To"] = HttpUtility.HtmlEncode(env1.To.ToString());
                        dr["CC"] = HttpUtility.HtmlEncode(env1.Cc.ToString());
                        dr["BCC"] = HttpUtility.HtmlEncode(env1.Bcc.ToString());
                        dr["Subject"] = env1.Subject.ToString();
                        dr["DateReceived"] = env1.DateReceived;
                        if (env1.Flags.ToString().ToLower().Contains("\\seen"))
                            bSeen = true;
                        else
                            bSeen = false;
                        dr["IsRead"] = bSeen;
                        dr["Size"] = Math.Round(BytesTO(env1.Size, convTo.KB), 1);
                        dr["UID"] = env1.Uid.ToString();
                        dr["MID"] = getSHA1Hash(env1.MessageID);
                        dt.Rows.Add(dr);
                    }

                    ds.Tables.Add(dt);
                }
                catch (Exception ex)
                {
                    bAccountOrLoginError = true;
                    sAccountOrLoginErrorMessage = ex.Message;
                    return ds;
                }

                imp.Disconnect();
                string addedDays;
                string text = DaysBack.ToString();
                int value = int.Parse(text);
                if (DaysBack != 0 && FromEmailID != "" && Subject != "")
                {
                    string SearchCriteria = FromEmailID;
                    addedDays = DateAndTime.Today.AddDays(-(value)).ToString();
                    string expression = "From like '%" + SearchCriteria + "%'AND Subject like '%" + Subject + "%' AND DateReceived > '" + addedDays + "'";
                    string sortOrder = "DateReceived DESC";
                    DataRow[] foundRows;
                    foundRows = dt.Select(expression, sortOrder);
                    DataTable dt1 = new DataTable();
                    dt1 = foundRows.CopyToDataTable();
                    if (dt1.Rows.Count != 0)
                    {
                        ds.Tables.Clear();
                        ds.Tables.Add(dt1);
                        return ds;
                    }
                    else
                    {
                        dt1 = null;
                        ds.Tables.Clear();
                        ds.Tables.Add(dt1);
                        return ds;
                    }
                }
                else if (DaysBack != 0 && Subject != "")
                {
                    string SearchCriteria = Subject;
                    addedDays = DateAndTime.Today.AddDays(-(value)).ToString();
                    string expression = "Subject like '%" + SearchCriteria + "%' AND DateReceived > '" + addedDays + "'";
                    string sortOrder = "DateReceived DESC";
                    DataRow[] foundRows;
                    foundRows = dt.Select(expression, sortOrder);
                    DataTable dt1 = new DataTable();
                    dt1 = foundRows.CopyToDataTable();
                    if (dt1.Rows.Count != 0)
                    {
                        ds.Tables.Clear();
                        ds.Tables.Add(dt1);
                        return ds;
                    }
                    else
                    {
                        dt1 = null;
                        ds.Tables.Clear();
                        ds.Tables.Add(dt1);
                        return ds;
                    }
                }
                else if (DaysBack != 0 && FromEmailID != "")
                {
                    string SearchCriteria = FromEmailID;
                    addedDays = DateAndTime.Today.AddDays(-(value)).ToString();
                    string expression = "From like '%" + SearchCriteria + "%' AND DateReceived > '" + addedDays + "'";
                    string sortOrder = "DateReceived DESC";
                    DataRow[] foundRows;
                    foundRows = dt.Select(expression, sortOrder);
                    DataTable dt1 = new DataTable();
                    dt1 = foundRows.CopyToDataTable();
                    if (dt1.Rows.Count != 0)
                    {
                        ds.Tables.Clear();
                        ds.Tables.Add(dt1);
                        return ds;
                    }
                    else
                    {
                        dt1 = null;
                        ds.Tables.Clear();
                        ds.Tables.Add(dt1);
                        return ds;
                    }

                    //return ds;
                }
                else if (DaysBack != 0)
                {
                    //addedDays = DateAndTime.Today.AddDays(-(value)).ToString();
                    //string expression = "DateReceived > '" + addedDays + "'";
                    //string sortOrder = "DateReceived DESC";
                    //DataRow[] foundRows;
                    //foundRows = dt.Select(expression, sortOrder);
                    //DataTable dt1 = new DataTable();
                    //dt1 = foundRows.CopyToDataTable();
                    //if (dt1.Rows.Count != 0)
                    //{
                    //    ds.Tables.Clear();
                    //    ds.Tables.Add(dt1);
                    //    return ds;
                    //}
                    //else
                    //{
                    //    dt1 = null;
                    //    ds.Tables.Clear();
                    //    ds.Tables.Add(dt1);
                    //    return ds;
                    //}

                    ds.Tables.Clear();
                    ds.Tables.Add(dt);
                    return ds;
                }
                else if (Subject != "")
                {
                    string SearchCriteria = Subject;
                    string expression = "Subject like '%" + SearchCriteria + "%'";
                    string sortOrder = "DateReceived DESC";
                    DataRow[] foundRows;
                    foundRows = dt.Select(expression, sortOrder);
                    DataTable dt1 = new DataTable();
                    dt1 = foundRows.CopyToDataTable();
                    if (dt1.Rows.Count != 0)
                    {
                        ds.Tables.Clear();
                        ds.Tables.Add(dt1);
                        return ds;
                    }
                    else
                    {
                        dt1 = null;
                        ds.Tables.Clear();
                        ds.Tables.Add(dt1);
                        return ds;
                    }
                }
                else if (FromEmailID != "")
                {
                    string SearchCriteria = FromEmailID;
                    string expression = "From like '%" + SearchCriteria + "%'";
                    string sortOrder = "DateReceived DESC";
                    DataRow[] foundRows;
                    foundRows = dt.Select(expression, sortOrder);
                    DataTable dt1 = new DataTable();
                    dt1 = foundRows.CopyToDataTable();
                    if (dt1.Rows.Count != 0)
                    {
                        ds.Tables.Clear();
                        ds.Tables.Add(dt1);
                        return ds;
                    }
                    else
                    {
                        dt1 = null;
                        ds.Tables.Clear();
                        ds.Tables.Add(dt1);
                        return ds;
                    }
                }
                else
                {
                    return ds;
                }
            }
            catch (Exception ex)
            {
                bAccountOrLoginError = true;
                sAccountOrLoginErrorMessage = ex.Message;
                return ds;
            }
        }
        private Collection ConvertAddressStringToCollection(string sAddresses)
        {
            if (sAddresses == "")
                return new Collection();
            sAddresses = Microsoft.VisualBasic.Strings.Replace(sAddresses, ";", ",");
            sAddresses = Microsoft.VisualBasic.Strings.Replace(sAddresses, ", ", ",");
            sAddresses = Microsoft.VisualBasic.Strings.Replace(sAddresses, ", ", ",");
            sAddresses = Microsoft.VisualBasic.Strings.Replace(sAddresses, ", ", ",");
            Collection cReturn = new Collection();
            string[] aAddresses = Microsoft.VisualBasic.Strings.Split(sAddresses, ",");
            foreach (string Addr in aAddresses)
            {
                cReturn.Add(Addr);
            }

            return cReturn;
        }


        private Collection GetAttachmentCollection(MailBee.Mime.AttachmentCollection ac)
        {
            Collection cReturn = new Collection();
            foreach (MailBee.Mime.Attachment attach in ac)
            {
                cReturn.Add(attach.Filename);
            }
            return cReturn;
        }
        private string ConvertAddressCollectionToString(Collection cAddresses)
        {
            string sReturn = "";
            if (cAddresses == null)
                return "";
            foreach (var Addr in cAddresses)
            {
                sReturn = sReturn + Addr + ", ";
            }

            if (Microsoft.VisualBasic.Strings.Len(sReturn) > 2)
            {
                return Microsoft.VisualBasic.Strings.Left(sReturn, Microsoft.VisualBasic.Strings.Len(sReturn) - 2);
            }
            else
            {
                return "";
            }
        }
        private object TestForAttachments(ref MailBee.ImapMail.Envelope env, bool AsBoolean)
        {
            if (env.IsValid)
            {
                System.Text.StringBuilder strBuffer = new System.Text.StringBuilder();
                ImapBodyStructureCollection parts = env.BodyStructure.GetAllParts();
                foreach (ImapBodyStructure part in parts)
                {
                    if ((part.Disposition != null && part.Disposition.ToLower() == "attachment") || (part.Filename != null && part.Filename != string.Empty) || (part.ContentType != null && part.ContentType.ToLower() == "message/rfc822"))
                    {
                        string filename;
                        if ((part.Filename != null))
                        {
                            filename = part.Filename;
                        }
                        else
                        {
                            filename = "untitled";
                        }

                        strBuffer.Append("[" + filename + "]");
                    }
                }

                if (strBuffer.Length > 0)
                {
                    if (AsBoolean)
                    {
                        return "True";
                    }
                    else
                    {
                        return strBuffer.ToString();
                    }
                }
                else
                {
                    if (AsBoolean)
                    {
                        return "False";
                    }
                    else
                    {
                        return "";
                    }
                }
            }

            if (AsBoolean == true)
            {
                return "False";
            }
            else
            {
                return "";
            }
        }
        private object getSHA1Hash(string strToHash)
        {
            System.Security.Cryptography.SHA1CryptoServiceProvider sha1Obj = new System.Security.Cryptography.SHA1CryptoServiceProvider();
            byte[] bytesToHash = System.Text.Encoding.ASCII.GetBytes(strToHash);
            bytesToHash = sha1Obj.ComputeHash(bytesToHash);
            string strResult = "";
            foreach (byte b in bytesToHash)
            {
                strResult += b.ToString("x2");
            }

            return strResult;
        }
        private double BytesTO(double lBytes, convTo convertto)
        {
            double BytesTO;
            return BytesTO = lBytes / (1024 ^ Convert.ToInt32(convertto));
        }
        private enum convTo
        {
            KB = 1,
            MB = 2,
            GB = 3,
            TB = 4
        }




        public UserMailSettings GetUserWebMailSettings()
        {
            UserMailSettings result = new UserMailSettings();
            try
            {

                goMeta = (clMetaData)Util.GetInstance("meta");
                goP = (clProject)Util.GetInstance("p");

                //clMetaData oMeta = (clMetaData)HttpContext.Current.Session["goMeta"];
                //clProject goP = HttpContext.Current.Session["goP"];
                result.UserName = goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", "IMAP_USER", "");
                string encrypPwd = goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", "IMAP_PASSWORD", "");
                //clEncrypt2 oCrypt2 = new clEncrypt2();
                result.Password = encrypPwd;
                result.ServerName = goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", "IMAP_SERVER", "");
                result.PortNumber = Convert.ToInt32(goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", "IMAP_SERVER_PORT", "143"));
                result.SmtpServerName = goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", "SMTP_SERVER", "");
                result.SmtpPortnumber = Convert.ToInt32(goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", "SMTP_SERVER_PORT", "587"));
                result.SmtpUserName = goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", "SMTP_USER", "");
                result.SmtpPassword = goMeta.LineRead("GLOBAL", "OTH_LOGMAILAUTOMATORDATA", "SMTP_PASSWORD", "");
                return result;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public string ConvertCollectionToString(Collection cAddresses)
        {

            string sReturn = "";
            if (cAddresses == null)
                return "";

            foreach (var Addr_loop in cAddresses)
            {
                sReturn = sReturn + Addr_loop + "; ";
            }

            if (Strings.Len(sReturn) > 2)
            {
                return Microsoft.VisualBasic.Strings.Left(sReturn, Strings.Len(sReturn) - 2);
            }
            else
            {
                return "";
            }

        }

        public string ConvertAttachmentCollectionToString(Collection cAttachments)
        {

            string sReturn = "";
            if (cAttachments == null)
                return "";

            foreach (var attachment in cAttachments)
            {
                sReturn = sReturn + attachment + "|";
            }

            if (Strings.Len(sReturn) > 2)
            {
                return Microsoft.VisualBasic.Strings.Left(sReturn, Strings.Len(sReturn) - 1);
            }
            else
            {
                return "";
            }

        }

        public bool LogMessageWebService(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            try
            {
                DataSet ds = (DataSet)par_doCallingObject;
                string GID_ID = par_oReturn.ToString();
                clEmail clE = new clEmail();
                LogMessage(ds.Tables[0], clE, ref GID_ID);
                par_oReturn = GID_ID;

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public string LogMessage(DataTable dt, clEmail clE, ref string GID_ID)
        {
            string functionReturnValue = null;

            string sProc = "clEmail::LogMessage";

            functionReturnValue = "";


            //try
            //{
                string sComp = null;
                string sCont = null;
                string sLink = null;
                string[] sAdd = null;
                int l = 0;
                string sComps = "";
                string sConts = "";
                string sMemo = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "", true);

                int iShare = 0;

                clRowSet objAddRowSet = new clRowSet("AC", 2);
                objAddRowSet.bBypassValidation = true;

                //VS 11252017 : Flag to easy filter Desktops. CHK_CreatedByEmailLogAutomator
                objAddRowSet.SetFieldVal("CHK_CREATEDBYEMAILLOGAUTOMATOR", 1, clC.SELL_SYSTEM);

                //objAddRowSet.SetFieldVal("MMO_HISTORY", PrepHistory() + " " + goP.GetUserCode + " E-mail Logged" + Constants.vbCrLf + "To: " + GetVal(dt, "To") + Constants.vbCrLf + "cc: " + GetVal(dt, "CC") + Constants.vbCrLf + "From: " + GetVal(dt, "From"));
                objAddRowSet.SetFieldVal("MMO_HISTORY", clE.PrepHistory().ToString() + " " + goP.GetUserCode() + " E-mail Logged" + Constants.vbCrLf + "To: " + clE.GetVal(dt, "To") + Constants.vbCrLf + "cc: " + clE.GetVal(dt, "CC") + Constants.vbCrLf + "From: " + clE.GetVal(dt, "From"));

                objAddRowSet.SetFieldVal("CHK_EXTDEVREVIEW", 1, clC.SELL_SYSTEM);

                objAddRowSet.SetFieldVal("LNK_From_SO", clE.GetSourceVal());
                objAddRowSet.SetFieldVal("DTE_STARTTIME", clE.GetVal(dt, "Date"));
                objAddRowSet.SetFieldVal("DTE_ENDTIME", clE.GetVal(dt, "Date"));
                objAddRowSet.SetFieldVal("TME_STARTTIME", clE.GetVal(dt, "Time"));
                objAddRowSet.SetFieldVal("TME_ENDTIME", clE.GetVal(dt, "Time"));
                objAddRowSet.SetFieldVal("LNK_CreatedBy_US", goP.gsUserID);

                if (dt.Columns.Contains("LinkRelatedField1") & dt.Columns.Contains("LinkRelatedValue1"))
                {
                    string sColumn = clE.GetVal(dt, "LinkRelatedField1");
                    string sValue = clE.GetVal(dt, "LinkRelatedValue1");

                    if (string.IsNullOrEmpty(sColumn) == false & string.IsNullOrEmpty(sValue) == false)
                    {
                        objAddRowSet.SetFieldVal(sColumn, sValue);
                        //VS 06222016
                        clE.AddEmailtoLNKRecJournal(sValue, clE.GetVal(dt, "Subject"), clE.GetVal(dt, "Body"), clE.GetVal(dt, "Attachments"));
                    }
                }

                if (dt.Columns.Contains("LinkRelatedField2") & dt.Columns.Contains("LinkRelatedValue2"))
                {
                    string sColumn = clE.GetVal(dt, "LinkRelatedField2");
                    string sValue = clE.GetVal(dt, "LinkRelatedValue2");

                    if (string.IsNullOrEmpty(sColumn) == false & string.IsNullOrEmpty(sValue) == false)
                    {
                        objAddRowSet.SetFieldVal(sColumn, sValue);
                        //VS 06222016
                        clE.AddEmailtoLNKRecJournal(sValue, clE.GetVal(dt, "Subject"), clE.GetVal(dt, "Body"), clE.GetVal(dt, "Attachments"));
                    }
                }

                if (dt.Columns.Contains("LinkRelatedField3") & dt.Columns.Contains("LinkRelatedValue3"))
                {
                    string sColumn = clE.GetVal(dt, "LinkRelatedField3");
                    string sValue = clE.GetVal(dt, "LinkRelatedValue3");

                    if (string.IsNullOrEmpty(sColumn) == false & string.IsNullOrEmpty(sValue) == false)
                    {
                        objAddRowSet.SetFieldVal(sColumn, sValue);
                        //VS 06222016
                        clE.AddEmailtoLNKRecJournal(sValue, clE.GetVal(dt, "Subject"), clE.GetVal(dt, "Body"), clE.GetVal(dt, "Attachments"));
                    }
                }

                if (dt.Columns.Contains("LinkRelatedField4") & dt.Columns.Contains("LinkRelatedValue4"))
                {
                    string sColumn = clE.GetVal(dt, "LinkRelatedField4");
                    string sValue = clE.GetVal(dt, "LinkRelatedValue4");

                    if (string.IsNullOrEmpty(sColumn) == false & string.IsNullOrEmpty(sValue) == false)
                    {
                        objAddRowSet.SetFieldVal(sColumn, sValue);
                        //VS 06222016
                        clE.AddEmailtoLNKRecJournal(sValue, clE.GetVal(dt, "Subject"), clE.GetVal(dt, "Body"), clE.GetVal(dt, "Attachments"));
                    }
                }

                //changes from 5.5 24/3/17
                string sType = clE.GetVal(dt, "Type");
                if (sType == "Sent")
                {
                    objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM);
                }
                else if (sType == "Received")
                {
                    objAddRowSet.SetFieldVal("MLS_TYPE", 5, clC.SELL_SYSTEM);
                }
                else
                {
                    objAddRowSet.SetFieldVal("MLS_TYPE", clE.GetVal(dt, "Type"), clC.SELL_FRIENDLY);
                }

                //Set CreditedTo_US based n Type
                objAddRowSet.ClearLinkAll("LNK_CREDITEDTO_US");
                if (sType == "Received")
                {
                    //Check the To User for a Selltis User Email ID
                    if (objAddRowSet.IsLinkEmpty("LNK_CREDITEDTO_US"))
                    {
                        objAddRowSet.SetFieldVal("LNK_CREDITEDTO_US", GetUserIDfromEmail(clE.GetVal(dt, "To")), 1);
                    }
                    //To not Found - Try CC Email
                    if (objAddRowSet.IsLinkEmpty("LNK_CREDITEDTO_US"))
                    {
                        objAddRowSet.SetFieldVal("LNK_CREDITEDTO_US", GetUserIDfromEmail(clE.GetVal(dt, "CC")), 1);
                    }
                    //CC not Found - Try BCC Email
                    if (objAddRowSet.IsLinkEmpty("LNK_CREDITEDTO_US"))
                    {
                        objAddRowSet.SetFieldVal("LNK_CREDITEDTO_US", GetUserIDfromEmail(clE.GetVal(dt, "BCC")), 1);
                    }
                }
                else if (sType == "Sent")
                {
                    objAddRowSet.SetFieldVal("LNK_CREDITEDTO_US", GetUserIDfromEmail(clE.GetVal(dt, "From")), 1);
                }
                //If Still Empty set Default User
                if (objAddRowSet.IsLinkEmpty("LNK_CREDITEDTO_US"))
                {
                    objAddRowSet.SetFieldVal("LNK_CREDITEDTO_US", goP.GetMe("ID"), 1);
                }

                objAddRowSet.SetFieldVal("MMO_NOTES", clE.GetVal(dt, "Subject"));
                //objAddRowSet.SetFieldVal("FIL_ATTACHMENTS", clE.GetVal(dt, "Attachments"));
                objAddRowSet.SetFieldVal("ADR_ATTACHMENTS", clE.GetVal(dt, "Attachments"));
                objAddRowSet.SetFieldVal("TXT_SUBJ", Strings.Left(clE.GetVal(dt, "Subject"), 80));
                objAddRowSet.SetFieldVal("MMO_LETTER", "From: " + clE.GetVal(dt, "From") + Constants.vbCrLf + "Received: " + clE.GetVal(dt, "Date") + " " + clE.GetVal(dt, "Time") + Constants.vbCrLf + "Subject: " + clE.GetVal(dt, "Subject") + Constants.vbCrLf + Constants.vbCrLf + clE.GetVal(dt, "Body"));

                if (clE.GetVal(dt, "Type") == "Sent")
                {
                    objAddRowSet.SetFieldVal("EML_EMAIL", clE.GetVal(dt, "To"));
                    objAddRowSet.SetFieldVal("CHK_SENT", 1, clC.SELL_SYSTEM);
                    objAddRowSet.SetFieldVal("MLS_STATUS", 1, clC.SELL_SYSTEM);

                    objAddRowSet.SetFieldVal("EML_EMAIL2", clE.GetVal(dt, "From"));
                    objAddRowSet.SetFieldVal("EML_EMAILCC", clE.GetVal(dt, "CC"));
                }
                else
                {
                    objAddRowSet.SetFieldVal("EML_EMAIL", clE.GetVal(dt, "From"));
                    //VS 02152017 : Getting value from Workgroup options
                    string sStatus = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "LOG_EMAIL_STATUS_DEFAULT", "0", false, "");
                    objAddRowSet.SetFieldVal("MLS_STATUS", sStatus, clC.SELL_SYSTEM);
                    //objAddRowSet.SetFieldVal("MLS_STATUS", 0, clC.SELL_SYSTEM)

                    objAddRowSet.SetFieldVal("EML_EMAIL2", clE.GetVal(dt, "To"));
                    objAddRowSet.SetFieldVal("EML_EMAILCC", clE.GetVal(dt, "CC"));
                }

                //VS 01102018: Calculate MLS_EmailType based on Subject start

                if (clE.GetVal(dt, "Subject").Trim().ToUpper().StartsWith("ALLIED AUTOMATION - QUOTE#"))
                {
                    objAddRowSet.SetFieldVal("MLS_EMAILTYPE", 1, 2);    //Quoted
                }
                else if (clE.GetVal(dt, "Subject").Trim().ToUpper().StartsWith("ALLIED AUTOMATION - INVOICE#"))
                {
                    objAddRowSet.SetFieldVal("MLS_EMAILTYPE", 2, 2);    //Invoiced
                }
                else if (clE.GetVal(dt, "Subject").Trim().ToUpper().StartsWith("ALLIED AUTOMATION - ACKNOWLEDGEMENT#"))
                {
                    objAddRowSet.SetFieldVal("MLS_EMAILTYPE", 3, 2);    //Acknowledged
                }
                else if (clE.GetVal(dt, "Subject").Trim().ToUpper().StartsWith("ALLIED AUTOMATION - PURCHASE ORDER#"))
                {
                    objAddRowSet.SetFieldVal("MLS_EMAILTYPE", 4, 2);    //Purchase order
                }

                //
                objAddRowSet.SetFieldVal("TXT_EmailUID", clE.GetVal(dt, "EmailUID"));


                //Email sent

                if (clE.GetVal(dt, "Type") == "Sent")
                {
                    iShare = Convert.ToInt32(goTR.StrRead(sMemo, "EMAILSHARESENT", "1"));
                    objAddRowSet.SetFieldVal("SI__SHARESTATE", iShare);


                    objAddRowSet.SetFieldVal("MMO_LETTER", "From: " + clE.GetVal(dt, "From") + Constants.vbCrLf + "Sent: " + clE.GetVal(dt, "Date") + " " + clE.GetVal(dt, "Time") + Constants.vbCrLf + "Subject: " + clE.GetVal(dt, "Subject") + Constants.vbCrLf + Constants.vbCrLf + clE.GetVal(dt, "Body"));

                    sLink = "";
                    sComp = "";
                    sCont = "";

                    sAdd = Strings.Split(clE.GetVal(dt, "To"), "; ");
                    for (l = 0; l <= Information.UBound(sAdd); l++)
                    {
                        clE.GetEmailAliasLinks(sAdd[l], ref sLink, ref sComp, ref sCont);
                        if (!string.IsNullOrEmpty(sComp))
                            objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
                        if (!string.IsNullOrEmpty(sCont))
                            objAddRowSet.SetLinkVal("LNK_Related_CN", sCont);
                    }

                    sLink = "";
                    sComp = "";
                    sCont = "";
                    sComps = "";
                    sConts = "";

                    sAdd = Strings.Split(clE.GetVal(dt, "CC"), "; ");
                    for (l = 0; l <= Information.UBound(sAdd); l++)
                    {
                        clE.GetEmailAliasLinks(sAdd[l], ref sLink, ref sComp, ref sCont);
                        if (!string.IsNullOrEmpty(sComp))
                            objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
                        if (!string.IsNullOrEmpty(sCont))
                            objAddRowSet.SetLinkVal("LNK_cc_CN", sCont);
                    }

                    sLink = "";
                    sComp = "";
                    sCont = "";
                    sComps = "";
                    sConts = "";

                    sAdd = Strings.Split(clE.GetVal(dt, "BCC"), "; ");
                    for (l = 0; l <= Information.UBound(sAdd); l++)
                    {
                        clE.GetEmailAliasLinks(sAdd[l], ref sLink, ref sComp, ref sCont);
                        if (!string.IsNullOrEmpty(sComp))
                            objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
                        if (!string.IsNullOrEmpty(sCont))
                            objAddRowSet.SetLinkVal("LNK_bc_CN", sCont);
                    }

                    //Email received

                }
                else
                {
                    iShare = Convert.ToInt32(goTR.StrRead(sMemo, "EMAILSHARERECEIVED", "1"));
                    objAddRowSet.SetFieldVal("SI__SHARESTATE", iShare);

                    objAddRowSet.SetFieldVal("MMO_LETTER", "From: " + clE.GetVal(dt, "From") + Constants.vbCrLf + "Received: " + clE.GetVal(dt, "Date") + " " + clE.GetVal(dt, "Time") + Constants.vbCrLf + "Subject: " + clE.GetVal(dt, "Subject") + Constants.vbCrLf + Constants.vbCrLf + clE.GetVal(dt, "Body"));

                    sLink = "";
                    sComp = "";
                    sCont = "";
                    clE.GetEmailAliasLinks(clE.GetVal(dt, "From"), ref sLink, ref sComp, ref sCont);
                    if (!string.IsNullOrEmpty(sLink))
                        objAddRowSet.SetLinkVal("LNK_Related_EL", sLink);
                    if (!string.IsNullOrEmpty(sCont))
                        objAddRowSet.SetLinkVal("LNK_Related_CN", sCont);
                    if (!string.IsNullOrEmpty(sComp))
                        objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);

                    //tkt #1872 new feature:To add all cc individuals and allowing to reply to all when log mail..J
                    sLink = "";
                    sComp = "";
                    sCont = "";
                    sComps = "";
                    sConts = "";
                    sAdd = Strings.Split(clE.GetVal(dt, "CC"), "; ");
                    for (l = 0; l <= Information.UBound(sAdd); l++)
                    {
                        clE.GetEmailAliasLinks(sAdd[l], ref sLink, ref sComp, ref sCont);
                        if (!string.IsNullOrEmpty(sComp))
                            objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
                        if (!string.IsNullOrEmpty(sCont))
                            objAddRowSet.SetLinkVal("LNK_cc_CN", sCont);
                    }
                }

                //return objAddRowSet.Commit().ToString();

                string sKey = clSettings.GetHostName() + "_";
                string sFieldName = "ADR_ATTACHMENTS";

                //setting tempfolder GID_ID to get attachements and save them any persistant folder
                HttpContext.Current.Session[sKey + sFieldName + "_TempId"] = GID_ID;

                int commitStatus = objAddRowSet.Commit();

                //S_B 02-16-2018 if record submission and attachments were exists for email proceeding to Saving Attachments in DB/Azure.
                if (commitStatus == 1 && !string.IsNullOrEmpty(clE.GetVal(dt, "Attachments")))
                {
                    clAttachments _clAttachments = new clAttachments();
                    _clAttachments.SaveAttachments("ADR_ATTACHMENTS", clE.GetVal(dt, "Attachments"), objAddRowSet.GetFieldVal("GID_ID", 1).ToString(), "AC");
                }

                return commitStatus.ToString();

           // }
            //catch (Exception ex)
            //{
            //    //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    //{
            //    //    goErr.SetError(ex, 45105, sProc);
            //    //}
            //    if (ex.GetType().ToString() != "System.Threading.ThreadAbortException" && ex.GetType().ToString() != "System.IO.IOException")
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}
            return functionReturnValue;

        }

        private string GetUserIDfromEmail(string sEmails)
        {
            if (!string.IsNullOrEmpty(sEmails.Trim()))
            {
                //Return the first user found from list of Emails
                foreach (string emlStr in Strings.Split(sEmails, ";"))
                {
                    //clRowSet doUSRS = new clRowSet("US", clC.SELL_READONLY, "EML_EMAIL='" + emlStr.Trim() + "'", "CHK_ActiveField DESC, CHK_ONNETWORK DESC", "", 1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 1800);
                    //if (doUSRS.GetFirst() == 1)
                    //{
                    //    return doUSRS.GetFieldVal("GID_ID").ToString();
                    //} 
                    clRowSet doELRS = new clRowSet("EL", clC.SELL_READONLY, "TXT_EMAILADDRESS='" + goTR.PrepareForSQL(PrepareEmails(emlStr.Trim())) + "' AND LNK_RELATED_US%%BI__ID>0", "", "LNK_RELATED_US", 1, "", "", "", "", "", false, false, false, false, 1, "", false, false, 1800);
                    if (doELRS.GetFirst() == 1)
                    {
                        return doELRS.GetFieldVal("LNK_RELATED_US", 1).ToString();
                    }
                }
            }
            return "";
        }

        public string SaveAttachementsAndGetAttachmentsBody(UserMailSettings currUsermailsettings, string GID_ID, string Uid, string sFolder)
        {
            string sPath = System.Web.HttpContext.Current.Server.MapPath("~/Temp/");

            string AttachmentsPath = ((DataTable)Util.GetSessionValue("SiteSettings")).Rows[0]["AttachmentsPath"].ToString();
            string AttachmentsStorageType = System.Web.Configuration.WebConfigurationManager.AppSettings["AttachmentsStorageType"].ToString();
            //string sAttachmentsPath = AttachmentsPath + "AC" + "\\" + GID_ID + "\\" + "ADR_ATTACHMENTS";
            string sAttachmentsPath = sPath + "AC" + "\\" + GID_ID + "\\" + "ADR_ATTACHMENTS";

            //UserMailSettings currUsermailsettings = GetUserWebMailSettings();

            string EmailUsername = currUsermailsettings.UserName;
            string EmailPwd = currUsermailsettings.Password;
            string serverName = currUsermailsettings.ServerName;
            int portNumber = currUsermailsettings.PortNumber;

            if (!Directory.Exists(sAttachmentsPath))
            {
                Directory.CreateDirectory(sAttachmentsPath);
            }

            //string sMailBeeLicense = "MN800-DE16E9C316B2167C1690076A02C4-FF98";
            //string sMailBeeLicense = "MN110-6FA7A7A0A725A7B6A7E82EE2BE79-9F18"; //trial license.. 
            string sMailBeeLicense = "MN110-D21AE5AB1BB11AAA1A6F90390211-94CA"; //trial license.. 

            MailBee.ImapMail.Imap imp = new MailBee.ImapMail.Imap(sMailBeeLicense);
            MailBee.Mime.MailMessage msg = new MailBee.Mime.MailMessage();

            if (portNumber.ToString() == "0")
            {
                imp.Connect(serverName);
            }
            else
            {
                imp.Connect(serverName, portNumber);
            }

            imp.Login(EmailUsername, EmailPwd);
            imp.SelectFolder(sFolder);
            msg = imp.DownloadEntireMessage(Convert.ToInt64(Uid), true);
            imp.Disconnect();

            string htmlBody = "";

            if (msg != null)
            {
                msg.Parser.PlainToHtmlMode = MailBee.Mime.PlainToHtmlAutoConvert.IfNoHtml;

                string sAttachmentsTempPath = sAttachmentsPath;
                msg.Parser.WorkingFolder = sAttachmentsTempPath;
                //htmlBody = msg.GetHtmlAndSaveRelatedFiles(sAttachmentsTempPath, MailBee.Mime.VirtualMappingType.NonWeb, MailBee.Mime.MessageFolderBehavior.CreateAndDelete);

                htmlBody = msg.GetHtmlWithBase64EncodedRelatedFiles();
                //string htmlBase64Body = msg.GetHtmlAndSaveRelatedFiles();                

                if (AttachmentsStorageType == "Server")
                {
                    //For every attachment...
                    foreach (MailBee.Mime.Attachment attach in msg.Attachments)
                    {
                        //Save it to selltispublic files folder where we save attachements..J
                        attach.SaveToFolder(sAttachmentsTempPath, true);
                    }
                }
                else
                {
                    //For every attachment...
                    foreach (MailBee.Mime.Attachment attach in msg.Attachments)
                    {
                        attach.SaveToFolder(sAttachmentsTempPath, true);

                        Stream fileStream = File.OpenRead(sAttachmentsTempPath + "\\" + attach.Filename);

                        //Save it to selltispublic files folder where we save attachements..J
                        var FinalFolder = clAzureFileStorage.GetFinalFolder("AC", GID_ID, "ADR_ATTACHMENTS", "", false);
                        //Microsoft.WindowsAzure.Storage.File.CloudFile cloudFile = FinalFolder.GetFileReference(attach.Filename);

                        clAzureFileStorage.UploadFromStream(FinalFolder, fileStream, attach.Filename);

                        fileStream.Flush();
                        fileStream.Close();

                        File.Delete(sAttachmentsTempPath + "/" + attach.Filename);
                    }
                }
            }

            return htmlBody;

        }

        #endregion

        //Update Sales Statistics Monthly on CO, VE, PD, US
        public bool UpdateSalesStats_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Rowset object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //PURPOSE
            //VS 12152017 : Update Invoicing Statistics on CO, VE, PD, US every Month.
            //AGE_3c35a741-4edb-4955-5858-a85d00ad4797

            //VS 01042017 : Running this everyday from now

            clRowSet doRS;
            clRowSet TrialRS;
            string sResult;
            Boolean bMore = true;
            string sCurrentUser = "";
            PublicDomain.TzTimeZone zone;
            DateTime dtUsersToday;
            DateTime dtDateTime;
            String sDateTime;
            String sPointers;
            String sPointerDateTime;
            DateTime dtPointerDate;
            int iPointerYear;
            int iPointerMonth;
            int iUsersYear;
            int iUsersMonth;

            sPointers = goMeta.PageRead("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED");

            sCurrentUser = goP.GetMe("ID");
            zone = goTR.UTC_GetUserTimeZone(sCurrentUser);

            //Get user's 'now' and 'today at midnight' in the user's time zone
            dtUsersToday = zone.ToLocalTime(goTR.NowUTC()).Date;       //Midnight on user's current date in his/her time zone

            //Get the 'last processed' date (no time) as a local datetime
            //Pointers are written as local datetimes in user's last login time zone.
            sPointerDateTime = Strings.Left(goTR.StrRead(sPointers, "UpdateSalesStats_LastRunDate", "", false), 10);

            int par_iValid = clC.SELL_TYPE_INVALID;
            if (sPointerDateTime == "")
            {
                //Leaving one whole day ahead of 'blank' datetime: '1753-01-02 23:59:59.000'.
                dtPointerDate = goTR.StringToDate("1753-01-04", "", ref par_iValid);
            }
            else
            {
                dtPointerDate = goTR.StringToDate(sPointerDateTime, clC.SELL_FORMAT_DATEDEF, ref par_iValid).Date;
            }

            dtPointerDate = DateTime.SpecifyKind(dtPointerDate, DateTimeKind.Local);

            iPointerYear = dtPointerDate.Year;
            iPointerMonth = dtPointerDate.Month;
            iUsersYear = dtUsersToday.Year;
            iUsersMonth = dtUsersToday.Month;

            //Run only when Next Month - Either User's Year is greated OR user's Year is same and Month is Greater
            //Next Month or Next Year
            //if (iUsersYear > iPointerYear || (iUsersYear == iPointerYear && iUsersMonth > iPointerMonth))
            //Now running Daily
            if (dtPointerDate < dtUsersToday)
            {
                //Doing this through SQL will dotNet will be very Resource Intensive and Time consuming
                //Let's do this with a Stored Proc
                string sSProc = "Cus_UpdateSalesStats_All";
                goData.RunSQLQuery(sSProc);

                //Update Metadata Pointer
                string par_sdelim = "|";
                par_iValid = clC.SELL_TYPE_INVALID;
                System.Data.SqlClient.SqlConnection par_oConnection = null;
                goMeta.LineWrite("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED", "UpdateSalesStats_LastRunDate", goTR.DateTimeToSysString(dtUsersToday, ref par_iValid, ref par_sdelim), ref par_oConnection);

            }

            par_bRunNext = false;

            return true;
        }

        public bool MergeRecord_Pre(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 03162018 : Merge added

            //TLD 8/13/2012 Added for merge
            par_bRunNext = false;

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;
            //Record being merged, will be deactivated
            clRowSet doRSMergeTo = default(clRowSet);
            //Good record, stays active

            clArray aFields = default(clArray);
            clArray aLinks = default(clArray);
            string sField = null;
            string sFieldType = null;
            clArray doLink = new clArray();
            string[] sLinkType = null;
            string sReturn = "";

            DataTable oTable = null;

            try
            {
                //Enumerate schema
                //aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                //aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                //Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName()) + "'", "", "**", -1, "", "", "", "", "", true, true, false, false, -1, "", true);

                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                if (string.IsNullOrEmpty(doRSMergeTo.GetFieldVal(sField).ToString()))
                                {
                                    doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                }
                                break;
                            case "MMO":
                                //Append
                                if (string.IsNullOrEmpty(doRSMergeTo.GetFieldVal(sField).ToString()))
                                {
                                    doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                }
                                else
                                {
                                    doRSMergeTo.SetFieldVal(sField, doRSMergeTo.GetFieldVal(sField) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + doRSMerge.GetFieldVal("SYS_Name") + " ==" + Constants.vbCrLf + doRSMerge.GetFieldVal(sField));
                                }
                                break;
                            case "CHK":
                                if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                {
                                    doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                }
                                break;
                            case "MLS":
                                if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                {
                                    doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                }
                                break;
                        }

                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        //If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9).ToString());
                        //NN OR Direction = 2 (backside of N1 or NN link should copy all)
                        if (sLinkType[4] == "NN" | Convert.ToInt32(sLinkType[1]) == 2)
                        {
                            oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else
                        {
                            if (string.IsNullOrEmpty(doRSMergeTo.GetFieldVal(aLinks.GetItem(i)).ToString()))
                            {
                                oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
                                doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                                doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                            }
                        }
                    }

                    //Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    //Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);
                    //Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    //Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    //Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();

                    par_doCallingObject = doRSMerge;

                }

                sReturn = "Success";


            }
            catch (Exception ex)
            {
                sReturn = "Failed";

            }

            par_oReturn = sReturn;

            return true;

        }

        //SB Modifying email text when special chars exists to avoid errors while performing record insertion
        public string PrepareEmails(string sEmail)
        {
            if (sEmail.Length > 0)
            {
                switch (sEmail.Substring(0, 1))
                {
                    case "'":
                    case "\"":
                        sEmail = sEmail.Substring(1, sEmail.Length - 1);
                        break;
                    default:
                        break;
                }
                switch (sEmail.Substring(sEmail.Length - 1, 1))
                {
                    case "'":
                    case "\"":
                        sEmail = sEmail.Substring(0, sEmail.Length - 1);
                        break;
                    default:
                        break;
                }
            }

            return sEmail;
        }

        public bool XW_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                return false;
            }

            //TLD 8/24/2009 Updated to read Product XX
            //Should only be 1 stored set of data here
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "XX");

            //Read values from MD and set in the form fields
            doForm.doRS.SetFieldVal("CUR_MILEAGERATE", Convert.ToDecimal(goTR.StrRead(sWOP, "EXPENSE_MILEAGERATE")), 2);
            doForm.doRS.SetFieldVal("CUR_MILEAGERATEMANAGEMENT", Convert.ToDecimal(goTR.StrRead(sWOP, "EXPENSE_MILEAGERATEMANAGEMENT")), 2);

            par_doCallingObject = doForm;
            return true;

        }
        public bool XW_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                return false;
            }

            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "XX");

            //Write values to MD
            goTR.StrWrite(ref sWOP, "EXPENSE_MILEAGERATE", doForm.doRS.GetFieldVal("CUR_MILEAGERATE", 2).ToString());
            goTR.StrWrite(ref sWOP, "EXPENSE_MILEAGERATEMANAGEMENT", doForm.doRS.GetFieldVal("CUR_MILEAGERATEMANAGEMENT", 2).ToString());

            goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP, "", "", "XX");

            doForm.CloseOnReturn = true;
            doForm.CancelSave();
            par_doCallingObject = doForm;
            return true;

        }

        //Expense Module
        #region ExpenseModule

        public bool EX_FormControlOnChange_SR__ODOMETERSTART_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            scriptManager.RunScript("EX_CalcOdometerMiles", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            par_bRunNext = false;

            par_doCallingObject = doForm;
            return true;
        }
        public bool EX_FormControlOnChange_SR__ODOMETEREND_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            scriptManager.RunScript("EX_CalcOdometerMiles", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            par_bRunNext = false;

            par_doCallingObject = doForm;
            return true;
        }
        public bool EX_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            bool bRunNextTemp = true;
            double rMileageRate = 0;

            //Set mileage rate from Workgroup Options if necessary
            //rMileageRate = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_MILEAGERATE", 2));
            //if (rMileageRate == 0)
            //{
            bRunNextTemp = true;
            object oTempCallingObject = doForm.doRS;
            scriptManager.RunScript("EX_SetMileageRate", ref oTempCallingObject, ref par_oReturn, ref bRunNextTemp, ref par_sSections);
            doForm.doRS = (clRowSet)oTempCallingObject;
            //}

            //Set mandatory field color
            string sColor = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("ADR_ATTACHMENTS", "LABELCOLOR", sColor);

            par_doCallingObject = doForm;
            return true;

        }
        public bool EX_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Write new custom code here

            par_doCallingObject = doForm;
            scriptManager.RunScript("EX_FilterOppsbyCompanies", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            doForm = (Form)par_doCallingObject;

            //Write new custom code here

            par_doCallingObject = doForm;
            return true;

        }
        public bool EX_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            //Dim doRowSet As Object
            int iTypeVal = 0;
            string sDate = null;
            //Dim lActionNo As Long
            string sRecID = doForm.GetRecordID();

            //Want to abort the save after the user answered answered that way on a MessageBoxEvent 
            if (doForm.oVar.GetVar("CancelSave").ToString() == "1")
            {
                //Reset variables so that save will run next time it is clicked.
                doForm.oVar.SetVar("EX_FormOnSave_Personal", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }

            ////--------------- ENFORCE -----------------
            //iTypeVal = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Type", 2));

            if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS").ToString().Trim()))
            {
                doForm.MoveToField("ADR_ATTACHMENTS");
                goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("ADR_ATTACHMENTS"), "", "", "", "", "", "", "", "", "ADR_ATTACHMENTS");
                return false;
            }

            bool bIsAutoMileage = false;
            if (doForm.doRS.GetFieldVal("LNK_RELATED_EC").ToString() == "92dde5e3-1bbb-4582-4543-98dd01419093")    //Auto-Mileage
                bIsAutoMileage = true;

            //CS: 7/6/07 In MD
            //Date not valid
            sDate = doForm.doRS.GetFieldVal("DTE_TIME", 2).ToString();

            //Full Amount not valid
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceAmount"))
            {
                //if (iTypeVal == 1 | iTypeVal == 2)
                if (!bIsAutoMileage)
                {
                    if (goTR.IsNumber(doForm.doRS.GetFieldVal("CUR_AMOUNT", 2).ToString()) != true | Convert.ToInt32(doForm.doRS.GetFieldVal("CUR_Amount", 2)) == 0)
                    {
                        doForm.MoveToField("CUR_AMOUNT");
                        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("EX", "CUR_AMount"), "", "", "", "", "", "", "", "", "CUR_Amount")
                        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_AMount"), "", "", "", "", "", "", "", "", "CUR_Amount");
                        return false;
                    }
                }
            }

            //Moved to RecordOnSave
            ////Set links
            //scriptManager.RunScript("Expense_SetConnections", ref par_doCallingObject, ref  par_oReturn, ref  par_bRunNext, ref  par_sSections);

            //------- Enforce links --------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceVendorLink"))
            {
                //No Vendor connected
                //goP.TraceLine("Vendor count (should be 0): '" & doForm.GetLinkCountEx("LNK_TO_VE") & "'", "", sProc)
                if (doForm.doRS.GetLinkCount("LNK_TO_VE") == 0)
                {
                    //goP.TraceLine("Type: (should be 3 or 4)'" & iTypeVal & "'", "", sProc)
                    switch (iTypeVal)
                    {
                        case 3:
                        case 4:
                            //Type is 'Mileage', links were set in SetConnections
                            break;
                        default:
                            doForm.MoveToField("LNK_TO_VE");
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_TO_VE"), "", "", "", "", "", "", "", "", "LNK_TO_VE");
                            return false;
                    }
                }
            }

            //No Expense Category connected
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceExpenseCategory"))
            {
                if (doForm.doRS.GetLinkCount("LNK_RELATED_EC") < 1)
                {
                    switch (doForm.doRS.GetFieldVal("LNK_RELATED_EC").ToString())
                    {
                        case "92dde5e3-1bbb-4582-4543-98dd01419093":
                            //Type is 'Mileage', links were set in SetConnections
                            break;
                        //goP.TraceLine("Type is mileage", "", sProc)
                        default:
                            //Make the user select the Expense Category
                            doForm.MoveToField("LNK_RELATED_EC");
                            //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("EX", "LNK_Related_EC"), "", "", "", "", "", "", "", "", "LNK_Related_EC")
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_Related_EC"), "", "", "", "", "", "",
                            "", "", "LNK_Related_EC");
                            return (false);
                    }
                }
            }

            //if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceTypeAndAccountMatch"))
            //{
            //    //Personal account can't be selected when Type is 'Company _'
            //    switch (iTypeVal)
            //    {
            //        case 2:
            //        case 4:
            //            if (doForm.doRS.GetLinkCount("LNK_RELATED_EA") > 0)
            //            {
            //                if (doForm.doRS.GetFieldVal("LNK_RELATED_EA%%CHK_PERSONAL", 1).ToString() == "Checked")
            //                {
            //                    doForm.MoveToField("LNK_RELATED_EA");
            //                    goErr.SetWarning(30200, sProc, "", "You can't select a personal account for a company expense. Either select a company account or select a 'Personal...' type in the 'Type' field.", "", "", "", "", "", "", "", "", "LNK_Related_EA");
            //                    return (false);
            //                }
            //            }
            //            break;
            //    }

            //    //Company account can't be selected when Type is 'Personal ...'
            //    switch (iTypeVal)
            //    {
            //        case 1:
            //        case 3:
            //            if (doForm.doRS.GetLinkCount("LNK_RELATED_EA") > 0)
            //            {
            //                if (doForm.doRS.GetFieldVal("LNK_RELATED_EA%%CHK_PERSONAL", 1).ToString() == "Unchecked")
            //                {
            //                    doForm.MoveToField("LNK_RELATED_EA");
            //                    goErr.SetWarning(30200, sProc, "", "You can't select a company account for a personal expense. Either select a personal account or select a 'Company...' type in the 'Type' field.", "", "", "", "", "", "", "", "", "LNK_Related_EA");
            //                    return (false);
            //                }
            //            }
            //            break;
            //    }
            //}


            //Bill Company not connected when Expense is billable
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceBillCompanyWhenBillable"))
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_BILLABLE", 2)) == 1)
                {
                    if (doForm.doRS.GetLinkCount("LNK_BILL_CO") < 1)
                    {
                        doForm.MoveToTab(2);
                        doForm.MoveToField("LNK_BILL_CO");
                        par_doCallingObject = doForm;
                        scriptManager.RunScript("Expense_FillBillingNotes", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                        goErr.SetWarning(30200, sProc, "", "Select the company to bill in the 'Bill Company' field and check the rest of the billing information on the 'Billing' tab.", "", "", "", "", "", "", "", "", "LNK_Bill_CO");
                        return (false);
                    }
                }
            }

            //if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceTypeReimbursableMatch"))
            //{
            //    //Company expense marked as reimbursable
            //    if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_REIMBURSABLE", 2)) == 1)
            //    {
            //        switch (iTypeVal)
            //        {
            //            case 2:
            //            case 4:
            //                //This is done here because ControlOnEnter doesn't fire w/MoveToField()
            //                doForm.oVar.SetVar("bReimbursableValue", 1);
            //                doForm.MoveToField("CHK_REIMBURSABLE");
            //                goErr.SetWarning(30200, sProc, "", "You can't make a 'Company...' type expense reimbursable." + System.Environment.NewLine + System.Environment.NewLine + "Either un-check the 'Reimbursable' checkbox or select a 'Personal...' type in the 'Type' field.", "", "", "", "", "", "", "", "", "LNK_Related_EA");
            //                return (false);
            //        }
            //    }
            //    else
            //    {
            //        //Personal expense not marked as reimbursable
            //        if (doForm.doRS.GetFieldVal("LNK_RELATED_EA%%CHK_Personal", 1).ToString() == "Checked" & doForm.oVar.GetVar("EX_FormONSave_Personal").ToString() != "1")
            //        {
            //            return true;
            //        }
            //    }
            //}

            //------------- FILL NAME FUNCTION --------------
            //Fill Day, Month and Year fields (IsDate is checked above)
            //Now run in RecordOnSave script
            //RunScript("FillYearMonthDay",doForm,NULL,sDate)

            //Fill Billing Notes
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillBillingNotes"))
            {
                scriptManager.RunScript("Expense_FillBillingNotes", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            }

            //Connect 'Bill Company' in 'Related Company' if Expense if billable
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "ConnectBillCompanyAsRelatedCompanyIfBillable"))
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_BILLABLE", 2)) == 1)
                {
                    doForm.doRS.SetFieldVal("LNK_RELATED_CO", doForm.doRS.GetFieldVal("LNK_BILL_CO", 2), 2);
                }
            }

            //Moved to RecordOnSave to accomodate Mobile APP save
            //if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CalcAmount"))
            //{
            //    scriptManager.RunScript("Expense_CalcAmount", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            //}

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "SetReimbursable"))
            {
                scriptManager.RunScript("Expense_SetReimbursable", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            }

            doForm.oVar.SetVar("EX_FormOnSave_Personal", "");

            par_bRunNext = false;

            par_doCallingObject = doForm;
            return (true);

        }
        public bool EX_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Rowset object containing the record to be saved.
            //par_doArray: Unused.
            //par_sMode: 'CREATION' or 'MODIF'.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //goP.TraceLine("", "", sProc)

            clRowSet doRS = default(clRowSet);
            clArray doLink = default(clArray);
            string sFileName = "EX";
            string sSysName = "";
            DateTime dtDateTime = default(DateTime);
            bool bRunNextTemp = true;

            doRS = (clRowSet)par_doCallingObject;

            if (doRS.bBypassValidation != true)
            {
                //'Connect 'Bill Company' in 'Related Company' if Expense if billable

            }

            bRunNextTemp = true;
            par_doCallingObject = doRS;
            scriptManager.RunScript("EX_SetMileageRate", ref par_doCallingObject, ref par_oReturn, ref bRunNextTemp, ref par_sSections);
            doRS = (clRowSet)par_doCallingObject;

            //New, UTC-aware code
            dtDateTime = Convert.ToDateTime(doRS.GetFieldVal("DTT_TIME", 2).ToString());
            doRS.SetFieldVal("TXT_YEAR", goTR.GetYear(dtDateTime));
            doRS.SetFieldVal("SI__MONTH", goTR.GetMonth(dtDateTime));
            doRS.SetFieldVal("SI__DAY", goTR.GetDay(dtDateTime));

            if (goP.GetRunMode() != "Import")
            {
                //LNK_RELATED_COMPANY - fill with 'Bill Company' only if billable
                if (Convert.ToInt32(doRS.GetFieldVal("CHK_BILLABLE", 2)) == 1)
                {
                    System.Data.DataTable oTable = new System.Data.DataTable();
                    doLink = new clArray();
                    oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
                    doLink = doRS.GetLinkVal("LNK_BILL_CO", ref doLink, false, 0, -1, "A_a", ref oTable);
                    //If doLink <> vbNull Then
                    doRS.SetLinkVal("LNK_RELATED_CO", doLink);
                    //delete(doLink)
                    doLink = null;
                    //End If
                }
            }

            doRS.SetFieldVal("SR__MILESDRIVEN", (Convert.ToDouble(doRS.GetFieldVal("SR__ODOMETEREND", 2)) - Convert.ToDouble(doRS.GetFieldVal("SR__ODOMETERSTART", 2))), 2);
            //par_doCallingObject = doRS;
            //scriptManager.RunScript("EX_CalcOdometerMiles", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            //doRS = (clRowSet)par_doCallingObject;

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CalcAmount"))
            {
                bRunNextTemp = true;
                par_doCallingObject = doRS;
                scriptManager.RunScript("Expense_CalcAmount", ref par_doCallingObject, ref par_oReturn, ref bRunNextTemp, ref par_sSections);
                doRS = (clRowSet)par_doCallingObject;
            }

            bRunNextTemp = true;
            par_doCallingObject = doRS;
            scriptManager.RunScript("Expense_SetConnections", ref par_doCallingObject, ref  par_oReturn, ref  bRunNextTemp, ref  par_sSections);
            doRS = (clRowSet)par_doCallingObject;

            par_bRunNext = false;

            par_doCallingObject = doRS;
            return true;

        }
        public bool EX_FormControlOnChange_MLS_TYPE_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            return true;
        }
        public bool EX_FormControlOnChange_LNK_RELATED_EC_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            bool bTempRunNext = true;

            scriptManager.RunScript("Expense_ManageControlState", ref par_doCallingObject, ref  par_oReturn, ref  bTempRunNext, ref  par_sSections);
            //scriptManager.RunScript("Expense_CalcAmount", ref par_doCallingObject, ref  par_oReturn, ref  par_bRunNext, ref  par_sSections);
            bTempRunNext = true;
            doForm = (Form)par_doCallingObject;
            object oTempCallingObject = doForm.doRS;
            scriptManager.RunScript("Expense_CalcAmount", ref oTempCallingObject, ref  par_oReturn, ref  bTempRunNext, ref  par_sSections);
            doForm.doRS = (clRowSet)oTempCallingObject;
            par_doCallingObject = doForm;
            //CS, per MI don't set reimb when change type. scriptManager.RunScript("Expense_SetReimbursable", doForm)
            bTempRunNext = true;
            oTempCallingObject = doForm.doRS;
            scriptManager.RunScript("Expense_SetConnections", ref oTempCallingObject, ref  par_oReturn, ref  bTempRunNext, ref  par_sSections);
            doForm.doRS = (clRowSet)oTempCallingObject;

            par_doCallingObject = doForm;
            return true;
        }

        public bool EX_FormControlOnChange_LNK_RELATED_CN_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Write new custom code here

            par_doCallingObject = doForm;
            scriptManager.RunScript("EX_FilterOppsbyCompanies", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "LNK_RELATED_CN", "LNK_RELATED_CO", "SILENT");
            doForm = (Form)par_doCallingObject;

            //Write new custom code here

            par_doCallingObject = doForm;

            return true;
        }
        public bool EX_FormControlOnChange_LNK_RELATED_CO_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Write new custom code here

            par_doCallingObject = doForm;
            scriptManager.RunScript("EX_FilterOppsbyCompanies", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "LNK_RELATED_CN", "LNK_RELATED_CO", "SILENT");
            doForm = (Form)par_doCallingObject;

            //Write new custom code here

            par_doCallingObject = doForm;

            return true;
        }
        public bool EX_FilterOppsbyCompanies_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.IsLinkEmpty("LNK_RELATED_CO"))
            {
                return true;
            }

            string sIniString = "";
            string sCondition = "";
            string sCompany = "";
            long iCompCount = 0;
            clArray doCompanies = (clArray)doForm.doRS.GetFieldVal("LNK_RELATED_CO", 2);
            iCompCount = doCompanies.GetDimension();

            for (int i = 1; i <= iCompCount; i++)
            {
                sCompany = doCompanies.GetItem(i);

                if (string.IsNullOrEmpty(sCondition))
                    sCondition = "LNK_FOR_CO='" + sCompany + "'";
                else
                    sCondition = sCondition + " OR LNK_FOR_CO='" + sCompany + "'";

                goTR.StrWrite(ref sIniString, "C" + i.ToString() + "CONDITION", "0");
                goTR.StrWrite(ref sIniString, "C" + i.ToString() + "FIELDNAME", "<%LNK_FOR_CO%>");
                goTR.StrWrite(ref sIniString, "C" + i.ToString() + "VALUE1", sCompany);

                if (i < iCompCount)
                    goTR.StrWrite(ref sIniString, "C" + i.ToString() + "KEYWORD", "OR");
            }
            goTR.StrWrite(ref sIniString, "ACTIVE", "1");
            goTR.StrWrite(ref sIniString, "FILE", "OP");
            goTR.StrWrite(ref sIniString, "CONDITION", sCondition);
            goTR.StrWrite(ref sIniString, "CCOUNT", iCompCount.ToString());
            goTR.StrWrite(ref sIniString, "SORT", "LNK_FOR_CO%%TXT_COMPANYNAME ASC,DTT_TIME DESC");
            goTR.StrWrite(ref sIniString, "SECTIONSGROUPED", "0");
            goTR.StrWrite(ref sIniString, "SORT1", "LNK_FOR_CO%%TXT_COMPANYNAME");
            goTR.StrWrite(ref sIniString, "DIRECTION1", "1");
            goTR.StrWrite(ref sIniString, "SORT2", "DTT_TIME");
            goTR.StrWrite(ref sIniString, "DIRECTION2", "2");

            doForm.SetFilterINI("LNK_RELATED_OP", sIniString);

            par_doCallingObject = doForm;
            return true;
        }
        public bool EX_CalcOdometerMiles_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            double OdStart = 0;
            double OdEnd = 0;

            OdStart = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__ODOMETERSTART", 2));
            OdEnd = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__ODOMETEREND", 2));

            if (OdStart < 0 || OdEnd < 0)
            {
                doForm.MessageBox("Odometer reading cannot be less than 0");
                return false;
            }

            if (OdEnd < OdStart)
            {
                doForm.MessageBox("Od End should be more than Od Start");
                return false;
            }

            doForm.doRS.SetFieldVal("SR__MILESDRIVEN", OdEnd - OdStart, 2);

            par_bRunNext = false;

            par_doCallingObject = doForm;
            return true;
        }
        public bool EX_SetMileageRate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //Form doForm = (Form)par_doCallingObject;
            clRowSet doRS = (clRowSet)par_doCallingObject;

            decimal rMileageRate = 0;
            string sMileageRate = "";

            string sUser = doRS.GetFieldVal("LNK_FOR_US").ToString();
            bool bUSisManagement = false;
            clRowSet doUSRS = new clRowSet("US", clC.SELL_READONLY, "GID_ID=" + sUser, "", "LNK_RELATED_GR");
            clArray doGroups = (clArray)doUSRS.GetFieldVal("LNK_RELATED_GR", 2);

            for (int i = 1; i <= doGroups.GetDimension(); i++)
            {
                if (doGroups.GetItem(i) == "39343863-6165-3761-4752-332f32312f32")   //Management Mileage
                {
                    bUSisManagement = true;
                    break;
                }
            }

            if (bUSisManagement)
            {
                sMileageRate = doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%EXPENSE_MILEAGERATEMANAGEMENT").ToString();
            }
            else
            {
                sMileageRate = doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%EXPENSE_MILEAGERATE").ToString();
            }

            if (!string.IsNullOrEmpty(sMileageRate))
            {
                rMileageRate = Convert.ToDecimal(sMileageRate);
            }
            doRS.SetFieldVal("CUR_MILEAGERATE", rMileageRate, 2);

            par_doCallingObject = doRS;
            return true;

        }
        public bool Expense_ManageControlState_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            int iTypeVal = 0;

            //Grayed fields
            doForm.SetControlState("MMO_ImportData", 4);

            //VS 03232018 - Set field states on Category change "Auto-Mileage" instead of Type change
            //iTypeVal = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_TYPE", 2));

            ////--------- Switch between payment vs mileage modes -----------
            //switch (iTypeVal)
            //{
            //    case 3:
            //    case 4:
            //        //"Personal Vehicle Mileage", "Company Vehicle Mileage"

            //        doForm.SetFieldProperties("LNK_TO_VE", 2);
            //        doForm.SetControlState("SR__MILESDRIVEN", 0);
            //        doForm.SetControlState("CUR_MILEAGERATE", 0);
            //        doForm.SetControlState("SR__ODOMETERSTART", 0);
            //        doForm.SetControlState("SR__ODOMETEREND", 0);
            //        break;
            //    default:
            //        //Payment

            //        doForm.SetControlState("SR__MILESDRIVEN", 2);
            //        doForm.SetControlState("CUR_MILEAGERATE", 2);
            //        doForm.SetControlState("SR__ODOMETERSTART", 2);
            //        doForm.SetControlState("SR__ODOMETEREND", 2);
            //        doForm.SetFieldProperties("LNK_TO_VE", 0);
            //        break;
            //}
            switch (doForm.doRS.GetFieldVal("LNK_RELATED_EC").ToString())
            {
                case "92dde5e3-1bbb-4582-4543-98dd01419093":
                    //"Auto-Mileage"

                    doForm.SetFieldProperties("LNK_TO_VE", 2);
                    doForm.SetControlState("SR__MILESDRIVEN", 0);
                    doForm.SetControlState("CUR_MILEAGERATE", 0);
                    doForm.SetControlState("SR__ODOMETERSTART", 0);
                    doForm.SetControlState("SR__ODOMETEREND", 0);
                    break;
                default:
                    //Payment

                    doForm.SetControlState("SR__MILESDRIVEN", 2);
                    doForm.SetControlState("CUR_MILEAGERATE", 2);
                    doForm.SetControlState("SR__ODOMETERSTART", 2);
                    doForm.SetControlState("SR__ODOMETEREND", 2);
                    doForm.SetFieldProperties("LNK_TO_VE", 0);
                    break;
            }

            //Check if Save button is enabled...if not, disable Save & Create Linked
            if (doForm.SaveEnabled() == false)
            {
                doForm.SetControlState("BTN_SAVECRU", 4);
            }

            //par_bRunNext = false;

            par_doCallingObject = doForm;
            return true;

        }
        public bool Expense_SetConnections_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: do
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //PURPOSE:
            //		Sets or clears connections depending on the 'Type' selected.
            //RETURNS:
            //		True.

            //VS 03302018 : Changing from Form to Rowset
            //Form doForm = (Form)par_doCallingObject;
            clRowSet doRS = (clRowSet)par_doCallingObject;
            clRowSet doSource = default(clRowSet);
            //Dim sID As String

            //int iTypeVal = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_TYPE", 2));

            //VS 03232018 - Set field states on Category change "Auto-Mileage" instead of Type change

            //switch (iTypeVal)
            //{
            //    case 0:
            //        //make selection
            //        return true;
            //    case 3:
            //        //"Personal Vehicle Mileage"
            //        doForm.doRS.ClearLinkAll("LNK_TO_VE");
            //        //doForm.doRS.SetLinkByVal("LNK_TO_VE", "VENDORNAME", "MILEAGE")
            //        doSource = new clRowSet("VE", 3, "TXT_VendorName='Mileage'", "", "GID_ID");
            //        if (doSource.GetFirst() == 1)
            //        {
            //            doForm.doRS.SetFieldVal("LNK_TO_VE", doSource.GetCurrentRecID());
            //        }
            //        //		'Old code, here just for reference		
            //        //		doRowSet as object  = new clRowset ("VENDOR", 1,_
            //        //							"_TUP_VENDORNAME='MILEAGE'", _
            //        //							"VENDORNAME a", _
            //        //							"SYS_ID")
            //        //		TraceLine("Instantiated a Vendor rowset","",sProc)
            //        //		If doRowSet.GetFirst() THEN
            //        //			TraceLine("GetFirst is true, about to SetFieldVal","",sProc)
            //        //			TraceLine("Rowset GetFieldVal of SYS_ID: '" & doRowSet.GetFieldVal("SYS_ID",2) & "'","",sProc)
            //        //			doForm.SetFieldVal("LNK_TO_VENDOR",doRowSet.GetFieldVal("SYS_ID",2))
            //        //			TraceLine("Value was set in the link","",sProc)
            //        //		End If
            //        //		delete doRowSet			
            //        //		TraceLine("RowSet deleted","",sProc)
            //        doForm.doRS.ClearLinkAll("LNK_RELATED_EC");
            //        //doForm.doRs.SetLinkByVal("LNK_RELATED_EC", "EXPCATNAME", "AUTOMILEAGE")
            //        doSource = new clRowSet("EC", 3, "TXT_ExpCatName='Auto-Mileage'", "", "GID_ID");
            //        if (doSource.GetFirst() == 1)
            //        {
            //            doForm.doRS.SetFieldVal("LNK_RELATED_EC", doSource.GetCurrentRecID());
            //        }
            //        doForm.doRS.ClearLinkAll("LNK_RELATED_EA");
            //        doSource = new clRowSet("EA", 3, "TXT_ExpAcctName='Personal Vehicle Mileage'", "", "GID_ID");
            //        if (doSource.GetFirst() == 1)
            //        {
            //            doForm.doRS.SetFieldVal("LNK_RELATED_EA", doSource.GetCurrentRecID());
            //        }
            //        break;
            //    //doForm.SetLinkByVal("LNK_RELATED_EA", "EXPACCTNAME", "PERSONALVEHICLEMILEAGE")
            //    case 4:
            //        //"Company Vehicle Mileage"
            //        doForm.doRS.ClearLinkAll("LNK_TO_VE");
            //        //doForm.SetLinkByVal("LNK_TO_VE", "VENDORNAME", "MILEAGE")
            //        doSource = new clRowSet("VE", 3, "TXT_VendorName='Mileage'", "", "GID_ID");
            //        if (doSource.GetFirst() == 1)
            //        {
            //            doForm.doRS.SetFieldVal("LNK_TO_VE", doSource.GetCurrentRecID());
            //        }
            //        doForm.doRS.ClearLinkAll("LNK_RELATED_EC");
            //        //doForm.SetLinkByVal("LNK_RELATED_EC", "EXPCATNAME", "AUTOMILEAGE")
            //        doSource = new clRowSet("EC", 3, "TXT_ExpCatName='Auto-Mileage'", "", "GID_ID");
            //        if (doSource.GetFirst() == 1)
            //        {
            //            doForm.doRS.SetFieldVal("LNK_RELATED_EC", doSource.GetCurrentRecID());
            //        }

            //        doForm.doRS.ClearLinkAll("LNK_RELATED_EA");
            //        //doForm.SetLinkByVal("LNK_RELATED_EA", "EXPACCTNAME", "COMPANYVEHICLEMILEAGE")
            //        doSource = new clRowSet("EA", 3, "TXT_ExpAcctName='Company Vehicle Mileage'", "", "GID_ID");
            //        if (doSource.GetFirst() == 1)
            //        {
            //            doForm.doRS.SetFieldVal("LNK_RELATED_EA", doSource.GetCurrentRecID());
            //        }
            //        break;
            //    default:
            //        //not mileage, but payment
            //        //Clear Vendor, Expense Category, and Expense Account connections
            //        //Clear Vendor if set to "Mileage"
            //        if (Strings.InStr(Strings.UCase(doForm.doRS.GetFieldVal("LNK_TO_VE%%TXT_VENDORNAME").ToString()), "MILEAGE") != 0)
            //        {
            //            doForm.doRS.ClearLinkAll("LNK_TO_VE");
            //        }
            //        //Clear Expense Category if set to "Auto-Mileage"
            //        if (Strings.InStr(Strings.UCase(doForm.doRS.GetFieldVal("LNK_RELATED_EC%%TXT_EXPCATNAME").ToString()), "MILEAGE") != 0)
            //        {
            //            doForm.doRS.ClearLinkAll("LNK_RELATED_EC");
            //        }
            //        //Clear Expense Account if set to "Personal Vehicle Mileage"
            //        if (Strings.InStr(Strings.UCase(doForm.doRS.GetFieldVal("LNK_RELATED_EA%%TXT_EXPACCTNAME").ToString()), "MILEAGE") != 0)
            //        {
            //            doForm.doRS.ClearLinkAll("LNK_RELATED_EA");
            //        }
            //        break;
            //}

            switch (doRS.GetFieldVal("LNK_RELATED_EC").ToString())
            {
                case "92dde5e3-1bbb-4582-4543-98dd01419093":
                    //"Personal Vehicle Mileage", "Company Vehicle Mileage"
                    doRS.ClearLinkAll("LNK_TO_VE");
                    //doForm.doRS.SetLinkByVal("LNK_TO_VE", "VENDORNAME", "MILEAGE")
                    doSource = new clRowSet("VE", 3, "TXT_VendorName='Mileage'", "", "GID_ID");
                    if (doSource.GetFirst() == 1)
                    {
                        doRS.SetFieldVal("LNK_TO_VE", doSource.GetCurrentRecID());
                    }
                    ////		'Old code, here just for reference		
                    ////		doRowSet as object  = new clRowset ("VENDOR", 1,_
                    ////							"_TUP_VENDORNAME='MILEAGE'", _
                    ////							"VENDORNAME a", _
                    ////							"SYS_ID")
                    ////		TraceLine("Instantiated a Vendor rowset","",sProc)
                    ////		If doRowSet.GetFirst() THEN
                    ////			TraceLine("GetFirst is true, about to SetFieldVal","",sProc)
                    ////			TraceLine("Rowset GetFieldVal of SYS_ID: '" & doRowSet.GetFieldVal("SYS_ID",2) & "'","",sProc)
                    ////			doForm.SetFieldVal("LNK_TO_VENDOR",doRowSet.GetFieldVal("SYS_ID",2))
                    ////			TraceLine("Value was set in the link","",sProc)
                    ////		End If
                    ////		delete doRowSet			
                    ////		TraceLine("RowSet deleted","",sProc)
                    //doForm.doRS.ClearLinkAll("LNK_RELATED_EC");
                    ////doForm.doRs.SetLinkByVal("LNK_RELATED_EC", "EXPCATNAME", "AUTOMILEAGE")
                    //doSource = new clRowSet("EC", 3, "TXT_ExpCatName='Auto-Mileage'", "", "GID_ID");
                    //if (doSource.GetFirst() == 1)
                    //{
                    //    doForm.doRS.SetFieldVal("LNK_RELATED_EC", doSource.GetCurrentRecID());
                    //}
                    //doForm.doRS.ClearLinkAll("LNK_RELATED_EA");
                    ////Personal Vehicle Mileage if Type 3, Company Vehicle Mileage if Type 4
                    //doSource = new clRowSet("EA", 3, "TXT_ExpAcctName='Personal Vehicle Mileage'", "", "GID_ID");
                    //if (doSource.GetFirst() == 1)
                    //{
                    //    doForm.doRS.SetFieldVal("LNK_RELATED_EA", doSource.GetCurrentRecID());
                    //}
                    if (doRS.IsLinkEmpty("LNK_RELATED_EA"))
                    {
                        //Personal Vehicle Mileage if Type 3, Company Vehicle Mileage if Type 4
                        doSource = new clRowSet("EA", 3, "TXT_ExpAcctName='Personal Vehicle Mileage'", "", "GID_ID");
                        if (doSource.GetFirst() == 1)
                        {
                            doRS.SetFieldVal("LNK_RELATED_EA", doSource.GetCurrentRecID());
                        }
                    }
                    break;
                default:
                    //not mileage, but payment
                    //Clear Vendor, Expense Category, and Expense Account connections
                    //Clear Vendor if set to "Mileage"
                    if (Strings.InStr(Strings.UCase(doRS.GetFieldVal("LNK_TO_VE%%TXT_VENDORNAME").ToString()), "MILEAGE") != 0)
                    {
                        doRS.ClearLinkAll("LNK_TO_VE");
                    }
                    ////Clear Expense Category if set to "Auto-Mileage"
                    //if (Strings.InStr(Strings.UCase(doForm.doRS.GetFieldVal("LNK_RELATED_EC%%TXT_EXPCATNAME").ToString()), "MILEAGE") != 0)
                    //{
                    //    doForm.doRS.ClearLinkAll("LNK_RELATED_EC");
                    //}
                    //Clear Expense Account if set to "Personal Vehicle Mileage"
                    if (Strings.InStr(Strings.UCase(doRS.GetFieldVal("LNK_RELATED_EA%%TXT_EXPACCTNAME").ToString()), "MILEAGE") != 0)
                    {
                        doRS.ClearLinkAll("LNK_RELATED_EA");
                    }
                    break;
            }

            par_bRunNext = false;

            par_doCallingObject = doRS;
            return true;

        }
        public bool Expense_CalcAmount_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //MI 10/6/07 goMeta.PageRead modified to goMeta.LineRead.
            //par_doCallingObject: doForm.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //PURPOSE:
            //		Calculates Amount for mileage, calculates Billable Amount, Cost To Our Co
            //		IMPORTANT: ControlOnLeave event script for the Type field ensures that the user can't change the 'Type'
            //					selection from a payment to a mileage when editing an item.
            //					DO NOT CHANGE THAT! This function will zero out or recalculate the originally 
            //					entered values without warning if the user changes Type from payment to
            //					mileage and vice versa.
            //RETURNS:
            //		True.

            //VS 03282018 : Moving this to RecordOnSave to accomodate Mobile saving
            //Form doForm = (Form)par_doCallingObject;
            clRowSet doRS = (clRowSet)par_doCallingObject;

            decimal cFullAmount = default(decimal);
            double rMilesDriven = 0;
            double rMileageRate = 0;
            int iTypeVal = 0;
            bool bTypeisMileage = false;
            decimal cCostOrig = default(decimal);
            decimal cReimbAmtOrig = default(decimal);
            decimal cBillAmount = default(decimal);
            bool bBillCust = false;
            bool bFreeVal = false;
            double rTotalMiles = 0;
            int iMileageDecimals = 0;
            //No of decimals to which the mileage should be rounded
            string sWork = null;

            //sWork = goMeta.PageRead("GLOBAL", "FLD_EX", "", True)  '*** MI 10/6/07
            //TraceLine("Page FLD_EXPENSE value: '" & sWork & "'","",sProc)  '*** MI 10/6/07
            //sWork = goTR.StrRead(sWork, "CUR_MILEAGERATE_FRM", "4", False)  '*** MI 10/6/07
            sWork = goMeta.LineRead("GLOBAL", "FLD_EX", "CUR_MILEAGERATE_FRM", "4");
            //*** MI 10/6/07
            //goP.TraceLine("SR__MILEAGERATE_FRM value: '" & sWork & "'", "", sProc)
            iMileageDecimals = Convert.ToInt32(sWork);
            if (iMileageDecimals < 0)
                iMileageDecimals = 0;
            if (iMileageDecimals > 20)
                iMileageDecimals = 20;

            //------------------- Set mileage fields and calc mileage amount -------------------
            cFullAmount = Convert.ToDecimal(doRS.GetFieldVal("CUR_AMOUNT", 2));
            //Full Amount
            rMilesDriven = Convert.ToDouble(doRS.GetFieldVal("SR__MILESDRIVEN", 2));
            rMileageRate = Convert.ToDouble(doRS.GetFieldVal("CUR_MILEAGERATE", 2));
            //VS 03232018 - Set field states on Category change "Auto-Mileage" instead of Type change
            iTypeVal = Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2));
            //switch (iTypeVal)
            //{
            //    case 3:
            //    case 4:
            //        bTypeisMileage = true;
            //        break;
            //}
            string sECVal = doRS.GetFieldVal("LNK_RELATED_EC").ToString();
            switch (sECVal)
            {
                case "92dde5e3-1bbb-4582-4543-98dd01419093":
                    bTypeisMileage = true;
                    break;
            }
            //Type is Payment
            if (bTypeisMileage == false)
            {
                if (rMilesDriven != 0)
                {
                    rMilesDriven = 0;
                    doRS.SetFieldVal("SR__MILESDRIVEN", 0, 2);
                    //Set Miles Driven to 0
                }
                if (rMileageRate != 0)
                {
                    rMileageRate = 0;
                    doRS.SetFieldVal("CUR_MILEAGERATE", 0, 2);
                    //Set Mileage Rate to 0
                }
                //Type is Mileage
            }
            else
            {
                if (rMileageRate == 0)
                {
                    rMileageRate = Convert.ToDouble(doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%EXPENSE_MILEAGERATE"));
                    rMileageRate = Math.Round(rMileageRate, iMileageDecimals);
                    //goP.TraceLine(rMileageRate, "", sProc);
                    doRS.SetFieldVal("CUR_MILEAGERATE", rMileageRate, 2);
                }
                rTotalMiles = rMilesDriven * rMileageRate;
                if (rTotalMiles != Convert.ToDouble(cFullAmount))
                {
                    cFullAmount = Convert.ToDecimal(rTotalMiles);
                    //*** MI added 9/10/07
                    doRS.SetFieldVal("CUR_AMOUNT", rTotalMiles, 2);
                }
            }


            //---------------------- Calc Cost To Our Co --------------------------------
            cCostOrig = Convert.ToDecimal(doRS.GetFieldVal("CUR_COSTTOOURCO", 2));
            //original value of the field

            //Personal funds or mileage: if reimbursed, enter reimbursed amount into Cost To Our Co field
            //switch (iTypeVal)
            //{
            //    case 1:
            //    case 3:
            //        //"Personal"
            //        if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_REIMBURSED", 2)) == 1)
            //        {
            //            cReimbAmtOrig = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_REIMBURSEDAMOUNT", 2));
            //            if (cCostOrig != cReimbAmtOrig)
            //            {
            //                doForm.doRS.SetFieldVal("CUR_COSTTOOURCO", cReimbAmtOrig, 2);
            //            }
            //        }
            //        else
            //        {
            //            if (cCostOrig != 0)
            //            {
            //                doForm.doRS.SetFieldVal("CUR_COSTTOOURCO", 0, 2);
            //            }
            //        }
            //        break;
            //}
            switch (sECVal)
            {
                case "92dde5e3-1bbb-4582-4543-98dd01419093":
                    //"Personal"
                    if (Convert.ToInt32(doRS.GetFieldVal("CHK_REIMBURSED", 2)) == 1)
                    {
                        cReimbAmtOrig = Convert.ToDecimal(doRS.GetFieldVal("CUR_REIMBURSEDAMOUNT", 2));
                        if (cCostOrig != cReimbAmtOrig)
                        {
                            doRS.SetFieldVal("CUR_COSTTOOURCO", cReimbAmtOrig, 2);
                        }
                    }
                    else
                    {
                        if (cCostOrig != 0)
                        {
                            doRS.SetFieldVal("CUR_COSTTOOURCO", 0, 2);
                        }
                    }
                    break;
            }

            //Company Funds: amount spent is Cost To Our Co
            //Company Funds Payment
            if (iTypeVal == 2)
            {
                //cFullAmount = doForm.doRs.GetFieldVal("CUR_AMOUNT", 2)  '*** MI commented out 9/10/07
                if (cCostOrig != cFullAmount)
                {
                    doRS.SetFieldVal("CUR_COSTTOOURCO", cFullAmount, 2);
                }
            }

            //Company vehicle mileage: used only for customer billing, not considered a direct cost
            //to our company, therefore always fill Cost To Our Co with 0
            //"Company Vehicle Mileage"
            if (iTypeVal == 4)
            {
                if (cCostOrig != 0)
                {
                    doRS.SetFieldVal("CUR_COSTTOOURCO", 0, 2);
                }
            }


            //----------------------------- Set Billable Amount -------------------------
            cBillAmount = Convert.ToDecimal(doRS.GetFieldVal("CUR_BILLABLEAMOUNT", 2));

            //Set Billable Amount based on its value and the values of the 'Bill The Customer' and 'Free' checkboxed
            bBillCust = Convert.ToBoolean(doRS.GetFieldVal("CHK_BILLABLE", 2));
            bFreeVal = Convert.ToBoolean(doRS.GetFieldVal("CHK_FREE", 2));
            //Bill The Customer is checked
            if (bBillCust == true)
            {
                //Free is checked
                if (bFreeVal == true)
                {
                    //Set Billable Amount to 0 if it's not 0
                    if (cBillAmount != 0)
                    {
                        doRS.SetFieldVal("CUR_BILLABLEAMOUNT", 0, 2);
                    }
                    //Free is not checked
                }
                else
                {
                    //Set Billable Amount to Full Amount
                    if (cBillAmount == 0)
                    {
                        doRS.SetFieldVal("CUR_BILLABLEAMOUNT", cFullAmount, 2);
                    }
                }
                //Bill The Customer is not checked
            }
            else
            {
                //Set Billable Amount to 0 if it's not 0
                if (cBillAmount != 0)
                {
                    doRS.SetFieldVal("CUR_BILLABLEAMOUNT", 0, 2);
                }
            }

            par_bRunNext = false;

            par_doCallingObject = doRS;
            return true;

        }
        public bool EX_MANAGEREIMBURSABLE_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Name of the control, e.g.CHK_BILLING
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //goP.TraceLine("", "", sProc)

            Form doForm = (Form)par_doCallingObject;
            //Dim sWork As String

            switch (Strings.UCase(par_s1))
            {
                case "LNK_TO_VE":
                case "LNK_RELATED_EC":
                case "LNK_RELATED_EA":
                    //CS 2/14/08: Disable this: scriptManager.RunScript("Expense_SetReimbursable", doForm)

                    object oTempCallingObject = doForm.doRS;
                    scriptManager.RunScript("Expense_SetConnections", ref oTempCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                    doForm.doRS = (clRowSet)oTempCallingObject;
                    break;
            }

            par_doCallingObject = doForm;

            par_bRunNext = false;
            return true;
        }

        public bool Expense_CreateMonthlyAutoAllowance_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //MI 10/6/07 goMeta.PageRead modified to goMeta.LineRead.
            //par_doCallingObject: doForm.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 03282018
            //PURPOSE:
            //		Creates an Auto-Allowance Expense Record on the 1st Monday of every month
            //RETURNS:
            //		True.

            string sResult = null;
            string sStartDate = null;
            string sDueDate = null;
            bool bMore = true;
            string sCurrentUser = "";
            System.DateTime dtDate = default(System.DateTime);
            long lDays = 0;
            string sPointers = null;
            string sPointerDateTime = null;
            DateTime dtPointerDate = default(DateTime);
            PublicDomain.TzTimeZone zone = default(PublicDomain.TzTimeZone);
            DateTime dtUsersNow = default(DateTime);
            //User time zone 'now'
            DateTime dtUsersToday = default(DateTime);
            //User time zone Today (now.Date())
            string sDateTime = null;
            DateTime dtDateTime = default(DateTime);
            string sStartTime = null;
            string sEndTime = null;
            string sMonthName;

            dtDateTime = goTR.NowLocal();
            sMonthName = dtDateTime.ToString("MMMM");// CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(dtDateTime.Month);

            sPointers = goMeta.PageRead("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED");

            clRowSet doUSRS = new clRowSet("US", clC.SELL_READONLY, "CHK_ACTIVE=1 AND LNK_RELATED_GR='39343863-6165-3761-4752-332f32312f32'");
            if (doUSRS.GetFirst() == 1)
            {
                do
                {
                    clRowSet doEXRS = new clRowSet("EX", clC.SELL_ADD, "", "", "", -1, "", "", "CRU_EX", "", "", true, true);

                    doEXRS.SetFieldVal("DTT_TIME", "Now");
                    //doEXRS.SetFieldVal("MLS_TYPE", 1, 2);   //Personal Funds Payment
                    doEXRS.SetFieldVal("LNK_RELATED_EC", "34643062-3433-6435-4543-332f32332f32");   //Auto-Allowance
                    doEXRS.SetFieldVal("CUR_Amount", 500, 2);   //Personal Funds Payment
                    doEXRS.SetFieldVal("MMO_NOTES", sMonthName + " Car Allowance");

                    doEXRS.Commit();

                } while (doUSRS.GetNext() == 1);
            }

            return true;

        }

        #endregion

    }
}


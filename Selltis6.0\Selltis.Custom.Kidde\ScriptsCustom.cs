﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;
using System.Data.SqlClient;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;
        //private clScrMng goScr;
        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";


        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();


           
        }


        public ScriptsCustom()
        {
            Initialize();
        }

        // SGR 25102016 
        


        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }

        public bool AP_RecordOnSave_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            try
            {

                // Fill Company Type if blank
                if (Convert.ToInt32(doRS.GetFieldVal("MLS_COMPANYTYPE", 2)) == 0)
                {
                    // TLD 10/4/2012 Get type from co
                    string sCOType = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_CO%%MLS_TYPE", 1));
                    // If exists in AP CO type, set it
                    clList goList = new clList();
                    string sAPType = goList.LReadSeek("AP:TYPE", "VALUE", sCOType);
                    if (sAPType != "" & goErr.GetLastError("NUMBER") != "E30035")
                    {
                        doRS.SetFieldVal("MLS_CompanyType", goTR.StringToNum(sAPType, "", ref par_iValid, ""), 2);

                    }
                }
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doRS;
            return true;
        }


        public bool AutoAlertEveryDay_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // MI 10/29/07 Replaced code with goTr.UTC_GetUserTimeZone().
            // MI 10/23/07 Cleaned up the script, implemented UTC.
            // MI 10/1/07 Change this script from overriding the main script to only running our customer
            // functionality and then running the main script. Commented out all the code that was identical
            // as in the main script.

            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: In a _Pre script set to False to prevent the main clScripts script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS;
            bool bMore = true;
            string sCurrentUser = "";
            PublicDomain.TzTimeZone zone;
            DateTime dtUsersToday;
            DateTime dtDateTime;
            string sPointers;
            string sPointerDateTime;
            DateTime dtPointerDate;
            string sDateTime;
            string sTomorrow;
            clArray doActivities = new clArray();
            int i = 0;
            int iDays = 0; // Quote Days Overdue
            string sDueDate = ""; // Quote Due Date
                                  // TLD 7/7/2009 Added to only process Quote Send Overdue once
            string sDailyPointer;
            string sDailyPointerDateTime;
            DateTime dtToday;
            bool bSkip = false;
            string par_sDelim = "|";

            var rsUsers = new clRowSet("US", clC.SELL_READONLY, "CHK_ACTIVEFIELD=1", null/* Conversion error: Set to default value for this argument */, "GID_ID");
            // No active users                        '*** MI 10/1/07 added checking the count and not running the loop if no active users found
            if (rsUsers.Count() < 1)
            {
                bMore = false; // *** MI 10/1/07 changed from = 0 to < 1.

            }

            sPointers = goMeta.PageRead("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED");

            while (bMore == true)
            {
                sCurrentUser =Convert.ToString( rsUsers.GetFieldVal("GID_ID", clC.SELL_FRIENDLY));

                // DEBUG ==> Remove
                // goLog.Log(sProc, "  Processing user '" & goData.GetRecordNameByID(sCurrentUser) & "' [" & sCurrentUser & "]", clC.SELL_LOGLEVEL_DETAILS)
                // END DEBUG

                zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
                // Get user's 'now' and 'today at midnight' in the user's time zone
                dtUsersToday = zone.ToLocalTime(goTR.NowUTC()).Date;                  // Midnight on user's current date in his/her time zone

                // -------- Skip this user if already processed today --------
                // Get the 'last processed' date (no time) as a local datetime
                // Pointers are written as local datetimes in user's last login time zone.
                sPointerDateTime = Strings.Left(goTR.StrRead(sPointers, sCurrentUser, "", false), 10);
                if (sPointerDateTime == "")
                {
                    // Leaving one whole day ahead of 'blank' datetime: '1753-01-02 23:59:59.000'.
                    dtPointerDate = goTR.StringToDate("1753-01-04", "", ref par_iValid);
                }
                    
                else
                {
                    dtPointerDate = goTR.StringToDate(sPointerDateTime, clC.SELL_FORMAT_DATEDEF, ref par_iValid).Date;
                    dtPointerDate = DateTime.SpecifyKind(dtPointerDate, DateTimeKind.Local);

                }
                if (dtPointerDate == dtUsersToday)
                {
                    goto ProcessNextUserNow;

                }

                ProcessNextUserNow:
                ;
                if (rsUsers.GetNext() == 0)
                {
                    bMore = false;

                }
            }

            rsUsers = null/* TODO Change to default(_) if this is not a reference type */;

            // --------------------- Non-user-dependent updates -----------------------

            // --------- Set 'today UTC' -------------
            // sDateTime replaces the Selltis keyword 'Today' with the datetime value that corresponds to midnight
            // TLD 7/7/2009 Added to get today's date
            dtToday = goTR.NowUTC().Date; // now date
            dtDateTime = goTR.NowUTC().Date;
            sDateTime = goTR.DateTimeToSysString(dtDateTime,ref par_iValid,ref par_sDelim);

            // --------- Set 'tomorrow UTC' -------------
            dtDateTime = goTR.AddDay(dtDateTime, 1);
            // sTomorrow = goTR.DateTimeToSysString(goTR.AddDay(dtDateTime, 1))
            sTomorrow = goTR.DateTimeToSysString(dtDateTime,  ref par_iValid, ref par_sDelim);

            // TLD 7/7/2009 ONLY runs if NOT already ran today
            // Reads pointer
            sDailyPointer = goMeta.PageRead("GLOBAL", "OTH_DAILY_CUSTOMSCRIPT_PROCESSED");
            sDailyPointerDateTime = Strings.Left(goTR.StrRead(sDailyPointer, "QUOTESENDOVERDUEDATETIME", false), 10);

            if (sDailyPointerDateTime == "")
            {

                // Leaving one whole day ahead of 'blank' datetime: '1753-01-02 23:59:59.000'.
                dtPointerDate = goTR.StringToDate("1753-01-04","",ref par_iValid);
            }
            else
            {
                dtPointerDate = goTR.StringToDate(sDailyPointerDateTime ,"",ref par_iValid).Date;
            }
               
            dtPointerDate = DateTime.SpecifyKind(dtPointerDate, DateTimeKind.Local);
            // TLD 2/24/2010 Commented below -- for testing only, forgot to comment
            // dtPointerDate = goTR.AddDay(dtPointerDate, 1)

            // TLD 7/7/2009 Doesn't run if already ran today
            if (dtPointerDate == dtToday)
                return true;

            // -----------------Quote Send Overdue Functionality
            // TLD 10/4/2012 Mod to process RS in 50 record increments
            // TLD 4/5/2010 Added to NOT run record on save again, must be some sort of
            // loop that causes issues
            // TLD 4/1/2010 Removed to NOT run record on save
            // TLD 4/2/2009 calculates days overdue between due date & today when NOT yet sent
            // Gets Quote Rowset
            // doRS = New clRowSet("QT", 1, , "GID_ID A", , , , , , , , True)
            // TLD 7/7/2009 Modified to NOT run record on save and to filter for Due Date is NOT blank
            // can't calculate send overdue if no due date
            // TLD 10/5/2010 Mod RS to optimize
            // doRS = New clRowSet("QT", 1, "CHK_SENT=0", "GID_ID A", , , , , , , , True, True)
            long lBiID = 0;
            int iCount = 0;

            do
            {
                doRS = new clRowSet("QT", 1, "CHK_SENT=0 and bi__id > " + lBiID + "", "GID_ID A", "*", 50, null, null, null, null, null, true, true, true);
                iCount = Convert.ToInt32(iCount + doRS.Count());

                // Loops through records to calculate Send Overdue
                if (doRS.GetFirst() == 1)
                {
                     

                    lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                    do
                    {
                        // TLD 3/31/2010 Is due date blank?
                        if (Convert.ToDateTime(doRS.GetFieldVal("DTE_DUEDATE", 2)) != goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, null, null,ref par_iValid,"|",false))
                        {

                            // Get due date
                            sDueDate = Convert.ToString(doRS.GetFieldVal("DTE_DUEDATE", 1));

                            // Is due date before today?
                            string sNowDate = goTR.DateTimeToString(goTR.NowUTC().Date,"","", ref par_iValid, ref par_sDelim);
                            if (Convert.ToDateTime(sDueDate) < Convert.ToDateTime(sNowDate))
                            {
                                // Check Overdue checkbox
                                doRS.SetFieldVal("CHK_SENDOVERDUE", 1);
                            }
                               
                            else
                            {
                                // Due Date is NOT before today
                                doRS.SetFieldVal("CHK_SENDOVERDUE", 0);
                            }
                               

                            // Calculate days different between due and now
                            iDays = Convert.ToInt32(DateAndTime.DateDiff(DateInterval.Day, Convert.ToDateTime(doRS.GetFieldVal("DTE_DUEDATE", 2)), goTR.NowUTC()));
                            if (iDays < -32767 | iDays > 32767)
                                // Set to 0?
                                iDays = 0;
                            doRS.SetFieldVal("INT_DAYSOVERDUE", iDays);

                            // Set system var so section in recordonsave won't run
                            goP.SetVar("FromAutoAlert", "1");

                            // TLD 10/4/2012 Write to log for failure
                            // doRS.Commit()
                            if (doRS.Commit() == 0)
                                // write message to log with error
                                goLog.Log(sProc, "Send overdue update failed for QT '" + doRS.GetFieldVal("TXT_QuoteNo") + " with error " + goErr.GetLastError("NUMBER") + "'", -1, true, true);

                            // Reset var
                            iDays = 0;
                            sDueDate = "";
                        }

                        if (doRS.GetNext() == 0)

                            break;
                    }
                    while (true);
                    lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                    //lBiID = doRS.GetFieldVal("BI__ID");
                }
                else
                    break;
            }
            while (true)// Overdue should NOT be checked// get last BI__ID processed
        ;

            // -------Check once more for any newly added records?
            doRS = new clRowSet("QT", 1, "CHK_SENT=0 and bi__id > " + lBiID + "", "GID_ID A", "*", 0, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true, true, true);
            iCount = Convert.ToInt32(iCount + doRS.Count());

            // Loops through records to calculate Send Overdue
            if (doRS.GetFirst() == 1)
            {
                    lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));

                //lBiID = (doRS.GetFieldVal("BI__ID");
                do
                {
                    // TLD 3/31/2010 Checks to see if due date is blank
                    if (Convert.ToDateTime(doRS.GetFieldVal("DTE_DUEDATE", 2)) != goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, null, null, ref par_iValid,"|",false))
                    {

                        // Get due date
                        sDueDate = Convert.ToString(doRS.GetFieldVal("DTE_DUEDATE", 1));

                        // Is due date before today?
                        string sNowDate = goTR.DateTimeToString(goTR.NowUTC().Date,"","",ref par_iValid,ref par_sDelim);
                        
                        if (Convert.ToDateTime(sDueDate) < Convert.ToDateTime(sNowDate))
                        // Check Overdue checkbox
                        {
                            doRS.SetFieldVal("CHK_SENDOVERDUE", 1);

                        }
                        else
                        {
                            // Due Date is NOT before today
                            doRS.SetFieldVal("CHK_SENDOVERDUE", 0);
                        }
                            

                        // Calculate days different between due and now
                        iDays = Convert.ToInt32(DateAndTime.DateDiff(DateInterval.Day, Convert.ToDateTime(doRS.GetFieldVal("DTE_DUEDATE", 2)), goTR.NowUTC()));
                        if (iDays < -32767 | iDays > 32767)
                            // Set to 0?
                            iDays = 0;
                        doRS.SetFieldVal("INT_DAYSOVERDUE", iDays);

                        // Set system var so section in recordonsave won't run
                        goP.SetVar("FromAutoAlert", "1");

                        // TLD 10/4/2012 Write to log for failure
                        // doRS.Commit()
                        if (doRS.Commit() == 0)
                            // write message to log with error
                            goLog.Log(sProc, "Send overdue update failed for QT '" + doRS.GetFieldVal("TXT_QuoteNo") + " with error " + goErr.GetLastError("NUMBER") + "'", -1, true, true);

                        // Reset var
                        iDays = 0;
                        sDueDate = "";
                    }

                    if (doRS.GetNext() == 0)
                        break;
                }
                while (true)// Overdue should NOT be checked
        ;
                lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 :Convert.ToInt64(doRS.GetFieldVal("BI__ID"));

            }
            // -------Check once more for any newly added records?
            doRS = null/* TODO Change to default(_) if this is not a reference type */;

            // TLD 7/7/2009 Sets pointer to say this ran
            goMeta.LineWrite("GLOBAL", "OTH_DAILY_CUSTOMSCRIPT_PROCESSED", "QUOTESENDOVERDUEDATETIME", goTR.DateTimeToSysString(goTR.NowUTC(),ref par_iValid,ref par_sDelim),ref par_oConnection,"","");
            // -----------------Quote Send Overdue Functionality

            return true;
        }

        public bool AutoQuoteCreateRevision_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 1/24/08 Changed from 1 to 0 to make the first rev to be Rev 1 not 2.
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PJ 1/12/09: Purpose of _Pre script: do not want to copy the link of the related QD. 1 QD per QT only!
            par_bRunNext = false;

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sID;
            clRowSet doRowset;
            string sFileName;
            Form doF;
            clRowSet doAllQTs;
            string sQuoteNo;
            string sQuoteOnlyNo;      // Quote number without the revision
            int iRevNo;
            int iRevNoTemp;
            int iLen;
            int i;
            int iPos;
            string sQuoteTitleOnly;

            // Check selected record
            sID = goUI.GetLastSelected("SELECTEDRECORDID");
            // goP.TraceLine("sID: " & sID & "'", "", sProc)
            sFileName = Strings.UCase(goTR.GetFileFromSUID(sID));
            if (sFileName != "QT")
            {
                goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            // Check if have permissions
            if (goData.GetAddPermission("QT") == false)
            {
                goUI.NewWorkareaMessage("You cannot create a revision of the selected Quote because you don't have permissions to create Quotes.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            // Copy the selected record
            // Get doRowset of current record
            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "**", 1);
            if (doRowset.Count() < 1)
            {
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            // ------------------- Update revision no in Quote No -------------------
            sQuoteNo = Strings.Trim(Convert.ToString(doRowset.GetFieldVal("TXT_QuoteNo")));
            if (sQuoteNo == "")
            {
                if (!scriptManager.RunScript("Quote_GenerateQuoteNo", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, sQuoteNo))
                    return false;
            }
            if (sQuoteNo == "")
            {
                // Should never happen
                goErr.SetError(35000, sProc, "Quote number is blank.");
                return false;
            }

            sQuoteOnlyNo = sQuoteNo;
            // Get the quote no without the revision number
            iPos = Strings.InStr(sQuoteOnlyNo, "-");
            if (iPos > 0)
                sQuoteOnlyNo = Strings.Left(sQuoteOnlyNo, iPos - 1);
            // Limit the length of the quote no just in case someone mangled it by hand
            iLen = Strings.Len(sQuoteOnlyNo);
            if (iLen > 14)
            {
                iLen = 14;
                sQuoteOnlyNo = Strings.Left(sQuoteOnlyNo, 14);
            }

            // Determine the highest revision no
            doAllQTs = new clRowSet(sFileName, clC.SELL_READONLY, "TXT_QuoteNo[='" + goTR.PrepareForSQL(sQuoteOnlyNo) + "'", "DTT_Time DESC", "TXT_QuoteNo");    // MI 11/6/08 Added goTr.PrepareForSQL
            iRevNo = 0;  // *** MI 1/24/08 Changed from 1 to 0 to make the first rev to be Rev 1 not 2.
            for (i = 1; i <= doAllQTs.Count(); i++)
            {
                sQuoteNo = Convert.ToString(doAllQTs.GetFieldVal("TXT_QuoteNO"));
                iPos = Strings.InStr(sQuoteNo, "-");
                if (iPos > 0)
                {
                    // The number contains a revision no
                    iRevNoTemp = Convert.ToInt32(goTR.StringToNum(Strings.Right(sQuoteNo, 3),"",ref par_iValid,""));
                    if (iRevNoTemp > iRevNo)
                        iRevNo = iRevNoTemp;
                }
                if (doAllQTs.GetNext() != 1)
                    break;
            }

            // Advance the revision number by 1, but not if revision is 1.
            iRevNo = iRevNo + 1;
            if (iRevNo > 999)
                iRevNo = 999;
            // Remove all spaces - there can be a space after the user code if shorter than 4 chars
            sQuoteNo = goTR.Replace(sQuoteOnlyNo, " ", "_");
            // 'Ensure fixed length - commented because it makes numbers ugly with "_"
            // sQuoteNo = goTR.Pad(sQuoteNo, 14, "_")
            sQuoteNo = sQuoteOnlyNo + "-" + goTR.Pad(iRevNo.ToString(), 3, "0", "L");
            if (!(doAllQTs == null))
            {
                doAllQTs = null/* TODO Change to default(_) if this is not a reference type */;

            }

            // Create the new form
            doF = new Form(sFileName, "", "CRU_" + sFileName);
            clRowSet doRTemp = doF.doRS;
            // Copy this quote to the new form's rowset
            if (!goData.CopyRecord(ref doRowset, ref doRTemp))
            {
                goErr.SetError(35000, sProc, "Copying the selected Quote '" + sID + "' failed.");
                return false;
            }

            // doF.doRS.SetFieldVal("GID_ID", goData.GenerateID(sFileName))
            doF.doRS.SetFieldVal("LNK_CreatedBy_US", goP.GetUserTID());
            doF.doRS.SetFieldVal("DTT_Time", "Today|Now");
            // CS 8/3/07: Reason and Status of revised QT should be same as original QT
            // doF.doRS.SetFieldVal("MLS_Status", 0, clC.SELL_SYSTEM)    'Open
            // doF.doRS.SetFieldVal("MLS_ReasonWonLost", 0, clC.SELL_SYSTEM)    '<Make selection>
            doF.doRS.SetFieldVal("DTT_DateClosed", "");
            // Set History tab & Cloned From Qt
            doF.doRS.SetFieldVal("MMO_History", "Revision of Quote" + doRowset.GetFieldVal("SYS_Name"));
            doF.doRS.SetLinkVal("LNK_ClonedFrom_QT", sID);
            sQuoteTitleOnly = Convert.ToString(doRowset.GetFieldVal("TXT_QuoteTitle"));
            iPos = Strings.InStr(sQuoteTitleOnly, " REV ");
            if (iPos > 0)
                sQuoteTitleOnly = Strings.Left(sQuoteTitleOnly, iPos - 1);
            doF.doRS.SetFieldVal("TXT_QuoteTitle", sQuoteTitleOnly + " REV " + iRevNo.ToString());
            doF.doRS.SetFieldVal("TXT_QuoteNo", sQuoteNo);
            // TLD 6/10/2010 Clear Send Date
            doF.doRS.SetFieldVal("DTT_SENDDATE", "");

            // !!
            doF.doRS.ClearLinkAll("LNK_Connected_QL");

            // PJ:
            // Clear QT links
            doF.doRS.ClearLinkAll("LNK_Related_QD");
            doF.doRS.ClearLinkAll("LNK_Connected_QE");
            // Clear QL links
            // Does QT contain QL's? (Connected QL)
            // TLD 10/5/2010 Mod RS to optimize
            clRowSet qlRS;
            // qlRS = New clRowSet("QL", 1, "LNK_IN_QT = '" & doF.doRS.GetFieldVal("GID_ID") & "'", , , , , , , , , True, True, , , , , True)
            qlRS = new clRowSet("QL", 1, "LNK_IN_QT = '" + doF.doRS.GetFieldVal("GID_ID") + "'", null/* Conversion error: Set to default value for this argument */, "*, LNK_CONNECTED_QE", 0, null, null, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true, true, true/* Conversion error: Set to default value for this argument */, true/* Conversion error: Set to default value for this argument */, 0/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true);
            if (qlRS.GetFirst() == 1)
            {
                do
                {
                    qlRS.ClearLinkAll("LNK_CONNECTED_QE");
                    qlRS.Commit();
                    if (qlRS.GetNext() == 0)
                        break;
                }
                while (true);
            }
            // 
            doF.SetControlVal("NDB_MMO_Lines", Convert.ToString(doRowset.GetFieldVal("LNK_Connected_QL%%SYS_Name")));      // Lines will display as a memo
            doF.oVar.SetVar("QuoteOpeningMode", "Revision");
            doF.oVar.SetVar("QuoteOrinalQuoteID", doRowset.GetFieldVal("GID_ID"));
            doF.MessagePanel("This is a revision of the Quote '" + doRowset.GetFieldVal("SYS_Name") + "'." + Constants.vbCrLf + "Check the Title and Quote Number and click Save or click Modify Lines to add, edit or remove them.", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Info.gif");

            goUI.Queue("FORM", doF);

            // Clean up objects
            doRowset = null/* TODO Change to default(_) if this is not a reference type */;

            return true;
        }

        public bool AutoQuoteDuplicate_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // MI 4/25/07 CREATED BY MI 4/22/07
            // PURPOSE:
            // TLD 10/3/2012 Mod to update LNK_OtherClones_QT, uncheck report
            // Duplicate an existing Quote and its line items allowing the user to connect a different
            // Contact, Company, etc.

            string sID;
            clRowSet doRowset;
            string sFileName;
            Form doF;
            string sOrigQuoteName;
            string sOrigQuoteID;

            // Check selected record
            sID = goUI.GetLastSelected("SELECTEDRECORDID");
            // goP.TraceLine("sID: " & sID & "'", "", sProc)
            sFileName = Strings.UCase(goTR.GetFileFromSUID(sID));
            if (sFileName != "QT")
            {
                goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            // Check if have permissions
            if (goData.GetAddPermission("QT") == false)
            {
                goUI.NewWorkareaMessage("You cannot duplicate the selected Quote because you don't have permissions to create Quotes.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            // Copy the selected record
            // Get doRowset of current record
            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "*,LNK_CONNECTED_QL%%SYS_Name", 1);
            if (doRowset.Count() < 1)
            {
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }
            else
            {
                sOrigQuoteName = Convert.ToString(doRowset.GetFieldVal("SYS_Name"));
                sOrigQuoteID = Convert.ToString(doRowset.GetFieldVal("GID_ID"));
            }

            // Create the new Quote form
            doF = new Form(sFileName, "", "CRU_" + sFileName);
            doF.oVar.SetVar("QuoteOpeningMode", "Duplicate");
            doF.oVar.SetVar("QuoteOrinalQuoteID", sID);
            doF.SetControlVal("NDB_MMO_Lines",Convert.ToString(doRowset.GetFieldVal("LNK_Connected_QL%%SYS_Name")));
            doF.doRS.SetFieldVal("TXT_Description", doRowset.GetFieldVal("TXT_Description", 2), 2);
            doF.doRS.SetFieldVal("CUR_Subtotal", doRowset.GetFieldVal("CUR_Subtotal", 2), 2);
            doF.doRS.SetFieldVal("CUR_Total", doRowset.GetFieldVal("CUR_Total", 2), 2);
            // Set History tab & Cloned from Quote
            doF.doRS.SetFieldVal("MMO_History", "Duplicated from Quote" + sOrigQuoteName);
            doF.doRS.SetLinkVal("LNK_ClonedFrom_QT", sOrigQuoteID);
            // TLD 10/3/2012 Update LNK_OtherClonesFor_QT
            doF.doRS.SetLinkVal("LNK_OtherClonesFor_QT", sOrigQuoteID);
            // TLD 10/3/2012 Uncheck Report
            doF.doRS.SetFieldVal("CHK_Report", 0, 2);

            doF.MessagePanel("This is a duplicate of the Quote '" + sOrigQuoteName + "'." + Constants.vbCrLf + "Fill out the form and click Save or click Modify Lines to add, edit or remove them.", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Info.gif");

            goUI.Queue("FORM", doF);

            // Clean up objects
            doRowset = null/* TODO Change to default(_) if this is not a reference type */;

            return true;
        }


        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 10/10/2012 Copied default, copied
            // from custom _Post (prevent QT saving twice)
            par_bRunNext = false;

            // MI 2/23/07 Mods
            // PURPOSE:
            // Calculate totals of a Quote from Quote Lines independent of the form context.
            // RETURNS:
            // True if successful, False if not with SetError. Returns calculation results
            // via gop:SetVar() in the following variables:
            // CUR_SUBTOTAL
            // CUR_SUBTOTALT
            // CUR_SALESTAX
            // CUR_TOTAL	

            //object doLines;   // 
            int lI;
            decimal cWork = 0 ;        // Non-taxable subtotals
            decimal cWorkT=0;       // Taxable subtotals only
            double rTaxPercent;
            decimal cTaxAmount;
            decimal cOtherCharge;
            decimal cTotalAmount;
            decimal cShipAmount;
            // Dim s1 As String = par_s1   'Quote GID_ID
            // Dim s2 As String = par_s2   'Quote Line GID_ID 
            // Dim s3 As String = par_s3
            // Dim s4 As String = par_s4
            // Dim s5 As String = par_s5
            clRowSet doRS = (clRowSet)par_doCallingObject;      // Quote rowset
            bool bCommit = false;
            bool bQLTaxable = false;
            decimal cQLSubtotal=0;
            bool bQLFound = false;
            string sFiles = ""; // FIL_INCLUSIONS from Quote Line Models
            bool bUpdInclusions = false;
            // TLD 10/10/2012 Moved from _Post
            decimal cOtherCharge2;
            decimal cPercMarginTotal=0;
            decimal cPercMarginWon=0;
            int iQuoteLines = 0;
            int iQuoteLinesWon = 0;
            double rPoA = 0;
            double rPoW = 0;
            double rSalesProb = 0;
            clRowSet doLines = default(clRowSet);

            // Vars are:
            // s1 = QT GID_ID
            // s2 = QL GID_ID
            // s3 = QL CHK_TAXABLE
            // s4 = QL CUR_Subtotal

            // Vars used to be:
            // s1 = goP.GetVar("QUOTE_GID_ID")
            // s2 = goP.GetVar("QUOTE_CHK_INCLUDETAXCHARGES")
            // s3 = goP.GetVar("QUOTE_SR__SALESTAXPERCENT")
            // s4 = goP.GetVar("QUOTE_CUR_OTHERCHARGE")
            // s5 = goP.GetVar("QUOTE_CUR_SHIPPING")
            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)



            // ------------- Validate parameters ----------------
            if (!goData.IsFileValid(goTR.GetFileFromSUID(par_s1.ToString())))
            {
                goErr.SetError(10100, sProc, null/* Conversion error: Set to default value for this argument */, goTR.GetFileFromSUID(par_s1), "File extracted from SUID in par_s1: '" + par_s1 + "'. Be sure to pass the GID_ID value of the Quote to recalculate.");
                // 10100: Invalid file name '[1]'. [2]
                return false;
            }
            if (par_s2.ToString() == "")
            {
                // Override QL ID not provided - ignore the rest of QL parameters
                par_s3 = "";
                par_s4 = "";
            }
            else
            {
                // Quote Line GID_ID was passed
                // QL's CHK_Taxable value
                if (par_s3.ToString() == "")
                {
                    goErr.SetError(35000, sProc, "par_s3 is blank. QL's CHK_Taxable value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
                    return false;
                }
                else
                    bQLTaxable = goTR.StringToCheckbox(par_s3,false,ref par_iValid);
                // QL's CUR_Subtotal value
                if (par_s4.ToString() == "")
                {
                    goErr.SetError(35000, sProc, "par_s4 is blank. QL's CUR_Subtotal value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
                    return false;
                }
                else
                    cQLSubtotal = Convert.ToDecimal(par_s4);
            }


            // -------------- Read Lines and calculate their amounts ------------
            // CS 12/2/08: Check if MO.FIL_INCLUSIONS exists. If so need to get it in the QL RS below
            // TLD 10/12/2012 ONLY calc quote lines if CHK_SubComponent is NOT checked
            // TLD 10/10/2012 Added to RS, moved from _Post
            if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                // doLines = New clRowSet("QL", 3, "LNK_IN_QT='" & par_s1.ToString & "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS")
                // doLines = New clRowSet("QL", 3, "LNK_IN_QT='" & par_s1.ToString & "'", goData.GetDefaultSort("QL"), _
                // "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS, SR__MARGIN, SR__UNITMARGIN, MLS_STATUS")
                doLines = new clRowSet("QL", 3, "CHK_SubComponent<>1 AND LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS, SR__MARGIN, SR__UNITMARGIN, MLS_STATUS");
            else
                // doLines = New clRowSet("QL", 3, "LNK_IN_QT='" & par_s1.ToString & "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL")
                // doLines = New clRowSet("QL", 3, "LNK_IN_QT='" & par_s1.ToString & "'", goData.GetDefaultSort("QL"), _
                // "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, SR__MARGIN, SR__UNITMARGIN, MLS_STATUS")
                doLines = new clRowSet("QL", 3, "CHK_SubComponent<>1 AND LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, SR__MARGIN, SR__UNITMARGIN, MLS_STATUS");
            // Browse through the rowset
            lI = 1;
            if (doLines.GetFirst() == 1)
            {
                // TLD 10/10/2012 Moved from _Post
                iQuoteLines = Convert.ToInt32(doLines.Count());
                do
                {
                    // Add up Quote Lines. Skip the one for which GID_ID is passed via par_s2
                    if (par_s2.ToString() == "" | Strings.UCase(par_s2) != Strings.UCase(Convert.ToString(doLines.GetFieldVal("GID_ID", 1))))
                    {
                        if (Convert.ToInt32(doLines.GetFieldVal("CHK_TAXABLE", 2)) == 1)
                        {
                            cWorkT += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));

                        }
                        else
                        {
                            cWork += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));

                        }
                        // CS 12/2/08: Get value from QL%%MO FIL_INCLUSIONS field
                        if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                        {
                            // Check if the file has already been added
                            if (goTR.Position(sFiles, Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"))) == 0)
                            {
                                // If this is the first file don't add a vbcrlf in front of it
                                if (sFiles == "")
                                {
                                    sFiles = Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"));

                                }
                                else
                                {
                                    sFiles = sFiles + Constants.vbCrLf + doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS");

                                }
                            }
                        }
                        // TLD 10/10/2012 Moved from _Post
                        if (Convert.ToInt32(doLines.GetFieldVal("SR__MARGIN", 2)) == 0)
                        {
                            cPercMarginTotal =Convert.ToDecimal(cPercMarginTotal) + Convert.ToDecimal(doLines.GetFieldVal("SR__UNITMARGIN", 2));
                            if (Convert.ToInt32(doLines.GetFieldVal("MLS_STATUS", 2)) == 2)
                            {
                                iQuoteLinesWon = iQuoteLinesWon + 1;
                                cPercMarginWon = Convert.ToDecimal(cPercMarginWon) + Convert.ToDecimal(doLines.GetFieldVal("SR__UNITMARGIN", 2));
                            }
                        }
                        else
                        {
                            cPercMarginTotal = Convert.ToDecimal(cPercMarginTotal) + Convert.ToDecimal(doLines.GetFieldVal("SR__MARGIN", 2));
                            if (Convert.ToInt32(doLines.GetFieldVal("MLS_STATUS", 2)) == 2)
                            {
                                iQuoteLinesWon = iQuoteLinesWon + 1;
                                cPercMarginWon = Convert.ToDecimal(cPercMarginWon) + Convert.ToDecimal(doLines.GetFieldVal("SR__MARGIN", 2));
                            }
                        }
                    }

                    if (doLines.GetNext() == 0)
                        break;
                    lI += 1;
                }
                while (true);
            }
            // delete(doLines)
            doLines = null;

            // Add the Quote Line passed via parameters
            if (par_s2 != "")
            {
                // CS 12/31/08: Get Fil_Inclusions of QL passed via parameter
                // This code needs to be run if you open a QL directly from
                // a QL view and it has file inclusions
                // CS 1/8/09: Check if FIL_INCLUSIONS exist in db first.
                if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                {
                    doLines = new clRowSet("QL", 3, "GID_ID='" + par_s2.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS");
                    if (doLines.GetFirst() == 1)
                    {
                        // If goData.IsFieldValid("MO", "FIL_INCLUSIONS") Then
                        // Check if the file has already been added
                        if (goTR.Position(sFiles, Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"))) == 0)
                        {
                            // If this is the first file don't add a vbcrlf in front of it
                            if (sFiles == "")
                                sFiles = Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"));
                            else
                                sFiles = sFiles + Constants.vbCrLf + doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS");
                        }
                        doLines = null;
                    }
                }

                // TLD 10/12/2012 ONLY calc QL if SubComponent NOT checked
                // TLD 10/10/2012 Moved from _Post
                // doLines = New clRowSet("QL", 3, "GID_ID='" & par_s2.ToString & "'", goData.GetDefaultSort("QL"), _
                // "SR__UNITMARGIN, MLS_STATUS, SR__MARGIN")
                doLines = new clRowSet("QL", 3, "CHK_SubComponent<> 1 AND GID_ID='" + par_s2.ToString() + "'", goData.GetDefaultSort("QL"), "SR__UNITMARGIN, MLS_STATUS, SR__MARGIN");
                if (doLines.GetFirst() == 1)
                {
                    if (Convert.ToInt32(doLines.GetFieldVal("SR__MARGIN", 2)) == 0)
                    {
                        cPercMarginTotal = Convert.ToDecimal(cPercMarginTotal) + Convert.ToDecimal(doLines.GetFieldVal("SR__UNITMARGIN", 2));
                        if (Convert.ToInt32(doLines.GetFieldVal("MLS_STATUS", 2)) == 2)
                        {
                            iQuoteLinesWon = iQuoteLinesWon + 1;
                            cPercMarginWon = Convert.ToDecimal(cPercMarginWon) + Convert.ToDecimal(doLines.GetFieldVal("SR__UNITMARGIN", 2));
                        }
                    }
                    else
                    {
                        cPercMarginTotal = Convert.ToDecimal(cPercMarginTotal) + Convert.ToDecimal(doLines.GetFieldVal("SR__MARGIN", 2));
                        if (Convert.ToDouble(doLines.GetFieldVal("MLS_STATUS", 2)) == 2)
                        {
                            iQuoteLinesWon = iQuoteLinesWon + 1;
                            cPercMarginWon = Convert.ToDecimal(cPercMarginWon) + Convert.ToDecimal(doLines.GetFieldVal("SR__MARGIN", 2));
                        }
                    }

                    // TLD 10/25/2012 Moved from below, only need to add QL subtotal
                    // if subcomponent is not checked?
                    if (bQLTaxable == true)
                        cWorkT += (cQLSubtotal);
                    else
                        cWork += cQLSubtotal;

                    doLines = null;
                }
            }

            // Subtotal = cWork + cWorkT
            // SubtotalT = cWorkT

            // ---------- Pull up the Quote -----------
            if (doRS == null)
            {
                // Get the quote from disk
                bCommit = true;
                // doRS = New clRowSet("QT", 1, _
                // "GID_ID='" & par_s1 & "'", _
                // "DTT_TIME ASC", _
                // "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL")
                // CS 7/26/07: Currently if you open a QT that has a required field missing
                // such as NA date, edit a QL and save the QL you get an error. Trying to avoid
                // that by bypassing validation.
                doRS = new clRowSet("QT", 1, "GID_ID='" + par_s1 + "'", "DTT_TIME ASC", "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL", 0/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true);
            }
            else
                // Validate the passed Rowset object
                if (Strings.UCase(doRS.GetFileName()) != "QT")
            {
                goErr.SetError(35000, sProc, "The file of the rowset in par_doCallingObject parameter is not QT (Quote). Either pass a Quote rowset or only pass the Quote GID_ID in par_s1 parameter.");
                return false;
            }

            // ----------- Read Quote data and calculate -------------
            if (doRS.GetFirst() == 1)
            {
                // CS 12/2/08: Get the value of the 'Do not update inclusions' on QT
                // If checked, do not update FIL_INCLUSIONS field on QT
                if (goData.IsFieldValid("QT", "CHK_NOUPDINCLUSIONS"))
                {
                    if (Convert.ToInt32(doRS.GetFieldVal("CHK_NOUPDINCLUSIONS", 2)) == 0)
                    {
                        bUpdInclusions = true;

                    }
                }
                rTaxPercent = Convert.ToDouble(doRS.GetFieldVal("SR__SALESTAXPERCENT", clC.SELL_SYSTEM));    // s3
                cTaxAmount = Convert.ToDecimal(cWorkT) * Convert.ToDecimal(rTaxPercent) / 100;     // cTaxAmount goes into CUR_SALESTAX
                                                             // If the 'Include Tax/Charges' check-box is not checked, do not add tax,
                                                             // other charge and shipping to Total. 
                if (Convert.ToInt32(doRS.GetFieldVal("CHK_INCLUDETAXCHARGES", clC.SELL_SYSTEM))== 1)
                {
                    cOtherCharge = Convert.ToDecimal(doRS.GetFieldVal("CUR_OTHERCHARGE", clC.SELL_SYSTEM));   // s4
                                                                                           // TLD 10/10/2012 Moved from _Post
                                                                                           // add Other Charge 2 to total
                    cOtherCharge2 = Convert.ToDecimal(doRS.GetFieldVal("CUR_OTHERCHARGE2", 2));
                    cShipAmount = Convert.ToDecimal(doRS.GetFieldVal("CUR_SHIPPING", clC.SELL_SYSTEM));   // s5
                                                                                       // cTotalAmount goes to CUR_TOTAL
                                                                                       // TLD 10/10/2012 Moved from _Post to add othercharge2 to total
                                                                                       // cTotalAmount = cWork + cWorkT + cTaxAmount + cOtherCharge + cShipAmount
                    cTotalAmount = cWork + cWorkT + cTaxAmount + cOtherCharge + cShipAmount + cOtherCharge2;
                }
                else
                    // cTotalAmount goes to CUR_TOTAL
                    cTotalAmount = cWork + cWorkT;
            }
            else
            {
                // goP.TraceLine("doRS GetFirst not found", "", sProc)
                doRS = null/* TODO Change to default(_) if this is not a reference type */;
                goErr.SetError(30032, sProc, "", "Quote");
                // The linked [1] can't be updated because it can't be found. 
                // goP.TraceLine("Return False", "", sProc)
                return false;
            }

            // --------------- Update calculated fields ----------------
            doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cWork + cWorkT), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_SUBTOTALT", goTR.RoundCurr(cWorkT), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_SALESTAX", goTR.RoundCurr(cTaxAmount), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_TOTAL", goTR.RoundCurr(cTotalAmount), clC.SELL_SYSTEM);

            // --------------Update FIL_Inclusions
            if (bUpdInclusions == true)
                doRS.SetFieldVal("FIL_INCLUSIONS", sFiles);

            // -------------Update Calc Perc Values
            // TLD 10/10/2012 Moved from _Post
            // TLD 10/10/2012 Moved from _Pre
            // TLD 11/2/2009 Changed to SR__ instead of SI__
            // Set new field averages
            if (cPercMarginTotal != 0 & iQuoteLines != 0)
                doRS.SetFieldVal("SR__PERCMARGINTOTAL", Math.Round(cPercMarginTotal / (decimal)iQuoteLines), 2);
            else
                doRS.SetFieldVal("SR__PERCMARGINTOTAL", 0);
            if (cPercMarginWon != 0 & iQuoteLinesWon != 0)
                doRS.SetFieldVal("SR__PERCMARGINWON", Math.Round(cPercMarginWon / (decimal)iQuoteLinesWon), 2);
            else
                doRS.SetFieldVal("SR__PERCMARGINWON", 0);

            // ----------------Update Sales Process
            // TLD 10/10/2012 Added Sales Process Calcs
            // TLD 10/9/2012 Calculate Sales process stuff
            // PoA Max Score = 20
            // PoW Max Score = 30
            // PoA = Budget & Authority
            // PoW = Need, Time Frame & Sales Gut Feel
            // SR__PoA = PoA Actual / PoA Max Score
            // SR__PoW = PoW Actual / PoW Max Score
            // Get list box values, which are currently
            // BUDGET:                                AUTHORITY:
            // US_0=<Make selection> -- (0)           US_0=<Make selection> -- (0)
            // US_1=Funded -- (10)                    US_1=Decision Maker -- (10)
            // US_2=In Process -- (8)                 US_2 = Recommender -- (8)
            // US_3=No Budget -- (1)                  US_3 = Influencer -- (5)

            // NEED:                                  TIME FRAME:
            // US_0=<Make selection> -- (0)           US_0=<Make selection> -- (0)
            // US_1=Specified Supplier is KFF -- (10) US_1 = Immediate -- (10)
            // US_2=Seeking Information -- (7)        US_2=Short-Term -- (8)
            // US_3=Third Quote -- (1)                US_3=Long Term -- (6)

            // SALES GUT FEEL:
            // US_0=<Make selection> -- (0)
            // US_1 = High -- (10)
            // US_2 = Medium -- (5)
            // US_3 = Low -- (1)

            // ------------PoA
            // Budget
            switch (doRS.GetFieldVal("MLS_Budget", 2))
            {
                case 1:
                    {
                        rPoA = 10;
                        break;
                    }

                case 2:
                    {
                        rPoA = 8;
                        break;
                    }

                case 3:
                    {
                        rPoA = 1;
                        break;
                    }
            }

            // Add Authority to PoA
            switch (doRS.GetFieldVal("MLS_Authority", 2))
            {
                case 1:
                    {
                        rPoA += 10;
                        break;
                    }

                case 2:
                    {
                        rPoA += 8;
                        break;
                    }

                case 3:
                    {
                        rPoA += 5;
                        break;
                    }
            }

            // Calc PoA
            rPoA = (rPoA / 20) * 100;
            doRS.SetFieldVal("SR__PoA", rPoA, 2);
            // ------------PoA

            // ------------PoW
            // Need
            switch (doRS.GetFieldVal("MLS_Need", 2))
            {
                case 1:
                    {
                        rPoW = 10;
                        break;
                    }

                case 2:
                    {
                        rPoW = 7;
                        break;
                    }

                case 3:
                    {
                        rPoW = 1;
                        break;
                    }
            }

            // Time Frame
            switch (doRS.GetFieldVal("MLS_TimeFrame", 2))
            {
                case 1:
                    {
                        rPoW += 10;
                        break;
                    }

                case 2:
                    {
                        rPoW += 7;
                        break;
                    }

                case 3:
                    {
                        rPoW += 1;
                        break;
                    }
            }

            // Sales Gut Feel
            switch (doRS.GetFieldVal("MLS_SalesGutFeel", 2))
            {
                case 1:
                    {
                        rPoW += 10;
                        break;
                    }

                case 2:
                    {
                        rPoW += 5;
                        break;
                    }

                case 3:
                    {
                        rPoW += 1;
                        break;
                    }
            }

            // Calc PoW
            rPoW = (rPoW / 30) * 100;
            doRS.SetFieldVal("SR__PoW", rPoW, 2);
            // ------------PoW

            // Calc PoA x PoW
            rSalesProb = (rPoA * rPoW) / 100;
            doRS.SetFieldVal("SR__SalesProb", rSalesProb, 2);

            // Set list box for icon display in view
            // MLS_SalesProb
            // US_0=<Make selection>
            // US_1 = Green(70 - 100%)
            // US_2 = Yellow(50 - 69%)
            // US_3=Red (<50%)

            if (rSalesProb < 50)
                doRS.SetFieldVal("MLS_SalesProb", 3, 2);// Red

            if (rSalesProb >= 50 & rSalesProb <= 69)
                doRS.SetFieldVal("MLS_SalesProb", 2, 2);// Yellow

            if (rSalesProb > 69)
                doRS.SetFieldVal("MLS_SalesProb", 1, 2);// Green


            // --------------- Save the Quote ---------------
            if (bCommit)
            {
                goP.SetVar("bDoNotUpdateQuoteLines", "1");
                // CS 11/5/09: Setting a variable here to let me know NOT to try to update the Qt total again in QT_RecOnSave. Issue was that if
                // you open a QT, add a Quote Line and then cancel out of the QT, the QT total did not reflect the actual total. This
                // was because CalcQuotetotal was being called here and then again in QT_RecordOnSave. We do NOT want it to be called in QT
                // RecOnSave if it was called here.
                goP.SetVar("bDoNotRecalcQuoteTotal", "1");
                // Save to disk
                if (doRS.Commit() != 1)
                {
                    // goP.TraceLine("Commit failed, raising error", "", sProc)
                    goP.DeleteVar("bDoNotUpdateQuoteLines");
                    // CS 11/6/09: Set in Ql_RecordOnSave
                    goP.DeleteVar("bDoNotRecalcQuoteTotal");
                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                    goErr.SetError(35000, sProc, "Error updating the Quote '" + par_s1 + "'."); // CS
                                                                                                // goP.TraceLine("Return False", "", sProc)
                    return false;
                }
                else
                {
                    goP.DeleteVar("bDoNotUpdateQuoteLines");
                    goP.DeleteVar("bDoNotRecalcQuoteTotal"); // CS 11/6/09
                    if (!(doRS == null))
                        doRS = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)
            par_doCallingObject = doRS;
            return true;
        }

        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 10/3/2012 Disable Merged checkbox
            doForm.SetControlState("CHK_Merged", 4);
            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_FormOnSave_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 10/3/2012 Updated
            // TLD 10/5/2010 Updated for clicking Cancel on Merge
            // If doForm.oVar.GetVar("CancelSave") = "1" Then
            // goP.SetVar("CN_Merge", "")
            // doForm.oVar.SetVar("CancelSave", "")
            // Return False
            // End If
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            par_doCallingObject=doForm;
            return true;
        }

        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // doForm.doRS.SetFieldVal("lnk_modifiedby_us", goP.GetMe("gid_id"))

            // TLD 10/3/2012 Mod to not allow merge record to itself
            // and use generic messagebox
            // 'PJ 3/26/09 Added merge functionality
            // If doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0 Then
            // If doForm.doRS.GetFieldVal("CHK_Merged", 2) = 0 Then
            // If goP.GetVar("CN_Merge") <> "1" Then
            // goP.SetVar("CN_Merge", "1")
            // doForm.MessageBox("This contact will be merged to the contact, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") & "'. Blank fields on the target contact will be filled from this contact record and all links will be copied to the target contact. Are you sure you want to merge this contact?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "MergeCN")
            // Return True
            // End If
            // End If
            // End If
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CN_Merge")) != "1")
                    {
                        // Don't allow merge of contact to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID"))
                            doForm.MessageBox("You cannot merge a contact to itself.  Please select a different merge to contact.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CN", "MergeFail");
                        else
                            doForm.MessageBox("This contact will be merged to the target contact, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") + "'. Blank fields on the target contact will be filled from this contact record and all links will be copied to the target contact. Are you sure you want to merge this contact?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CN", "Merge");
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_CreateActLog_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // goP.TraceLine("In " & sProc, "", sProc)

            // PURPOSE:
            // TLD 5/27/2009 Adds Act Log with new notes of Company.
            // RETURNS:
            // 1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.

            // 2004/12/08 10:03:25 MAR Considering changes to Journal instead of Notes.

            Form doForm = (Form)par_doCallingObject;
            string sNotes;
            string sWork;
            int lWork;

            // If answered Yes on MB to create a journal, then go directly to script that creates journal.
            // If UCase(par_s1) = "YES" Then
            // GoTo CREATEACTLOG
            // End If

            string sMessage;

            if (Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")) <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))
                return true;
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Company is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);
                doForm.oVar.SetVar("CO_CreateActLog_Ran", "1");
                // Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                return true;
            }
            // If doForm.GetMode() = "CREATION" Then Return True		'The record must be in edit mode so we have its ID for links to it.
            doForm.oVar.SetVar("CO_CreateActLog_Ran", "1");

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, 0/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm.doRS.bBypassValidation);

            // ==> Use goData:CopyAllLinks() method instead of copying links one by one?

            sWork = Convert.ToString(doForm.doRS.GetFieldVal("MMO_JOURNAL"));
            // goP.TraceLine("Length of sWork: '" & Len(sWork) & "'", "", sProc)
            // goP.TraceLine("Val of GetVar of lLenJournal: '" & Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")) & "'", "", sProc)
            // goP.TraceLine("Length - Val = '" & (Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"))) & "'", "", sProc)
            // goP.TraceLine("lWork (Left of sWork for Length - Val): '" & Left(sWork, (Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))) & "'", "", sProc)
            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, lWork);
            // goP.TraceLine("sNotes (Left(sWork, lWork)): '" & sNotes & "'", "", sProc)
            sNotes = sNotes + "== Created from Company '" + doForm.doRS.GetFieldVal("SYS_Name") + "'";
            // goP.TraceLine("Full sNotes: '" & sNotes & "'", "", sProc)
            // goP.TraceLine("Setting field MMO_NOTES '" & sNotes & "'", "", sProc)
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            // CS goP.TraceLine("Setting field LNK_Involves_US '" & goP.GetMe("ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            // goP.TraceLine("Setting field MLS_Status '" & 1 & "'", "", sProc)
            doNew.SetFieldVal("MLS_Status", 1, 2);     // Completed
                                                       // goP.TraceLine("Setting field TME_STARTTIME '" & "Now" & "'", "", sProc)
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            // goP.TraceLine("Setting field TME_ENDTIME '" & "Now" & "'", "", sProc)
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            // goP.TraceLine("Setting field DTE_STARTTIME '" & "Today" & "'", "", sProc)
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            // goP.TraceLine("Setting field DTE_ENDTIME '" & "Today" & "'", "", sProc)
            doNew.SetFieldVal("DTE_ENDTIME", "Today");
            // goP.TraceLine("Setting field LNK_CreditedTo_US '" & goP.GetMe("ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));
            // TLD 5/27/2009 Commented -- do NOT fill contact on AC
            // doLink = doForm.doRS.GetLinkVal("LNK_Related_CN", doLink)
            // doNew.SetLinkVal("LNK_Related_CN", doLink)
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Foam_PD", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_PD", doLink);
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Related_GR", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_GR", doLink);
            // goP.TraceLine("Setting field EML_EMAIL '" & doForm.doRS.GetFieldVal("EML_EMAIL") & "'", "", sProc)
            // doNew.SetFieldVal("EML_EMAIL", doForm.doRS.GetFieldVal("EML_EMAIL"))
            // goP.TraceLine("Setting field TEL_FAX '" & doForm.doRS.GetFieldVal("TEL_FAX") & "'", "", sProc)
            doNew.SetFieldVal("TEL_FAX", doForm.doRS.GetFieldVal("TEL_FAXNO"));
            // TLD 5/27/2009 Gets ID of CO Record
            // doLink = doForm.doRS.GetLinkVal("LNK_Related_CO", doLink)
            doNew.SetFieldVal("LNK_Related_CO", doForm.doRS.GetFieldVal("GID_ID"));
            // goP.TraceLine("Setting field MLS_TYPE '" & 31 & "'", "", sProc)
            doNew.SetFieldVal("MLS_TYPE", 31, 2);      // Journal
                                                       // goP.TraceLine("Setting field MMO_HISTORY '" & goTR.WriteLogLine(doForm.GetFieldVal("MMO_HISTORY"), "Created.") & "'", "", sProc)
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine("", "Created."));

            // goP.TraceLine("Setting field LNK_Connected_AC '" & doForm.GetFieldVal("GID_ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_Related_AC", doForm.doRS.GetCurrentRecID());
            // goP.TraceLine("About to commit.", "", sProc)

            if (doNew.Commit() != 1)
            {
                // delete(doNew)
                // delete(doLink)
                doNew = null;
                doLink = null;

                string sError = goErr.GetLastError();
                // Dim sMessage As String
                sMessage = goErr.GetLastError("MESSAGE");
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);

                goLog.Log("CO_CreateActLog", Convert.ToString(doForm.oVar.GetVar("ScriptMessages")), -1, true, true);

                return false;
            }

            doNew = null;
            doLink = null;

            par_doCallingObject = doForm;
            return true;
        }


        public bool CO_FormControlOnChange_BTN_INSERTLINE_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)
            Form doForm = (Form)par_doCallingObject;
            // Dim sWork As String
            string sParams = "";
            string sDateStamp = "";

            // TLD 5/27/2009 CO journal functionality

            // GetDateTimeStamp parameters:
            // par_s1: Format of the date and time. Supported values:
            // NEUTRAL		'Default: Ex: '2004-09-24 16:37 GMT' if par_s4 is 'UTC'.
            // REGIONAL	'Same as NEUTRAL until regional formatting is implemented.
            // par_s2: Name of the variable in which to return the date/time stamp.
            // par_s3: Format of the user code to include. Supported values:
            // NONE		'Default. Add no user code.
            // CODE		'User Code.
            // par_s4: Time zone (UTC (GMT) is the default). Supported values:
            // UTC         'Default
            // UTCNOOFFSETLABEL
            // USER        
            // USERNOOFFSETLABEL
            // SERVER  
            // SERVERNOOFFSETLABEL

            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", null, "CODE", "USERNOOFFSETLABEL", null); // returns var sDateStamp

            doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", "Cancel", null/* Conversion error: Set to default value for this argument */, sDateStamp + " ", "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, System.Reflection.MethodInfo.GetCurrentMethod().Name);

            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sProducts = "";
            int i = 0;

            // TLD 10/3/2012 Disable Merged checkbox
            doForm.SetControlState("CHK_Merged", 4);

            // TLD 5/27/2009 Auto fills LNK_FOAM_PD if blank
            if (doForm.doRS.GetLinkCount("LNK_FOAM_PD") == 0)
            {
                clRowSet doProducts = new clRowSet("PD", 3, "LNK_RELATED_GR%%TXT_GROUPNAME['foam concentrate'", null, "GID_ID");
                if (doProducts.GetFirst() ==1)
                {
                    do
                    {
                        if (sProducts == "")
                            sProducts = Convert.ToString(doProducts.GetFieldVal("GID_ID"));
                        else
                            sProducts = sProducts + Constants.vbCrLf + doProducts.GetFieldVal("GID_ID");
                        if (doProducts.GetNext() == 0)
                            break;
                    }
                    while (true);
                    doForm.doRS.SetFieldVal("LNK_FOAM_PD", sProducts);
                    doProducts = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }

            // TLD 5/27/2009 Sets var for co journal functionality
            doForm.oVar.SetVar("lLenJournal", Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")));
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sGroup = goMeta.PageRead("GLOBAL", "OTH_CUSTOM_CO_TEAMLEADER_GROUP");
            string sGroupID = "";
            bool bEnforceTL = true;
            string sTeamLeaderID = Convert.ToString(doForm.doRS.GetFieldVal("LNK_TeamLeader_US"));

            // TLD 10/3/2012 Updated
            // 'TLD 10/5/2010 Updated for clicking Cancel on Merge
            // If doForm.oVar.GetVar("CancelSave") = "1" Then
            // goP.SetVar("CO_Merge", "")
            // doForm.oVar.SetVar("CancelSave", "")
            // Return False
            // End If
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }

            // TLD 7/6/2012 -----------Enforce Team Leader as Outside Sales group member
            // If selected Team Leader is NOT a member of
            // the Team Leader - Outside Sales Users group, force user
            // to select one that is -- name written to MD in
            // OTH_CUSTOM_CO_TEAMLEADER_GROUP
            if (sTeamLeaderID != "")
            {
                clRowSet doGroup;
                if (sGroup == "")
                    // No Group in MD, try using Team Leader & Name of Group
                    // Try to get RS by Group Name?
                    doGroup = new clRowSet("GR", 3, "LNK_RefersTo_US='" + sTeamLeaderID + "' AND TXT_GroupName='Team Leader - Outside Sales Users'", null/* Conversion error: Set to default value for this argument */, "GID_ID", 1);
                else
                {
                    // Get Group ID from MD
                    sGroupID = goTR.StrRead(sGroup, "TEAMLEADERGROUPID", "", false);
                    // See if this user is a member of this group
                    doGroup = new clRowSet("GR", 3, "LNK_RefersTo_US='" + sTeamLeaderID + "' AND GID_ID='" + sGroupID + "'", null/* Conversion error: Set to default value for this argument */, "GID_ID", 1);
                }
                if (doGroup.GetFirst() == 1)
                    bEnforceTL = false;
            }

            if (bEnforceTL)
            {
                doForm.MessageBox("The team leader must be an outside sales user.");
                return false;
            }
            // TLD 7/6/2012 -----------Enforce Team Leader as Outside Sales group member

            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            bool bResult;

            // doForm.doRS.SetFieldVal("lnk_modifiedby_us", goP.GetMe("gid_id"))

            // TLD 5/27/2009 Added AC Journal capability
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CreateJournalActivity"))
            {
                // If doForm.GetMode() <> "CREATION" and doForm.GetFieldVal("MLS_Purpose",2) = 8 Then	'8=Lead
                // Not testing for lead, creating Activity whenever a Journal entry has been added.
                // BKG OKed this 2/16/05
                // If doForm.GetMode() <> "CREATION" Then
                // check if we already ran this script. If so don't run it again.
                if (Convert.ToString(doForm.oVar.GetVar("CO_CreateActLog_Ran")) != "1")
                {
                    if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVECO_CREATE_AC")) == "1")
                        bResult = scriptManager.RunScript("CO_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                }
            }

            // TLD 10/3/2012 Mod to not allow merge record to itself
            // and use generic messagebox
            // 'PJ 3/26/09 Added merge functionality
            // If doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0 Then
            // If doForm.doRS.GetFieldVal("CHK_Merged", 2) = 0 Then
            // If goP.GetVar("CO_Merge") <> "1" Then
            // goP.SetVar("CO_Merge", "1")
            // doForm.MessageBox("This company will be merged to the target company, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") & "'. Blank fields on the target company will be filled from this company record and all links will be copied to the target company. Are you sure you want to merge this company?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "MergeCO")
            // Return True
            // End If
            // End If
            // End If
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CO_Merge")) != "1")
                    {
                        // Don't allow merge of company to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID"))
                            doForm.MessageBox("You cannot merge a company to itself.  Please select a different merge to company.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CO", "MergeFail");
                        else
                            doForm.MessageBox("This company will be merged to the target company, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") + "'. Blank fields on the target company will be filled from this company record and all links will be copied to the target company. Are you sure you want to merge this company?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CO", "Merge");
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool FIND_FormControlOnChange_BTN_QTSearch_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            // CS 8/17/09: Do NOT consider already saved filter of the desktop.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 7/22/2010 Prevent main from running
            par_bRunNext = false;

            Form oForm = (Form)par_doCallingObject;

            // Find dialog; Quote tab Search button
            string sQuoteNo;
            string sQuoteRefNo;
            string sCompany;
            string sCreditedTo;
            // TLD 7/22/2010 Added for 3 new search fields
            string sPRLocation;
            string sPRName;
            string sEndUser;
            // TLD 10/8/2010 Added for QL Model Name
            string sModel;
            // TLD 11/12/2010 Added for QL Product
            string sProduct;

            string sView;
            int iCondCount = 0;
            string sViewCondition;
            string sNewCondition;
            int iOrigCondCount = 0;
            int i;
            string sFilter = "";

            // Get values from form
            sQuoteNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_QUOTENO"));
            sQuoteRefNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_QUOTEREFNO"));
            sCompany = oForm.GetControlVal("NDB_TXT_COMPANY");
            sCreditedTo = oForm.GetControlVal("NDB_TXT_CREDITEDTO");
            // TLD 7/22/2010 Added for 3 new search fields
            sPRLocation = Strings.Trim(oForm.GetControlVal("NDB_TXT_PRLOCATION"));
            sPRName = Strings.Trim(oForm.GetControlVal("NDB_TXT_PRNAME"));
            sEndUser = Strings.Trim(oForm.GetControlVal("NDB_TXT_ENDUSER"));
            // TLD 10/8/2010 Added for QL Model Name
            sModel = Strings.Trim(oForm.GetControlVal("NDB_TXT_MODEL"));
            // TLD 11/12/2010 Added for QL Product Name
            sProduct = Strings.Trim(oForm.GetControlVal("NDB_TXT_PRODUCT"));

            // Use values to filter Quote - Search Results desktop if it exists
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_D8E2511C-031F-45DD-5858-9AF500ECEC44");
            // Edit views in DT

            // View 1:Quote - Search Results
            sView = oDesktop.GetViewMetadata("VIE_4D836010-021D-4AD9-5858-9AF500ECEC44");
            // iCondCount = goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"))


            // 'If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            // If iCondCount = 1 Then
            // If goTR.StrRead(sView, "C1FIELDNAME") = "<%ALL%>" Then
            // iCondCount = 0 'Will overwrite these values
            // End If
            // End If
            // Original condition count
            iOrigCondCount = iCondCount;

            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (sQuoteNo != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sQuoteRefNo != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sCompany != "")
            {
                iCondCount = iCondCount + 1;
            }
                
            if (sCreditedTo != "")
            {

                iCondCount = iCondCount + 1;
                // TLD 10/8/2010 Added for QL Model
            }
            if (sModel != "")
            {
                iCondCount = iCondCount + 1;
                // TLD 11/12/2010 Added for QL Product
            }

            if (sProduct != "")
            {
                iCondCount = iCondCount + 1;

            }

            // Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (sQuoteNo != "")
            {
                // Add 'Quote No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_QUOTENO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sQuoteNo);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_QUOTENO['" + sQuoteNo + "'";

                }
                else
                {
                    sFilter = "TXT_QUOTENO['" + sQuoteNo + "'";

                }
            }
            if (sQuoteRefNo != "")
            {
                // Add 'Quote Ref No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_QUOTEREFNO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sQuoteRefNo);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_QUOTEREFNO['" + sQuoteRefNo + "'";

                }
                else
                {
                    sFilter = "TXT_QUOTEREFNO['" + sQuoteRefNo + "'";

                }
            }
            if (sCompany != "")
            {
                // Add 'To Company' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_TO_CO%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCompany);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND LNK_TO_CO%%SYS_NAME['" + sCompany + "'";

                }
                else
                {
                    sFilter = "LNK_TO_CO%%SYS_NAME['" + sCompany + "'";

                }
            }
            if (sCreditedTo != "")
            {
                // Add 'Credited To User' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_CREDITEDTO_US%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCreditedTo);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND LNK_CREDITEDTO_US%%SYS_NAME['" + sCreditedTo + "'";

                }
                else
                {
                    sFilter = "LNK_CREDITEDTO_US%%SYS_NAME['" + sCreditedTo + "'";

                }
            }
            if (sPRLocation != "")
            {
                // TLD 7/22/2010 Add 'Project Location' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_PROJECTLOCATION%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sPRLocation);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_PROJECTLOCATION['" + sPRLocation + "'";

                }
                else
                {
                    sFilter = "TXT_PROJECTLOCATION['" + sPRLocation + "'";

                }
            }
            if (sPRName != "")
            {
                // TLD 7/22/2010 Add 'Project Name' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_PROJECTNAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sPRName);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_PROJECTNAME['" + sPRName + "'";

                }
                else
                {
                    sFilter = "TXT_PROJECTNAME['" + sPRName + "'";

                }
            }
            if (sEndUser != "")
            {
                // TLD 7/22/2010 Add 'End User' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_ENDUSER%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sEndUser);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_ENDUSER['" + sEndUser + "'";

                }
                else
                {
                    sFilter = "TXT_ENDUSER['" + sEndUser + "'";

                }
            }
            // TLD 10/8/2010 Added for QL Model Name
            if (sModel != "")
            {
                // Add 'QL Model' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_CONNECTED_QL%><%LNK_FOR_MO%><%TXT_MODELNAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sModel);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND LNK_CONNECTED_QL%%LNK_FOR_MO%%TXT_MODELNAME['" + sModel + "'";

                }
                else
                {
                    sFilter = "LNK_CONNECTED_QL%%LNK_FOR_MO%%TXT_MODELNAME['" + sModel + "'";

                }
            }
            // TLD 11/12/2010 Added for QL Product Name
            if (sProduct != "")
            {
                // Add 'QL Product' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_CONNECTED_QL%><%LNK_FOR_MO%><%LNK_OF_PD%><%TXT_PRODUCTNAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sProduct);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND LNK_CONNECTED_QL%%LNK_FOR_MO%%LNK_OF_PD%%TXT_PRODUCTNAME['" + sProduct + "'";

                }
                else
                {
                    sFilter = "LNK_CONNECTED_QL%%LNK_FOR_MO%%LNK_OF_PD%%TXT_PRODUCTNAME['" + sProduct + "'";

                }
            }


            // Edit CONDITION= line in view MD
            // sViewCondition = goTR.StrRead(sView, "CONDITION")
            // If sViewCondition = "" Then
            sNewCondition = sFilter; // No filter in view already
                                     // Else
                                     // sNewCondition = sViewCondition & " AND " & sFilter
                                     // End If
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_4D836010-021D-4AD9-5858-9AF500ECEC44", sView);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;

            // Que OP Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);
           //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
            goUI.Queue("","");

            par_doCallingObject = oForm;
            return true;
        }


        public bool MergeRecord(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + Convert.ToString(doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName())) + "'", null/* Conversion error: Set to default value for this argument */, "**", 0/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true, true, true/* Conversion error: Set to default value for this argument */, true/* Conversion error: Set to default value for this argument */, 0/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true);
                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Microsoft.VisualBasic.Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField).ToString() == "")
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));

                                    }
                                    break;
                                }

                            case "MMO":
                                {
                                    // Append
                                    if (Convert.ToString(doRSMergeTo.GetFieldVal(sField)) == "")
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));

                                    }
                                    else
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMergeTo.GetFieldVal(sField) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + doRSMerge.GetFieldVal("SYS_Name") + " ==" + Constants.vbCrLf + doRSMerge.GetFieldVal(sField));

                                    }
                                    break;
                                }

                            case "CHK":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }

                            case "MLS":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9).ToString());
                        if (sLinkType[4] == "NN" | sLinkType[1] == "2")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (Convert.ToString(doRSMergeTo.GetFieldVal(aLinks.GetItem(i))) == "")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);
                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;
            par_doCallingObject = doRSMerge;
            return true;
        }

        public bool GenerateSysName_Post(ref object par_doCallingObject , ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";

            // We assume that sFileName is valid. If this is a problem, test it here and SetError.

            switch (Strings.UCase(sFileName))
            {
                case "QD":
                    {
                        if (!doRS.IsLoaded("LNK_Related_QT"))
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_Related_QT");

                        sResult = Convert.ToString(doRS.GetFieldVal("LNK_Related_QT%%SYS_NAME"));

                        sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
                        sResult = goTR.Replace(sResult, Constants.vbTab, " ");
                        par_oReturn = sResult;
                        break;
                    }

                case "QE":
                    {
                        if (!doRS.IsLoaded("LNK_Connected_QL"))
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_Connected_QL");

                        sResult = Convert.ToString(doRS.GetFieldVal("LNK_Connected_QL%%SYS_NAME"));
                        sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
                        sResult = goTR.Replace(sResult, Constants.vbTab, " ");
                        if (sResult == "")
                            sResult = "Quote Line Detail";


                        par_oReturn = sResult;
                        break;
                    }

                case "ML":
                    {
                        if (!doRS.IsLoaded("LNK_CONNECTED_MO"))
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_CONNECTED_MO");

                        sResult = Convert.ToString(doRS.GetFieldVal("TXT_MODELDETAILNAME"));
                        sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
                        sResult = goTR.Replace(sResult, Constants.vbTab, " ");
                        par_oReturn = sResult;
                        break;
                    }

                case "PP":
                    {
                        if (!doRS.IsLoaded("TXT_ProjectPurposeName"))
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_ProjectPurposeName");
                        sResult = Convert.ToString(doRS.GetFieldVal("TXT_ProjectPurposeName"));
                        sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
                        sResult = goTR.Replace(sResult, Constants.vbTab, " ");
                        par_oReturn = sResult;
                        break;
                    }
            }

            par_doCallingObject = doRS;
            return true;
        }

        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }
        public bool MO_FormControlOnChange_LNK_CONNECTED_ML_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/30/2009 Calls script to manage control state
            scriptManager.RunScript("MO_ManageControlState", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            par_doCallingObject = doForm;
            return true;
        }

        public bool MO_FormOnLoadRecord_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/30/2009 Calls script to manage control state
            scriptManager.RunScript("MO_ManageControlState", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doForm;
            return true;
        }

        public bool MO_ManageControlState_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/30/2009 Protects them from themselves
            // Disable Add Cost button if Model Cost Detail link
            // alread there -- they were connecting multiple
            // cost records for some reason
            if (doForm.doRS.IsLinkEmpty("LNK_CONNECTED_ML") == true)
            {
                // Enable Add cost checkbox
                doForm.SetControlState("CHK_CREATECOST", 0);
            }     
            else
            {
                // Disable, cost already attached
                doForm.SetControlState("CHK_CREATECOST", 4);
            }
               

            par_doCallingObject = doForm;
            return true;
        }


        public bool MO_RecordOnSave_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Dim doForm As clForm = par_doCallingObject
            clRowSet doRS = (clRowSet)par_doCallingObject;
            if (Convert.ToInt32(doRS.GetFieldVal("CHK_CREATECOST", 2)) == 1)
            {
                scriptManager.RunScript("Model_CreateCost",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections,null,"","","","","");

            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool MessageBoxEvent_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 4/24/2009 
            // When Complete All checked on Corr In Box - My
            // par_callingObject is clDesktop, so throws an error
            // so changed per Christy
            // Dim doForm As clForm = par_doCallingObject
            Form doForm = (Form)par_doCallingObject;
            string sID;
            string sWork; // input value
            string sJournal; // original value in journal field

            switch (Strings.UCase(par_s5))
            {
                case "CO_FORMCONTROLONCHANGE_BTN_INSERTLINE_POST":
                    {
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMO_Journal"));
                                    sWork = par_s2;
                                    if (sWork != "")
                                    {
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                        doForm.MoveToField("MMO_JOURNAL");
                                    }

                                    break;
                                }
                        }

                        break;
                    }

                case "PR_FORMONSAVE_POST_UPDATEVALUE":
                    {
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    doForm.doRS.SetFieldVal("CUR_VALUE", par_s4);
                                    break;
                                }
                        }

                        break;
                    }

                case "QT_DELETEQL":
                    {
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    string sRecID = doForm.GetLinkSelection("LNK_Connected_QL");
                                    clRowSet doSource = new clRowSet("QL", 1, "GID_ID = '" + sRecID + "'", null/* Conversion error: Set to default value for this argument */, "**");
                                    if (doSource.GetFirst() == 1)
                                    {
                                        // Check if have delete perm
                                        if (goData.GetRecordPermission(sRecID, "D") == false)
                                        {
                                            doForm.MessageBox("You do not have permission to delete Quote Lines.");
                                            return true;
                                        }
                                        // CS 5/6/09: Set variable that will tell us we are coming from
                                        // the form. THis is used in QL_RecordBeforeDelete/AfterDelete
                                        goP.SetVar("QLDeletedFromForm", "1");
                                        if (doSource.DeleteRecord() != 1)
                                            return false;
                                        doForm.doRS.ClearLink("LNK_CONNECTED_QL", sRecID);
                                        doForm.doRS.UpdateLinkState("LNK_Connected_QL");
                                        scriptManager.RunScript("Quote_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections); // This updates the QT totals on the form (were already updated on disk from the QL RecordAfterDelete script)
                                    }
                                    else
                                    {
                                        doForm.MessageBox("The Quote Line you selected no longer exists. It may have gotten deleted since opening the record.");

                                    }
                                    break;
                                }

                            case "NO":
                                {
                                    return false;
                                }
                        }

                        break;
                    }

                case "QT_FORMONSAVE_POST_UPDATEOP":
                    {
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // Discontinue save, let user manually change OP statuses
                                    doForm.MoveToTab(12);
                                    doForm.MoveToField("LNK_RELATED_OP");
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    doForm.oVar.SetVar("OPQuestionRan", "1");
                                    break;
                                }

                            case "NO":
                                {
                                    // continue save
                                    doForm.oVar.SetVar("OPQuestionRan", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "QT_FORMONSAVE_POST_UPDATEVALUE":
                    {
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // Get linked Project and update value with Quote's total
                                    sID = par_s4;
                                    if (sID != "")
                                    {
                                        // TLD 10/5/2010 Mod RS to optimize
                                        // Dim doRS As New clRowSet("PR", 1, "GID_ID='" & sID & "'")
                                        clRowSet doRS = new clRowSet("PR", 1, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "*");
                                        if (doRS.GetFirst() == 1)
                                        {
                                            doRS.SetFieldVal("CUR_VALUE", doForm.doRS.GetFieldVal("CUR_TOTAL"));
                                            // TLD 10/5/2010 Mod to write to XL Log instead of error
                                            // If doRS.Commit <> 1 Then
                                            // 'Error updating linked Project
                                            // goErr.SetError(35000, sProc, "Unable to update linked Project value.")
                                            // Return False
                                            // End If
                                            if (doRS.Commit() == 0)
                                            {
                                                if (goErr.GetLastError("NUMBER") == "E47250")
                                                {
                                                    // failed due to permissions, write to log
                                                    goLog.Log(sProc, "QT update of connected PR Value failed for PR '" + doRS.GetFieldVal("TXT_PROJECTNAME") + " due to permissions.'", clC.SELL_LOGLEVEL_NONE, true);
                                                }
                                                    
                                                else
                                                {
                                                    // write message to log with error
                                                    goLog.Log(sProc, "QT update of connected PR Value failed for PR '" + doRS.GetFieldVal("TXT_PROJECTNAME") + " with error " + goErr.GetLastError("NUMBER") + "'", clC.SELL_LOGLEVEL_NONE, true);
                                                }
                                            }
                                                    
                                        }
                                    }

                                    break;
                                }
                        }

                        break;
                    }

                case "QL_FORMONSAVE_POST_MARGIN":
                    {
                        switch (Strings.UCase(par_s1))
                        {
                            case "NO":
                                {
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "QL_FORMCONTROLONCHANGE_BTN_INSERTSPEC_PRE":
                    {
                        par_bRunNext = false;
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    scriptManager.RunScript("AddTextToField", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "MMO_DETAILS", Convert.ToString(doForm.doRS.GetFieldVal(par_s4)), "PREPEND");
                                    break;
                                }
                        }

                        break;
                    }

                case "QL_FORMCONTROLONCHANGE_CHK_UPDATELISTPRICE":
                    {
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    doForm.doRS.SetFieldVal("CUR_PRICEUNIT", doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_PRICE"));
                                    break;
                                }
                        }

                        break;
                    }

                case "MERGE":
                    {
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // run merge script, continue save
                                    scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections,null,"","","","","");
                                    break;
                                }

                            case "NO":
                                {
                                    // Clear merged to co linkbox, continue save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    break;
                                }

                            case "CANCEL":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "MERGEFAIL":
                    {
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool Model_CreateCost(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doMORS = (clRowSet)par_doCallingObject;
            clRowSet doMLRS;

            // Values from CN record
            string sModelID = Convert.ToString(doMORS.GetFieldVal("GID_ID"));
            string sModelName = Convert.ToString(doMORS.GetFieldVal("TXT_MODELNAME"));
            decimal cCost = Convert.ToDecimal(doMORS.GetFieldVal("cur_cost"));

            doMLRS = new clRowSet("ML", 2, null, null, null, 0, null, null, null, null, null, true, true);
            doMLRS.SetFieldVal("TXT_ModelDetailName", sModelName);
            doMLRS.SetFieldVal("LNK_Connected_MO", sModelID);
            doMLRS.SetFieldVal("CUR_Cost", cCost);
            doMLRS.SetFieldVal("SYS_NAME", sModelName);

            doMLRS.Commit();

            // Set Create Cost to false and MO cost to 0
            doMORS.SetFieldVal("CHK_CreateCost", 0, 2);
            doMORS.SetFieldVal("Cur_Cost", 0);

            par_doCallingObject = doMORS;
            return true;
        }

        public bool OP_FormControlOnChange_LNK_FOR_PD_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 3/22/2011 Call script to filter Product fields
            scriptManager.RunScript("Opp_FilterProducts", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            int i;

            // TLD 11/17/2008 Opens on Details tab if New Op
            if (doForm.GetMode() == "CREATION")
                doForm.MoveToTab(2);

            // TLD 11/19/2008 Added to gray out 14 value fields
            for (i = 2; i <= 15; i++)
                doForm.SetControlState("cur_value" + i, 4);

            // TLD 3/22/2011 Call script to filter Product fields
            scriptManager.RunScript("Opp_FilterProducts", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //string par_sDateTimeDelim = "|";
            Form doForm = (Form)par_doCallingObject;
            int iLeadTime = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_LEADTIME", 2));
            string sExpCloseDate = Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));

            // TLD 1/30/2009 Sets OPSHIPDATE to Expected Close Date + Lead Time if Expected Close Date and Lead Time are not blank or 0
            if (sExpCloseDate != Convert.ToString(goTR.StringToDateTime(clC.SELL_BLANK_DATETIME,"","",ref par_iValid,"|",false)) & iLeadTime != 0)
            {
                DateTime _dt = Convert.ToDateTime(goTR.StringToDate(sExpCloseDate, "", ref par_iValid));
                sExpCloseDate = _dt.AddDays(iLeadTime * 7).ToString(); //goTR.AddDay.Convert.ToInt32(iLeadTime) * 7);
                doForm.doRS.SetFieldVal("DTE_OPSHIPDATE", sExpCloseDate);
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_sSalesProcess = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_sSalesProcess: 1 or 0 (default): if 1, use checkboxes in the Sales Process tab
            // to calculate probability %, else just calculate value and value index.
            // 2 to calc both
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // CUSTOMIZATION: CHANGE VALUES OF %'S

            par_bRunNext = false;

            Form doForm = null;
            clRowSet doRS1 = null;

            // Check if we passed doForm or doRs to the script. We pass doRs from OP_RecordOnsave. This 
            // allows calculating value/prob on save of the form. In all other cases it is a form (clicking Calculate
            // buttons for example).
            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;

            }
            else
            {
                doForm = (Form)par_doCallingObject;

            }

            long lStatus;
            double rProb;
            decimal cValueFld;
            decimal cTotValue;
            double rQty;
            double rNewValue;
            double rQ03Worth = 6.25;  // Needs Ass                      CHK_Q01
            double rQ07Worth = 6.25;  // Can Kff Meet Need             CHK_Q02
            double rQ10Worth = 6.25;  // CEO Contacted           CHK_Q03
            double rQ20Worth = 6.25;  // Front-End Buy In        CHK_Q04
            double rQ30Worth = 6.25;  // Influences IDd          CHK_Q05
            double rQ35Worth = 6.25;  // Needs Assessed          CHK_Q06
            double rQ37Worth = 6.25;  // Approved/Funded         CHK_Q07
            double rQ40Worth = 6.25;  // Competition IDd         CHK_Q08
            double rQ50Worth = 12.5;  // Champion Built          CHK_Q09
            double rQ60Worth = 6.25;  // Decision Process        CHK_Q10
            double rQ65Worth = 6.25;  // Timing Estimate         CHK_Q11
            double rQ70Worth = 12.5;  // Key Questions           CHK_Q12
            double rQ75Worth = 6.25;  // Present/Demo                  CHK_Q13
            double rQ80Worth = 12.5;  // Quote                   CHK_Q14
            double rQ85Worth = 6.25;  // Mark Status             CHK_Q15

            bool bQ03;
            bool bQ07;
            bool bQ10;
            bool bQ20;
            bool bQ30;
            bool bQ35;
            bool bQ37;
            bool bQ40;
            bool bQ50;
            bool bQ60;
            bool bQ65;
            bool bQ70;
            bool bQ75;
            bool bQ80;
            bool bQ85;

            // TLD 11/17/2008 Added for multi product selections
            int i;

            if (par_s2 == "doRS")
            {
                // TLD 11/17/2008 Modified to allow for 15 Product fields and prices
                // -----      Calculate Value for First Product
                rProb = Convert.ToDouble(doRS1.GetFieldVal("SI__PROBABILITY", 2));
                cValueFld = Convert.ToDecimal(doRS1.GetFieldVal("CUR_UNITVALUE", 2));
                if (!goTR.IsNumber(Convert.ToString(cValueFld)))
                {
                    cValueFld = 0;

                }
                rQty = Convert.ToDouble(doRS1.GetFieldVal("SR__QTY"));
                if (!goTR.IsNumeric(rQty))
                {
                    rQty = 0;

                }
                rNewValue = Convert.ToDouble(cValueFld) * rQty;
                doRS1.SetFieldVal("CUR_VALUE", rNewValue, 2);
                cTotValue = Convert.ToDecimal(rNewValue);

                // TLD 11/17/2008 Added 14 N1 product fields, need to calculate unit prices
                // ----- Loops thru and Calculates 14 other Value and Velud Index fields
                for (i = 2; i <= 15; i++)
                {
                    cValueFld = Convert.ToDecimal(doRS1.GetFieldVal("CUR_UNITVALUE" + i, 2));
                    if (!goTR.IsNumber(Convert.ToString(cValueFld)))
                    {
                        cValueFld = 0;

                    }
                    rQty = Convert.ToDouble(doRS1.GetFieldVal("SR__QTY" + i));
                    if (!goTR.IsNumeric(rQty))
                    {
                        rQty = 0;

                    }
                    rNewValue = Convert.ToDouble(cValueFld) * rQty;
                    doRS1.SetFieldVal("CUR_VALUE" + i, rNewValue, 2);
                    cTotValue = Convert.ToDecimal(cTotValue) + Convert.ToDecimal(rNewValue);
                }

                // TLD 11/17/2008 to calculate total value index
                // ------ Calculates a total value index
                doRS1.SetFieldVal("CUR_TOTVALUE", cTotValue);
                doRS1.SetFieldVal("CUR_VALUEINDEX", Convert.ToDouble(cTotValue) * (Math.Round(rProb, 0) / 100), 2);   // Value Index
            }
            else
            {
                // Working on a form
                // Get Probability % value from form
                rProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY", 2));
                // Calc probability and value index
                lStatus = Convert.ToInt64(doForm.doRS.GetFieldVal("MLS_STATUS", 2));
                // goP.TraceLine("lStatus is " & lStatus, "", sProc)
                switch (lStatus)
                {
                    case 0:
                    case 1:
                    case 6    // Status = Open, On-hold, Quoted
                   :
                        {
                            // goP.TraceLine("par_sSalesProcess: '" & par_sSalesProcess & "'", "", sProc)
                            if (par_sSalesProcess == "1")
                            {
                                // Calc based on sales process prompts
                                bQ03 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q01", 2));
                                bQ07 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q02", 2));
                                bQ10 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q03", 2));
                                bQ20 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q04", 2));
                                bQ30 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q05", 2));
                                bQ35 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q06", 2));
                                bQ37 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q07", 2));
                                bQ40 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q08", 2));
                                bQ50 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q09", 2));
                                bQ60 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q10", 2));
                                bQ65 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q11", 2));
                                bQ70 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q12", 2));
                                bQ75 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q13", 2));
                                bQ80 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q14", 2));
                                bQ85 = Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_Q15", 2));

                                rProb = 0;

                                if (bQ03)
                                {
                                    rProb += rQ03Worth;

                                }
                                if (bQ07)
                                {
                                    rProb += rQ07Worth;

                                }
                                if (bQ10)
                                {
                                    rProb += rQ10Worth;

                                }
                                if (bQ20)
                                {
                                    rProb += rQ20Worth;

                                }
                                if (bQ30)
                                {
                                    rProb += rQ30Worth;

                                }
                                if (bQ35)
                                {
                                    rProb += rQ35Worth;

                                }
                                if (bQ37)
                                {
                                    rProb += rQ37Worth;

                                }
                                if (bQ40)
                                {
                                    rProb += rQ40Worth;

                                }
                                if (bQ50)
                                {
                                    rProb += rQ50Worth;

                                }
                                if (bQ60)
                                {
                                    rProb += rQ60Worth;

                                }
                                if (bQ65)
                                {
                                    rProb += rQ65Worth;

                                }
                                if (bQ70)
                                {
                                    rProb += rQ70Worth;

                                }
                                if (bQ75)
                                {
                                    rProb += rQ75Worth;

                                }
                                if (bQ80)
                                {
                                    rProb += rQ80Worth;

                                }
                                if (bQ85)
                                {
                                    rProb += rQ85Worth;

                                }
                                // Leaving the top 10% for the 'Won' Status
                                // Leaving the bottom 10% when no check-boxes are checked
                                rProb = 10 + (rProb * 0.8);
                            }

                            break;
                        }

                    case 2      // Status = Won
             :
                        {
                            // Set probability to 100%
                            rProb = 100;
                            break;
                        }

                    case 3:
                    case 4:
                    case 5    // Status = Lost, Cancelled, Delete
             :
                        {
                            rProb = 0;
                            break;
                        }
                }
                // goP.TraceLine("Value of SI__PROBABILITY: '" & doForm.doRS.GetFieldVal("SI__PROBABILITY", 2) & "'", "", sProc)
                // goP.TraceLine("rProb: '" & Math.Round(rProb, 0) & "'", "", sProc)
                if (Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY", 2)) != Math.Round(rProb, 0))
                    // goP.TraceLine("Setting SI__PROBABILITY to '" & Math.Round(rProb, 0) & "'", "", sProc)
                    doForm.doRS.SetFieldVal("SI__PROBABILITY", Math.Round(rProb, 0), 2);

                // TLD 11/17/20008 Modified to calculate first product value field
                // -----      Calculate Value
                cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2));
                if (!goTR.IsNumber(Convert.ToString(cValueFld)))
                {
                    cValueFld = 0;

                }
                rQty = (Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY")));
                if (!goTR.IsNumeric(rQty))
                {
                    rQty = 0;

                }
                rNewValue = Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty);
                doForm.doRS.SetFieldVal("CUR_VALUE", rNewValue, 2);
                cTotValue = Convert.ToDecimal(rNewValue);

                // TLD 11/17/2008 Added 14 N1 product fields, need to calculate unit prices
                // ----- Loops thru and Calculates 14 other Value and Velud Index fields
                for (i = 2; i <= 15; i++)
                {
                    cValueFld = (Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE" + i, 2)));
                    if (!goTR.IsNumber(Convert.ToString(cValueFld)))
                    {
                        cValueFld = 0;
                    }
                        
                    rQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY" + i));
                    if (!goTR.IsNumeric(rQty))
                    {
                        rQty = 0;
                    }
                       
                    rNewValue = Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty);
                    doForm.doRS.SetFieldVal("CUR_VALUE" + i, rNewValue, 2);
                   
                    cTotValue = Convert.ToDecimal(cTotValue) + Convert.ToDecimal(rNewValue);
                }

                // TLD 11/17/2008 to calculate total value index
                // ------ Calculates a total value index
                // doForm.doRS.SetFieldVal("CUR_VALUEINDEX", cValueFld * rQty * (Math.Round(rProb, 0) / 100), 2)   'Value Index
                doForm.doRS.SetFieldVal("CUR_TOTVALUE", cTotValue);
                doForm.doRS.SetFieldVal("CUR_VALUEINDEX", Convert.ToDouble(cTotValue) * (Math.Round(rProb, 0) / 100), 2);   // Value Index
            }

            return true;
        }

        public bool OP_ViewControlOnChange_BTN_SendToExcel(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Custom: This button is on the 'Sales Pipeline Open/Quote Opps - JK Rev' desktop. It is used to send the desktop to Excel
            // but as only 1 spreadsheet.
            // oBigDatatable.TableName ends up being the worksheet name.  
            // EXCEL column headers show the names of your datatable columns

            Desktop doDesktop = (Desktop)par_doCallingObject;
            int i;
            string sViewCondition = "";
            
            int iCounterCol = 0;
            DataTable oDataTable = new DataTable();
            string sStartDate=null;
            string sEndDate=null;
            //string sStartDate = doDesktop.LastSelectedStartDate; // Start date entered by user
            //string sEndDate = doDesktop.LastSelectedEndDate; // End date entered by user
            // Grabs view MD for filter, fields and labels
            string sViewID = goUI.GetLastSelected("VIEWPAGEID");
            string sMeta = doDesktop.GetViewMetadata(sViewID);
            string sState = doDesktop.GetViewStateMetadata(sViewID);
            string sFilter = goTR.StrRead(sState, "FILTER");
            string sSort = goTR.StrRead(sState, "SORT");
            int iFieldCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sMeta, "COLCOUNT","",true),"",ref par_iValid,""));
            string sFields = ""; // to record field names in view for rowset
            DataTable oTable = new DataTable();
            string sColLabel = "";

            // TLD 3/4/2009 Checks to see if date strings are valid
            if (!goTR.IsDate(sStartDate) | sStartDate == "")
            {
                goErr.SetWarning(30200, sProc, "", "Please enter/select a valid From date.", "", "", "", "", "", "", "", "", "doDesktop.LastSelectedStartDate");
                return false;
            }
            if (!goTR.IsDate(sEndDate) | sEndDate == "")
            {
                goErr.SetWarning(30200, sProc, "", "Please enter/select a valid To date.", "", "", "", "", "", "", "", "", "doDesktop.LastSelectedEndDate");
                return false;
            }

            // For i = 0 To 49
            // 'Add cols
            // 'oBigDatatable.Columns.Add(i.ToString, System.Type.GetType("System.String"))
            // 'Add columns, but don't give column headers here. Will do this as a row.
            // 'oBigDatatable.TableName = "Site Report"           
            // oTable.Columns.Add(i.ToString, System.Type.GetType("System.String"))
            // oTable.Columns(i).Caption = ""
            // Next
            // i = 0

            // Gets Fieldnames and Labels Rowset and excel
            for (i = 1; i <= iFieldCount; i++)
            {
                if (sFields == "")
                {
                    sFields = goTR.RemoveDelimiters(goTR.StrRead(sMeta, "COL" + i + "FIELD"));
                }
                else
                {
                    sFields = sFields + "," + goTR.RemoveDelimiters(goTR.StrRead(sMeta, "COL" + i + "FIELD"));
                }
            }

            // Creates OP Rowset from view data
            // Assume all 15 views have the same fields/filters
            clRowSet doOPRS = new clRowSet("OP", 3, sFilter, sSort, sFields);
            if (doOPRS.GetFirst() == 1)
            {
                // Create datatable to hold all Op data
                oTable.TableName = "Sales Pipeline Open/Quote Opps";

                // Add column headers
                for (i = 1; i <= iFieldCount; i++)
                {
                    // For i = 1 To iFieldCount - 1
                    // Sets column label in table from field label
                    sColLabel = goTR.StrRead(sMeta, "COL" + i + "LABEL");
                    oTable.Columns.Add(sColLabel, System.Type.GetType("System.String"));
                }

                doOPRS.ToTable();

                DataTable oTableNew = doOPRS.dtTransTable;
               // DataRow oRow;
                DataRow oNewRow;
                
                foreach (DataRow oRow in oTableNew.Rows)
                {
                    oNewRow = oTable.NewRow();
                    // Go thru all cols
                    for (i = 0; i <= iFieldCount - 1; i++)
                    {
                        oNewRow[i] = oRow[i];
                        if (oNewRow[i] == System.DBNull.Value)
                            oNewRow[i] = "";
                    }

                    oTable.Rows.Add(oNewRow);
                }
            }

            // Send the new Datatable to Excel
            DataSet oDataSet = new DataSet();
            oDataSet.DataSetName = "Sales Pipeline Open/Quote Opps";
            oDataSet.Tables.Add(oTable);
            goData.DatasetToTextFile(oDataSet);

            par_doCallingObject = doDesktop;
            return true;
        }

        public bool OPP_FilterProducts_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sFilter = "";

            // TLD 4/12/2010 Filters LNK_For_MO for selected product's models
            // Gets Model
            if (doForm.doRS.GetLinkCount("LNK_For_PD") == 1)
            {
                // TLD 10/4/2012 Mod for apostrophe in name
                // Dim sPDID As String = doForm.doRS.GetFieldVal("LNK_For_PD%%SYS_Name")
                string sPDID = goTR.PrepareForSQL(Convert.ToString(doForm.doRS.GetFieldVal("LNK_For_PD%%SYS_Name")));

                // Sets filter for first product field
                goTR.StrWrite( ref sFilter, "ACTIVE", "1");
                goTR.StrWrite(ref sFilter, "CONDITION", "LNK_Of_PD%%SYS_NAME='" + sPDID + "'");
                goTR.StrWrite(ref sFilter, "C1CONDITION", "=");
                goTR.StrWrite(ref sFilter, "C1FIELDNAME", "<%LNK_OF_PD%><%SYS_NAME%>");
                goTR.StrWrite(ref sFilter, "C1VALUE1", "'" + sPDID + "'");
                goTR.StrWrite(ref sFilter, "CCOUNT", "1");
                goTR.StrWrite(ref sFilter, "SORT", "SYS_NAME ASC");
                goTR.StrWrite(ref sFilter, "SORT1", "SYS_NAME");
                goTR.StrWrite(ref sFilter, "DIRECTION1", "1");
                goTR.StrWrite(ref sFilter, "FILE", "MO");
            }
            else

                sFilter = "";

            doForm.SetFilterINI("LNK_FOR_MO", sFilter);

            par_doCallingObject = doForm;
            return true;
        }

        public bool PR_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // Get original values and store in variables to compare on save
            doForm.oVar.SetVar("Bid Date", doForm.doRS.GetFieldVal("DTE_Q50"));
            doForm.oVar.SetVar("Close Date", doForm.doRS.GetFieldVal("DTE_Q90"));
            doForm.oVar.SetVar("Status", doForm.doRS.GetFieldVal("MLS_STATUS"));

            // Lock History
            doForm.SetControlState("MMO_HISTORY", 4);

            par_doCallingObject = doForm;
            return true;
        }

        public bool PR_FormOnSave_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sWork = Convert.ToString(doForm.doRS.GetFieldVal("MMO_HISTORY"));
            string sOrigBid = Convert.ToString(doForm.oVar.GetVar("Bid Date"));
            string sNewBid = Convert.ToString(doForm.doRS.GetFieldVal("DTE_Q50"));
            string sOrigClose = Convert.ToString(doForm.oVar.GetVar("Close Date"));
            string sNewClose = Convert.ToString(doForm.doRS.GetFieldVal("DTE_Q90"));
            string sOrigSts = Convert.ToString(doForm.oVar.GetVar("Status"));
            string sNewSts = Convert.ToString(doForm.doRS.GetFieldVal("MLS_STATUS"));
            int i;
            clArray doQuote;
            string sValue;

            // Write to History field
            sWork = Convert.ToString(doForm.doRS.GetFieldVal("MMO_HISTORY"));
            if (doForm.GetMode() == "CREATION")
            {
                sWork = goTR.WriteLogLine(sWork, "Created");

            }
            else
            {
                sWork = goTR.WriteLogLine(sWork, "Edited");
                // Check which fields have changed
                if (sOrigBid != sNewBid)
                {
                    sWork = goTR.WriteLogLine(sWork, "Changed 'Completed' from " + sOrigBid + " to " + sNewBid + ".");

                }
                if (sOrigClose != sNewClose)
                {
                    sWork = goTR.WriteLogLine(sWork, "Changed 'RFQ Due Date' from " + sOrigClose + " to " + sNewClose + ".");

                }
                if (sOrigSts != sNewSts)
                {
                    sWork = goTR.WriteLogLine(sWork, "Changed 'Status' from " + sOrigSts + " to " + sNewSts + ".");

                }
            }
            doForm.doRS.SetFieldVal("MMO_HISTORY", sWork);

            // Update Project value from linked Quote
            if (Convert.ToString(doForm.oVar.GetVar("UpdatedProjectValue")) != "1")
            {
                if (sNewSts == "Bid" & sNewSts != sOrigSts)
                {
                    doQuote = (clArray)doForm.doRS.GetFieldVal("LNK_CONNECTED_QT", 2);
                    for (i = 1; i <= doQuote.GetDimension(); i++)
                    {
                        clRowSet doRSQT = new clRowSet("QT", 3, "GID_ID='" + doQuote.GetItem(i) + "'", null, "MLS_STATUS,CUR_TOTAL");
                        if (doRSQT.GetFirst() == 1)
                        {
                            if (Convert.ToInt32(doRSQT.GetFieldVal("MLS_STATUS", 2)) != 6)
                            {
                                // Pass the Quote's total to MessageBoxEvent
                                sValue = Convert.ToString(doRSQT.GetFieldVal("CUR_TOTAL"));
                                doForm.oVar.SetVar("UpdatedProjectValue", "1");
                                doForm.MessageBox("Would you like to update the Project's 'RFQ Value' with the value from the linked Quote?", clC.SELL_MB_YESNO, null, null, null, null, "Yes", "MessageBoxEvent", null, null, doForm, null, "Yes", null, null, sValue, "PR_FormOnSave_Post_UpdateValue");
                                return true;
                            }
                        }
                    }
                }
            }

            doForm.oVar.SetVar("UpdatedProjectValue", "");
            par_doCallingObject = doForm;
            return true;
        }

        public bool QD_FormControlOnChange_BTN_CalcMargin(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            try
            {
                scriptManager.RunScript("QuoteDetail_CalcMargin",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections,null,"","","","","");
            }

            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            try
            {
                // Calc margin
                scriptManager.RunScript("QuoteDetail_CalcMargin", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            }

            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool QL_FormAfterSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            try
            {
                // TLD 10/12/2012 Moved most to FormOnLoad, since need for Save & Create Another?
                // Create linked option ql
                if (Convert.ToString(doForm.oVar.GetVar("CreateSubcomponent")) == "1")
                {
                    Form doFormQL = new Form("QL", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), "CRL_QL");
                    doFormQL.doRS.SetFieldVal("LNK_IN_QT", doForm.doRS.GetFieldVal("LNK_IN_QT", 2), 2);
                    doFormQL.doRS.SetFieldVal("CHK_SUBCOMPONENT", 1, 2);
                    // TLD 10/3/2012 Added to set primary QL on new QL
                    // doFormQL.doRS.SetFieldVal("LNK_Primary_QL", doForm.doRS.GetCurrentRecID())
                    if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_SubComponent", 2)) == 1)
                    {

                        // From QL is already an option?
                        doFormQL.doRS.SetFieldVal("LNK_Primary_QL", doForm.doRS.GetFieldVal("LNK_Primary_QL"));
                    }
                    else
                    {
                        doFormQL.doRS.SetFieldVal("LNK_Primary_QL", doForm.doRS.GetCurrentRecID());
                    }
                        // This QL is primary QL?
                       
                    goUI.Queue("FORM", doFormQL);
                }
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool QL_FormControlOnChange_BTN_SAVECRU_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            // IF gbWriteLog THEN oLog is clLogObj(sProc, "Start", 3)

            Form doForm = (Form)par_doCallingObject;

            // TLD 10/12/2012 If Create Option is checked, don't run main
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_CreateSubcomponent", 2)) == 1)
            {
                par_bRunNext = false; // Don't run main

            }
            else
            {
                return true;

            }

            if (doForm.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
            {
                return false;

            }

            Form doNewForm;
            string sID;
            string sToCo;
            string SInQT;
            string SRelPR;
            string SStat;
            string sReason;
            string SCredUs;
            string SPeerUs;
            string SRelOp;
            string SRelTer;

            sID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));
            sToCo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_TO_CO"));
            SInQT = Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_QT"));
            SRelPR = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_PR"));
            SStat = Convert.ToString(doForm.doRS.GetFieldVal("MLS_STATUS"));
            sReason = Convert.ToString(doForm.doRS.GetFieldVal("MLS_REASONWONLOST"));
            SCredUs = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            SPeerUs = Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US"));
            SRelOp = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_OP"));
            SRelTer = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_TE"));

            // Create new QL

            doNewForm = new Form("QL", "", "CRU_QL");

            // Set values in new form
            doNewForm.doRS.SetFieldVal("LNK_TO_CO", sToCo);
            doNewForm.doRS.SetFieldVal("LNK_IN_QT", SInQT);
            doNewForm.doRS.SetFieldVal("LNK_Related_PR", SRelPR);
            doNewForm.doRS.SetFieldVal("MLS_STATUS", SStat);
            doNewForm.doRS.SetFieldVal("MLS_REASONWONLOST", sReason);
            doNewForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", SCredUs);
            doNewForm.doRS.SetFieldVal("LNK_PEER_US", SPeerUs);
            doNewForm.doRS.SetFieldVal("LNK_RELATED_OP", SRelOp);
            doNewForm.doRS.SetFieldVal("LNK_RELATED_TE", SRelTer);

            // TLD 10/12/2012 Create linked subcomponent ql from save & create another
            if (Convert.ToString(doForm.oVar.GetVar("CreateSubcomponent")) == "1")
            {
                // Dim doFormQL As New clForm("QL", doForm.doRS.GetFieldVal("GID_ID"), "CRL_QL")
                doNewForm.doRS.SetFieldVal("CHK_SUBCOMPONENT", 1, 2);
                // TLD 10/12/2012 Get LNK_Primary_QL from QL
                // TLD 10/3/2012 Added to set primary QL on new QL
                // doNewForm.doRS.SetFieldVal("LNK_Primary_QL", doForm.doRS.GetCurrentRecID())
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Subcomponent", 2)) == 1)
                {
                    // From QL is already an option?
                    doNewForm.doRS.SetFieldVal("LNK_Primary_QL", doForm.doRS.GetFieldVal("LNK_Primary_QL"));
                }       
                else
                {
                    // This QL is primary QL?
                    doNewForm.doRS.SetFieldVal("LNK_Primary_QL", doForm.doRS.GetCurrentRecID());
                }
                   
            }

            doNewForm.OpenForm();
            par_doCallingObject = doForm;
            return true;
        }

        public bool QL_FormControlOnChange_CHK_Override(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections,  clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            try
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_OVERRIDE", 2)) == 1)
                    doForm.MessagePanel("You are overriding the Unit Price After Discount value. Please remember to include the Commission amount when you enter a new value in this field.");
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QL_FormControlOnChange_CHK_SUBCOMPONENT(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // Enable 'Create Subcomponent' if this QL is not a subcomponent
            try
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_SUBCOMPONENT", 2)) == 0)
                {
                    doForm.SetControlState("CHK_CREATESUBCOMPONENT", 0);

                }
                else
                {
                    doForm.SetControlState("CHK_CREATESUBCOMPONENT", 4);

                }
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    goErr.SetError(ex, 45105, sProc);

                }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool QL_FormControlOnChange_CHK_UpdateListPrice(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            try
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_UPDATELISTPRICE", 2)) == 1)
                {
                    doForm.doRS.SetFieldVal("CHK_UPDATELISTPRICE", 0, 2);
                    doForm.MessageBox("Are you sure you want to update the List Price from the Model?" + Constants.vbCrLf + "If you click Yes, the Model's current List Price will replace the List Price on the Quote Line.", clC.SELL_MB_YESNO, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Yes", "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "Yes", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "QL_FormControlOnChange_CHK_UpdateListPrice");
                    return true;
                }
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    goErr.SetError(ex, 45105, sProc);

                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QL_FormControlOnChange_BTN_INSERTSPEC_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 12/8/2008 Prevents main from running
            par_bRunNext = false;

            // TLD 12/8/2008 Adds to MMO_SPECIFICATIONS the TXT_Description of Print Short Descr checked
            // Else, add the MO.MMO_SPECIFICATIONS to it.
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_PRINTLONGDESCR", 2)) == 1)
            {
                if (Strings.Trim(Convert.ToString(doForm.doRS.GetFieldVal("MMO_DETAILS"))) == "")
                    // blank, set it to model description field
                    doForm.doRS.SetFieldVal("MMO_DETAILS", doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_SPECIFICATIONS"));
                else
                {
                    // not blank, In the MsgBox below, 64 is for 'i' icon, 4 for Yes/No, 256 for 2nd button focus
                    doForm.MessageBox("The 'Details' field may already contains a set of specifications from the connected Model. Are you sure you want the specifications prepended?", clC.SELL_MB_YESNO, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Yes", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "LNK_FOR_MO%%MMO_SPECIFICATIONS", System.Reflection.MethodInfo.GetCurrentMethod().Name);
                    return true;
                }
            }
            else if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_PRINTSHORTDESCR", 2)) == 1)
            {
                if (Strings.Trim(Convert.ToString(doForm.doRS.GetFieldVal("MMO_DETAILS"))) == "")
                    // blank, set it to model description field
                    doForm.doRS.SetFieldVal("MMO_DETAILS", doForm.doRS.GetFieldVal("LNK_FOR_MO%%TXT_DESCRIPTION"));
                else
                {
                    // not blank, In the MsgBox below, 64 is for 'i' icon, 4 for Yes/No, 256 for 2nd button focus
                    doForm.MessageBox("The 'Details' field may already contains a set of specifications from the connected Model. Are you sure you want the specifications prepended?", clC.SELL_MB_YESNO, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Yes", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "LNK_FOR_MO%%TXT_DESCRIPTION", System.Reflection.MethodInfo.GetCurrentMethod().Name);
                    return true;
                }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool QL_FormControlOnChange_BTN_MOSEARCH(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = "Script::QT_ControlOnChange_BTN_MOSEARCH";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // Purpose: Filter lnk_For_MO connection based on 3 nbd fields
            // --ndb_txt_description relates to the model's txt_modeldescription field.
            // --ndb_txt_modelno relates to the model's LI__SKUNo.
            // --ndb_txt_product relates to the model's LNK_RELATED_PD%%TXT_ProductName.

            string sModelName;
            string sModelDescr;
            string sProduct;
            string sFilter = "";
            int iCounter = 0;
            string sCondition = "";

            sModelDescr = Strings.Trim(doForm.GetControlVal("NDB_TXT_DESCRIPTION"));
            sModelName = Strings.Trim(doForm.GetControlVal("NDB_TXT_ModelName"));
            sProduct = Strings.Trim(doForm.GetControlVal("NDB_TXT_Product"));

            if (sModelName == "" & sModelDescr == "" & sProduct == "")
            {
                // Use default filter if there is one, otherwise none
                sFilter = goMeta.PageRead("GLOBAL", "LBF_QL_LNK_FOR_MO");
                if (sFilter != "")
                {
                }
                else
                {
                    // Set ALL filter
                    goTR.StrWrite(ref sFilter, "C1FIELDNAME", "<%ALL%>");
                    goTR.StrWrite(ref sFilter, "CCOUNT", "1");
                    goTR.StrWrite(ref sFilter, "CONDITION", "");
                    goTR.StrWrite(ref sFilter, "FILE", "MO");
                    goTR.StrWrite(ref sFilter, "SORT", "SYS_NAME");
                    goTR.StrWrite(ref sFilter, "SORT1", "SYS_Name");
                    goTR.StrWrite(ref sFilter, "DIRECTION1", "1");
                    goTR.StrWrite(ref sFilter, "TABLENAME", "MO");
                }
                doForm.SetFilterINI("LNK_FOR_MO", sFilter);
            }

            // Build filter string
            goTR.StrWrite(ref sFilter, "ACTIVE", "1");
            goTR.StrWrite(ref sFilter, "FILE", "MO");
            goTR.StrWrite(ref sFilter, "SORT", "SYS_NAME ASC");
            goTR.StrWrite(ref sFilter, "SORT1", "SYS_NAME");
            goTR.StrWrite(ref sFilter, "DIRECTION1", "1");
            goTR.StrWrite(ref sFilter, "TABLENAME", "MO");

            if (doForm.GetControlVal("NDB_TXT_DESCRIPTION") != "")
            {
                iCounter = iCounter + 1;
                sCondition = sCondition + "TXT_DESCRIPTION['" + sModelDescr + "'";
                goTR.StrWrite(ref sFilter, "C" + iCounter + "CONDITION", "[");
                goTR.StrWrite(ref sFilter, "C" + iCounter.ToString() + "FIELDNAME", "<%TXT_DESCRIPTION%>");
                goTR.StrWrite(ref sFilter, "C" + iCounter.ToString() + "VALUE1", sModelDescr);
            }

            if (doForm.GetControlVal("NDB_TXT_MODELNAME") != "")
            {
                iCounter = iCounter + 1;
                if (sCondition != "")
                    sCondition += " and ";
                sCondition = sCondition + "TXT_ModelName['" + sModelName + "'";
                goTR.StrWrite(ref sFilter, "C" + iCounter + "CONDITION", "[");
                goTR.StrWrite(ref sFilter, "C" + iCounter.ToString() + "FIELDNAME", "<%TXT_ModelName%>");
                goTR.StrWrite(ref sFilter, "C" + iCounter.ToString() + "VALUE1", sModelName);
            }

            if (doForm.GetControlVal("NDB_TXT_PRODUCT") != "")
            {
                iCounter = iCounter + 1;
                if (sCondition != "")
                    sCondition += " and ";
                sCondition = sCondition + "LNK_OF_PD%%TXT_PRODUCTNAME['" + sProduct + "'";
                goTR.StrWrite(ref sFilter, "C" + iCounter + "CONDITION", "[");
                goTR.StrWrite(ref sFilter, "C" + iCounter.ToString() + "FIELDNAME", "<%LNK_OF_PD%><%TXT_PRODUCTNAME%>");
                goTR.StrWrite(ref sFilter, "C" + iCounter.ToString() + "VALUE1", sProduct);
            }

            goTR.StrWrite(ref sFilter, "CCOUNT", iCounter.ToString());
            goTR.StrWrite(ref sFilter, "CONDITION", sCondition);

            doForm.SetFilterINI("LNK_FOR_MO", sFilter);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QL_FormControlOnChange_LNK_FOR_MO_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 10/8/2009 Sets Unit Cost to 0
            // this allows in a roundabout way
            // for the user to change Unit Cost
            doForm.doRS.SetFieldVal("CUR_UNITCOST", "");
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_LNK_TO_CO_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 3/18/2009 Sets RSM (LNK_CREDITEDTO_US) to LNK_TO_CO%%LNK_TEAMLEADER_US
            // If new quote and LNK_TO_CO Not empty
            if (doForm.doRS.GetLinkCount("LNK_TO_CO") != 0)
            {
                doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_TO_CO%%LNK_TEAMLEADER_US"));

            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            decimal cUnitCostVal;
            decimal cMargin;
            // Dim bItemFilled As Boolean = False

            // 'TLD 10/12/2012 Comment, can create now
            // 'If QL is a subcomponent, disable 'Create Subcomponent'
            // If doForm.doRS.GetFieldVal("CHK_Subcomponent", 2) = 1 Then
            // doForm.SetControlState("CHK_CreateSubcomponent", 4)
            // End If

            // TLD 10/25/2012 Changed <> to = "CREATION"
            // TLD 10/12/2012 Enable if checked on edit only, so they can uncheck
            // and subtotal will calc in total?
            // TLD 10/3/2012 Always disable, this is a subcomponent
            // because can't renumber?
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_SubComponent", 2)) != 1 | doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("CHK_SubComponent", 4);

            }

            // Fill fields on load; cleared on save. Some users will not have permission
            // to read Quote Line Detail recs
            cUnitCostVal = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITCOST", 2));
            cMargin = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_MARGIN", 2));

            // TLD 10/5/2010 Created QE RS to optimize
            clRowSet doQERS = new clRowSet("QE", 3, "LNK_CONNECTED_QL='" + doForm.GetRecordID() + "'", null/* Conversion error: Set to default value for this argument */, "CUR_UNITCOST, CUR_COST, SR__MARGIN, CUR_GROSSPROFIT", 1);

            if (cUnitCostVal == 0 & cMargin == 0)
            {
                // TLD 10/5/2010 Mod to use QE RS above
                // If doForm.doRS.GetLinkCount("LNK_CONNECTED_QE") = 1 Then
                if (doQERS.GetFirst() == 1)
                {
                    // doForm.doRS.SetFieldVal("CUR_UNITCOST", doForm.doRS.GetFieldVal("LNK_CONNECTED_QE%%CUR_UNITCOST"))
                    // doForm.doRS.SetFieldVal("CUR_COST", doForm.doRS.GetFieldVal("LNK_CONNECTED_QE%%CUR_COST"))
                    // doForm.doRS.SetFieldVal("CUR_MARGIN", doForm.doRS.GetFieldVal("LNK_CONNECTED_QE%%SR__MARGIN"))
                    // doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", doForm.doRS.GetFieldVal("LNK_CONNECTED_QE%%CUR_GROSSPROFIT"))
                    doForm.doRS.SetFieldVal("CUR_UNITCOST", doQERS.GetFieldVal("CUR_UNITCOST"));
                    doForm.doRS.SetFieldVal("CUR_COST", doQERS.GetFieldVal("CUR_COST"));
                    doForm.doRS.SetFieldVal("CUR_MARGIN", doQERS.GetFieldVal("SR__MARGIN"));
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", doQERS.GetFieldVal("CUR_GROSSPROFIT"));
                }
                else if (doQERS.Count() > 1)
                {
                    doForm.MessagePanel("There is more than one linked Cost Detail record. Click the 'Cost Detail' link field and unlink the incorrect Cost Detail record.");
                    return false;
                }
            }
            doQERS = null/* TODO Change to default(_) if this is not a reference type */;

            // TLD 10/12/2012 Mod to allow creating components
            // from components, but still use Primary QL highest # system
            // moved to script
            scriptManager.RunScript("QL_GetHighestLine", ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/1/2012 Prevent main from running
            // Custom enforce Reason Won/Lost in _Post?
            goTR.StrWrite(ref par_sSections, "EnforceReasonWonLost", "0");
            par_doCallingObject = doForm;
            return true;
        }
        public bool QL_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            int iStatus;

            // TLD 6/1/2012 Prevent main from running in _Pre
            // Custom enforce Reason Won/Lost here?
            // CS 5/28/10 Now that Quote Line reason can be set independent of QT with the status need to enforce it
            // If goScr.IsSectionEnabled(sProc, par_sSections, "EnforceReasonWonLost") Then
            if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_STATUSMGMT")) == "1")
            {
                if (Strings.UCase(Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_QT%%CHK_UPDQLSTATUS"))) == "UNCHECKED")
                {
                    iStatus = (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)));
                    switch (iStatus)
                    {
                        case 0      // Do Nothing
                       :
                            {
                                break;
                            }

                        default:
                            {
                                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_REASONWONLOST", 2)) == 0)
                                {
                                    doForm.MoveToField("MLS_REASONWONLOST");
                                    goErr.SetWarning(30200, sProc, "", "Please select the reason this quote line was lost.", "", "", "", "", "", "", "", "", "");
                                    return false;
                                }

                                break;
                            }
                    }
                }
            }

            // Question if saving a Margin quote line with 0% margin
            if (Convert.ToString(doForm.oVar.GetVar("AskMargin")) != "1")
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_TYPE", 2)) == 2)
                {
                    if (Convert.ToInt32(doForm.doRS.GetFieldVal("SR__MARGIN", 2)) == 0)
                    {
                        doForm.oVar.SetVar("AskMargin", "1");
                        doForm.MessageBox("The Margin % for this Quote line is 0. Are you sure you want to save this Quote Line with a 0% Margin?", clC.SELL_MB_YESNO, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Yes", null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "Yes", "No", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "QL_FormOnSave_Post_Margin");
                        return true;
                    }
                }
            }

            // Set var if 'Create Subcomponent' is checked; will check in FormAfterSave
            // Also uncheck field here
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_CREATESUBCOMPONENT", 2)) == 1)
            {
                doForm.doRS.SetFieldVal("CHK_CREATESUBCOMPONENT", 0, 2);
                doForm.oVar.SetVar("CreateSubcomponent", "1");
            }


            // TLD 10/23/2012 Mod to fill TXT_QLOption for print template, clear first
            // 'TLD 10/5/2010 Commented, this does nothing......
            // 'Update primary Quote line when a sub component Quote line is saved.
            // 'CalcUnitPrice should run on the primary qL which runs CalcTotal and
            // 'scoops up values on subcomponent lines.
            // 'Check if this is a subcomponent QL with a linked primary
            // If doForm.doRS.GetFieldVal("CHK_CREATESUBCOMPONENT", 2) = 1 Or doForm.doRS.GetFieldVal("CHK_SUBCOMPONENT", 2) = 1 Then
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_SUBCOMPONENT", 2)) == 1)
            {
                // Put asterisk in txt field
                doForm.doRS.SetFieldVal("TXT_QLOption", "*");
            }
               
            else
            {
                // clear
                doForm.doRS.SetFieldVal("TXT_QLOption", "");
            }
               

            // TLD 10/12/2012 Need to update highest line to
            // make sure it stays updated for sub-options?
            scriptManager.RunScript("QL_UpdateHighestLine", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            doForm.oVar.SetVar("AskMargin", "");
            par_doCallingObject = doForm;

            return true;
        }

        public bool QL_GetHighestLine_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            double lLine;
            double lHighestLine;
            double dbLine;
            int i;

            // TLD 10/12/2012 Moved from formonload_post

            // Determine the next Line number for the QL 
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_SUBCOMPONENT", 2)) == 0)
            {
                clArray doComponents = (clArray)doForm.doRS.GetFieldVal("LNK_COMPONENT_QL", 2);
                lHighestLine = 0;
                if (doComponents.GetDimension() == 0)
                    lHighestLine = Convert.ToInt32(doForm.doRS.GetFieldVal("SR__LINENO", 2));
                else
                    for (i = 1; i <= doComponents.GetDimension(); i++)
                    {
                        clRowSet doRSComponents = new clRowSet("QL", 3, "GID_ID='" + doComponents.GetInfo(i) + "'", null/* Conversion error: Set to default value for this argument */, "SR__LINENO", 1);
                        if (doRSComponents.GetFirst() == 1)
                        {
                            lLine = Convert.ToInt32(doRSComponents.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            else
                                lHighestLine = Convert.ToInt32(doForm.doRS.GetFieldVal("SR__LINENO", 2));
                        }
                    }
                doForm.doRS.SetFieldVal("SR__HighestItemNo", lHighestLine);
                doComponents = null/* TODO Change to default(_) if this is not a reference type */;
            }
            else
                // This is a new subcomponent; Don't edit on existing QLs
                if (doForm.GetMode() == "CREATION")
            {
                lHighestLine = goTR.StringToNum(doForm.doRS.GetFieldVal("LNK_PRIMARY_QL%%SR__HIGHESTITEMNO",0,-1,true,-1,"",""),"",ref par_iValid,"");
                dbLine = lHighestLine + 0.1;
                doForm.doRS.SetFieldVal("SR__LINENO", dbLine);
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 12/8/2008 Prevents main from running; set in QL_RECORDONSAVE_POST
            goTR.StrWrite(ref par_sSections, "FillUnitFromModel", "0"); // do not run FillUnitFromModel

            return true;
        }

        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            clRowSet doRSQE;
            string sINI = "";

            // Create Quote Line Detail record if doesn't exist
            if (doRS.GetLinkCount("LNK_CONNECTED_QE") == 0)
                // Add
                doRSQE = new clRowSet("QE", 2, null, null, null, 0, null, null, "CRL_QE", Convert.ToString(doRS.GetFieldVal("GID_ID")));
            else
            {
                // Update
                // doRSQE = New clRowSet("QE", 1, "GID_ID='" & doRS.GetFieldVal("LNK_CONNECTED_QE") & "'")
                // If doRSQE.GetFirst = 0 Then
                // goErr.SetError(35000, sProc, "Unable to find linked Quote Line Detail record.")
                // Return False
                // End If
                // CS 10/14/08:
                // Loophole for user without R permission on QE, but still needs to edit QE
                goTR.StrWrite(ref sINI, "ACCESSTYPE", "E");
                goTR.StrWrite(ref sINI, "TABLENAME", "QE");
                goTR.StrWrite(ref sINI, "FIELDS", "**");
                goTR.StrWrite(ref sINI, "CONDITION", "LNK_CONNECTED_QL='" + doRS.GetFieldVal("GID_ID") + "'");
                goTR.StrWrite(ref sINI, "SORT", "SYS_NAME");
                goTR.StrWrite(ref sINI, "GETALLUSERSUNSHAREDRECS", false);
                goTR.StrWrite(ref sINI, "MODE", "SELECT");
                doRSQE = new clRowSet("QE", 1, null, null, null, 0, sINI);

                if (doRSQE.GetFirst() != 1)
                {
                    goErr.SetError(35000, sProc, "Creating/Updating Quote Detail record failed. Cannot find linked Quote Detail.");
                    return false;
                }
            }

            // update fields
            doRSQE.SetFieldVal("LNK_FOR_MO", doRS.GetFieldVal("LNK_FOR_MO", 2), 2);
            doRSQE.SetFieldVal("LI__QTY", doRS.GetFieldVal("SR__QTY"));
            doRSQE.SetFieldVal("CUR_PRICEUNITNODISC", doRS.GetFieldVal("CUR_PRICEUNIT"));
            doRSQE.SetFieldVal("CUR_PRICEUNIT", doRS.GetFieldVal("CUR_PRICEUNITAFTERDISC"));
            doRSQE.SetFieldVal("DTT_EXPECTEDCLOSEDATE", doRS.GetFieldVal("DTT_EXPCLOSEDATE"));
            doRSQE.SetFieldVal("BI__DISCPERCENT", doRS.GetFieldVal("SR__DISCPERCENT"));
            doRSQE.SetFieldVal("CUR_UNITCOST", doRS.GetFieldVal("CUR_UNITCOST"));
            doRSQE.SetFieldVal("CUR_COST", doRS.GetFieldVal("CUR_COST"));
            doRSQE.SetFieldVal("SR__MARGIN", doRS.GetFieldVal("SR__MARGIN"));
            doRSQE.SetFieldVal("CUR_GROSSPROFIT", doRS.GetFieldVal("CUR_GROSSPROFIT"));
            doRSQE.SetFieldVal("CUR_SUBTOTAL", doRS.GetFieldVal("CUR_SUBTOTAL"));
            doRSQE.SetFieldVal("TXT_Item", doRS.GetFieldVal("TXT_Model"));

            // TLD 12/8/2008 Added to override main -- pulls to QL from MLS_UNIT instead
            if (Convert.ToString(doRS.GetFieldVal("TXT_UNIT")) == "")
            {

                doRS.SetFieldVal("TXT_UNIT", doRS.GetFieldVal("LNK_FOR_MO%%MLS_UNIT", 1));
            }
            // TLD 12/8/2008 This one can pull from TXT_UNIT, now that it is set correctly
            doRSQE.SetFieldVal("TXT_UNIT", doRS.GetFieldVal("TXT_UNIT"));

            doRSQE.SetFieldVal("MLS_QLSTATUS", doRS.GetFieldVal("MLS_STATUS", 2), 2);
            doRSQE.SetFieldVal("LNK_RELATED_QT", doRS.GetFieldVal("LNK_IN_QT", 2), 2);
            doRSQE.SetFieldVal("CUR_PERUNITCONTRIBUTE", Convert.ToInt32(doRS.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2)) - Convert.ToInt32(doRS.GetFieldVal("CUR_UNITCOST", 2)));

            // doRSQE.SetFieldVal("CUR_PERUNITMARKUP", doRS.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2) * doRS.GetFieldVal("SR__DISCPERCENT", 2))
            doRSQE.SetFieldVal("CUR_PERUNITMARKUP", Convert.ToInt32(doRS.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2)) - Convert.ToInt32(doRS.GetFieldVal("CUR_PRICEUNIT", 2)));

            // doRSQE.SetFieldVal("CUR_UNITSELLWMARKUP", doRS.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2) + (doRS.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2) * doRS.GetFieldVal("SR__DISCPERCENT", 2)))

            // doRSQE.SetFieldVal("CUR_TOTALSELLWMARKUP", doRS.GetFieldVal("SR__QTY", 2) * doRS.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2) + (doRS.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2) * doRS.GetFieldVal("SR__DISCPERCENT", 2)))
            // doRSQE.SetFieldVal("CUR_NETCONTRIBUTION", doRS.GetFieldVal("CUR_UNITCOST", 2) / (1 - doRS.GetFieldVal("CUR_MARGIN", 2) / 100))
            // doRSQE.SetFieldVal("CUR_TOTALMARKUP", doRS.GetFieldVal("SR__QTY", 2) * doRS.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2) * doRS.GetFieldVal("SR__DISCPERCENT", 2))
            doRSQE.SetFieldVal("LNK_CONNECTED_QL", doRS.GetFieldVal("GID_ID"));

            if (doRSQE.Commit() != 1)
            {
                goErr.SetError(35000, sProc, "Creating or updating linked Quote Line Detail record failed.");
                return false;
            }

            // DVF 3/10/2010 - testing non-zero of QL
            // 'Clear Cost Fields. They are copied from the Quote Line Detail form
            // doRS.SetFieldVal("CUR_UNITCOST", 0, 2)
            // doRS.SetFieldVal("CUR_COST", 0, 2)
            // doRS.SetFieldVal("CUR_MARGIN", 0, 2)
            // doRS.SetFieldVal("CUR_GROSSPROFIT", 0, 2)

            par_doCallingObject = doRS;

            return true;
        }
        public bool QL_UpdateHighestLine_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            double lLine;
            double lHighestLine;
            // Dim dbLine As Double
            int i;
            clRowSet doPrimaryQLRS;

            // TLD 10/12/2012 Need to update Primary QL Highes Line NO.

            // Determine the next Line number for the QL 
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_SUBCOMPONENT", 2)) == 0)
            {
                clArray doComponents = (clArray)doForm.doRS.GetFieldVal("LNK_COMPONENT_QL", 2);
                lHighestLine = 0;
                if (doComponents.GetDimension() == 0)
                {
                    lHighestLine = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINENO", 2));

                }
                else
                    for (i = 1; i <= doComponents.GetDimension(); i++)
                    {
                        clRowSet doRSComponents = new clRowSet("QL", 3, "GID_ID='" + doComponents.GetInfo(i) + "'", null, "SR__LINENO", 1);
                        if (doRSComponents.GetFirst() == 1)
                        {
                            lLine = Convert.ToDouble(doRSComponents.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                            {
                                lHighestLine = lLine;

                            }
                            else
                            {
                                lHighestLine = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINENO", 2));

                            }
                        }
                    }
                doForm.doRS.SetFieldVal("SR__HighestItemNo", lHighestLine);
                doComponents = null/* TODO Change to default(_) if this is not a reference type */;
            }
            else
            {
                // This is an option, need to update prmary QL via RS
                lHighestLine = goTR.StringToNum(doForm.doRS.GetFieldVal("LNK_PRIMARY_QL%%SR__HIGHESTITEMNO"),"",ref par_iValid,"");
                // dbLine = lHighestLine + 0.1
                // doForm.doRS.SetFieldVal("SR__LINENO", dbLine)
                // Also need to update Primary QL with latest #, exclude this QL
                string sPrimaryQLID = Convert.ToString(doForm.doRS.GetFieldVal("LNK_PRIMARY_QL"));
                clRowSet doComponents = new clRowSet("QL", 3, "GID_ID='" + sPrimaryQLID + "' AND GID_ID<>'" + doForm.doRS.GetCurrentRecID() + "'", null, "SR__HighestItemNo");
                if (doComponents.GetFirst() == 1)
                {
                    do
                    {
                        lLine = Convert.ToDouble(doComponents.GetFieldVal("SR__LINENO", 2));
                        if (lLine > lHighestLine)
                        {
                            lHighestLine = lLine;
                        }
                        else
                        {
                            lHighestLine = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINENO", 2));
                        }
                        if (doComponents.GetNext() == 0)
                        {

                        }
                            break;
                    }
                    while (true);
                    // Now check this QL's Line?
                    if (lLine > Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINENO", 2)))
                    {
                        lHighestLine = lLine;

                    }

                    // Get Primary QL RS
                    doPrimaryQLRS = new clRowSet("QL", 1, "GID_ID='" + sPrimaryQLID + "'", null, "SR__HIGHESTITEMNO", 1, null, null, null, null, null, true, true, true, true, 0, null, true);
                    if (doPrimaryQLRS.GetFirst() == 1)
                    {
                        if (lHighestLine > Convert.ToDouble(doPrimaryQLRS.GetFieldVal("SR__HIGHESTITEMNO", 2)))
                        {
                            doPrimaryQLRS.SetFieldVal("SR__HIGHESTITEMNO", lHighestLine, 2);
                            if (doPrimaryQLRS.Commit() == 0)
                                // write to log?
                                goLog.Log(sProc, "QL Update Highest Item No on Primary QL failed due to error " + goErr.GetLastError("NUMBER"), -1, true, true);
                        }
                        doPrimaryQLRS = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                    doComponents = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }
            par_doCallingObject = doForm; 
            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // ---------Set vars
            // Set var to compare later
            doForm.oVar.SetVar("Total", doForm.doRS.GetFieldVal("CUR_TOTAL"));
            // TLD 1/30/2009 Captures qt status on open for use on save
            doForm.oVar.SetVar("QT_Status", doForm.doRS.GetFieldVal("MLS_Status", 2));

            // 'TLD 10/12/2012 Combine with below
            // 'Set Quote Ref No on form load if there is a linked project
            // If doForm.GetMode = "CREATION" Then
            // If doForm.doRS.GetLinkCount("LNK_RELATED_PR") > 0 Then
            // doForm.doRS.SetFieldVal("TXT_QUOTEREFNO", doForm.doRS.GetFieldVal("LNK_RELATED_PR%%TXT_PROJECTREF"))
            // End If
            // End If

            // TLD 10/12/2012 Mod to combine with above
            // no need to check Mode twice!
            // TLD 3/18/2009 Sets RSM (LNK_CREDITEDTO_US) to LNK_TO_CO%%LNK_TEAMLEADER_US
            // If new quote and LNK_TO_CO Not empty
            // If doForm.GetMode() = "CREATION" And doForm.doRS.GetLinkCount("LNK_TO_CO") <> 0 Then
            if (doForm.GetMode() == "CREATION")
            {
                if (doForm.doRS.GetLinkCount("LNK_TO_CO") != 0)
                    doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_TO_CO%%LNK_TEAMLEADER_US"));

                if (doForm.doRS.GetLinkCount("LNK_RELATED_PR") > 0)
                    doForm.doRS.SetFieldVal("TXT_QUOTEREFNO", doForm.doRS.GetFieldVal("LNK_RELATED_PR%%TXT_PROJECTREF"));
            }

            // -----------Control States
            // TLD 3/23/2009 Disables system filled fields
            doForm.SetControlState("DTE_SENDDATE", 4); // Grayed
            doForm.SetControlState("CHK_SENDOVERDUE", 4); // Grayed
            doForm.SetControlState("INT_DAYSOVERDUE", 4); // Grayed
                                                          // TLD 11/2/2009 Changed to SR__ instead of SI__
                                                          // TLD 10/27/2009 Added 2 fields
            doForm.SetControlState("SR__PERCMARGINTOTAL", 4);
            doForm.SetControlState("SR__PERCMARGINWON", 4);

            // TLD 10/9/2012 Set Sales Process tab color to red
            doForm.SetControlState("TAB5", Convert.ToInt32("LABELCOLOR"), Convert.ToInt32("Red"));
            // TLD 10/9/2012 Disable calc fields
            doForm.SetControlState("SR__POA", 4);
            doForm.SetControlState("SR__POW", 4);
            doForm.SetControlState("SR__SalesProb", 4);
            doForm.SetControlState("MLS_SalesProb", 4);
            // -----------Control States

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sMessage = "";

            // TLD 6/1/2012 Prevent main Enforce Reason Won/Lost from running
            // Customize in _Post?
            goTR.StrWrite(ref par_sSections, "EnforceReasonWonLost", "0");

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sOrigTotal = Convert.ToString(doForm.oVar.GetVar("Total"));
            string sNewTotal = Convert.ToString(doForm.doRS.GetFieldVal("CUR_TOTAL"));
            int i;
            clArray doProject;
            string sID;
            int iNowStatus = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2));
            int iOpenStatus = Convert.ToInt32(goTR.StringToNum(doForm.oVar.GetVar("QT_STATUS"),"",ref par_iValid,""));

            // TLD 6/1/2012 Prevent main from running in _Pre
            // Custom enforce Reason Won/Lost here in case
            // other stuff in main needs to run?
            // If goScr.IsSectionEnabled(sProc, par_sSections, "EnforceReasonWonLost") Then
            // iStatus = doForm.doRS.GetFieldVal("MLS_STATUS", 2)
            switch (iNowStatus)
            {
                case 0     // Open, do nothing
               :
                    {
                        break;
                    }

                default:
                    {
                        if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_REASONWONLOST", 2)) == 0)
                        {
                            // CS 4/18/08: Added this code to reset var if FormOnSave returns false.
                            // This comes into play if user clicks Add button, gets validation and then clicks
                            // Save (instead of Add) the second time.
                            if (Convert.ToString(doForm.oVar.GetVar("QT_AddQuoteLine")) == "1")
                                doForm.oVar.SetVar("QT_AddQuoteLine", "");
                            // doForm.MoveToTab(3)     'Details
                            doForm.MoveToField("MLS_REASONWONLOST");
                            goErr.SetWarning(30200, sProc, "", "Please select the reason this quote was lost.", "", "", "", "", "", "", "", "", "");
                            return false;
                        }

                        break;
                    }
            }
            // End If

            // If Quote total changed, Update linked Project value
            if (Convert.ToString(doForm.oVar.GetVar("UpdatedProjectValue")) != "1")
            {
                if (sNewTotal != sOrigTotal)
                {
                    doProject = (clArray)doForm.doRS.GetFieldVal("LNK_RELATED_PR", 2);
                    for (i = 1; i <= doProject.GetDimension(); i++)
                    {
                        clRowSet doRSProject = new clRowSet("PR", 3, "GID_ID='" + doProject.GetItem(i) + "'", null, "MLS_STATUS");
                        if (doRSProject.GetFirst() == 1)
                        {
                            if (Convert.ToInt32(doRSProject.GetFieldVal("MLS_STATUS", 2)) == 6)
                            {
                                // Pass the Project's ID to MessageBoxEvent
                                sID = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_PR"));
                                doForm.oVar.SetVar("UpdatedProjectValue", "1");
                                doForm.MessageBox("Would you like to update the linked Project's 'RFQ Value' with the total from this Quote?", clC.SELL_MB_YESNO, null, null, null, null, "Yes", "MessageBoxEvent", null, null, doForm, null, "Yes", null, null, sID, "QT_FormOnSave_Post_UpdateValue");
                                return true;
                            }
                        }
                    }
                }
            }

            doForm.oVar.SetVar("UpdatedProjectValue", "");

            // TLD 1/29/2009 Compares status, if changed to Won or Lost
            // Asks user if they want to update related op statuses
            if (Convert.ToString(doForm.oVar.GetVar("OPQuestionRan")) != "1")
            {
                if (iNowStatus != iOpenStatus & Convert.ToString(iNowStatus) == "2" | Convert.ToString(iNowStatus) == "3")
                {
                    if (doForm.doRS.GetLinkCount("LNK_RELATED_OP") != 0)
                        // Asks user if they want to update relate OP Statues
                        doForm.MessageBox("Close Related Opportunities? Double-click on the Opportunities shown to change the status.", clC.SELL_MB_YESNO, "Change OP Status?", "YES", "NO", null, null, "MessageBoxEvent", "MessageBoxEvent", null, doForm, null, "YES", "NO", null, null, "QT_FORMONSAVE_POST_UPDATEOP");
                }
            }

            // TLD 10/3/2012 If Not already run and status changes
            if (Convert.ToString(doForm.oVar.GetVar("QT_OtherClonesExist")) != "1")
            {
                if (iNowStatus != iOpenStatus)
                {
                    if (doForm.doRS.IsLinkEmpty("LNK_OtherClones_QT") == false | doForm.doRS.IsLinkEmpty("LNK_OtherClonesFor_QT") == false)
                    {
                        doForm.oVar.SetVar("QT_OtherClonesExist", "1");
                        doForm.MessageBox("Other potential duplicate quotes exist.  Please review and update those statuses as necessary.");
                        doForm.SetControlState("TAB_FORM[13]", 0); // Visible just in case?
                        doForm.MoveToTab(13);
                        return false;
                    }
                }
            }
            par_doCallingObject = doForm;

            return true;
        }

        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doQuote = (clRowSet)par_doCallingObject;

            // TLD 6/1/2012 Prevent main from running
            // Custom enforce Reason Won/Lost here?
            goTR.StrWrite(ref par_sSections, "EnforceReasonWonLostAndDateClosed", "0");

            if (doQuote.bBypassValidation != true)
            {
                // If goScr.IsSectionEnabled(sProc, par_sSections, "EnforceReasonWonLostAndDateClosed") Then
                switch (doQuote.GetFieldVal("MLS_STATUS", 2))
                {
                    case 0      // Open, do nothing
                   :
                        {
                            break;
                        }

                    case 2      // Won
           :
                        {
                            if (Convert.ToInt32(doQuote.GetFieldVal("MLS_REASONWONLOST", 2)) == 0)
                            {
                                goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "mls_reasonwonlost"), "", "", "", "", "", "", "", "", "mls_reasonwonlost");
                                return false;
                            }
                            else if (goTR.IsDate(Convert.ToString(doQuote.GetFieldVal("DTE_DATECLOSED", 1))) == false | (Convert.ToString(doQuote.GetFieldVal("DTE_DateClosed"))) == "")
                                doQuote.SetFieldVal("DTE_DATECLOSED", goTR.NowLocal(), 2);
                            break;
                        }

                    case 3      // Lost
             :
                        {
                            if (Convert.ToInt32(doQuote.GetFieldVal("MLS_REASONWONLOST", 2)) == 0)
                            {
                                goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "mls_reasonwonlost"), "", "", "", "", "", "", "", "", "mls_reasonwonlost");
                                return false;
                            }
                            else if (goTR.IsDate(Convert.ToString(doQuote.GetFieldVal("DTE_DATECLOSED", 1))) == false | (Convert.ToString(doQuote.GetFieldVal("DTE_DateClosed")) == ""))
                                doQuote.SetFieldVal("DTE_DATECLOSED", goTR.NowLocal(), 2);
                            break;
                        }

                    default:
                        {
                            if (Convert.ToInt32(doQuote.GetFieldVal("MLS_REASONWONLOST", 2)) == 0)
                            {
                                goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "mls_reasonwonlost"), "", "", "", "", "", "", "", "", "mls_reasonwonlost");
                                return false;
                            }

                            break;
                        }
                }
            }

            par_doCallingObject = doQuote;

            return true;
        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sWork;
            string sDateSent = "";
            int i = 0;
            string sDueDate = Convert.ToString(doRS.GetFieldVal("DTE_DUEDATE", 2));
            int iDays = 0;

            // Update linked Project's Value if Project Status <> Bid
            if (doRS.GetLinkCount("LNK_RELATED_PR") > 0)
            {
                // TLD 10/5/2010 Mod RS to optimize
                // Dim doRSProject As New clRowSet("PR", 1, "GID_ID='" & doRS.GetFieldVal("LNK_RELATED_PR") & "'")
                clRowSet doRSProject = new clRowSet("PR", 1, "GID_ID='" + doRS.GetFieldVal("LNK_RELATED_PR") + "'", null, "*");
                if (doRSProject.GetFirst() == 1)
                {
                    if (Convert.ToInt32(doRSProject.GetFieldVal("MLS_STATUS", 2)) != 6)
                    {
                        // Set the following
                        // Status=Bid; Bid Date = Today; Revenue=Quote subtotal; Value=
                        // Quote subtotal;Q80Bid=checked; History
                        doRSProject.SetFieldVal("MLS_STATUS", "Bid");
                        doRSProject.SetFieldVal("DTE_Q50", "Today");
                        doRSProject.SetFieldVal("CUR_REVENUE", doRS.GetFieldVal("CUR_SUBTOTAL"));
                        doRSProject.SetFieldVal("CUR_VALUE", doRS.GetFieldVal("CUR_SUBTOTAL"));
                        doRSProject.SetFieldVal("CHK_Q50", 1, 2);
                        sWork = Convert.ToString(doRSProject.GetFieldVal("MMO_HISTORY"));
                        sWork = goTR.WriteLogLine(sWork, "Created Quote" + doRS.GetFieldVal("SYS_NAME"));
                        doRSProject.SetFieldVal("MMO_HISTORY", sWork);
                        // TLD 10/5/2010 Mod to write to XL Log instead of erroring
                        // If doRSProject.Commit <> 1 Then
                        // 'Can't update project
                        // goErr.SetError(35000, sProc, "Unable to update linked Project record.")
                        // Return False
                        // End If
                        if (doRSProject.Commit() == 0)
                        {
                            if (goErr.GetLastError("NUMBER") == "E47250")
                                // failed due to permissions, write to log
                                goLog.Log(sProc, "QT update of connected PR Value failed for PR '" + doRSProject.GetFieldVal("TXT_PROJECTNAME") + " due to permissions.'", clC.SELL_LOGLEVEL_NONE, true);
                            else
                                // write message to log with error
                                goLog.Log(sProc, "QT update of connected PR Value failed for PR '" + doRSProject.GetFieldVal("TXT_PROJECTNAME") + " with error " + goErr.GetLastError("NUMBER") + "'", clC.SELL_LOGLEVEL_NONE, true);
                        }
                    }
                }
            }

            // Add/Edit Quote Detail record
            scriptManager.RunScript("Quote_FillQuoteDetail", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            // -----------------Sent Overdue Functionality
            // TLD 4/5/2010 Commented autoalert, don't run record onsave from there instead
            // If NOT coming from AutoAlert, then run else don't run
            // If goP.GetVar("FromAutoAlert") <> "1" Then
            // TLD 3/31/2010 First, record Sent Date
            if (Convert.ToInt32(doRS.GetFieldVal("CHK_SENT", 2)) == 1)
            {
                // If Send Date is Blank, then this is the first time it has been sent, so record today
                if (Convert.ToDateTime(doRS.GetFieldVal("DTE_SENDDATE", 2)) == Convert.ToDateTime(goTR.StringToDateTime(clC.SELL_BLANK_DATETIME,"","",ref par_iValid,"|",false)))
                {
                    // Then this is first time being sent, so set date to today
                    sDateSent = Convert.ToString(goTR.NowLocal().Date);
                    doRS.SetFieldVal("DTT_SENDDATE", goTR.NowLocal(), 2);
                    // TLD 4/1/2010 Mod to ALWAYS set send time to noon, so eastern time zone user days
                    // overdue calcs won't appear off if they send a quote close to midnight.
                    doRS.SetFieldVal("DTT_SENDDATE", goTR.StringToDateTime(sDateSent + "|12:00","","",ref par_iValid,"|",false), 2);
                }
                else
                {
                    // Date Sent is NOT blank, grab it from the field
                    sDateSent = Convert.ToString(doRS.GetFieldVal("DTE_SENDDATE", 2));
                }
                  
            }
            else
                // Sent is NOT checked, so send date should be blank
                sDateSent = "";

            // TLD 3/31/2010 Checks to see if due date is blank
            if (Convert.ToDateTime(doRS.GetFieldVal("DTE_DUEDATE", 2)) != Convert.ToDateTime(goTR.StringToDateTime(clC.SELL_BLANK_DATETIME,"","",ref par_iValid,"|",false)))
            {
                if (sDateSent != "")
                {
                    if (Convert.ToDateTime(sDueDate) < Convert.ToDateTime(Strings.Left(sDateSent, 10)))
                    {
                        // Due Date is before Sent Date, Check Overdue checkbox
                        doRS.SetFieldVal("CHK_SENDOVERDUE", 1);
                    }
                    else
                    {
                        // Due Date is NOT before Sent Date
                        doRS.SetFieldVal("CHK_SENDOVERDUE", 0);
                    }
                        

                    // Calculate days different between due and Sent date
                    iDays = Convert.ToInt32(DateAndTime.DateDiff(DateInterval.Day, Convert.ToDateTime(doRS.GetFieldVal("DTE_DUEDATE", 2)), Convert.ToDateTime(goTR.StringToDate(sDateSent, "",ref par_iValid))));
                    if (iDays < -32767 | iDays > 32767)
                        iDays = 0;
                }
                else
                {
                    string par_sDelim =null;
                    // Date Sent is blank, check to see if due date is before today
                    if (Convert.ToDateTime(sDueDate) < Convert.ToDateTime(goTR.DateTimeToString(goTR.NowUTC(), "", "", ref par_iValid, ref par_sDelim)))
                        // Calulcate overdue if duedate is before today
                        // Check Overdue checkbox
                        doRS.SetFieldVal("CHK_SENDOVERDUE", 1);
                    else
                        // Due Date is NOT before today
                        doRS.SetFieldVal("CHK_SENDOVERDUE", 0);// Overdue should NOT be checked
                                                               // Calculate days different between due and now
                    iDays = Convert.ToInt32(DateAndTime.DateDiff(DateInterval.Day, Convert.ToDateTime(doRS.GetFieldVal("DTE_DUEDATE", 2)), goTR.NowUTC()));
                    if (iDays < -32767 | iDays > 32767)
                        iDays = 0;
                }
            }
            else
            {
                // Due Date is blank, so reset Days Overdue to 0, uncheck overdue
                iDays = 0;
                doRS.SetFieldVal("CHK_SENDOVERDUE", 0);
            }

            doRS.SetFieldVal("INT_DAYSOVERDUE", iDays);

            // Reset var
            // goP.SetVar("FromAutoAlert", "")
            // End If
            // -----------------Sent Overdue Functionality

            par_doCallingObject = doRS;
            return true;
        }

        public bool QT_FormAfterSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PJ 1/13/09: Added one line for case of revision, clear link LNK_Connected_QE in quote lines
            // MI 4/26/07
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sID;
            clRowSet doRS;
            // Dim doOrigQL As clRowSet
            clRowSet doNewQL;
            clRowSet doOrigQuote;
            int i;
            var bQLNotFound = false;
            string sQuoteOpeningMode;
            string sMessage;
            string sAlertMessage = "";

            if (doForm.GetMode() == "CREATION")
            {
                if (Convert.ToString(doForm.oVar.GetVar("QT_AddQuoteLine")) == "1")
                {
                    // Redisplay the same form so the user thinks the form never went away
                    Form doFormSame = new Form("QT", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), "");
                    goUI.Queue("FORM", doFormSame);
                }
                sQuoteOpeningMode = Convert.ToString(doForm.oVar.GetVar("QuoteOpeningMode"));
                switch (sQuoteOpeningMode)
                {
                    case "Duplicate":
                    case "Revision":
                        {
                            // Create Quote Lines by copying the original ones
                            sID = Convert.ToString(doForm.oVar.GetVar("QuoteOrinalQuoteID"));
                            // TLD 10/5/2010 Mod RS to optimize
                            // doRS = New clRowSet("QL", clC.SELL_EDIT, "LNK_In_QT='" & sID & "'")
                            doRS = new clRowSet("QL", clC.SELL_EDIT, "LNK_In_QT='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "*");
                            // For each quote line found create a new one, linked to the new Quote
                            goP.SetVar("bDoNotUpdateQuote", "1");
                            for (i = 1; i <= doRS.Count(); i++)
                            {
                                switch (sQuoteOpeningMode)
                                {
                                    case "Duplicate":
                                        {
                                            // ---- Technique 1: copy only model-related fields ----
                                            doNewQL = new clRowSet("QL", clC.SELL_ADD, null, null, null, 0, null, null, "CRL_QL", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), null, true); // bBypassValidation
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       // doNewQL.SetFieldVal("LNK_in_QT", doForm.doRS.GetFieldVal("GID_ID"))
                                            doNewQL.SetFieldVal("LNK_For_MO", doRS.GetFieldVal("LNK_For_MO", 2), 2);
                                            if (doNewQL.GetLinkCount("LNK_CreditedTo_US") < 1)
                                                doNewQL.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));
                                            // doNewQL.SetFieldVal("LNK_CreatedBy_US", goP.GetUserTID())      'System linked - filled automatically
                                            if (doNewQL.GetLinkCount("LNK_Peer_US") < 1)
                                                doNewQL.SetFieldVal("LNK_Peer_US", doNewQL.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
                                            doNewQL.SetFieldVal("SR__LineNo", doRS.GetFieldVal("SR__LineNo", 2), 2);
                                            doNewQL.SetFieldVal("SR__Qty", doRS.GetFieldVal("SR__Qty", 2), 2);
                                            doNewQL.SetFieldVal("TXT_Unit", doRS.GetFieldVal("TXT_Unit"));
                                            doNewQL.SetFieldVal("CUR_PriceUnit", doRS.GetFieldVal("CUR_PriceUnit", 2), 2);
                                            doNewQL.SetFieldVal("SR__DiscPercent", doRS.GetFieldVal("SR__DiscPercent", 2), 2);
                                            doNewQL.SetFieldVal("CUR_Subtotal", doRS.GetFieldVal("CUR_Subtotal", 2), 2);
                                            doNewQL.SetFieldVal("CUR_DiscAddlAmt", doRS.GetFieldVal("CUR_DiscAddlAmt", 2), 2);
                                            doNewQL.SetFieldVal("CUR_PriceUnitAfterDisc", doRS.GetFieldVal("CUR_PriceUnitAfterDisc", 2), 2);
                                            doNewQL.SetFieldVal("CUR_Cost", doRS.GetFieldVal("CUR_Cost", 2), 2);
                                            doNewQL.SetFieldVal("CUR_GrossProfit", doRS.GetFieldVal("CUR_GrossProfit", 2), 2);
                                            doNewQL.SetFieldVal("CHK_Taxable", doRS.GetFieldVal("CHK_Taxable", 2), 2);
                                            doNewQL.SetFieldVal("CHK_Report", doRS.GetFieldVal("CHK_Report", 2), 2);
                                            doNewQL.SetFieldVal("CHK_Include", doRS.GetFieldVal("CHK_Include", 2), 2);
                                            doNewQL.SetFieldVal("TXT_Model", doRS.GetFieldVal("TXT_Model"));
                                            doNewQL.SetFieldVal("MMO_Details", doRS.GetFieldVal("MMO_Details"));

                                            // TLD 2/15/2013 Copy more fields
                                            doNewQL.SetFieldVal("TXT_CommCode", doRS.GetFieldVal("TXT_CommCode"));
                                            doNewQL.SetFieldVal("TXT_CommissionPerc", doRS.GetFieldVal("TXT_CommissionPerc"));
                                            doNewQL.SetFieldVal("CUR_Commission", doRS.GetFieldVal("CUR_Commission", 2), 2);
                                            doNewQL.SetFieldVal("CUR_UnitCost", doRS.GetFieldVal("CUR_UnitCost", 2), 2);
                                            doNewQL.SetFieldVal("SR__UnitMargin", doRS.GetFieldVal("SR__UnitMargin", 2), 2);
                                            doNewQL.SetFieldVal("SR__Margin", doRS.GetFieldVal("SR__Margin", 2), 2);

                                            // 'Fields filled in QL_FormOnLoadRecord:
                                            // doForm.doRS.SetFieldVal("DTT_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTT_TIME"))
                                            // doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_EXPCLOSEDATE"))
                                            // doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2), 2)
                                            // doForm.doRS.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_PEER_US", 2), 2)
                                            // doForm.doRS.SetFieldVal("LNK_RELATED_PR", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_RELATED_PR", 2), 2)
                                            // doForm.doRS.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_TO_CO", 2), 2)
                                            // doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"))

                                            // 'Fields filled in CRL_QL:
                                            // LNK_IN_QT=<%GID_ID%>
                                            // LNK_Related_PR=<%LNK_Related_PR%>
                                            // MLS_REASONWONLOST=<%MLS_REASONWONLOST%>
                                            // MLS_Status=<%MLS_Status%>

                                            if (doNewQL.Commit() != 1)
                                            {
                                                goP.DeleteVar("bDoNotUpdateQuote");
                                                // CS 6/23/08 Reset var
                                                goP.SetVar("USEQTSTATUS", "");
                                                // CS 8/27/08 Reset var
                                                goP.SetVar("USEQTUSERS", "");
                                                goErr.SetError(0,"","","","","","","","","","","","");
                                                return false;
                                            }

                                            break;
                                        }

                                    case "Revision":
                                        {
                                            // ---- Copy all fields, then reset them as needed ------
                                            // doOrigQL = New clRowSet("QL", clC.SELL_EDIT, "GID_ID='" & doRS.GetFieldVal("GID_ID") & "'")
                                            // If doOrigQL.Count < 1 Then
                                            // 'Record not found - skip it and tell the user later
                                            // bQLNotFound = True
                                            // Else
                                            doNewQL = new clRowSet("QL", clC.SELL_ADD, null, null, null, 0, null, null, "CRU_QL", null, null, true); // True: bBypassValidation
                                            if (!goData.CopyRecord(ref doRS, ref doNewQL))
                                            {
                                                goP.DeleteVar("bDoNotUpdateQuote");
                                                // CS 6/23/08 Reset var
                                                goP.SetVar("USEQTSTATUS", "");
                                                // CS 8/27/08 Reset var
                                                goP.SetVar("USEQTUSERS", "");
                                                goErr.SetError(35000, sProc, "Error running goData.CopyRecord(). Are both rowsets in clc.SELL_EDIT mode?");
                                                return false;
                                            }
                                            else
                                            {
                                                // Link the line to the new quote
                                                doNewQL.ClearLinkAll("LNK_In_QT");
                                                doNewQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("GID_ID"), clC.SELL_SYSTEM);
                                                // Reset datetime, quote datetime
                                                doNewQL.SetFieldVal("DTT_Time", "Today|Now");
                                                doNewQL.SetFieldVal("DTT_QTETIME", doForm.doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM), clC.SELL_SYSTEM);
                                                // Reset Status, Reason, and Completed
                                                // CS 8/3/07: Per PJ, statuses/reasons should remain as they were in original QT
                                                // doNewQL.SetFieldVal("MLS_Status", 0, clC.SELL_SYSTEM)           'Open
                                                // doNewQL.SetFieldVal("MLS_ReasonWonLost", 0, clC.SELL_SYSTEM)    '<Make selection>
                                                doNewQL.SetFieldVal("DTT_TimeCompleted", "");
                                                // >>>>remove QE here<<<<<<

                                                doNewQL.ClearLinkAll("LNK_Connected_QE");

                                                // TLD 10/12/2012 Need to NOT copy Option fields?
                                                doNewQL.ClearLinkAll("LNK_Primary_QL");
                                                doNewQL.ClearLinkAll("LNK_Component_QL");
                                                doNewQL.SetFieldVal("CHK_SubComponent", 0, 2);

                                                if (doNewQL.Commit() != 1)
                                                {
                                                    goP.DeleteVar("bDoNotUpdateQuote");
                                                    // CS 6/23/08 Reset var
                                                    goP.SetVar("USEQTSTATUS", "");
                                                    // CS 8/27/08 Reset var
                                                    goP.SetVar("USEQTUSERS", "");
                                                    goErr.SetError(0, "", "", "", "", "", "", "", "", "", "", "", "");
                                                    return false;
                                                }
                                            }

                                            break;
                                        }
                                }
                                if (doRS.GetNext() != 1)
                                    break;
                            }
                            goP.DeleteVar("bDoNotUpdateQuote");

                            if (sQuoteOpeningMode == "Revision")
                            {
                                // Set Status of the original quote to Revised (6)
                                // TLD 10/5/2010 Mod RS to optimize
                                // doOrigQuote = New clRowSet("QT", clC.SELL_EDIT, "GID_ID='" & sID & "'")
                                doOrigQuote = new clRowSet("QT", clC.SELL_EDIT, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "*");
                                if (doOrigQuote.Count() > 0)
                                {
                                    doOrigQuote.SetFieldVal("MLS_Status", 6, clC.SELL_SYSTEM);
                                    if (doOrigQuote.Commit() != 1)
                                        sMessage = "The Status of the original Quote can't be changed to 'Revised'. It will be reported in totals as a duplicate of the quote you just created. Please contact your Selltis administrrator. Quote Name: '" + doOrigQuote.GetFieldVal("SYS_Name") + "'. Quote ID: '" + sID + "'.";
                                    // Change the status of all linked QLs to 'revised (6)'
                                    // Have this above: doRS = New clRowSet("QL", clC.SELL_EDIT, "LNK_In_QT='" & sID & "'")
                                    if (doRS.GetFirst() == 1)
                                    {
                                        do
                                        {
                                            doRS.SetFieldVal("MLS_STATUS", 6, 2);
                                            if (doRS.Commit() != 1)
                                                sMessage = "The Status of one or more of the original Quote lines can't be changed to 'Revised'. Please contact your Selltis administrator. Quote Name: '" + doOrigQuote.GetFieldVal("SYS_NAME") + "'. Quote ID: '" + sID + "'.";
                                            if (doRS.GetNext() == 0)
                                                break;
                                        }
                                        while (true);
                                    }
                                }
                            }

                            // Recalc the new quote
                            scriptManager.RunScript("CalcQuoteTotal",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections,null,"","","","","");

                            if (Convert.ToString(doForm.oVar.GetVar("QuoteDuplicateManageLines")) == "1")
                            {
                                // Redisplay the Quote form
                                Form doFormSame = new Form("QT", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), "");
                                if (bQLNotFound)
                                    doFormSame.MessageBox("One or more Quote Lines from the original Quote couldn't be created because they don't exist. They may have been deleted by another user.");
                                doFormSame.MessagePanel("Click the buttons next to the Lines linkbox to create, edit, or remove the lines.", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "info.gif");
                                goUI.Queue("FORM", doFormSame);
                            }

                            break;
                        }
                }
            }

            // TLD 1/15/2013 Don't send if CreditedToUsr is Me
            // TLD 10/23/2012 Need to send an alert to RSM if any changes
            // made, but can't figure out how to do it without getting two alerts
            // when user creates, more testing.....
            string sQuoteNo = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QuoteNo"));
            string sUser = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CreditedTo_US"));
            if (Convert.ToString(goP.GetVar("QT_AlertSent")) != sQuoteNo)
            {
                if (doForm.GetMode() == "CREATION")
                    sAlertMessage = "New";
                else if (doForm.IsDirty)
                    sAlertMessage = "Updated";
                if (sAlertMessage != "" & sUser != goP.GetMe("ID"))
                {
                    goUI.AddAlert(sAlertMessage + " Quote", clC.SELL_ALT_OPENRECORD, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), Convert.ToString(doForm.doRS.GetFieldVal("LNK_CreditedTo_US")), "Quote16.gif");
                    goP.SetVar("QT_AlertSent", sQuoteNo);
                }
            }

            // CS 6/23/08 Reset var
            goP.SetVar("USEQTSTATUS", "");
            // CS 8/27/08
            goP.SetVar("USEQTUSERS", "");

            par_doCallingObject = doForm;

            return true;
        }
        public bool QT_FormControlOnChange_BTN_LINEDELETE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 9/26/2012 Custom to prompt user before deleting
            par_bRunNext = false;

            // CS 5/21/09: When a QL is deleted via the form we now update the QT's totals on disk. Previously the user had to click Save on the
            // QT for the total to 'stick'.

            Form doForm = (Form)par_doCallingObject;
            // Dim doSource As Object
            string sRecID;

            // Check if have permissions to edit this QT
            if (goData.GetRecordPermission(Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot delete a Quote Line.");
                return true;
            }

            sRecID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (sRecID == "")
                doForm.MessageBox("Please select a Quote Line to delete.");

            // TLD 9/26/2012 Need to ask user if they are sure they want to delete?
            doForm.MessageBox("Are you sure you want to delete the selected quote line?", clC.SELL_MB_YESNO, null, null, null, null, null, "MessageBoxEvent", "MessageBoxEvent", null, null, null, "YES", "NO", null, null, "QT_DELETEQL");

            par_doCallingObject = doForm;

            return true;
        }
        public bool QT_FormControlOnChange_BTN_SaveMarkSent_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 2/15/2010 Simulates Send without sending
            // Checks Sent checkbox, writes to history, then saves
            // Copied from QT_FormControlOnChange_BTN_CORR

            Form doForm = (Form)par_doCallingObject;
            string sWork = "";
            string sDateStamp = "";
            string sNewEntry = "";
            // Dim doNewForm As Object

            // Dim sURL As String
            // Dim sName As String

            // If goP.GetProduct() = "MB" Then
            // sURL = "../Pages/Mobile_DiaSend.aspx"
            // Else
            // sURL = "../Pages/diaSend.aspx"
            // End If
            // 'objectotsend: VIEW, RECORD, CORR
            // goTR.URLWrite(sURL, "objecttosend", "CORR")

            // We don't know how the user wants to send a Quote 
            // Send type: EMAIL, FAX, LETTER, or WPRESPONSE
            // goTR.URLWrite(sURL, "sendtype", "EMAIL")

            // goTR.URLWrite(sURL, "objectid", doForm.doRS.GetFieldVal("GID_ID"))
            // sName = doForm.doRS.GetFieldVal("SYS_Name")
            // 'CS: Always update sys name in case one of the affected fields changed.
            // 'If sName = "" Then
            // goScr.RunScript("GenerateSysName", doForm.doRS, , , , , , , sName)
            // 'End If
            // goTR.URLWrite(sURL, "objectname", sName)
            // 'goTR.URLWrite(sURL, "returnto", "MI_Test.aspx")



            // If doForm.Save(1, , System.Reflection.MethodInfo.GetCurrentMethod().Name) = 0 Then
            // Return False
            // End If

            // Check Sent checkbox
            doForm.doRS.SetFieldVal("CHK_SENT", 1, 2);

            // ----- Write an entry in the History field 
            sWork = Convert.ToString(doForm.doRS.GetFieldVal("MMO_HISTORY"));
            // Get Date Time Stamp
            scriptManager.RunScript("GetDateTimeStamp",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections, null, "NEUTRAL", null, null, "USERNOOFFSETLABEL", null); // returns var sDateStamp

            sNewEntry = sDateStamp + " GMT " + goP.GetMe("Code") + " Sent";
            // Write the History field
            doForm.doRS.SetFieldVal("MMO_HISTORY", sNewEntry + Constants.vbCrLf + sWork);

            // CS 6/28/07:Changing to doForm.save(2) so that the _post script will be called if there is any. When
            // set to doform.save(1) the code never returned here after save so the function never
            // returned true and the post script would never be called. So now the post script can be called and I
            // just set a boolean that tells the form to close so that the Send dialog displays.
            if (doForm.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                return false;

            // CS 9/13/08 Added
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
            goUI.Queue("", "");
            // CS 9/13/08: Commenting out below. 
            // Reason: Open an existing QT; edit its contents; Create linked correspondence
            // and send it. When the send dialog opens, both the corr activity AND the Quote are
            // closed and thus changes are lost.
            // goUI.SetNext("DIALOG", sURL) 'CS: Moved to after save call. Was before
            // doForm.CloseOnReturn = True 'flag       

                par_doCallingObject = doForm;
            return true;
        }
        public bool Quote_FillQuoteDetail(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            clRowSet doRSQD;
            string sINI = "";

            // This function for adding/editing QD record from save of a Quote.
            // Check if a Quote detail record exists; if not, create one
            if (doRS.GetLinkCount("LNK_RELATED_QD") == 0)
            {
                doRSQD = new clRowSet("QD", 2, null, null, null, 0, null, null, "CRL_QD", Convert.ToString(doRS.GetFieldVal("GID_ID")));
                doRSQD.SetFieldVal("LNK_RELATED_QT", doRS.GetFieldVal("GID_ID"));
            }
            else
            {
                // doRSQD = New clRowSet("QD", 1, "LNK_RELATED_QT='" & doRS.GetFieldVal("GID_ID") & "'")
                // If doRSQD.GetFirst() <> 1 Then
                // goErr.SetError(35000, sProc, "Creating/Updating Quote Detail record failed. Cannot find linked Quote Detail.")
                // Return False
                // End If
                // 9e7865cd-2427-4c8d-5154-9b52011e1fa4 = Original quote, linked to this QD
                // b183e735-10f0-4f0d-5144-9b52011e95a7 = dors.getfieldval("LNK_related_QD") = Quote Detail
                // e97d389c-26c8-4733-5154-9b8f010c23b8 = doRS.GetFieldVal("GID_ID") = this quote

                // CS 10/14/08:
                // Loophole for user without R permission on QE, but still needs to edit QE
                goTR.StrWrite(ref sINI, "ACCESSTYPE", "E");
                goTR.StrWrite(ref sINI, "TABLENAME", "QD");
                goTR.StrWrite(ref sINI, "FIELDS", "**");
                goTR.StrWrite(ref sINI, "CONDITION", "LNK_RELATED_QT='" + doRS.GetFieldVal("GID_ID") + "'");
                goTR.StrWrite(ref sINI, "SORT", "SYS_NAME");
                goTR.StrWrite(ref sINI, "GETALLUSERSUNSHAREDRECS", false);
                goTR.StrWrite(ref sINI, "MODE", "SELECT");

                doRSQD = new clRowSet("QD", 1, null, null, null, 0, sINI);
            }

            // Set values in Quote detail record
            // When commit it will cause function to CalcMargin
            if (doRSQD.Commit() != 1)
            {
                goErr.SetError(35000, sProc, "Creating/Updating Quote Detail record failed.");
                return false;
            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool QuoteDetail_CalcMargin(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            int i;
            int iQty;
            decimal cSubTotal;
            decimal cCommUnit;
            decimal cCostUnit;
            decimal cTotalPrice=0;
            decimal cTotalCost=0;
            decimal cTotalComm=0;
            decimal cMargin;

            try
            {
                clArray doQuotelines = (clArray)doRS.GetFieldVal("LNK_RELATED_QT%%LNK_CONNECTED_QL", 2);
                for (i = 1; i <= doQuotelines.GetDimension(); i++)
                {
                    clRowSet doRSQL = new clRowSet("QL", 3, "LNK_IN_QT='" + doQuotelines.GetItem(i) + "'", null/* Conversion error: Set to default value for this argument */, "SR__QTY,CUR_SUBTOTAL,CUR_COMMISSIONPERUNIT,LNK_FOR_MO%%LNK_CONNECTED_ML%%CUR_COST");
                    if (doRSQL.GetFirst() == 1)
                    {
                        // Get QL Qty, Subtotal, Comm per unit, for Mo
                        iQty = Convert.ToInt32(doRSQL.GetFieldVal("SR__QTY", 2));
                        cSubTotal = Convert.ToDecimal(doRSQL.GetFieldVal("CUR_SUBTOTAL", 2));
                        cCommUnit = Convert.ToDecimal(doRSQL.GetFieldVal("CUR_COMMISSIONPERUNIT", 2));
                        cCostUnit = Convert.ToDecimal(doRSQL.GetFieldVal("LNK_FOR_MO%%LNK_CONNECTED_ML%%CUR_COST"));
                        cTotalPrice = cTotalPrice + cSubTotal;
                        cTotalCost = cTotalCost + (iQty * cCostUnit);
                        cTotalComm = cTotalComm + (iQty * cCommUnit);
                    }
                }

                if (cTotalPrice > 0)
                    cMargin = (1 - ((cTotalCost + cTotalComm) / (decimal)cTotalPrice)) * 100;
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doRS;

            return true;
        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //Form doObject = (Form)par_doCallingObject;
            clRowSet doObject = null;  // form or rowset

            par_bRunNext = false;

            if (par_s1 == "doRS")
            {
                 doObject = (clRowSet)par_doCallingObject;

            }
            else
            {
                doObject = ((Form)par_doCallingObject).doRS;

            }

            decimal cPriceUnit;
            double rQtyFld;
            double rDiscPerc;
            decimal cDiscAddAmt;
            decimal cCostVal;
            decimal cWork;
            decimal cSubtotal;
            decimal cUnitCostVal;
            string sCommPerc;
            decimal cCommUnit=0;
            decimal cCost=default(decimal);
            decimal cMargin=default(decimal);
            decimal cUnitPriceFld;
            string sSpec = "";
            int i;
            // TLD 10/5/2010 New var to minimize getfields
            int lOverride = 0;
            int lType = 0;

            // PURPOSE:
            // Calc Subtotal if Include is checked, otherwise enter 0 as subtotal
            // Field 'Price Unit No Disc' = Unit Price before discount
            // Field 'Price Unit' = Unit Price after discount
            // RETURNS:
            // True.

            // TLD 10/5/2010 Set new vars
            lOverride = Convert.ToInt32(doObject.GetFieldVal("CHK_OVERRIDE", 2));
            lType = Convert.ToInt32(doObject.GetFieldVal("MLS_TYPE", 2));


            // CUSTOMIZED; overriding core
            // Call custom function to calc unit price before calc total
            if (par_s1 == "doRS")
            {
                scriptManager.RunScript("QuotLine_CalcUnitPrice", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "doRS");

            }
            else
            {
                scriptManager.RunScript("QuotLine_CalcUnitPrice", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            }

            cPriceUnit = Convert.ToDecimal(doObject.GetFieldVal("CUR_PRICEUNIT", 2));
            rQtyFld = Convert.ToDouble(doObject.GetFieldVal("SR__QTY", 2));
            rDiscPerc = Convert.ToDouble(doObject.GetFieldVal("SR__DISCPERCENT", 2));
            cDiscAddAmt = Convert.ToDecimal(doObject.GetFieldVal("CUR_DISCADDLAMT", 2));
            cCostVal = Convert.ToDecimal(doObject.GetFieldVal("CUR_COST", 2));
            // NEW
            cUnitCostVal = Convert.ToDecimal(doObject.GetFieldVal("CUR_UNITCOST", 2));

            // Calc Commission
            sCommPerc = Convert.ToString(doObject.GetFieldVal("TXT_CommissionPerc"));
            if (sCommPerc != "")
            {
                // Is it a valid number
                if (goTR.IsNum(sCommPerc))
                {
                    // Validate value
                    if (goTR.StringToNum(sCommPerc,"",ref par_iValid,"") < 0 | goTR.StringToNum(sCommPerc, "", ref par_iValid, "") > 100)
                    // doForm.MessageBox("Please enter a number between 0 and 100 in the Commission Percent field or leave the field blank.")
                    // Return True
                    // Can't do MB in Rowset context; ok to just 0 it out??
                    {
                        sCommPerc = "";
                    }
                    else
                        // TLD 10/5/2010 Mod to use vars
                        // If doObject.GetFieldval("CHK_OVERRIDE", 2) = 0 Then
                        if (lOverride == 0)
                    {
                        // TLD 10/5/2010 Mod to use vars
                        // If doObject.GetFieldVal("MLS_TYPE", 2) = 1 Then 'LIST
                        if (lType == 1)
                            cCommUnit = Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(1 - rDiscPerc / 100) * Convert.ToDecimal(goTR.StringToNum(sCommPerc, "", ref par_iValid, "") / (double)100);
                        else
                        {
                            // Type is margin
                            cCost = Convert.ToDecimal(doObject.GetFieldVal("LNK_FOR_MO%%LNK_CONNECTED_ML%%CUR_COST"));
                            cMargin = Convert.ToDecimal(doObject.GetFieldVal("sr__MARGIN", 2));
                            cCommUnit = Convert.ToDecimal(cCost) / (decimal)(1 - Convert.ToDecimal(cMargin) / (decimal)100) * Convert.ToDecimal(goTR.StringToNum(sCommPerc,"",ref par_iValid,"") / (double)100);
                        }
                    }
                }
                else
                {
                    sCommPerc = "";

                }
            }
            doObject.SetFieldVal("CUR_CommissionPerUnit", cCommUnit, 2);
            doObject.SetFieldVal("CUR_Commission", Convert.ToDecimal(rQtyFld) * cCommUnit);

            // Calc Unit Price after discount
            // Calculate unit price after discount
            // TLD 10/5/2010 Mod to use vars
            // If doObject.GetfieldVal("MLS_TYPE", 2) = 1 Then 'List 
            if (lType == 1)
            {
                // TLD 10/5/2010 Mod to use vars
                // If doObject.GetfieldVal("CHK_OVERRIDE", 2) = 0 Then
                if (lOverride == 0)
                {
                    cWork =Convert.ToDecimal(cPriceUnit) - Convert.ToDecimal(cPriceUnit) * (Convert.ToDecimal(rDiscPerc) / 100);
                    // UnitPriceAfterDisc includes Comm per Unit
                    doObject.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork) + cCommUnit);
                    cSubtotal = Convert.ToDecimal(rQtyFld) * Convert.ToDecimal(doObject.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2));
                }
                else
                    // KAM 1/16/08 was getting an error when cPriceUnit was equal to zero b/c of divide by zero. Only do this calc if not zero.
                    if (cPriceUnit != 0)
                {
                    doObject.SetFieldVal("SR__DISCPERCENT", (1 - Convert.ToDouble(doObject.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2)) / (double)cPriceUnit) * 100);
                    cWork = Convert.ToDecimal(doObject.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2));
                    cSubtotal = Convert.ToDecimal(rQtyFld) * Convert.ToDecimal(cWork);
                }
                else
                    cSubtotal = 0;
                doObject.SetFieldVal("CUR_SUBTOTAL", cSubtotal, 2);

                // Fill QI subtotal for all Quote lines, including primary
                doObject.SetFieldVal("CUR_QISUBTOTAL", cSubtotal, 2);

                // Calc Cost and Gross Profit
                cCostVal = Convert.ToDecimal(cUnitCostVal) * Convert.ToDecimal(rQtyFld);
                cWork = cSubtotal - cCostVal - Convert.ToDecimal(doObject.GetFieldVal("CUR_COMMISSION", 2));
                doObject.SetFieldVal("CUR_COST", cCostVal, 2);
                doObject.SetFieldVal("CUR_GROSSPROFIT", cWork, 2);
            }
            else
            {
                // Type is not list
                if (cUnitCostVal == 0 & doObject.GetLinkCount("LNK_FOR_MO") == 1)
                {
                    // TLD 4/28/2009 More than 1 ML can be connected to the MO
                    // This causes an error, modified below to NOT get the cost if more
                    // than 1 model detail attached
                    clRowSet doMORS;
                    doMORS = new clRowSet("MO", 3, "GID_ID='" + doObject.GetFieldVal("LNK_FOR_MO%%GID_ID") + "'", null, "LNK_CONNECTED_ML,LNK_CONNECTED_ML%%CUR_COST");
                    clArray doLink = new clArray();
                    oTable = null;
                    doLink = doMORS.GetLinkVal("LNK_CONNECTED_ML", ref doLink, true, 0, -1, "A_a", ref oTable);
                    if (doLink.GetDimension() == 1)
                    {
                        cCost = Convert.ToDecimal(doMORS.GetFieldVal("LNK_CONNECTED_ML%%CUR_COST"));
                        doObject.SetFieldVal("CUR_UNITCOST", cCost, 2);
                    }
                    else
                        cCost = cUnitCostVal;
                }
                else
                    cCost = cUnitCostVal;

                // Margin
                cMargin = Convert.ToDecimal(doObject.GetFieldVal("SR__MARGIN", 2));

                // Disc %
                doObject.SetFieldVal("SR__DISCPERCENT", 0, 2);

                // If override not checked
                // If doObject.getfieldval("CHK_OvERRIDE", 2) = 0 Then
                if (lOverride == 0)
                {
                    if (cMargin < 100)
                    {
                        cUnitPriceFld = Convert.ToDecimal(cCost) / (decimal)1 - Convert.ToDecimal(cMargin) / (decimal)100 + Convert.ToDecimal(doObject.GetFieldVal("CUR_COMMISSION", 2)) / Convert.ToDecimal(rQtyFld);
                        cUnitPriceFld = Math.Round(cUnitPriceFld, 2);
                        doObject.SetFieldVal("CUR_PRICEUNITAFTERDISC", cUnitPriceFld, 2);
                    }
                }
                else
                    // CS 6/17/08: Was getting an error when CUR_PRICEUNITAFTERDISC is 0 b/c was trying to
                    // divide by 0. I am going to only set the CUR_MARGIN field if that
                    // field is not 0.
                    if (Convert.ToDecimal(doObject.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2)) != 0)
                    doObject.SetFieldVal("CUR_MARGIN", 1 - cCost / (decimal)doObject.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2) - Convert.ToDecimal(doObject.GetFieldVal("CUR_COMMISSION", 2)) / Convert.ToDecimal(rQtyFld) * 100);

                // Set fields
                doObject.SetFieldVal("CUR_SUBTOTAL", Convert.ToDecimal(rQtyFld) * Convert.ToDecimal(doObject.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2)));
                doObject.SetFieldVal("CUR_QISUBTOTAL", Convert.ToDecimal(rQtyFld) * cCost / (1 - cMargin / (decimal)100));
                doObject.SetFieldVal("CUR_COST", Convert.ToDecimal(rQtyFld) * cCost);
                doObject.SetFieldVal("CUR_GROSSPROFIT", Convert.ToDecimal(doObject.GetFieldVal("CUR_SUBTOTAL", 2)) - Convert.ToDecimal(doObject.GetFieldVal("CUR_COST", 2)) - Convert.ToDecimal(doObject.GetFieldVal("CUR_COMMISSION", 2)));
            }

            // Fill the Memo field
            // Append long description to QL Memo field. Overwrite Memo
            if (doObject.GetLinkCount("LNK_COMPONENT_QL") > 0)
            {
                clArray doComponents = (clArray)(doObject.GetFieldVal("LNK_COMPONENT_QL", 2));
                for (i = 1; i <= doComponents.GetDimension(); i++)
                {
                    clRowSet doRSComponents = new clRowSet("QL", 3, "GID_ID='" + doComponents.GetInfo(i) + "'", null, "MMO_DETAILS");
                    if (doRSComponents.GetFirst() == 1)
                    {
                        sSpec = sSpec + doRSComponents.GetFieldVal("MMO_DETAILS", 2);
                        if (doRSComponents.GetNext() == 0)
                            break;
                    }
                }
                doObject.SetFieldVal("MMO_DETAILS", sSpec);
                doComponents = null/* TODO Change to default(_) if this is not a reference type */;
            }

            // TLD 1/30/2009 Calculates Unit Margin %
            cWork = Convert.ToDecimal(doObject.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2));
            if (cWork != 0 & cUnitCostVal != 0)
            {
                doObject.SetFieldVal("SR__UNITMARGIN", Convert.ToDecimal(cWork) - cUnitCostVal / (decimal)cWork * 100);

            }
            else
            {
                doObject.SetFieldVal("SR__UNITMARGIN", 0);

            }

            if (Convert.ToDecimal(doObject.GetFieldVal("CHK_INCLUDE", 2)) != 1)
            {
                // Set calculated fields to 0
                doObject.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                doObject.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                doObject.SetFieldVal("CUR_COST", 0, 2);
                doObject.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                doObject.SetFieldVal("CUR_SALESTAX", 0, 2);
            }

            return true;
        }
        public bool Quotline_CalcUnitPrice_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Calc Unit Price of Quote Line based on Type

            clRowSet doObject = null; // Could be form or rowset
            int i;
            decimal cPriceUnit=0;
            decimal cCostTotal;
            decimal cUnitCostVal;
            string sNew = "";

            // If par_s1 = "doRS" Then
           doObject = (clRowSet)par_doCallingObject;
            // Else
            // doObject = par_doCallingObject.doRS
            // End If

            // 'TLD 10/8/2009 Don't need this any longer
            // '---------TLD 9/30/2009 Captures whether record is new
            // If doObject.GetType.ToString() = "clForm" Then
            // If doObject.GetMode() = "CREATION" Then
            // sNew = "Yes"
            // End If
            // End If

            // If doObject.GetType.ToString() = "clRowSet" Then
            // If doObject.iRSType() = 2 Then
            // sNew = "Yes"
            // End If
            // End If
            // '---------TLD 9/30/2009 Captures whether record is new

            try
            {
                if (doObject.GetLinkCount("LNK_COMPONENT_QL") == 0)
                {
                }
                else
                {
                    clArray doComponents = (clArray)doObject.GetFieldVal("LNK_COMPONENT_QL", 2);
                    for (i = 1; i <= doComponents.GetDimension(); i++)
                    {
                        clRowSet doRSComponents = new clRowSet("QL", 3, "GID_ID='" + doComponents.GetInfo(i) + "'", null, "CUR_QISUBTOTAL");
                        if (doRSComponents.GetFirst() == 1)
                        {
                            cPriceUnit = Convert.ToDecimal(cPriceUnit) + Convert.ToDecimal(doRSComponents.GetFieldVal("CUR_QISUBTOTAL", 2));
                            if (doRSComponents.GetNext() == 0)
                                break;
                        }
                    }
                    doObject.SetFieldVal("CUR_PRICEUNIT", cPriceUnit);
                    doComponents = null/* TODO Change to default(_) if this is not a reference type */;
                }

                // Fill Unit Cost if Type=List; Margin unit price filled in Calctotal
                if (Convert.ToInt32(doObject.GetFieldVal("MLS_TYPE", 2)) == 1)
                {
                    // TLD 10/8/2009 ONLY need to pull from ML if Unit Cost is 0
                    // This will solve all cases, I think.
                    // TLD -- ONLY sets unit cost here if new record
                    // Apparently, if user changes Model Cost Detail price on current QLs
                    // it updates the price, and they don't want it to do that
                    // unless the QL is new
                    // TLD 10/8/2009 added to check to see if 0.00
                    // if NOT zero, does not replace so that user
                    // can modify cost (set to 0.00 if user selects an MO)
                    // If sNew = "Yes" And doObject.GetFieldVal("CUR_UNITCOST") = 0 Then
                    if (Convert.ToInt32(doObject.GetFieldVal("CUR_UNITCOST")) == 0)
                    {
                        doObject.SetFieldVal("CUR_UNITCOST", doObject.GetFieldVal("LNK_FOR_MO%%LNK_CONNECTED_ML%%CUR_COST"));

                    }
                }
                else
                {
                    // TLD 12/12/2012 Commented for now, not sure what this should be....
                    // Waiting for their response
                    // Dim sQLID As String = doObject.GetFieldVal("GID_ID")
                    // If doObject.GetLinkCount("LNK_COMPONENT_QL") > 0 Then
                    // Dim doComponents As clArray = doObject.getfieldVal("LNK_COMPONENT_QL", 2)
                    // For i = 1 To doComponents.GetDimension
                    // 'TLD 12/7/2012 This should be QE, NOT QD.
                    // 'Dim doRSQuoteDetail As New clRowSet("QD", 3, "GID_ID='" & doComponents.GetItem(i) & "'", , "CUR_COST")
                    // Dim doRSQuoteDetail As New clRowSet("QE", 3, "LNK_Connected_QL='" & sQLID & "'", , "CUR_COST")
                    // If doRSQuoteDetail.GetFirst = 1 Then
                    // cCostTotal = cCostTotal + doRSQuoteDetail.GetFieldVal("CUR_COST", 2)
                    // If doRSQuoteDetail.GetNext = 0 Then Exit For
                    // End If
                    // Next
                    // doObject.SetFieldVal("CUR_UNITCOST", cCostTotal)
                    // Else
                    // This is a margin Quote line with no sub component QL's.
                    // Users cannot change Unit cost value, they can only change Margin
                    // or override. If Unit Cost > 0 do not overwrite. This is b/c
                    // costs may change over time
                    cUnitCostVal = Convert.ToDecimal(doObject.GetFieldVal("CUR_UNITCOST", 2));
                    if (doObject.GetLinkCount("LNK_FOR_MO") > 0)
                    {
                        // Get Model Detail
                        clArray doModelDetail = (clArray)doObject.GetFieldVal("LNK_FOR_MO", 2);
                        for (i = 1; i <= doModelDetail.GetDimension(); i++)
                        {
                            clRowSet doRSModelDetail = new clRowSet("ML", 3, "GID_ID='" + doModelDetail.GetItem(i) + "'", null, "CUR_COST");
                            if (doRSModelDetail.GetFirst() == 1)
                                // Is not looping thru; just getting the first one
                                cUnitCostVal = Convert.ToDecimal(doRSModelDetail.GetFieldVal("CUR_COST"));
                        }
                    }
                    doObject.SetFieldVal("CUR_UNITCOST", cUnitCostVal);
                }
                // End If

                // For subcomponents, uncheck include and fill spec so user can edit
                // before rolled into primary
                if (Convert.ToInt32(doObject.GetFieldVal("CHK_SUBCOMPONENT", 2)) == 1)
                {
                    // TLD 10/12/2012 Include needs to be checked for printing.
                    // doObject.SetfieldVal("CHK_INCLUDE", 0, 2)
                    if (Convert.ToString(doObject.GetFieldVal("MMO_DETAILS")) == "")
                    {
                        doObject.SetFieldVal("MMO_DETAILS", doObject.GetFieldVal("LNK_FOR_MO%%MMO_SPECIFICATIONS"));

                    }
                }
            }

            // DVF 3/10/2010 Test non-zeroing of QL values

            // See if user has cost permission (Quote Line detail)
            // If goPerm.GetSelectivePermission("QE", "R") = 0 Then
            // Zero out fields
            // doObject.SetFieldVal("CUR_UnitCost", 0, 2)
            // doObject.SetFieldVal("CUR_Cost", 0, 2)
            // doObject.SetFieldVal("CUR_Margin", 0, 2)
            // doObject.SetFieldVal("CUR_GrossProfit", 0, 2)
            // End If

            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool Quotline_FillItem_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // CS 10/24/08: If User doesn't have permission to read Quote Line
            // Cost Detail records then don't fill the CUR_UnitCost or CUR_Margin
            // DVF 3/10/2010 commented out - testing non-zeroing of QL values

            // Try
            // If goPerm.GetSelectivePermission("QE", "R") = 0 Then
            // 'Zero out fields
            // doForm.doRS.SetFieldVal("CUR_UnitCost", 0, 2)
            // doForm.doRS.SetFieldVal("CUR_Cost", 0, 2)
            // doForm.doRS.SetFieldVal("CUR_Margin", 0, 2)
            // doForm.doRS.SetFieldVal("CUR_GrossProfit", 0, 2)
            // End If


            // Catch ex As Exception
            // If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            // goErr.SetError(ex, 45105, sProc)
            // End If

            // End Try

            // TLD 12/8/2008 Modified main fill of TXT_UNIT
            // Fill unit with linked model's MLS_UNIT
            doForm.doRS.SetFieldVal("TXT_Unit", doForm.doRS.GetFieldVal("LNK_FOR_MO%%MLS_UNIT", 1));
            par_doCallingObject = doForm;

            return true;
        }

        public bool Utility_RunImportUtility(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                goUI.OpenURLExternal("../Pages/cus_diaImportMan.aspx", "Selltis", "height=840,width=1250,left=100,top=100,status=yes,location=no,toolbar=no,resizable=yes,titlebar=no,dependent=yes");
            }


            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool XW_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = "Script::XW_FormOnLoadREcord_Pre";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS");

            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                return false;
            }

            // Read values from MD and set in the form fields
            doForm.doRS.SetFieldVal("CHK_Company", goTR.StrRead(sWOP, "ONSAVECO_CREATE_AC"));
            par_doCallingObject = doForm;

            return true;
        }
        public bool XW_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = "Script::XW_FormOnSave_PRE";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS");

            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                return false;
            }

            // Write values to MD
            goTR.StrWrite(ref sWOP, "ONSAVECO_CREATE_AC", doForm.doRS.GetFieldVal("CHK_Company", 2));

            goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP, null, null, "XX");
            // --Write MD page setting Product parameter to "XX"

            doForm.CloseOnReturn = true;
            doForm.CancelSave();
            par_doCallingObject = doForm;
            return true;
        }

    }
}

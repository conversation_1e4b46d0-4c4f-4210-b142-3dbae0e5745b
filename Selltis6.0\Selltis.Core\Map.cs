﻿using Microsoft.VisualBasic;
using Selltis.BusinessLogic;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Selltis.Core
{
    public class Map : View
    {
        private clTransform goTR;
        private clData godata;
        private clMetaData goMeta;
        private clDefaults goDef;
        private bool isLoadFromSession = true;
        private string _viewMetaData = string.Empty;

        public string GroupedColumns { get; set; }
        public string GroupLinkFile { get; set; }
        public string File { get; set; }
        public string FilterText { get; set; }
        public string SortText { get; set; }
        public bool GroupedData { get; set; }
        public string MapLatitudeField { get; set; }
        public string MapLongitudeField { get; set; }
        public string MapRegionFile { get; set; }
        public string MapRegionRAWDataField { get; set; }
        public string MapRegionFilterCondition { get; set; }
        public string MapIconField { get; set; }
        public string ShowLines_Markers { get; set; }
        public IList<GridColumn> Columns { get; set; }
        public IList<string> Sorts { get; set; }
        public int ViewWidth { get; set; }
        public bool IsInvalid { get; set; }
        public string DefaultSortField { get; set; }
        public string DefaultSortDirection { get; set; }
        public string CURSymbols { get; set; }
        private string GetCurSymbolFromMeta(string strMeta)
        {
            string retval = "$";

            if (!string.IsNullOrEmpty(strMeta))
            {
                var strMetaarr = strMeta.Split('|');
                if (strMetaarr.Length > 6)
                {
                    retval = strMetaarr[6];
                }
            }

            return retval;
        }
        private void GetColumnsCollection()
        {
            goTR = (clTransform)Util.GetInstance("tr");
            int ColumnCount = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COLCOUNT", 0));
            var fldmata = goMeta.PageRead("GLOBAL", "fld_" + TableName, "", true);
            //goTR.StrRead(fldmata, "VIEWCOUNT", "0")
            Columns = new List<GridColumn>();
            
            //RN #1873 Multiselect functionality added.
            if (ViewMultiSelect == "1")
            {
                GridColumn _reportcolumn = new GridColumn();
                _reportcolumn.Name = "CheckBoxColumn";
                _reportcolumn.Alignment = "L";
                _reportcolumn.IsSortable = false;
                _reportcolumn.Title = " ";
                _reportcolumn.Width = 20;
                _reportcolumn.IsVisible = true;
                _reportcolumn.NameOrg = "CheckBoxColumn";
                Columns.Add(_reportcolumn);
            }
            //SB #2573 Merge Utility.
            else if (MergeMultiSelect == "1")
            {
                GridColumn _reportcolumn = new GridColumn();
                _reportcolumn.Name = "CheckBoxColumn";
                _reportcolumn.Alignment = "L";
                _reportcolumn.IsSortable = false;
                _reportcolumn.Title = " ";
                _reportcolumn.Width = 20;
                _reportcolumn.IsVisible = true;
                _reportcolumn.NameOrg = "CheckBoxColumn";
                Columns.Add(_reportcolumn);
            }

            if (ViewRecordOpen != "1")
            {
                GridColumn _reportcolumn = new GridColumn();
                _reportcolumn.Name = "OpenLink";
                _reportcolumn.Alignment = "L";
                _reportcolumn.IsSortable = false;
                _reportcolumn.Title = " ";
                _reportcolumn.Width = 90;
                if (ViewMultiSelect == "1")
                {
                    _reportcolumn.Width = 40;
                }
                else if (MergeMultiSelect == "1")
                {
                    _reportcolumn.Width = 40;
                }
                _reportcolumn.IsVisible = true;
                _reportcolumn.NameOrg = "OpenLink";
                Columns.Add(_reportcolumn);
            }

            for (int i = 1; i <= ColumnCount; i++)//starting from 1
            {
                GridColumn _reportcolumn = new GridColumn();
                _reportcolumn.Name = goTR.StrRead(_viewMetaData, "COL" + i + "FIELD", "").ToUpper();
                _reportcolumn.NameOrg = goTR.StrRead(_viewMetaData, "COL" + i + "FIELD", "").ToUpper();
                _reportcolumn.Name = goTR.GetFieldsFromLine(TableName, _reportcolumn.NameOrg).ToUpper();
                _reportcolumn.Name = _reportcolumn.Name.Replace("<%", string.Empty).Replace("%>", string.Empty).Replace("%%", "__");

                if (_reportcolumn.Name.Substring(0, 4).Equals("CUR_"))
                {
                    var CURSymbol = _reportcolumn.Name + "~" + GetCurSymbolFromMeta(goTR.StrRead(fldmata, _reportcolumn.Name + "_FRM", ""));
                    CURSymbols = CURSymbols + "," + CURSymbol;
                }

                _reportcolumn.Title = goTR.StrRead(_viewMetaData, "COL" + i + "LABEL", "");
                _reportcolumn.Alignment = goTR.StrRead(_viewMetaData, "COL" + i + "ALIGNMENT", "");
                var _Width = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "WIDTH", 0) == "" ? 0 : Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "WIDTH", 0)));
                //if (i == 1 && _Width < 18)
                //{
                //    _Width = 18;
                //}
                _reportcolumn.Width = _Width * 6;

                bool _isSortable = false;
                if (!goTR.IsFieldCombined(_reportcolumn.NameOrg) && godata.IsFieldSortable(_reportcolumn.NameOrg, TableName))
                {
                    _isSortable = true;
                }
                else
                {
                    _isSortable = false;
                }

                //bool _isSortable = godata.IsFieldSortable(_gridcolumn.Name, TableName);
                _reportcolumn.IsSortable = _isSortable;

                int _isLink = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "DISPLAYASLINK", 0));
                int _isIcon = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "DISPLAYASICON", 0));
                _reportcolumn.IsLink = _isLink == 1 ? true : false;
                _reportcolumn.IsIcon = _isIcon == 1 ? true : false;
                _reportcolumn.IsVisible = true;

                //set link fields with their GID_IDs..J
                if (_isLink == 1)
                {
                    string sLink = _reportcolumn.Name;
                    if (Strings.Left(sLink, 4) == "LNK_" & goTR.ExtractString(sLink, 2, "%%") != "GID_ID")
                    {
                        //"#$%^&*"
                        //modify then gen field
                        string sFieldFormatted = goTR.StrRead(_viewMetaData, "COL" + i + "FIELD", "").ToUpper();
                        sFieldFormatted = goTR.GetFieldsFromLine(TableName, _reportcolumn.NameOrg).ToUpper();
                        _reportcolumn.NameOrg = _reportcolumn.NameOrg + "#$%^&*<%" + goTR.ExtractString(sFieldFormatted, 1, "%%") + "%%GID_ID%>";
                    }
                }

                var Total = goTR.StrRead(_viewMetaData, "COL" + i + "TOTAL", 0);
                if (Total.Equals("1"))
                {
                    AggregateColumns = AggregateColumns + "," + "SUM_" + _reportcolumn.Name;
                    _reportcolumn.IsTotal = true;
                }
                var Average = goTR.StrRead(_viewMetaData, "COL" + i + "AVERAGE", 0);
                if (Average.Equals("1"))
                {
                    AggregateColumns = AggregateColumns + "," + "AVG_" + _reportcolumn.Name;
                    _reportcolumn.IsAverage = true;
                }

                var Median = goTR.StrRead(_viewMetaData, "COL" + i + "MEDIAN", 0);
                if (Median.Equals("1"))
                {
                    AggregateColumns = AggregateColumns + "," + "MED_" + _reportcolumn.Name;
                    _reportcolumn.IsMedian = true;
                }
                var Minimum = goTR.StrRead(_viewMetaData, "COL" + i + "MINIMUM", 0);
                if (Minimum.Equals("1"))
                {
                    AggregateColumns = AggregateColumns + "," + "MIN_" + _reportcolumn.Name;
                    _reportcolumn.IsMinimum = true;
                }
                var Maximum = goTR.StrRead(_viewMetaData, "COL" + i + "MAXIMUM", 0);
                if (Maximum.Equals("1"))
                {
                    AggregateColumns = AggregateColumns + "," + "MAX_" + _reportcolumn.Name;
                    _reportcolumn.IsMaximum = true;
                }
                var Percent = goTR.StrRead(_viewMetaData, "COL" + i + "PERCENT", 0);
                if (Percent.Equals("1"))
                {
                    AggregateColumns = AggregateColumns + "," + "PER_" + _reportcolumn.Name;
                    _reportcolumn.IsPercent = true;
                }

                var rptcolumnCompar = _reportcolumn.Name;
                if (_reportcolumn.Name.Substring(0, 4).Equals("LNK_"))
                {
                    rptcolumnCompar = rptcolumnCompar.Replace("__", "%%");
                }
                if (Fields.IndexOf(rptcolumnCompar) > -1)
                {
                    if (string.IsNullOrEmpty(_reportcolumn.Title))
                    {
                        _reportcolumn.Title = " ";
                    }
                    Columns.Add(_reportcolumn);
                }
            }


            GridColumn _Tgridcolumn = new GridColumn();
            _Tgridcolumn.Name = "GID_ID";
            _Tgridcolumn.NameOrg = "<%GID_ID%>";
            _Tgridcolumn.Title = "GID_ID";
            _Tgridcolumn.Alignment = "left";
            _Tgridcolumn.Width = 20 * 6;
            _Tgridcolumn.IsLink = false;
            _Tgridcolumn.IsVisible = false;
            Columns.Add(_Tgridcolumn);
        }
        private void GetSortColumnsCollection()
        {
            Microsoft.VisualBasic.Collection gcSortFields = godata.GetFilterSortFields("SORT=" + goTR.StrRead(_viewMetaData, "SORT", ""), false);
            Sorts = new List<string>();
            for (int i = 1; i <= gcSortFields.Count; i++)
            {
                string sSortField = gcSortFields[i].ToString();

                sSortField = goTR.ExtractString(sSortField, 1, "|");

                if (string.IsNullOrEmpty(DefaultSortField))
                {
                    DefaultSortField = sSortField;
                    DefaultSortDirection = goTR.ExtractString(gcSortFields[i].ToString(), 2, "|");
                }

                sSortField = "<%" + sSortField + "%>";
                string sFieldFormatted = goTR.GetFieldsFromLine(TableName, sSortField);

                var sInvalidFields = goTR.GetFieldsFromLine(TableName, sSortField, true, false);
                if (string.IsNullOrEmpty(sInvalidFields) == false)
                {
                    IsInvalid = true;
                    break;
                }

                Sorts.Add(sSortField);
            }
        }
        public string AggregateColumns { get; set; }
        public int LinksTop { get; set; }
        public Map(string _viewId, bool _isTabView, string _section, string _desktopmetadata, int _index, bool _isLoadFromSession = true, int _TotalIndex = 0, string _key = "")
            : base(_viewId, _isTabView, _section, _desktopmetadata, _index, _isLoadFromSession, _TotalIndex, _key)
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            godata = (clData)Util.GetInstance("data");
            goTR = (clTransform)Util.GetInstance("tr");

            isLoadFromSession = _isLoadFromSession;
            _viewMetaData = ViewMetaData;

            File = goTR.StrRead(_viewMetaData, "FILE");
            SortText = goTR.StrRead(_viewMetaData, "SORT");
            FilterText = goTR.StrRead(_viewMetaData, "CONDITION");

            if (System.Web.HttpContext.Current.Session[ViewKey] != null && isLoadFromSession == true)
            {
                SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + ViewKey);
                SelectedRecord = _SessionViewInfo.LastSelectedRecord;
                SelectedRowIndex = _SessionViewInfo.LastSelectedRowIndex;
                _viewMetaData = _SessionViewInfo.ViewMetaData;
            }
            if (goTR.StrRead(_viewMetaData, "SECTIONSGROUPED", "0", true) != "0")
            {
                GroupedData = true;
                GroupLinkFile = goTR.GetFileFromLinkName(SortText.Split(',')[0].Replace("ASC", "").Replace("DESC", "").Trim());
            }
            else
                GroupedData = false;

            GetColumnsCollection();
            GetSortColumnsCollection();

            if (GroupedColumns != null)
                GroupedColumns = GroupedColumns.TrimStart(',');
            if (AggregateColumns != null)
                AggregateColumns = AggregateColumns.TrimStart(',');
            if (!string.IsNullOrEmpty(CURSymbols))
                CURSymbols = CURSymbols.TrimStart(',');

            LinksTop = Convert.ToInt32(goTR.StrRead(_viewMetaData, "LINKSTOP", "5"));

            MapLatitudeField = goTR.StrRead(_viewMetaData, "MAP_LATITUDEFIELD", "");
            MapLongitudeField = goTR.StrRead(_viewMetaData, "MAP_LONGITUDEFIELD", "");
            MapRegionFile = goTR.StrRead(_viewMetaData, "MAP_REGIONFILE", "");
            MapRegionRAWDataField = goTR.StrRead(_viewMetaData, "MAP_REGIONRAWDATAFIELD", "");
            MapRegionFilterCondition = goTR.StrRead(_viewMetaData, "MAP_REGIONFILTER_CONDITION", "");
            MapIconField = goTR.StrRead(_viewMetaData, "MAP_ICONFIELD", "");
            ShowLines_Markers = goTR.StrRead(_viewMetaData, "SHOW_LINES_MARKERS", "");
        }
        


    }
}

﻿<appSettings>
  <add key="webpages:Version" value="*******" />
  <add key="webpages:Enabled" value="false" />
  <add key="ClientValidationEnabled" value="true" />
  <add key="UnobtrusiveJavaScriptEnabled" value="true" />
  <add key="HostingEnvironment" value="debugging" />
  <add key="AppVersion" value="v6.0.0" />
  <add key="CustomFilesPath" value="C:\TFSAzure\Selltis 6.0 TFVC\Selltis 6.0\Dev\Selltis6.0\Selltis.WebApi\Configs\" />
  <add key="LogFilePath" value="C:\TFSAzure\Selltis 6.0 TFVC\Selltis 6.0\Dev\Selltis6.0\Selltis.WebApi\Configs\"/>

  <add key="AttachmentsStorageType" value="Azure" />  <!-- Azure-->

  <add key="StorageConnectionString" value="DefaultEndpointsProtocol=http;AccountName=selltisfilestore;AccountKey=****************************************************************************************" />

  <!--<add key="smtpServer" value="mail.selltis.com" />  
  <add key="smtpUser" value="sellsmtp" />-->
  
  <add key="smtpServer" value="smtp.office365.com" />  
  <add key="smtpPort" value="587" />  
  <add key="smtpUser" value="<EMAIL>" />
  <add key="smtpSender" value="<EMAIL>" />
  <add key="smtpPassword" value="uF9sv#fo8!" />

  <add key="ida:TenantId" value="b52bb686-d6be-4b2c-8aa6-7437749f2714"/>
  <add key="ida:STenantId" value="5f051dd9-2747-42ed-88a2-567df7968bab"/>

  <add key="ida:Audience" value="https://graph.microsoft.com"/>
  <add key="ida:Audiences" value="8f25f41f-3679-447b-87d9-7722e8ce605c,api://8e784e2d-ade0-4ba8-afbc-1c10e89e5f4f,api://b0dd79d8-a18c-49e2-9f64-c673b1f1b40d"/>

  <add key="ida:AADInstance" value="https://login.microsoftonline.com/{0}/v2.0/"/>
  <add key="ida:STSInstance" value="https://sts.windows.net/{0}/"/>
  <add key="BlobConnectionString" value="DefaultEndpointsProtocol=https;AccountName=outlookadd;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"/>
  <add key="BlobContainerName"    value="addinfiles"/>
</appSettings>
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Selltis.WebApi.Models
{
    public class ParamModel
    {
        public string sFile;
        public int iType;
        public string sCondition;
        public string sSort;
        public string sFields;
        public int iTop;
        public string sINI;
        public string par_sGenFieldsDefs;
        public string hostName;
        public string userName;
        public string password;
        public string iTopRec;
    }

    public class ParamUpdateModel
    {
        public string par_sDataSet;
        public string strCompareField;
        public string strExtSource;
        public string sInstructions;
        public string hostName;
        public string userName;
    }
    public class LinkDialogModel
    {
        public int ordinal { get; set; }
        public string tabName;
        public string tabTableName;
     
        public int pageSize;
        public int tabWidth;
        public string linkField;
        public string linkValue;

        public string defaultTab;
        public string searchField;
        public string searchFieldHintText;
        public List<columns> columns;

        public List<Filters> filters;      
    }

    public class SelltisOutlookLinkSettings
    {
        public string UserTimeZoneIANA;
        public string UserTimeZone;
        public bool CalendarSyncEnabled;
        public bool ContactSyncEnabled;
        public bool TaskSyncEnabled;
        public string CalendarSyncType;
        public string ContactSyncType;
        public string TaskSyncType; 
        public string SyncOutlookCalendarName;
        public bool SyncPrivateAppointments;
        public int SyncAppointmentsFromMonth;
        public int SyncAppointmentsToMonth;

        public bool CalendarAutoSyncEnabled;
        public int CalendarAutoSyncInterval;
        public string CalendarFileName;
        public string CalendarFields;
        public string CalanderFilter;
        public string CalendarSort;

        public bool ContactAutoSyncEnabled;
        public int ContactAutoSyncInterval;
        public string ContactFileName;
        public string ContactFields;
        public string ContactFilter;
        public string ContactSort;

        public bool TaskAutoSyncEnabled;
        public int TaskAutoSyncInterval;
        public string TaskFileName;
        public string TaskFields;
        public string TaskFilter;
        public string TaskSort;
    }

    public class OLContact
    {
        public string FirstName;
        public string LastName;
        public string Email;
        public string BusinessPhone;
        public string CellPhone;
        public string JobTitle;
        public string Address;
        public string City;
        public string State;
        public string Zip;
        public string Country;
        public string Note;
        public string OutlookId;
    }

    public class ParamOLContactModel
    {
        public List<OLContact> OLContacts;
        public string hostName;
        public string userName;
    }

    public class OLAppointment
    {
        public string Description;
        public string Notes;
        public DateTime StartTime;
        public DateTime EndTime;
        public string OutlookId;
        public bool RecurrenceAppointment;       
    }

    public class ParamOLAppointmentModel
    {
        public List<OLAppointment> OLAppointments;
        public string hostName;
        public string userName;
    }

    public class OLTask
    {
        public string Subject;
        public string Notes;
        public DateTime StartDate;
        public DateTime DueDate;
        public DateTime? CompletedDate;        
        public int PercentageCompleted;
        public int Priority;
        public bool Completed;
        public string OutlookId;
    }

    public class ParamOLTaskModel
    {
        public List<OLTask> OLTasks;
        public string hostName;
        public string userName;
    }

    public class OLItem
    {
        public string OutlookId;
        public string SelltisId;       
    }

    public class ParamOLModel
    {
        public List<OLItem> OLItems;
        public string hostName;
        public string userName;
        public string fileName;
    }

    public class columns
    {
        public int width { get; set; }
        public string name;
        public string fontStyle;
        public string fontSize;
        public string fontName;
        public string label;
        public string visible;
    }
    public class Filters
    {
        public int ordinal;
        public string filterName;
        public string filtervalue;
        public string filterCondition;
        public string filterSort;
        public string defaultFilter;
    }
}
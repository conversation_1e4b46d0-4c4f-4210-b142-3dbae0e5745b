﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Selltis.BusinessLogic;
using Microsoft.VisualBasic;
using System.Web;  

namespace Selltis.Core
{
    public class Calendar : View
    {
        private clData goData;
        private clTransform goTR;
        private clDefaults goDef;
        private bool isLoadFromSession = true;
        private string _viewMetaData = string.Empty;
    
        public Collection cFieldDefs = new Collection();
        public bool IsInvalid { get; set; }

      //  public IList<GridColumn> Columns { get; set; }

        public bool gbHasCalendarIcon { get; set; }
        public string gsStartDateField { get; set; }
        public bool gbInvalidView { get; set; }
        public string gsEndDateField { get; set; }

        public bool gbHasAlarmIcon { get; set; }

        
        public Calendar(string _viewId, bool _isTabView, string _section, string _desktopmetadata, int _index, bool _isLoadFromSession = true, int _TotalIndex = 0, string _key = "")
            : base(_viewId, _isTabView, _section, _desktopmetadata, _index, _isLoadFromSession, _TotalIndex, _key)
        {
            goData = (clData)Util.GetInstance("data");
            goTR = (clTransform)Util.GetInstance("tr");
            goDef = (clDefaults)Util.GetInstance("def");
            goDef = new clDefaults();
            isLoadFromSession = _isLoadFromSession;
            _viewMetaData = ViewMetaData;

         Columns =  GetColumnsCollection();
         LinksTop = Convert.ToInt32(goTR.StrRead(_viewMetaData, "LINKSTOP", "5"));

            if(ViewType.ToLower() == "day" )
            {
                Util.SetSessionValue("IsAllDay", "false");
            }
            else
            {
                Util.SetSessionValue("IsAllDay", "true");
            }

        }
        private Collection  GetColumnsCollection()
        {

            goData = (clData)Util.GetInstance("data");
            goTR = (clTransform)Util.GetInstance("tr");
            goDef = (clDefaults)Util.GetInstance("def");


            string sField = string.Empty;
            string sInvalidFields = string.Empty;
            string sFieldFormatted = string.Empty;
            string sFieldDef = string.Empty;
            int iEllipsis = 0;
            int iColTextLength = 0;

            int ColumnCount = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COLCOUNT", 0));

          //  Columns = new List<>();

            switch (ViewType.ToUpper())
            {
                case "CALDAY":
                case "CALWEEK":
                case "CALMONTH":
                case "CALYEAR":
                    sField = goTR.StrRead(ViewMetaData, ViewType + "_LINE", "");

                    if (string.IsNullOrEmpty(sField))
                    {
                        sField = goDef.GetViewCalLine(TableName, ViewType);
                        _viewMetaData = ViewMetaData;
                        goTR.StrWrite(ref _viewMetaData, ViewType + "_LINE", sField);
                        ViewMetaData = _viewMetaData;
                    }

                    //get field list
                    sInvalidFields = goTR.GetFieldsFromLine(TableName, sField, true, false);

                    if (!string.IsNullOrEmpty(sInvalidFields))
                    {
                        IsInvalid = true;
                        break;
                    }
                    else
                    {
                        iColTextLength = Convert.ToInt32(goTR.StrRead(ViewMetaData, ViewType + "_LINELENGTH", -1));
                        iEllipsis = Convert.ToInt32(goTR.StrRead(ViewMetaData, ViewType + "_LINEELLIPSIS", 1));

                        sFieldFormatted = goTR.GetFieldsFromLine(TableName, sField);

                        sFieldDef = sFieldFormatted + Strings.Chr(1) + goData.GetFieldLabel(TableName, sField) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + sField + Strings.Chr(1) + Strings.Chr(1) + iColTextLength + Strings.Chr(1) + iEllipsis;

                        //'add field definition to collection

                        cFieldDefs.Add(sFieldDef);


                       //'----------------------------------------------------------------------------
                       //'now add starttime/endtime fields


                        string sDateDefaults = goTR.StrRead(_viewMetaData, "CAL_DATETIMEFIELDSDEF");

                        if(string.IsNullOrEmpty(sDateDefaults))
                        {
                            sDateDefaults = goData.GetDefaultCalDateTimeFields(TableName);
                        }

                        gsStartDateField = goTR.ExtractString(sDateDefaults, 1, "|");
                        gsEndDateField = goTR.ExtractString(sDateDefaults, 2, "|");
                        if (string.IsNullOrEmpty(gsEndDateField) | gsEndDateField == Convert.ToString(clC.EOT))
                        {
                            gsEndDateField = gsStartDateField;
                        }

                        for (int i = 1; i <= 2; i++)
                        {
                            switch (i)
                            {
                                case 1:
                                    sField = gsStartDateField;
                                    break;
                                case 2:
                                    sField = gsEndDateField;
                                    break;
                            }

                            sField = "<%" + sField + "%>";

                            sInvalidFields = goTR.GetFieldsFromLine(TableName, sField, true, false);


                            if (!string.IsNullOrEmpty(sInvalidFields))
                            {
                                gbInvalidView = true;
                                IsInvalid = true;
                            }
                            else
                            {
                                //get field list
                                sFieldFormatted = goTR.GetFieldsFromLine(TableName, sField);
                                if (string.IsNullOrEmpty(Fields))
                                {
                                    Fields = sFieldFormatted;
                                }
                                else
                                {
                                    Fields = Fields + ", " + sFieldFormatted;
                                }

                                //create delimited field definition string   
                                sFieldDef = sFieldFormatted + Strings.Chr(1) + goData.GetFieldLabel(TableName, sField) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + sField + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1);

                                //add field definition to collection
                                cFieldDefs.Add(sFieldDef);

                            }

                        }

                        //NOW ADD CHK_ALARM
                        sField = goTR.StrRead(_viewMetaData, "CAL_ALARMDEF", "");
                        if (Strings.InStr(sField, "<%") == 0)
                            sField = "";
                        if (string.IsNullOrEmpty(sField))
                        {
                            sField = goDef.GetViewCalAlarmDef(TableName);
                            _viewMetaData = ViewMetaData;
                            goTR.StrWrite(ref _viewMetaData, "CAL_ALARMDEF", sField);
                            ViewMetaData = _viewMetaData;
                        }


                        if (!string.IsNullOrEmpty(sField))
                        {
                            sInvalidFields = goTR.GetFieldsFromLine(TableName, sField, true, false);


                            if (!string.IsNullOrEmpty(sInvalidFields))
                            {
                                gbInvalidView = true;
                                IsInvalid = true;
                            }
                            else
                            {
                                //get field list
                                sFieldFormatted = goTR.GetFieldsFromLine(TableName, sField);
                                if (string.IsNullOrEmpty(Fields))
                                {
                                    Fields = sFieldFormatted;
                                }
                                else
                                {
                                    Fields = Fields + ", " + sFieldFormatted;
                                }
                                //create delimited field definition string   
                                sFieldDef = sFieldFormatted + Strings.Chr(1) + goData.GetFieldLabel(TableName, sField) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + sField + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1);
                                //add field definition to collection
                                cFieldDefs.Add(sFieldDef);
                                gbHasAlarmIcon = true;
                            }
                        }

                        //NOW ADD ICON FIELD
                        sField = goTR.StrRead(_viewMetaData, "CAL_ICONFIELD", "");


                        if (!string.IsNullOrEmpty(sField))
                        {
                            sInvalidFields = goTR.GetFieldsFromLine(TableName, sField, true, false);


                            if (!string.IsNullOrEmpty(sInvalidFields))
                            {
                                gbInvalidView = true;
                            }
                            else
                            {
                                //get field list
                                sFieldFormatted = goTR.GetFieldsFromLine(TableName, sField);
                                if (string.IsNullOrEmpty(Fields))
                                {
                                    Fields = sFieldFormatted;
                                }
                                else
                                {
                                    Fields = Fields + ", " + sFieldFormatted;
                                }
                                //create delimited field definition string   
                                sFieldDef = sFieldFormatted + Strings.Chr(1) + goData.GetFieldLabel(TableName, sField) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + sField + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1) + Strings.Chr(1);
                                //add field definition to collection
                                cFieldDefs.Add(sFieldDef);
                                gbHasCalendarIcon = true;
                            }
                        }
                    }


                    break;
                default:
                    break;
            }

            return cFieldDefs;

        }

      //  public IList<GridColumn> Columns { get; set; }
    }


}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.IO;
using System.Net;
using Newtonsoft.Json;
using System.Web.Script.Serialization;
using System.Xml;
using System.Text.RegularExpressions;
using System.Diagnostics;
using Selltis.BusinessLogic;
using Selltis.Core;

namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clProject goP;
        private clLog goLog;
        private clError goErr;
        private clPerm goPerm;
        private ClUI goUI;
        ScriptManager scriptManager = new ScriptManager();

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        public string sError;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();
        }
        public ScriptsCustom()
        {
            Initialize();
        }



        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //*** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //try
            //{




            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }

            //}

            return true;
        }

        public bool AC_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.GetMode() == "CREATION")
            {
                doForm.doRS.SetFieldVal("DTE_ENDTIME", doForm.doRS.GetFieldVal("DTE_STARTTIME"));
                doForm.doRS.SetFieldVal("TME_ENDTIME", doForm.doRS.GetFieldVal("TME_STARTTIME"));
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Dim bMultiLinks As Boolean
            string sWork = null;
            //Dim iStart As Integer
            //Dim iEnd As Integer
            string sNotes = null;
            bool bIsCorr = false;
            //Dim bIsLead As Boolean
            long lType = 0;
            //Dim sAddress As String
            //Dim iResult As Integer
            bool bResult = false;
            string sFieldName = null;
            string sFieldCode = null;
            int i = 0;
            string sSubject = null;
            bool bCopySubjectToNotes = false;
            string sScriptVar = null;
            //Dim doLink As Object
            //Dim doRS As clRowSet
            clArray doContacts = new clArray();
            clArray doCompanies = new clArray();
            //Dim lI As Long
            clArray doLink = new clArray();
            clRowSet doRowset = default(clRowSet);
            bool bMailList = true;
            int l = 0;
            string sDateStamp = "";
            string sJournal = null;


            //CS: Currently no message displayed from the AC would make us want to cancel the save.
            //Clicking no on the WP message did, but this message has been removed.
            //Want to abort the save after the user answered No to a messageboxevent (AC_FormOnSave_WPMessage).
            //If doForm.oVar.GetVar("CancelSave") = "1" Then
            //    'Reset variables so that save will run next time it is clicked.
            //    doForm.ovar.setvar("AC_FormOnSave_WPMessage", "")
            //    doForm.ovar.setvar("CancelSave", "")
            //    Return False
            //End If

            //Clicking Cancel on the message to unlink CNs not on mailing list message means we need to cancel save.
            if (doForm.oVar.GetVar("CancelSave").ToString() == "1")
            {
                doForm.oVar.SetVar("AC_MailList_Ran", "");
                doForm.oVar.SetVar("CancelSave", "");
                par_doCallingObject = doForm;
                return false;
            }

            //7/6/07: Moved to MD
            //If doForm.dors.GetFieldVal("DTT_StartTime", clC.SELL_FRIENDLY) = "" Then
            //    doForm.Movetofield("DTE_StartTime")
            //    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "DTT_StartTime"), "", "", "", "", "", "", "", "6", "DTT_StartTime")
            //    Return False
            //End If

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillEndTimeIfBlank"))
            {
                //Fill end time with Now if blank.
                if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("DTT_EndTime", clC.SELL_FRIENDLY).ToString()))
                {
                    int par_iValid = 4;
                    //	gop.traceline("Filling end time with start time.","",sProc)
                    DateTime goTrDatetime = Convert.ToDateTime(Microsoft.VisualBasic.Strings.Format(goTR.NowLocal(), "HH:mm"));
                    doForm.doRS.SetFieldVal("TME_ENDTIME", goTR.TimeToString(goTrDatetime, "", ref par_iValid), 1);
                    //doForm.dors.SetFieldVal("DTE_ENDTIME", goTR.DateToString(Format(goTR.NowLocal(), "yyyy-MM-dd")), 1)
                    doForm.doRS.SetFieldVal("DTE_ENDTIME", doForm.doRS.GetFieldVal("DTE_StartTime"), 1);
                }
            }

            //CS: If only end time is blank, fill with Now
            //If doForm.dors.GetFieldVal("TME_EndTime", clC.SELL_FRIENDLY) = "" Then
            //    doForm.dors.SetFieldVal("TME_ENDTIME", goTR.TimeToString(Format(goTR.NowLocal(), "HH:mm")), 1)
            //End If

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceValidEndTime"))
            {
                //If End Time is < start time AND end date is = to start date, give message to user to enter a valid end time. For now just
                //validation,later with messaging can give a better message to user.
                if (Convert.ToDateTime(doForm.doRS.GetFieldVal("TME_Endtime", clC.SELL_FRIENDLY)) < Convert.ToDateTime(doForm.doRS.GetFieldVal("TME_StartTime", clC.SELL_FRIENDLY)) & doForm.doRS.GetFieldVal("dte_endtime") == doForm.doRS.GetFieldVal("dte_starttime"))
                {
                    doForm.MoveToField("TME_EndTime");
                    // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "TME_EndTime"), "", "", "", "", "", "", "", "", "TME_EndTime")
                    goErr.SetWarning(30200, sProc, "", "The end time is before the start time.", "", "", "", "", "", "", "", "", "TME_EndTime");
                    return false;
                }
            }


            //----- Enforce date and time fields
            //bResult = scriptManager.RunScript("Activity_EnforceTime", doForm)
            //If bResult = False Then Return bResult


            //7/6/07: Enforce in MD
            //----- Enforce Credited to User ---------
            //If doForm.doRS.GetLinkCount("LNK_CreditedTo_US") < 1 Then
            //    doForm.MoveToTab(0)
            //    doForm.MovetoField("LNK_CreditedTo_US")
            //    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "LNK_CREDITEDTO_US"), "", "", "", "", "", "", "", "", "LNK_CREDITEDTO_US")
            //    Return False
            //End If

            //7/6/07: Enforce in MD
            lType = Convert.ToInt64(doForm.doRS.GetFieldVal("MLS_Type", 2));
            //'----- Enforce Type
            //If lType < 1 Then
            //    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "MLS_TYPE"), "", "", "", "", "", "", "", "", "MLS_TYPE")
            //    doForm.MovetoField("MLS_Type")
            //    Return False
            //End If

            //If doRS.GetFieldVal("TME_EndTime", clC.SELL_FRIENDLY) = "" Then
            //    doRS.SetFieldVal("TME_EndTime", doRS.GetFieldVal("TME_StartTime", clC.SELL_FRIENDLY), clC.SELL_FRIENDLY)
            //End If

            //Establish whether the type is correspondence or not
            //goP.TraceLine("MLS_TYPE: '" & doForm.dors.GetFieldVal("MLS_TYPE", 2) & "'" & vbCrLf & _
            //"MLS_Purpose: '" & doForm.dors.GetFieldVal("MLS_Purpose", 2) & "'", "", sProc)
            switch (lType)
            {
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                case 8:
                    //CS 1/19/09 remove wp types, 9, 10 'Correspondence
                    bIsCorr = true;
                    break;
            }

            //Select Case doForm.doRS.GetFieldVal("MLS_PURPOSE", 2)
            //    Case 8, 21, 22, 23, 24, 25      '8=Lead, 21-24=Request, 25=Submit Promoter Lead
            //        bIsLead = True
            //End Select


            //--------------------------------
            //Cs: Per MI, clear out Email, Fax, or letter as appropriate based on type.
            par_doCallingObject = doForm;
            scriptManager.RunScript("Activity_ClearRedundantFields", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            doForm = (Form)par_doCallingObject;
            //----------- ENFORCE ------------
            //Offer to share records created externally. 
            //Keep this as the first thing in the OnSave script.
            //Only run this script if we didn't run it already.
            if (doForm.oVar.GetVar("ShareItem_Ran").ToString() != "1")
            {
                par_doCallingObject = doForm;
                scriptManager.RunScript("ShareItem", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            }

            //----- Subject in correspondence ---------
            //Subject can be blank, but if it's not, the first line of the Notes field
            //must be the same as the subject or the subject is here prepended to the Notes.
            //This is so that the Notes field accurately reflects the Subject for correspondence
            //Activities.
            doForm = (Form)par_doCallingObject;
            sNotes = Microsoft.VisualBasic.Strings.Trim(doForm.doRS.GetFieldVal("MMO_Notes").ToString());
            sSubject = Microsoft.VisualBasic.Strings.Trim(doForm.doRS.GetFieldVal("TXT_SUBJ").ToString());
            if (bIsCorr)
            {
                if (!string.IsNullOrEmpty(sSubject))
                {
                    //If the first line of the Notes is not = subject, add the line to Notes
                    if (Microsoft.VisualBasic.Strings.InStr(sNotes, Environment.NewLine) < 1)
                    {
                        if (sNotes != sSubject)
                            bCopySubjectToNotes = true;
                    }
                    else
                    {
                        if (goTR.ExtractString(sNotes, 1, Environment.NewLine) != sSubject)
                            bCopySubjectToNotes = true;
                    }
                    if (bCopySubjectToNotes)
                    {
                        //Try to prepend subject to Notes
                        if (string.IsNullOrEmpty(sNotes))
                        {
                            sNotes = sSubject;
                        }
                        else
                        {
                            sNotes = sSubject + Environment.NewLine + Environment.NewLine + sNotes;
                        }
                        //goP.TraceLine("GetVar('lMemoLimit'): '" & goP.GetVar("lMemoLimit") & "'", "", sProc)
                        //goP.TraceLine("GetVar('lMandatoryFieldColor'): '" & goP.GetVar("lMandatoryFieldColor") & "'", "", sProc)
                        //goP.TraceLine("Val() of GetVar('lMemoLimit'): '" & Convert.ToString(Convert.ToInt32(goP.GetVar("lMemoLimit"))) & "'", "", sProc)
                        //goP.TraceLine("Length(sNotes): '" & Convert.ToString(Len(sNotes)) & "'", "", sProc)
                        if (!string.IsNullOrEmpty(goP.GetVar("lMemoLimit").ToString()))
                        {
                            if (Microsoft.VisualBasic.Strings.Len(sNotes) > Convert.ToInt32(goP.GetVar("lMemoLimit")))
                            {
                                doForm.MoveToTab(2);
                                //Notes
                                //scriptManager.RunScript("Activity_ManageExtraFields", doForm)
                                doForm.MoveToField("MMO_Notes");
                                goErr.SetWarning(30200, sProc, "", "Please remove some text from the Notes field so that the Subject can be prepended to it.", "", "", "", "", "", "", "", "", "MMO_notes");
                                return false;
                            }
                        }
                        else
                        {
                            doForm.doRS.SetFieldVal("MMO_NOTES", sNotes);
                        }
                    }
                    //ElseIf bIsCorr And (lType = 10 Or lType = 4 Or lType = 6 Or lType = 8) Then 'Subject is blank on corr sent: prompt
                    //    doForm.MoveToTab(1)     'Corr
                    //    doForm.MoveToField("TXT_Subj")
                    //    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "TXT_Subj"), "", "", "", "", "", "", "", "", "TXT_subj")
                    //    Return False
                }
            }

            //----- Enforce Notes field
            //CS 5/30: Only if not Corr
            //CS 6/18: Per RH, never enforce Notes
            //If sNotes = "" Or InStr(sNotes, vbCrLf) = 1 Then
            //    If bIsCorr Then
            //        'doForm.MoveToTab(2)     'Notes
            //        ''scriptManager.RunScript("Activity_ManageExtraFields", doForm)
            //        'doForm.MoveToField("MMO_Notes")
            //        'CS: 5/30/07: Notes not mandatory
            //    Else
            //        If bIsLead Then
            //            doForm.MoveToTab(2)     'Notes
            //            'scriptManager.RunScript("Activity_ManageExtraFields", doForm)
            //            doForm.MoveToField("MMO_Notes")
            //        Else
            //            doForm.MoveToTab(2)     'Notes
            //            'scriptManager.RunScript("Activity_ManageExtraFields", doForm)
            //            doForm.MoveToField("MMO_Notes")
            //        End If
            //    End If
            //    If InStr(sNotes, vbCrLf) = 2 And bIsCorr = False Then
            //        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "MMO_Notes"), "", "", "", "", "", "", "", "", "MMO_Notes")
            //        Return False
            //    ElseIf bIsCorr = False Then                'Type is not correspondence sent
            //        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "MMO_Notes"), "", "", "", "", "", "", "", "", "MMO_Notes")
            //        Return False
            //    End If

            //End If


            //----- Enforce Product if Purpose is Inquiry
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceProductIfPurposeIsInquiry"))
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Purpose", 2).ToString()) == 1 & doForm.doRS.IsLinkEmpty("LNK_Related_PD") == true)
                {
                    doForm.MoveToTab(4);
                    //Details
                    //scriptManager.RunScript("Activity_ManageExtraFields", doForm)
                    doForm.MoveToField("LNK_Related_PD");
                    //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "LNK_RELATED_PD"), "", "", "", "", "", "", "", "", "LNK_RELATED_PD")
                    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_RELATED_PD"), "", "", "", "", "", "",
                    "", "", "LNK_RELATED_PD");
                    return false;
                }
            }

            //----- Enforce Source if Purpose = "Lead" OR Purpose contains "Request" or "Submission"
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceSourceIfPurposeIsLead"))
            {
                switch (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_PURPOSE", 2)))
                {
                    case 8:
                    case 21:
                    case 22:
                    case 23:
                    case 24:
                    case 25:
                        //8=Lead, 21-24=Request, 25=Submit Promoter Lead
                        if (doForm.doRS.GetLinkCount("LNK_FROM_SO") < 1)
                        {
                            doForm.MoveToTab(4);
                            //Details
                            //scriptManager.RunScript("Activity_ManageExtraFields", doForm)
                            doForm.MoveToField("LNK_FROM_SO");
                            //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "LNK_from_so"), "", "", "", "", "", "", "", "", "LNK_from_so")
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_from_so"), "", "", "", "", "", "", "", "", "LNK_from_so");
                            return false;
                        }
                        break;
                }
            }

            //----- At least 1 recipient must be selected if Type is '... Sent'
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceRecipientIfTypeIsCorrSent"))
            {
                switch (lType)
                {
                    case 4:
                    case 6:
                    case 8:
                        //CS 1/19/09 Remove WP code, 10
                        if (doForm.doRS.IsLinkEmpty("LNK_Related_CN") == true)
                        {
                            doForm.MoveToField("LNK_Related_CN");
                            //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "LNK_RELATED_CN"), "", "", "", "", "", "", "", "", "LNK_RELATED_CN")
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_RELATED_CN"), "", "", "", "", "", "", "", "", "LNK_RELATED_CN");
                            return false;
                        }
                        break;
                }
            }


            //goP.TraceLine("GetLinkCountEx of Related Contact: '" & Convert.ToString(doForm.GetLinkCountEx("LNK_Related_CN")) & "'", "", sProc)

            //----- E-mail address field must be filled if only 1 recipient is selected and type = E-mail Sent (6)
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceEmailIfEmailSentTo1Recipient"))
            {
                //E-mail Sent
                if (lType == 6)
                {
                    if (string.IsNullOrEmpty(Microsoft.VisualBasic.Strings.Trim(doForm.doRS.GetFieldVal("EML_EMAIL").ToString())))
                    {
                        //RN #2155 30/3/2018 skip the setwarning when the EML_EMAIL field control is not at all there in AC Form.
                        if (doForm.doRS.GetLinkCount("LNK_Related_CN") < 2 && doForm.AllFormFields != null && doForm.AllFormFields.Contains("EML_EMAIL"))
                        {
                            doForm.MoveToField("EML_EMAIL");
                            //Enforce email field
                            //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "EML_EMAIL"), "", "", "", "", "", "", "", "", "EML_EMAIL")
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("EML_EMAIL"), "", "", "", "", "", "", "", "", "EML_EMAIL");


                            //CS: Comment filling in formOnsave.
                            //If scriptManager.RunScript("Activity_FillEmail", doForm) <> True Then
                            //    'CS: Msgbox has to be replaced in the web context
                            //    'MsgBox("Please enter an e-mail address into the 'E-mail' field." & vbCrLf & vbCrLf & _
                            //    '"If this address is permanent, also enter it into the 'E-mail' field of the linked Contact", 64, "Selltis")
                            //    goErr.SetWarning(30200, sProc, "", "Please enter an e-mail address into the E-mail field." & vbCrLf & vbCrLf & _
                            //             "If this address is permanent, also enter it into the 'E-mail' field of the linked Contact.", "", "", "", "", "", "", "", "", "Eml_email")
                            //Else
                            //    'CS: Msgbox has to be replaced in the web context
                            //    'MsgBox("Please check the e-mail address that Selltis entered into the 'E-mail' field and edit it if necessary.", 64, "Selltis")
                            //    goErr.SetWarning(30200, sProc, "", "Please check the e-mail address that Selltis entered into the E-mail field and edit it if necessary.", "", "", "", "", "", "", "", "", "Eml_email")
                            //End If
                            return false;
                        }
                    }
                }
            }

            //CS: No longer filling fields on save...only if click button on form.
            //----- Fill Address field it Type = Letter Sent (4) and there is only 1 recipient
            //bResult = scriptManager.RunScript("Activity_FillAddress", doForm)
            //Select Case bResult
            //    Case False
            //        doForm.MoveToField("TXT_ADDRESS")
            //        'CS: Msgbox has to be replaced in the web context
            //        'MsgBox("Please check the address that Selltis entered into the 'Address' field." & vbCrLf & _
            //        '  "Edit the address if necessary here and in the linked Contact record." & vbCrLf & vbCrLf & _
            //        '  "To leave the address blank, delete everything in the 'Address' field and enter a single hard return in it.", 64, "Selltis")
            //        goErr.SetWarning(30200, sProc, "", "Please check the address that Selltis entered into the 'Address' field." & vbCrLf & vbCrLf & _
            //                         "Edit the address if necessary here and in the linked Contact record." & vbCrLf & vbCrLf & _
            //                         "To leave the address blank, delete everything in the 'Address' field and enter a single hard return in it.", "", "", "", "", "", "", "", "", "Eml_email")
            //        Return False
            //    Case True
            //        'Nothing to do
            //    Case Else
            //        'Script error
            //        goErr.DisplayLastError()
            //        'CS: Msgbox has to be replaced in the web context
            //        'MsgBox("An error occurred. The address wasn't filled out.", 48, "Selltis")
            //        goErr.SetWarning(30200, sProc, "", "An error occurred. The address field wasn't filled out.", "", "", "", "", "", "", "", "", "Eml_email")
            //        goErr.SetError()
            //End Select


            //CS: No longer filling fields in Save
            //----- Fill Fax field it Type = Fax Sent (8) and there is only 1 recipient
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillFaxIfTypeIsFaxSentTo1Recipient"))
            {
                //goP.TraceLine("lType: '" & lType & "'", "", sProc)
                if (lType == 8)
                {
                    if (string.IsNullOrEmpty(Microsoft.VisualBasic.Strings.Trim(doForm.doRS.GetFieldVal("TEL_FAX").ToString())))
                    {
                        if (doForm.doRS.GetLinkCount("LNK_Related_CN") < 2)
                        {
                            doForm.MoveToField("TEL_Fax");
                            //Enforce fax field
                            //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "TEL_FAX"), "", "", "", "", "", "", "", "", "TEL_FAX")
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("TEL_FAX"), "", "", "", "", "", "", "", "", "TEL_FAX");
                            doForm.MoveToField("TEL_FAX");

                            //If scriptManager.RunScript("Activity_FillFax", doForm) <> True Then
                            //    'CS: Msgbox must be replaced in the web context.
                            //    'MsgBox("Please enter a fax number in the 'Fax' field." & vbCrLf & vbCrLf & _
                            //    '    "Also enter the number into the 'Fax' field of the linked Contact.", 64, "Selltis")
                            //    goErr.SetWarning(30200, sProc, "", "Please enter a fax number into the 'Fax' field." & vbCrLf & vbCrLf & _
                            //             "Also enter the number into the 'Fax' field of the linked Contact.", "", "", "", "", "", "", "", "", "TEL_Fax")
                            //Else
                            //    'CS: Msgbox must be replaced in the web context.
                            //    'MsgBox("Please check the fax number that Selltis entered into the 'Fax' field and edit it if necessary.", 64, "Selltis")
                            //    goErr.SetWarning(30200, sProc, "", "Please check the fax number that Selltis entered into the 'Fax' field and edit it if necessary.", "", "", "", "", "", "", "", "", "Eml_email")
                            //End If
                            return false;
                        }
                    }
                }
            }

            //----- If Billable, select company, fill Billing Notes, and calc billing charges
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceBillCompanyIfBillable"))
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Billable", 2).ToString()) == 1)
                {
                    if (doForm.doRS.IsLinkEmpty("LNK_Bill_CO") == true)
                    {
                        doForm.MoveToTab(8);
                        //Billing
                        //scriptManager.RunScript("Activity_ManageExtraFields", doForm)
                        doForm.MoveToField("LNK_Bill_CO");
                        par_doCallingObject = doForm;
                        bResult = scriptManager.RunScript("Activity_CalcBilling", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                        doForm = (Form)par_doCallingObject;
                        if (bResult == false)
                        {
                            return bResult;
                        }
                        if (doForm.doRS.GetLinkCount("LNK_Bill_CO") < 1)
                        {
                            //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "LNK_BILL_CO"), "", "", "", "", "", "", "", "", "LNK_BILL_CO")
                            goErr.SetWarning(30200, sProc, "", "Select the company to bill in the 'Bill Company' field and check the rest of the billing information.", "", "", "", "", "", "", "", "", "LNK_Bill_CO");
                        }
                        else
                        {
                            goErr.SetWarning(30200, sProc, "", "Check the company to bill in the 'Bill Company' field and check the rest of the billing information.", "", "", "", "", "", "", "", "", "LNK_Bill_CO");
                        }
                        return false;
                    }
                }
            }

            //SB 01102020 Next Action Date not to be mandatory
            ////----- Next Action Date must be a valid date if Purpose=Lead and Status=Open
            //if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceNextActionDateIfOpenLead"))
            //{
            //    //goP.TraceLine("IsDate of Next Action Date (should be 1 if date): '" & IsDate(doForm.GetFieldVal("DTE_NEXTACTIONDATE", 3)) & "'", "", sProc)
            //    //==> Retest: IsDate was coded wrong.
            //    //CS: The below line allows as blank NA date.
            //    //If goTR.IsDate(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 2)) <> True Then
            //    if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1).ToString()))
            //    {
            //        //goP.TraceLine("Purpose (should be 8): '" & doForm.GetFieldVal("MLS_PURPOSE", 2) & "'" & vbCrLf & _
            //        //  "Status (should be 0): '" & doForm.GetFieldVal("MLS_STATUS", 2) & "'", "", sProc)
            //        if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_PURPOSE", 2).ToString()) == 8 & Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2).ToString()) == 0)
            //        {
            //            //Purpose 8 = Lead; Status 0 = Open
            //            doForm.MoveToTab(3);
            //            //Journal
            //            //scriptManager.RunScript("Activity_ManageExtraFields", doForm)
            //            doForm.MoveToField("DTE_NEXTACTIONDATE");
            //            //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE")
            //            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE");
            //            return false;
            //        }
            //    }
            //}

            //----If Email Sent AC and Contact(s) connected have Mailing List unchecked, warn user.
            //Check if WOP is enabled
            string sWarn = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "ONSAVEAC_WARNNOTONMAILLIST", "1");
            if (sWarn == "1")
            {
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "WarnIfNotOnMailingList"))
                {
                    if (doForm.oVar.GetVar("AC_MailList_Ran").ToString() != "1")
                    {
                        if ((lType == 6 | lType == 8 | lType == 4) & doForm.doRS.GetLinkCount("LNK_RELATED_CN") > 0)
                        {
                            //Loop thru Linked CNs and see if any have mailing list unchecked.
                            DataTable oTable = new DataTable();
                            oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
                            doLink = doForm.doRS.GetLinkVal("LNK_Related_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
                            for (l = 1; l <= doLink.GetDimension(); l++)
                            {
                                doRowset = new clRowSet("CN", 3, "GID_ID=" + doLink.GetItem(l) + "", "TXT_NAMELAST A, TXT_NAMEFIRST A, TXT_CONTACTCODE A", "CHK_MAILINGLIST");
                                //"NameLastFirstCode a", "CHK_MAILINGLIST") 
                                if (doRowset.GetFirst() == 1)
                                {
                                    if (Convert.ToInt32(doRowset.GetFieldVal("CHK_MAILINGLIST", 2).ToString()) == 0)
                                    {
                                        bMailList = false;
                                        break; // TODO: might not be correct. Was : Exit For
                                        //CS 3/12/13: No need to continue looping when one found
                                    }
                                }
                            }

                            if (bMailList == false)
                            {
                                //Found linked CNs not on Mailing List so prompt user
                                doForm.MessageBox("Some Contacts linked to this Activity are not on your mailing list. Do you wish to unlink them?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "", "AC_FormOnSave_MailingList");
                                return true;
                            }
                        }
                    }
                }
                doRowset = null;
                doLink = null;
            }



            //sWork = doLink.Get(l)
            //'Parameters: (sFile, iRowSetType[, sCondition][, sSortKey[ sDirection]][, sFields])


            //If doRowset.SeekRecordByID(sWork) Then
            //    If doRowset.GetFieldVal("CHK_MailingList", 2) <> 1 Then
            //        'Mailing List isn't checked, unlink the Contact
            //        doForm.ClearLink("LNK_Related_CN", sWork)
            //        lCount += 1
            //    End If
            //Else
            //    'the Contact wasn't found - unlink it
            //    doForm.ClearLink("LNK_Related_CN", sWork)
            //    lCount += 1
            //End If
            //'delete(doRowSet)
            //doRowset = Nothing
            //Next l


            //CS: Msgbox must be replaced in web context.
            //If MsgBox("Are you sure you want to disconnect contacts who are not on our mailing list?" & vbCrLf & vbCrLf & _
            //           "This may take a few minutes.", MsgBoxStyle.YesNo) = MsgBoxResult.No Then
            //Return False
            //End If

            //CS 4/3/09
            //---Copy Notes to Journal on creation of a lead
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CopyNotesToJournalIfNewLead"))
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_PURPOSE", 2)) == 8 & doForm.GetMode() == "CREATION" & doForm.oVar.GetVar("CopiedNotes").ToString() != "1")
                {
                    //Purpose 8 = Lead
                    //CS 4/20
                    //Check if there is a value in the Journal field already
                    //CS 4/28: Make sure Notes field has a value
                    if (!string.IsNullOrEmpty(doForm.doRS.GetFieldVal("MMO_NOTES").ToString()))
                    {
                        sJournal = doForm.doRS.GetFieldVal("MMO_JOURNAL").ToString();
                        doForm.oVar.SetVar("CopiedNotes", "1");
                        //5/20/09
                        if (!string.IsNullOrEmpty(sJournal))
                        {
                            doForm.doRS.SetFieldVal("MMO_JOURNAL", sJournal + " " + doForm.doRS.GetFieldVal("MMO_NOTES"));
                        }
                        else
                        {
                            par_doCallingObject = doForm; par_oReturn = sDateStamp;
                            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "", "CODE", "USERNOOFFSETLABEL");
                            //returns var sDateStamp
                            sDateStamp = par_oReturn.ToString();
                            doForm = (Form)par_doCallingObject;
                            doForm.doRS.SetFieldVal("MMO_JOURNAL", sJournal + sDateStamp + " " + doForm.doRS.GetFieldVal("MMO_NOTES"));
                        }
                    }
                }
            }



            //----- Uncheck CHK_EXTDEVREVIEW
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "UncheckExternalDeviceReview"))
            {
                if (doForm.GetMode() != "CREATION")
                {
                    if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_EXTDEVREVIEW", 2)) == 1)
                    {
                        doForm.doRS.SetFieldVal("CHK_EXTDEVREVIEW", 0, 2);
                    }
                }
            }


            //----- Calc billing
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CalcBilling"))
            {
                par_doCallingObject = doForm;
                bResult = scriptManager.RunScript("Activity_CalcBilling", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                doForm = (Form)par_doCallingObject;
                if (bResult == false)
                    return bResult;
            }

            //CS Moved to RecordOnSave
            //bResult = scriptManager.RunScript("Activity_EmailAlias", doForm)
            //We don't care about the result here
            //If bResult = False Then Return bResult

            //bResult = scriptManager.RunScript("Activity_FillEmplConns", doForm)
            //If bResult = False Then Return bResult

            // CS comment outbResult = scriptManager.RunScript("Activity_FillSignature", doForm)
            //If bResult = False Then Return bResult

            //bResult = scriptManager.RunScript("Activity_FillInvolvesCC", doForm)
            //'Result doesn't matter
            //If bResult = False Then Return bResult


            //----- Disallow self-references in edit fields
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "DisallowSelfReferencesInTextFields"))
            {
                sWork = "MMO_NOTES" + Environment.NewLine;
                sWork += "MMO_LETTER" + Environment.NewLine;
                sWork += "TXT_ADDRESS" + Environment.NewLine;
                sWork += "EML_EMAIL" + Environment.NewLine;
                sWork += "TEL_FAX" + Environment.NewLine;
                sWork += "MMO_BILLINGNOTES" + Environment.NewLine;
                sWork += "TXT_SUBJ" + Environment.NewLine;
                sWork += "TXT_SIGNATURE" + Environment.NewLine;
                sWork += "TXT_ABOVESIGNATURE" + Environment.NewLine;
                sWork += "MMO_UNDERSIGNATURE";
                i = 1;
                do
                {
                    sFieldName = goTR.ExtractString(sWork, i, Environment.NewLine);
                    if (sFieldName == clC.EOT.ToString())
                        break; // TODO: might not be correct. Was : Exit Do
                    sFieldCode = "(%" + sFieldName + "%)";
                    if (Microsoft.VisualBasic.Strings.InStr(Microsoft.VisualBasic.Strings.UCase(doForm.doRS.GetFieldVal(sFieldName).ToString()), sFieldCode) > 0)
                    {
                        doForm.MoveToField(sFieldName);
                        goErr.SetWarning(30200, sProc, "", "Field '" + sFieldName + "' contains a field code that references itself. Please delete '" + sFieldCode + "' from this field.", "", "", "", "", "", "", "", "", "Eml_email");
                        return false;
                    }
                    i = i + 1;
                } while (true);
            }

            //Set Status as completed when the user is creating an Opp.
            //==> Address how to mark Status as completed when the user uses the Create Linked>Opp feature.
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "SetStatusCompleted"))
            {
                if (doForm.GetControlVal("CHK_CREATEOP") == "CHECKED")
                {
                    doForm.doRS.SetFieldVal("MLS_STATUS", 1, 2);
                    //1="Completed"
                }
            }

            //'----- Matrix mailer disconnect Contacts
            //'==> Put this on a button on the form
            //If NameOrig = "Matrix Mailer" Then DisconSelltisCustomers
            //

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CreateJournalActivity"))
            {
                //If doForm.GetMode() <> "CREATION" and doForm.GetFieldVal("MLS_Purpose",2) = 8 Then	'8=Lead
                //Not testing for lead, creating Activity whenever a Journal entry has been added.
                //BKG OKed this 2/16/05
                //If doForm.GetMode() <> "CREATION" Then
                //check if we already ran this script. If so don't run it again.
                //This only happens when a journal entry is added via the form. This is b/c it requires interaction with the journal messagebox.
                if (doForm.oVar.GetVar("Ac_CreateActLog_Ran").ToString() != "1")
                {
                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEAC_CREATE_AC").ToString() == "1")
                    {
                        par_doCallingObject = doForm;
                        bResult = scriptManager.RunScript("Activity_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

                        //*** MI 11/28/07 Commented the following block because Return True caused FormOnSave
                        //code to exit when the Journal Activity record was created successfully. I am not 100%
                        //sure, but it appears that the ElseIf test below is redundant. I can't explain Return True
                        //in the ElseIf case any other way than this being a hasty attempt to stop some code below this
                        //from running when it should be conditionalized separately. Whether a journal activity
                        //is or isn't created in Activity_CreateActLog, we must continue AC_FormOnSave or the
                        //Activity itself will be saved (Return True!) without being processed by half of FormOnSave.

                        //If bResult = False Then
                        //    'we set a var in Activity_CreateActLog which tells us to display error at end of save
                        //    'If ran create act log and need to display message asking if want to create journal
                        //    'Also continue with save if Act_CreateActLog returned true b/c need to display msg
                        //    'to user that record is not shared and therefore can't create activity. This will
                        //    'display at end of save.
                        //ElseIf bResult = True And doForm.oVar.GetVar("Ac_CreateActLog_Ran") = "1" And doForm.oVar.GetVar("ContinueSave") <> "1" Then
                        //    Return True
                        //End If

                    }
                }
                //End If
            }

            //'----- mark licenses as reported if necessary
            //'License mgmt not ported to NGP
            //If gstrCompaniesReportedToCmc <> "" Then 
            //	Dim intResult
            //	intResult = MsgBox("Saving this Activity Log will mark all Licenses listed in the Letter field as 'Reported to Cmc'."  &  vbCrLf  &  vbCrLf  &  _
            //					"Do you still want to save this Activity Log?", vbYesNo  &  vbQuestion, "Selltis")
            //	If intResult = 6 Then		'6=Yes
            //		iResult = scriptManager.runscript("Activity_MarkLicAsReported",doForm)
            //		If iResult < 1 Then Return iResult	
            //	Else
            //		Abort
            //		doForm.MoveToTab("Activity")
            //		scriptManager.runscript("Activity_ManageExtraFields",doForm)
            //		doForm.MoveToField("Notes")
            //		Exit Function
            //	End If
            //End If

            //CS:1/24/07: Per MI - remove all of this.
            //----- Leads should have only one Contact linked
            //goP.TraceLine("MLS_PURPOSE: '" & doForm.dors.GetFieldVal("MLS_PURPOSE", 2) & "'", "", sProc)
            //If doForm.dors.GetFieldVal("MLS_PURPOSE", 2) = 8 Then     '8=Lead
            //    If doForm.dors.GetLinkCount("LNK_Related_CN") > 1 Then bMultiLinks = True
            //    If doForm.dors.GetLinkCount("LNK_Related_CO") > 1 Then bMultiLinks = True
            //    If bMultiLinks And doForm.ovar.getvar("AC_FormOnSave_WPMessage") <> "1" Then 'only display message if didn't already
            //        doForm.ovar.setvar("AC_FormOnSave_WPMessage", "1")
            //        doForm.MessageBox("This lead Activity has multiple linked Contacts or Companies. In reports and " & _
            //        "WebPARTNER, it will appear only under the top-most linked Contact and Company." & vbCrLf & vbCrLf & _
            //        "Are you sure you want to save the record?", clC.SELL_MB_YESNO + clC.SELL_MB_DEFBUTTON1, , , , , , "MessageBoxEvent", "MessageBoxEvent", , doForm, , "YES", "NO", , , "AC_FormOnSave_WPMessage")

            //        Return True
            //        'CS: Msgbox must be replaced in the web context.
            //        'If MsgBox("This lead Activity has multiple linked Contacts or Companies. In reports and " & _
            //        '      "WebPARTNER, it will appear only under the top-most linked Contact and Company." & vbCrLf & vbCrLf & _
            //        '      "Are you sure you want to save the record?", MsgBoxStyle.YesNo) = MsgBoxResult.No Then
            //        '    doForm.MoveToField("EDT_Related_CONTACT")
            //        'Return (False)
            //        'End If
            //    End If
            //End If


            //CS 1/19/09 Remove WP code
            //'----- Manage WPData for replies to WebPARTNER responses
            //If scriptManager.IsSectionEnabled(sProc, par_sSections, "ManageWPDataForReplies") Then
            //    'goP.TraceLine("MLS_Type: '" & lType & "'", "", sProc)
            //    If doForm.GetMode() = "CREATION" Then
            //        If lType = 10 Then
            //            'WP Submission Sent
            //            sWork = doForm.doRS.GetFieldVal("MMO_WPDATA")
            //            iStart = InStr(sWork, vbCrLf)
            //            If iStart > 0 Then
            //                'iEnd = InStr(iStart + 1, sWork, vbCrLf)
            //                iEnd = goTR.Position(sWork, vbCrLf, iStart + 1)
            //                If iEnd > 0 Then
            //                    sWork = Mid(sWork, 1, iStart - 1) & goTR.FromTo(sWork, iEnd)
            //                    doForm.doRS.SetFieldVal("MMO_WPData", sWork)
            //                End If
            //            End If
            //        End If
            //    End If
            //End If

            //CS 1/19/09: Remove WP code
            //'----- If WP Response Sent, set Purpose to 'WP Submission' 
            //'This is needed for WebPartner to display a threaded view of linked responses
            //If scriptManager.IsSectionEnabled(sProc, par_sSections, "SetPurposeIfWPResonseSent") Then
            //    If lType = 9 Then
            //        If doForm.doRS.GetFieldVal("MLS_Purpose", 2) <> 81 Then
            //            doForm.doRS.SetFieldVal("MLS_PURPOSE", 81, 2)
            //        End If
            //    End If
            //End If


            //-------------- FILL FIELDS ------------
            //Ported from FillName function

            doForm = (Form)par_doCallingObject;

            //----- Write an entry in the History field 
            if (doForm.oVar.GetVar("WriteToHistory").ToString() != "1")
            {
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "WriteEntryInHistoryField"))
                {
                    //goP.TraceLine("Considering whether to write in History.", "", sProc)
                    sWork = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    if (doForm.GetMode() == "CREATION")
                    {
                        sWork = goTR.WriteLogLine(sWork, "Created.");
                    }
                    else
                    {
                        //goP.TraceLine("Mode not creation. IsDirty: '" & doForm.IsDirty() & "'", "", sProc)
                        if (doForm.IsDirty)
                        {
                            sWork = goTR.WriteLogLine(sWork, "Edited.");
                            //Not logging edits
                            //8=Lead
                            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Purpose", 2)) == 8)
                            {
                                //goP.TraceLine("GetFieldVal(LNK_CreditedTo_US): '" & doForm.dors.GetFieldVal("LNK_CreditedTo_US") & "'", "MAR", sProc)
                                //goP.TraceLine("doForm.oVar.GetVar(sEmplOrigID): '" & doForm.oVar.GetVar("sEmplOrigID") & "'", "MAR", sProc)
                                if (doForm.doRS.GetFieldVal("LNK_CreditedTo_US") != doForm.oVar.GetVar("sEmplOrigID"))
                                {
                                    //CS 8/25/08: Changing from US SYS NAMe to Last, First b/c SYS Name can change
                                    //sWork = goTR.WriteLogLine(sWork, "Lead transferred from " & doForm.oVar.GetVar("sEmplOrig") & " to " & doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%SYS_Name") & ".")
                                    sWork = goTR.WriteLogLine(sWork, "Lead transferred from " + doForm.oVar.GetVar("sEmplOrig") + " to " + doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%TXT_NameLast") + ", " + doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%TXT_NAMEFIRST") + ".");
                                }
                            }
                        }
                    }
                    //Write the History log
                    doForm.oVar.SetVar("WriteToHistory", "1");
                    doForm.doRS.SetFieldVal("MMO_HISTORY", sWork);
                }
            }
            AfterActLogOnAdd:

            //'----- Managing attachment symbol for views ==> solve this more elegantly if needed.
            //'Attachment management is under development.
            //If doForm.Connection("Attached", "File").ConnectedItemCount > 0 Then
            //	Field("Attachment Char") = "+"
            //Else
            //	Field("Attachment Char") = ""
            //End If


            //CS: Moving to RecOnSave
            //'----- Set the Related Territory connection from Related Company's territory
            //doForm.ClearLinkAll("LNK_Related_TE")
            //doLink = New clArray()
            //doLink = doForm.GetLinkVal("LNK_Related_CO%%LNK_In_TE", doLink)
            //doForm.SetLinkVal("LNK_Related_TE", doLink)
            //'delete(doLink)
            //doLink = Nothing

            //=============== Sub Form_OnSave Code ===============

            //'RemoveContactsNotOnMailingList
            //iResult = scriptManager.runscript("RemoveCntNotOnMailList",doForm)
            //IF iResult < 1 THEN RETURN iResult

            //'Broadcast checkbox doesn't exist in NGP	
            //If doForm.GetLinkCountEx("LNK_Related_CN") > 1 Then
            //	doForm.SetFieldVal("Broadcast") = 1
            //Else
            //	doForm.Field("Broadcast") = 0
            //End If



            //============= Agents ================
            //ActLog:ReplyOnSave
            //==> Write

            //CS 3/1/13: Moving the below to RecordAfterSave to try to speed it up
            //Now Contacts will be updated w/out validation and RecOnSave running.
            //If scriptManager.IsSectionEnabled(sProc, par_sSections, "UpdateContacts") Then

            //    'Only try to update linked CNs next and last CN dates if workgroup option is set.
            //    If doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEAC_UPDATE_CN_DATES") = "1" Then
            //        If doForm.doRS.GetLinkCount("LNK_RELATED_CN") > 0 And doForm.oVar.GetVar("AC_FormONSave_CN_NADate") <> "1" Then
            //            doForm.oVar.SetVar("AC_FormOnSave_CN_NADate", "1")
            //            'automatically update next and last CN dates of linked CNs
            //            doContacts = doForm.doRS.GetLinkVal("LNK_RELATED_CN", doContacts)
            //            lI = 1
            //            'goP.TraceLine("doContacts array: '" & doContacts.GetDimension() & "'", "", sProc)
            //            For lI = 1 To doContacts.GetDimension()
            //                doRS = New clRowSet("CN", 1, "GID_ID='" & doContacts.GetItem(lI) & "'", "", "*,LNK_INTERESTEDIN_PD", , , , , , True, True)
            //                'Browse through the rowset
            //                If doRS.GetFirst() <> 1 Then
            //                    'The Contact couldn't be found, can't be updated
            //                    'goP.TraceLine("doRS.GetFirst returned False", "", sProc)
            //                Else
            //                    'goP.TraceLine("doRS.GetFirst found the record.", "", sProc)
            //                    doRS.SetFieldVal("LNK_INTERESTEDIN_PD", doForm.doRS.GetFieldVal("LNK_RELATED_PD", 2), 2)
            //                    doRS.SetFieldVal("DTE_LASTCONTACTDATE", "Today", 1)
            //                    doRS.SetFieldVal("TXT_LASTCONTACTEDBY", goP.GetMe("CODE"))
            //                    doRS.SetFieldVal("DTE_NEXTCONTACTDATE", goTR.NumToString(doRS.GetFieldVal("INT_REVIEWINTERVAL", 2)) & " days from today", 1)
            //                    'goP.TraceLine("About to Commit Contact RS", "", sProc)
            //                    If doRS.Commit() <> 1 Then
            //                        Dim sError As String = goErr.GetLastError()
            //                        Dim sMessage As String
            //                        sMessage = "The next and last contact dates of Contact '" & doRS.GetFieldVal("SYS_NAME") & "' could not be updated due to the following error:" & vbCrLf
            //                        'Catch other errors and display to user for now. I am leaving the above
            //                        'code commented because we may want to trap particular errors only.
            //                        'But for now we are trapping all errors.
            //                        sMessage = sMessage & goErr.GetLastError("MESSAGE")
            //                        doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") & sMessage & vbCrLf)
            //                        '//goLog.Log("MessageBoxEvent", doForm.ovar.getvar("ScriptMessages"), , , True)
            //                        goErr.SetError()   'Reset the error
            //                    End If
            //                End If
            //                'goP.TraceLine("About to delete doRS", "", sProc)
            //                doRS = Nothing
            //            Next lI
            //            doContacts = Nothing
            //        End If
            //    End If
            //End If



            //CS 3/1/13: Move below to RecordAfterSave to speed this up
            //Now Companies will be updated w/out validation and RecOnSave running.
            //'CS 1/1/09: Update linked Companies
            //'Check if Review field is in database
            //If goData.IsFieldValid("CO", "CHK_REVIEW") = True Then
            //    If scriptManager.IsSectionEnabled(sProc, par_sSections, "UpdateCompanies") Then
            //        'Only try to update linked COs next and last review dates if workgroup option is set.
            //        If doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEAC_UPDATE_CO_DATES") = "1" Then
            //            If doForm.doRS.GetLinkCount("LNK_RELATED_CO") > 0 And doForm.oVar.GetVar("AC_FormONSave_CO_NRDate") <> "1" Then
            //                doForm.oVar.SetVar("AC_FormOnSave_CO_NRDate", "1")
            //                'automatically update next and last CN dates of linked CNs
            //                doCompanies = doForm.doRS.GetLinkVal("LNK_RELATED_CO", doCompanies)
            //                lI = 1
            //                For lI = 1 To doCompanies.GetDimension()
            //                    'doRS = New clRowSet("CO", 1, "GID_ID='" & doCompanies.GetItem(lI) & "'", "", "DTT_LASTREVIEWDATE,TXT_LASTREVIEWEDBY,DTT_NEXTREVIEWDATE", , , , , , , True, True)
            //                    doRS = New clRowSet("CO", 1, "GID_ID='" & doCompanies.GetItem(lI) & "'", , "*", , , , , , , True, True)
            //                    'Browse through the rowset
            //                    If doRS.GetFirst() <> 1 Then
            //                        'The Company couldn't be found, can't be updated
            //                    Else
            //                        doRS.SetFieldVal("DTE_LASTREVIEWDATE", "Today", 1)
            //                        doRS.SetFieldVal("TXT_LASTREVIEWEDBY", goP.GetMe("CODE"))
            //                        doRS.SetFieldVal("DTE_NEXTREVIEWDATE", goTR.NumToString(doRS.GetFieldVal("INT_REVIEWINTERVAL", 2)) & " days from today", 1)
            //                        If doRS.Commit() <> 1 Then
            //                            Dim sError As String = goErr.GetLastError()
            //                            Dim sMessage As String
            //                            sMessage = "The next and last review dates of Company '" & doRS.GetFieldVal("SYS_NAME") & "' could not be updated due to the following error:" & vbCrLf
            //                            'Catch other errors and display to user for now. I am leaving the above
            //                            'code commented because we may want to trap particular errors only.
            //                            'But for now we are trapping all errors.
            //                            sMessage = sMessage & goErr.GetLastError("MESSAGE")
            //                            doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") & sMessage & vbCrLf)
            //                            goErr.SetError()   'Reset the error
            //                        End If
            //                    End If
            //                    doRS = Nothing
            //                Next lI
            //                doCompanies = Nothing
            //            End If
            //        End If
            //    End If
            //End If

            //CS 9/17/14: Testing new MMR field
            //doForm.doRS.SetFieldVal("MMR_Letter", doForm.doRS.GetFieldVal("MMR_Letter") & vbCrLf & "This is a test")



            //This prevents the FormOnSave code from executing twice. This is a temporary fix
            //for a problem with the script delaying the processing of WD's internal events,
            //which causes the button code to be executed twice.
            //doForm.MoveToField("DTE_STARTTIME")


            //doForm.ovar.setvar("AC_FormOnSave_WPMessage", "") 'reset messaging variable

            //If we had previously run this script which called updating linked CN's next action/contact
            //dates and/or creating an ACT the commit of the Contact  or AC had failed we need to display a message on the AC form to
            //to the user.
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "DisplayErrorMessages"))
            {
                sScriptVar = doForm.oVar.GetVar("ScriptMessages").ToString();
                if (!string.IsNullOrEmpty(sScriptVar))
                {
                    if (Microsoft.VisualBasic.Strings.Len(sScriptVar) < 500)
                    {
                        doForm.MessageBox(sScriptVar, clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", "AC_FormOnSave_ScriptMessages");
                    }
                    else
                    {
                        doForm.MessageBox(Microsoft.VisualBasic.Strings.Left(sScriptVar, 497) + "...", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", "AC_FormOnSave_ScriptMessages");
                    }
                    return true;
                }
            }

            par_doCallingObject = doForm;
            par_bRunNext = false;
            return true;
        }
        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            if (doRS.iRSType == 2)//add
            {
                if (!doRS.IsLinkEmpty("LNK_RELATED_OP"))
                {
                    doRS.SetLinkVal("LNK_RELATED_CO", doRS.GetFieldVal("LNK_RELATED_OP%%LNK_FOR_CO", 2));
                    doRS.SetLinkVal("LNK_RELATED_CN", doRS.GetFieldVal("LNK_RELATED_OP%%LNK_ORIGINATEDBY_CN", 2));
                }
            }

            par_doCallingObject = doRS;
            return true;
        }
        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            clRowSet doRS = (clRowSet)par_doCallingObject;
            //AD ******** TKT#1956 : Update Last Date for Company
            //goScr.RunScript("UpdateLastDateinCO", doRS);
            scriptManager.RunScript("UpdateLastDateinCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            if (doRS.iRSType == 2)
            {
                int extdevreview = Convert.ToInt32(doRS.GetFieldVal("CHK_EXTDEVREVIEW", 2));

                if (extdevreview == 1)
                {

                    clRowSet doRSTD = new clRowSet("TD", 2, "", "", "", -1, "", "", "CRL_TD", doRS.GetFieldVal("GID_ID").ToString(), "", true);
                    doRSTD.SetFieldVal("LNK_ASSIGNEDTO_US", goP.GetMe("GID_ID"));
                    doRSTD.SetFieldVal("TXT_DESCRIPTION", doRS.GetFieldVal("TXT_SUBJ"));
                    doRSTD.SetFieldVal("MLS_TYPE", 10, 2);
                    doRSTD.SetFieldVal("MMO_NOTES", doRS.GetFieldVal("MMO_LETTER"));
                    doRSTD.SetFieldVal("LNK_RELATED_CN", doRS.GetFieldVal("LNK_RELATED_CN"));
                    doRSTD.SetFieldVal("LNK_RELATED_CO", doRS.GetFieldVal("LNK_RELATED_CO"));
                    //doForm.doRS.GetFieldVal("DTE_StartTime")
                    doRSTD.SetFieldVal("DTE_DUETIME", goTR.AddDay(Convert.ToDateTime(doRS.GetFieldVal("DTE_STARTTIME", 2)), 1), 2);
                    doRSTD.Commit();

                }
            }

            par_doCallingObject = doRS;
            return true;
        }
        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CalcQuoteTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.SetControlState("CHK_CCMailList1", 4);
            //Grayed Out
            doForm.SetControlState("CHK_CCMailList2", 4);
            //Grayed Out
            //VNK 12172015
            doForm.SetControlState("MLS_Bouncereason", 4);
            //Grayed Out

            //dynamic _lastUpdatesell = doForm.doRS.GetFieldVal("");
            par_doCallingObject = doForm;

            return true;
        }
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //AD 06262017 Ticket#1929: Make "TXT_CURANDPOT" Field GrayedOut as it is being Calculated.
            Form doForm = (Form)par_doCallingObject;
            doForm.SetControlState("TXT_CURANDPOT", 4);
            return true;
        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sName = doForm.doRS.GetFieldVal("TXT_COMPANYNAME").ToString();
            string sCity = doForm.doRS.GetFieldVal("txt_citymailing").ToString();
            Util.SetSessionValue("Currentcity", sCity);
            string sPhone1 = doForm.doRS.GetFieldVal("TEL_PHONENO").ToString();
            Util.SetSessionValue("CurrentsPhone1", sPhone1);
            string sFax = doForm.doRS.GetFieldVal("TEL_FAXNO").ToString();
            string sPhone2 = doForm.doRS.GetFieldVal("TEL_PHONE2").ToString();
            string sadd = doForm.doRS.GetFieldVal("TXT_ADDRMAILING").ToString();
            Util.SetSessionValue("Currentadd", sadd);
            string sState = doForm.doRS.GetFieldVal("TXT_STATEMAILING").ToString();
            string sZip = doForm.doRS.GetFieldVal("TXT_ZIPMAILING").ToString();
            string sCountryMail = doForm.doRS.GetFieldVal("TXT_COUNTRYMAILING").ToString();
            string sCounty = doForm.doRS.GetFieldVal("TXT_COUNTY").ToString();
            Util.SetSessionValue("Currentcountry", sCountryMail);


            string gudi = doForm.doRS.GetFieldVal("GID_ID").ToString();
            clRowSet companyRowSet = new clRowSet("CO", 3, "GID_ID= '" + doForm.FormRowSet.GetFieldVal("GID_ID") + "'", "", "TXT_COMPANYNAME,TEL_PHONENO,TEL_FAXNO,URL_WEBPAGE,TXT_ADDRMAILING,TXT_CITYMAILING,TXT_STATEMAILING,TXT_ZIPMAILING,TXT_COUNTRYMAILING,LNK_RELATED_IU");
            //doLink = doForm.doRS.GetLinkVal("CO" + "%%LNK_Related_CO%%GID_ID", ref doLink, true, 0, -1, "A_a", ref oTable);
            string city = companyRowSet.GetFieldVal("TXT_CITYMAILING").ToString();
            string Name = companyRowSet.GetFieldVal("TXT_COMPANYNAME").ToString();
            string Phone1 = companyRowSet.GetFieldVal("TEL_PHONENO").ToString();
            string Fax = companyRowSet.GetFieldVal("TEL_FAXNO").ToString();
            string Phone2 = companyRowSet.GetFieldVal("TEL_PHONE2").ToString();
            string add = companyRowSet.GetFieldVal("TXT_ADDRMAILING").ToString();
            string State = companyRowSet.GetFieldVal("TXT_STATEMAILING").ToString();
            string Zip = companyRowSet.GetFieldVal("TXT_ZIPMAILING").ToString();
            string CountryMail = companyRowSet.GetFieldVal("TXT_COUNTRYMAILING").ToString();
            string County = companyRowSet.GetFieldVal("TXT_COUNTY").ToString();


            if (sName != Name || sCity != city || sPhone1 != Phone1 || sPhone2 != Phone2 || sFax != Fax || sadd != add || sState != State || sZip != Zip || sCountryMail != CountryMail || sCounty != County)
            {
                doForm.MessageBox("The Company address is changed so do you want to do update the address for the related Contacts also ?", clC.SELL_MB_YESNO, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "", doForm, null, "Yes", "No", "", "", "COMPANY_UPDATE");
                par_doCallingObject = doForm;
                return true;
            }

            return true;
        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //Ticket 1956 ********
            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sCurVol = "";
            string sPotVol = "";
            int iCurCount = Convert.ToInt32(doRS.GetLinkCount("LNK_CURRENT_PD"));
            int iPotCount = Convert.ToInt32(doRS.GetLinkCount("LNK_POTENTIAL_PD"));
            //Ticket 1929
            //Target Account Matrix Profiling
            //Copy LNK_Current_PD to LNK_Potential_PD
            doRS.SetFieldVal("LNK_POTENTIAL_PD", doRS.GetFieldVal("LNK_CURRENT_PD"));

            //Record total selections from LNK_Current_PD and LNK_Potential_PD
            doRS.SetFieldVal("INT_CURCOUNT", iCurCount, 2);
            doRS.SetFieldVal("INT_POTCOUNT", iPotCount, 2);

            //Calculate Potential Percentage & Potential Portfolio
            clRowSet doPDRS = new clRowSet("PD", 3, "", "", "GID_ID");
            int iPDCount = Convert.ToInt32(doPDRS.Count());
            if (iPDCount != 0)
            {
                if (iPotCount != 0)
                {
                    doRS.SetFieldVal("SR__POTENTIALPERC", (iCurCount / iPotCount) * 100, 2);
                }
                doRS.SetFieldVal("SR__POTENTIALPORTFOLIO", (iPotCount / iPDCount) * 100, 2);

                doRS.SetFieldVal("SR__ProdLinePot", (iPotCount - iCurCount), 2);
                sCurVol = (doRS.GetFieldVal("MLS_CURVOLUME") == null) ? "" : Strings.Left(doRS.GetFieldVal("MLS_CURVOLUME").ToString(), 1);
                sPotVol = (doRS.GetFieldVal("MLS_POTVOLUME") == null) ? "" : Strings.Left(doRS.GetFieldVal("MLS_POTVOLUME").ToString(), 1);
                //for now record a "Z" if it is make selection
                if (sCurVol == "<")
                {
                    sCurVol = "Z";
                }
                //for now record a "Z" if it is make selection
                if (sPotVol == "<")
                {
                    sPotVol = "Z";
                }

                //set field to cur & pot
                doRS.SetFieldVal("TXT_CURANDPOT", sCurVol + sPotVol);
            }

            //---------- Target Account -----------------
            //Set Product Potential Quadrant
            double rTotalPortfolio = (doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2) == null) ? 0 : Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2));
            double rPotentialProduct = (doRS.GetFieldVal("SR__POTENTIALPERC", 2) == null) ? 0 : Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPERC", 2));

            if (rTotalPortfolio >= 51 & rTotalPortfolio <= 100)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                {
                    //Set to 1
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "1");
                }

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                {
                    //Set to 3
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "3");
                }
            }

            if (rTotalPortfolio >= 0 & rTotalPortfolio <= 50)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                {
                    //Set to 2
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "2");
                }

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                {
                    //Set to 4
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "4");
                }
            }

            //Because COs are updated nightly to set custom
            //date fields, need to write to custom mod time and mod by fields
            //AutoCOUpdate does NOT run recordonsave
            doRS.SetFieldVal("TXT_CusModBy", goP.GetMe("CODE"));
            doRS.SetFieldVal("DTT_CusModTime", "Today|Now");
            //---------End Target Account Matrix Profiling

            par_doCallingObject = doRS;

            return true;
        }
        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_sFileName: file for which to return the sort.
            //par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //PURPOSE:
            //	Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            //       By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            //       add CASEs for them here. To not override the default sort, par_oReturn must be "".
            //       IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            //       with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            //       the sort likely should be ASC.
            //RETURNS:
            //		Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            //Select Case (par_sFileName)
            //    Case "AA"
            //        'This is a reverse sort, typically used for datetime fields
            //        If par_sReverseDirection = "1" Then
            //            sResult = "SYS_NAME ASC"
            //        Else
            //            sResult = "SYS_NAME DESC"
            //        End If
            //    Case "BB"
            //        'Reverse sort on Creation datetime
            //        If par_sReverseDirection = "1" Then
            //            sResult = "DTT_CREATIONTIME ASC"
            //        Else
            //            sResult = "DTT_CREATIONTIME DESC"
            //        End If
            //        'Case Else
            //        '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            //        '    'it is not needed here
            //End Select

            par_oReturn = sResult;

            return true;

        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //Refill Vendor for the Product

            doRS.ClearLinkAll("LNK_RELATED_VE");
            doRS.SetFieldVal("LNK_RELATED_VE", doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_VE"));
            //AD ******** TKT#1956 : Update Last Date for Company
            scriptManager.RunScript("UpdateLastDateinCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doRS;
            return true;
        }
        public bool OP_FormControlOnChange_LNK_FOR_PD_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Refill Vendor for the Product

            doForm.doRS.ClearLinkAll("LNK_RELATED_VE");
            doForm.doRS.SetFieldVal("LNK_RELATED_VE", doForm.doRS.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_VE"));

            par_doCallingObject = doForm;
            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }

        public bool OP_FormControlOnChange_MLS_STAGE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STAGE", 2)) == 20) //Qualified OPP
            {
                doForm.MoveToTab(4); //Qualification Process
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool PR_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.GetMode() == "CREATION")
            {
                string sID = doForm.GetCreateLinkedSourceRecSUID;

                if (!string.IsNullOrEmpty(sID) & sID != null)
                {
                    if (goTR.GetFileFromSUID(sID) == "QT")
                    {
                        clRowSet doRSQT = new clRowSet("QT", 3, "GID_ID='" + sID + "'", "", "GID_ID,LNK_RELATED_PD");
                        if (doRSQT.GetFirst() == 1)
                        {
                            doForm.doRS.SetLinkVal("LNK_FOR_PD", doRSQT.GetFieldVal("LNK_RELATED_PD", 2));
                        }
                    }

                }
            }


            if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD")) != "")
            {
                //int cCount = 0;
                string sCondition = "", sFilterIni = "";

                sCondition = sCondition + "(" + "LNK_OF_PD=" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD")) + ")";
                goTR.StrWrite(ref sFilterIni, "C1CONDITION", "=");
                goTR.StrWrite(ref sFilterIni, "C1FIELDNAME", "<%LNK_OF_PD%>");
                //goTR.StrWrite(ref sFilterIni, "C" + cCount + "PARENBEFORE", "(");
                goTR.StrWrite(ref sFilterIni, "C1VALUE1", "" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD")) + "");
                //goTR.StrWrite(ref sFilterIni, "C1FIELDLABEL", goData.GetFieldLabel("MO", "LNK_OF_PD"));
                goTR.StrWrite(ref sFilterIni, "C1FIELDLABEL", "Of Product");

                goTR.StrWrite(ref sFilterIni, "ACTIVE", "1");
                goTR.StrWrite(ref sFilterIni, "CCOUNT", "1");
                goTR.StrWrite(ref sFilterIni, "CONDITION", sCondition);
                goTR.StrWrite(ref sFilterIni, "SORT", "SYS_NAME ASC");
                goTR.StrWrite(ref sFilterIni, "SORT1", "SYS_NAME");
                //goTR.StrWrite(ref sFilterIni, "FILE", "VE");
                //goTR.StrWrite(ref sFilterIni, "DIRECTION1", "1");
                goTR.StrWrite(ref sFilterIni, "US_NAME", "Linkbox selector filter");
                doForm.SetFilterINI("LNK_FOR_MO", sFilterIni);

                sCondition = "";
                sFilterIni = "";

            }
            //else
            //{
            //    doForm.doRS.ClearLinkAll("LNK_RELATED_VE");
            //    doForm.SetFieldProperty("LNK_RELATED_VE", "LABELCOLOR", "#666666");
            //}

            par_doCallingObject = doForm;

            return true;
        }
        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //SB 03052019 Tkt#2688 Check CHK_QUOTESTARTED on QL adding to QT
            clRowSet QTdoRS = new clRowSet("QT", clC.SELL_EDIT, "GID_ID=" + doRS.GetFieldVal("LNK_IN_QT%%GID_ID").ToString(), "", "*");

            if (QTdoRS.GetFirst() == 1)
            {
                QTdoRS.SetFieldVal("CHK_RFQCREATED", "0", 2);

                if (QTdoRS.Commit() != 1)
                {
                    goErr.SetError(35000, sProc, "Adding QT 'CHK_QUOTESTARTED' checking failed in QL_RecordOnSave_Pre.");
                }
            }

            par_doCallingObject = doRS;     //Assign doRS to par_doCallingObject
            return true;
        }
        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.SetControlState("CHK_RFQCREATED", 4);    //SB 03052018 Tk#2688 Disable RFQ Created


            if (doForm.GetMode() == "CREATION")
            {
                string sID = doForm.GetCreateLinkedSourceRecSUID;

                if (!string.IsNullOrEmpty(sID) & sID != null)
                {
                    if (goTR.GetFileFromSUID(sID) == "OP")
                    {
                        clRowSet doRSOP = new clRowSet("OP", 3, "GID_ID='" + sID + "'", "", "GID_ID,LNK_FOR_PD");
                        if (doRSOP.GetFirst() == 1)
                        {
                            doForm.doRS.SetLinkVal("LNK_RELATED_PD", doRSOP.GetFieldVal("LNK_FOR_PD", 2));
                        }
                    }

                }

            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //S_B 02-01-2019 Tkt#2637 make 3 fields not Menadatory
            doForm.SetFieldProperty("LNK_CREDITEDTO_US", "LABELCOLOR", "");
            doForm.SetFieldProperty("LNK_PEER_US", "LABELCOLOR", "");
            doForm.SetFieldProperty("LNK_TAKENAT_LO", "LABELCOLOR", "");

            doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", goP.GetMe("ID"));

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doQuote = (clRowSet)par_doCallingObject;

            //SB 03052019 Tkt#2688 Check CHK_RFQCREATED on Qt save with QLs
            if (doQuote.iRSType == 2)
            {
                doQuote.SetFieldVal("CHK_RFQCREATED", "1", 2);
            }

            par_doCallingObject = doQuote;
            return true;
        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //AD ******** TKT#1956 : Update Last Date for Company
            scriptManager.RunScript("UpdateLastDateinCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            return true;
        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Quotline_CalcTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        //AD TASK 1929 11082017
        public bool UpdateLastDateinCO(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //AD 07072017 TKT#1559 : Update Last Date for Company
            //Purpose Update LastDate for OP, QT, LEAD, AC, AC Sales and also in CO

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sFile = doRS.GetFileName();
            string sField = "";
            //field to update in company/contact
            string sFieldLabel = "";
            DateTime dDate = default(DateTime);
            string sCOLinkFieldName = "";

            //Need to update only for new records created
            //new Record
            if (doRS.GetInfo("TYPE") == "2")
            {

                if (sFile == "AC")
                {
                    sCOLinkFieldName = "LNK_RELATED_CO";
                    //If Activity is of type Sales Visit, update linked Related Company.Last AC Sales date
                    //sales visit
                    if (doRS.GetFieldVal("MLS_TYPE", 2) != null && Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2)) == 11)
                    {
                        sField = "DTT_LASTACSALES";
                    }
                    else
                    {
                        //Activity is not of type sales visit
                        sField = "DTT_LASTAC";
                    }

                    //LEAD Activity
                    if (doRS.GetFieldVal("MLS_PURPOSE", 2) != null && Convert.ToInt32(doRS.GetFieldVal("MLS_PURPOSE", 2)) == 8)
                    {
                        sField = "DTT_LASTLEAD";
                    }

                }
                else if (sFile == "OP")
                {
                    sCOLinkFieldName = "LNK_FOR_CO";
                    sField = "DTT_LASTOP";

                    //ElseIf sFile = "QT" Then
                    //    sCOLinkFieldName = "LNK_TO_CO"
                    //    sField = "DTT_LASTQT"

                }

                dDate = (doRS.GetFieldVal("DTT_CreationTime", 2) == null) ? DateTime.MinValue : Convert.ToDateTime(doRS.GetFieldVal("DTT_CreationTime", 2));

                if (!string.IsNullOrEmpty(sField))
                {
                    sFieldLabel = goData.GetFieldLabel("CO", sField);
                    //Update CO Record
                    if (doRS.GetLinkCount(sCOLinkFieldName) > 0)
                    {
                        clArray doCompanies = (clArray)doRS.GetFieldVal(sCOLinkFieldName, 2);
                        //Bipass reconsave and validation to speed up AC save
                        for (int i = 1; i <= doCompanies.GetDimension(); i++)
                        {
                            clRowSet doRSCompany = new clRowSet("CO", 1, "GID_ID='" + doCompanies.GetItem(i) + "'", "", sField, -1, "", "", "", "", "", true, true);
                            if (doRSCompany.GetFirst() == 1)
                            {
                                doRSCompany.SetFieldVal(sField, dDate, 2);
                                //log error but proceed
                                if (doRSCompany.Commit() == 0)
                                {
                                    goLog.Log(sProc, "CO update of last " + sFieldLabel + " field failed for CO " + ((doRSCompany.GetFieldVal("TXT_CompanyName") == null) ? "" : doRSCompany.GetFieldVal("TXT_CompanyName").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                }
                            }
                            doRSCompany = null;
                        }
                    }
                }

            }

            par_doCallingObject = doRS;
            return true;

        }
        //AD TASK 1929 11082017
        public bool AutoCOUpdate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.


            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //AD 07072017 TKT#1559 : Update Last Date for Company
            //AGE_D0D32818-BC80-4333-5858-A09800E45CDE
            //PURPOSE:
            //       Called by agent to update 3 custom date fields that
            //       record latest AC of type Sales Visit, latest OP and latest Quote
            //A_01_EXECUTE=AutoCOUpdate
            //A_01_OBJSHARED = 1
            //A_01_TYPE = RUNSCRIPT
            //A_ORDER=1,
            //ACTIONS=1,
            //ACTIVE = 1
            //E_TM_HOUR = 19 '7:00 PM
            //E_TM_INTERVAL = 3 'Every day
            //E_TM_MINUTE = 0 'top of hour
            //EVENT=TIMER
            //US_NAME=AutoCOUpdate
            //US_PURPOSE=Runs daily Update of Company records
            //SORTVALUE1=TIMER_ACTIVE

            long lBiID = 0;
            long lastBiId = 0;
            clRowSet doRS = default(clRowSet);
            clRowSet doNewRS = default(clRowSet);
            long iCount = 0;
            bool bUpdateCO = false;
            string par_sDelim = " ";
            string sNowDate = goTR.DateTimeToSysString(goTR.NowLocal(), ref par_iValid, ref par_sDelim);
            string sRecID = "";
            string sWOP = goMeta.PageRead("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED");
            int iFailedOther = 0;
            int iFailedPerm = 0;
            int iFailedTotal = 0;
            int iSuccess = 0;

            try
            {
                do
                {
                    doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT", 50, "", "", "", "", "", true, true, true);
                    iCount = iCount + doRS.Count();
                    if (doRS.GetFirst() == 1)
                    {
                        lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));

                        do
                        {
                            bUpdateCO = false;
                            //until set to true below
                            sRecID = (doRS.GetCurrentRecID() == null) ? "" : doRS.GetCurrentRecID().ToString();

                            //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                            if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                            {
                                doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            //TLD 5/18/2011 -----------Added to include date of latest OP
                            if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                            {
                                doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            //TLD 5/18/2011 -----------Added to include date of latest QT
                            if (doRS.GetLinkCount("LNK_Received_QT") > 0)
                            {
                                doNewRS = new clRowSet("QT", 3, "LNK_To_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }

                            //Update CO
                            if (bUpdateCO == true)
                            {
                                if (doRS.Commit() == 0)
                                {
                                    if (goErr.GetLastError("NUMBER") == "E47250")
                                    {
                                        //Commit failed b/c user has no permissions to edit record; log it, but proceed
                                        iFailedPerm = iFailedPerm + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " due to permissions.", 1, false, true);
                                        //testing
                                    }
                                    else
                                    {
                                        //Commit failed for some other reason.
                                        iFailedOther = iFailedOther + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                        //testing
                                    }
                                }
                            }
                            if (doRS.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                        lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                        //get last BI__ID processed
                    }
                    else
                    {
                        break; // TODO: might not be correct. Was : Exit Do
                    }
                } while (true);

                //Check once more for any newly added records
                doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT", -1, "", "", "", "", "", true, true, true);
                iCount = iCount + doRS.Count();
                if (doRS.GetFirst() == 1)
                {
                    do
                    {
                        bUpdateCO = false;
                        //until set to true below
                        sRecID = (doRS.GetCurrentRecID() == null) ? "" : doRS.GetCurrentRecID().ToString();

                        //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                        if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                        {
                            doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        //TLD 5/18/2011 -----------Added to include date of latest OP
                        if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                        {
                            doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        //TLD 5/18/2011 -----------Added to include date of latest QT
                        //If doRS.GetLinkCount("LNK_Received_QT") > 0 Then
                        //    doNewRS = New clRowSet("QT", 3, "LNK_To_CO='" & sRecID & "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1)
                        //    If doNewRS.GetFirst Then
                        //        doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2)
                        //        bUpdateCO = True
                        //    End If
                        //End If

                        //Update CO
                        if (bUpdateCO == true)
                        {
                            if (doRS.Commit() == 0)
                            {
                                if (goErr.GetLastError("NUMBER") == "E47250")
                                {
                                    //Commit failed b/c user has no permissions to edit record; log it, but proceed
                                    iFailedPerm = iFailedPerm + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " due to permissions.", 1, false, true);
                                    //testing
                                }
                                else
                                {
                                    //Commit failed for some other reason.
                                    iFailedOther = iFailedOther + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                    //testing
                                }
                            }
                        }

                        if (doRS.GetNext() == 0)
                            break; // TODO: might not be correct. Was : Exit Do
                    } while (true);
                    lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                    //get last bi__id processed        
                }
                iFailedTotal = iFailedOther + iFailedPerm;
                iSuccess = Convert.ToInt32(iCount) - iFailedTotal;

                //Write to WOP
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Started " + sNowDate + " and Completed " + goTR.DateTimeToSysString(goTR.NowLocal(), ref par_iValid, ref par_sDelim) + " with " + iSuccess + " successful updates; " + iFailedPerm + " failed updates due to permissions; " + iFailedOther + " total failed updates.");
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);

                iCount = 0;
                iFailedOther = 0;
                iFailedPerm = 0;
                iFailedTotal = 0;
                iSuccess = 0;
                doRS = null;
                lBiID = 0;

            }
            catch (Exception ex)
            {
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Failed at Record " + sRecID + " " + goErr.GetLastError("NUMBER"));
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);
            }

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__TOTALINSTALLATIONS(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__TOTALINSTALLATIONS");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO1(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO1");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO2(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO2");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO3(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO3");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO4(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO4");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO5(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO5");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormControlOnChange_SR__COMPETITIONINSTALLEDBASENO6(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08012016 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__COMPETITIONINSTALLEDBASENO6");

            return true;

        }
        //AD TASK 1681 08/03/2017 Make Fields Read-Only for Market Share
        public bool GP_FormControlOnChange_SR__OURPRODUCTINSTALLEDBASE1(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //VS 08072017 Calc %s
            Form doForm = (Form)par_doCallingObject;

            GP_SetInstallPercentages(ref par_doCallingObject, "SR__OURPRODUCTINSTALLEDBASE1");

            return true;

        }
        //VS TASK 1681 08/07/2017 Make Fields Read-Only for Market Share
        public bool GP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //'VS 08022016 : 
            //doForm.oVar.SetVar("lLenJournal", Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")))
            //doForm.SetControlState("MMO_JOURNAL", 1)
            //doForm.SetControlState("DTE_CALLCAMPAIGNSTARTDATE", 4)
            //doForm.SetControlState("DTE_CALLCAMPAIGNCOMPLETEDATE", 4)

            //'Implement Profile Start and Complete
            //If doForm.doRS.GetFieldVal("CHK_CALLCAMPAIGNSTARTED", 2) = 0 Then
            //    doForm.doRS.oVar.SetVar("CALLCAMPAIGNNotStarted_OnLoad", "1")
            //End If
            //If doForm.doRS.GetFieldVal("CHK_CALLCAMPAIGNCOMPLETED", 2) = 0 Then
            //    doForm.doRS.oVar.SetVar("CALLCAMPAIGNNotCompleted_OnLoad", "1")
            //End If

            //AD TASK 1681 08/03/2017
            doForm.SetControlState("SR__OURPRODUCTINSTALLEDBASEPERCENT1", 4);
            doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT1", 4);
            doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT2", 4);
            doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT3", 4);
            //doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT4", 4)
            //doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT5", 4)
            //doForm.SetControlState("SR__COMPETITIONINSTALLEDBASEPERCENT6", 4)

            par_doCallingObject = doForm;
            return true;
        }
        public bool GP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //VS 08022016 : Implement Profile Start and Complete
            //If doRS.oVar.GetVar("CALLCAMPAIGNNotStarted_OnLoad") = "1" And doRS.GetFieldVal("CHK_CALLCAMPAIGNSTARTED", 2) = 1 Then
            //    doRS.SetFieldVal("DTE_CALLCAMPAIGNSTARTDATE", Now, 2)
            //End If
            //If doRS.oVar.GetVar("CALLCAMPAIGNNotCompleted_OnLoad") = "1" And doRS.GetFieldVal("CHK_CALLCAMPAIGNCOMPLETED", 2) = 1 Then
            //    doRS.SetFieldVal("DTE_CALLCAMPAIGNCOMPLETEDATE", Now, 2)
            //End If

            return true;
        }
        //AD TASK 1681 08/03/2017 Make Calculations for Market Share
        public bool GP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //VS 08052016 : Calc %s
            GP_SetInstallPercentages(ref par_doCallingObject, "SR__TOTALINSTALLATIONS");

            doForm = (Form)par_doCallingObject;

            if (doForm.oVar.GetVar("GP_InstallPercent_Save") != "1")
            {
                double dTotalPercent = 0;
                double SR__OURPRODUCTINSTALLEDBASEPERCENT1_var = (doForm.doRS.GetFieldVal("SR__OURPRODUCTINSTALLEDBASEPERCENT1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__OURPRODUCTINSTALLEDBASEPERCENT1", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT1_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT1", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT2_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT2", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT2", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT3_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT3", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT3", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT4_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT4", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT4", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT5_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT5", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT5", 2));
                double SR__COMPETITIONINSTALLEDBASEPERCENT6_var = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT6", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT6", 2));
                dTotalPercent = SR__OURPRODUCTINSTALLEDBASEPERCENT1_var + SR__COMPETITIONINSTALLEDBASEPERCENT1_var + SR__COMPETITIONINSTALLEDBASEPERCENT2_var + SR__COMPETITIONINSTALLEDBASEPERCENT3_var + SR__COMPETITIONINSTALLEDBASEPERCENT4_var + SR__COMPETITIONINSTALLEDBASEPERCENT5_var + SR__COMPETITIONINSTALLEDBASEPERCENT6_var;

                if (dTotalPercent > 100)
                {
                    doForm.oVar.SetVar("GP_FormSaveMode", doForm.MessageBoxFormMode);
                    doForm.MessageBox("The Overall Installation % is more than 100. Do you want to continue saving the record?", clC.SELL_MB_YESNO, "Selltis", "Yes", "No", "", "", "MessageBoxEvent", "", "", null, null, "Yes", "No", "", "", "GP_InstallPercent_Save");
                    par_doCallingObject = doForm;
                    return false;
                }
            }

            //VS 08022016 : Create Activity Log of Journal Entry
            if (doForm.oVar.GetVar("GP_CreateActLog_Ran") != "1")
            {
                //goScr.RunScript("GP_CreateActLog", doForm);
                par_doCallingObject = doForm;
                scriptManager.RunScript("GP_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                doForm = (Form)par_doCallingObject;
            }


            return true;
        }

        //AD TASK 1681 08/03/2017 Make Calculations for Market Share

        public void GP_SetInstallPercentages(ref object par_doCallingObject, string par_sPercentField)
        {
            //VS 08012016 Added for Profile Update Journal Entry
            Form doForm = (Form)par_doCallingObject;

            double dOverallInstalls = 0;
            double dInstalls = 0;
            double dInstallPercent = 0;
            dOverallInstalls = (doForm.doRS.GetFieldVal("SR__TOTALINSTALLATIONS", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__TOTALINSTALLATIONS", 2));

            //Our Installation

            if (par_sPercentField == "SR__OURPRODUCTINSTALLEDBASE1" | par_sPercentField == "SR__TOTALINSTALLATIONS")
            {
                dInstalls = (doForm.doRS.GetFieldVal("SR__OURPRODUCTINSTALLEDBASE1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__OURPRODUCTINSTALLEDBASE1", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__OURPRODUCTINSTALLEDBASEPERCENT1", Math.Round(dInstallPercent, 2), 2);

            }
            //Competition 1

            if (par_sPercentField == "SR__COMPETITIONINSTALLEDBASENO1" | par_sPercentField == "SR__TOTALINSTALLATIONS")
            {
                dInstalls = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO1", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO1", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT1", Math.Round(dInstallPercent, 2), 2);

            }
            //Competition 2

            if (par_sPercentField == "SR__COMPETITIONINSTALLEDBASENO2" | par_sPercentField == "SR__TOTALINSTALLATIONS")
            {
                dInstalls = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO2", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO2", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT2", Math.Round(dInstallPercent, 2), 2);

            }
            //Competition 3

            if (par_sPercentField == "SR__COMPETITIONINSTALLEDBASENO3" | par_sPercentField == "SR__TOTALINSTALLATIONS")
            {
                dInstalls = (doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO3", 2) == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO3", 2));

                if (dOverallInstalls == 0 | dInstalls == 0)
                {
                    dInstallPercent = 0;
                }
                else
                {
                    dInstallPercent = (dInstalls / dOverallInstalls) * 100;
                }

                doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT3", Math.Round(dInstallPercent, 2), 2);

            }
            //'Competition 4
            //If par_sPercentField = "SR__COMPETITIONINSTALLEDBASENO4" Or par_sPercentField = "SR__TOTALINSTALLATIONS" Then

            //    dInstalls = doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO4", 2)

            //    If dOverallInstalls = 0 Or dInstalls = 0 Then
            //        dInstallPercent = 0
            //    Else
            //        dInstallPercent = (dInstalls / dOverallInstalls) * 100
            //    End If

            //    doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT4", Math.Round(dInstallPercent, 2), 2)

            //End If
            //'Competition 5
            //If par_sPercentField = "SR__COMPETITIONINSTALLEDBASENO5" Or par_sPercentField = "SR__TOTALINSTALLATIONS" Then

            //    dInstalls = doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO5", 2)

            //    If dOverallInstalls = 0 Or dInstalls = 0 Then
            //        dInstallPercent = 0
            //    Else
            //        dInstallPercent = (dInstalls / dOverallInstalls) * 100
            //    End If

            //    doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT5", Math.Round(dInstallPercent, 2), 2)

            //End If
            //'Competition 6
            //If par_sPercentField = "SR__COMPETITIONINSTALLEDBASENO6" Or par_sPercentField = "SR__TOTALINSTALLATIONS" Then

            //    dInstalls = doForm.doRS.GetFieldVal("SR__COMPETITIONINSTALLEDBASENO6", 2)

            //    If dOverallInstalls = 0 Or dInstalls = 0 Then
            //        dInstallPercent = 0
            //    Else
            //        dInstallPercent = (dInstalls / dOverallInstalls) * 100
            //    End If

            //    doForm.doRS.SetFieldVal("SR__COMPETITIONINSTALLEDBASEPERCENT6", Math.Round(dInstallPercent, 2), 2)

            //End If

            par_doCallingObject = doForm;


        }

        //VS 06192018 : Kennedy Valves - Demo
        public bool RETENTIONSEARCH_FormControlOnChange_BTN_SEARCH_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form oForm = (Form)par_doCallingObject;

            //Find dialog; Activity tab Search button
            string sComments = null;
            string sCreditedToUser = null;
            string sEmailsSent = null;
            string sEmailsReceived = null;
            string sCorr = null;


            string sView = null;
            int iCondCount = 0;
            //Cs 8/17/09
            string sViewCondition = null;
            string sNewCondition = null;
            int iOrigCondCount = 0;
            int i = 0;
            string sFilter = "";

            //Get values from form
            sComments = Strings.Trim(oForm.GetControlVal("NDB_TXT_CONTAINS"));
            //if (!string.IsNullOrEmpty(sComments))
            //{
            //    sComments = goTR.ConvertStringForQS(goTR.PrepareForSQL(sComments), "MMO_NOTES", "AC", true);
            //}
            sEmailsSent = Microsoft.VisualBasic.Strings.UCase(oForm.GetControlVal("NDB_CHK_EMAILSSENT"));
            sEmailsReceived = Microsoft.VisualBasic.Strings.UCase(oForm.GetControlVal("NDB_CHK_EMAILSRECEIVED"));

            //Use values to filter Actvity - Search Results desktop if it exists
            string Key = Guid.NewGuid().ToString();
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_2B194999-753C-4B51-5858-A90400E1BE49", false, Key);
            //Edit views in DT

            //View 1:Activities - Search Results
            sView = oDesktop.GetViewMetadata("VIE_A444AEF0-2FC7-4D1B-5858-A90400E1E24D");
            //iCondCount = goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"))



            //Only want to filter if the NDB fields contained a value
            //Get the total # of conditions
            if (!string.IsNullOrEmpty(sComments) & sComments != "''")
                iCondCount = iCondCount + 1;
            if (sEmailsSent == "CHECKED")
                iCondCount = iCondCount + 1;
            if (sEmailsReceived == "CHECKED")
                iCondCount = iCondCount + 1;
            //Add to remove alerady applied Policy records
            iCondCount = iCondCount + 1;

            //Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (!string.IsNullOrEmpty(sComments))
            {
                ////Add 'Notes' condition
                //goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MMO_NOTES%>");
                //goTR.StrWrite(ref sView, "C" + i + "CONDITION", "[");
                ////contains
                //goTR.StrWrite(ref sView, "C" + i + "VALUE1", sComments);
                //i = i + 1;
                //if (!string.IsNullOrEmpty(sFilter))
                //{
                //    sFilter = sFilter + " AND MMO_NOTES[" + sComments + "";
                //}
                //else
                //{
                //    sFilter = "MMO_NOTES[" + sComments + "";
                //}

                //Add 'Letters' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MMO_LETTER%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "[");
                //contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sComments);
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                {
                    sFilter = sFilter + " AND MMO_LETTER[" + sComments + "";
                }
                else
                {
                    sFilter = "MMO_LETTER[" + sComments + "";
                }
            }
            if (sEmailsSent == "CHECKED" && sEmailsReceived == "CHECKED")
            {
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MLS_TYPE%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "=");
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", "5");
                goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                i = i + 1;

                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MLS_TYPE%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "=");
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", "6");
                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                i = i + 1;

                if (!string.IsNullOrEmpty(sFilter))
                    sFilter = sFilter + " AND (MLS_TYPE=5 OR MLS_TYPE=6)";
                else
                    sFilter = "(MLS_TYPE=5 OR MLS_TYPE=6)";

            }
            else if (sEmailsSent != "CHECKED" && sEmailsReceived == "CHECKED")
            {
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MLS_TYPE%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "=");
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", "5");
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                    sFilter = sFilter + " AND MLS_TYPE=5";
                else
                    sFilter = "MLS_TYPE=5";
            }
            else if (sEmailsSent == "CHECKED" && sEmailsReceived != "CHECKED")
            {
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MLS_TYPE%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "=");
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", "6");
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                    sFilter = sFilter + " AND MLS_TYPE=6";
                else
                    sFilter = "MLS_TYPE=6";
            }

            goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_RELATED_RP%>");
            goTR.StrWrite(ref sView, "C" + i + "CONDITION", "13");
            goTR.StrWrite(ref sView, "C" + i + "VALUE1", "");
            i = i + 1;
            if (!string.IsNullOrEmpty(sFilter))
                sFilter = sFilter + " AND LNK_RELATED_RP%%BI__ID<1 ";
            else
                sFilter = "LNK_RELATED_RP%%BI__ID<1 ";


            sNewCondition = sFilter;
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);


            oDesktop.SetViewMetadata("VIE_A444AEF0-2FC7-4D1B-5858-A90400E1E24D", sView);

            //Load current view dataset with find filtertext..J
            SessionViewInfo _sessionViewInfo = new SessionViewInfo();
            _sessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + "VIE_A444AEF0-2FC7-4D1B-5858-A90400E1E24D");
            _sessionViewInfo.ViewMetaData = sView;
            _sessionViewInfo.ViewCondition = sNewCondition;
            Util.LoadViewData("VIE_A444AEF0-2FC7-4D1B-5858-A90400E1E24D", _sessionViewInfo.SortText, 1, "", false, Key);
            //update all the child views data sets
            Util.LoadViewDataSets(_sessionViewInfo.TopViewCount, _sessionViewInfo.TabViewCount, "VIE_A444AEF0-2FC7-4D1B-5858-A90400E1E24D", true, Key);

            oDesktop = new Desktop("GLOBAL", "DSK_2B194999-753C-4B51-5858-A90400E1BE49", true, Key);

            //Que Activity Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);

            par_doCallingObject = oForm;

            return true;
        }
        public bool RETENTIONSEARCH_FormControlOnChange_BTN_CANCEL_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            doForm.CloseOnReturn = true;

            par_doCallingObject = doForm;
            return true;
        }
        public bool AC_ViewControlOnChange_BTN_APPLYPOLICY_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            scriptManager.RunScript("APPLYPOLICY", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            par_bRunNext = false;
            return true;
        }
        public bool RP_ViewControlOnChange_BTN_APPLYPOLICY_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            scriptManager.RunScript("APPLYPOLICY", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            par_bRunNext = false;
            return true;
        }

        public bool APPLYPOLICY_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            string sID = null;

            Desktop doDesktop = (Desktop)par_doCallingObject;

            //Get selected record and uncheck ext dev review
            //goUI - Not Implemented
            sID = goUI.GetLastSelected("SELECTEDRECORDID");
            string Key = "";
            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
            {
                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
            }

            //if (Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
            //    sID = Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();

            //sID = GetSelectedRecordIds(Key);
            sID = Util.GetSessionValue(Key + "_" + "VIE_A444AEF0-2FC7-4D1B-5858-A90400E1E24D" + "_" + "MultiSelectedRecords").ToString();

            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select an Activity.",0,"Selltis","","","","","","","",ref par_doCallingObject)
                doDesktop.MessageBox(ref par_doCallingObject, "Please check a Record.");
                return true;
            }
            string[] RecordIds = sID.Split(',');

            foreach (var RecordId in RecordIds)
            {
                clRowSet doRECRS = new clRowSet("AC", clC.SELL_EDIT, "GID_ID=" + RecordId, "", "*", 1, "", "", "", "", "", true, true);
                if (doRECRS.GetFirst() == 1)
                {
                    doRECRS.SetFieldVal("LNK_RELATED_RP", "30353934-3439-3538-5250-362f31392f32");
                    doRECRS.Commit();
                }
            }

            return true;
        }

        //V_S changes for Maps
        public bool RG_FormControlOnChange_BTN_DRAWTERR_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //For notes on how to create a custom script, see clScrMng. ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTitleBar = "Region Editor - " + doForm.doRS.GetFieldVal("SYS_NAME");

            //goUI.OpenURLExternal("../Pages/Cus_DrawTerritoryRegion.aspx?GID_ID=" & doForm.RecordID, sTitleBar, "height=740,width=1250,left=100,top=100,status=yes,location=no,toolbar=no,resizable=yes,titlebar=yes,dependent=yes")

            doForm.Save(3);



            string sURL = null;
            string sName = null;

            par_bRunNext = false;

            sURL = "";
            sName = doForm.doRS.GetFieldVal("SYS_Name").ToString();

            goTR.URLWrite(ref sURL, "sTitleBar", doForm.Title);
            goTR.URLWrite(ref sURL, "objectid", doForm.doRS.GetFieldVal("GID_ID").ToString());
            goTR.URLWrite(ref sURL, "objectname", sName);
            goTR.URLWrite(ref sURL, "regionTable", doForm.doRS.sRSFile);


            //string sURL = "";
            //string sName = null;

            //sURL = "../Pages/Cus_DrawTerritoryRegion.aspx?GID_ID=" + doForm.RecordID;

            //goTR.URLWrite(ref sURL, "objectid", doForm.doRS.GetFieldVal("GID_ID").ToString());
            //sName = doForm.doRS.GetFieldVal("SYS_Name").ToString();

            //goTR.URLWrite(ref sURL, "objectname", sName);
            //HttpContext.Current.Response.Redirect(sURL);

            goUI.Queue("MAPDIALOG", sURL);

            return true;

        }

        private string GetSelectedRecordIds(string Key)
        {
            string LastSelectedMultiSelect = "0";
            string sReturnValue = "";
            if (Util.GetSessionValue(Key + "_" + "LastSelectedMultiSelect") != null)
            {
                LastSelectedMultiSelect = Util.GetSessionValue(Key + "_" + "LastSelectedMultiSelect").ToString();
            }

            if (LastSelectedMultiSelect == "0" && Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
            {
                return Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();
            }
            else if (LastSelectedMultiSelect == "1" && Util.GetSessionValue(Key + "_" + "MultiSelectedRecords") != null)
            {
                sReturnValue = Util.GetSessionValue(Key + "_" + "MultiSelectedRecords").ToString();

                Util.ClearSessionValue(Key + "_" + "MultiSelectedRecords");
                return sReturnValue;
            }
            else
            {
                return sReturnValue;
            }
        }

        public bool MessageBoxEvent_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //MI 11/27/07 Changed Dim doForm As clForm to Dim doForm As Object.
            //MI 11/21/07 Updated comments.
            //MI 10/8/07 Added Return True per CS.
            //Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            //the user's response.
            //par_doCallingObject is always the doForm under which MessageBox was executed.
            //par_doArray is an array of strings through which you can pass multiple strings to this method.
            //Par_s1 is the identifier of the button clicked. usually "YES", "NO", "CANCEL", or "1", "2", or "3". It is
            //   one of the values from par_s1-3 passed to doForm.MessageBox itself, depending on the button clicked.
            //   For example, if you click button 2, the value from par_s2 will be passed here. 
            //par_s2 is the value the user entered in an input box, if the type is input box. Else, blank.
            //par_s3 is the identifier of the third button clicked. usually "CANCEL" or "3".
            //Par_s4 can be whatever you want to pass to this method.
            //Par_s5 is the name of the script that called doform.MessageBox plus a description of what it's doing, e.g. "CO_FormOnSave_LinkTeamLeaderMsgBox".

            //After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc = null;
            sProc = "Script::MessageBoxEvent";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //DEBUG - MI experimenting with declaring a variable to be clForm or clDesktop
            //Dim oType As System.Type = Nothing
            //oType = par_doCallingObject.GetType.ToString
            //Dim doForm As oType

            bool bNoPerm = false;
            bool bError = false;
            bool bReqMissing = false;
            //Dim sView As String
            string sJournal = null;
            //original value in journal field
            string sWork = null;
            //input value
            //Dim sWork2 As String
            clRowSet doRS = default(clRowSet);
            decimal cAmount = default(decimal);
            bool bUpdateFailed = false;
            Form doForm;

            try
            {
                switch (Strings.UCase(par_s5))
                {
                    case "CS_FORMCONTROLONCHANGE_BTN_SENDTESTEMAIL_EMAILADDRESSES":
                        //VS 07302015 ConstContact Test Email
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                doForm = (Form)par_doCallingObject;
                                doForm.oVar.SetVar("SENDTESTEMAIL_EMAILADDRESSES", par_s2);
                                doForm.MessageBox("Enter a personal note that will appear at the top of your email.", clC.SELL_MB_INPUTBOX, "Personal Note", "OK", "CANCEL", "", "", "MESSAGEBOXEVENT", "MESSAGEBOXEVENT", "", doForm, null, "OK", "CANCEL", "", "", "CS_FORMCONTROLONCHANGE_BTN_SENDTESTEMAIL_PERSONALNOTE");
                                break;
                            case "CANCEL":

                                break;
                        }
                        break;
                    case "CS_FORMCONTROLONCHANGE_BTN_SENDTESTEMAIL_PERSONALNOTE":
                        //VS 07302015 ConstContact Test Email
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                //  par_doCallingObject=doForm;
                                //scriptManager.RunScript("ToDo_ReassignToTesting", ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections);
                                //doForm=(Form)par_doCallingObject;
                                bool runnext = true;
                                scriptManager.RunScript("CS_SENDTESTEMAIL", ref par_doCallingObject, ref par_oReturn, ref runnext, ref par_sSections, null, "", par_s2);
                                doForm = (Form)par_doCallingObject;
                                break;
                            case "CANCEL":

                                break;
                        }
                        break;
                    case "COMPANY_UPDATE":

                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                doForm = (Form)par_doCallingObject;
                                clRowSet clrwsetcontacts = new clRowSet("CN", clC.SELL_EDIT, "LNK_RELATED_CO%%GID_ID='" + doForm.FormRowSet.GetFieldVal("GID_ID").ToString() + "'", "", "*", -1, "", "", "", "", "", true, true);
                                if (clrwsetcontacts.Count() > 0)
                                {
                                    do
                                    {
                                        //work = objForm.FormRowSet.GetFieldVal("TXT_CompanyName").ToString();
                                        if (clrwsetcontacts.GetFieldVal("TXT_CompanyNameText").ToString() != "")
                                        {
                                            string work = Util.GetSessionValue("Currentcity").ToString();
                                            string a = Util.GetSessionValue("Currentadd").ToString();
                                            string c = Util.GetSessionValue("Currentcountry").ToString();
                                            string p = Util.GetSessionValue("CurrentsPhone1").ToString();

                                            clrwsetcontacts.SetFieldVal("TXT_CITYMAILING", work);
                                            clrwsetcontacts.SetFieldVal("TXT_CITYMAILING", Util.GetSessionValue("Currentadd").ToString());
                                            clrwsetcontacts.SetFieldVal("TXT_CITYMAILING", Util.GetSessionValue("Currentcountry").ToString());
                                            clrwsetcontacts.SetFieldVal("TXT_CITYMAILING", Util.GetSessionValue("CurrentsPhone1").ToString());
                                            par_doCallingObject = doForm;
                                            clrwsetcontacts.Commit();
                                            if (clrwsetcontacts.Commit() != 1)
                                            {
                                                // 'The user doesn't have Contact edit permission, nothing we can do
                                            }
                                        }
                                        if (clrwsetcontacts.GetNext() != 1)
                                        {
                                            break;
                                        }


                                    } while (true);
                                    break;
                                }
                                break;
                            case "NO":
                                break;
                        }
                        break;

                }

            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    //goErr.SetError(ex, 45105, sProc);
                    return false;
                }
            }

            return true;

        }


        //P_R Td priority on change
        public bool TD_FormControlOnChange_MLS_PRIORITY_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            Form doForm = (Form)par_doCallingObject;


            string _priority = Convert.ToString(doForm.doRS.GetFieldVal("MLS_PRIORITY", 2));
            if (_priority == "0")
            {
                doForm.doRS.SetFieldVal("DTE_DUETIME", doForm.doRS.GetFieldVal("DTE_STARTDATE"));
            }
            else if (_priority == "1")
            {
                doForm.doRS.SetFieldVal("DTE_DUETIME", goTR.AddDay(Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_STARTDATE", 2)), 1), 2);
            }
            else if (_priority == "2")
            {
                doForm.doRS.SetFieldVal("DTE_DUETIME", goTR.AddDay(Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_STARTDATE", 2)), 2), 2);
            }
            else if (_priority == "3")
            {
                doForm.doRS.SetFieldVal("DTE_DUETIME", goTR.AddDay(Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_STARTDATE", 2)), 3), 2);
            }
            else if (_priority == "4")
            {
                doForm.doRS.SetFieldVal("DTE_DUETIME", goTR.AddDay(Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_STARTDATE", 2)), 4), 2);
            }
            else if (_priority == "5")
            {
                doForm.doRS.SetFieldVal("DTE_DUETIME", goTR.AddDay(Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_STARTDATE", 2)), 5), 2);
            }


            par_doCallingObject = doForm;
            return true;
        }

        public bool AutoAlertEveryNSecs_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //MI 4/10/08 Added True for par_bGetAllUsersUnsharedRecs param in rowset declarations to get all private records,
            //       not only the ones created by the current user.
            //MI 10/26/07 Fixed alerts not firing when set to few minutes to start time or if end time = start time.
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DETAILS, true);

            //PURPOSE:
            //  Every several seconds (minute?), this script is run from a timer automator.
            //  It creates alerts for appointment alarms.
            //  It adds alerts about new messages and to dos received from the
            //  workgroup or saved locally, etc.

            string sResult = null;
            clRowSet doRS = default(clRowSet);
            DateTime dtDate = default(DateTime);
            //Date pointer for Appt alarms in local time
            DateTime dtNow = goTR.NowLocal();
            //Current local time
            string sName = null;
            PublicDomain.TzTimeZone zone = default(PublicDomain.TzTimeZone);

            int par_iValid = 4;
            string par_sDelim = "|";
            //overdue tasks alerts

            try
            {
                //Overdue Tasks alerts

                DateTime dtDateTime = goTR.NowUTC().Date;
                par_iValid = 4;
                par_sDelim = "|";
                string sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sDelim);

                //MLS_TYPE='10' AND CHK_OPEN=0 AND DTT_DUETIME<='" + sDateTime + "' AND CHK_DUEALERTSENT=0 AND LNK_ASSIGNEDTO_US%%BI__ID>0 AND LNK_ASSIGNEDTO_US%%LNK_SUPERVISOR_US%%BI__ID>0
                //CHK_DUEALERTSENT=0 AND MLS_STATUS<>'4' AND MLS_TYPE='10' AND DTT_DUETIME<='today' AND LNK_ASSIGNEDTO_US%%LNK_SUPERVISOR_US%%BI__ID>0 

                doRS = new clRowSet("TD", clC.SELL_EDIT, "CHK_DUEALERTSENT=0 AND MLS_STATUS<>'4' AND MLS_TYPE='10' AND DTT_DUETIME<='today' AND LNK_ASSIGNEDTO_US%%LNK_SUPERVISOR_US%%BI__ID>0", "DTT_DUETIME A", "**", -1, "", "", "", "", "", false, false, true);

                if (doRS.GetFirst() == 1)
                {
                    doRS.bBypassValidation = true;

                    do
                    {
                        string sDescription = Convert.ToString(doRS.GetFieldVal("TXT_DESCRIPTION"));
                        string sDueDate = Convert.ToString(doRS.GetFieldVal("DTE_DUETIME"));
                        string sAssignedToName = Convert.ToString(doRS.GetFieldVal("LNK_ASSIGNEDTO_US%%SYS_NAME")).Replace(Constants.vbCrLf, ",");
                        string sSupervisorEmailId = Convert.ToString(doRS.GetFieldVal("LNK_ASSIGNEDTO_US%%LNK_SUPERVISOR_US%%EML_EMAIL"));
                        string sSupervisorUserId = Convert.ToString(doRS.GetFieldVal("LNK_ASSIGNEDTO_US%%LNK_SUPERVISOR_US%%GID_ID"));

                        if (!string.IsNullOrEmpty(sSupervisorEmailId))
                        {
                            clEmail _email = new clEmail();
                            string sSubject = "The task \"" + sDescription + "\" assigned to \"" + sAssignedToName + "\" is due on " + sDueDate;
                            goUI.AddAlert("Overdue Task: " + sSubject, "OPENRECORD", doRS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY).ToString(), sSupervisorUserId, "TODOURGENT16.GIF");
                            bool bretval = _email.SendSMTPEmailNew(sSubject, sSubject, sSupervisorEmailId, "", "", "", "<EMAIL>");

                            if (bretval == true)
                            {
                                doRS.SetFieldVal("CHK_DUEALERTSENT", 1, 2);
                                doRS.Commit();
                            }

                        }

                        if (doRS.GetNext() == 0)
                            break;
                    } while (true);

                }
                else
                {
                    goLog.Log("Overdue Tasks Alert", "No records found.", 1, false, true);
                }

            }
            catch (Exception ex)
            {
                goLog.Log("Overdue Tasks Alert", ex.ToString(), 1, false, true);
            }


            //overdue tasks alerts

            //------- Appt Alarms ----------
            //Replaces agents:
            //  Appt:Alarm20mins
            //  Appt:Alarm20MinsMessage

            par_iValid = 4;
            par_sDelim = "|";
            dtDate = goTR.UTC_UTCToLocal(goTR.StringToDateTime(goMeta.LineRead("GLOBAL", "OTH_ALERT_POINTERS", "AP_ALARM|DATE"), "", "", ref par_iValid));

            //Pointers don't exist, create them
            if (dtDate == Convert.ToDateTime(clC.SELL_BLANK_DATETIME))
            {
                //Date pointer wasn't set before - set it to midnight today
                //This will allow the alarms of Appts that may be in progress to be processed.
                //Appts that start earlier will not be processed.
                dtDate = Convert.ToDateTime(goTR.StringToDateTime("today", "", "", ref par_iValid));
            }

            //If the pointers are in the future, set them to midnight today. This ensures 
            //processing proper alarms when the clock is set back.
            if (dtDate > dtNow)
            {
                dtDate = Convert.ToDateTime(goTR.StringToDateTime("today", "", "", ref par_iValid));
            }

            //Process records
            clArray aUsers = new clArray();
            clArray aUsers2 = new clArray();
            int i = 0;
            int j = 0;
            //*** MI 10/26/07 Fixing the filter, which is causing Alarms set to 0 minutes not to fire
            //doRS = New clRowSet("AP", 3, _
            //    "CHK_COMPLETED=0 AND " & _
            //    "CHK_ALARM=1 AND " & _
            //    "DTT_ALARMTIME>'" & goTR.DateTimeToString(dtDate) & "' AND DTT_ALARMTIME<='" & goTR.DateTimeToString(dtNow) & "'" & _
            //    " AND DTT_ENDTIME>'" & goTR.DateTimeToString(dtNow) & "'", _
            //    "DTT_ALARMTIME A", _
            //    "GID_ID,LNK_CoordinatedBy_US%%TXT_Code,DTT_StartTime,TXT_Description,LNK_INVOLVES_US,SYS_NAME,LNK_COORDINATEDBY_US")
            //Alarm time must be: 1. greater than the pointer, 2. less or equal to current time plus 10 minutes (to fire an alarm up to 10
            //minutes into an appointment and 3. End Time must be greater than 10 minutes from now (to allow for server busy situations
            //and to fire an alarm up to 10 minutes after the fact.
            //MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all private recs created by all users
            doRS = new clRowSet("AP", 3, "CHK_COMPLETED=0 AND " + "CHK_ALARM=1 AND " + "DTT_ALARMTIME>'" + goTR.DateTimeToString(dtDate, "", "", ref par_iValid, ref par_sDelim) + "'" + " AND DTT_ALARMTIME<='" + goTR.DateTimeToString(dtNow, "", "", ref par_iValid, ref par_sDelim) + "'" + " AND DTT_ENDTIME>'" + goTR.DateTimeToString(dtNow.AddMinutes(-10), "", "", ref par_iValid, ref par_sDelim) + "'", "DTT_ALARMTIME A", "GID_ID,LNK_CoordinatedBy_US%%TXT_Code,DTT_StartTime,TXT_Description,LNK_INVOLVES_US,SYS_NAME,LNK_COORDINATEDBY_US", -1, "", "", "", "", "", false, false, true);

            DataSet rs = doRS.oDataSet;
            if (doRS.GetFirst() == 1)
            {
                do
                {
                    dtDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_ALARMTIME", clC.SELL_SYSTEM));

                    //We are currently not supporting Message Box

                    //If doRS.GetFieldVal("CHK_ALARMWITHMESSAGE", 2) = 1 Then
                    //    'Display an alarm messagebox

                    //    sWork = "Appointment alarm: '" & doRS.GetFieldVal("TXT_DESCRIPTION") & "' is scheduled for " & _
                    //      doRS.GetFieldVal("TME_STARTTIME", 1) & "." & vbCrLf & vbCrLf & _
                    //      "Would you like to view/reschedule this appointment?"
                    //    sResult = goP.AddAlert(sWork, "OPENRECORD", doRS.GetFieldVal("GID_ID"), "ALARMRED.ICO", 1, "YESNO")

                    //    If sResult = "" Then
                    //        goErr.SetError()
                    //        Return False
                    //    End If
                    // Else
                    //Add a desktop alert
                    DataTable oTable = null;  //SB 02222019 Pass it as null, otherwise it will throw error.
                    doRS.GetLinkVal("LNK_INVOLVES_US", ref aUsers, true, 0, -1, "A_a", ref oTable);

                    for (i = 1; i <= aUsers.GetDimension(); i++)
                    {
                        //*** MI 10/29/07 Added block below: converting datetimes to user's time zone
                        //Get user's time zone
                        zone = goTR.UTC_GetUserTimeZone(aUsers.GetItem(i));
                        //Concatenate the equivalent of a SYS_Name, but with datetime in the user's time zone
                        //goUI - Not Implemented
                        //sName = Strings.Left(goTR.DateTimeToSysString(zone.ToLocalTime(goTR.UTC_LocalToUTC(doRS.GetFieldVal("DTT_StartTime", 2))), , " "), 16);
                        sName += " " + doRS.GetFieldVal("LNK_CoordinatedBy_US%%TXT_Code");
                        sName += " " + doRS.GetFieldVal("TXT_Description");
                        //*** MI 10/29/07 Added block above
                        //sResult = goUI.AddAlert("Appt Alarm: " & doRS.GetFieldVal("SYS_NAME", clC.SELL_FRIENDLY), "OPENRECORD", doRS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY), aUsers.GetItem(i), "ALARMRED.gif")
                        sResult = goUI.AddAlert("Appt Alarm: " + sName, "OPENRECORD", doRS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY).ToString(), aUsers.GetItem(i), "ALARMRED.gif").ToString();
                        if (string.IsNullOrEmpty(sResult))
                        {
                            return false;
                        }
                    }

                    //goLog.Log("AutoAlert", "Before", , , True)
                    oTable = null;  //SB 02222019 Pass it as null, otherwise it will throw error.
                    doRS.GetLinkVal("LNK_Coordinatedby_US", ref aUsers2, true, 0, -1, "A_a", ref oTable);
                    for (j = 1; j <= aUsers2.GetDimension(); j++)
                    {
                        //goLog.Log("AutoAlertEveryNSecs", aUsers2.GetItem(j), , , True)
                        //*** MI 10/29/07 Added block below: converting datetimes to user's time zone
                        //Get user's time zone
                        zone = goTR.UTC_GetUserTimeZone(aUsers2.GetItem(j));
                        //Concatenate the equivalent of a SYS_Name, but with datetime in the user's time zone
                        DateTime dtt_StartTime = Convert.ToDateTime(doRS.GetFieldVal("DTT_StartTime", 2));
                        par_sDelim = " ";
                        sName = Strings.Left(goTR.DateTimeToSysString(zone.ToLocalTime(goTR.UTC_LocalToUTC(ref dtt_StartTime)), ref par_iValid, ref par_sDelim), 16);
                        sName += " " + doRS.GetFieldVal("LNK_CoordinatedBy_US%%TXT_Code");
                        sName += " " + doRS.GetFieldVal("TXT_Description");
                        //*** MI 10/29/07 Added block above
                        //sResult = goUI.AddAlert("Appt Alarm: " & doRS.GetFieldVal("SYS_NAME", clC.SELL_FRIENDLY), "OPENRECORD", doRS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY), aUsers2.GetItem(j), "ALARMRED.gif")
                        sResult = goUI.AddAlert("Appt Alarm: " + sName, "OPENRECORD", doRS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY).ToString(), aUsers2.GetItem(j), "ALARMRED.gif").ToString();
                        if (string.IsNullOrEmpty(sResult))
                        {
                            return false;
                        }
                    }

                    // End If
                    if (doRS.GetNext() != 1)
                        break; // TODO: might not be correct. Was : Exit Do
                } while (true);

                System.Data.SqlClient.SqlConnection par_oConnection = goData.GetConnection(); //new System.Data.SqlClient.SqlConnection();
                //Write the pointer
                goMeta.LineWrite("GLOBAL", "OTH_ALERT_POINTERS", "AP_ALARM|DATE", goTR.DateTimeToString(goTR.UTC_LocalToUTC(ref dtNow), "", "", ref par_iValid, ref par_sDelim), ref par_oConnection);

            }
            //delete(doRS)
            doRS = null;



            par_bRunNext = false;

            return true;

        }

    }
}

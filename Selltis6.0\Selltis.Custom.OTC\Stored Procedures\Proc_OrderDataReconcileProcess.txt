﻿  CREATE PROC [dbo].[Proc_OrderDataReconcileProcess]    
(    
@DataTable AS DBO.ORDERTABLETYPE READONLY ,   
@RowCount Int  
)    
AS    
Begin    
    
---TRUNCATE TABLE ORDERDATARECONCILE    
INSERT INTO ORDERDATARECONCILE(quoteNo, Linekey, transdttmz)    
SELECT quoteNo, Linekey, transdttmz FROM @DataTable    
    
--SELECT COUNT(*)     
----UPDATE QO SET MLS_STATUS =4    
--FROM QO    
--LEFT JOIN ORDERDATARECONCILE T    
--ON QO.TXT_SXORDERNO =T.quoteNo     
--WHERE T.quoteNo IS NULL  ---87520  
    
--SELECT COUNT(*)     
----UPDATE QD SET MLS_STATUS =4    
--FROM QD    
--LEFT JOIN ORDERDATARECONCILE T    
--ON QD.TXT_SXLINENO = T.Linekey     
--WHERE T.Linekey IS NULL   ---235526  
    
END    
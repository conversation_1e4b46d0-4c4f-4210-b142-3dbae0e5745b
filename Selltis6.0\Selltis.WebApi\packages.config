﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Azure.Core" version="1.44.1" targetFramework="net48" />
  <package id="Azure.Storage.Blobs" version="12.25.0" targetFramework="net48" />
  <package id="Azure.Storage.Common" version="12.24.0" targetFramework="net48" />
  <package id="Microsoft.ApplicationInsights" version="2.15.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.Agent.Intercept" version="2.4.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.DependencyCollector" version="2.15.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.PerfCounterCollector" version="2.15.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.Web" version="2.15.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.WindowsServer" version="2.15.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" version="2.15.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.Cors" version="5.2.9" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor" version="3.2.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.TelemetryCorrelation" version="1.0.8" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.9" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.9" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Cors" version="5.2.9" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net461" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net461" />
  <package id="Microsoft.IdentityModel.Abstractions" version="6.23.1" targetFramework="net461" requireReinstallation="true" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="6.23.1" targetFramework="net461" requireReinstallation="true" />
  <package id="Microsoft.IdentityModel.Logging" version="6.23.1" targetFramework="net461" requireReinstallation="true" />
  <package id="Microsoft.IdentityModel.Protocols" version="6.23.1" targetFramework="net461" requireReinstallation="true" />
  <package id="Microsoft.IdentityModel.Protocols.OpenIdConnect" version="6.23.1" targetFramework="net461" requireReinstallation="true" />
  <package id="Microsoft.IdentityModel.Tokens" version="6.23.1" targetFramework="net461" requireReinstallation="true" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="12.0.2" targetFramework="net461" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net461" />
  <package id="System.ClientModel" version="1.1.0" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="6.0.1" targetFramework="net48" />
  <package id="System.IdentityModel.Tokens.Jwt" version="6.23.1" targetFramework="net461" requireReinstallation="true" />
  <package id="System.IO.Hashing" version="8.0.0" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Memory.Data" version="6.0.0" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net461" />
  <package id="System.Text.Encodings.Web" version="6.0.0" targetFramework="net48" />
  <package id="System.Text.Json" version="6.0.10" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net461" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net461" requireReinstallation="true" />
</packages>
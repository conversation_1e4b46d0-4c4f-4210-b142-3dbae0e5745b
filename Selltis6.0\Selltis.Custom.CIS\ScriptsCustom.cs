﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.IO;
using System.Net;
//using Newtonsoft.Json;
//using System.Web.Script.Serialization;
using System.Xml;
using System.Text.RegularExpressions;
using System.Diagnostics;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Data.SqlClient;
using System.Threading;

namespace Selltis.Custom
{
    public class ScriptsCustom
    {
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clProject goP;
        private clLog goLog;
        private clError goErr;
        private clPerm goPerm;
        private ClUI goUI;
        ScriptManager scriptManager = new ScriptManager();
        SqlConnection par_oConnection = null;
        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        public string sError;


        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();

            Util.SetSessionValue("SkipQLSpecificLogic", "Y");
        }
        public ScriptsCustom()
        {
            Initialize();
        }

        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (ex.Message  != clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }

        public bool AM_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sDate = "";
            string sTempDate = "";

            // TLD 9/15/2014 Fill DTT_PMSummary with earliest
            // date from PM1....PM4
            // Set to blank date?
            doForm.doRS.SetFieldVal("DTT_PMSummary", Convert.ToDateTime("1753-01-02"));
            sDate = doForm.doRS.GetFieldVal("DTT_PM1", 1).ToString();
            // Loop through date fields
            for (int i = 2; i <= 4; i++)
            {
                sTempDate = doForm.doRS.GetFieldVal("DTT_PM" + i, 1).ToString();
                if (sDate != "" && sTempDate != "" && Convert.ToDateTime(sDate) < Convert.ToDateTime(sTempDate))
                    doForm.doRS.SetFieldVal("DTT_PMSummary", sDate, 1);
                else
                    doForm.doRS.SetFieldVal("DTT_PMSummary", sTempDate, 1);
            }

            doForm.doRS.SetFieldVal("LNK_RELATED_PD", doForm.doRS.GetFieldVal("LNK_RELATED_MO%%LNK_OF_PD"));
            doForm.doRS.SetFieldVal("LNK_PARTNUMBER_VE", doForm.doRS.GetFieldVal("LNK_RELATED_MO%%LNK_RELATED_VE"));

            return true;
        }
        public bool AM_FormControlOnChange_BTN_SEARCHMO_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // goScr.RunScript("AM_FilterModels", doForm);
            scriptManager.RunScript("AM_FilterModels", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);


            return true;
        }
        public bool AM_FormControlOnChange_LNK_PARTNUMBER_VE_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // VS 02232015 TKT#366 : Added to Filter Models based on Vendor
            //goScr.RunScript("AM_FilterModels", doForm);
            // End If
            scriptManager.RunScript("AM_FilterModels", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            return true;
        }
        public bool AM_FilterModels_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS 02232015 TKT#366 : Create a filter/sort statement

            Form doForm = (Form)par_doCallingObject;
            string sFilterIni = "";
            string sCondition = "";
            string sCondition1 = "";
            string sCondition2 = "";
            string sPDID = "";
            clArray oArrayVE = new clArray();
            int cCount = 0;
            DataTable oTable = new DataTable();
            string sMO;
            sMO = doForm.GetControlVal("NDB_TXT_SEARCH");

            if (sMO != "" | doForm.doRS.IsLinkEmpty("LNK_PARTNUMBER_VE") == false)
            {
                if (sMO != "")
                {
                    cCount = cCount + 1;
                    goTR.StrWrite(ref sFilterIni, "C1CONDITION", "[");
                    goTR.StrWrite(ref sFilterIni, "C1FIELDNAME", "<%TXT_DESCRIPTION%>");
                    goTR.StrWrite(ref sFilterIni, "C1VALUE1", sMO);

                    sCondition1 = "TXT_DESCRIPTION['" + sMO + "'";
                }

                if (doForm.doRS.IsLinkEmpty("LNK_PARTNUMBER_VE") == false)
                {
                    // Filter by Vendor's Products
                    // Get Related Companies
                    oArrayVE = (clArray)doForm.doRS.GetFieldVal("LNK_PARTNUMBER_VE", 2);
                    if (oArrayVE.GetDimension() > 0)
                    {
                        for (int i = 1; i <= oArrayVE.GetDimension(); i++)
                        {
                            // Only adds to filter if there is a model connected to the vendor
                            if (oArrayVE.GetItem(i) != "")
                            {
                                cCount = cCount + 1;
                                sCondition2 = sCondition2 + "LNK_Related_VE='" + oArrayVE.GetItem(i) + "'";
                                goTR.StrWrite(ref sFilterIni, "C" + cCount + "CONDITION", "0");
                                goTR.StrWrite(ref sFilterIni, "C" + cCount + "FIELDNAME", "<%LNK_RELATED_VE%>");
                                goTR.StrWrite(ref sFilterIni, "C" + cCount + "VALUE1", "" + oArrayVE.GetItem(i) + "");
                            }

                            if (oArrayVE.GetDimension() > i)
                            {
                                goTR.StrWrite(ref sFilterIni, "C" + cCount + "KEYWORD", "OR");
                                sCondition2 = sCondition2 + " OR ";
                            }
                            if (i == 1)
                            {
                                sCondition2 = " ( " + sCondition2;
                                goTR.StrWrite(ref sFilterIni, "C" + cCount + "PARENBEFORE", "(");
                            }
                            if (i == oArrayVE.GetDimension())
                            {
                                sCondition2 = sCondition2 + " ) ";
                                goTR.StrWrite(ref sFilterIni, "C" + cCount + "PARENAFTER", ")");
                            }
                        }
                    }
                }

                // Build Final Condition
                if (sCondition1 != "")
                {
                    sCondition = sCondition1;
                    if (sCondition2 != "")
                    {
                        sCondition = sCondition + " AND " + sCondition2;
                        goTR.StrWrite(ref sFilterIni, "C1KEYWORD", "AND");
                    }
                }
                else if (sCondition2 != "")
                    sCondition = sCondition2;
            }

            if (cCount == 0)
            {
                // just active filter
                cCount = cCount + 1;
                sCondition = sCondition + "CHK_ACTIVEFIELD = 1";
                goTR.StrWrite(ref sFilterIni, "C" + cCount + "CONDITION", "1");
                goTR.StrWrite(ref sFilterIni, "C" + cCount + "FIELDNAME", "<%CHK_ACTIVEFIELD%>");
            }
            else
            {
                // add active filter
                cCount = cCount + 1;
                sCondition = sCondition + " AND ";
                goTR.StrWrite(ref sFilterIni, "C" + cCount + "KEYWORD", "AND");
                sCondition = sCondition + "CHK_ACTIVEFIELD = 1";
                goTR.StrWrite(ref sFilterIni, "C" + cCount + "CONDITION", "1");
                goTR.StrWrite(ref sFilterIni, "C" + cCount + "FIELDNAME", "<%CHK_ACTIVEFIELD%>");
            }

            goTR.StrWrite(ref sFilterIni, "CONDITION", sCondition);
            goTR.StrWrite(ref sFilterIni, "ACTIVE", "1");
            goTR.StrWrite(ref sFilterIni, "CCOUNT", cCount);
            goTR.StrWrite(ref sFilterIni, "SORT", "SYS_NAME ASC");
            goTR.StrWrite(ref sFilterIni, "SORT1", "SYS_NAME");
            goTR.StrWrite(ref sFilterIni, "FILE", "MO");
            goTR.StrWrite(ref sFilterIni, "TABLENAME", "MO");
            goTR.StrWrite(ref sFilterIni, "DIRECTION1", "1");
            goTR.StrWrite(ref sFilterIni, "SHOWTOP", "10");
            goTR.StrWrite(ref sFilterIni, "TOP", "10");
            goTR.StrWrite(ref sFilterIni, "US_NAME", "Linkbox selector filter");

            doForm.SetFilterINI("LNK_RELATED_MO", sFilterIni);

            return true;
        }
        public bool AutoAlertEveryDay_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter in all rowsets to get all users' private recs
            // MI 10/19/07 started converting the script to be aware of the start of the day depending on the user's time zone
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DETAILS, true);

            Form doForm = (Form)par_doCallingObject;


            // CS 10/20/2014: Prevent main overdue opps script from running and replace here.
            goTR.StrWrite(ref par_sSections, "AlertOverdueOpps", "0");
            goTR.StrWrite(ref par_sSections, "AlertOverdueQuotes", "0");

            // *** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
            // It is unlikely that there would be non-shared User records, but just in case...
            clRowSet rsUsers = new clRowSet("US", clC.SELL_READONLY, "CHK_ACTIVEFIELD=1", "SYS_Name", "GID_ID", -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
            // goLog.Log(sProc, "Users count: " & rsUsers.Count)
            // No active users
            if (rsUsers.Count() == 0)
                return true;

            // PURPOSE:
            // TLD 9/15/2014 Asset Management Alert
            // CS 10/20/2014 Opps to Review alert modified
            // Every day display alerts about things that require review or followup,
            // update Appt dates (reschedule)/To Do due dates.
            // The alerts are typically set up to open an appropriate desktop.
            // This replaces 'alarm' agents in Selltis 1.0. The name of the agent 
            // is on the top of each section of code.
            // MI 10/19/07: Making the script aware of ea user's time zone so that
            // the alerts, auto-updates, rescheduling, etc. occurs after midnight
            // user's time and not server's time. See additional notes in the code.
            // HOW THIS WORKS:
            // This script runs hourly by an automator AGE_2005072616363748192MAR 00002XX
            // that has the following definition:
            // A_01_EXECUTE = AutoAlertEveryDay
            // A_01_OBJSHARED = 1
            // A_01_TYPE = RUNSCRIPT
            // A_ORDER=1,
            // ACTIONS=1,
            // ACTIVE = 1
            // E_TM_HOUR = 0
            // E_TM_INTERVAL = 2
            // E_TM_MINUTE = 1
            // E_TM_MISSED = 1
            // EVENT=TIMER
            // US_NAME=AutoAlertEveryDay
            // US_PURPOSE=Add alerts every morning for followup of leads, contacts, to dos, etc.
            // SORTVALUE1=TIMER_ACTIVE
            // Make sure the timer automator engine is running on this site's web server.
            // WARNING:
            // Tread carefully - this is a very sensitive area.

            clRowSet doRS;
            string sResult;
            // Dim sStartDate As String
            // Dim sDueDate As String
            bool bMore = true;
            string sCurrentUser = "";
            // Dim dtDate As Date
            // Dim lDays As Long
            string sPointers;
            string sPointerDateTime;
            DateTime dtPointerDate;
            PublicDomain.TzTimeZone zone;
            DateTime dtUsersNow;       // User time zone 'now'
            DateTime dtUsersToday;     // User time zone Today (now.Date())
            string sDateTime;
            DateTime dtDateTime;
            // Dim sStartTime As String
            // Dim sEndTime As String
            string sBlankDate = clC.SELL_BLANK_SYSDATETIME;

            // Get a page with date stamps of last dates when daily timer script was processed for ea user. Ex:
            // USER1GID_ID=2008-03-25
            // USER2GID_ID=2008-03-22
            // ...
            // where USERnGID_ID is a Selltis global ID (SUID).
            sPointers = goMeta.PageRead("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED");


            // --------------------- Non-user-dependent updates -----------------------


            // ---------------------- Per-user processing loop ------------------
            while (bMore == true)
            {

                // The timer automator fires this script every hour.
                // Here we make sure the actual update is made only once in a day for each user.
                // This is very important because we are modifying data (changing Appointment 
                // date field values, for example) for the whole day. This should not be done 
                // piece meal or it will be confusing to users.

                // A 'day' is seen from the perspective of the currently processed user's
                // current time zone. If the user switches to a different time zone within 
                // the same day, the processing should not occur again until the next day 
                // starts in that time zone. As a consequence, for users traveling west 
                // this script may not fire for up to an extra day. Users traveling east 
                // will have the processing occur in less than 24 hours from the last time it ran.

                // Read user's 'last processed' date (recorded in user's time zone at the time)
                sCurrentUser = rsUsers.GetFieldVal("GID_ID", clC.SELL_FRIENDLY).ToString();
                // CS REMOVE 1/6/12
                // goLog.Log(sProc, "Current user: " & rsUsers.GetFieldVal("SYS_NAME"))

                // DEBUG 
                // goLog.Log(sProc, "  Processing user '" & goData.GetRecordNameByID(sCurrentUser) & "' [" & sCurrentUser & "]", clC.SELL_LOGLEVEL_DETAILS)
                // END DEBUG

                // Get user's time zone
                zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
                // Get user's 'now' and 'today at midnight' in the user's current time zone
                dtUsersNow = zone.ToLocalTime(goTR.NowUTC());   // User's current datetime in his/her time zone
                dtUsersToday = dtUsersNow.Date;                  // Midnight on user's current date in his/her time zone
                                                                 // Get the 'last processed' date (no time) as a local datetime
                                                                 // Pointers are written as local datetimes in user's last login time zone.
                sPointerDateTime = Microsoft.VisualBasic.Strings.Left(goTR.StrRead(sPointers, sCurrentUser, "", false), 10);
                if (sPointerDateTime == "")
                    // Leaving one whole day ahead of 'blank' datetime: '1753-01-02 23:59:59.000'.
                    dtPointerDate = goTR.StringToDate("1753-01-04", "", ref par_iValid);
                else
                    dtPointerDate = goTR.StringToDate(sPointerDateTime, clC.SELL_FORMAT_DATEDEF, ref par_iValid).Date;
                dtPointerDate = DateTime.SpecifyKind(dtPointerDate, DateTimeKind.Local);
                // dtPointerDate = goTR.AddDay(dtPointerDate, 1) 'Testing

                // *** MI 10/23/07 Replaced >= with just = to avoid not processing for days or months in case 
                // the server clock was accidentally set ahead and the processing occurred... In that case, 
                // pointer datetimes set far in the future would preclude running this.
                // If dtPointerDate >= dtUsersToday Then GoTo ProcessNextUser

                // DEBUG
                // goLog.Log(sProc, "    Pointer date: '" & goTR.DateTimeToSysString(dtPointerDate) & "' User's date: '" & goTR.DateTimeToSysString(dtUsersToday) & "'", clC.SELL_LOGLEVEL_DETAILS)
                // END DEBUG

                if (dtPointerDate == dtUsersToday)
                    goto ProcessNextUser;

                // --------- Set 'today' -------------
                // sDateTime replaces the Selltis keyword 'Today' with the datetime value that corresponds to midnight
                // in the currently processed user's time zone
                dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid/* Conversion error: Set to default value for this argument */, ref sDelim);

                // --------- Set 'tomorrow' -----------
                // Set user's 'tomorrow at midnight' as local datetime for filtering
                dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));
                dtDateTime = goTR.AddDay(dtDateTime, 1);
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid/* Conversion error: Set to default value for this argument */, ref sDelim);

                // ---------- Asset Management to Review -----------
                // AM:ReviewOverdueAlarm
                // sDateTime: tomorrow
                doRS = new clRowSet("AM", 3, "LNK_AssignedTo_US='" + sCurrentUser + "' AND DTT_PMSummary<>'" + sBlankDate + "' AND DTT_PMSummary<'" + sDateTime + "'", null/* Conversion error: Set to default value for this argument */, "GID_ID", 1, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
                if (doRS.GetFirst() == 1)
                    sResult = goUI.AddAlert("Overdue Asset Mgmt", "OPENDESKTOP", "DSK_4A359065-B65C-470A-5858-A3A4010DEA45", sCurrentUser, "a.gif").ToString();
                doRS = null/* TODO Change to default(_) if this is not a reference type */;


                // Both Opps and Quote use the Qt file and have a stage of either Opp or Quote. Make alerts based on that.
                // Overdue Opps based on QT.Next Action Date and stage... and open Opps - My to Review desktop.
                doRS = new clRowSet("QT", 3, "CHK_OPEN=1 AND DTT_NEXTACTIONDATE<'" + sDateTime + "' AND (LNK_CreditedTo_US='" + sCurrentUser + "' OR LNK_Peer_US='" + sCurrentUser + "') AND MLS_STAGE=1", null/* Conversion error: Set to default value for this argument */, "GID_ID", 1, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
                if (doRS.GetFirst() == 1)
                    sResult = goUI.AddAlert("Overdue Opps", "OPENDESKTOP", "DSK_591C6667-C02A-4E45-5858-A3C6013737EA", sCurrentUser, "OPP16.gif").ToString();

                doRS = null/* TODO Change to default(_) if this is not a reference type */;

                // Overdue Quotes have a stage of Quote
                doRS = new clRowSet("QT", 3, "CHK_OPEN=1 AND DTT_NEXTACTIONDATE<'" + sDateTime + "' AND (LNK_CreditedTo_US='" + sCurrentUser + "' OR LNK_Peer_US='" + sCurrentUser + "') AND MLS_STAGE=2", null/* Conversion error: Set to default value for this argument */, "GID_ID", 1, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
                if (doRS.GetFirst() == 1)
                    sResult = goUI.AddAlert("Overdue Quotes", "OPENDESKTOP", "DSK_2004060109460200235C_S 00016XX", sCurrentUser, "QUOTE16.gif").ToString();

                doRS = null/* TODO Change to default(_) if this is not a reference type */;





                ProcessNextUser:
                ;
                if (rsUsers.GetNext() == 0)
                    bMore = false;
            }

            rsUsers = null/* TODO Change to default(_) if this is not a reference type */;

            return true;
        }
        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doform = (Form)par_doCallingObject;

            // SKO 02052015 Ticket310: Assigned to User is Mandatory when type is Service Call
            if (Convert.ToString(doform.doRS.GetFieldVal("MLS_TYPE", 2)) == "16")
            {
                if (doform.doRS.GetLinkCount("LNK_ASSIGNEDTO_US") < 1)
                {
                    doform.MoveToField("LNK_ASSIGNEDTO_US");
                    goErr.SetWarning(30029, sProc, "", doform.GetFieldLabel("LNK_ASSIGNEDTO_US"), "", "", "", "", "", "", "", "", "LNK_ASSIGNEDTO_US");
                    return false;
                }
                if (Convert.ToString(doform.doRS.GetFieldVal("DTE_DUEDATE", 1)) == "")
                {
                    doform.MoveToField("DTE_DUEDATE");
                    goErr.SetWarning(30029, sProc, "", doform.GetFieldLabel("DTE_DUEDATE"), "", "", "", "", "", "", "", "", "DTE_DUEDATE");
                    return false;
                }
            }

            // SKO 02052015 Ticket310: NEXTACTIONDATE and DUEDATE sre Mandatory when Status is open.
            if (Convert.ToString(doform.doRS.GetFieldVal("MLS_STATUS", 2)) == " 0")
            {
                if (Convert.ToString(doform.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1)) == "")
                {
                    doform.MoveToField("DTE_NEXTACTIONDATE");
                    goErr.SetWarning(30029, sProc, "", doform.GetFieldLabel("DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE");
                    return false;
                }
            }

            // SKO 02052015 Ticket310:If ServiceCallAppointment was checked Create linked new Appointment
            if (Convert.ToString(doform.doRS.GetFieldVal("chk_ServiceCallAppointment", 2)) == "1")
            {
                Form doFormAC = new Form("AP", doform.doRS.GetFieldVal("GID_ID").ToString(), "CRL_AP");
                doFormAC.doRS.SetFieldVal("LNK_RELATED_CO", doform.doRS.GetFieldVal("LNK_RELATED_CO"));
                doFormAC.doRS.SetFieldVal("LNK_WITH_CN", doform.doRS.GetFieldVal("LNK_RELATED_CN"));
                doFormAC.doRS.SetFieldVal("LNK_RELATED_OP", doform.doRS.GetFieldVal("LNK_RELATED_OP"));
                doFormAC.doRS.SetFieldVal("LNK_RELATED_QT", doform.doRS.GetFieldVal("LNK_RELATED_QT"));
                doFormAC.doRS.SetFieldVal("lnk_coordinatedby_us", doform.doRS.GetFieldVal("lnk_assignedto_us"));
                doFormAC.doRS.SetFieldVal("DTE_STARTTIME", doform.doRS.GetFieldVal("DTE_NEXTACTIONDATE"));
                doFormAC.doRS.SetFieldVal("MLS_TYPE", 8, 2); // Service Call
                goUI.Queue("FORM", doFormAC);
            }

            // Uncheck ServiceCallAppointment
            doform.doRS.SetFieldVal("chk_ServiceCallAppointment", 0, 2);


            return true;
        }
        public bool AC_FormControlOnChange_MLS_TYPE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sColor = goP.GetVar("sMandatoryFieldColor").ToString();

            // 'SKO 02052015 Ticket310: Assigned to User is Mandatory when type is Service Call
            // If doForm.doRS.GetFieldVal("MLS_TYPE", 2) = 16 Then 'Service Call
            // If doForm.doRS.GetLinkCount("LNK_ASSIGNEDTO_US") < 1 Then
            // doForm.MoveToField("LNK_ASSIGNEDTO_US")
            // goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_ASSIGNEDTO_US"), "", "", "", "", "", "", "", "", "LNK_ASSIGNEDTO_US")
            // Return False
            // End If
            // End If

            // SKO 02052015 Ticket310:Assigned to User is Mandatory when type is Service Call
            if (Convert.ToString(doForm.doRS.GetFieldVal("MLS_TYPE", 2)) == "16")
            {
                doForm.SetFieldProperty("LNK_ASSIGNEDTO_US", "LABELCOLOR", sColor);
                // SKO 02052015 Ticket310 :Move to Service Call tab when type is Service Call
                doForm.MoveToTab(13);    // Service Call
                                         // VS 02232015 TKT#366 : DTE_DUE is mandatory in all instances BUT we want it to be mandatory only when the MLS_TYPE is ‘Service Call’.
                doForm.SetFieldProperty("DTE_DUEDATE", "LABELCOLOR", sColor);
            }
            else
            {
                doForm.SetFieldProperty("LNK_ASSIGNEDTO_US", "LABELCOLOR", "black");
                // VS 02232015 TKT#366 : DTE_DUE is mandatory in all instances BUT we want it to be mandatory only when the MLS_TYPE is ‘Service Call’.
                doForm.SetFieldProperty("DTE_DUEDATE", "LABELCOLOR", "black");
            }

            // SKO 02052015 Ticket310: Generate Sequential SERVICETICKETNUMBER
            if (Convert.ToString(doForm.doRS.GetFieldVal("SR__SERVICETICKETNUMBER", 1)) == "0")
            {
                if (Convert.ToString(doForm.doRS.GetFieldVal("MLS_TYPE", 2)) == "16")
                    doForm.doRS.SetFieldVal("SR__SERVICETICKETNUMBER", AC_GenerateSequentialServiceNo());
            }


            return true;
        }
        public bool Activity_ManageControlState_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PURPOSE: Gray Out chk_ServiceCallAppointment on Creation
            Form doForm = (Form)par_doCallingObject;

            // VS 03092015 TKT#398 : CHK_ServiceCallAppointment Enable only for saved ACtivites
            if (doForm.GetMode() == "CREATION")
                doForm.SetControlState("chk_ServiceCallAppointment", 4); // Gray Out
            else
                doForm.SetControlState("chk_ServiceCallAppointment", 0);// Active

            return true;
        }
        public bool AC_FormControlOnChange_MLS_STATUS_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sColor = goP.GetVar("sMandatoryFieldColor").ToString();

            // SKO 02052015 Ticket310: NEXTACTIONDATE and DUEDATE sre Mandatory when Status is open.
            if (Convert.ToString(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == "0")
            {
                doForm.SetFieldProperty("DTE_NEXTACTIONDATE", "LABELCOLOR", sColor);
                doForm.SetFieldProperty("DTE_DUEDATE", "LABELCOLOR", sColor);
            }
            else
            {
                doForm.SetFieldProperty("DTE_NEXTACTIONDATE", "LABELCOLOR", "black");
                doForm.SetFieldProperty("DTE_DUEDATE", "LABELCOLOR", "black");
            }


            return true;
        }
        public bool AC_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doform = (Form)par_doCallingObject;

            string sColor = goP.GetVar("sMandatoryFieldColor").ToString();

            // SKO 02052015 Ticket310: If purpose=Service Call set required fields color
            if (Convert.ToString(doform.doRS.GetFieldVal("MLS_TYPE", 2)) == "16")
            {
                doform.SetFieldProperty("LNK_ASSIGNEDTO_US", "LABELCOLOR", sColor);
                // SKO 02052015 Ticket310 :Move to Service Call tab when type is Service Call
                doform.MoveToTab(13);   // Service Call
                                        // VS 02232015 TKT#366 : DTE_DUE is mandatory in all instances BUT we want it to be mandatory only when the MLS_TYPE is ‘Service Call’.
                doform.SetFieldProperty("DTE_DUEDATE", "LABELCOLOR", sColor);
            }

            // SKO 02052015 Ticket310: NEXTACTIONDATE and DUEDATE sre Mandatory when Status is open.
            if (Convert.ToString(doform.doRS.GetFieldVal("MLS_STATUS", 2)) == "0")
                doform.SetFieldProperty("DTE_NEXTACTIONDATE", "LABELCOLOR", sColor);

            // SKO 02052015 Ticket310: Generate Sequential SERVICETICKETNUMBER
            if (doform.GetMode() == "CREATION")
            {
                if (Convert.ToString(doform.doRS.GetFieldVal("SR__SERVICETICKETNUMBER", 1)) == "0")
                {
                    if (Convert.ToString(doform.doRS.GetFieldVal("MLS_TYPE", 2)) == "16")
                        doform.doRS.SetFieldVal("SR__SERVICETICKETNUMBER", AC_GenerateSequentialServiceNo());
                }
            }

            return true;
        }
        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doform = (Form)par_doCallingObject;
            // VS 02162016 TKT#959 : Set TXT_DURATIONMINUTES in HH:MM format
            doform.SetControlState("TXT_DURATIONMINUTES", 4);

            return true;
        }
        public string AC_GenerateSequentialServiceNo()
        {

            // SKO 02052015 Ticket310: Generate Sequential SERVICETICKETNUMBER
            int iServiceNo = Convert.ToInt32(goMeta.LineRead("", "OTH_ACTIVITYSEQUENTIALSERVICENO", "NEXTSERVICENO"));
            DateTime dtNow = goTR.NowLocal(); // Current local time
            int iMonth = dtNow.Month;
            string Month;
            string Year = dtNow.ToString("yy");

            if (iMonth < 10)
                Month = "0" + iMonth.ToString();
            else
                Month = iMonth.ToString();

            string iServiceNoText = iServiceNo.ToString() + "" + Month.ToString() + "" + Year.ToString();

            //goMeta.LineWrite("", "OTH_ACTIVITYSEQUENTIALSERVICENO", "NEXTSERVICENO", iServiceNo + 1);
            goMeta.LineWrite("GLOBAL", "OTH_ACTIVITYSEQUENTIALSERVICENO", "NEXTSERVICENO", iServiceNo + 1, ref par_oConnection);

            return iServiceNoText.ToString();
        }
        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/27/07 Sectionalized the script.
            // par_doCallingObject: Rowset object containing the record to be saved.
            // par_doArray: Unused.
            // par_sMode: 'CREATION' or 'MODIF'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // VS 02162016 TKT#959 : Set TXT_DURATIONMINUTES in HH:MM format
            int iMins = Convert.ToInt32(doRS.GetFieldVal("LI__DURATION", 2));
            int iHrs = Convert.ToInt32(Math.Floor(iMins / (double)60));
            string sHrs = "";
            string sMins = "";

            iMins = iMins % 60;
            if (iHrs < 10)
                sHrs = Strings.Right("00" + iHrs.ToString(), 2);
            else
                sHrs = iHrs.ToString();
            sMins = Strings.Right("00" + iMins.ToString(), 2);

            doRS.SetFieldVal("TXT_DURATIONMINUTES", sHrs + ":" + sMins);

            return true;
        }
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/4/2014 Disable Merged checkbox
            doForm.SetControlState("CHK_Merged", 4);

            return true;
        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/4/2014 Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }

            return true;
        }
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/4/2014 Merge Functionality - run at end of CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToString(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == "0")
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CN_Merge")) != "1")
                    {
                        // Don't allow merge of contact to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID"))
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CN", "MergeFail");
                        else
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CN", "Merge");
                    }
                }
            }

            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/4/2014 Disable Merged checkbox
            doForm.SetControlState("CHK_Merged", 4);

            return true;
        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/4/2014 Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }

            return true;
        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/4/2014 Merge Functionality - run at end of CO_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToString(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == "0")
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CO_Merge")) != "1")
                    {
                        // Don't allow merge of company to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID"))
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CO", "MergeFail");
                        else
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CO", "Merge");
                    }
                }
            }

            return true;
        }
        public bool GenerateSysName_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            // PURPOSE:
            // Send back via the par_oReturn parameter the 'User Friendly' Name of the current record in par_oRowset.
            // This is to be called from each RecordOnSave script, but can be called
            // from any other code to generate a SYS_Name value. Do NOT set the name in 
            // the par_doCallingObject rowset or the script won't be usable simply for evaluating the returned
            // string.
            // IMPORTANT: Keep this "in sync" with clScripts.GetDefaultSort(), which is called from
            // clData.GetDefaultSort.
            // PARAMETERS:
            // par_doCallingObject: rowset object (for example, 'doRS'). The rowset must contain
            // all links and fields being referenced in the code below or error 45163 will be
            // raised. This can be achieved by putting '**' in the FIELDS parameter of the rowset, but 
            // avoid this when possible for performance reasons. The object, declared ByRef to conserve
            // resources by avoiding duplicating the object in memory, should not be altered directly
            // by this method (the purpose of the method is to return the name, not set it), but check
            // the code below to be sure.
            // par_doArray: not used
            // par_s1 - 5: not used
            // par_oReturn: String containing the generated SysName.
            // RETURNS:
            // True as a result. Returns friendly name or an empty string if the
            // filename is invalid via par_oReturn parameter.
            // EXAMPLE:
            // 'From a RecordOnSave script (not tested):
            // Dim sName as string = goScr.RunScript("GenerateSysName", doRS)
            // NOTES:
            // When a "Name" that is built with this method
            // is displayed in a View or linkbox and the same Name field is used
            // to sort the View or linkbox, at least the first field should match
            // the first field defined in clData::LKGetSortValue(). Otherwise what's
            // displayed will appear to be sorted arbitrarily.
            // NOTE 2:  
            // Links will not be tested because they are loaded automatically, but 
            // currently there is a bug in clRowset. RH working on this.


            string sProc = "clScripts:GenerateSysName";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";


            // We assume that sFileName is valid. If this is a problem, test it here and SetError.

            try
            {
                switch (Strings.UCase(sFileName))
                {
                    case "AM" // Asset Management CS 10/20/2014
                   :
                        {
                            par_bRunNext = false;

                            if (!doRS.IsLoaded("TXT_SerialNo"))
                                goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_SerialNo");
                            sResult = doRS.GetFieldVal("TXT_SerialNo").ToString();
                            break;
                        }
                }

                if (!par_bRunNext)
                {
                    sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
                    sResult = goTR.Replace(sResult, Constants.vbTab, " ");
                    par_oReturn = sResult;
                }
            }

            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }
        public bool MergeRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 9/4/2014 Added for merge
            par_bRunNext = false;

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName()) + "'", null/* Conversion error: Set to default value for this argument */, "**", -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true, true, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument *//* Conversion error: Set to default value for this argument */, -1/* Conversion error: Set to default value for this argument */, "");
                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (Convert.ToString(doRSMergeTo.GetFieldVal(sField)) == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    break;
                                }

                            case "MMO":
                                {
                                    // Append
                                    if (Convert.ToString(doRSMergeTo.GetFieldVal(sField)) == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    else
                                        doRSMergeTo.SetFieldVal(sField, doRSMergeTo.GetFieldVal(sField) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + doRSMerge.GetFieldVal("SYS_Name") + " ==" + Constants.vbCrLf + doRSMerge.GetFieldVal(sField));
                                    break;
                                }

                            case "CHK":
                                {
                                    if (Convert.ToString(doRSMergeTo.GetFieldVal(sField, 2)) == "0")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }

                            case "MLS":
                                {
                                    if (Convert.ToString(doRSMergeTo.GetFieldVal(sField, 2)) == "0")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        //sLinkType =Spilt(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9));
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9).ToString());
                        if (sLinkType[4] == "NN" | sLinkType[1] == "2")
                        {
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (Convert.ToString(doRSMergeTo.GetFieldVal(aLinks.GetItem(i))) == "")
                        {
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    // Check Active if exists
                    if (goData.IsFieldValid(doRSMerge.GetFileName(), "CHK_ACTIVEFIELD") == true)
                        doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);

                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;

            return true;
        }
        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sJournal = "";
            string sWork = "";

            switch (Strings.UCase(par_s5))
            {
                case "MERGE":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // run merge script, continue save
                                    //goScr.RunScript("MergeRecord", doForm.doRS);
                                    scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                                    break;
                                }

                            case "NO":
                                {
                                    // Clear merged to co linkbox, continue save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    break;
                                }

                            case "CANCEL":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "MERGEFAIL":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }
            }

            return true;
        }
        public bool QL_FormControlOnChange_BTN_SEARCHMO_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // goScr.RunScript("QL_FilterModels", doForm);
            scriptManager.RunScript("QL_FilterModels", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            // Dim sMO As String
            // sMO = doForm.GetControlVal("NDB_TXT_SEARCH")

            // 'VS 02232015 TKT#366 : Create a filter/sort statement
            // Dim sFilterIni As String = ""
            // goTR.StrWrite(sFilterIni, "ACTIVE", 1)
            // goTR.StrWrite(sFilterIni, "CONDITION", "CHK_ACTIVEFIELD=1 AND TXT_MODELNAME['" & sMO & "'")
            // goTR.StrWrite(sFilterIni, "C1CONDITION", "1")
            // goTR.StrWrite(sFilterIni, "C1FIELDNAME", "<%CHK_ACTIVEFIELD%>")
            // goTR.StrWrite(sFilterIni, "C2CONDITION", "[")
            // goTR.StrWrite(sFilterIni, "C2FIELDNAME", "<%TXT_MODELNAME%>")
            // goTR.StrWrite(sFilterIni, "C2VALUE1", sMO)
            // goTR.StrWrite(sFilterIni, "CCOUNT", "2")
            // goTR.StrWrite(sFilterIni, "FILE", "MO")
            // goTR.StrWrite(sFilterIni, "SORT", "SYS_NAME ASC")
            // goTR.StrWrite(sFilterIni, "SORT1", "SYS_NAME")
            // goTR.StrWrite(sFilterIni, "DIRECTION1", "1")
            // goTR.StrWrite(sFilterIni, "TABLENAME", "MO")

            // doForm.SetFilterINI("LNK_FOR_MO", sFilterIni)

            return true;
        }
        public bool QL_FormControlOnChange_LNK_PARTNUMBER_VE_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/3/2011 Activefield added, so always call script
            // TLD 1/31/2011 Calls script to filter LNK_FOR_PD
            // If doForm.doRS.GetLinkCount("LNK_RELATED_VE") <> 0 Then
            // goScr.RunScript("QL_FilterProducts", doForm)
            // VS 02232015 TKT#366 : Added to Filter Models based on Vendor
            // goScr.RunScript("QL_FilterModels", doForm);
            scriptManager.RunScript("QL_FilterModels", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            // End If

            return true;
        }
        public bool QL_FilterModels_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS 02232015 TKT#366 : Create a filter/sort statement

            Form doForm = (Form)par_doCallingObject;
            string sFilterIni = "";
            string sCondition = "";
            string sCondition1 = "";
            string sCondition2 = "";
            string sPDID = "";
            clArray oArrayVE = new clArray();
            int cCount = 0;

            string sMO;
            sMO = doForm.GetControlVal("NDB_TXT_SEARCH");

            if (sMO != "" | doForm.doRS.IsLinkEmpty("LNK_PARTNUMBER_VE") == false)
            {
                if (sMO != "")
                {
                    cCount = cCount + 1;
                    goTR.StrWrite(ref sFilterIni, "C1CONDITION", "[");
                    goTR.StrWrite(ref sFilterIni, "C1FIELDNAME", "<%TXT_DESCRIPTION%>");
                    goTR.StrWrite(ref sFilterIni, "C1VALUE1", sMO);

                    sCondition1 = "TXT_DESCRIPTION['" + sMO + "'";
                }

                if (doForm.doRS.IsLinkEmpty("LNK_PARTNUMBER_VE") == false)
                {
                    // Filter by Vendor's Products
                    // Get Related Companies
                    oArrayVE = (clArray)doForm.doRS.GetFieldVal("LNK_PARTNUMBER_VE", 2);
                    if (oArrayVE.GetDimension() > 0)
                    {
                        for (int i = 1; i <= oArrayVE.GetDimension(); i++)
                        {
                            // Only adds to filter if there is a model connected to the vendor
                            if (oArrayVE.GetItem(i) != "")
                            {
                                cCount = cCount + 1;
                                sCondition2 = sCondition2 + "LNK_Related_VE='" + oArrayVE.GetItem(i) + "'";
                                goTR.StrWrite(ref sFilterIni, "C" + cCount + "CONDITION", "0");
                                goTR.StrWrite(ref sFilterIni, "C" + cCount + "FIELDNAME", "<%LNK_RELATED_VE%>");
                                goTR.StrWrite(ref sFilterIni, "C" + cCount + "VALUE1", "" + oArrayVE.GetItem(i) + "");
                            }

                            if (oArrayVE.GetDimension() > i)
                            {
                                goTR.StrWrite(ref sFilterIni, "C" + cCount + "KEYWORD", "OR");
                                sCondition2 = sCondition2 + " OR ";
                            }
                            if (i == 1)
                            {
                                sCondition2 = " ( " + sCondition2;
                                goTR.StrWrite(ref sFilterIni, "C" + cCount + "PARENBEFORE", "(");
                            }
                            if (i == oArrayVE.GetDimension())
                            {
                                sCondition2 = sCondition2 + " ) ";
                                goTR.StrWrite(ref sFilterIni, "C" + cCount + "PARENAFTER", ")");
                            }
                        }
                    }
                }

                // Build Final Condition
                if (sCondition1 != "")
                {
                    sCondition = sCondition1;
                    if (sCondition2 != "")
                    {
                        sCondition = sCondition + " AND " + sCondition2;
                        goTR.StrWrite(ref sFilterIni, "C1KEYWORD", "AND");
                    }
                }
                else if (sCondition2 != "")
                    sCondition = sCondition2;
            }

            if (cCount == 0)
            {
                // just active filter
                cCount = cCount + 1;
                sCondition = sCondition + "CHK_ACTIVEFIELD = 1";
                goTR.StrWrite(ref sFilterIni, "C" + cCount + "CONDITION", "1");
                goTR.StrWrite(ref sFilterIni, "C" + cCount + "FIELDNAME", "<%CHK_ACTIVEFIELD%>");
            }
            else
            {
                // add active filter
                cCount = cCount + 1;
                sCondition = sCondition + " AND ";
                goTR.StrWrite(ref sFilterIni, "C" + cCount + "KEYWORD", "AND");
                sCondition = sCondition + "CHK_ACTIVEFIELD = 1";
                goTR.StrWrite(ref sFilterIni, "C" + cCount + "CONDITION", "1");
                goTR.StrWrite(ref sFilterIni, "C" + cCount + "FIELDNAME", "<%CHK_ACTIVEFIELD%>");
            }

            goTR.StrWrite(ref sFilterIni, "CONDITION", sCondition);
            goTR.StrWrite(ref sFilterIni, "ACTIVE", "1");
            goTR.StrWrite(ref sFilterIni, "CCOUNT", cCount);
            goTR.StrWrite(ref sFilterIni, "SORT", "SYS_NAME ASC");
            goTR.StrWrite(ref sFilterIni, "SORT1", "SYS_NAME");
            goTR.StrWrite(ref sFilterIni, "FILE", "MO");
            goTR.StrWrite(ref sFilterIni, "TABLENAME", "MO");
            goTR.StrWrite(ref sFilterIni, "DIRECTION1", "1");
            goTR.StrWrite(ref sFilterIni, "SHOWTOP", "10");
            goTR.StrWrite(ref sFilterIni, "TOP", "10");
            goTR.StrWrite(ref sFilterIni, "US_NAME", "Linkbox selector filter");

            doForm.SetFilterINI("LNK_FOR_MO", sFilterIni);

            return true;
        }
        public bool Quote_CalcOppValue(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // CS 10/7/14: Added calculating Opp value (Opp tab)

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            decimal cUnitValue = Convert.ToDecimal(doRS.GetFieldVal("CUR_UNITPROJECTVALUE", 2));
            double rOppQty = Convert.ToDouble(doRS.GetFieldVal("SR__OppQty", 2));
            decimal cOppValue;

            try
            {
                if (!goTR.IsNumber(cUnitValue.ToString()))
                    cUnitValue = 0;
                if (!goTR.IsNumber(rOppQty.ToString()))
                    rOppQty = 0;
                // Calculate Opp value
                cOppValue = cUnitValue * Convert.ToDecimal(rOppQty);
                doRS.SetFieldVal("CUR_OPPVALUE", cOppValue, 2);
            }

            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool QT_FormControlOnChange_MLS_STAGE_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sColor = goP.GetVar("sMandatoryFieldColor").ToString();

            try
            {
                // CS 10/13/2014: On change of Stage to 'Quote', move the user to the Lines tab.
                if (Convert.ToString(doForm.doRS.GetFieldVal("MLS_STAGE", 2)) == "2")
                {
                    doForm.MoveToTab(1);
                    // CS 10/20/2014: Update the Valid Until date and Expected Close Date from POP if blank.
                    if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_VALIDUNTILDATE", 1).ToString()) | Convert.ToString(doForm.doRS.GetFieldVal("DTE_VALIDUNTILDATE", 1)) == "")
                        doForm.doRS.SetFieldVal("DTE_ValidUntilDate", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTEVALID") + " days from today");

                    if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1).ToString()) | Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1)) == "")
                        doForm.doRS.SetFieldVal("DTE_ExpCloseDate", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTEEXPCLOSEDATE"));
                }

                // CS 10/20/2014 Set Valid Until date as mandatory field color if Stage is not Opp.
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Stage", 2)) != 1)
                    doForm.SetFieldProperty("DTE_ValidUntilDate", "LABELCOLOR", sColor);
                else
                    doForm.SetFieldProperty("DTE_VALIDUNTILDATE", "LABELCOLOR", "black");
            }



            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool QT_FormControlOnChange_LNK_CREDITEDTO_US_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // VS 12022014 Ticket# 106: Save & Create Alternate
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.


            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            // IF gbWriteLog THEN oLog is clLogObj(sProc, "Start", 3)

            Form doForm = (Form)par_doCallingObject;

            // VS 02242015 TKT#366 : Custom Copy Dates from Original Quote to Duplicated Quote

            clRowSet doSIGNATURERS = new clRowSet("MD", 3, "TXT_PAGE='POP_PERSONAL_OPTIONS' AND TXT_PROPERTY='CORRSIGNATURE' AND GID_SECTION='" + doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_VALUE", -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
            clRowSet doABOVESIGNATURERS = new clRowSet("MD", 3, "TXT_PAGE='POP_PERSONAL_OPTIONS' AND TXT_PROPERTY='CORRABOVESIGNATURE' AND GID_SECTION='" + doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_VALUE", -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
            clRowSet doBELOWSIGNATURERS = new clRowSet("MD", 3, "TXT_PAGE='POP_PERSONAL_OPTIONS' AND TXT_PROPERTY='CORRBELOWSIGNATURE' AND GID_SECTION='" + doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_VALUE", -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
            if (doSIGNATURERS.GetFirst() == 1)
                doForm.doRS.SetFieldVal("TXT_Signature", doSIGNATURERS.GetFieldVal("TXT_VALUE"));
            else
                doForm.doRS.SetFieldVal("TXT_Signature", "");
            if (doABOVESIGNATURERS.GetFirst() == 1)
                doForm.doRS.SetFieldVal("TXT_ABOVESIGNATURE", doABOVESIGNATURERS.GetFieldVal("TXT_VALUE"));
            else
                doForm.doRS.SetFieldVal("TXT_ABOVESIGNATURE", "");
            if (doBELOWSIGNATURERS.GetFirst() == 1)
                doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", goTR.Replace(doBELOWSIGNATURERS.GetFieldVal("TXT_VALUE").ToString(), (Strings.Chr(1) + Strings.Chr(1)).ToString(), Constants.vbCrLf.ToString()));
            else
                doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", "");

            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sColor = goP.GetVar("sMandatoryFieldColor").ToString();

            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }

            // TLD 9/29/2014 Open to Opportunity tab when stage is Opportunity
            if (Convert.ToString(doForm.doRS.GetFieldVal("MLS_Stage", 2)) == "1")
                doForm.MoveToTab(16);

            // CS 10/20/2014 Set Valid Until date as mandatory if Stage is not Opp.
            if (Convert.ToString(doForm.doRS.GetFieldVal("MLS_Stage", 2)) != "1")
                doForm.SetFieldProperty("DTE_ValidUntilDate", "LABELCOLOR", sColor);
            else
                doForm.SetFieldProperty("DTE_VALIDUNTILDATE", "LABELCOLOR", "black");

            // CS 10/20/2014 In creation mode do NOT fill Valid Until date from POP. This is filled in clscripts so need to
            // blank out that value here.
            if (Convert.ToString(doForm.doRS.GetFieldVal("MLS_STAGE", 2)) == " 1")
                doForm.doRS.SetFieldVal("DTE_VALIDUNTILDATE", "");

            // SGR 01/12/2014: Ticket No.120
            // Storing status value.
            doForm.oVar.SetVar("lMlsStatusonload", doForm.doRS.GetFieldVal("MLS_STATUS"));

            // SGR:01/12/2014'
            // Setting Default Value
            if (doForm.GetMode() == "CREATION")
            {
                doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 8.25, 2);
                // e768de8b-a36a-45ac-5354-a3ed0119b5aa
                doForm.doRS.SetFieldVal("LNK_RELATED_ST", "e768de8b-a36a-45ac-5354-a3ed0119b5aa", 2);
                doForm.SetControlState("CUR_ShippingTax", 4);

                // SGR 01/23/2015: Ticket No.273
                string sAdditionalInfo = "Thank you for the opportunity to provide pricing. Should you have any questions or comments regarding this quote, please feel free to contact our office at 281-938-1800.";
                doForm.doRS.SetFieldVal("MMO_AdditionalInfo", sAdditionalInfo, 2);
            }

            // VS 01272015 TKT#280 : New Field CUR_TOTALTAX
            doForm.SetControlState("CUR_TOTALTAX", 4); // Gray Out
            double ShippingTax = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_SHIPPINGTAX",2));
            double SalesTax = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_SALESTAX",2));
           


            doForm.doRS.SetFieldVal("CUR_TOTALTAX", ShippingTax + SalesTax);

            // VS 02252015 TKT#366 : When duplicating a Quote, link the DTE_NEXTACTIONDATE, DTE_EXPCLOSEDATE, DTE_VALIDUNTILDATE to the respective dates from the original Quote.
            // Get Dates from Variables set in AutoQuoteDuplicate_Pre
            if (Convert.ToString(doForm.oVar.GetVar("QuoteOpeningMode")) == "Duplicate")
            {
                doForm.doRS.SetFieldVal("DTE_NEXTACTIONDATE", doForm.oVar.GetVar("lDTE_NEXTACTIONDATE"));
                doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.oVar.GetVar("lDTE_EXPCLOSEDATE"));
                doForm.doRS.SetFieldVal("DTE_VALIDUNTILDATE", doForm.oVar.GetVar("lDTE_VALIDUNTILDATE"));
            }

            // VS 02252015 TKT#366 : Copy Signature Fields from LNK_CREDITEDTO_US instead of MEID
            clRowSet doSIGNATURERS = new clRowSet("MD", 3, "TXT_PAGE='POP_PERSONAL_OPTIONS' AND TXT_PROPERTY='CORRSIGNATURE' AND GID_SECTION='" + doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_VALUE", -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
            clRowSet doABOVESIGNATURERS = new clRowSet("MD", 3, "TXT_PAGE='POP_PERSONAL_OPTIONS' AND TXT_PROPERTY='CORRABOVESIGNATURE' AND GID_SECTION='" + doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_VALUE", -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
            clRowSet doBELOWSIGNATURERS = new clRowSet("MD", 3, "TXT_PAGE='POP_PERSONAL_OPTIONS' AND TXT_PROPERTY='CORRBELOWSIGNATURE' AND GID_SECTION='" + doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_VALUE", -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, false/* Conversion error: Set to default value for this argument */, true);
            if (doSIGNATURERS.GetFirst() == 1)
            {
                doForm.doRS.SetFieldVal("TXT_Signature", doSIGNATURERS.GetFieldVal("TXT_VALUE"));
            }
            else
            {
                doForm.doRS.SetFieldVal("TXT_Signature", "");
            }
            if (doABOVESIGNATURERS.GetFirst() == 1)
            {
                doForm.doRS.SetFieldVal("TXT_ABOVESIGNATURE", doABOVESIGNATURERS.GetFieldVal("TXT_VALUE"));
            }
            else
            {
                doForm.doRS.SetFieldVal("TXT_ABOVESIGNATURE", "");
            }
            if (doBELOWSIGNATURERS.GetFirst() == 1)
            {
                doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", goTR.Replace(doBELOWSIGNATURERS.GetFieldVal("TXT_VALUE").ToString(), (Strings.Chr(1) + Strings.Chr(1)).ToString(), Constants.vbCrLf.ToString()));
            }
            else
            {
                doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", "");
            }
            Refresh_QouteTotal(doForm.doRS);
            return true;
        }
        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));
            string sModelText = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_MODELNAME"));
            string sModelDesc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_DESCRIPTION"));

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);
            rsQL.SetFieldVal("TXT_MODEL", sModelText);
            rsQL.SetFieldVal("MMO_DETAILS", sModelDesc);

            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);

            //rsQL.SetFieldVal("TXT_Model", sModelText);      
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }
        public bool AutoQuoteDuplicate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // MI 4/25/07 CREATED BY MI 4/22/07
            // PURPOSE:
            // Duplicate an existing Quote and its line items allowing the user to connect a different
            // Contact, Company, etc.

            // VS 02242015 TKT#366 : Custom Copy Dates from Original Quote to Duplicated Quote

            string sID="";
            clRowSet doRowset;
            string sFileName;
            Form doF;
            string sOrigQuoteName;
            string sOrigQuoteID;

            par_bRunNext = false;

            // Check selected record
            //sID = goUI.GetLastSelected("SELECTEDRECORDID");

            string Key = "";
            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
            {
                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
            }

            if (Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
                sID = Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();

            // goP.TraceLine("sID: " & sID & "'", "", sProc)
            sFileName = goTR.GetFileFromSUID(sID).ToString().ToUpper();
            if (sFileName != "QT")
            {
                // goUI.NewWorkareaMessage("Please select a Quote first.");
                goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                return true;
            }

            // Check if have permissions
            if (goData.GetAddPermission("QT") == false)
            {
                //goUI.NewWorkareaMessage("You cannot duplicate the selected Quote because you don't have permissions to create Quotes.");
                goUI.NewWorkareaMessage("You cannot duplicate the selected Quote because you don't have permissions to create Quotes", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                return true;
            }


            // Copy the selected record
            // Get doRowset of current record
            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "*,LNK_CONNECTED_QL%%SYS_Name", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");

                return true;
            }
            else
            {
                sOrigQuoteName = doRowset.GetFieldVal("SYS_Name").ToString();
                sOrigQuoteID = doRowset.GetFieldVal("GID_ID").ToString();
            }

            // Create the new Quote form
            doF = new Form(sFileName, "", "CRU_" + sFileName);
            doF.oVar.SetVar("QuoteOpeningMode", "Duplicate");
            doF.oVar.SetVar("QuoteOrinalQuoteID", sID);
            doF.SetControlVal("NDB_MMO_Lines", doRowset.GetFieldVal("LNK_Connected_QL%%SYS_Name").ToString());
            doF.doRS.SetFieldVal("TXT_Description", doRowset.GetFieldVal("TXT_Description", 2), 2);
            doF.doRS.SetFieldVal("CUR_Subtotal", doRowset.GetFieldVal("CUR_Subtotal", 2), 2);
            doF.doRS.SetFieldVal("CUR_Total", doRowset.GetFieldVal("CUR_Total", 2), 2);
            // VS 02242015 TKT#366 : Custom Copy Dates from Original Quote to Duplicated Quote
            doF.oVar.SetVar("lDTE_NEXTACTIONDATE", doRowset.GetFieldVal("DTE_NEXTACTIONDATE"));
            doF.oVar.SetVar("lDTE_EXPCLOSEDATE", doRowset.GetFieldVal("DTE_EXPCLOSEDATE"));
            doF.oVar.SetVar("lDTE_VALIDUNTILDATE", doRowset.GetFieldVal("DTE_VALIDUNTILDATE"));
            // Set History tab & Cloned from Quote
            doF.doRS.SetFieldVal("MMO_History", "Duplicated from Quote" + sOrigQuoteName);
            doF.doRS.SetLinkVal("LNK_ClonedFrom_QT", sOrigQuoteID);

            doF.MessagePanel("This is a duplicate of the Quote '" + sOrigQuoteName + "'." + Constants.vbCrLf + "Fill out the form and click Save or click Modify Lines to add, edit or remove them.", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Info.gif");

            // 'Copy to the new rowset
            // If Not goData.CopyRecord(doRowset, doF.doRS) Then
            // goErr.SetError(35000, sProc, "Copying the selected Quote '" & sID & "' failed.")
            // Return False
            // End If
            // doF.doRS.SetFieldVal("GID_ID", goData.GenerateID(sFileName))
            // doF.doRS.ClearLinkAll("LNK_OriginatedBy_CN")
            // doF.doRS.ClearLinkAll("LNK_To_CO")

            // 'Save the new record (?)
            // If doNewRowset.Commit() = 0 Then
            // goErr.SetWarning(30200, sProc, "", "An error occurred duplicating the selected Quote.", "", "", "", "", "", "", "", "", "")
            // doRowset = Nothing
            // doNewRowset = Nothing
            // Return False
            // End If

            goUI.Queue("FORM", doF);

            // Clean up objects
            doRowset = null/* TODO Change to default(_) if this is not a reference type */;

            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sFrmMLSStatus = doForm.oVar.GetVar("lMlsStatusonload").ToString();
            string sMLSStatus = doForm.doRS.GetFieldVal("MLS_STATUS").ToString();
            string sLnkClonedFromQT = doForm.doRS.GetFieldVal("LNK_CLONEDFROM_QT").ToString();
            string sLnkClonedToQT = doForm.doRS.GetFieldVal("LNK_CLONEDTO_QT").ToString();
            if (sFrmMLSStatus != sMLSStatus & (sLnkClonedFromQT != "" | sLnkClonedToQT != ""))
            {
                doForm.MessageBox("There are connected quotes to this quote. Click on the 'Links' tab below and open/update each quote.", 0, null, null, null, null, null, "MessageBoxEvent", null, null, doForm, null, "OK", null, null, null, "QT_FormOnSave_QuoteStatus");
            }
            string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sGidId + "' And LNK_FOR_MO%%BI__ID<1 ", "LNK_IN_QT", "LNK_FOR_MO");

            if ((rsQL.GetFirst() == 1))
            {
                goErr.SetWarning(35000, sProc, "Please fill Model before saving the Quote  ");
                doForm.FieldInFocus = "LNK_FOR_MO";
                par_doCallingObject = doForm;
                return false;
            }
            return true;
        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            try
            {
                // CS 10/20/2014 Enforce Valid Until Date if stage is not Opp.
                if (Convert.ToString(doForm.doRS.GetFieldVal("MLS_STAGE", 2)) != "1")
                {
                    if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_VALIDUNTILDATE", 1).ToString()) | doForm.doRS.GetFieldVal("DTE_VALIDUNTILDATE", 1).ToString() == "")
                    {
                        doForm.MoveToTab(1);
                        doForm.MoveToField("DTE_VALIDUNTILDATE");
                        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_VALIDUNTILDATE"), "", "", "", "", "", "", "", "", "DTE_VALIDUNTILDATE");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            try
            {
                //  goScr.RunScript("Quote_CalcOppValue", doRS);
                scriptManager.RunScript("Quote_CalcOppValue", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                doRS = (clRowSet)par_doCallingObject;
            }

            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Optional: editable clRowset of the Quote header record. If passed, 
            // this rowset is updated and the record on disk is not touched. If not passed,
            // the record on disk is updated. in this case par_s1 (Quote ID) is mandatory.
            // par_doArray: Unused.
            // par_s1: GID_ID of the Quote to calculate. Mandatory even when par_doCallingObject is passed.
            // par_s2: Optional: ID of the Quote Line for which to take amounts from par_s3 and par_s4
            // instead of from disk. This is to allow recalculating the quote from a Quote Line
            // which hasn't been saved yet. If "", all quote lines are read from disk and par_s3 and par_s4
            // are ignored.
            // par_s3: Mandatory only if par_s2 <> "". CHK_TAXABLE value (1 or 0) from Quote Line (ID passed via par_s2). 
            // Use goTr.CheckboxToString(doRS.GetFieldVal("CHK_TAXABLE", 2)) to get it in this format.
            // par_s4: mandatory only is par_s2 <> "". CUR_SUBTOTAL as unformatted number from Quote Line (ID passed via par_s2).
            // Use goTr.CurrToString(doRS.GetFieldVal("CUR_SUBTOTAL", 2)) to get it in this format.
            // par_s5: Unused.

            // Old NGP parameters:
            // par_s1: ID of the Quote. If blank, all parameters are read from global variables named QUOTE_<FieldName>
            // and the Quote is saved on disk.
            // par_s2: System-format value of Quote's CHK_INCLUDETAXCHARGES as boolean: 1 (default) or 0.
            // par_s3: System-format value of Quote's SR__SALESTAXPERCENT as single real. Default is '0'.
            // par_s4: System-format value of Quote's CUR_OTHERCHARGE as currency. Default is '0'.
            // par_s5: System-format value of Quote's CUR_SHIPPING as currency. Default is '0'.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;

            // MI 2/23/07 Mods
            // PURPOSE:
            // Calculate totals of a Quote from Quote Lines independent of the form context.
            // RETURNS:
            // True if successful, False if not with SetError. Returns calculation results
            // via gop:SetVar() in the following variables:
            // CUR_SUBTOTAL
            // CUR_SUBTOTALT
            // CUR_SALESTAX
            // CUR_TOTAL	

            clRowSet doLines;  // 
            int lI;
            decimal cWork = 0;        // Non-taxable subtotals
            decimal cWorkT = 0;       // Taxable subtotals only
            double rTaxPercent;
            decimal cTaxAmount;
            decimal cOtherCharge;
            decimal cTotalAmount;
            // SGR:Ticket # 102
            decimal cShipTaxAmount = 0;
            decimal cShipAmount;
            // Dim s1 As String = par_s1   'Quote GID_ID
            // Dim s2 As String = par_s2   'Quote Line GID_ID 
            // Dim s3 As String = par_s3
            // Dim s4 As String = par_s4
            // Dim s5 As String = par_s5
            clRowSet doRS = (clRowSet)par_doCallingObject;      // Quote rowset
            bool bCommit = false;
            bool bQLTaxable = false;
            decimal cQLSubtotal = 0;
            bool bQLFound = false;
            string sFiles = ""; // FIL_INCLUSIONS from Quote Line Models
            bool bUpdInclusions = false;

            // Vars are:
            // s1 = QT GID_ID
            // s2 = QL GID_ID
            // s3 = QL CHK_TAXABLE
            // s4 = QL CUR_Subtotal

            // Vars used to be:
            // s1 = goP.GetVar("QUOTE_GID_ID")
            // s2 = goP.GetVar("QUOTE_CHK_INCLUDETAXCHARGES")
            // s3 = goP.GetVar("QUOTE_SR__SALESTAXPERCENT")
            // s4 = goP.GetVar("QUOTE_CUR_OTHERCHARGE")
            // s5 = goP.GetVar("QUOTE_CUR_SHIPPING")
            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)



            // ------------- Validate parameters ----------------
            if (!goData.IsFileValid(goTR.GetFileFromSUID(par_s1.ToString())))
            {
                goErr.SetError(10100, sProc, null/* Conversion error: Set to default value for this argument */, goTR.GetFileFromSUID(par_s1), "File extracted from SUID in par_s1: '" + par_s1 + "'. Be sure to pass the GID_ID value of the Quote to recalculate.");
                // 10100: Invalid file name '[1]'. [2]
                return false;
            }
            if (par_s2.ToString() == "")
            {
                // Override QL ID not provided - ignore the rest of QL parameters
                par_s3 = "";
                par_s4 = "";
            }
            else
            {
                // Quote Line GID_ID was passed
                // QL's CHK_Taxable value
                if (par_s3.ToString() == "")
                {
                    goErr.SetError(35000, sProc, "par_s3 is blank. QL's CHK_Taxable value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
                    return false;
                }
                else
                    bQLTaxable = goTR.StringToCheckbox(par_s3, false, ref par_iValid);
                // QL's CUR_Subtotal value
                if (par_s4.ToString() == "")
                {
                    goErr.SetError(35000, sProc, "par_s4 is blank. QL's CUR_Subtotal value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
                    return false;
                }
                else
                    cQLSubtotal = Convert.ToDecimal(par_s4);
            }


            // -------------- Read Lines and calculate their amounts ------------
            // CS 12/2/08: Check if MO.FIL_INCLUSIONS exists. If so need to get it in the QL RS below
            if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                doLines = new clRowSet("QL", 3, "LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS");
            else
                doLines = new clRowSet("QL", 3, "LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL");
            // Browse through the rowset
            lI = 1;
            if (doLines.GetFirst() == 1)
            {
                do
                {
                    // Add up Quote Lines. Skip the one for which GID_ID is passed via par_s2
                    if (par_s2.ToString() == "" | Strings.UCase(par_s2) != doLines.GetFieldVal("GID_ID", 1).ToString().ToUpper())
                    {
                        if (Convert.ToString(doLines.GetFieldVal("CHK_TAXABLE", 2)) == "1")
                            cWorkT += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));
                        else
                            cWork += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));
                        // CS 12/2/08: Get value from QL%%MO FIL_INCLUSIONS field
                        if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                        {
                            // Check if the file has already been added
                            if (goTR.Position(sFiles, Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS", 2))) == 0)
                            {
                                // If this is the first file don't add a vbcrlf in front of it
                                if (sFiles == "")
                                    sFiles = doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS").ToString();
                                else
                                    sFiles = sFiles + Constants.vbCrLf + doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS");
                            }
                        }
                    }

                    if (doLines.GetNext() == 0)
                        break;
                    lI += 1;
                }
                while (true);
            }
            // delete(doLines)
            doLines = null;

            // Add the Quote Line passed via parameters
            if (par_s2 != "")
            {
                // CS 12/31/08: Get Fil_Inclusions of QL passed via parameter
                // This code needs to be run if you open a QL directly from
                // a QL view and it has file inclusions
                // CS 1/8/09: Check if FIL_INCLUSIONS exist in db first.
                if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                {
                    doLines = new clRowSet("QL", 3, "GID_ID='" + par_s2.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS");
                    if (doLines.GetFirst() == 1)
                    {
                        // If goData.IsFieldValid("MO", "FIL_INCLUSIONS") Then
                        // Check if the file has already been added
                        if (goTR.Position(sFiles, doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS").ToString()) == 0)
                        {
                            // If this is the first file don't add a vbcrlf in front of it
                            if (sFiles == "")
                                sFiles = doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS").ToString();
                            else
                                sFiles = sFiles + Constants.vbCrLf + doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS");
                        }
                    }
                }

                if (bQLTaxable == true)
                    cWorkT += cQLSubtotal;
                else
                    cWork += cQLSubtotal;
            }

            // Subtotal = cWork + cWorkT
            // SubtotalT = cWorkT

            // ---------- Pull up the Quote -----------
            if (doRS == null)
            {
                // Get the quote from disk
                bCommit = true;
                // doRS = New clRowSet("QT", 1, _
                // "GID_ID='" & par_s1 & "'", _
                // "DTT_TIME ASC", _
                // "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL")
                // CS 7/26/07: Currently if you open a QT that has a required field missing
                // such as NA date, edit a QL and save the QL you get an error. Trying to avoid
                // that by bypassing validation.
                // VS 10272015 TKT#280 : CUR_TOTALTAX New Field Added 
                doRS = new clRowSet("QT", 1, "GID_ID='" + par_s1 + "'", "DTT_TIME ASC", "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL, CUR_TOTALTAX", -1/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, true);
            }
            else
                // Validate the passed Rowset object
                if (doRS.GetFileName().ToUpper() != "QT")
            {
                goErr.SetError(35000, sProc, "The file of the rowset in par_doCallingObject parameter is not QT (Quote). Either pass a Quote rowset or only pass the Quote GID_ID in par_s1 parameter.");
                return false;
            }

            // ----------- Read Quote data and calculate -------------
            if (doRS.GetFirst() == 1)
            {
                // CS 12/2/08: Get the value of the 'Do not update inclusions' on QT
                // If checked, do not update FIL_INCLUSIONS field on QT
                if (goData.IsFieldValid("QT", "CHK_NOUPDINCLUSIONS"))
                {
                    if (Convert.ToString(doRS.GetFieldVal("CHK_NOUPDINCLUSIONS", 2)) == " 0")
                        bUpdInclusions = true;
                }
                rTaxPercent = Convert.ToDouble(doRS.GetFieldVal("SR__SALESTAXPERCENT", clC.SELL_SYSTEM));    // s3
                cTaxAmount = cWorkT * Convert.ToDecimal(rTaxPercent) / 100;     // cTaxAmount goes into CUR_SALESTAX
                                                                                // If the 'Include Tax/Charges' check-box is not checked, do not add tax,
                                                                                // other charge and shipping to Total. 
                if (Convert.ToString(doRS.GetFieldVal("CHK_INCLUDETAXCHARGES", clC.SELL_SYSTEM)) == "1")
                {
                    cOtherCharge = Convert.ToDecimal(doRS.GetFieldVal("CUR_OTHERCHARGE", clC.SELL_SYSTEM));   // s4
                    cShipAmount = Convert.ToDecimal(doRS.GetFieldVal("CUR_SHIPPING", clC.SELL_SYSTEM));   // s5
                                                                                                          // SGR 12/10/12 Ticket No-102 
                    cShipTaxAmount = cShipAmount * Convert.ToDecimal(rTaxPercent) / 100;
                    // cTotalAmount goes to CUR_TOTAL
                    cTotalAmount = cWork + cWorkT + cTaxAmount + cOtherCharge + cShipAmount + cShipTaxAmount;
                }
                else
                    // cTotalAmount goes to CUR_TOTAL
                    cTotalAmount = cWork + cWorkT;
            }
            else
            {
                // goP.TraceLine("doRS GetFirst not found", "", sProc)
                doRS = null/* TODO Change to default(_) if this is not a reference type */;
                goErr.SetError(30032, sProc, "", "Quote");
                // The linked [1] can't be updated because it can't be found. 
                // goP.TraceLine("Return False", "", sProc)
                return false;
            }

            // --------------- Update calculated fields ----------------
            doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cWork + cWorkT), clC.SELL_SYSTEM);
            // SGR 12/10/12 Ticket No-102 
            doRS.SetFieldVal("CUR_SUBTOTALT", goTR.RoundCurr(cWorkT + cShipTaxAmount), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_SALESTAX", goTR.RoundCurr(cTaxAmount), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_TOTAL", goTR.RoundCurr(cTotalAmount), clC.SELL_SYSTEM);
            // SGR 12/10/12 Ticket No-102 
            doRS.SetFieldVal("CUR_ShippingTax", goTR.RoundCurr(cShipTaxAmount), clC.SELL_SYSTEM);
            // VS 01272015 TKT#280 : New Field CUR_TOTALTAX
            doRS.SetFieldVal("CUR_TOTALTAX", cShipTaxAmount + cTaxAmount);

            // --------------Update FIL_Inclusions
            if (bUpdInclusions == true)
                doRS.SetFieldVal("FIL_INCLUSIONS", sFiles);

            // --------------- Save the Quote ---------------
            if (bCommit)
            {
                goP.SetVar("bDoNotUpdateQuoteLines", "1");
                // CS 11/5/09: Setting a variable here to let me know NOT to try to update the Qt total again in QT_RecOnSave. Issue was that if
                // you open a QT, add a Quote Line and then cancel out of the QT, the QT total did not reflect the actual total. This
                // was because CalcQuotetotal was being called here and then again in QT_RecordOnSave. We do NOT want it to be called in QT
                // RecOnSave if it was called here.
                goP.SetVar("bDoNotRecalcQuoteTotal", "1");
                // Save to disk
                if (doRS.Commit() != 1)
                {
                    // goP.TraceLine("Commit failed, raising error", "", sProc)
                    goP.DeleteVar("bDoNotUpdateQuoteLines");
                    // CS 11/6/09: Set in Ql_RecordOnSave
                    goP.DeleteVar("bDoNotRecalcQuoteTotal");
                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                    goErr.SetError(35000, sProc, "Error updating the Quote '" + par_s1 + "'."); // CS
                                                                                                // goP.TraceLine("Return False", "", sProc)
                    return false;
                }
                else
                {
                    goP.DeleteVar("bDoNotUpdateQuoteLines");
                    goP.DeleteVar("bDoNotRecalcQuoteTotal"); // CS 11/6/09
                    if (doRS != null)
                        doRS = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

            return true;
        }
        public bool Utility_RunImportUtility(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                goUI.OpenURLExternal("../Pages/cus_diaImportMan.aspx", "Selltis", "height=840,width=1250,left=100,top=100,status=yes,location=no,toolbar=no,resizable=yes,titlebar=no,dependent=yes");
            }
            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool QL_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.
            par_bRunNext = false;
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3)

            // PORTING FROM COMMENCE IN PROGRESS. MI.

            // ******************************
            // DIFFERS FROM SHIPPING DATABASE
            // ******************************

            // --- HISTORY ---
            // 2005/08/17 10:46:47 MAR Added traces to diagnose MLS_REASONWONLOST not being pulled properly from Quote

            // goP.TraceLine("", "", sProc)

            Form doForm = (Form)par_doCallingObject;
            long lLine;
            long lHighestLine;
            string sWork;
            string sColor = goP.GetVar("sMandatoryFieldColor").ToString();

            // goP.TraceLine("GetFieldVal('LNK_IN_QT%%LNK_CREDITEDTO_US',2): '" & doForm.dors.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2) & "'", "", sProc)
            // goP.TraceLine("GetFieldVal('LNK_IN_QT%%LNK_CREDITEDTO_US',1): '" & doForm.dors.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 1) & "'", "", sProc)

            // Set mandatory field color
            // doForm.SetFieldProperty("LNK_FOR_MO", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("SR__QTY", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("CUR_COST", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("SR__LINENO", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("LNK_CreditedTo_US", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("LNK_Peer_US", "LABELCOLOR", sColor)
            doForm.SetFieldProperty("LNK_IN_QT", "LABELCOLOR", sColor);

            // Set button tooltips
            doForm.SetFieldProperties("BTN_RecalcTotals", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Recalculate Quote Line totals");
            doForm.SetFieldProperties("BTN_UseModelPrice", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Fill unit price and cost from the linked Model");
            doForm.SetFieldProperties("BTN_INSERTSPEC", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend specifications from the linked Model");

            // CS: Set status and reason fields grayed out
            // 7/3/07: Can set status independnet of Quote Status doForm.setcontrolstate("MLS_STATUS", 4)
            // 5/28/10: Allow setting reason independent of Quote status
            // doForm.SetControlState("MLS_REASONWONLOST", 4)

            // CS: 5/21/09: Gray out CUR_Cost field on the QL. The Cost will be calculated based on Model Cost * Qty
            // If a customer needs to manually enter costs, this can be customized to ungray the field
            // SGR 08172015:TKT # 671 Commented below line of code for enable edit
            // doForm.SetControlState("CUR_COST", 4)

            // If save button is disabled, disable Save and Create unlinked button
            if (doForm.SaveEnabled() == false)
                doForm.SetControlState("BTN_SAVECRU", 4);


            // Grayed fields
            doForm.SetControlState("MMO_ImportData", 4);
            doForm.SetControlState("cur_subtotal", 4);
            doForm.SetControlState("sr__salestaxpercent", 4);
            doForm.SetControlState("cur_salestax", 4);

            // locked
            doForm.SetControlState("lnk_for_pd", 1);
            doForm.SetControlState("lnk_related_dv", 1);
            doForm.SetControlState("lnk_involves_us", 1);
            doForm.SetControlState("lnk_related_ve", 1);

            // grayed button
            doForm.SetControlState("LNK_IN_QT", 5);
            doForm.SetControlState("LNK_TO_CO", 5);

            // ----------- DEFAULT VALUES ------------
            if (doForm.GetMode() == "CREATION")
            {
                if (doForm.doRS.GetLinkCount("LNK_IN_QT") < 1)
                {
                    doForm.MessageBox("You are creating a Quote Line that is not linked to a Quote." + Constants.vbCrLf + Constants.vbCrLf + "To create a new Quote Line, open an existing Quote or create a new Quote and click New on its Details tab.");
                    return true;
                }
                else
                {
                    // CS 6/22/09: Create rowset for getting all Quote values instead of all double hops below (creating multiple rowsets)
                    // SGR 01/23/2015: : Ticket # 273 :Added CHK_INCLUDELETTERHEAD
                    clRowSet doRSQT = new clRowSet("QT", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_IN_QT") + "'", null/* Conversion error: Set to default value for this argument */, "DTT_TIME,DTE_EXPCLOSEDATE,LNK_CREDITEDTO_US,LNK_PEER_US,LNK_RELATED_PR,LNK_TO_CO,SR__SALESTAXPERCENT,CHK_INCLUDELETTERHEAD,CHK_INCLUDETAXCHARGES");
                    // doForm.doRS.SetFieldVal("DTT_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTT_TIME"))
                    // doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_EXPCLOSEDATE"))
                    // doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2), 2)
                    // doForm.doRS.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_PEER_US", 2), 2)
                    // doForm.doRS.SetFieldVal("LNK_RELATED_PR", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_RELATED_PR", 2), 2)
                    // doForm.doRS.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_TO_CO", 2), 2)
                    // doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"))
                    if (doRSQT.GetFirst() == 1)
                    {
                        doForm.doRS.SetFieldVal("DTT_QTETIME", doRSQT.GetFieldVal("DTT_TIME"));
                        doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doRSQT.GetFieldVal("DTE_EXPCLOSEDATE"));
                        doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doRSQT.GetFieldVal("LNK_CREDITEDTO_US", 2), 2);
                        doForm.doRS.SetFieldVal("LNK_PEER_US", doRSQT.GetFieldVal("LNK_PEER_US", 2), 2);
                        doForm.doRS.SetFieldVal("LNK_RELATED_PR", doRSQT.GetFieldVal("LNK_RELATED_PR", 2), 2);
                        doForm.doRS.SetFieldVal("LNK_TO_CO", doRSQT.GetFieldVal("LNK_TO_CO", 2), 2);
                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", doRSQT.GetFieldVal("SR__SALESTAXPERCENT"));
                        // SGR 01/23/2015: : Ticket # 273
                        // doForm.doRS.SetFieldVal("CHK_TAXABLE", doRSQT.GetFieldVal("CHK_INCLUDELETTERHEAD"))
                        // SKO 02052015 Ticket310
                        doForm.doRS.SetFieldVal("CHK_TAXABLE", doRSQT.GetFieldVal("CHK_INCLUDETAXCHARGES"));
                    }

                    // CS 8/27/08 If WOP to manage users independent of QT is off, always gray out fields on QL
                    if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_USERMGMT")) != "1")
                    {
                        doForm.SetControlState("LNK_CREDITEDTO_US", 4);
                        doForm.SetControlState("LNK_PEER_US", 4);
                    }

                    // -------- Status, Reason Won/Lost -------
                    // ******************************
                    // In Selltis DB, Status and Reason Won/Lost are the same as in Quote
                    // goP.TraceLine("Setting MLS_STATUS from Quote: '" & doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2) & "'", "", sProc)
                    // goLog.Log("QL_FormOnLoadRecord", doForm.dors.getfieldval("LNK_IN_QT%%MLS_status", 2), , , True)
                    clArray oArray = new clArray();
                    string sVal = "";
                    clArray oArrayReason = new clArray(); // CS 5/28/10: Allow setting reason for each QL if can set status ind of QT
                    string sReason = "";
                    // CS 7/3/07: Allow setting QL status independent of QT
                    // oArray = doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2)
                    // If oArray.GetDimension > 0 Then sVal = oArray.GetItem(1)
                    // If sVal <> "" Then
                    // doForm.doRS.SetFieldVal("MLS_Status", sVal, 2)
                    // End If
                    // oArray = Nothing
                    // sVal = ""


                    // If quote set to set status of QL from QT, set it here.
                    // CS 6/12/08: Changed code from setting status using friendly value.
                    // This caused issue b/c the friendly names did not match (ie On Hold in QT vs 20 On Hold in QL)

                    // CS 6/20/08 If WOP is off, always gray out status and set to
                    // same as QT
                    // CS 5/28/10: Do same for Reason Won/Lost
                    if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_STATUSMGMT")) != "1")
                    {
                        doForm.SetControlState("MLS_STATUS", 4);
                        doForm.SetControlState("MLS_REASONWONLOST", 4);
                        oArray = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2);
                        oArrayReason = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2);
                        sVal = oArray.GetItem(1);
                        sReason = oArrayReason.GetItem(1);
                        if (sVal != "")
                        {
                            // goTR.StringToNum(sDuration, "", ref par_iValid).ToString()
                            doForm.doRS.SetFieldVal("MLS_STATUS", goTR.StringToNum(sVal, "", ref par_iValid), 2);
                            doForm.SetControlState("MLS_STATUS", 4);
                        }
                        if (sReason != "")
                        {
                            doForm.doRS.SetFieldVal("MLS_REASONWONLOST", goTR.StringToNum(sReason, "", ref par_iValid), 2);
                            doForm.SetControlState("MLS_REASONWONLOST", 4);
                        }
                        sReason = "";
                        oArrayReason = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                    else if (doForm.doRS.GetFieldVal("LNK_IN_QT%%CHK_UPDQLSTATUS").ToString().ToUpper() == "CHECKED")
                    {
                        oArray = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2);
                        oArrayReason = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2);
                        sVal = oArray.GetItem(1);
                        sReason = oArrayReason.GetItem(1);
                        if (sVal != "")
                        {
                            // doForm.doRS.SetFieldVal("MLS_STATUS", doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 1), 1)
                            doForm.doRS.SetFieldVal("MLS_STATUS", goTR.StringToNum(sVal, "", ref par_iValid), 2);
                            doForm.SetControlState("MLS_STATUS", 4);
                        }
                        else
                            // status active
                            doForm.SetControlState("MLS_STATUS", 0);
                        sVal = "";
                        oArray = null/* TODO Change to default(_) if this is not a reference type */;
                        if (sReason != "")
                        {
                            doForm.doRS.SetFieldVal("MLS_reasonwonlost", goTR.StringToNum(sReason, "", ref par_iValid), 2);
                            doForm.SetControlState("MLS_REASONWONLOST", 4);
                        }
                        else
                            // status active
                            doForm.SetControlState("MLS_REASONWONLOST", 0);
                        sReason = "";
                        oArrayReason = null/* TODO Change to default(_) if this is not a reference type */;
                    }

                    // CS 5/28/10 commented
                    // oArray = doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2)
                    // If oArray.GetDimension > 0 Then sVal = oArray.GetItem(1)
                    // If sVal <> "" Then
                    // doForm.doRS.SetFieldVal("MLS_REASONWONLOST", sVal, 2)
                    // End If


                    // doForm.dors.SetFieldVal("MLS_STATUS", doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2), 2)
                    // goP.TraceLine("Setting MLS_REASONWONLOST from Quote: '" & doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2) & "'", "", sProc)
                    // doForm.dors.SetFieldVal("MLS_REASONWONLOST", doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2), 2)
                    // ******************************
                    // ----------------------------------------
                    // goScr.RunScript("Quotline_CalcTotal", doForm);      // Runs Quotline_CalcTotal

                    scriptManager.RunScript("Quotline_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                }
                // ------- Set Line number to highest used line + 1 ----------
                clRowSet doRS;
                lHighestLine = 0;
                // goP.TraceLine("Starting a rowset on all Quote's Quote Lines", "", sProc)


                // CS: The code below would get quote lines that existed with a null lnk_in_qt. There
                // are 8 in the db with no linked quote. I am changing this to only run if lnk_in_qt
                // is not blank.

                if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_In_QT", 1)) != "")
                {

                    // doRS = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("LNK_In_QT") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    // *** MI 11/14/07 Optimization
                    // doRS = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("LNK_In_QT") & "'", , "SR__LINENO", , , , , , , doForm.doRS.bBypassValidation)
                    // *** MI 11/21/07 Optimization: use a read-only rowset
                    doRS = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("LNK_In_QT") + "'", null/* Conversion error: Set to default value for this argument */, "SR__LINENO");
                    if (doRS.GetFirst() == 1)
                    {
                        do
                        {
                            // goP.TraceLine("Reading Quote Line '" & doRS.GetFieldVal("SR__LINENO", 2) & "'", "", sProc)
                            lLine = Convert.ToInt64(doRS.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doRS.GetNext() == 0)
                                break;
                        }
                        while (true);
                    }
                    else
                    {
                    }
                    // delete(doRS)
                    doRS = null;
                }
                // goP.TraceLine("Highest line: '" & lHighestLine & "'", "", sProc)
                sWork = Convert.ToString(lHighestLine);
                sWork = goTR.ExtractString(sWork, 1, ".");
                if (sWork == clC.EOT.ToString())
                    sWork = "0";
                // goP.TraceLine("Integer part of highest line: '" & sWork & "'", "", sProc)
                lHighestLine = Convert.ToInt32(sWork) + 1;
                // goP.TraceLine("Highest line after '+1': '" & lHighestLine & "'", "", sProc)
                doForm.doRS.SetFieldVal("SR__LINENO", lHighestLine, 2);
            }
            else
                // goP.TraceLine("LNK_IN_QT link count: " & doForm.dors.GetLinkCount("LNK_IN_QT"), "", sProc)
                if (doForm.doRS.GetLinkCount("LNK_IN_QT") > 0)
            {
                // CS 6/22/09: Create rowset for getting all Quote values instead of all double hops below (creating multiple rowsets)
                clRowSet doRSQT = new clRowSet("QT", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_IN_QT") + "'", null/* Conversion error: Set to default value for this argument */, "DTE_TIME,TME_TIME,DTE_EXPCLOSEDATE,LNK_CREDITEDTO_US,LNK_PEER_US,LNK_RELATED_PR,LNK_TO_CO,MLS_REASONWONLOST");
                if (doRSQT.GetFirst() == 1)
                {
                    doForm.doRS.SetFieldVal("DTE_QTETIME", doRSQT.GetFieldVal("DTE_TIME"));
                    doForm.doRS.SetFieldVal("TME_QTETIME", doRSQT.GetFieldVal("TME_TIME"));

                    if (goTR.IsDate(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1).ToString()) != true)
                        doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doRSQT.GetFieldVal("DTE_EXPCLOSEDATE"));
                    if (doForm.doRS.GetLinkCount("LNK_CREDITEDTO_US") < 1)
                        doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doRSQT.GetFieldVal("LNK_CREDITEDTO_US", 2), 2);
                    if (doForm.doRS.GetLinkCount("LNK_PEER_US") < 1)
                        doForm.doRS.SetFieldVal("LNK_PEER_US", doRSQT.GetFieldVal("LNK_PEER_US", 2), 2);
                    doForm.doRS.SetFieldVal("LNK_RELATED_PR", doRSQT.GetFieldVal("LNK_RELATED_PR", 2), 2);
                    doForm.doRS.SetFieldVal("LNK_TO_CO", doRSQT.GetFieldVal("LNK_TO_CO", 2), 2);
                }
                // CS 6/22/09---------
                // doForm.doRS.SetFieldVal("DTE_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_TIME"))
                // doForm.doRS.SetFieldVal("TME_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%TME_TIME"))

                // If goTR.IsDate(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1)) <> True Then
                // doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_EXPCLOSEDATE"))
                // End If
                // If doForm.doRS.GetLinkCount("LNK_CREDITEDTO_US") < 1 Then
                // doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2), 2)
                // End If
                // If doForm.doRS.GetLinkCount("LNK_PEER_US") < 1 Then
                // doForm.doRS.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_PEER_US", 2), 2)
                // End If
                // doForm.doRS.SetFieldVal("LNK_RELATED_PR", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_RELATED_PR", 2), 2)
                // doForm.doRS.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_TO_CO", 2), 2)
                // CS -------------


                // -------- Status, Reason Won/Lost -------
                // ******************************
                // In Selltis DB, Status and Reason Won/Lost are the same as in Quote
                // goP.TraceLine("Setting MLS_STATUS from Quote: '" & doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2) & "'", "", sProc)
                // CS 7/3/07: Allow setting status of QL independent of QT
                // doForm.doRS.SetFieldVal("MLS_STATUS", doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 1), 1)
                // goP.TraceLine("Setting MLS_REASONWONLOST from Quote: '" & doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2) + "'", "", sProc)
                // doForm.doRS.SetFieldVal("MLS_REASONWONLOST", doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 1), 1)
                // 'In Selltis DB, Status and Reason Won/Lost are the same as in Quote
                // If doForm.GetFieldVal("MLS_STATUS",2) <> doForm.GetFieldVal("LNK_IN_QT%%MLS_STATUS",2) Then
                // doForm.SetFieldVal("MLS_STATUS",doForm.GetFieldVal("LNK_IN_QT%%MLS_STATUS",2),2)
                // End If
                // If doForm.GetFieldVal("MLS_REASONWONLOST",2) <> doForm.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST",2) Then
                // doForm.SetFieldVal("MLS_REASONWONLOST",doForm.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST",2),2)
                // End If
                // ******************************
                // ----------------------------------------
                // If quote set to set status of QL from QT, gray out the Status selection on the QL.
                // QLEditStatus is set in the button click of the Edit button on the QT form.
                // CS 6/12/08: 
                // NOTE: There is an issue if you open a QT that previously had the 'Update 
                // QL status' checkbox checked and now uncheck it and click the Edit button to edit the QL.
                // In this case the code below still reports the Update QL Status as checked and therefore
                // the status on the QL you are trying to edit is still grayed out.
                // CS 6/20/08: 
                // Could be directly opening a QL record which means we need to check
                // the linked QT OR could be opening from the Edit button of the QT form
                // or double clicking the line from the LInes linkbox on Quote
                string sMOde = doForm.LinkboxAction;
                // CS 6/20/08 If WOP to manage QLs is off, always gray out status field of QL
                // During save of QT, the QL status will be updated to match the QT, even if it had 
                // previously been on.
                if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_STATUSMGMT")) != "1")
                {
                    doForm.SetControlState("MLS_STATUS", 4);
                    doForm.SetControlState("MLS_REASONWONLOST", 4); // CS 5/28/10
                }
                else if (Strings.InStr(Strings.UCase(sMOde), "CLICKTHRU_QT_LNK_CONNECTED_QL") == 0 & Convert.ToString(doForm.oVar.GetVar("QLOpenFromEditButton")) != "1")
                {
                    // Try to check the linkboxaction to determine if QL form is being opened
                    // as a result of a linkbox click through
                    // If QL not opened as result of click thru in Lines linkbox or from clicking edit button need to get QT status
                    // from Quote link; otherwise need from QT form in history
                    if (doForm.doRS.GetFieldVal("LNK_IN_QT%%CHK_UPDQLSTATUS").ToString().ToUpper() == "CHECKED")
                    {
                        // If (UCase(doForm.doRS.GetFieldVal("LNK_IN_QT%%CHK_UPDQLSTATUS")) = "CHECKED" AND goP.GetVar(") Or goP.GetVar("QLEditStatus") <> "1" Then
                        doForm.SetControlState("MLS_STATUS", 4);
                        doForm.SetControlState("MLS_REASONWONLOST", 4); // CS 5/28/10
                    }
                    else
                    {
                        // status active
                        doForm.SetControlState("MLS_STATUS", 0);
                        doForm.SetControlState("MLS_REASONWONLOST", 0); // CS 5/28/10
                    }
                }
                else
                {
                    //// New: Get QT form in history for this QL
                    //string sQuoteID = doForm.doRS.GetFieldVal("LNK_IN_QT").ToString();
                    //int i;
                    //Form oForm;
                    //string sHistory = "";
                    //clArray oHistory = goUI.GetUIObjectGUIDs;
                    //if (oHistory.GetDimension() > 0)
                    //{

                    //    // CS 9/9/08: Loop thru items in UI stack and find QT for this QL
                    //    for (i = 1; i <= oHistory.GetDimension(); i++)
                    //    {
                    //        sHistory = oHistory.GetItem(i);
                    //        // Check if a form, if so, check if is the linked QT for the QL
                    //        object oObject = goUI.GetUIObject(sHistory);
                    //        if (Strings.UCase(oObject.GetType().ToString()) == "CLFORM")
                    //        {
                    //            oForm = (Form)oObject;
                    //            if (oForm.GetRecordID() == sQuoteID)
                    //            {
                    //                if (oForm.doRS.GetFieldVal("CHK_UPDQLSTATUS").ToString().ToUpper()== "CHECKED")
                    //                {
                    //                    doForm.SetControlState("MLS_STATUS", 4);
                    //                    doForm.SetControlState("MLS_REASONWONLOST", 4); // CS 5/28/10
                    //                }
                    //                else
                    //                {
                    //                    // status active
                    //                    doForm.SetControlState("MLS_STATUS", 0);
                    //                    doForm.SetControlState("MLS_REASONWONLOST", 0); // CS 5/28/10
                    //                }
                    //                break;
                    //            }
                    //        }
                    //        else
                    //        {
                    //        }
                    //    }
                    //}

                    // New: Get QT form in history for this QL
                    string sQuoteID = Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_QT"));
                    int i;
                    Form oForm = default(Form);
                    string sHistory = "";
                    //clArray oHistory = goUI.GetUIObjectGUIDs;
                    clArray oHistory = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT", 2);
                    if (oHistory.GetDimension() > 0)
                    {

                        // CS 9/9/08: Loop thru items in UI stack and find QT for this QL
                        for (i = 1; i <= oHistory.GetDimension(); i++)
                        {
                            sHistory = oHistory.GetItem(i);
                            // Check if a form, if so, check if is the linked QT for the QL
                            //object oObject = goUI.GetUIObject(sHistory);

                            string LastSelectedItemInHistory = "";
                            if (Util.GetSessionValue("SelectedHistoryItem") != null)
                            {
                                LastSelectedItemInHistory = Util.GetSessionValue("SelectedHistoryItem").ToString();
                            }
                            string ObjectType = "";
                            List<HistoryItems> _desktopHistoryObjects = new List<HistoryItems>();
                            _desktopHistoryObjects = (List<HistoryItems>)Util.GetSessionValue("DesktopHistoryObjects");
                            ObjectType = _desktopHistoryObjects.Find(e => e.Key == LastSelectedItemInHistory).IsDesktop_or_Form.ToLower();

                            if (Strings.UCase(ObjectType) == "CLFORM")
                            {
                                //oForm = (Form)oObject;
                                if (_desktopHistoryObjects.Find(e => e.Key == LastSelectedItemInHistory).RecordId == sQuoteID)
                                {
                                    if (Convert.ToString(oForm.doRS.GetFieldVal("CHK_UPDQLSTATUS")).ToUpper() == "CHECKED")
                                    {
                                        doForm.SetControlState("MLS_STATUS", 4);
                                        doForm.SetControlState("MLS_REASONWONLOST", 4); // CS 5/28/10
                                    }
                                    else
                                    {
                                        // status active
                                        doForm.SetControlState("MLS_STATUS", 0);
                                        doForm.SetControlState("MLS_REASONWONLOST", 0); // CS 5/28/10
                                    }
                                    break;
                                }
                            }
                            else
                            {
                            }
                        }
                    }



                }


                // CS 8/27/08: Quote line users mgmt
                if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_USERMGMT")) != "1")
                {
                    doForm.SetControlState("LNK_CREDITEDTO_US", 4);
                    doForm.SetControlState("LNK_PEER_US", 4);
                }
                else
                {
                    doForm.SetControlState("LNK_CREDITEDTO_US", 0);
                    doForm.SetControlState("LNK_PEER_US", 0);
                }
            }

            // CS: think not needed---------- VARIABLES -----------
            // doForm.oVar.SetVar("sForSKUOrigVal", doForm.dors.GetFieldVal("LNK_FOR_MO", 2))
            // doForm.oVar.SetVar("srQtyEnterVal", doForm.dors.GetFieldVal("SR__QTY", 2))

            // ----------- STATES ----------
            // ******************************
            // In Selltis DB, Status and Reason Won/Lost are the same as in Quote
            // CS: Commenting b/c SetFieldProperty causes syntax error
            // doForm.SetFieldProperty("MLS_STATUS", "STATE", Grayed)
            // doForm.SetFieldProperty("MLS_REASONWONLOST", "STATE", Grayed)
            // ******************************


            goP.SetVar("QLEditStatus", "");
            // SGR 08172015:TKT # 671
            if (Convert.ToString(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2)) != "0")
            {
                decimal Margin = 0;
                Margin = (Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2)) - Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_COST", 2)) / Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2))) * 100;
                doForm.doRS.SetFieldVal("SR__MARGIN", Margin, 2);
            }

            return true;
        }
        public bool QT_FormControlOnChange_LNK_RELATED_DM_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // VS 01272015 TKT#280 : New Field CUR_TOTALTAX
            double ShippingTax = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_SHIPPINGTAX"));
            double SalesTax = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_SALESTAX"));

            doForm.doRS.SetFieldVal("CUR_TOTALTAX", ShippingTax + SalesTax);

            return true;
        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;
            // SGR 08172015:TKT # 671
            if (Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2)) > 0)
            {
                decimal Margin = 0;
                Margin = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2)) - Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_COST", 2)) / Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2)) * 100;
                doForm.doRS.SetFieldVal("SR__MARGIN", Margin, 2);
            }

            return true;
        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // SGR TKT#:671 08212015
            par_bRunNext = false;

            Form doForm = null;
            clRowSet doRS1 = null;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3)
            if (par_s1 == "doRS")
                doRS1 = (clRowSet)par_doCallingObject;
            else
                doForm = (Form)par_doCallingObject;

        
            decimal cPriceUnit;
            double rQtyFld;
            double rDiscPerc;
            decimal cDiscAddAmt;
            decimal cCostVal;
            decimal cWork;
            decimal cSubtotal;
            double rSalesTaxPerc;
            decimal cSKUCost;
            string sSKUCost;

            // PURPOSE:
            // Calc Subtotal if Include is checked, otherwise enter 0 as subtotal
            // Field 'Price Unit No Disc' = Unit Price before discount
            // Field 'Price Unit' = Unit Price after discount
            // RETURNS:
            // True.

            // goP.TraceLine("", "", sProc)

            // CS Need to check if coming from RecOnSave b/c in that case we are working with a rowset and 
            // otherwise we are on a form.
            if (par_s1 != "doRS")
            {
                if (Convert.ToString(doForm.doRS.GetFieldVal("CHK_INCLUDE", 2)) != "1")
                {
                    // Set calculated fields to 0
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_COST", 0, 2);
                    doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_SALESTAX", 0, 2);
                }
                else
                {
                    cPriceUnit = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2));
                    rQtyFld = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));
                    rDiscPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__DISCPERCENT", 2));
                    cDiscAddAmt = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_DISCADDLAMT", 2));
                    // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                    // cCostVal = doForm.doRS.GetFieldVal("CUR_COST", 2)
                    // SGR TKT#:671 08212015 Commented below line and added second line
                    // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                    sSKUCost = doForm.doRS.GetFieldVal("CUR_COST").ToString();
                    if (sSKUCost != "")
                        cSKUCost = goTR.StringToCurr(sSKUCost, "", ref par_iValid);
                    else
                        cSKUCost = 0;
                    cCostVal = cSKUCost * Convert.ToDecimal(rQtyFld);
                    doForm.doRS.SetFieldVal("CUR_COST", cCostVal);                  

                    // Calculate unit price after discount
                    cWork = cPriceUnit - (cPriceUnit * Convert.ToDecimal(rDiscPerc) / 100);
                    doForm.doRS.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                    // Calculate Subtotal
                    //  cSubtotal = (cPriceUnit *(decimal) rQtyFld) - (Convert.ToInt64( cPriceUnit) * Convert.ToInt64(rQtyFld * rDiscPerc) / 100) + cDiscAddAmt;
                    cSubtotal = (cPriceUnit * Convert.ToDecimal(rQtyFld)) - (cPriceUnit * Convert.ToDecimal(rQtyFld * rDiscPerc) / 100) + cDiscAddAmt;
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                    // Calc Gross Profit
                    // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                    // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS 6/13/07: Added rQtyField per DF
                    cWork = cSubtotal - cCostVal;
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));

                    // Sales tax
                    if (Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_TAXABLE", 2)))
                    {
                        // CS 6/2/09: Get value from variable if set
                        if (Convert.ToString(goP.GetVar("QuoteInfo")) == "")
                            rSalesTaxPerc = Convert.ToDouble (doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));
                        else
                            // rSalesTaxPerc = goTR.StringToNum(goTR.StrRead(goP.GetVar("QuoteInfo").ToString(), "QT_SALESTAXPERCENT", null/* Conversion error: Set to default value for this argument */, false));
                            rSalesTaxPerc = goTR.StringToNum(goTR.StrRead(goP.GetVar("QuoteInfo").ToString(), "VIEWCOUNT", 0, false), "", ref par_iValid, par_s1);

                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", Convert.ToDouble(cSubtotal) * rSalesTaxPerc / 100);
                    }
                    else
                    {
                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", 0);
                    }
                }
            }
            else if (Convert.ToString(doRS1.GetFieldVal("CHK_INCLUDE", 2)) != "1")
            {
                // Set calculated fields to 0
                doRS1.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                doRS1.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                doRS1.SetFieldVal("CUR_COST", 0, 2);
                doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                doRS1.SetFieldVal("CUR_SALESTAX", 0, 2);
            }
            else
            {
                cPriceUnit = Convert.ToDecimal(doRS1.GetFieldVal("CUR_PRICEUNIT", 2));
                rQtyFld = Convert.ToDouble(doRS1.GetFieldVal("SR__QTY", 2));
                rDiscPerc = Convert.ToDouble(doRS1.GetFieldVal("SR__DISCPERCENT", 2));
                cDiscAddAmt = Convert.ToDecimal(doRS1.GetFieldVal("CUR_DISCADDLAMT", 2));
                // cCostVal = doRS1.GetFieldVal("CUR_COST", 2)
                // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                string sModel = goP.GetVar("QuoteLineInfo_" + doRS1.GetFieldVal("GID_ID")).ToString();
                // SGR TKT#:671 08212015
                // If sModel = "" Then
                // sSKUCost = doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST")

                // Else
                // sSKUCost = goTR.StrRead(sModel, "MO_CUR_COST", , False)
                // End If
                sSKUCost = doRS1.GetFieldVal("CUR_COST").ToString();
                if (sSKUCost != "")
                    cSKUCost = goTR.StringToCurr(sSKUCost, "", ref par_iValid, "");
                else
                    cSKUCost = 0;
                cCostVal = cSKUCost * Convert.ToDecimal(rQtyFld);
                doRS1.SetFieldVal("CUR_COST", cCostVal);

                // 'CS: only set the cost if it is 0
                // If cCostVal = 0 Then
                // cSKUCost = goTR.StringToCurr(doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST"))
                // cCostVal = cSKUCost * rQtyFld
                // doRS1.SetFieldVal("CUR_COST", goTR.RoundCurr(cCostVal))
                // End If

                // Calculate unit price after discount
                cWork = cPriceUnit - (cPriceUnit * Convert.ToDecimal(rDiscPerc) / 100);
                doRS1.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                // Calculate Subtotal
                cSubtotal = (cPriceUnit * Convert.ToDecimal(rQtyFld)) - (cPriceUnit * Convert.ToDecimal(rQtyFld * rDiscPerc) / 100) + cDiscAddAmt;
                doRS1.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                // Calc Gross Profit
                cWork = cSubtotal - cCostVal;
                // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS Added rQtyField 
                doRS1.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));

                // Sales tax
                if (Convert.ToBoolean(doRS1.GetFieldVal("CHK_TAXABLE", 2)))
                {
                    // CS 6/2/09:
                    if (Convert.ToString(goP.GetVar("QuoteInfo")) == "")
                    {
                        // CS 10/19/10: Check if have a linked QT. Should always, but causes error if not.
                        if (doRS1.GetLinkCount("LNK_IN_QT") > 0)
                            rSalesTaxPerc = Convert.ToDouble(doRS1.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));
                        else
                            rSalesTaxPerc = 0;
                    }
                    else
                        // rSalesTaxPerc = goTR.StringToNum(goTR.StrRead(goP.GetVar("QuoteInfo").ToString(), "QT_SALESTAXPERCENT", null/* Conversion error: Set to default value for this argument */, false));
                        rSalesTaxPerc = goTR.StringToNum(goTR.StrRead(goP.GetVar("QuoteInfo").ToString(), "QT_SALESTAXPERCENT", 0, false), "", ref par_iValid, par_s1);

                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                    doRS1.SetFieldVal("CUR_SALESTAX", Convert.ToDouble(cSubtotal) * rSalesTaxPerc / 100);
                }
                else
                {
                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0);
                    doRS1.SetFieldVal("CUR_SALESTAX", 0);
                }
            }
            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)



            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_CIS Quote No Discount_Darft.docx";
                }
                else if (sQTTemplate == "CIS CGG Quote")
                {
                    return "cus_CIS CGG Quote_Draft.docx";
                }
                else if (sQTTemplate == "CIS Choice Partners Quote")
                {
                    return "cus_CIS_Choice_Partners_Quote_Template_Darft.docx";
                }
                else if (sQTTemplate == "CIS Quote With Discount")
                {
                    return "cus_CIS Quote with Discount_Darft.docx";
                }
                
            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_CIS Quote No Discount.docx";
                }
                else if (sQTTemplate == "CIS CGG Quote")
                {
                    return "cus_CIS CGG Quote.docx";
                }
                else if (sQTTemplate == "CIS Choice Partners Quote")
                {
                    return "cus_CIS_Choice_Partners_Quote_Template.docx";
                }
                else if (sQTTemplate == "CIS Quote With Discount")
                {
                    return "cus_CIS Quote with Discount.docx";
                }
                
            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                       
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;

            doForm.doRS.SetFieldVal("DTT_OppDate", "Today|Now");

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);

            //convert to Qt button code
            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_RELATED_PD,LNK_FOR_MO,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);
            rsOL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }
        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);

            //This will generate line no's in mobile.
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }
        public bool QT_MoveQuoteLine_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            string sDirection = "";
            string sLtGt = "";
            string sFirstLast = "";
            string sMoveSortDir = "";

            string sRecID = "";
            string sQuoteID = "";
            string sSelLineNO = "";
            string sMoveLineNO = "";
            double dSelLineNO = 0;
            double dMoveLineNO = 0;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot edit Quote Lines.");
                return true;
            }

            if (par_s3.ToUpper() == "UP")
            {
                sDirection = "up";
                sLtGt = "<";
                sFirstLast = "first";
                sMoveSortDir = "desc";
            }
            else if (par_s3.ToUpper() == "DOWN")
            {
                sDirection = "down";
                sLtGt = ">";
                sFirstLast = "last";
                sMoveSortDir = "asc";
            }
            else
            {
                return false;
            }

            sQuoteID = doForm.doRS.GetFieldVal("GID_ID").ToString();
            sRecID = doForm.GetLinkSelection("LNK_Connected_QL");

            if (string.IsNullOrEmpty(sRecID))
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }

            clRowSet doQLSelRS = new clRowSet("QL", clC.SELL_EDIT, "GID_ID=" + sRecID, "", "*,**", 1, par_bBypassValidation: true);
            if (doQLSelRS.GetFirst() == 0)
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }
            sSelLineNO = doQLSelRS.GetFieldVal("SR__LineNo").ToString();
            dSelLineNO = Convert.ToDouble(doQLSelRS.GetFieldVal("SR__LineNo"));

            clRowSet doQLMoveRS = new clRowSet("QL", clC.SELL_EDIT, ("LNK_IN_QT=" + sQuoteID + " AND SR__LineNo" + sLtGt + sSelLineNO), "SR__LineNo " + sMoveSortDir, "*,**", 1, par_bBypassValidation: true);
            if (doQLMoveRS.GetFirst() == 0)
            {
                doForm.MessageBox("This is the " + sFirstLast + " Qoute Line. Please select another Qoute Line to move " + sDirection + ".");
                par_doCallingObject = doForm;
                return false;
            }
            sMoveLineNO = doQLMoveRS.GetFieldVal("SR__LineNo").ToString();
            dMoveLineNO = Convert.ToDouble(doQLMoveRS.GetFieldVal("SR__LineNo"));

            doQLSelRS.SetFieldVal("SR__LineNo", dMoveLineNO, 2);
            doQLMoveRS.SetFieldVal("SR__LineNo", dSelLineNO, 2);

            if (doQLSelRS.Commit() == 0 || doQLMoveRS.Commit() == 0)
            {
                doForm.MessageBox("Could not save the Quote Lines. Please reopen the quote and try again.");
                par_doCallingObject = doForm;
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");
            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;
        }

        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            clRowSet doRS1 = null;


            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS1.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doRS1.SetFieldVal("CUR_VALUE", curValue);
                    doRS1.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doRS1;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                    doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doForm;
            }

            return true;

        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;


            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

            }
            par_doCallingObject = doRS;
            par_bRunNext = false;
            return true;
        }
        public bool OL_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.oVar.SetVar("OppID", doRS.GetFieldVal("LNK_IN_OP"));

            par_doCallingObject = doRS;

            return true;
        }

        public bool OL_RecordAfterDelete_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_s2 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sID = doRS.oVar.GetVar("OppID").ToString();

            if (!string.IsNullOrEmpty(sID))
            {
                clRowSet doOPLines = default(clRowSet);

                //Create a rowset of quote lines linked to the deleted quote.
                doOPLines = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo,Gid_id", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    StringBuilder sbQuery = new StringBuilder();
                    for (int i = 1; i <= doOPLines.Count(); i++)
                    {
                        double iLineno = Convert.ToDouble(doOPLines.GetFieldVal("SR__LineNo"));
                        string sGid_id = Convert.ToString(doOPLines.GetFieldVal("Gid_ID"));
                        string sTemp = "#" + goTR.Pad(i.ToString(), 6, " ", "L", true, "R");
                        sbQuery.AppendLine("Update OL Set SR__LineNo='" + i.ToString() + "',sys_name='" + sTemp + "' where Gid_id='" + sGid_id + "' AND SR__LineNo = '" + iLineno.ToString() + "'");

                        if (doOPLines.GetNext() == 0)
                        {
                            break;
                        }

                    }
                    if (!string.IsNullOrEmpty(sbQuery.ToString()))
                    {
                        bool bretval = goData.RunSQLQuery(sbQuery.ToString());
                    }

                }

                doOPLines = null;

            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_ENDUSER_CO", rsOP.GetFieldVal("LNK_ENGINEERING_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_DISTREP_CO", rsOP.GetFieldVal("LNK_DISTRIBUTOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECOMPLETED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }
        public bool QT_FormControlOnChange_BTN_RECALC_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_QouteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            par_doCallingObject = doForm;
            return true;

        }
        private static void Refresh_QouteTotal(clRowSet doQuote)
        {
            string sGidId = Convert.ToString(doQuote.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_GROUPBY, "LNK_IN_QT='" + sGidId + "' ", "LNK_IN_QT", "CUR_SUBTOTAL|SUM");

            double curTotalAmt = 0.0;

            if ((rsQL.GetFirst() == 1))
            {
                curTotalAmt = Convert.ToDouble(rsQL.GetFieldVal("CUR_SUBTOTAL|SUM", 2));
                doQuote.SetFieldVal("CUR_SUBTOTAL", curTotalAmt, 2);
                doQuote.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);
            }
            else
            {
                doQuote.SetFieldVal("CUR_SUBTOTAL", 0.0);
                doQuote.SetFieldVal("CUR_TOTAL", 0.0);
            }


        }
    }
}

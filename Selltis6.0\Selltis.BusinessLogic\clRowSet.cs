﻿using System;
using MailBee.Mime;
using System.IO;
using System.Web;
using System.Data;
using System.Collections.Generic;
using System.Diagnostics;

//Owner: RH 

namespace Selltis.BusinessLogic
{
	public class clRowSet
	{

#region Declarations
		//MI 10/30/08 Added iLinksTop, sLinksTopIni.

		public DataSet oDataSet = new DataSet(); //==> Change to private later
		public DataTable dtTransTable;
		public clVar oVar = new clVar();

		private DataTable oDataTable = new DataTable(); // Root table. Always in system format.
		private int iDataFormat = clC.SELL_FRIENDLY;
		private long lCurrentRow = 0;

		private Microsoft.VisualBasic.Collection cFields = new Microsoft.VisualBasic.Collection();
		private Microsoft.VisualBasic.Collection cEditableLinks = new Microsoft.VisualBasic.Collection(); // Collection of editable Links and properties
		private Microsoft.VisualBasic.Collection cLoadedLinks = new Microsoft.VisualBasic.Collection(); // Collection for mapping Link column names to data tables
		//Private cLinkTables As New Collection

		public int iRSType = 3; // 1=edit, 2=add, 3=read only
		public string sRSFile;
		public bool bUpdateSysNames = false;
		private string sIntermediate = "";
		private string sDefaults = "";
		private string sCreateType = "";
		private string sCreateFromID = "";
		private string sBaseGID = "";
		private string sBaseSYSNAME = "";
		private int iLinksTop = -1; //MI 10/30/08 added 'number of links to return for links not defined in sLinksTopIni
		private string sLinksTopIni = ""; //MI 10/30/08 added 'Ini-format list of link=<n> where <n> is max no of links to return

		//Public BI__ID_LastCommit As Long
		public string SYS_NAME_LastCommit;

		public bool bBypassValidation = false;
		public bool bNoRecordOnSave = false;
		public bool bNoGenerateSysName = false;
		public bool bRSIsDirty = false;
		public long lSQLLoadTime;

		public bool bSysNameUpdateLimitExceeded = false;
		public bool bBypassSysNameUpdateLimit = false;

		public string sSort; //MI 1/11/09 Added to be able to add sort fields to fields in ToTable, GROUPBY and GROUPBYWITHROLLUP modes.
		public System.Collections.Generic.Dictionary<string, Tuple<object, object>> gsDirtyFields; //V_T 10/15/2015 Added to maintain the list of dirty field original & new values

		private struct LinkDataStructure
		{
			public bool LinkAllowsMultiple; //True if link allows multiple links
			public int LinkDirection; //1 for front, 2 for back
			public string LinkID;
			public string FQLinkName;
			public string FromFile;
			public string ToFile;
			public string Link;
			public string LinkLabel;
			public string LinkType;
			public bool IsEditable;
		}

		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clScrMngRowSet goScr;
		private clLog goLog;
		private clUtil goUt;


#endregion

#region Public Methods
		public clRowSet(string par_sTableName, int par_iType, string par_sCondition = "", string par_sSort = "", string par_sFields = "", int par_iTop = -1, string par_sINI = "", string par_sGenFieldsDefs = "", string par_sCreateType = "", string par_sCreateFromID = "", string par_sTopRecord = "", bool par_bBypassValidation = false, bool par_bNoRecordOnSave = false, bool par_bGetAllUsersUnsharedRecs = false, bool par_bReverseSort = false, int par_iLinksTop = -1, string par_sLinksTop = "", bool par_bNoGenerateSysName = false, bool par_bSysFileFullPermissions = false, int par_iGetDataTimeout = 1800)
		{
			//MI 3/28/14 Added support for MMR_.
			//MI 11/20/12 Added comments about default values for ADD type.
			//MI 9/7/11 .DeleteRecord: changed logging from level 2 to level 0 (always).
			//MI 4/20/10 Expanded comments on par_sFields parameter.
			//MI 1/8/10 Added support for par_iType clc.SELL_GROUPBYWITHROLLUP.
			//MI 12/10/09 Added support for par_iType clc.SELL_GROUPBY, clc.SELL_COUNT.
			//MI 11/3/09 Added raising error when type=3 and FIELDS= is blank.
			//MI 10/15/09 Added substituting view-syntax properties (SHOWTOP/TOP, FILE/TABLENAME).
			//MI 3/17/09 Added par_bSysFileFullPermissions.
			//MI 12/10/08 Changed logging to SELL_LOGLEVEL_DETAILS.
			//MI 10/30/08 Edited comments.
			//MI 10/23/08 Prepended old parameters with 'par_', edited notes.
			//MI 10/15/08 Added par_bNoGenerateSysName.
			//MI 10/10/08 Added par_bReverseSort, par_iLinksTop, par_sLinksTop.
			//MI 4/9/08 Added par_bGetAllUsersUnsharedRecs.
			//MI 7/27/07 Improved comments on par_bBypassValidation and par_bNoRecorOnSave parameters.
			//RH: {insert date here} They came by and said no more params.
			//MI 9/13/06 Fixes.
			//MI 9/12/06 Added par_sTopRecord. Changes commented with '*** MI 9/12/06.


			//PURPOSE:
			//		Constructor for new Rowset.  Parameters define rowset.
			//       For more in-depth notes, see clSQL.GenerateSQL comments.
			//       SECURITY NOTES: Rowset works within the security context of the currently logged on
			//       user. Only admins and full authors can access system files (MD, TN, XL) unless
			//       you set par_bSysFileFullPermissions to True. Non-shared records are only
			//       accessible by their creators unless you set par_bGetAllUsersUnsharedRecs to True.
			//PARAMETERS (see clSQL.GenerateSQL for more detailed notes):
			//		par_sTablename:         Selltis file.
			//       par_iType:              clc.SELL_EDIT=1             'editable rowset. All fields and links
			//                                                           'are loaded. Don't forget to run Commit
			//                                                           'after SetFieldVal or SetLinkVals in ea.
			//                                                           'record to saves changes.
			//                               clc.SELL_ADD=2              'Creation rowset. All fields and links are
			//                                                           'loaded. Default values are set based on
			//                                                           'code and metadata. See 
			//                                                           '"DEFAULT VALUES IN ADD ROWSETS" below. 
			//                               clc.SELL_READONLY=3         'Only par_sFields are loaded. 
			//                               clc.SELL_GROUPBY=4          'Each sort field is used to group and summarize data.
			//                                                           'Fields should have calculations defined, e.g.
			//                                                           ''|SUM', '|AVG', etc appended.
			//                               clc.SELL_GROUPBYWITHROLLUP=5'Same as SELL_GROUPBY, except with rollup
			//                                                           'grouping rows that perform calculations
			//                                                           'on all rows from the group. This mode is used
			//                                                           'for chart views, for example.
			//                               clC.SELL_COUNT = 6          'Returns one row with a virtual field 'BI__COUNT'
			//                                                           'that contains the total number of records 
			//                                                           'in the database for the given filter.
			//       par_sCondition:         Filter condition in Selltis syntax (no spaced around
			//                               comparison symbols like '=', '<', '][', etc.
			//       par_sSort:              Sort (Syntax: Field1 [ASC|DESC], Field2 [ASC|DESC], ...).
			//                               N1 link fields are supported (ex: LNK_TeamLeader_US%%SYS_Name).
			//                               Link names without a field, i.e. 'LNK_TeamLeader_US' evaluate to
			//                               the link's GID_ID, i.e. 'LNK_TeamLeader_US%%GID_ID'.
			//       par_sFields:            Comma delimited field list, * (all direct fields), or **
			//                               (all fields and links). In clc.SELL_EDIT and clc.SELL_ADD modes
			//                               ** is implicit - fields needn't be defined. To force * in clc.SELL_EDIT
			//                               mode, pass 'NOLINKS' in this parameter. NOLINKS is dangerous
			//                               because you are likely to end up with a 'field not loaded' error if defaults
			//                               are defined for any links, if CRL or CRU reference any links, and if 
			//                               RecordOnSave, FormOnLoadRecord, GenerateSysName, etc reference any links.
			//                               Notes for GROUPBY and GROUPBYWITHROLLUP par_iType only:
			//                                   Each field must be followed with "|<calc>", e.g. "CUR_Value|SUM". 
			//                                   Supported calcs:
			//                                       SUM, AVG, MIN, MAX, MED (median)
			//                                   Future support is possible for:
			//                                       STD (standard deviation), STP (stdev on population), 
			//                                       VAR (variance) and VAP (variance on population).
			//                                   COUNTs can be returned by defining a virtual field 'BI__GROUPBYCOUNT'.
			//                                   Only direct fields are supported. Fields and/or links without calcs
			//                                   will be skipped.

			//                               -If rowset type is edit and no value is passed in the fields parameter, * is used. 
			//                               Otherwise, * and whatever links are passed are used.

			//                               -If 'NOLINKS' is not passed, then the AutoLoad MD is added to the list of fields.

			//                               -If rowset type is add, ** is always used no matter what is passed in fields param

			//                               -If rowset type is read, only whatever is passed in fields parameter is used.

			//       par_iTop:               Top number of records to return. Ignored if par_iType is COUNT. In GROUPBY 
			//                               and GROUPBYWITHROLLUP types, this will not reduce the amount of underlying 
			//                               data queried, but will return only the first n rows returned by the
			//                               database. As a result, summary and rollup (grouping) data will be correct
			//                               in each row, but some rows may not be returned. In GROUPBYWITHROLLUP mode
			//                               this affects grouping rows that summarize rows above them. For example,
			//                               you may not get the total of all SUMs because par_iTop is 100 and the total
			//                               row gets cut off because it would be row 132.
			//                               For that reason we suggest to test whether there is more data and, if so, 
			//                               not return any data to the user. For example, if you don't want the rowset 
			//                               to receive more than 10,000 rows, set par_iTop to 10001 and test:
			//                                   If oRS.Count > 10000 Then 'refuse to display the data.
			//       par_sINI:               Alternative to passing all previous parameters (see clSQL.GenerateSQL
			//                               for syntax and details).
			//       par_sGenFieldsDefs:     Ini string defining any generated fields listed in field list.
			//       par_sCreateType:        Create linked, or unlinked, metadata page to use to determine
			//                               how to create the record.
			//                               e.g.  CRU_AC:EMAILSENT, CRU_AC, CRL_AC, CRL_AC:LEAD
			//                               If nothing passed, CRU_<%RowSetFile%> is used.
			//       par_sCreateFromID:      ID of the record to create from when CRL.  If not passed, 
			//                               goData.LastSelected.SelectedRecordID is used.
			//       par_sTopRecord:         Ini string that contains values of all sort fields in
			//                               the following format:
			//                                   TOPREC_<FieldName1>=<Value>
			//                                   TOPREC_<FieldName2>=<Value>
			//                                   ...
			//                               For more info, see clSQL.GenerateSQL().
			//       par_bBypassValidation:  Sets bBypassValidation and bypasses Commit validations based on:
			//                                   - FIELDSREQUIRED= value in FDL_xx metadata
			//                                   - Validations in XX_RecordOnSave scripts that are coded within the
			//                                   'If doQuote.bBypassValidation <> True Then...' block
			//       par_bNoRecordOnSave:    Skips running XX_RecordOnSave script in Commit. Sets bNoRecOnSave.
			//                               This does NOT suppress FIELDSREQUIRED validations. 
			//       par_bGetAllUsersUnsharedRecs: When True all records that are not shared (private records)
			//                               will be returned, else only the current user's unshared records
			//                               will be returned (False is the default). This parameter is ignored
			//                               if par_iType is clc.SELL_ADD (2).
			//                               WARNING: Set this parameter to True only in code that reads
			//                               data for all users and then processes it using conditional code.
			//                               For all normal data access leave the parameter at default (false)
			//                               so that the user sees only his/her unshared records.
			//       par_bReverseSort:       True indicates that the "primary" direction of the sort is backwards,
			//                               for example when the view is browsing 'pages' of data backward, i.e.
			//                               the user clicked the |< or < buttons.
			//                               This reverses the sort of BI__ID which is appended to ORDER BY in GenerateSQL 
			//                               to make sorts on non-discriminating fields predictable for page browsing, 
			//                               selecting the right record after postback, etc. (False is the default.)
			//       par_iLinksTop:          -1 (default) returns all links. If set to a positive number, all NN links
			//                               in the rowset will return up to that many linked records.
			//                               It's as if the rest of the links don't exist from the standpoint of the 
			//                               rowset - GenerateSQL returns NN link datatables using SELECT TOP <n> 
			//                               instead of just SELECT. 
			//                               This is like a selective read permission causing fewer or no linked records 
			//                               to be returned. To limit individual links, use par_sLinksTop.
			//       par_sLinksTop:          List of links and how many top records to return for each. Syntax:
			//                                   par_sLinksTop=<linkname1>=<n1>|<linkname2>=<n2>|.... 
			//                               This is optional and blank by default. 
			//                               If ea link's top value needs to be controlled individually (as is the case 
			//                               for linkboxes, which can have different heights), concatenate a pipe-delimited 
			//                               list like:
			//                                   LNK_involves_US=5|LNK_Involves_CN=10
			//                               and the rowset will have that many linked recs in ea of those links. 
			//                               iLinksTop value doesn't override this parameter. 
			//                               For example, if iLinksTop=3 and sLinksTop has LNK_Connected_OP=10, 
			//                               the Connected Opp link will return 10, but if LNK_Connected_OP were not
			//                               defined in this parameter, Connected Opp link would return 3.
			//       par_bNoGenerateSysName: When set to True, causes Commit to bypass goScr.GenerateSysName. 
			//                               WARNING: This will result in the record 'Name' being either blank or wrong.
			//                               Use this only when you plan to generate SYS_Names manually from
			//                               Admin>Import (ImportTasks.aspx).
			//       par_bSysFileFullPermissions: When true, the rowset on system files (as determined by goData.IsFileSystem)
			//                               is executed with full Read, Add, Edit, and Delete permission, allowing 
			//                               programmatic access to those files under regular user logins. Logins with admin and 
			//                               full author permissions automatically have R-A-E-D permissions on system files
			//                               regardless of this parameter.
			//       par_iGetDataTimeout:     SQL Timeout for GetData call
			//
			//DEFAULT VALUES IN ADD ROWSETS:
			//       When par_iType = SELL_ADD (2), field default values are taken from:
			//       - Code:
			//           - GID_ID is generated
			//           - SI__ShareState is set to 2
			//           - SYS_Name is generated as part of Commit unless 
			//               the rowset is instantiated with par_bNoGenerateSysName = True.
			//           - dtt_creationtime is set to goTR.NowUTC()
			//           - LNK_CreatedBy_US is set to goP.GetUserTID() [current user's GID_ID)
			//           - SQL Server default values:
			//               - BI__ID (this is a SQL counter)
			//               - DTT_ModTime is set to (getutcdate())
			//               - Possibly other values, look at SS schema with SSMS or VS
			//       - FLD_XX metadata (XX is file name):
			//           - _DEF= lines: MLS_STATUS_DEF=0 sets 0 in the Status combo (MLS_STATUS must be in FIELDSWDEFS=).
			//           - LINKMELINKS=
			//       - CRU_XX metadata (where XX is file name): MLS_STATUS=3 or MLS_STATUS=Open sets Status to either the 
			//           index value or label, as defined in FLD_ metadata (MLS_STATUS must be in FIELDS=).
			//       - CRL_XX metadata (where XX is file name of the record being created): MLS_STATUS=<%MLS_Status%> sets the Status
			//           to the value of the Status field of the selected record in any file  (MLS_STATUS must be in FIELDS=).
			//       If form is involved:
			//       - clScripts.XX_FormOnLoadRecord (XX is file name)
			//       For more information on all supported sources of MLS values, see the Knowledgebase Activity
			//       'Setting MLS values in CRL_ and CRU_ metadata' in the Selltis company database.
			//REQUIRED VALUES IN ADD ROWSETS:
			//       When par_iType = SELL_ADD (2), values must be provided for fields defined in FLD_XX (where XX is file name)
			//           in FIELDSREQUIRED=.
			//
			//RETURNS:
			//		Nothing
			//AUTHOR: RH

			string sProc = "clRowset::New";
			string sSQLString = "";

			//Try
			Initialize();
			//**********************************************************
			//Log start and parameters
			string sParams = "";
			//MI 4/9/08 Changed += to &= in this block
			sParams += "par_sTableName: '" + par_sTableName + "'" + "\r\n";
			sParams += "par_iType: '" + par_iType.ToString() + "'" + "\r\n";
			sParams += "par_sCondition: '" + par_sCondition + "'" + "\r\n";
			sParams += "par_sSort: '" + par_sSort + "'" + "\r\n";
			sParams += "par_sFields: '" + par_sFields + "'" + "\r\n";
			sParams += "par_iTop: '" + par_iTop.ToString() + "'" + "\r\n";
			sParams += "par_sINI: '" + par_sINI + "'" + "\r\n";
			sParams += "par_sGenFieldsDefs: '" + par_sGenFieldsDefs + "'" + "\r\n";
			sParams += "par_sCreateType: '" + par_sCreateType + "'" + "\r\n";
			sParams += "par_sCreateFromID: '" + par_sCreateFromID + "'" + "\r\n";
			sParams += "par_sTopRecord: '" + par_sTopRecord + "'" + "\r\n"; //*** MI 9/12/06
			sParams += "par_bGetAllUsersUnsharedRecs: '" + goTR.CheckBoxToText(par_bGetAllUsersUnsharedRecs ? -1 : 0, true) + "'" + "\r\n"; //*** MI 4/9/08
			sParams += "par_bReverseSort: '" + goTR.CheckBoxToText(par_bReverseSort ? -1 : 0, true) + "'" + "\r\n"; //MI 10/10/08 added
			sParams += "par_iLinksTop: '" + par_iLinksTop.ToString() + "'" + "\r\n"; //MI 10/10/08 added
			sParams += "par_sLinksTop: '" + par_sLinksTop + "'" + "\r\n"; //MI 10/10/08 added
			sParams += "par_bNoGenerateSysName: '" + goTR.CheckBoxToText(par_bNoGenerateSysName ? -1 : 0, true) + "'" + "\r\n"; //MI 10/15/08
			sParams += "par_bSysFileFullPermissions: '" + goTR.CheckBoxToText(par_bSysFileFullPermissions ? -1 : 0, true) + "'" + "\r\n"; //MI 3/17/09

			goLog.Log(sProc, "Start" + "\r\n" + sParams, (short)clC.SELL_LOGLEVEL_DETAILS, true);

			// rh 3/13/13 Added rowset new logging with calling method for debug
			if (goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "LOGROWSET", "0") == "1")
			{
				string sCallingMetod = null;
				System.Diagnostics.StackFrame stackframe = new System.Diagnostics.StackFrame(1);
				sCallingMetod = stackframe.GetMethod().Name;
				goLog.Log(sProc, "Calling Method: " + sCallingMetod + "\r\n" + "\r\n" + sParams, 0, false, false, 0, 40);
			}

			//**********************************************************
			// Prep Fields list double hop fields

			string[] sFLDs = null;
			int n = 0;
			string sWrkStr = "";

			sFLDs = par_sFields.ToUpper().Split(',');

			for (n = sFLDs.GetLowerBound(0); n <= sFLDs.GetUpperBound(0); n++)
			{

				if ((sFLDs[n].IndexOf("%%LNK") + 1) != 0)
				{
					sWrkStr = sWrkStr + "," + goTR.ExtractString(sFLDs[n], 1, "%%");
				}
				else
				{
					sWrkStr = sWrkStr + "," + sFLDs[n];
				}
			}

			if (sWrkStr.Substring(0, 1) == ",")
			{
				par_sFields = sWrkStr.Substring(1);
			}

			//**********************************************************

			//Manage sIntermediate
			if (par_sINI != "")
			{
				sIntermediate = par_sINI;
				//MI 10/15/09 Substitute view-syntax properties if needed
				if (goTR.StrRead(sIntermediate, "TABLENAME", "", false) == "")
				{
					goTR.StrWrite(ref sIntermediate, "TABLENAME", goTR.StrRead(sIntermediate, "FILE", "", false));
				}
				if (goTR.StrRead(sIntermediate, "TOP", "-1", false) == "-1")
				{
					goTR.StrWrite(ref sIntermediate, "TOP", goTR.StrRead(sIntermediate, "SHOWTOP", "-1", false));
				}
				//Remove view-syntax properties to eliminate ambiguity
				goTR.StrDelete(ref sIntermediate, "FILE");
				goTR.StrDelete(ref sIntermediate, "SHOWTOP");
				//MI 10/15/09 End Substitute view-syntax properties if needed
			}
			else
			{
				goTR.StrWrite(ref sIntermediate, "TABLENAME", par_sTableName);
				goTR.StrWrite(ref sIntermediate, "TYPE", par_iType.ToString());
				goTR.StrWrite(ref sIntermediate, "FIELDS", par_sFields);
				goTR.StrWrite(ref sIntermediate, "CONDITION", par_sCondition);
				goTR.StrWrite(ref sIntermediate, "SORT", par_sSort);
				if (par_iTop != -1)
				{
					goTR.StrWrite(ref sIntermediate, "TOP", par_iTop.ToString());
				}
				goTR.StrWrite(ref sIntermediate, "CREATETYPE", par_sCreateType);
				goTR.StrWrite(ref sIntermediate, "CREATEFROMID", par_sCreateFromID);
				if (sIntermediate.Substring(sIntermediate.Length - 2) != "\r\n")
				{
					sIntermediate += "\r\n";
				}
				sIntermediate += par_sGenFieldsDefs;
				//*** MI 9/12/06 Added section below
				if (par_sTopRecord != "")
				{
					sIntermediate = goTR.TrimChars(sIntermediate, "\r\n", "R");
					sIntermediate += "\r\n" + par_sTopRecord; //*** MI 9/13/06
				}
				//*** MI 9/12/06 Added section above
				//*** MI 4/9/08
				goTR.StrWrite(ref sIntermediate, "GETALLUSERSUNSHAREDRECS", goTR.CheckBoxToText(par_bGetAllUsersUnsharedRecs ? -1 : 0, true));
			}

			//**********************************************************
			//If TableName or Type are not in sIntermediate, then write values from first two params.
			if (goTR.StrRead(sIntermediate, "TABLENAME", "", false) == "")
			{
				goTR.StrWrite(ref sIntermediate, "TABLENAME", par_sTableName);
			}
			if (goTR.StrRead(sIntermediate, "TYPE", "", false) == "")
			{
				goTR.StrWrite(ref sIntermediate, "TYPE", par_iType.ToString());
			}

			//**********************************************************
			//Force loading of all fields and links for edit rowset
			string sFields2 = goTR.StrRead(sIntermediate, "FIELDS", null, false);
			bool bNoMDLinks = false;
			if (sFields2.ToUpper() == "NOLINKS")
			{
				bNoMDLinks = true;
			}

			if (sFields2 == "")
			{
				sFields2 = "*";
			}

			if (sFields2.ToUpper() == "NOLINKS")
			{
				sFields2 = "*";
			}
			else
			{
				if (sFields2.IndexOf("*") + 1 == 0)
				{
					sFields2 = "*," + sFields2;
				}
			}

			if (sFields2.IndexOf("***") + 1 > 0)
			{
				sFields2 = sFields2.Replace("***", "*");
				bNoMDLinks = true;
			}

			if (bNoMDLinks == false)
			{
				sFields2 = sFields2 + "," + goMeta.LineRead("GLOBAL", "OTH_FILES", "AUTOLOAD_" + goTR.StrRead(sIntermediate, "TABLENAME", ""), "*", true);
				sFields2 = sFields2.Replace(",,", ",");
			}

			if (par_iType == clC.SELL_EDIT) //MI 12/10/09 changed from If par_iType = 1...
			{
				goTR.StrWrite(ref sIntermediate, "FIELDS", sFields2);
			}
			//**********************************************************

			sSort = goTR.StrRead(sIntermediate, "SORT", null, false); //MI 1/11/10 Added

			//**********************************************************
			//Set RS variables equal to sIntermediate values
			if (goTR.StrRead(sIntermediate, "TYPE", "", false) == "")
			{
				iRSType = 0;
			}
			else
			{
				iRSType = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sIntermediate, "TYPE", "0", false), "0")); //MI 12/10/09 Added StringToNum.
			}
			sRSFile = goTR.StrRead(sIntermediate, "TABLENAME", "", false);
			sCreateType = goTR.StrRead(sIntermediate, "CREATETYPE", null, false);
			sCreateFromID = goTR.StrRead(sIntermediate, "CREATEFROMID", null, false);
			//**********************************************************
			//Set create type to CRU for this file if nothing else passed.
			if (sCreateType == "")
			{
				sCreateType = "CRU_" + sRSFile;
			}
			//**********************************************************

			if (goTR.StrRead(sIntermediate, "TOPREC_BI__ID", null, false) != "")
			{
				goTR.StrWrite(ref sIntermediate, "TOPREC_BI__ID", goTR.NumToString(goTR.StringToNum(goTR.StrRead(sIntermediate, "TOPREC_BI__ID", null, false)), "\t" + "\t"));
			}
			//Write MODE in COUNT and GROUPBY case
			switch (iRSType)
			{
				case clC.SELL_COUNT:
					goTR.StrWrite(ref sIntermediate, "MODE", "COUNT");
					break;
				case clC.SELL_GROUPBY:
					goTR.StrWrite(ref sIntermediate, "MODE", "GROUPBY");
					break;
				case clC.SELL_GROUPBYWITHROLLUP:
					goTR.StrWrite(ref sIntermediate, "MODE", "GROUPBYWITHROLLUP");
					break;
			}

			goTR.StrWrite(ref sIntermediate, "REVERSESORT", goTR.CheckBoxToText(par_bReverseSort ? -1 : 0, true)); //MI 10/10/08 added
			iLinksTop = par_iLinksTop; //MI 10/30/08 added
			goTR.StrWrite(ref sIntermediate, "LINKSTOP", par_iLinksTop.ToString()); //MI 10/10/08 added
			sLinksTopIni = goTR.Replace(par_sLinksTop, "|", "\r\n"); //MI 10/30/08 added
			goTR.StrWrite(ref sIntermediate, "LINKSTOPLIST", par_sLinksTop); //MI 10/10/08 added
			goTR.StrWrite(ref sIntermediate, "SYSFILEFULLPERMISSIONS", par_bSysFileFullPermissions);


			//----------------- GetData ----------------


			switch (iRSType)
			{
				case clC.SELL_EDIT:
				case clC.SELL_READONLY:
				case clC.SELL_GROUPBY:
				case clC.SELL_GROUPBYWITHROLLUP:
				case clC.SELL_COUNT:
					sSQLString = goData.GenerateSQL(sIntermediate);
					GetData(sSQLString, par_iGetDataTimeout, par_sTableName);
					break;
				case clC.SELL_ADD:
					par_sTableName = goTR.StrRead(sIntermediate, "TABLENAME", null, false);
					//Reset the variable
					sIntermediate = "";
					//Build a new sIntermediate variable
					goTR.StrWrite(ref sIntermediate, "TABLENAME", par_sTableName);
					goTR.StrWrite(ref sIntermediate, "FIELDS", "**");
					goTR.StrWrite(ref sIntermediate, "TOP", "0");
					sSQLString = goData.GenerateSQL(sIntermediate);
					GetData(sSQLString, par_iGetDataTimeout, par_sTableName);
					SeedRecord();
					break;
				case clC.SELL_READONLY_ALL:
					sSQLString = goData.GenerateSQL(sIntermediate);
					string sreplaceTop = "Top " + par_iTop.ToString();
					sSQLString = sSQLString.Replace(sreplaceTop, "");
					GetData(sSQLString, par_iGetDataTimeout, par_sTableName);
					break;
				default:
					goErr.SetError(46170, sProc,"", iRSType.ToString());
					break;
					//Invalid rowset type
			}
			//**********************************************************

			bBypassValidation = par_bBypassValidation;
			bNoRecordOnSave = par_bNoRecordOnSave;
			bNoGenerateSysName = par_bNoGenerateSysName;

			goLog.Log(sProc, "sIntermediate: " + "\r\n" + sIntermediate, (short)clC.SELL_LOGLEVEL_DEBUG, false);
			goLog.Log(sProc, "Exit", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			bRSIsDirty = false;

			// Dim ds As DataSet = oDataSet    'Inspect oDataset to see underlying dataset from SQL
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45101, sProc)
			//    End If
			//End Try

		}


		public bool ToTable(string sSubstituteFields = "", string sSystemValFields = "", bool bOverideStartStop = false, int startCounter = 0, int recordCount = 0)
		{
			//RH 11/12/09 mod.
			//PURPOSE:
			//   Creates datatable, dtTransTable, based on field list. Processes generated fields, etc.
			//   MI 1/7/10: All columns are of string type. The purpose of this is to have a table of data
			//   that can be bound to a grid control,.
			//PARAMETERS:
			//  None
			//RETURNS:
			//  True if successful, errors if not.
			//AUTHOR: RH

			string sProc = "clRowset::ToTable";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//Try

			Stopwatch sTimer = new Stopwatch();
			sTimer.Start();

			//'Only Read rowsets are transformable
			//MI 1/21/10 If uncommenting this ever, be sure to add testing GROUPBY, GROUPBYWITHROLLUP and COUNT iRSTypes
			//If iRSType <> clC.SELL_READONLY Then Return False

			DataTable dt = new DataTable();
			DataColumn dc = null;
			DataRow dr = null;
			int i = 0;
			int j = 0;
			int k = 0;
			string[] sFields = new string[1];
			string[] sSysFields = new string[1];
			string sGenLine = "";
			Stopwatch sw = new Stopwatch();
			string[] sLinks = new string[1];
			string sLink1 = "";
			string sLink2 = "";
			string[] sSortFields = new string[1];
			string sTemp = null;
			clArray aSortFields = null;
			clArray aFields = null;

			if (sSubstituteFields == "")
			{
				sFields = goTR.StrRead(sIntermediate, "FIELDS", null, false).Split(',');
				//MI 1/11/10 Added the following block
				//Add sort fields to fields in GROUPBY and SELL_GROUPBYWITHROLLUP modes
				if (this.iRSType == clC.SELL_GROUPBY || iRSType == clC.SELL_GROUPBYWITHROLLUP)
				{
					sSortFields = sSort.Split(',');
					aSortFields = new clArray();
					aFields = new clArray();
					//Load all fields
					for (i = sFields.GetLowerBound(0); i <= sFields.GetUpperBound(0); i++)
					{
						aFields.AddInfo(sFields[i].Trim(' '));
					}
					//Load all sort fields
					for (i = sSortFields.GetLowerBound(0); i <= sSortFields.GetUpperBound(0); i++)
					{
						//Remove ASC or DESC from the sort field definition
						sTemp = goTR.ExtractString(sSortFields[i].Trim(' '), 1, " ");
						if (sTemp[0] == clC.EOT || sTemp == "")
						{
							//Blank sort field, skip 
						}
						else
						{
							aSortFields.AddInfo(sTemp);
						}
					}
					//If the sort field doesn't exist in the list of fields, add it
					for (i = 1; i <= aSortFields.GetDimension(); i++)
					{
						if (aFields.SeekInfo(aSortFields.GetInfo(i), true, false) == 0)
						{
							Array.Resize(ref sFields, sFields.GetUpperBound(0) + 2);
							sFields[sFields.GetUpperBound(0)] = aSortFields.GetInfo(i);
							if (iRSType == clC.SELL_GROUPBYWITHROLLUP)
							{
								//Also add INT_<FieldName>_GROUPING field (SELL_GROUPBYWITHROLLUP mode only)
								Array.Resize(ref sFields, sFields.GetUpperBound(0) + 2);
								sFields[sFields.GetUpperBound(0)] = "INT_" + aSortFields.GetInfo(i) + "_GROUPING";
							}
						}
					}

				}
			}
			else
			{
				sFields = sSubstituteFields.Split(' ');
			}

			//Remove leading and trailing spaces
			for (i = sFields.GetLowerBound(0); i <= sFields.GetUpperBound(0); i++)
			{
				sFields[i] = sFields[i].Trim(' ');
			}

			//Create the columns

			for (i = sFields.GetLowerBound(0); i <= sFields.GetUpperBound(0); i++)
			{
				dc = new DataColumn(sFields[i], typeof(string));
				if (sFields[i] != "**")
				{

					try
					{
						dt.Columns.Add(dc);
					}
					catch (Exception exs)
					{
					}

				}
			}

			//Create system value fields.. SYS_

			if (sSystemValFields != "")
			{

				sSysFields = sSystemValFields.Split(',');

				//Remove leading and trailing spaces
				for (i = sSysFields.GetLowerBound(0); i <= sSysFields.GetUpperBound(0); i++)
				{
					sSysFields[i] = sSysFields[i].Trim(' ');
				}

				//Create the columns
				for (i = sSysFields.GetLowerBound(0); i <= sSysFields.GetUpperBound(0); i++)
				{
					dc = new DataColumn("SYS_" + sSysFields[i], typeof(string));
					if (sSysFields[i] != "**")
					{
						try
						{
							dt.Columns.Add(dc);
						}
						catch (Exception exs)
						{
						}
					}
				}
			}

			if (Count() == 0) //No data to transform. Return empty table.
			{
				dtTransTable = dt;
				return true;
			}

			//Add rows

			//V_T 4/20/2015 , optimizing the ToTable() function

			int startFor = 0;
			int endFor = 0;

			if (bOverideStartStop)
			{
				startFor = startCounter;

				endFor = startCounter + recordCount;
				if (endFor >= Count())
				{
					endFor = (int)Count();
				}
				endFor = endFor - 1;
			}
			else
			{
				startFor = 0;
				endFor = (int)(Count() - 1);
			}

// INSTANT C# NOTE: There is no C# equivalent to VB's implicit 'once only' variable initialization within loops, so the following variable declaration has been placed prior to the loop:
			clArray oArray = null;
			for (i = startFor; i <= endFor; i++) //For i = 0 To Count() - 1
			{

				if ((i + 1) % 5000 == 0)
				{
					GC.Collect();
					GC.WaitForPendingFinalizers();
				}

				dr = dt.NewRow();
				lCurrentRow = i;
				for (j = sFields.GetLowerBound(0); j <= sFields.GetUpperBound(0); j++)
				{

                    //if (sFields[j].ToUpper().Substring(0, 3) == "GEN")
                    if (sFields[j].ToUpper().Length >= 3 && sFields[j].ToUpper().Substring(0, 3) == "GEN")
                    {
                        // Your logic here                   
                        sGenLine = goTR.StrRead(sIntermediate, sFields[j], null, false);
					   }
					else
					{
						sGenLine = sFields[j];
					}


					if (sGenLine != "")
					{
						if (goTR.IsFieldCombined(sGenLine, true) == false)
						{

							sGenLine = sGenLine.Replace("<%", "");
							sGenLine = sGenLine.Replace("%>", "");

							if (sFields[j] == "**")
							{
							}
							else
							{
								dr[sFields[j]] = GetFieldVal(sGenLine, clC.SELL_FRIENDLY);
							}

						}
						else if (sGenLine.IndexOf("#$%^&*") + 1 > 0)
						{

							sLinks = Microsoft.VisualBasic.Strings.Split(sGenLine, "#$%^&*");


							if (sLinks[0].IndexOf(" ") + 1 == 0)
							{
								sLinks[0] = sLinks[0].Replace("<%", "");
								sLinks[0] = sLinks[0].Replace("%>", "");
								sLink1 = Convert.ToString(GetFieldVal(sLinks[0], clC.SELL_FRIENDLY));
							}
							else
							{
								sLinks[0] = sLinks[0].Replace("<%", "");
								sLinks[0] = sLinks[0].Replace("%>", "");
								object tempVar = this;
								sLink1 = goTR.GetLineValue(ref sLinks[0], ref sRSFile, "", false, ref tempVar);
							}
							if (sLinks[1].IndexOf(" ") + 1 == 0)
							{
								sLinks[1] = sLinks[1].Replace("<%", "");
								sLinks[1] = sLinks[1].Replace("%>", "");
								sLink2 = Convert.ToString(GetFieldVal(sLinks[1], clC.SELL_FRIENDLY));
							}
							else
							{
								sLinks[1] = sLinks[1].Replace("<%", "");
								sLinks[1] = sLinks[1].Replace("%>", "");
								object tempVar2 = this;
								sLink2 = goTR.GetLineValue(ref sLinks[1], ref sRSFile, "", false, ref tempVar2);
							}

							dr[sFields[j]] = sLink1 + "#$%^&*" + sLink2;

							sLink1 = "";
							sLink2 = "";


						}
						else
						{
							if (sGenLine == "**")
							{
							}
							else
							{
								object tempVar3 = this;
								dr[sFields[j]] = goTR.GetLineValue(ref sGenLine, ref sRSFile, "", false, ref tempVar3);
							}

						}
					}
				}

	//			Dim oArray As clArray

				if (sSystemValFields != "")
				{
					for (k = sSysFields.GetLowerBound(0); k <= sSysFields.GetUpperBound(0); k++)
					{
						if (sSysFields[k].ToUpper().Substring(0, 3) == "LNK")
						{
							if (GetLinkTable(sSysFields[k]) == 0)
							{
								oArray = (Selltis.BusinessLogic.clArray)GetFieldVal(sSysFields[k], clC.SELL_SYSTEM);

								if (oArray.GetDimension() > 0)
								{
									dr["SYS_" + sSysFields[k]] = oArray.GetItem(1).ToString();
								}


							}
							else
							{
								dr["SYS_" + sSysFields[k]] = "";
							}
						}
						else
						{
							dr["SYS_" + sSysFields[k]] = GetFieldVal(sSysFields[k], clC.SELL_SYSTEM).ToString();
						}
					}
				}
				dt.Rows.Add(dr);
			}

			//Set public table object = to transformed table
			dt.TableName = this.sRSFile;
			dtTransTable = dt;

			sTimer.Stop();
			int m = (int)sTimer.ElapsedMilliseconds;


			return true;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45103, sProc)
			//    End If

			//End Try


		}
		public int ClearLink(string sLinkName, object oLinks)
		{

			//PURPOSE:
			//       Clears one or more links 		
			//PARAMETERS:
			//		sLinkName:  Name of the link field to clear
			//       oLinks: Either clArray of links or hard return delimited string of link IDs 
			//RETURNS:
			//		1 if successful, errors if not.
			//AUTHOR: RH

			string sProc = "clRowset::ClearLink";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//MI 1/21/10 Disallow all non-editable types (= clc.SELL_READONLY was tested)
			switch (iRSType)
			{
				case clC.SELL_EDIT:
				case clC.SELL_ADD:
				break;
					//OK
				default:
					goErr.SetError(45161, sProc);
					break;
			}

			//Try
			if (IsLoaded(sLinkName))
			{

				this.bRSIsDirty = true;

				int i = 0;
				int iTable = Convert.ToInt32(cLoadedLinks[(sLinkName + "%%GID_ID").ToUpper()]);
				clArray oclArray = new clArray();

				//Test if a clArray is passed
				if (oLinks.GetType().ToString().ToLower().Contains("clarray"))
				{
					oclArray = (Selltis.BusinessLogic.clArray)oLinks;
				}
				else
				{
					oclArray.StringToArray(Convert.ToString(oLinks));
				}

				for (i = 1; i <= oclArray.GetDimension(); i++)
				{
					DeleteLink(iTable, sLinkName, oclArray.GetInfo(i));
				}

				return 1;

			}
			else
			{
				goErr.SetError(45160, sProc, "", sLinkName);

			}

			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return 0;
		}
		public int ClearLinkAll(string sLinkName)
		{
			//MI 9/27/07 Edited comments.
			//PURPOSE:
			//       Clears all links 		
			//PARAMETERS:
			//		sLinkName:  Name of the link field to clear
			//RETURNS:
			//		Integer: 1 if successful, 0 and errors if not.
			//AUTHOR: RH

			string sProc = "clRowset::ClearLinkAll";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//MI 1/21/10 Disallow all non-editable types (= clc.SELL_READONLY was tested)
			switch (iRSType)
			{
				case clC.SELL_EDIT:
				case clC.SELL_ADD:
				break;
					//OK
				default:
					goErr.SetError(45161, sProc);
					break;
			}

			//Try
			if (IsLoaded(sLinkName))
			{

				this.bRSIsDirty = true;

				int i = 0;
				int iTable = Convert.ToInt32(cLoadedLinks[(sLinkName + "%%GID_ID").ToUpper()]);

				DeleteLink(iTable, sLinkName, "");
				return 1;

			}
			else
			{
				goErr.SetError(45160, sProc, "", sLinkName);
			}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return 0;
		}

		public bool ValidateRecord()
		{

			//MI 9/11/08 Added support for FAILVALIDATION property in LST_ metadata.

			//PURPOSE:
			//       Tests whether all required fields have values		
			//PARAMETERS:
			//		None
			//RETURNS:
			//		true if valid, false is not (warning is set)  
			//AUTHOR: RH

			string sProc = "clRowset::ValidateRecord";

			//bNoRecordOnSave does not affect metadata validation
			if (bBypassValidation)
			{
				return true;
			}


			string sFields = "";
			string sFieldName = "";
			string sValue = "";

			int i = 0;
			string sPrefix = "";

			string sMeta = goMeta.PageRead("GLOBAL", "FLD_" + this.sRSFile, "", true);
			var sDef = goTR.StrRead(sMeta, "FIELDSREQUIRED", "", false);

			string[] sArrReqFields = sDef.Split('|');

			clList oList = new clList(); //MI 9/11/08 Added

			//Try

			for (i = sArrReqFields.GetLowerBound(0); i <= sArrReqFields.GetUpperBound(0); i++)
			{

				sFieldName = sArrReqFields[i];
				if (sFieldName == "")
				{
					goto moveon;
				}



				sPrefix = sFieldName.Substring(0, 3).ToUpper();
				sValue = Convert.ToString(GetFieldVal(sFieldName, clC.SELL_FRIENDLY));


				switch (sPrefix)
				{
					case "CHK":
					case "CMB":
					case "DR_":
					case "INT":
					case "LI_":
					case "BI_":
					case "LST":
					case "SEL":
					case "SI_":
					case "SR_":
						if (sValue == "0")
						{
							goErr.SetWarning(47260, sProc, "", sFieldName);
							return false;
						}
						break;

					case "CUR":
						//VS 04142016 : sVlue returning $0 fixed this using dValue below
						double dValue = Convert.ToDouble(GetFieldVal(sFieldName, clC.SELL_SYSTEM));
						if (sValue == "0" || dValue == 0)
						{
							goErr.SetWarning(47260, sProc, "", sFieldName);
							return false;
						}
						break;

					case "MLS":
						//If GetFieldVal(sFieldName, clC.SELL_SYSTEM) = 0 Then       'MI 9/11/08 Commented
						//MI 9/11/08 Added the following line to support FAILVALIDATION= property in LST_ MD for required fields.
						if (Convert.ToInt32(GetFieldVal(sFieldName, clC.SELL_SYSTEM)) == oList.GetFailValidationIndex(sRSFile, goTR.FromTo(sFieldName, 5, -1)))
						{
							goErr.SetWarning(47260, sProc, "", sFieldName);
							return false;
						}
						break;


					case "DTE":
					case "TME":
					case "TML":
					case "DTT":
						if (sValue == clC.SELL_BLANK_DATETIME)
						{
							goErr.SetWarning(47260, sProc, "", sFieldName);
							return false;
						}

						if (sValue == "")
						{
							goErr.SetWarning(47260, sProc, "", sFieldName);
							return false;
						}
						break;


					case "LNK":

						if (GetLinkCount(sFieldName) == 0)
						{
							goErr.SetWarning(47260, sProc, "", sFieldName);
							return false;
						}
						break;


					default:
						if (sValue == "")
						{
							goErr.SetWarning(47260, sProc, "", sFieldName);
							return false;
						}
						break;

				}

	moveon: ;
			}

			return true;

			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If

			//End Try


		}
		public int Commit(bool par_bRetryIfConcurrencyFailure = true)
		{
			//MI 12/10/09 Disallowing SELL_GROUPBY, SELL_GROUPBYWITHROLLUP, SELL_COUNT.
			//RH 5/1/09 Added using dtMerge to allow having different values on disk and in the datatable
			//       that clSQL.GenerateSQL returns as a result of observing permissions on linked files.
			//MI 12/10/08 Changed logging to SELL_LOGLEVEL_DETAILS.
			//MI 10/15/08 conditionalized running GenerateSysName.
			//MI 2/20/07 Added bBypassValidation on line 526: Dim NewRS As New clRowSet(sRSFile, 1, "GID_ID='" & sBaseGID & "'", , , , , , , , , bBypassValidation)
			//PURPOSE:
			//       Commits the current record changes to database		
			//PARAMETERS:
			//		None
			//RETURNS:
			//		1 if successful, 0 if not.
			//AUTHOR: RH

			int k = 0;
			string sRealName = "";
			bool bDelete = false;
			string sColumn = "";
			string sID = "";
			bool bIsDelete = false;

			//************   START LOGGING   ************************
			//goLog.Log 
			string sProc = "clRowset::Commit";

			//MI 1/21/10 Disallow all non-editable types (non-editable types were tested explicitly until this change)
			switch (iRSType)
			{
				case clC.SELL_EDIT:
				case clC.SELL_ADD:
				break;
					//OK
				default:
					goLog.Log(sProc, "Start (" + sRSFile + ") type: '" + iRSType.ToString() + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);
					goErr.SetError(45161, sProc);
					break;
			}

			if (oDataTable.Rows[(int)lCurrentRow].RowState != DataRowState.Deleted)
			{

				sBaseGID = oDataTable.Rows[(int)lCurrentRow]["GID_ID"].ToString();
				sID = sBaseGID;
				goLog.Log(sProc, "Start (" + sRSFile + ") type: '" + iRSType.ToString() + "' ID: '" + sID + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);
			}
			else
			{
				bIsDelete = true;
				goLog.Log(sProc, "Start (" + sRSFile + ") type: '" + iRSType.ToString() + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);
			}
			//************   END LOGGING   ************************
			//************   START PERMISSION CHECK   ************************
			if (this.iRSType == clC.SELL_ADD)
			{

				if (goData.GetAddPermission(this.sRSFile) == false)
				{
					goErr.SetWarning(47251, sProc, "Permissions denied adding record '" + sBaseSYSNAME + " (" + sBaseGID + ")");
					return 0;
				}

			}
			else if (this.iRSType == clC.SELL_EDIT)
			{
				if (oDataTable.Rows[(int)lCurrentRow].RowState != DataRowState.Deleted)
				{
					if (goData.GetRecordPermission(sBaseGID, "E") == false)
					{
						goErr.SetWarning(47250, sProc, "Permissions denied editing record '" + sBaseSYSNAME + " (" + sBaseGID + ")");
						return 0;
					}
				}
			}
			//************   END PERMISSION CHECK    ************************

			try
			{

				if (oDataTable.Rows[(int)lCurrentRow].RowState != DataRowState.Deleted)
				{
					//Skip these prep tasks as deleted records can't be edited in the datatable
					//**********************************************************
					//Run Validate, RecordOnSave script, Validate again
					if (ValidateRecord() == false)
					{
						return 0;
					}
					if (bNoRecordOnSave == false)
					{
						long lPrevCurrent = lCurrentRow;
						object tempVar = this;
						bool bRecSave = goScr.RunScript(sRSFile + "_RecordOnSave", ref tempVar);
						lCurrentRow = lPrevCurrent;
						if (bRecSave == false)
						{
							return 0;
						}
					}
					if (ValidateRecord() == false)
					{
						return 0;
					}
					//**********************************************************
					//Generate SYS_NAME
					string sReturnValue = "";
					if (!bNoGenerateSysName) //MI 10/15/08 added
					{
						object tempVar2 = this;
						object temp_sReturnValue = sReturnValue;
						if (goScr.RunScript("GenerateSysName", ref tempVar2, null, "", "", "", "", "", ref temp_sReturnValue))
						{
								sReturnValue = Convert.ToString(temp_sReturnValue);
							SetFieldVal("SYS_Name", sReturnValue);
						}
						else
						{
								sReturnValue = temp_sReturnValue.ToString();
							goErr.SetError(25000, sProc);
							//25000: GenerateSysName failed: Unknown error.
						}
					} //MI 10/15/08 added

					SYS_NAME_LastCommit = Convert.ToString(GetFieldVal("SYS_NAME", clC.SELL_FRIENDLY));

					//**********************************************************
					//'Set modified by and date fields if the are loaded/exist
					if (IsLoaded("TXT_ModBy"))
					{
						SetFieldVal("TXT_ModBy", goP.GetUserCode(), clC.SELL_FRIENDLY);
					}
					if (IsLoaded("DTT_ModTime"))
					{
						oDataTable.Rows[(int)lCurrentRow]["DTT_ModTime"] = goTR.NowUTC(); //*** MI 9/27/07
					}
					//**********************************************************
				}
				//**********************************************************
				// Get Connection
				System.Data.SqlClient.SqlConnection connection = goData.GetConnection();
				System.Data.SqlClient.SqlDataAdapter adapter = new System.Data.SqlClient.SqlDataAdapter("Select * From [" + sRSFile + "] WHERE GID_ID='" + sBaseGID + "'", connection);
				System.Data.SqlClient.SqlCommandBuilder builder = new System.Data.SqlClient.SqlCommandBuilder(adapter);

				builder.QuotePrefix = "[";
				builder.QuoteSuffix = "]";
				//**********************************************************

				//**********************************************************
				//Put row to be commited into oTempTable, convert LNK to GID, 
				//Set null DTT to SELL_BLANK_DATETIME, remove non-db bound columns
				DataTable oTempTable = oDataTable.Clone();
				oTempTable.ImportRow(oDataTable.Rows[(int)lCurrentRow]);

				for (k = 0; k < oTempTable.Columns.Count; k++)
				{
					//Rename LNK fields to GID
					if (oTempTable.Columns[k].ColumnName.StartsWith("LNK"))
					{
						if (oTempTable.Columns[k].ColumnName.EndsWith("GID_ID"))
						{
							sRealName = oTempTable.Columns[k].ColumnName;
							sRealName = goTR.Replace(sRealName, "LNK_", "GID_");
							sRealName = goTR.ExtractString(sRealName, 1, "%%");
							oTempTable.Columns[k].ColumnName = sRealName;
						}
					}

					if (oDataTable.Rows[(int)lCurrentRow].RowState != DataRowState.Deleted)
					{
						//Set all null DTT to SELL_BLANK_DATETIME
						if (oTempTable.Columns[k].ColumnName.StartsWith("DTT"))
						{
							if (oDataTable.Rows[(int)lCurrentRow][k].GetType().ToString() == "System.DBNull")
							{
								oDataTable.Rows[(int)lCurrentRow][k] = clC.SELL_BLANK_DTDATETIME;
							}
						}
					}

				}

				//Strip non-db bound columns here
				for (k = oTempTable.Columns.Count - 1; k >= 0; k--)
				{
					if (goData.IsFieldVirtualInDB(oTempTable.Columns[k].ColumnName)) //MI 11/5/09 added
					{
						//If sPrefix = "DTY" Or sPrefix = "DTQ" Or sPrefix = "DTM" Or sPrefix = "DTD" Then       'MI 11/5/09 commented
						oTempTable.Columns.Remove(oTempTable.Columns[k]);
					}
				}
				//**********************************************************

				if (iRSType == clC.SELL_EDIT) //Edit rowset
				{

					if (oTempTable.Rows[0].RowState == DataRowState.Deleted)
					{
						bDelete = true;
					}

					//Replace Nulls with 'empty' values 
					LoadDefaultVals(ref oTempTable);

					try
					{

						DataTable dtMerge = new DataTable();
						int r = 0;
						adapter.Fill(dtMerge);

						if (bDelete == true)
						{
							dtMerge.Rows[0].Delete();
						}
						else
						{

							for (r = 0; r < oTempTable.Columns.Count; r++)
							{

								if (oTempTable.Rows[0][r, DataRowVersion.Original].Equals(oTempTable.Rows[0][r, DataRowVersion.Current]))
								{
								}
								else
								{
									sColumn = oTempTable.Columns[r].ColumnName;
									if (ColumnIsInTable(sColumn, dtMerge))
									{
										dtMerge.Rows[0][sColumn] = oTempTable.Rows[0][r, DataRowVersion.Current];
									}

								}
							}
						}

						adapter.Update(dtMerge);

					}
					catch (DBConcurrencyException ex)
					{

						HandleConcurrency(ref oTempTable, par_bRetryIfConcurrencyFailure);


					}

					connection.Close();
					oDataTable.Rows[(int)lCurrentRow].AcceptChanges(); //Resets the object�s state to unchanged by accepting the modifications.

					oTempTable = null;
					CommitLinks(sBaseGID);

					//RenameGIDFieldsToLNK()

				}
				else if (iRSType == clC.SELL_ADD) //Add rowset
				{


					LoadDefaultVals(ref oTempTable);
					adapter.Fill(oTempTable);
					adapter.Update(oTempTable);
					connection.Close();



					CommitLinks(sBaseGID); //call with 2nd param true to clear link tables after commiting
					oDataTable.Rows[(int)lCurrentRow].AcceptChanges();
					this.iRSType = clC.SELL_EDIT;

					//RenameGIDFieldsToLNK()

				}

				if (bIsDelete == false)
				{
					object tempVar3 = this;
					if (goScr.RunScript(sRSFile + "_RecordAfterSave", ref tempVar3) == false)
					{
						goErr.SetWarning(47270, sProc, "", sBaseGID, sRSFile);
						//The record after save script returned false for record [1] in file [2].  
					}
				}


				goLog.Log(sProc, "End (" + sRSFile + ") type: '" + iRSType.ToString() + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);
				return 1;


			}
			catch (System.Data.SqlClient.SqlException ex)
			{
				//RenameGIDFieldsToLNK()
				if (ex.Number == 2601 || ex.Number == 2627 || ex.Number == 3604)
				{
					goErr.SetWarning(46171, sProc, "", ex.Message);
					//  The commit failed due to a duplicate record.  The server returned the following error:

					//  '[1]'
					return 0;
				}
				else
				{
					goErr.SetError(ex, 45102, sProc);
				}

				//Catch ex As Exception
				//    'RenameGIDFieldsToLNK()
				//    'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    '    goErr.SetError(ex, 45102, sProc)
				//    'End If
			}


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return 0;
		}

		public void HandleConcurrency(ref DataTable oTempTable, bool par_bRetryIfConcurrencyFailure)
		{


			string sProc = "clRowset::HandleConcurrency";
			string sColumn = "";


			//Try

			//Test if record still exists in database

			if (goData.IsRecordValid(sBaseGID) == false)
			{

				//==> Change to recreate the record
				goErr.SetError(47176, sProc,"", GetFieldVal("SYS_NAME", clC.SELL_FRIENDLY).ToString(), sRSFile);
				//Unable to save the record '[1]', in the file '[2]', as it is no longer exists in the database.

				//RenameGIDFieldsToLNK
				return;

			}


			if (oTempTable.Rows[0].RowState == DataRowState.Deleted)
			{
				//Attempt Delete manually
				if (par_bRetryIfConcurrencyFailure == true)
				{
					if (goData.DeleteRecord(sRSFile, sBaseGID) == false)
					{
						goErr.SetError(47172, sProc, "", sBaseGID, sRSFile);
					}
				}
			}
			else
			{
				goData.NullifyInvalidN1Links(sBaseGID);

				//Reload record into new edit rs, set changed values, attempt to save again.
				if (par_bRetryIfConcurrencyFailure == true)
				{

					clRowSet NewRS = new clRowSet(sRSFile, 1, "GID_ID='" + sBaseGID + "'", "", "", -1, "", "", "", "", "", true, true);
					DataTable NewTempTable = new DataTable();
					int p = 0;
					NewTempTable = NewRS.oDataTable;

					for (p = 0; p < oTempTable.Columns.Count; p++)
					{
						if (oTempTable.Rows[0][p, DataRowVersion.Original].Equals(oTempTable.Rows[0][p, DataRowVersion.Current]))
						{
						}
						else
						{
							sColumn = oTempTable.Columns[p].ColumnName;
							if (sColumn.StartsWith("GID_"))
							{
								if (!(sColumn == "GID_ID"))
								{
									if (Instr2(oTempTable.Columns[p].ColumnName) > 0)
									{
										sColumn = goTR.Replace(sColumn, "GID_", "LNK_");
										sColumn += "%%GID_ID";
									}
								}
							}
							NewRS.oDataTable.Rows[0][sColumn] = oTempTable.Rows[0][p, DataRowVersion.Current];
						}
					}

					if (NewRS.Commit(false) == 0)
					{
						goErr.SetError(47172, sProc, "", sBaseGID, sRSFile);
					}
				}
				else
				{
					//Unable to edit record on second attempt.  Raise error.
					goErr.SetError(47172, sProc, "", sBaseGID, sRSFile);
				}

			}

			//Catch ex As Exception
			//    'RenameGIDFieldsToLNK()
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45102, sProc)
			//    End If
			//End Try

		}

		//Sub RenameGIDFieldsToLNK()

		//    Dim k As Integer
		//    Dim sRealName As String = ""


		//    For k = 0 To oDataTable.Columns.Count - 1
		//        'Rename fields GID to LNK
		//        If oDataTable.Columns(k).ColumnName.StartsWith("GID") Then
		//            If Not oDataTable.Columns(k).ColumnName.StartsWith("GID_ID") Then
		//                If Instr2(oDataTable.Columns(k).ColumnName) > 0 Then
		//                    sRealName = oDataTable.Columns(k).ColumnName
		//                    sRealName = goTR.Replace(sRealName, "GID_", "LNK_")
		//                    sRealName = sRealName & "%%GID_ID"
		//                    oDataTable.Columns(k).ColumnName = sRealName
		//                End If
		//            End If
		//        End If
		//    Next

		//End Sub

		public bool ColumnIsInTable(string sColumn, DataTable oDT)
		{

// INSTANT C# NOTE: Commented this declaration since looping variables in 'foreach' loops are declared in the 'foreach' header in C#:
//			Dim dc As DataColumn
			foreach (DataColumn dc in oDT.Columns)
			{
				if (sColumn.ToUpper() == dc.ColumnName.ToUpper())
				{
					return true;
				}
			}

			return false;

		}


		public int Instr2(string sSearchText)
		{


			int iPos1 = 0;
			int iPos2 = 0;

			iPos1 = sSearchText.IndexOf("_") + 1;
			sSearchText = sSearchText.Substring(iPos1);

			if (iPos1 > 0)
			{
				iPos2 = sSearchText.IndexOf("_") + 1;
			}

			return iPos2;



		}

		public long Count()
		{



			//PURPOSE:
			//       Returns the number of records in rowset	
			//PARAMETERS:
			//		None
			//RETURNS:
			//		Number of records
			//AUTHOR: RH

			string sProc = "clRowset::Count";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//Try
			return oDataTable.Rows.Count;
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try


		}
		public int DeleteRecord()
		{

			//PURPOSE:
			//       Deletes the current record and all of its links		
			//PARAMETERS:      	
			//       None
			//RETURNS:
			//		1 if successful, errors if not.
			//AUTHOR: RH

			string sProc = "clRowset::DeleteRecord";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//MI 1/21/10 Changed to testing editable types explicitly
			//If iRSType = clC.SELL_READONLY Then goErr.SetError(45161, sProc)
			switch (iRSType)
			{
				case clC.SELL_EDIT:
				case clC.SELL_ADD:
				break;
					//OK
				default:
					goErr.SetError(45161, sProc);
					break;
			}

			sBaseGID = oDataTable.Rows[(int)lCurrentRow]["GID_ID"].ToString();

			if (goData.GetRecordPermission(sBaseGID, "D") == false)
			{

				goErr.SetWarning(47252, sProc, "Permissions denied deleting record '" + this.GetFieldVal("SYS_Name").ToString() + "' (" + sBaseGID + ")");
				return 0;

			}

			//Try

			object tempVar = this;
			if (goScr.RunScript(sRSFile + "_RecordBeforeDelete", ref tempVar) == false)
			{
				return 0;
			}


			var sName = oDataTable.Rows[(int)lCurrentRow]["SYS_NAME"].ToString();

			clArray oArray = null;
			int i = 0;
			oArray = goData.GetLinks(this.sRSFile);

			for (i = 1; i <= oArray.GetDimension(); i++)
			{
				this.ClearLinkAll(oArray.GetItem(i));
			}

			sBaseGID = oDataTable.Rows[(int)lCurrentRow]["GID_ID"].ToString();
			sBaseSYSNAME = oDataTable.Rows[(int)lCurrentRow]["SYS_NAME"].ToString();

			oDataTable.Rows[(int)lCurrentRow].Delete();

			int retval = this.Commit();

			//V_T Delete attachments
			//If retval = 1 Then
			//    Dim objclAttachments As New clAttachments()
			//    objclAttachments.DeleteAllAttachmentsByFileGidId(sBaseGID, sRSFile)
			//End If

			//MI 9/7/11 Per DF's request changing the logging of deletions from level 1 and above to always (level 0)
			//goLog.Log(sProc, sRSFile & " '" & sBaseSYSNAME & "' (" & sBaseGID & ")", clC.SELL_LOGLEVEL_STANDARD)
			goLog.Log(sProc, sRSFile + " '" + sBaseSYSNAME + "' (" + sBaseGID + ")", (short)clC.SELL_LOGLEVEL_NONE);

			lCurrentRow = lCurrentRow - 1;

			object tempVar2 = this;
			if (goScr.RunScript(sRSFile + "_RecordAfterDelete", ref tempVar2, null, sBaseGID, sName) == false)
			{
				return 0;
			}

			return 1;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public int DeleteAll()
		{
				int tempDeleteAll = 0;

			//PURPOSE:
			//       Deletes all records in rowset and all of their links.  Executes commit for each delete. 		
			//PARAMETERS:      	
			//       None
			//RETURNS:
			//		# of records deleted if successful, errors if not.
			//AUTHOR: RH

			string sProc = "clRowset::DeleteAll";
			goLog.Log(sProc, sRSFile + " filter: '" + goTR.StrRead(this.sIntermediate, "CONDITION", "", false) + "'", (short)clC.SELL_LOGLEVEL_NONE, true);

			//MI 1/21/10 Changed to testing editable types explicitly
			//If iRSType = clC.SELL_READONLY Then goErr.SetError(45161, sProc)
			switch (iRSType)
			{
				case clC.SELL_EDIT:
				case clC.SELL_ADD:
				break;
					//OK
				default:
					goErr.SetError(45161, sProc);
					break;
			}

			//Try

			long iRecCount = Count();
			long i = 0;
			DataTable dt = oDataTable;

			for (i = iRecCount - 1; i >= 0; i--)
			{


				lCurrentRow = i;
				tempDeleteAll += DeleteRecord();

			}


			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
			return tempDeleteAll;
		}
		public int GetDataFormat()
		{
			//Returns current data format.  
			return iDataFormat;

		}
		public int SetDataFormat(int par_iFormat)
		{
			//Sets current data format. 

			string sProc = "clRowset::SetDataFormat";
			goLog.Log(sProc, "Start par_iFormat: '" + par_iFormat.ToString() + "'", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			if (par_iFormat == clC.SELL_SYSTEM || par_iFormat == clC.SELL_FRIENDLY)
			{
				iDataFormat = par_iFormat;
			}
			else
			{
				goErr.SetError(45162, sProc, "", par_iFormat.ToString());
			}

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return 0;
		}

		public object GetFieldVal(string sFieldName, int iFormat = 0, int iLength = -1, bool bEllipsis = true, int iRecs = -1, string sDelim = "CR", string sSort = "A_a")
		{
				object tempGetFieldVal = null;
			//MI 6/15/10 Changed to calling IsFieldSystemCode from goTr.
			//MI 6/14/10 Added evaluating system codes like MENAME, LOCATIONNAME, etc., which are supported by goTr.GetLinePart (called from goTr.GetLineValue)
			//MI 11/5/09 Added support for date grouping fields DTY DTQ DTM DTD.
			//MI 10/30/08 Added considering par_iLinksTop and par_sLinksTop parameters from New().
			//MI 9/26/08 Fix: DBNull with MLS returns default value, not just 0.
			//MI 6/8/08 Added interpreting 'DEF' in currency and floating point no of decimals definition.
			//       Until now was interpreted as 0.
			//MI 10/8/07 Added rounding DR_, SR_, CUR values based on _FRM properties in FLD_ MD.
			//MI 9/27/07 Added UTC conversions. Changes commented with "'*** MI 9/27/07".
			//PURPOSE:
			//       Returns field of link value		
			//PARAMETERS:
			//		sFieldName:  Name of the field or link
			//       iFormat: Data format to return
			//           clC.SELL_SYSTEM         (2)
			//           clC.SELL_FRIENDLY       (1)
			//           clC.SELL_SQLSTRING      (4)
			//           Format set to rowset default format if no value is not provided
			//       iLength: MAX length of string
			//       bEllipsis: Adds ellipsis if length is truncated or if getting a lnk field with more records than iRecs
			//       iRecs: MAX number of linked records to return. The number of records returned
			//           is iRecs or as defined in par_iLinksTop or par_sLinksTop in New(). Note that
			//           GetLinkVal, when iRecs is not defined, returns ONE MORE record than defined 
			//           in par_iLinksTop/par_sLinksTop in New().
			//RETURNS:
			//		FieldValue if successful, errors if not.

			//AUTHOR: RH

			sFieldName = sFieldName.ToUpper();
			string sProc = "clRowset::GetFieldVal";
			goLog.Log(sProc, "Start sFieldName: '" + sFieldName + "', iFormat: ' " + iFormat.ToString() + "'", (short)clC.SELL_LOGLEVEL_DEBUG, true);
			tempGetFieldVal = "";

			//Try

			if (this.Count() == 0)
			{
				goErr.SetError(35000, sProc, "Unable to return the requested value.  The rowset does not have any records.");
			}

			string sTempFieldName = sFieldName;
            //string sPrefix = goTR.GetPrefix(sTempFieldName).Substring(0, 3);
            string prefix = goTR.GetPrefix(sTempFieldName);
            string sPrefix = prefix.Length >= 3 ? prefix.Substring(0, 3) : prefix;
            string sReturn = "";
			int i = 0;
			clArray aLnkArray = new clArray();
			string sGenLine = "";
			object oValue = null;
			bool bEllipsis2 = true;
			int iDecimals = 0; //*** MI 10/8/07
			string sTemp = null; //*** MI 10/8/07
			clList oList = new clList(); //MI 9/26/08 added
			string sLinkField = null; //MI 10/1/08 added
			string sLinkFile = null; //MI 10/1/08 added
			string sFieldNameNoCalc = sTempFieldName; //MI 12/23/09 added. This value contains no GROUPBY calc like '|AVG' or '|SUM'.

			//**************************************************
			//Set Data format to default if not passed be caller
			//**************************************************
			if (iFormat == 0)
			{
				iFormat = iDataFormat;
			}
			//Validate format
			//*** MI 9/12/06
			if (iFormat != clC.SELL_SYSTEM && iFormat != clC.SELL_FRIENDLY && iFormat != clC.SELL_SQLSTRING)
			{
				goErr.SetError(45162, sProc, "", iFormat.ToString());
			}

			//****************************************
			//Test if field is in the rowset
			//****************************************
			if (sPrefix == "DTE" || sPrefix == "TME" || sPrefix == "TML")
			{
				sTempFieldName = goTR.Replace(sTempFieldName, goTR.GetPrefix(sTempFieldName), "DTT_");
			}

			//wt 102814
			if (sPrefix == "MMP")
			{
				sTempFieldName = goTR.Replace(sTempFieldName, goTR.GetPrefix(sTempFieldName), "MMR_");
			}

			//V_T 5/14/2015
			if (sPrefix == "ADV")
			{
				sTempFieldName = goTR.Replace(sTempFieldName, goTR.GetPrefix(sTempFieldName), "ADR_");
			}

			//MI 11/5/09 Is the field 'NONE'?
			if (sTempFieldName.ToUpper() == "NONE")
			{
				return Convert.ToString("");
			}

			if (this.iRSType == clC.SELL_GROUPBY || this.iRSType == clC.SELL_GROUPBYWITHROLLUP)
			{
				//Remove calculation code like |SUM, |AVG, etc
				i = sTempFieldName.IndexOf("|") + 1;
				if (i > 0)
				{
					sFieldNameNoCalc = goTR.FromTo(sTempFieldName, 1, i - 1);
				}
			}



			if (sPrefix == "LNK" || sPrefix == "MTA")
			{
				//Don't validate here
			}
			else
			{
				//MI 6/14/10 Added supporting system codes. A system code doesn't require the field to be loaded.
				if (!goTR.IsFieldSystemCode(sTempFieldName))
				{
					if (!IsLoaded(sTempFieldName))
					{
						if (goData.IsFieldValid(sRSFile, sTempFieldName))
						{
							//MI 11/5/09 DEBUG - ==> revert the changes in this block before release 5.5.156 or 157.
							//DEBUG Commenting GetFieldValUnloaded to see where fields are not loaded correctly.
							return GetFieldValUnloaded(sFieldName, iFormat, iLength, bEllipsis, iRecs, sDelim, sSort);
							//DEBUG Raising an error
							//goErr.SetError(45163, sProc, "Invalid rowset field '" & sRSFile & "." & sFieldName & "'. Add this field to sFields parameter. sIntermediate:" & vbCrLf & sIntermediate)
							//45163: Field or link not in rowset. The requested field or link, [1], is not valid in this rowset. Add this field to sFields parameter or use "*" for all direct fields or "**" for all direct fields and all links' GID_ID and SYS_Name fields. Note that linked fields such as 'LNK_Related_CN%%TXT_NameLast' have to be added to sFields individually.
						}
						else
						{
							//MI 11/5/09 Added invalid field error instead of field not in rowset error.
							goErr.SetError(10107, sProc, "", sFieldName);
							//10107: Invalid field name '[1]'.
							//MI 11/5/09 Commented
							//goErr.SetError(45163, sProc, , sFieldName)
						}
					}
				}
			}


			//MI 10/30/08 added this section
			//****************************************       
			//Determine no of linked records to return             
			//****************************************
			if (sPrefix == "LNK")
			{
				//Is iRecs parameter defined?
				if (iRecs < 0)
				{
					//iRecs parameter not defined; use par_iLinksTop from New() if it is defined
					if (iLinksTop >= 0)
					{
						//par_iLinksTop in New() is defined, use it
						iRecs = iLinksTop;
					}
					else
					{
						//par_iLinksTop in New() not defined, is the link defined in par_sLinksTop in New()?
						iRecs = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(this.sLinksTopIni, sFieldName, iRecs.ToString(), false), "0"));
					}
				}
			}


			//****************************************
			//Get field value in proper format
			//****************************************
			if (iFormat == clC.SELL_SYSTEM) //2=System format
			{

				if (sPrefix != "LNK")
				{

					oValue = oDataTable.Rows[(int)lCurrentRow][sTempFieldName];

					if (oValue.GetType().ToString() == "System.DBNull")
					{
						switch (sPrefix)
						{
							case "CHK":
							case "CMB":
							case "CUR":
							case "DR_":
							case "INT":
							case "LI_":
							case "BI_":
							case "LST":
							case "SEL":
							case "SI_":
							case "SR_": //MI 9/26/08 Removed "MLS"
								return 0;
							case "MLS": //MI 9/26/08 added case "MLS"
								if (iRSType == clC.SELL_GROUPBY || iRSType == clC.SELL_GROUPBYWITHROLLUP) //MI 12/23/09 added
								{
									return 0;
								}
								else
								{
									return oList.GetDefaultIndex(sRSFile, goTR.RemovePrefix(sTempFieldName)); //MI 9/26/08 Added
								}
								break;
							case "DTE":
							case "TME":
							case "TML":
							case "DTT":
							case "DTY":
							case "DTQ":
							case "DTM":
							case "DTD":
								return clC.SELL_BLANK_DTDATETIME;
							default:
								tempGetFieldVal = "";
								return Convert.ToString(tempGetFieldVal);
						}
					}
					//*************************************************************
				}

				//IMPORTANT: The above code ensures that we can't have a DBNull in oDataTable.Rows(lCurrentRow).Item(sTempFieldName).    '*** MI 10/3/07
				//It's important to keep this test or goTR.UTC_UTCToLocal will fail.    '*** MI 10/3/07

				switch (sPrefix)
				{

					case "DTE":
						//Datetime returned as DTE must be midnightized unless it's a blank datetime
						tempGetFieldVal = goTR.UTC_UTCToLocal(Convert.ToDateTime(oDataTable.Rows[(int)lCurrentRow][sTempFieldName]));

						//Midnightize datetime unless it's a blank datetime
						if (!(Convert.ToDateTime(tempGetFieldVal) == clC.SELL_BLANK_DTDATETIME))
						{
							int iValid = 0;
							tempGetFieldVal = goTR.DateAndTimeToDateTime(Convert.ToDateTime(tempGetFieldVal), Convert.ToDateTime("00:00:00.000"), ref iValid);
							if (iValid == clC.SELL_TYPE_INVALID)
							{
								goErr.SetError(35600, sProc,"", "goTR.DateAndTimeToDateTime", tempGetFieldVal.ToString(), "00:00:00.000");
								//35600: Data transformation failed.
								// 
								//Method:                     '[1]'
								//Value(1) '[2]'
								//Value(2) '[3]'
								//Format(1) '[4]'
								//Format(2) '[5]'
								//[6]
							}
						}
						break;
					case "TME":
					case "TML":
					case "DTT":
						return goTR.UTC_UTCToLocal(Convert.ToDateTime(oDataTable.Rows[(int)lCurrentRow][sTempFieldName]));
					case "DTY":
					case "DTQ":
					case "DTM":
					case "DTD": //MI 11/5/09 added DTY DTQ DTM DTD. These values are converted to user's local time and midnightized by SS and are not editable. Do not midnightize them here.
						//The way SS converts to local time, the value may be earlier than SELL_BLANK_DTDATETIME (1753-01-02 23:59:59)
						if (Convert.ToDateTime(oDataTable.Rows[(int)lCurrentRow][sTempFieldName]) < clC.SELL_BLANK_DTDATETIME)
						{
							return clC.SELL_BLANK_DTDATETIME;
						}
						else
						{
							return oDataTable.Rows[(int)lCurrentRow][sTempFieldName];
						}
						break;
					case "CHK":
					case "CMB":
					case "INT":
					case "LI_":
					case "BI_":
					case "LST":
					case "SEL":
					case "SI_":
					case "MLS": //*** MI 10/8/07 Moved CUR, DR_, SR_ into a separate CASE
						return oDataTable.Rows[(int)lCurrentRow][sTempFieldName];
					case "CUR":
					case "DR_":
					case "SR_": //*** MI 10/8/07
						//Round to the no of decimals in FLD_ MD XXX_YYYY_FORMAT property.    '*** MI 10/8/07
						sTemp = goTR.ExtractString(goData.GetFieldFormat(sRSFile, sTempFieldName), 1, "\t"); //*** MI 10/8/07
						if (sTemp == "" || sTemp[0] == clC.EOT) //*** MI 10/8/07
						{
							//Decimals not specified, do not round   '*** MI 10/8/07
							tempGetFieldVal = oDataTable.Rows[(int)lCurrentRow][sTempFieldName]; //*** MI 10/8/07
						}
						else //*** MI 10/8/07
						{
							//Decimals specified, round   '*** MI 10/8/07
							if (sTemp.ToUpper() == "DEF") //*** MI 6/8/08
							{
								//Default no of decimals - let's find what that default is    '*** MI 6/8/08
								sTemp = goTR.ExtractString(goTR.GetCurrFormat(), 1, "\t"); //*** MI 6/8/08
							} //*** MI 6/8/08
							iDecimals = Convert.ToInt32(goTR.StringToNum(sTemp, "0")); //*** MI 10/8/07
							tempGetFieldVal = Math.Round(Convert.ToDouble(oDataTable.Rows[(int)lCurrentRow][sTempFieldName]), iDecimals); //*** MI 10/8/07
						} //*** MI 10/8/07
						break;
					case "TXT":
					case "SYS":
					case "TEL":
					case "MMR":
					case "FIL":
					case "EML":
					case "MMO":
					case "GID": //"MMO",
						return oDataTable.Rows[(int)lCurrentRow][sTempFieldName];
					case "LNK":
						clArray oArray = new clArray();
						return GetLinkVal(sFieldName, ref oArray, true, iFormat, iRecs, sSort);
					case "MTA":
						return goTR.GetLineValue(ref sFieldName);
					default:
						//MI 6/14/10 Added evaluating codes like MENAME, which are supported by goTr.GetLinePart (called from goTr.GetLineValue)
						if (goTR.IsFieldSystemCode(sFieldName))
						{
							string tempVar = goTR.AddDelimiters(sFieldName);
							string tempVar2 = "";
							object tempVar3 = this;
							return goTR.GetLineValue(ref tempVar, ref tempVar2, "", false, ref tempVar3);
						}
						else
						{
							return oDataTable.Rows[(int)lCurrentRow][sTempFieldName];
						}
						break;
				}

				//*** MI 9/12/06 Added SELL_SQLSTRING section below
			}
			else if (iFormat == clC.SELL_SQLSTRING) //4=SQLString
			{

				//Return a string usable in filter conditions and sorts and compatible with
				//GenerateSQL.
				//Deal with nulls
				if (sPrefix != "LNK")
				{
					oValue = oDataTable.Rows[(int)lCurrentRow][sTempFieldName];
					if (oValue.GetType().ToString() == "System.DBNull")
					{
						switch (sPrefix)
						{
							case "CHK":
							case "CMB":
							case "CUR":
							case "DR_":
							case "INT":
							case "LI_":
							case "BI_":
							case "LST":
							case "SEL":
							case "SI_":
							case "SR_": //MI 9/26/08 removed "MLS"
								tempGetFieldVal = "0";
								break;
							case "MLS": //MI 9/26/08 added Case "MLS"
								if (iRSType == clC.SELL_GROUPBY || iRSType == clC.SELL_GROUPBYWITHROLLUP) //MI 12/23/09 added
								{
									tempGetFieldVal = "0";
								}
								else
								{
									tempGetFieldVal = oList.GetDefaultIndex(sRSFile, goTR.RemovePrefix(sTempFieldName)).ToString(); //MI 9/26/08 changed from Return "0" to returning the default index as string
								}
								break;
							case "DTE":
							case "TME":
							case "TML":
							case "DTT":
							case "DTY":
							case "DTQ":
							case "DTM":
							case "DTD": //MI 7/12/10 Added this case to address an issue with TOPREC containing blank DTx values.
								tempGetFieldVal = clC.SELL_BLANK_SYSDATETIME; //MI 7/12/10 Added
								break;
							default:
								//MI 7/12/10 Restored Case "DTE", "TME", "TML", "DTT", "DTY", "DTQ", "DTM", "DTD" 
								//MI 11/5/09 Removed case "DTE", "TME", "TML", "DTT", "DTY", "DTQ", "DTM", "DTD", which early on used to return clC.SELL_BLANK_DATETIME
								tempGetFieldVal = "";
								break;
						}
						return Convert.ToString(tempGetFieldVal);
					}
				}

				//*** MI 9/27/07: changed If Then Else to Select Case.
				switch (sPrefix)
				{
					case "LNK":
						//In SELL_SQLSTRING mode we return as many linked recs as defined in iRecs 
						//without '...'.
						aLnkArray = GetLinkVal(sFieldName, ref aLnkArray, true, iFormat, iRecs, sSort); //MI 10/30/08 added iRecs
						sDelim = "\r\n";
						for (i = 1; i <= aLnkArray.GetDimension(); i++)
						{
							sReturn = sReturn + aLnkArray.GetItem(i) + sDelim;
						}
						if (sReturn.Length > 0)
						{
							//Some links were returned
							//Remove the trailing CR (sDelim)
							tempGetFieldVal = sReturn.Substring(0, sReturn.Length - sDelim.Length);
						}
						else
						{
							//No links were returned
							//MI 10/1/08 Added the following section to return a value consistent with the field type
							//instead of '', which causes TSQL errors if the value returned is used in a WHERE statement.
							sLinkField = goTR.GetFieldPartFromLinkName(sFieldName);
							sLinkFile = goTR.GetFileFromLinkName(sTempFieldName);
							switch (goTR.GetPrefix(sLinkField).Substring(0, 3)) //link field prefix
							{
								case "CHK":
								case "CMB":
								case "CUR":
								case "DR_":
								case "INT":
								case "LI_":
								case "BI_":
								case "LST":
								case "SEL":
								case "SI_":
								case "SR_":
									tempGetFieldVal = "0";
									break;
								case "MLS":
									if (iRSType == clC.SELL_GROUPBY || this.iRSType == clC.SELL_GROUPBYWITHROLLUP)
									{
										tempGetFieldVal = "0";
									}
									else
									{
										tempGetFieldVal = oList.GetDefaultIndex(sLinkFile, goTR.RemovePrefix(sLinkField)).ToString();
									}
									break;
								default:
									//MI 11/5/09 Removed case "DTE", "TME", "TML", "DTT", "DTY", "DTQ", "DTM", "DTD", which early on used to return clC.SELL_BLANK_DATETIME
									tempGetFieldVal = "";
									break;
							}
							return Convert.ToString(tempGetFieldVal);
						}
						break;
					case "GEN":
						//Generated fields not supported in this context
						goErr.SetError(45164, sProc);
						break;
					case "DTT":
					case "DTE":
					case "TME":
					case "TML":
						tempGetFieldVal = goTR.FieldToSQLString(goTR.UTC_UTCToLocal(Convert.ToDateTime(oDataTable.Rows[(int)lCurrentRow][sTempFieldName])), sFieldName); //*** MI 9/27/07
						break;
					case "DTY":
					case "DTQ":
					case "DTM":
					case "DTD": //MI 11/5/09 Added DTY, DTQ, DTM, DTD
						//MI 11/5/09 added DTY DTQ DTM DTD. These values are converted to user's local time and midnightized by SS and are not editable. Do not convert/midnightize them here.
						tempGetFieldVal = goTR.FieldToSQLString(oDataTable.Rows[(int)lCurrentRow][sTempFieldName], sFieldName); //*** MI 9/27/07
						break;
					case "CUR":
					case "DR_":
					case "SR_": //*** MI 10/8/07
						//Round to the no of decimals in FLD_ MD XXX_YYYY_FORMAT property.    '*** MI 10/8/07
						sTemp = goTR.ExtractString(goData.GetFieldFormat(sRSFile, sTempFieldName), 1, "\t"); //*** MI 10/8/07
						if (sTemp == "" || sTemp[0] == clC.EOT) //*** MI 10/8/07
						{
							//Decimals not specified, do not round   '*** MI 10/8/07
							tempGetFieldVal = goTR.FieldToSQLString(oDataTable.Rows[(int)lCurrentRow][sTempFieldName], sFieldName); //*** MI 10/8/07
						}
						else //*** MI 10/8/07
						{
							//Decimals specified, round   '*** MI 10/8/07
							if (sTemp.ToUpper() == "DEF") //*** MI 6/8/08
							{
								//Default no of decimals - let's find what that default is    '*** MI 6/8/08
								sTemp = goTR.ExtractString(goTR.GetCurrFormat(), 1, "\t"); //*** MI 6/8/08
							} //*** MI 6/8/08
							iDecimals = Convert.ToInt32(goTR.StringToNum(sTemp, "0")); //*** MI 10/8/07
							tempGetFieldVal = goTR.FieldToSQLString(Math.Round(Convert.ToDouble(oDataTable.Rows[(int)lCurrentRow][sTempFieldName]), iDecimals), sFieldName); //*** MI 10/8/07
						} //*** MI 10/8/07
						break;
					default:
						//MI 6/14/10 Added evaluating codes like MENAME, which are supported by goTr.GetLinePart (called from goTr.GetLineValue).
						//System code values are meaningless in the SQL context unless you know this is how a particular value is stored in SS.
						if (goTR.IsFieldSystemCode(sFieldName))
						{
							string tempVar4 = goTR.AddDelimiters(sFieldName);
							string tempVar5 = "";
							object tempVar6 = this;
							tempGetFieldVal = goTR.GetLineValue(ref tempVar4, ref tempVar5, "", false, ref tempVar6);
						}
						else
						{
							tempGetFieldVal = goTR.FieldToSQLString(oDataTable.Rows[(int)lCurrentRow][sTempFieldName], sFieldName, clC.SELL_TYPE_INVALID, sRSFile);
						}
						break;

				}


			}
			else if (iFormat == clC.SELL_FRIENDLY) //1=Friendly format
			{

				//*** MI 9/27/07: changed If Then Else to Select Case.
				switch (sPrefix)
				{
					case "LNK":
						bEllipsis2 = true;
						if (iRecs != -1)
						{
							//iRecs or par_iLinksTop (in New) or par_sLinksTop (in New) defined,
							//return only that many linked recs
							aLnkArray = GetLinkVal(sFieldName, ref aLnkArray, true, iFormat, iRecs + 1, sSort);
							if (aLnkArray.GetDimension() <= iRecs)
							{
								bEllipsis2 = false; //No limiting, no ellipsis
							}
							if (aLnkArray.GetDimension() < iRecs)
							{
								iRecs = (int)aLnkArray.GetDimension(); //We are using iRecs to loop through array
							}
						}
						else
						{
							//iRecs not defined, return all linked recs
							aLnkArray = GetLinkVal(sFieldName, ref aLnkArray, true, iFormat, -1, sSort);
							iRecs = (int)aLnkArray.GetDimension();
							bEllipsis2 = false;
						}

						if (sDelim.ToUpper() == "CR")
						{
							sDelim = "\r\n";
						}

						for (i = 1; i <= iRecs; i++)
						{
							sReturn = sReturn + aLnkArray.GetItem(i) + sDelim;
						}
						if (sReturn.Length > 0)
						{
							//Some links were returned
							tempGetFieldVal = sReturn.Substring(0, sReturn.Length - sDelim.Length);
						}
						else
						{
							//No links were returned
							tempGetFieldVal = "";
						}
						if (bEllipsis2 == true) //Was set false earlier if true but when not needed
						{
							if (bEllipsis == true)
							{
								tempGetFieldVal = tempGetFieldVal + sDelim + "...";
							}
						}
						break;

							//Return GetFieldVal      'MI 3/6/08 commented to allow truncation to occur below

					case "GEN": //Generated field
						switch (iRSType)
						{
							case clC.SELL_EDIT:
							case clC.SELL_READONLY:
							case clC.SELL_GROUPBY:
							case clC.SELL_GROUPBYWITHROLLUP:
							case clC.SELL_COUNT:
								sGenLine = goTR.StrRead(sIntermediate, sFieldName, null, false);
								string tempVar7 = "";
								object tempVar8 = this;
								tempGetFieldVal = goTR.GetLineValue(ref sGenLine, ref tempVar7, "", false, ref tempVar8); //MI 3/6/08 changed from Return to GetFieldVal =
								break;
							default:
								goErr.SetError(45164, sProc);
								break;
						}
						break;
					case "MTA":
						tempGetFieldVal = goTR.GetLineValue(ref sFieldName); //MI 3/6/08 changed from Return to GetFieldVal =
						break;

					case "DTT":
					case "DTE":
					case "TME":
					case "TML": //*** MI 9/27/07
						//Convert UTC to local     '*** MI 9/27/07
						//Deal with DBNull to avoid an error in the call to goTR.UTC_UTCToLocal().   '*** MI 10/3/07
						if (oDataTable.Rows[(int)lCurrentRow][sTempFieldName].GetType().ToString() == "System.DBNull") //*** MI 10/1/07
						{
							tempGetFieldVal = ""; //*** MI 10/5/07
						}
						else //*** MI 10/1/07
						{
							tempGetFieldVal = GetFriendly(sRSFile, sFieldName, goTR.UTC_UTCToLocal(Convert.ToDateTime(oDataTable.Rows[(int)lCurrentRow][sTempFieldName]))); //*** MI 9/27/07
						} //*** MI 10/1/07
						break;

						//Case "CUR", "SR_", "DR_"   '*** MI 10/8/07
						//Not processed here because they are processed in GetFriendly   '*** MI 10/8/07

					default:
						//MI 6/14/10 Added evaluating codes like MENAME, which are supported by goTr.GetLinePart (called from goTr.GetLineValue)
						if (goTR.IsFieldSystemCode(sFieldName))
						{
							string tempVar9 = goTR.AddDelimiters(sFieldName);
							string tempVar10 = "";
							object tempVar11 = this;
							tempGetFieldVal = goTR.GetLineValue(ref tempVar9, ref tempVar10, "", false, ref tempVar11);
						}
						else
						{
							//Includes Case "DTY", "DTQ", "DTM", "DTD"     'MI 11/5/09 Added DTY DTQ DTM DTD
							tempGetFieldVal = GetFriendly(sRSFile, sFieldName, oDataTable.Rows[(int)lCurrentRow][sTempFieldName]);
						}
						break;

				}

				//Truncate the string if length is specified
				if (iLength != -1)
				{
					tempGetFieldVal = goTR.TruncateText(Convert.ToString(tempGetFieldVal), iLength, bEllipsis);
				}

			}

			//DEBUG     '*** MI 10/3/07
			//Dim ds As DataSet = Me.oDataSet     '*** MI 10/3/07

			return tempGetFieldVal;


			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

		}

		private object GetFieldValUnloaded(string sFieldName, int iFormat = 0, int iLength = -1, bool bEllipsis = true, int iRecs = -1, string sDelim = "CR", string sSort = "A")
		{
			//MI 11/5/09 Added raising a warning so we have a trail of missing fields. A missing field is not normal and can cause DoS.
			//PURPOSE:
			//		Retrieves a field value for the given ID and field. 
			//RETURNS:
			//		Value in requested format if successful, or "" if not.
			//AUTHOR: RH

			string par_sID = Convert.ToString(this.GetCurrentRecID());

			string sProc = "clRowset::GetFieldValUnloaded";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			try
			{
				string sFile = goTR.GetFileFromSUID(par_sID);
				//MI 11/5/09 Added raising a warning so we have a trail of missing fields. A missing field is not normal and can cause DoS.
				goErr.SetWarning(35000, sProc, "Missing rowset field '" + sFile + "." + sFieldName + "'. GID_ID: '" + par_sID + "'.");
				clRowSet rs = new clRowSet(sFile, 3, "GID_ID='" + par_sID + "'", "", sFieldName, -1, "", "", "", "", "", false, false, true);
				if (rs.Count() > 0)
				{
					return rs.GetFieldVal(sFieldName, iFormat, iLength, bEllipsis, iRecs, sDelim, sSort);
				}

			}
			catch (Exception ex)
			{
				return "";
			}

			return "";



		}
		public int SetFieldVal(string sFieldName, object oValue, int iFormat = 0)
		{
			//MI 3/12/10 Decoupled testing is Nothing from testing System.DBNull.
			//MI 11/5/09 Minor mods.
			//MI 9/29/08 Fix: DBNull for MLS fields is now the default MLS value, not just 0.
			//MI 6/19/08 Added support for 'Def' no of decimals in SELL_SYSTEM mode, CUR, DR, SR field types.
			//MI 9/27/07 added UTC conversions. See comments "'*** MI 9/27/07".
			//PURPOSE:
			//       Sets field or link value. Read-only fields cause a warning to be raised.		
			//PARAMETERS:
			//		sFieldName:  Name of the field or link
			//       oValue:     Value to set
			//       iFormat: Data format to return
			//           clC.SELL_SYSTEM         (2)
			//           clC.SELL_FRIENDLY       (1)
			//           Format set to rowset default format if no value is not provided
			//       
			//RETURNS:
			//		1 if successful, 0 if not, errors if not.  
			//AUTHOR: RH

			string sProc = "clRowset::SetFieldVal";
			string sPrefix = sFieldName.Substring(0, 3).ToUpper();
			clList oList = new clList();
			bool bNothing = false;

			//MI 3/12/10 Decoupled testing is Nothing from testing System.DBNull.
			if (oValue == null)
			{
				bNothing = true;
			}
			else
			{
				if (oValue.GetType().ToString() == "System.DBNull")
				{
					bNothing = true;
				}
			}

			if (bNothing) //*** MI 10/3/07 Added testing DBNull
			{
				switch (sPrefix)
				{
					case "CHK":
					case "CMB":
					case "CUR":
					case "DR_":
					case "INT":
					case "LI_":
					case "BI_":
					case "LST":
					case "SEL":
					case "SI_":
					case "SR_": //MI 9/29/08 Removed "MLS"
						oValue = 0;
						break;
					case "MLS": //MI 9/29/08 Added Case "MLS"
						oValue = oList.GetDefaultIndex(sRSFile, goTR.RemovePrefix(sFieldName)); //MI 9/29/08 Added Case "MLS"
						break;
					default:
						//MI 11/5/09 Removed Case "DTE", "TME", "TML", "DTT"
						oValue = "";
						break;
				}
			}

			//If oValue Is Nothing Then      '*** MI 10/3/07
			//    goErr.SetError(45175, sProc, , sFieldName.ToString)    '*** MI 10/3/07
			//End If             '*** MI 10/3/07

			sFieldName = sFieldName.ToUpper();

			goLog.Log(sProc, "Start " + "sFieldName: '" + sFieldName + "' oValue: '" + oValue.ToString() + "' iFormat: '" + iFormat.ToString() + "'", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sTempFieldName = sFieldName;

			string sP = goTR.GetPrefix(sFieldName);
			object oReturn = null;
			object oReturn2 = null;
			int par_iType = 0;

			DateTime dtDate = default(DateTime);
			DateTime dtTime = default(DateTime);

			bool bInvalid = false;
			string sTemp = null; //*** MI 10/8/07
			int iDecimals = 0; //*** MI 10/8/07


			//Try

			//****************************************
			//Test if rowset is add or edit
			//If Not (iRSType = 1 Or iRSType = 2) Then
			//    goErr.SetError(45161, sProc)
			//End If
			//MI 1/21/10 Added testing all non-editable type
			switch (iRSType)
			{
				case clC.SELL_EDIT:
				case clC.SELL_ADD:
				break;
					//OK
				default:
					//Not supported: clC.SELL_READONLY, clC.SELL_GROUPBY, clC.SELL_GROUPBYWITHROLLUP, clC.SELL_COUNT
					goErr.SetError(45161, sProc);
					break;
			}

			bRSIsDirty = true;
			//****************************************
			//Set Data format to default if not passed be caller
			if (iFormat == 0)
			{
				iFormat = iDataFormat;
			}
			//Validate format
			if (iFormat != clC.SELL_SYSTEM && iFormat != clC.SELL_FRIENDLY)
			{
				goErr.SetError(45162, sProc, "", iFormat.ToString());
			}
			//****************************************
			//Convert DTE TME and TML to DTT
			if (sP == "DTE_" || sP == "TME_" || sP == "TML_")
			{
				sTempFieldName = goTR.Replace(sFieldName, goTR.GetPrefix(sFieldName), "DTT_");
			}
			//Convert MMP to MMR
			if (sP == "MMP_")
			{
				sTempFieldName = goTR.Replace(sFieldName, goTR.GetPrefix(sFieldName), "MMR_");
			}
			//Convert ADV to ADR
			if (sP == "ADV_")
			{
				sTempFieldName = goTR.Replace(sFieldName, goTR.GetPrefix(sFieldName), "ADR_");
			}
			//****************************************

			//****************************************

			//V_T 03/16/2021 ''manage dirty fields collection

			if (gsDirtyFields != null && sPrefix != "LNK" && sFieldName != "MMO_FIELDSAUDITTRAIL" && sFieldName != "MMO_HISTORY")
			{

				var sprevval = oDataTable.Rows[(int)lCurrentRow][sTempFieldName]; //'GetFieldVal(sTempFieldName, 2)



				bool bIsFieldDirty = false;

				if (sPrefix == "SI_" || sPrefix == "LI_" || sPrefix == "INT" || sPrefix == "CHK")
				{
					if (sprevval == null || Convert.IsDBNull(sprevval))
					{
						sprevval = 0;
					}
					if (!(Convert.ToInt32(sprevval).Equals(Convert.ToInt32(oValue))))
					{
						bIsFieldDirty = true;
					}
				}
				else if (sPrefix == "SR_" || sPrefix == "CUR")
				{
					if (sprevval == null || Convert.IsDBNull(sprevval))
					{
						sprevval = 0;
					}
					if (!(Convert.ToDouble(sprevval).Equals(Convert.ToDouble(oValue))))
					{
						bIsFieldDirty = true;
					}
				}
				else
				{
					if (sprevval == null || Convert.IsDBNull(sprevval))
					{
						sprevval = "";
					}
					if (!sprevval.Equals(oValue))
					{
						bIsFieldDirty = true;
					}
				}
				if (bIsFieldDirty)
				{
					if (gsDirtyFields.ContainsKey(sTempFieldName))
					{
						gsDirtyFields.Remove(sTempFieldName);
					}
					gsDirtyFields.Add(sTempFieldName, new Tuple<object, object>(sprevval, oValue));
				}

			}

			//V_T 10/27/2015 ''manage dirty fields collection
			//If gsDirtyFields IsNot Nothing AndAlso gsDirtyFields.Count > 0 Then
			//    If gsDirtyFields.ContainsKey(sTempFieldName) Then
			//        Dim sOrigvalue = gsDirtyFields(sTempFieldName).Item1
			//        Dim sNewvalue = oValue
			//        gsDirtyFields.Remove(sTempFieldName)
			//        gsDirtyFields.Add(sTempFieldName, New Tuple(Of Object, Object)(sOrigvalue, sNewvalue))
			//    End If
			//End If



			//****************************************

			//Enforce readonly
			//MI 11/5/09 Replacing this block with goData.IsFieldReadOnly test below
			//Dim bEnforce As Boolean
			//If sTempFieldName = "DTT_CREATIONTIME" Or sTempFieldName = "BI__ID" Then bEnforce = True
			//If sP = "GID_" Then
			//    If sTempFieldName = "GID_ID" Then
			//        bEnforce = True
			//    Else
			//        If InStr(5, sTempFieldName, "_") > 0 Then
			//            bEnforce = True
			//        End If
			//    End If
			//End If

			if (goData.IsFieldReadOnly(sTempFieldName)) //MI 11/5/09 Added
			{
				//If bEnforce = True Then        'MI 11/5/09 Commented
				goErr.SetWarning(47173, sProc, "", sFieldName, sRSFile, oValue.ToString());
				return 1;
				//==> change to return 0
				//The field '[1]', in file '[2]', is read only.  The value '[3]' was not written.
			}
			//****************************************
			//Test if field is in rowset
			if (!IsLoaded(sTempFieldName))
			{
				goErr.SetError(45163, sProc, "", sFieldName);
				return 0;
			}
			//****************************************

			//If sP = "MLS" Then

			//    Dim clog As New clLog
			//    clog.WriteToEventLog(sFieldName & "  " & oValue.ToString & "   SET")

			//End If

			//Set field value in proper format
			if (iFormat == clC.SELL_SYSTEM) //System format
			{

				switch (sPrefix)
				{

						//Case "SR_", "DR_"

						//    '==> read format info and pass to function
						//    oReturn = goTR.StringToNum(oValue, "3", par_iType, sFieldName)
						//    If par_iType = clC.SELL_TYPE_INVALID Then
						//        goErr.SetError(45165, sProc, , oValue, sFieldName)
						//    End If
						//    oDataTable.Rows(lCurrentRow).Item(sFieldName) = oReturn

					case "TXT":
					case "EML":
					case "URL":
					case "MMO":
					case "MMR":
					case "TEL":
					case "FIL":
					{

						//Truncate to max length.
						//==> possible optimization.. change godata.GetFieldSize to use smaller dataset for this purpose
						int lLength = Convert.ToInt32(goData.GetFieldSize(sRSFile, sFieldName));
						if (oValue.ToString().Length > lLength)
						{
							oValue = oValue.ToString().Substring(0, lLength);
							goErr.SetWarning(10125, sProc, "", Convert.ToString(oValue), sRSFile + "." + sFieldName);
							//10125: Text written to field '[2]' was truncated: '[1]'
						}
						oDataTable.Rows[(int)lCurrentRow][sFieldName] = oValue;
						break;

					}
					case "SYS":
					{

						//Truncate to max length.
						//==> possible optimization.. change godata.GetFieldSize to use smaller dataset for this purpose
						int lLength = Convert.ToInt32(goData.GetFieldSize(sRSFile, sFieldName));
						if (oValue.ToString().Length > lLength)
						{
							oValue = oValue.ToString().Substring(0, lLength);
							goErr.SetWarning(10125, sProc, "", Convert.ToString(oValue), sRSFile + "." + sFieldName);
							//10125: Text written to field '[2]' was truncated: '[1]'
						}
						//'SYSName having | sysymbol is creating problem when linking the contact
						oValue = oValue.ToString().Replace("\r\n", " ");
						oValue = oValue.ToString().Replace("\n", " ");
						oValue = oValue.ToString().Replace("\r", " ");
						oValue = oValue.ToString().Replace("\t", " ");
						oValue = oValue.ToString().Replace("|", " ");
						oDataTable.Rows[(int)lCurrentRow][sFieldName] = oValue;
						break;

					}
					case "LNK":
					{
						return SetLinkVal(sFieldName, oValue);

					}
					case "DTT":
					case "DTE":
					case "TME": //*** MI 9/27/07
					{
						//Convert local to UTC
						//Testing DBNull above   '*** MI 10/3/07
						DateTime tempVar = Convert.ToDateTime(oValue);
						oDataTable.Rows[(int)lCurrentRow][sTempFieldName] = goTR.UTC_LocalToUTC(ref tempVar); //*** MI 9/27/07
							oValue = tempVar;
							break;

					}
					case "CUR":
					case "DR_":
					case "SR_": //*** MI 10/8/07
					{
						//*** MI 10/8/07
						//Round to the no of decimals in FLD_ MD XXX_YYYY_FORMAT property.    '*** MI 10/8/07
						sTemp = goTR.ExtractString(goData.GetFieldFormat(sRSFile, sTempFieldName), 1, "\t"); //*** MI 10/8/07
						if (sTemp == "" || sTemp[0] == clC.EOT) //*** MI 10/8/07
						{
							//Decimals not specified, do not round   '*** MI 10/8/07
							oDataTable.Rows[(int)lCurrentRow][sTempFieldName] = oValue; //*** MI 10/8/07
						}
						else //*** MI 10/8/07
						{
							//Decimals specified, round   '*** MI 10/8/07
							if (sTemp.ToUpper() == "DEF") //*** MI 6/19/08
							{
								//Default no of decimals - let's find what that default is    '*** MI 6/19/08
								sTemp = goTR.ExtractString(goTR.GetCurrFormat(), 1, "\t"); //*** MI 6/19/08
							}
							iDecimals = Convert.ToInt32(goTR.StringToNum(sTemp, "0")); //*** MI 10/8/07
							oDataTable.Rows[(int)lCurrentRow][sTempFieldName] = Math.Round(Convert.ToDouble(oValue), iDecimals); //*** MI 10/8/07
						} //*** MI 10/8/07
						break;

					}
					case "TML": //*** MI 9/27/07
					{
						goErr.SetError(45166, sProc, "", sFieldName); //*** MI 9/27/07
						break;

					}
					case "ADV": //V_T 5/27/15
					{
					break;
						//do nothing
					}
					default:
					{
						oDataTable.Rows[(int)lCurrentRow][sTempFieldName] = oValue;
						break;


					}
				}

			}
			else if (iFormat == clC.SELL_FRIENDLY) //Friendly format
			{


				switch (sPrefix)
				{
					case "SYS":
					{

						//Truncate to max length.
						//==> possible optimization.. change godata.GetFieldSize to use smaller dataset for this purpose
						int lLength = Convert.ToInt32(goData.GetFieldSize(sRSFile, sFieldName));
						if (oValue.ToString().Length > lLength)
						{
							oValue = oValue.ToString().Substring(0, lLength);
							goErr.SetWarning(10125, sProc, "", Convert.ToString(oValue), sRSFile + "." + sFieldName);
							//10125: Text written to field '[2]' was truncated: '[1]'
						}
						//'SYSName having | sysymbol is creating problem when linking the contact
						oValue = oValue.ToString().Replace("\r\n", " ");
						oValue = oValue.ToString().Replace("\n", " ");
						oValue = oValue.ToString().Replace("\r", " ");
						oValue = oValue.ToString().Replace("\t", " ");
						oValue = oValue.ToString().Replace("|", " ");
						oDataTable.Rows[(int)lCurrentRow][sFieldName] = oValue;
						break;

					}
					case "LNK":
					{
						return SetLinkVal(sFieldName, oValue);

					}
					case "DTE":
					{
						//Get provided Date as DateTime
						oReturn = goTR.StringToDate(oValue, "", ref par_iType);
						if (par_iType == clC.SELL_TYPE_INVALID)
						{
							goErr.SetWarning(45165, sProc, "", Convert.ToString(oValue), sFieldName);
							bInvalid = true;
							oReturn = goTR.StringToDate("", "", ref par_iType);
						}

						dtDate = Convert.ToDateTime(oReturn);

						//Get current stored Time as DateTime
						oReturn2 = GetFieldVal(sTempFieldName, clC.SELL_SYSTEM);
						if (oReturn2.GetType().ToString() == "System.DBNull")
						{
							//Time is null, set to midnight
							dtTime = goTR.StringToTime("00:00:00.000"); //goTR.StringToTime("")
						}
						else
						{
							if (Convert.ToDateTime(oReturn2) <= clC.SELL_BLANK_DTDATETIME)
							{
								//Time is blank or invalid, set it to midnight
								dtTime = goTR.StringToTime("00:00:00.000");
							}
							else
							{
								dtTime = Convert.ToDateTime(oReturn2);
							}
						}

						//Set DTT field
						if (dtDate == clC.SELL_BLANK_DTDATETIME)
						{
							//Date is blank, blank the time as well  
							DateTime tempVar2 = clC.SELL_BLANK_DTDATETIME;
							oDataTable.Rows[(int)lCurrentRow][sTempFieldName] = goTR.UTC_LocalToUTC(ref tempVar2); //*** MI 9/27/07
						}
						else
						{
							//Date is not blank, merge the date and time
							oReturn = goTR.DateAndTimeToDateTime(dtDate, dtTime, ref par_iType);
							if (par_iType == clC.SELL_TYPE_INVALID)
							{
								bInvalid = true;
								goErr.SetWarning(45165, sProc, "", Convert.ToString(oValue), sFieldName);
							}
							DateTime tempVar3 = Convert.ToDateTime(oReturn);
							oDataTable.Rows[(int)lCurrentRow][sTempFieldName] = goTR.UTC_LocalToUTC(ref tempVar3); //*** MI 9/27/07
								oReturn = tempVar3;
						}
						break;

					}
					case "TME":
					{
						//Get provided Time as DateTime
						oReturn = goTR.StringToTime(oValue, "", ref par_iType);
						if (par_iType == clC.SELL_TYPE_INVALID)
						{
							goErr.SetWarning(45165, sProc, "", Convert.ToString(oValue), sFieldName);
							oReturn = goTR.StringToTime("", "", ref par_iType);
							bInvalid = true;
						}
						dtTime = Convert.ToDateTime(oReturn);

						//Get current stored Date as DateTime
						oReturn2 = GetFieldVal(sTempFieldName, clC.SELL_SYSTEM);
						if (oReturn2.GetType().ToString() == "System.DBNull")
						{
							dtDate = goTR.StringToDate("");
						}
						else
						{
							dtDate = Convert.ToDateTime(oReturn2);
						}

						//Set DTT field
						oReturn = goTR.DateAndTimeToDateTime(dtDate, dtTime, ref par_iType);
						if (par_iType == clC.SELL_TYPE_INVALID)
						{
							bInvalid = true;
							goErr.SetWarning(45165, sProc, "", Convert.ToString(oValue), sFieldName);
						}
						DateTime tempVar4 = Convert.ToDateTime(oReturn);
						oDataTable.Rows[(int)lCurrentRow][sTempFieldName] = goTR.UTC_LocalToUTC(ref tempVar4); //*** MI 9/27/07
							oReturn = tempVar4;
							break;

					}
					case "TML":
					{
						//TML_ is a read-only notion     '*** MI 10/3/07
						goErr.SetError(45166, sProc, "", sFieldName);
						break;

					}
					case "DTT":
					{
						//Get provided Datetime as pipe-delimited datetime string
						//Ex: '2007-03-16|11:47'
						oReturn = goTR.StringToDateTime(oValue, "", "", ref par_iType);
						if (par_iType == clC.SELL_TYPE_INVALID)
						{
							goErr.SetWarning(45165, sProc, "", Convert.ToString(oValue), sFieldName);
							oReturn = goTR.StringToTime("", "", ref par_iType);
							bInvalid = true;
						}

						DateTime tempVar5 = Convert.ToDateTime(oReturn);
						oDataTable.Rows[(int)lCurrentRow][sFieldName] = goTR.UTC_LocalToUTC(ref tempVar5); //*** MI 9/27/07
							oReturn = tempVar5;
							break;

					}
					case "INT":
					case "LI_":
					case "BI_":
					case "SI_":
					case "SEL":
					case "CMB":
					case "LST":
					{
						oReturn = goTR.StringToNum(oValue, "", ref par_iType, sFieldName);
						if (par_iType == clC.SELL_TYPE_INVALID)
						{
							oReturn = 0;
							goErr.SetWarning(45165, sProc, "", Convert.ToString(oValue), sFieldName);
						}
						oDataTable.Rows[(int)lCurrentRow][sFieldName] = oReturn;
						break;

					}
					case "CUR":
					{
						oReturn = goTR.StringToCurr(oValue, goData.GetFieldFormat(sRSFile, sFieldName), ref par_iType, sFieldName);
						if (par_iType == clC.SELL_TYPE_INVALID)
						{
							oReturn = 0;
							goErr.SetWarning(45165, sProc, "", Convert.ToString(oValue), sFieldName);
						}
						oDataTable.Rows[(int)lCurrentRow][sFieldName] = oReturn;
						break;

					}
					case "SR_":
					case "DR_":
					{
						oReturn = goTR.StringToNum(oValue, goData.GetFieldFormat(sRSFile, sFieldName), ref par_iType, sFieldName);
						if (par_iType == clC.SELL_TYPE_INVALID)
						{
							oReturn = 0;
							goErr.SetWarning(45165, sProc, "", Convert.ToString(oValue), sFieldName);
						}
						oDataTable.Rows[(int)lCurrentRow][sFieldName] = oReturn;
						break;

					}
					case "MLS":
					{
						oReturn = goTR.StringToMLS(sRSFile, sFieldName, oValue);
						oDataTable.Rows[(int)lCurrentRow][sFieldName] = oReturn;
						break;

					}
					case "CHK":
					{
						oReturn = goTR.StringToCheckbox(oValue, false, ref par_iType);
						if (par_iType == clC.SELL_TYPE_INVALID)
						{
							oReturn = 0;
							goErr.SetWarning(45165, sProc, "", Convert.ToString(oValue), sFieldName);

						}
						oDataTable.Rows[(int)lCurrentRow][sFieldName] = oReturn;
						break;

					}
					case "GID":
					{
						if (oValue.ToString() == "")
						{
							oDataTable.Rows[(int)lCurrentRow][sTempFieldName] = System.DBNull.Value;
						}
						else
						{
							oDataTable.Rows[(int)lCurrentRow][sTempFieldName] = goTR.StringToGuid(oValue.ToString());
						}
						break;

					}
					case "MMR":
					{
						//xxx MMR
						// html string is passed
						// Set to mailmessage object with embedded resources integrated locally
						// Mailitem converted to serialized text (msg.savemessage, or streaming get working)
						// Text set to field

						// Dim sPath As String = System.Web.HttpContext.Current.Server.MapPath("../Temp/MMR/") & UCase(Guid.NewGuid().ToString)
						//System.IO.Directory.CreateDirectory(sPath)

						//Dim oMessage As New MailBee.Mime.MailMessage
						//oMessage.BodyHtmlText = oValue.ToString()

						// oMessage.Builder.RelatedFilesFolder = System.Web.HttpContext.Current.Server.MapPath("")
						//oMessage.ImportRelatedFiles(ImportRelatedFilesOptions.None)
						//oMessage.SaveMessage(sPath & "/msg.eml")

						// oDataTable.Rows(lCurrentRow).Item(sFieldName) = System.IO.File.ReadAllText(sPath & "/msg.eml")
						//V_T:2/13/2015
						oDataTable.Rows[(int)lCurrentRow][sFieldName] = oValue.ToString();
						break;

					}
					default:
					{
						//All assumed to be text/memo with defined field size
						//Truncate to max length.
						//==> possible optimization.. change godata.GetFieldSize to use smaller dataset/datatype for this purpose
						int lLength = Convert.ToInt32(goData.GetFieldSize(sRSFile, sFieldName));
						long lLength2 = oValue.ToString().Length;
						if (lLength2 > lLength)
						{
							//==> RAH TODO: warning commented do to nvarchar unicode character length being double.  
							oValue = oValue.ToString().Substring(0, lLength);
							//goErr.SetWarning(10125, sProc, "", oValue, sRSFile + "." + sFieldName)
							//10125: Text written to field '[2]' was truncated: '[1]'   
						}
						oDataTable.Rows[(int)lCurrentRow][sFieldName] = oValue;
						break;
					}
				}

			}

			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If

			//End Try

			if (bInvalid)
			{
				return 0;
			}
			else
			{
				return 1;
			}

		}

		public int SetLinkVal(string sLinkName, object oLinks)
		{
			//MI 7/19/06 Added returning 0 when file is wrong, changed 'RETURNS' comment.	'*** MI 7/19/06

			//PURPOSE:
			//       Sets one or more links 		
			//PARAMETERS:
			//		sLinkName:  Name of the link field to clear
			//       oLinks: Either clArray of links or hard return delimited string of link IDs 
			//RETURNS:
			//		1 if successful, 0 if wrong file, errors if not.    '*** MI 7/19/06
			//AUTHOR: RH

			string sProc = "clRowset::SetLinkVal";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);
			string sTable = "";
			bool bWrongFile = false;

			//Try

			bRSIsDirty = true;
			//'Dim oLinkData As LinkDataStructure = cEditableLinks.Item(sLinkName)
			//' Moved inside of isloaded test

			//MI 1/21/10 Added testing other non-editable types
			//If iRSType = clC.SELL_READONLY Then goErr.SetError(45161, sProc)
			switch (iRSType)
			{
				case clC.SELL_EDIT:
				case clC.SELL_ADD:
				break;
				default:
					goErr.SetError(45161, sProc);
					break;
			}

			if (IsLoaded(sLinkName))
			{

				LinkDataStructure oLinkData = (LinkDataStructure)cEditableLinks[sLinkName];

				int i = 0;
				int iTable = (int)cLoadedLinks[(sLinkName + "%%GID_ID").ToUpper()];
				clArray oclArray = new clArray();

				if (oLinks.GetType().ToString().ToLower().Contains("clarray"))
				{
					oclArray = (Selltis.BusinessLogic.clArray)oLinks;
				}
				else
				{
					oLinks = oLinks.ToString().Replace("\r\n", "\n");
					oLinks = oLinks.ToString().Replace("\n", "\r\n");

					oclArray.StringToArray(Convert.ToString(oLinks));
				}

				for (i = 1; i <= oclArray.GetDimension(); i++)
				{

					if (oclArray.GetInfo(i).Length < 5)
					{
						bWrongFile = true;
					}
					else
					{
						sTable = goTR.GetFileFromSUID(oclArray.GetInfo(i));
						if (sTable == oLinkData.ToFile)
						{
							AddLink(iTable, sLinkName, oclArray.GetInfo(i));
						}
						else
						{
							bWrongFile = true;
						}
					}
				}

				if (bWrongFile)
				{
					return 0;
				}
				else
				{
					return 1;
				}

			}
			else
			{
				goErr.SetError(45160, sProc, "", sLinkName);
			}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(45160, sProc, , sLinkName)
			//    End If
			//End Try

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return 0;
		}
		public string GetFileName()
		{
			//Returns the rowset file
			return sRSFile;
		}
		public int GetFirst()
		{
				int tempGetFirst = 0;

			if (oDataTable.Rows.Count > 0)
			{
				lCurrentRow = 0;
				tempGetFirst = 1;
			}
			else
			{
				tempGetFirst = 0;
			}

			return tempGetFirst;
		}
		public int GetLast()
		{
				int tempGetLast = 0;

			if (oDataTable.Rows.Count > 0)
			{
				lCurrentRow = oDataTable.Rows.Count - 1;
				tempGetLast = 1;
			}
			else
			{
				tempGetLast = 0;
			}

			return tempGetLast;
		}
		public int GetNext()
		{
				int tempGetNext = 0;

			if (oDataTable.Rows.Count > 0)
			{
				if (lCurrentRow != oDataTable.Rows.Count - 1)
				{
					lCurrentRow = lCurrentRow + 1;
					tempGetNext = 1;
				}
				else
				{
					tempGetNext = 0;
				}
			}
			else
			{
				tempGetNext = 0;
			}

			return tempGetNext;
		}
		public int GetPrevious()
		{
				int tempGetPrevious = 0;

			if (oDataTable.Rows.Count > 0)
			{
				if (lCurrentRow != 0)
				{
					lCurrentRow = lCurrentRow - 1;
					tempGetPrevious = 1;
				}
				else
				{
					tempGetPrevious = 0;
				}
			}
			else
			{
				tempGetPrevious = 0;
			}

			return tempGetPrevious;
		}

		public int AddNext()
		{

			if (this.iRSType == clC.SELL_ADD)
			{
				SeedRecord();
				return 1;
			}
			else
			{
				return 0;
			}

		}
		public long GetLinkCount(string sLinkName)
		{

			//PURPOSE:
			//       Returns number of records currently linked for given link name		
			//PARAMETERS:
			//		sLinkName:  Name of the link field 
			//RETURNS:
			//		Number of links if successful, errors if not.
			//AUTHOR: RH

			string sProc = "clRowset::GetLinkCount";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);
			sLinkName = sLinkName.ToUpper();

			//Try
			if (IsLoaded(sLinkName))
			{

				int iTable = (int)cLoadedLinks[(sLinkName + "%%GID_ID").ToUpper()];
				DataTable dvTable = null;
				string expression = null;
				int i = 0;

				if (iTable == 0)
				{
					if (oDataSet.Tables[0].Rows[(int)lCurrentRow][sLinkName + "%%GID_ID"].ToString() == "")
					{
						return 0;
					}
					else
					{
						return 1;
					}
				}
				else
				{

					expression = "[BASE%%GID_ID]='" + oDataSet.Tables[0].Rows[(int)lCurrentRow]["GID_ID"].ToString() + "'";
					oDataSet.Tables[iTable].DefaultView.RowFilter = expression;
					oDataSet.Tables[iTable].DefaultView.RowStateFilter = DataViewRowState.CurrentRows;
					dvTable = oDataSet.Tables[iTable].DefaultView.ToTable();
					return dvTable.Rows.Count;

				}
			}
			else
			{
				goErr.SetError(45160, sProc, "", sLinkName);
			}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return 0;
		}
		public clArray GetLinkVal(string sLinkName, ref clArray oArray, bool bClearArray, int iFormat, int iRecs, string sSort)
		{
			DataTable tempVar = null;
			return GetLinkVal(sLinkName, ref oArray, bClearArray, iFormat, iRecs, sSort, ref tempVar);
		}

		public clArray GetLinkVal(string sLinkName, ref clArray oArray, bool bClearArray, int iFormat, int iRecs)
		{
			DataTable tempVar = null;
			return GetLinkVal(sLinkName, ref oArray, bClearArray, iFormat, iRecs, "A_a", ref tempVar);
		}

		public clArray GetLinkVal(string sLinkName, ref clArray oArray, bool bClearArray, int iFormat)
		{
			DataTable tempVar = null;
			return GetLinkVal(sLinkName, ref oArray, bClearArray, iFormat, -1, "A_a", ref tempVar);
		}

		public clArray GetLinkVal(string sLinkName, ref clArray oArray, bool bClearArray)
		{
			DataTable tempVar = null;
			return GetLinkVal(sLinkName, ref oArray, bClearArray, 0, -1, "A_a", ref tempVar);
		}

		public clArray GetLinkVal(string sLinkName, ref clArray oArray)
		{
			DataTable tempVar = null;
			return GetLinkVal(sLinkName, ref oArray, true, 0, -1, "A_a", ref tempVar);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Public Function GetLinkVal(ByVal sLinkName As String, ByRef oArray As clArray, Optional ByVal bClearArray As Boolean = True, Optional ByVal iFormat As Integer = 0, Optional ByVal iRecs As Integer = -1, Optional ByVal sSort As String = "A_a", Optional ByRef oTable As DataTable = Nothing) As clArray
		public clArray GetLinkVal(string sLinkName, ref clArray oArray, bool bClearArray, int iFormat, int iRecs, string sSort, ref DataTable oTable)
		{
			//MI 11/5/09 Added support for grouping date fields DTY DTQ DTM DTD.
			//MI 10/30/08 Added considering par_iLinksTop and par_sLinksTop parameters from New().
			//MI 10/1/08 Added dealing with SELL_SQLSTRING format, cleaned up and reorganized some code.
			//MI 9/30/08 in case clc.SELL_SQLSTRING, added adding the value to oArray. Until now this case was not returning anything.
			//MI 8/21/08 Fixed no of decimals returned by GetLinkVal when Def is used in FLD MD, <field>_FRM= property. GetFieldVal was fixed on 6/8/08.
			//MI 9/12/06 Added support for clc.SELL_SQLSTRING format

			//PURPOSE:
			//       Returns links		
			//PARAMETERS:
			//		sLinkName:  Name of the link field to clear
			//       oArray: ByRef clArray to return.  May be used to build collection 
			//               of links by passing in prepopulated array.
			//       bClearArray: If true then oArray is cleared before getting any new links.
			//       iFormat: Data format to return
			//           clC.SELL_SYSTEM         (2) 
			//           clC.SELL_FRIENDLY       (1)  
			//           clC.SELL_SQLSTRING      (4) 
			//           Format set to rowset default format if no value is not provided
			//       iRecs: MAX number of links to return. If provided, that number of linked
			//           records will be returned. If at default, then the number of links will
			//           be as defined in par_sLinksTop in New() PLUS ONE. If not defined there,
			//           it will be as defined in par_iLinksTop in New() PLUS ONE.
			//           The extra link record is returned for GetFieldVal to know when to
			//           add the ellipsis ("...").
			//       sSort: either ASC or DESC
			//       oTable: datatable passed by reference.
			//RETURNS:
			//		Link data if successful, errors if not.
			//AUTHOR: RH

			string sProc = "clRowset::GetLinkVal";
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)
			goLog.Log(sProc, "Start sFieldName: '" + sLinkName + "', iFormat: ' " + iFormat.ToString() + "'", (short)clC.SELL_LOGLEVEL_DEBUG, true);
			sLinkName = sLinkName.ToUpper();
			bBypassSysNameUpdateLimit = false;
			if (this.iRSType == clC.SELL_ADD)
			{
				//force bypass when add rs
				bBypassSysNameUpdateLimit = true;
			}

			bool byPassSort = false;
			var sFieldInSkipSort = Convert.ToString(oVar.GetVar("LNKFieldInSkipSORT"));

			if (!string.IsNullOrEmpty(sFieldInSkipSort) && sFieldInSkipSort.Contains(sLinkName))
			{
				byPassSort = true;
			}

			if (sSort == "A_a")
			{

				string sDefaultSort = goData.GetDefaultSort(goTR.GetFileFromLinkName(sLinkName));

				if (sDefaultSort == "SYS_NAME ASC")
				{
					sSort = "A";
				}
				else if (sDefaultSort == "SYS_NAME DESC")
				{
					sSort = "D";
				}

				if (sSort == "A_a")
				{
					sSort = "A";
				}

			}


			if (this.Count() == 0)
			{
				goErr.SetError(35000, sProc, "Unable to return the requested value.  The rowset does not have any records.");
			}

			sSort = sSort.ToUpper();
			sSort = sSort.Substring(0, 1);
			if (sSort == "A")
			{
				sSort = "ASC";
			}
			else
			{
				sSort = "DESC";
			}

			sLinkName = sLinkName.ToUpper();

			DataSet ds = this.oDataSet;
			int iRecsOrig = iRecs;

			if (bClearArray == true)
			{
				oArray.ClearArray();
			}

			long l = 0;
			string[] sSplit = new string[1];
			string sField = "";
			string sFile = "";
			clArray oArrayHolder = oArray;
			clArray oArrayReducer = null;

			if (iFormat == 0) //Set Data format to default if not passed be caller
			{
				iFormat = iDataFormat;
			}

			//Try

			//MI 10/30/08 added this section
			//****************************************       
			//Determine no of linked records to return             
			//****************************************
			if (goTR.GetPrefix(sLinkName) == "LNK_")
			{
				//Is iRecs parameter defined?
				if (iRecs < 0)
				{
					//iRecs parameter not defined; use par_iLinksTop from New() if it is defined
					if (iLinksTop >= 0)
					{
						//par_iLinksTop in New() is defined, use it
						//We return one link more than iLinksTop defines so that GetFieldVal knows when to add '...'
						iRecs = iLinksTop + 1;
					}
					else
					{
						//par_iLinksTop in New() not defined, is the link defined in par_sLinksTop in New()?
						//Return one link more than defined for GetFieldVal to know when to add '...'.
						iRecs = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(this.sLinksTopIni, sLinkName, (iRecs - 1).ToString(), false), "0") + 1);
					}
				}
			}



			if (IsLoaded(sLinkName))
			{
				int iTable = 0;
				//Dim dvTable As DataTable   'Unused
				string expression = null;
				int i = 0;
				string sSysName = "";
				string sGID = "";
				string sPrefix = null; //*** MI 9/27/07
				string sLinkField = null; //*** MI 9/27/07
				object oValue = null;
				string sTemp = null; //*** MI 10/9/07
				string sFile2 = null; //*** MI 10/9/07 moved here - was declared inside the code and it's now used in more than one place
				int iDecimals = 0; //*** MI 10/9/07

				if (sLinkName.IndexOf("%%") + 1 == 0)
				{
					sLinkName = sLinkName + "%%GID_ID";
				}

				sSysName = goTR.ExtractString(sLinkName, 1, "%%") + "%%SYS_NAME";
				sGID = goTR.ExtractString(sLinkName, 1, "%%") + "%%GID_ID";

				sSplit = Microsoft.VisualBasic.Strings.Split(sLinkName, "%%");

				sField = sSplit[sSplit.GetUpperBound(0)]; //In LNK_Related_CO%%SYS_Name: 'SYS_Name'
				sLinkField = sField; //goTR.ExtractString(sLinkName, 2, "%%") 'MI 10/1/08 commented ExtractString 'In LNK_Related_CO%%SYS_Name: 'SYS_Name'
				sFile = goTR.GetFileFromLinkName(sSplit[sSplit.GetUpperBound(0) - 1]); //In LNK_Related_CO%%SYS_Name: 'CO' 'MI 10/1/08 replaced Right(sSplit..., 2) with goTr.GetFileFromLinkName
				sPrefix = goTR.GetPrefix(sField); //In LNK_Related_CO%%SYS_Name: 'SYS_'

				string sTempLinkName = sLinkName.ToUpper(); //In LNK_Related_CO%%SYS_Name: 'LNK_RELATED_CO%%SYS_NAME'
				//If sTempLinkName.EndsWith("SYS_NAME") Then
				//    sTempLinkName = goTr.Replace(sTempLinkName, "SYS_NAME", "GID_ID")
				//End If

				if (sTempLinkName.IndexOf("%%DTE_") + 1 > 0)
				{
					sTempLinkName = goTR.Replace(sTempLinkName, "%%DTE_", "%%DTT_");
				}
				else if (sTempLinkName.IndexOf("%%TME_") + 1 > 0)
				{
					sTempLinkName = goTR.Replace(sTempLinkName, "%%TME_", "%%DTT_");
				}
				else if (sTempLinkName.IndexOf("%%TML_") + 1 > 0)
				{
					sTempLinkName = goTR.Replace(sTempLinkName, "%%TML_", "%%DTT_");
				}

				//sLinkName is normalized above to always have %%FieldName - don't change that or edit this block!

				iTable = (int)cLoadedLinks[sTempLinkName.ToUpper()];

				//LNK is in the BASE Table
				if (iTable == 0)
				{
					//check if sys_name missing, if missing, acquire and set.

					try
					{
						if (iRSType == clC.SELL_EDIT || iRSType == clC.SELL_ADD) //MI 1/21/10 Changed from 'If iRSType <> 3 Then'. This wasn't taking into account GROUPBY, COUNT, etc.
						{
							try
							{
								if (oDataSet.Tables[0].Rows[(int)lCurrentRow][sSysName].GetType().ToString() == "System.DBNull")
								{
									oDataSet.Tables[0].Rows[(int)lCurrentRow][sSysName] = goData.GetSysNameForGID(oDataSet.Tables[0].Rows[(int)lCurrentRow][sGID].ToString());

								}
								else
								{

									if (oDataSet.Tables[0].Rows[(int)lCurrentRow][sSysName] == "" || bUpdateSysNames == true)
									{
										oDataSet.Tables[0].Rows[(int)lCurrentRow][sSysName] = goData.GetSysNameForGID(oDataSet.Tables[0].Rows[(int)lCurrentRow][sGID].ToString());
									}
								}



							}
							catch (Exception ex)
							{
								if (oDataSet.Tables[0].Rows[(int)lCurrentRow][sSysName].GetType().ToString() == "System.DBNull")
								{
									oDataSet.Tables[0].Rows[(int)lCurrentRow][sSysName] = goData.GetSysNameForGID(oDataSet.Tables[0].Rows[(int)lCurrentRow][sGID].ToString());
								}
							}
						}
					}
					catch (Exception exOK)
					{
						//Expect error if linked field..
					}


					if (oDataSet.Tables[0].Rows[(int)lCurrentRow][sTempLinkName].ToString() != "") //MMM
					{
						//A record is linked
						//Make sure that DTE_, TME_, TML_ are replaced with DTT_ above   '*** MI 9/27/07
						switch (sPrefix) //*** MI 10/9/07 changed If Then Else to Select Case
						{
							case "DTT_": //*** MI 9/27/07
								//Test DBNull to prevent an error when calling goTR.UTC_UTCToLocal().    '*** MI 10/3/07
								if (oDataSet.Tables[0].Rows[(int)lCurrentRow][sTempLinkName].GetType().ToString() == "System.DBNull") //*** MI 10/3/07
								{
									//MI 11/5/09 Commenting converting a blank datetime to local
									//oValue = goTR.UTC_UTCToLocal(goTR.StringToDateTime(""))  '*** MI 10/3/07
									oValue = clC.SELL_BLANK_DTDATETIME; //MI 11/5/09 added
								}
								else //*** MI 10/3/07
								{
									oValue = goTR.UTC_UTCToLocal((DateTime)oDataSet.Tables[0].Rows[(int)lCurrentRow][sTempLinkName]); //*** MI 9/27/07
								} //*** MI 10/3/07
								break;
							case "DR__":
							case "SR__":
							case "CUR_": //*** MI 10/9/07 added
								//Get the field format that defines the no of decimals   '*** MI 10/9/07 added
								sTemp = goTR.ExtractString(goData.GetFieldFormat(sFile, sField), 1, "\t"); //*** MI 10/9/07 added
								if (sTemp == "" || sTemp[0] == clC.EOT) //*** MI 10/9/07 added
								{
									//Decimals not specified, do not round    '*** MI 10/9/07 added 
									oValue = oDataSet.Tables[0].Rows[(int)lCurrentRow][sTempLinkName]; //*** MI 10/9/07 added
								}
								else //*** MI 10/9/07 added
								{
									//Decimals specified, round    '*** MI 10/9/07 added 
									if (sTemp.ToUpper() == "DEF") //*** MI 8/21/08
									{
										//Default no of decimals - let's find what that default is    '*** MI 8/21/08
										sTemp = goTR.ExtractString(goTR.GetCurrFormat(), 1, "\t"); //*** MI 8/21/08
									} //*** MI 8/21/08
									iDecimals = Convert.ToInt32(goTR.StringToNum(sTemp, "0")); //*** MI 10/9/07 added
									oValue = Math.Round(Convert.ToDouble(oDataSet.Tables[0].Rows[(int)lCurrentRow][sTempLinkName]), iDecimals); //*** MI 10/9/07 added
								} //*** MI 10/9/07 added
								break;
							default: //*** MI 9/27/07
								//Includes Case "DTY_", "DTQ_", "DTM_", "DTD_"  'MI 11/5/09 Added DTY DTQ DTM DTD
								oValue = oDataSet.Tables[0].Rows[(int)lCurrentRow][sTempLinkName]; //*** MI 9/27/07
								break;
						} //*** MI 9/27/07

						//MI 10/1/08 Replace the following block with the one below. This wasn't dealing with SELL_SQLSTRING format.
						//If iFormat = clC.SELL_FRIENDLY Then
						//    If InStr(sLinkName, "%%") > 0 Then
						//        'sFile2 = goTR.ExtractString(goTR.ExtractString(sLinkName, 1, "%%"), 3, "_") '*** MI 10/9/07 commented old methodology
						//        sFile2 = goTR.GetFileFromLinkName(sLinkName)    '*** MI 10/9/07 replaced the above with this
						//        'oArray.Add(GetFriendly(sFile2, goTR.ExtractString(sLinkName, 2, "%%"), oDataSet.Tables(0).Rows(lCurrentRow).Item(sTempLinkName))) 'MMM '*** MI 9/27/07 commented
						//        oArray.Add(GetFriendly(sFile2, sField, oValue)) 'MMM '*** MI 9/27/07 added
						//    Else
						//        'oArray.Add(oDataSet.Tables(0).Rows(lCurrentRow).Item(sTempLinkName).ToString)  '*** MI 9/27/07 commented
						//        oArray.Add(oValue.ToString)  '*** MI 9/27/07 added
						//    End If
						//Else
						//    'oArray.Add(oDataSet.Tables(0).Rows(lCurrentRow).Item(sTempLinkName).ToString)  '*** MI 9/27/07 commented
						//    oArray.Add(oValue.ToString)  '*** MI 9/27/07 added
						//End If

						//MI 10/1/08 Added the following section (wasn't dealing with SELL_SQLSTRING format until now)
						if (iFormat == clC.SELL_SYSTEM)
						{
							oArray.Add(oValue.ToString());
						}
						else if (iFormat == clC.SELL_SQLSTRING)
						{
							oArray.Add(goTR.FieldToSQLString(oValue, sField, clC.SELL_TYPE_INVALID, sFile));
						}
						else if (iFormat == clC.SELL_FRIENDLY)
						{
							oArray.Add(Convert.ToString(GetFriendly(sFile, sField, oValue)));
						}

					}


					//If oTable object passed in create datatable and set link info into oTable
					if (oTable == null)
					{

					}
					else
					{
						DataTable dt = new DataTable();
						DataColumn dc = null;
						DataRow dr = null;

						dc = new DataColumn(sSysName, typeof(string));
						dt.Columns.Add(dc);
						dc = new DataColumn(sGID, typeof(string));
						dt.Columns.Add(dc);
						dr = dt.NewRow();
						dr[sSysName] = oDataSet.Tables[0].Rows[(int)lCurrentRow][sSysName].ToString();
						dr[sGID] = oDataSet.Tables[0].Rows[(int)lCurrentRow][sGID].ToString();
						dt.Rows.Add(dr);
						oTable = dt;
					}
				}
				else
				{
					//LNK is in the SECONDARY Tables


					expression = "[BASE%%GID_ID]='" + oDataSet.Tables[0].Rows[(int)lCurrentRow]["GID_ID"].ToString() + "'";
					//==>Test for missing sys_name.  If any, acquire and set.  All are needed for correct sort.
					//MI 1/21/10 Testing editable types explicitly
					//If Me.iRSType <> clC.SELL_READONLY Then
					if (this.iRSType == clC.SELL_EDIT || iRSType == clC.SELL_ADD)
					{
						UpdateMissingSysNames(expression, iTable, sSysName, sGID);
					}
					if (oTable == null)
					{
					}
					else
					{
						oTable = oDataSet.Tables[iTable].Clone();
					}


					DataRow[] returnValue = null;
					DataRow dr = null;
					//returnValue = oDataSet.Tables(iTable).Select(expression, sSysName & " " & sSort, DataViewRowState.CurrentRows)

					//'VT 4/28/21 -- skips the sorting of the items
					if (byPassSort)
					{
						returnValue = oDataSet.Tables[iTable].Select(expression, "", DataViewRowState.CurrentRows);
					}
					else
					{
						returnValue = oDataSet.Tables[iTable].Select(expression, sSysName + " " + sSort, DataViewRowState.CurrentRows);
					}


					for (i = returnValue.GetLowerBound(0); i <= returnValue.GetUpperBound(0); i++)
					{

						dr = returnValue[i];

						if (sLinkName.IndexOf("%%DTE_") + 1 > 0)
						{
							sLinkName = goTR.Replace(sLinkName, "%%DTE_", "%%DTT_");
						}
						else if (sLinkName.IndexOf("%%TME_") + 1 > 0)
						{
							sLinkName = goTR.Replace(sLinkName, "%%TME_", "%%DTT_");
						}
						else if (sLinkName.IndexOf("%%TML_") + 1 > 0) //*** MI 9/27/07
						{
							sLinkName = goTR.Replace(sLinkName, "%%TML_", "%%DTT_"); //*** MI 9/27/07
						}


						//sLinkName is normalized above to always have %%FieldName - don't change that or edit this block!    '*** MI 9/27/07
						sPrefix = goTR.GetPrefix(sField); //*** MI 9/27/07

						switch (sPrefix)
						{
							case "DTT_": //*** MI 9/27/07 added
								//Convert UTC to local    '*** MI 9/27/07 added 
								//Test DBNull to prevent an error when calling goTR.UTC_UTCToLocal()   '*** MI 10/3/07 added
								if (dr[sLinkName].GetType().ToString() == "System.DBNull") //*** MI 10/3/07
								{
									//MI 11/5/09 Replaced converting a blank datetime to local with blank datetime
									//oValue = goTR.UTC_UTCToLocal(goTR.StringToDateTime(""))  '*** MI 9/27/07
									oValue = clC.SELL_BLANK_DTDATETIME; //MI 11/5/09
								}
								else //*** MI 10/3/07
								{
									oValue = goTR.UTC_UTCToLocal((DateTime)dr[sLinkName]); //*** MI 9/27/07
								} //*** MI 10/3/07
								break;
							case "DR__":
							case "SR__":
							case "CUR_": //*** MI 10/9/07 added
								sFile2 = goTR.GetFileFromLinkName(sLinkName); //*** MI 10/9/07 added
								//Get the field format that defines the no of decimals   '*** MI 10/9/07 added
								sTemp = goTR.ExtractString(goData.GetFieldFormat(sFile2, sField), 1, "\t"); //*** MI 10/9/07 added
								if (sTemp == "" || sTemp[0] == clC.EOT) //*** MI 10/9/07 added
								{
									//Decimals not specified, do not round    '*** MI 10/9/07 added 
									oValue = dr[sLinkName]; //*** MI 10/9/07 added
								}
								else //*** MI 10/9/07 added
								{
									//Decimals specified, round    '*** MI 10/9/07 added 
									if (sTemp.ToUpper() == "DEF") //*** MI 8/21/08
									{
										//Default no of decimals - let's find what that default is    '*** MI 8/21/08
										sTemp = goTR.ExtractString(goTR.GetCurrFormat(), 1, "\t"); //*** MI 8/21/08
									} //*** MI 8/21/08
									iDecimals = Convert.ToInt32(goTR.StringToNum(sTemp, "0")); //*** MI 10/9/07 added
									oValue = Math.Round(Convert.ToDouble(dr[sLinkName]), iDecimals); //*** MI 10/9/07 added
								} //*** MI 10/9/07 added
								break;
							default: //*** MI 9/27/07
								//Includes Case "DTY_", "DTQ_", "DTM_", "DTD_"   'MI 11/5/09 added DTY DTQ DTM DTD
								oValue = dr[sLinkName]; //*** MI 9/27/07
								break;
						} //*** MI 9/27/07

						if (iFormat == clC.SELL_SYSTEM)
						{
							oArray.Add(oValue.ToString()); //*** MI 9/27/07
						}
						else if (iFormat == clC.SELL_SQLSTRING)
						{
							oArray.Add(goTR.FieldToSQLString(oValue, sField, clC.SELL_TYPE_INVALID, sFile)); //MI 9/30/08 Added oArray.Add(), this case was not doing anything! '*** MI 9/27/07
						}
						else if (iFormat == clC.SELL_FRIENDLY)
						{
							oArray.Add(Convert.ToString(GetFriendly(sFile, sField, oValue))); //*** MI 9/27/07
						}
						if (oTable == null)
						{
						}
						else
						{
							oTable.Rows.Add(dr.ItemArray);
						}
					}

					if (oTable == null)
					{
					}
					else
					{
						oTable.Columns.Remove("GID_ID");
						oTable.Columns.Remove("BASE%%GID_ID");
					}

				}

			}
			else
			{

				//LNK Not loaded

				string sLinkName2 = goTR.ExtractString(sLinkName, 1, "%%");
				string sLinkField = goTR.ExtractString(sLinkName, 2, "%%");
				string sLinkedLink = goTR.ExtractString(sLinkName, 3, "%%");
				int iLinkCount = 0;
				clArray oArrayLinks = new clArray();
				clArray oArrayScooper = new clArray();


				int j = 0;
				clArray oArray2 = new clArray();

				if (sLinkedLink[0] == clC.EOT)
				{
					sLinkedLink = "GID_ID";
				}

				string sLinkFullName = sLinkField + "%%" + sLinkedLink;

				if (IsLoaded(sLinkName2))
				{
					iLinkCount = (int)this.GetLinkCount(sLinkName2);
				}
				else
				{
					if (GetMissingLinkorLinks(sLinkName2) == true)
					{
						return GetLinkVal(sLinkName, ref oArray, bClearArray, iFormat, iRecs, sSort, ref oTable);
					}
					else
					{
						goErr.SetError(45163, sProc, "", sLinkName2); //xxx change to formal error number
					}


				}

				if (iRecs != -1)
				{
					if (iRecs > iLinkCount)
					{
						iRecs = iLinkCount;
					}
				}
				else
				{
					iRecs = iLinkCount;
				}

				this.GetLinkVal(sLinkName2, ref oArray2, true, 0, -1, sSort);

				if (sLinkField.ToUpper().Substring(0, 3) == "LNK")
				{
					//Dim oMergeTable As New DataTable
					//Dim oWorkTable As New DataTable

					for (j = 1; j <= iRecs; j++)
					{
						sFile = goTR.GetFileFromSUID(oArray2.GetItem(j));
						clRowSet rs = new clRowSet(sFile, 3, "GID_ID='" + oArray2.GetItem(j) + "'", "", sLinkFullName);
						if (rs.Count() > 0)
						{
							rs.GetLinkVal(sLinkFullName, ref oArrayScooper, true, iFormat);
							if (oArrayScooper.GetDimension() > 0)
							{
								oArrayScooper.CopyAllIn(ref oArrayLinks);
							}
							else
							{
								//oArrayLinks.Add("")
							}
						}
					}

					if (bClearArray == true)
					{
						oArray = oArrayLinks;
					}
					else
					{
						oArrayLinks.CopyAllIn(ref oArray);
					}

				}
				else
				{

					//Not LNK
					for (j = 1; j <= iRecs; j++)
					{
						oArray.Add(goData.GetFieldValueFromRec(oArray2.GetItem(j), sLinkField, iFormat).ToString());
					}
				}
			}


			if (oTable == null)
			{
			}
			else
			{
				if (oTable.Rows.Count == 1)
				{
					if (oTable.Rows[0][0] == null && oTable.Rows[0][1] == "")
					{
						oTable.Rows[0].Delete();
						oTable.AcceptChanges();
					}
				}
			}

			if (iRecsOrig != -1)
			{
				if (oArray.GetDimension() > iRecs)
				{
					oArrayReducer = new clArray();
					int z = 0;
					for (z = 1; z <= iRecs; z++)
					{
						oArrayReducer.Add(oArray.GetItem(z));
					}
					oArray = oArrayReducer;
				}
			}
			bBypassSysNameUpdateLimit = false;

			return oArray;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then

			//        goLog.Log(sProc, sLinkName, 3, True, True)
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return oArray;

		}

		private int GetLinkTable(string sLinkName)
		{

			try
			{
				if (sLinkName.IndexOf("%%") + 1 == 0)
				{
					sLinkName = sLinkName + "%%GID_ID";
				}

				string sTempLinkName = sLinkName.ToUpper(); //In LNK_Related_CO%%SYS_Name: 'LNK_RELATED_CO%%SYS_NAME'


				if (sTempLinkName.IndexOf("%%DTE_") + 1 > 0)
				{
					sTempLinkName = goTR.Replace(sTempLinkName, "%%DTE_", "%%DTT_");
				}
				else if (sTempLinkName.IndexOf("%%TME_") + 1 > 0)
				{
					sTempLinkName = goTR.Replace(sTempLinkName, "%%TME_", "%%DTT_");
				}
				else if (sTempLinkName.IndexOf("%%TML_") + 1 > 0)
				{
					sTempLinkName = goTR.Replace(sTempLinkName, "%%TML_", "%%DTT_");
				}

				//sLinkName is normalized above to always have %%FieldName - don't change that or edit this block!

				return (int)cLoadedLinks[sTempLinkName.ToUpper()];
			}
			catch (Exception ex)
			{
				return -1;
			}

		}




		public bool GetMissingLinkorLinks(string sLinkNames)
		{

			//PURPOSE:
			//       Loads missing link or links	
			//PARAMETERS:
			//		sLinkNames:  comma delimited list of links
			//RETURNS:
			//		true on success or if already loaded, false if not
			//AUTHOR: RH

			if (iRSType == clC.SELL_ADD)
			{
				return false;
			}
			if (sLinkNames.ToUpper().IndexOf("%%LNK") + 1 > 0)
			{
				return false;
			}

			if (sLinkNames.IndexOf(",") + 1 > 0)
			{

				string[] sFields = sLinkNames.Split(',');
				int q = 0;
				sLinkNames = "";

				for (q = sFields.GetLowerBound(0); q <= sFields.GetUpperBound(0); q++)
				{
					if (IsLoadedNoLoad(sFields[q].Trim(' ')) == false)
					{
						sLinkNames = sLinkNames + "," + sFields[q];
					}
				}
				sLinkNames = sLinkNames.Substring(1);
			}
			else
			{
				if (IsLoadedNoLoad(sLinkNames) == true)
				{
					return true;
				}
			}

			string sCondition = goTR.StrRead(sIntermediate, "CONDITION");
			long lTop = Convert.ToInt64(goTR.StrRead(sIntermediate, "TOP", "-1"));
			string sSort = goTR.StrRead(sIntermediate, "SORT", "");

			clRowSet rs = new clRowSet(this.sRSFile, clC.SELL_READONLY, sCondition, sSort, sLinkNames, (int)lTop, "", "", "", "", "", false, false, true);
			if (rs.Count() == 0)
			{
				return false;
			}

			int i = 0;
			int k = 0;
			int y = oDataSet.Tables.Count - 1;
			string sSysName = "";
			string sLinkName = "";
			LinkDataStructure LinkData = new LinkDataStructure();
			string sLinkType = "";
			DataRow dr = null;
			DataTable dt = null;


			if (rs.oDataSet.Tables.Count > 1) //Link tables exist
			{

// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of rs.oDataSet.Tables(1).Rows.Count for every iteration:
				int tempVar = rs.oDataSet.Tables[1].Rows.Count;
				for (i = 0; i < tempVar; i++)
				{

					if (rs.oDataSet.Tables[1].Rows[i]["LinkName"].ToString().EndsWith("GID_ID"))
					{
						sSysName = rs.oDataSet.Tables[1].Rows[i]["LinkName"].ToString();
						sSysName = goTR.Replace(sSysName, "%%GID_ID", "%%SYS_NAME");

// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of rs.oDataSet.Tables(i + 2).Columns.Count for every iteration:
						int tempVar2 = rs.oDataSet.Tables[i + 2].Columns.Count;
						for (k = 0; k < tempVar2; k++)
						{
							if (rs.oDataSet.Tables[i + 2].Columns[k].ColumnName.ToUpper() == sSysName.ToUpper())
							{
								if (!IsLoaded(sSysName))
								{
									cLoadedLinks.Add(sSysName.ToUpper());
								}
							}
						}
					}

					//Try
					if (oDataSet.Tables.Count == 1)
					{
						dt = rs.oDataSet.Tables[1].Clone();
						dt.TableName = "Links";
						oDataSet.Tables.Add(dt);
						y = 1;
					}

					cLoadedLinks.Add(y + 1, rs.oDataSet.Tables[1].Rows[i]["LinkName"].ToString().ToUpper());

					y = y + 1;
					dr = rs.oDataSet.Tables[1].Rows[i];
					oDataSet.Tables[1].ImportRow(dr);
					dt = rs.oDataSet.Tables[i + 2].Copy();
					dt.TableName = "Table" + y;
					oDataSet.Tables.Add(dt);


					//Catch ex As Exception
					//    'sTest = sTest
					//End Try

					if (rs.oDataSet.Tables[1].Rows[i]["LinkName"].ToString().IndexOf("%%") + 1 > 0)
					{
						sLinkName = rs.oDataSet.Tables[1].Rows[i]["LinkName"].ToString().Substring(0, rs.oDataSet.Tables[1].Rows[i]["LinkName"].ToString().IndexOf("%%"));
					}
					else
					{
						sLinkName = Convert.ToString(rs.oDataSet.Tables[1].Rows[i]["LinkName"].ToString());
					}

					if (IsTableEditable(rs.oDataSet.Tables[1].Rows[i]["LinkName"].ToString()))
					{

						LinkData = new LinkDataStructure();

						sLinkType = goData.LK_GetType(sRSFile, sLinkName);
						LinkData.FQLinkName = goTR.ExtractString(sRSFile + "." + sLinkName, 1);
						LinkData.FromFile = goTR.ExtractString(sLinkType, 4);
						LinkData.Link = sLinkName;
						if (goTR.ExtractString(sLinkType, 1) == "N")
						{
							LinkData.LinkAllowsMultiple = true;
						}
						else
						{
							LinkData.LinkAllowsMultiple = false;
						}
						LinkData.LinkDirection = Convert.ToInt32(goTR.ExtractString(sLinkType, 2));
						LinkData.LinkID = goTR.ExtractString(sLinkType, 7);
						LinkData.LinkLabel = goTR.ExtractString(sLinkType, 6);
						LinkData.LinkType = goTR.ExtractString(sLinkType, 5);
						LinkData.ToFile = goTR.ExtractString(sLinkType, 3);
						cEditableLinks.Add(LinkData, sLinkName);

					}
				}
			}

			DataTable dtholder;
			dtholder = rs.oDataSet.Tables[0];
			if (dtholder.Columns.Contains("sys_name"))
			{
				dtholder.Columns.Remove("sys_name");
			}

			oDataSet.Tables[0].Merge(dtholder, false, MissingSchemaAction.Add);

			for (i = 0; i < dtholder.Columns.Count; i++)
			{

				if (dtholder.Columns[i].ColumnName.IndexOf("%%") + 1 > 0 && goTR.GetPrefix(dtholder.Columns[i].ColumnName) == "LNK_")
				{

					cLoadedLinks.Add(0, dtholder.Columns[i].ColumnName.ToUpper());

					//MI 1/21/10 This test is redundant, should this be added in an Else case?
					if (dtholder.Columns[i].ColumnName.IndexOf("%%") + 1 > 0)
					{
						sLinkName = dtholder.Columns[i].ColumnName.Substring(0, dtholder.Columns[i].ColumnName.IndexOf("%%"));
					}
					else
					{
						sLinkName = dtholder.Columns[i].ColumnName;
					}

					if (IsTableEditable(dtholder.Columns[i].ColumnName))
					{

						LinkData = new LinkDataStructure();

						sLinkType = goData.LK_GetType(sRSFile, sLinkName);
						LinkData.FQLinkName = goTR.ExtractString(sRSFile + "." + sLinkName, 1);
						LinkData.FromFile = goTR.ExtractString(sLinkType, 4);
						LinkData.Link = sLinkName;
						if (goTR.ExtractString(sLinkType, 1) == "N")
						{
							LinkData.LinkAllowsMultiple = true;
						}
						else
						{
							LinkData.LinkAllowsMultiple = false;
						}
						LinkData.LinkDirection = Convert.ToInt32(goTR.ExtractString(sLinkType, 2));
						LinkData.LinkID = goTR.ExtractString(sLinkType, 7);
						LinkData.LinkLabel = goTR.ExtractString(sLinkType, 6);
						LinkData.LinkType = goTR.ExtractString(sLinkType, 5);
						LinkData.ToFile = goTR.ExtractString(sLinkType, 3);
						cEditableLinks.Add(LinkData, sLinkName);

					}
				}
			}



			rs = null;
			return true;

		}
		public bool IsLinkEmpty(string sLinkName)
		{


			//PURPOSE:
			//       Test whether link is empty or not		
			//PARAMETERS:
			//		sLinkName:  Name of the link field
			//RETURNS:
			//		true if empty, false if not
			//AUTHOR: RH

			string sProc = "clRowset::IsLinkEmpty";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//Try
			if (IsLoaded(sLinkName))
			{
				if (GetLinkCount(sLinkName) == 0)
				{
					return true;
				}
				else
				{
					return false;
				}
			}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}
		public int SeekRecordByID(object oID)
		{
				int tempSeekRecordByID = 0;

			//PURPOSE:
			//       Tests whether record is in rowset and makes current if found		
			//PARAMETERS:
			//		oID: LinkValue GID of string
			//RETURNS:
			//		1 if found and set, 0 if not
			//AUTHOR: RH

			string sProc = "clRowset::SeekRecordByID";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			try
			{
				lCurrentRow = oDataTable.Rows.IndexOf(oDataTable.Rows.Find(oID));

				if (lCurrentRow != -1)
				{
					tempSeekRecordByID = 1;
				}
				else
				{
					tempSeekRecordByID = 0;
				}

			}
			catch (Exception ex)
			{

				return 0;

			}



			return tempSeekRecordByID;
		}
		public object GetCurrentRecID(int iFormat = 0)
		{

			//PURPOSE:
			//       Returns ID of current record		
			//PARAMETERS:
			//		iFormat: Data format to return
			//RETURNS:
			//		ID as GID or string
			//AUTHOR: RH

			string sProc = "clRowset::GetCurrentRecID";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//Try

			if (iFormat == 0) //Set Data format to default if not passed be caller
			{
				iFormat = iDataFormat;
			}
			//Validate format
			if (iFormat != clC.SELL_SYSTEM && iFormat != clC.SELL_FRIENDLY)
			{
				goErr.SetError(45162, sProc, "", iFormat.ToString());
			}

			if (iFormat == clC.SELL_SYSTEM)
			{
				return oDataTable.Rows[(int)lCurrentRow]["GID_ID"];
			}
			else if (iFormat == clC.SELL_FRIENDLY)
			{
				return oDataTable.Rows[(int)lCurrentRow]["GID_ID"].ToString();
			}

			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If

			//End Try

			return "";

		}
		public string GetInfo(string par_sProperty)
		{

			//PURPOSE:
			//       Returns information about the rowset		
			//PARAMETERS:
			//		par_sProperty: Data format to return
			//           Supported values include:
			//           Public Const SELL_ROWSET_TYPE = "TYPE"
			//           Public Const SELL_ROWSET_FIELDS = "FIELDS"
			//           Public Const SELL_ROWSET_CONDITION = "CONDITION"
			//           Public Const SELL_ROWSET_SORT = "SORT"
			//           Public Const SELL_ROWSET_TOP = "TOP"
			//           Public Const SELL_ROWSET_TABLE = "TABLE"
			//RETURNS:
			//		Value of requested rowset property
			//AUTHOR: RH
			//EXAMPLE:
			//       If oRS.GetInfo(clC.SELL_ROWSET_TYPE) = "3" Then msgbox("The RowSet is read only.")


			string sProc = "clRowset::GetInfo";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			goLog.Log(sProc, "Start par_sProperty: '" + par_sProperty + "'", (short)clC.SELL_LOGLEVEL_DEBUG, true);
			//goLog.Log(sProc, "End", clC.SELL_LOGLEVEL_DEBUG, True)





			try
			{



				par_sProperty = par_sProperty.ToUpper();

				switch (par_sProperty)
				{


					case clC.SELL_ROWSET_TYPE:
						return iRSType.ToString();

					case clC.SELL_ROWSET_CONDITION:
						return goTR.StrRead(sIntermediate, "CONDITION", null, false);

					case clC.SELL_ROWSET_FIELDS:
						return goTR.StrRead(sIntermediate, "FIELDS", null, false);

					case clC.SELL_ROWSET_SORT:
						return goTR.StrRead(sIntermediate, "SORT", null, false);

					case clC.SELL_ROWSET_TABLE:
						return sRSFile;

					case clC.SELL_ROWSET_TOP:
						return goTR.StrRead(sIntermediate, "TOP", null, false);

					default:
						goErr.SetError(46168, sProc, "", par_sProperty);
						//The rowset property requested, '[1]', is invalid.
						return "";

				}


			}
			catch (Exception ex)
			{

				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return "";

			}
		}

		public bool UpdateLinkState(string par_sLink = "ALL")
		{


			//PURPOSE:
			//       Reconciles all links or given link with state of link(s) from disk		
			//PARAMETERS:
			//		par_sLink: Name of link or "" and "ALL" for all links
			//RETURNS:
			//		True if successful
			//AUTHOR: RH
			//EXAMPLE:
			//       oRS.UpdateLinkState("") 

			clArray oArray = new clArray();
			clArray oLinks = null;
			string sLinks = "";
			int i = 0;
			int j = 0;


			string sProc = "clRowset::UpdateLinkState";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			if (this.iRSType == clC.SELL_ADD)
			{
				return true;
			}

			try
			{

				sBaseGID = oDataTable.Rows[(int)lCurrentRow]["GID_ID"].ToString();

				clRowSet oRS = new clRowSet(this.sRSFile, clC.SELL_READONLY, "GID_ID='" + this.sBaseGID + "'", "", "**", 1);

				if (par_sLink == "ALL")
				{
					oLinks = goData.GetLinks(this.sRSFile);
				}
				else
				{
					oLinks = new clArray();
					oLinks.Add(par_sLink);
				}

				for (i = 1; i <= oLinks.GetDimension(); i++)
				{

					oRS.GetLinkVal(oLinks.GetItem(i), ref oArray, true);

					for (j = 1; j <= oArray.GetDimension(); j++)
					{
						if (!this.LinkState(oLinks.GetItem(i), oArray.GetItem(j), "EXISTS"))
						{
							if (!this.LinkState(oLinks.GetItem(i), oArray.GetItem(j), "DELETED"))
							{
								this.SetLinkVal(oLinks.GetItem(i), oArray.GetItem(j));
							}
						}
					}

					this.GetLinkVal(oLinks.GetItem(i), ref oArray, true);

					for (j = 1; j <= oArray.GetDimension(); j++)
					{
						if (!oRS.LinkState(oLinks.GetItem(i), oArray.GetItem(j), "EXISTS"))
						{
							if (!this.LinkState(oLinks.GetItem(i), oArray.GetItem(j), "ADDED"))
							{
								this.ClearLink(oLinks.GetItem(i), oArray.GetItem(j));
							}
						}
						if (goData.IsRecordValid(oArray.GetItem(j)) == false)
						{
							this.ClearLink(oLinks.GetItem(i), oArray.GetItem(j));
						}


					}
				}

			}
			catch (Exception ex)
			{

				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return false;

			}




// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public bool LinkState(string par_sLinkName, string par_sLinkVal, string par_sTestState)
		{


			int i = 0;

			string sProc = "clRowset::LinkState";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			DataRow dr = null;

			//Try
			if (IsLoaded(par_sLinkName))
			{


				int iTable = (int)cLoadedLinks[(par_sLinkName + "%%GID_ID").ToUpper()];

				if (iTable == 0) //Link is one to one or one side of one to many, therefore in base table
				{

					switch (par_sTestState)
					{

						case "EXISTS":

							if (oDataSet.Tables[iTable].Rows[(int)lCurrentRow][par_sLinkName + "%%GID_ID"].ToString() == par_sLinkVal)
							{
								return true;
							}
							else
							{
								return false;
							}
							break;

						case "DELETED":

							dr = oDataSet.Tables[iTable].Rows[(int)lCurrentRow];
							if (dr[par_sLinkName + "%%GID_ID", DataRowVersion.Original].ToString() == par_sLinkVal)
							{
								if (dr[par_sLinkName + "%%GID_ID", DataRowVersion.Original] != dr[par_sLinkName + "%%GID_ID", DataRowVersion.Current])
								{
									return true;
								}
								else
								{
									return false;
								}
							}
							else
							{
								return false;
							}
							break;

						case "ADDED":

							dr = oDataSet.Tables[iTable].Rows[(int)lCurrentRow];

							if (oDataSet.Tables[iTable].Rows[(int)lCurrentRow][par_sLinkName + "%%GID_ID"].ToString() == par_sLinkVal)
							{

								if (dr[par_sLinkName + "%%GID_ID", DataRowVersion.Original].ToString() != par_sLinkVal)
								{
									return true;
								}
								else
								{
									return false;
								}
							}
							else
							{
								return false;
							}
							break;

					}

				}
				else
				{

					DataTable table = oDataSet.Tables[iTable];
					string expression = null;

					if (par_sLinkVal != "")
					{
						expression = "[" + par_sLinkName + "%%GID_ID]='" + par_sLinkVal + "' AND [BASE%%GID_ID]='" + oDataSet.Tables[0].Rows[(int)lCurrentRow]["GID_ID"].ToString() + "'";
					}
					else
					{
						expression = "[BASE%%GID_ID]='" + oDataSet.Tables[0].Rows[(int)lCurrentRow]["GID_ID"].ToString() + "'";
					}

					DataRow[] foundRows;

					// Use the Select method to find all rows matching the filter.
					foundRows = table.Select(expression);


					switch (par_sTestState)
					{

						case "EXISTS":
							for (i = 0; i <= foundRows.GetUpperBound(0); i++)
							{
								return true;
							}
							return false;
						case "DELETED":

							for (i = 0; i <= foundRows.GetUpperBound(0); i++)
							{
								if (foundRows[i].RowState == DataRowState.Deleted)
								{
									return true;
								}
							}

							return false;

						case "ADDED":

							for (i = 0; i <= foundRows.GetUpperBound(0); i++)
							{
								if (foundRows[i].RowState == DataRowState.Added)
								{
									return true;
								}
							}

							return false;
					}

				}


			}
			else
			{
				//goErr.SetError(45160, sProc, , par_sLinkName)

			}

			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public string GetRowsetIDs(bool par_bIncludeSysNames = false, string par_sDelimiter = "^^^|*|*|^^^")
		{


			//PURPOSE:
			//       Returns a hard return delimited list of the IDs of all records in the rowset and optional sysnames  
			//PARAMETERS:
			//AUTHOR: RH

			string sProc = "clRowset::GetRowsetIDs";

			//Try

			int i = oDataSet.Tables[0].Rows.Count;
			string sReturn = "";
			string sReturnTag = "";

// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of oDataSet.Tables(0).Rows.Count for every iteration:
			int tempVar = oDataSet.Tables[0].Rows.Count;
			for (i = 0; i < tempVar; i++)
			{
				sReturn = sReturn + oDataSet.Tables[0].Rows[i]["GID_ID"].ToString() + "\r\n";

				if (par_bIncludeSysNames == true)
				{
					sReturnTag = sReturnTag + oDataSet.Tables[0].Rows[i]["SYS_NAME"].ToString() + "\r\n";
				}
			}
			if (par_bIncludeSysNames == true)
			{
				sReturn = sReturn.Substring(0, sReturn.Length - 2) + par_sDelimiter + sReturnTag.Substring(0, sReturnTag.Length - 2);
			}
			else
			{
				sReturn = sReturn.Substring(0, sReturn.Length - 2);
			}



			return sReturn;
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return "";

		}

		//V_T 10/6/2015 Batch update
		public bool BatchUpdate(string sFileName, string sWhereClause, Dictionary<string, object> dColCollection, string sBaseGidId)
		{

			try
			{
				if (string.IsNullOrEmpty(sFileName) == false && string.IsNullOrEmpty(sWhereClause) == false && dColCollection != null && dColCollection.Count > 0)
				{

					string sFields = "";
					string sUpdatestmt = "";
// INSTANT C# NOTE: Commented this declaration since looping variables in 'foreach' loops are declared in the 'foreach' header in C#:
//					Dim pair As KeyValuePair(Of String, Object)

					foreach (KeyValuePair<string, object> pair in dColCollection)
					{
						if (pair.Key.Substring(0, 4) == "LNK_")
						{
							UpdateLinkValues(pair.Key, pair.Value, sBaseGidId);
						}
						else
						{
							if (string.IsNullOrEmpty(sFields))
							{
								sFields = " [" + pair.Key + "] = '" + pair.Value.ToString() + "' ";
							}
							else
							{
								sFields = sFields + "," + " [" + pair.Key + "] = '" + pair.Value.ToString() + "' ";
							}

						}
					}

					sUpdatestmt = "UPDATE " + sFileName + " SET " + sFields + " WHERE " + sWhereClause;

					return goData.RunSQLQuery(sUpdatestmt);

				}

				return false;
			}
			catch (Exception ex)
			{
				return false;
			}

		}

		//V_T 10/6/2015 Batch update
		private bool UpdateLinkValues(string sLinkName, object LinkValue, string sBaseGIDID = "")
		{

			try
			{

				clArray _clArray = (clArray)LinkValue;
				LinkDataStructure oLinkData = (LinkDataStructure)cEditableLinks[sLinkName];

				if (sLinkName.IndexOf("%%") + 1 > 0)
				{
					sLinkName = goTR.ExtractString(sLinkName, 1, "%%");
				}

				string sID = "";
				string sTableName = "";
				string sFields = "";
				string sValues = "";
				string sSql = "";

				if (oLinkData.LinkType == "N1")
				{

					sTableName = goTR.ExtractString(oLinkData.LinkID, 1, ",");

					sFields = "GID_" + goTR.ExtractString(oLinkData.LinkID, 3, ",") + "_" + goTR.ExtractString(oLinkData.LinkID, 2, ",");

					string sLinkId = _clArray.GetItem(1);

					if (string.IsNullOrEmpty(sLinkId) == false)
					{
						sSql = "UPDATE [" + sTableName + "]" + "\r\n" + "SET [" + sFields + "] = '" + _clArray.GetItem(1) + "'" + "\r\n" + "WHERE [GID_ID] = '" + sBaseGIDID + "'";
					}

				}
				else if (oLinkData.LinkType == "NN")
				{

					sTableName = GetLinkFile(oLinkData.LinkID);

					sFields = "GID_ID,GID_" + oLinkData.FromFile + ",GID_" + oLinkData.ToFile;

					for (int i = 1; i < _clArray.GetDimension(); i++)
					{

						sID = goData.GenSUID(oLinkData.ToFile);

						sValues = "'" + sID + "'";
						sValues = sValues + "," + "'" + sBaseGIDID + "'";
						sValues = sValues + "," + "'" + _clArray.GetItem(i) + "'";

						sSql = sSql + Environment.NewLine + goData.GenSQLForAddingNNLinks(sTableName, sFields, sValues) + ";";

					}

				}

				if (string.IsNullOrEmpty(sSql) == false)
				{
					return goData.RunSQLQuery(sSql);
				}

				return false;

			}
			catch (Exception ex)
			{
				return false;
			}

		}


		//V_T 10/27/2015 Prepare dirty fields collection
		public void PrepareDirtyFieldsCollection()
		{
			string sProc = "clRowset::PrepareDirtyFieldsCollection";
			//Try
			if (gsDirtyFields == null)
			{
				gsDirtyFields = new System.Collections.Generic.Dictionary<string, Tuple<object, object>>();
			}

			DataTable dt = oDataSet.Tables[0];
			if (dt != null && dt.Rows.Count > 0)
			{
				for (var index = 0; index < dt.Columns.Count; index++)
				{
					string sField = dt.Columns[index].ColumnName.ToUpper();
					object sOriginalValue = dt.Rows[0][index];
					if (gsDirtyFields.ContainsKey(sField))
					{
						gsDirtyFields.Remove(sField);
					}
					gsDirtyFields.Add(sField, new Tuple<object, object>(sOriginalValue, sOriginalValue));
				}
			}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

#endregion
#region Private Methods
		private void Initialize()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goScr = (Selltis.BusinessLogic.clScrMngRowSet)HttpContext.Current.Session["goScr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goUt = new clUtil();

		}
		private DataTable GetData(string SelectStatement, int iTimeOut = 1800, string TableName = "")
		{
				DataTable tempGetData = null;

			//PURPOSE:
			//		Calls to SQL server to populate rowset dataset
			//PARAMETERS:
			//		SelectStatement: SQL select statement generated by GenerateSQL
			//RETURNS:
			//		Base DataTable
			//AUTHOR: RH

			//Debug.Print(SelectStatement)

			string sProc = "clRowset::GetData";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			try
			{
				string sString = "";
// INSTANT C# NOTE: Commented this declaration since looping variables in 'foreach' loops are declared in the 'foreach' header in C#:
//				Dim column As DataColumn
				string sLinkType = null;
				LinkDataStructure LinkData = new LinkDataStructure();
				string sSysName = "";
				int iTable = 0;

				oDataSet = null;
				oDataSet = new DataSet();

				bool bIsFromCache = clCache.IsCachable(SelectStatement, TableName, "");
				bool bIsCached = false;

				//If bIsFromCache Then
				//    oDataTable = clCache.GetItemFromCache(SelectStatement)
				//    If oDataTable IsNot Nothing Then
				//        Return oDataTable
				//    End If
				//End If

				if (bIsFromCache)
				{
					oDataSet = (DataSet)clCache.GetItemFromCache(SelectStatement);
					if (oDataSet != null)
					{
						bIsCached = true;
					}
				}

				Stopwatch tTimer = new Stopwatch();
				tTimer.Start();

				System.Data.SqlClient.SqlConnection connection = null;

				if (bIsCached == false)
				{

					oDataSet = new DataSet();
					connection = goData.GetConnection();

					using (connection)
					{
						//Debug.Print(SelectStatement)
						string queryString = SelectStatement;
						System.Data.SqlClient.SqlDataAdapter adapter = new System.Data.SqlClient.SqlDataAdapter(queryString, connection);
						adapter.SelectCommand.CommandTimeout = iTimeOut;
						if (adapter.Fill(oDataSet) == 1)
						{
							if (oDataSet.Tables[0].Columns[0].ColumnName == "ERROR")
							{
								goErr.SetError(45150, sProc,"", (oDataSet.Tables[0].Rows[0][0].ToString()));
							}
						}
					}

				}

				oDataTable = oDataSet.Tables[0];

				foreach (DataColumn column in oDataTable.Columns)
				{

					if (column.ColumnName.Substring(0, 3).ToUpper() == "LNK")
					{
						cLoadedLinks.Add(column.ColumnName.ToUpper());
					}
					else
					{
						cFields.Add(column.ColumnName.ToUpper(), column.ColumnName.ToUpper());
					}
				}

				//Load link table name and position into cEditableLinks collection
				int i = 0;
				int k = 0;
				string sLinkName = "";
				string sTest = "";

				//Dim tkey(1) As DataColumn

				if (oDataSet.Tables.Count > 1) //Link tables exist
				{

// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of oDataSet.Tables(1).Rows.Count for every iteration:
					int tempVar = oDataSet.Tables[1].Rows.Count;
					for (i = 0; i < tempVar; i++)
					{

						if (oDataSet.Tables[1].Rows[i]["LinkName"].ToString().EndsWith("GID_ID"))
						{
							sSysName = oDataSet.Tables[1].Rows[i]["LinkName"].ToString();
							sSysName = goTR.Replace(sSysName, "%%GID_ID", "%%SYS_NAME");

// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of oDataSet.Tables(i + 2).Columns.Count for every iteration:
							int tempVar2 = oDataSet.Tables[i + 2].Columns.Count;
							for (k = 0; k < tempVar2; k++)
							{
								if (oDataSet.Tables[i + 2].Columns[k].ColumnName.ToUpper() == sSysName.ToUpper())
								{
									if (!IsLoaded(sSysName))
									{
										cLoadedLinks.Add(i + 2, sSysName.ToUpper());
									}
								}
							}
						}

						sTest = sTest + "\r\n" + Convert.ToString(oDataSet.Tables[1].Rows[i]["LinkName"].ToString());

						//Try
						cLoadedLinks.Add(i + 2, oDataSet.Tables[1].Rows[i]["LinkName"].ToString().ToUpper());
						iTable = (int)cLoadedLinks[oDataSet.Tables[1].Rows[i]["LinkName"].ToString().ToUpper()];
						//Catch ex As Exception
						//    'sTest = sTest

						//End Try



						if (oDataSet.Tables[1].Rows[i]["LinkName"].ToString().IndexOf("%%") + 1 > 0)
						{
							sLinkName = oDataSet.Tables[1].Rows[i]["LinkName"].ToString().Substring(0, oDataSet.Tables[1].Rows[i]["LinkName"].ToString().IndexOf("%%"));
						}
						else
						{
							sLinkName = Convert.ToString(oDataSet.Tables[1].Rows[i]["LinkName"].ToString());
						}

						//tkey(1) = oDataSet.Tables.Item(i + 2).Columns("GID_ID")
						//tkey(0) = oDataSet.Tables.Item(i + 2).Columns("BASE%%GID_ID")

						// oDataSet.Tables.Item(i + 2).PrimaryKey = tkey



						if (IsTableEditable(oDataSet.Tables[1].Rows[i]["LinkName"].ToString()))
						{

							LinkData = new LinkDataStructure();

							sLinkType = goData.LK_GetType(sRSFile, sLinkName);
							LinkData.FQLinkName = goTR.ExtractString(sRSFile + "." + sLinkName, 1);
							LinkData.FromFile = goTR.ExtractString(sLinkType, 4);
							LinkData.Link = sLinkName;
							if (goTR.ExtractString(sLinkType, 1) == "N")
							{
								LinkData.LinkAllowsMultiple = true;
							}
							else
							{
								LinkData.LinkAllowsMultiple = false;
							}
							LinkData.LinkDirection = Convert.ToInt32(goTR.ExtractString(sLinkType, 2));
							LinkData.LinkID = goTR.ExtractString(sLinkType, 7);
							LinkData.LinkLabel = goTR.ExtractString(sLinkType, 6);
							LinkData.LinkType = goTR.ExtractString(sLinkType, 5);
							LinkData.ToFile = goTR.ExtractString(sLinkType, 3);
							cEditableLinks.Add(LinkData, sLinkName);

						}
					}
				}

				// Map Link column names

// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of oDataSet.Tables(0).Columns.Count for every iteration:
				int tempVar3 = oDataSet.Tables[0].Columns.Count;
				for (i = 0; i < tempVar3; i++)
				{
					//MI 1/21/10 Added 'And goTR.GetPrefix(oDataSet.Tables(0).Columns(i).ColumnName) = "LNK_"' to 
					//prevent picking up columns like 'INT_LNK_xxx_xxx%%xxx_xxxx_GROUPING' as link columns. 
					//These are added for each sort field in GROUPBY and GROUPBYWITHROLLUP modes.
					if (oDataSet.Tables[0].Columns[i].ColumnName.IndexOf("%%") + 1 > 0 && goTR.GetPrefix(oDataSet.Tables[0].Columns[i].ColumnName) == "LNK_")
					{

						cLoadedLinks.Add(0, oDataSet.Tables[0].Columns[i].ColumnName.ToUpper());

						//MI 1/21/10 This test is redundant, should this be added in an Else case?
						if (oDataSet.Tables[0].Columns[i].ColumnName.IndexOf("%%") + 1 > 0)
						{
							sLinkName = oDataSet.Tables[0].Columns[i].ColumnName.Substring(0, oDataSet.Tables[0].Columns[i].ColumnName.IndexOf("%%"));
						}
						else
						{
							sLinkName = oDataSet.Tables[0].Columns[i].ColumnName;
						}

						if (IsTableEditable(oDataSet.Tables[0].Columns[i].ColumnName))
						{

							LinkData = new LinkDataStructure();

							sLinkType = goData.LK_GetType(sRSFile, sLinkName);
							LinkData.FQLinkName = goTR.ExtractString(sRSFile + "." + sLinkName, 1);
							LinkData.FromFile = goTR.ExtractString(sLinkType, 4);
							LinkData.Link = sLinkName;
							if (goTR.ExtractString(sLinkType, 1) == "N")
							{
								LinkData.LinkAllowsMultiple = true;
							}
							else
							{
								LinkData.LinkAllowsMultiple = false;
							}
							LinkData.LinkDirection = Convert.ToInt32(goTR.ExtractString(sLinkType, 2));
							LinkData.LinkID = goTR.ExtractString(sLinkType, 7);
							LinkData.LinkLabel = goTR.ExtractString(sLinkType, 6);
							LinkData.LinkType = goTR.ExtractString(sLinkType, 5);
							LinkData.ToFile = goTR.ExtractString(sLinkType, 3);
							cEditableLinks.Add(LinkData, sLinkName);

						}
					}
				}

				DataColumn[] key = new DataColumn[1];
				key[0] = oDataTable.Columns["GID_ID"];

				oDataTable.PrimaryKey = key;

				//SetLinkTableKey()
				if (bIsCached == false)
				{
					connection.Close();
				}

				if (bIsFromCache)
				{
					clCache.AddtoCache(SelectStatement, oDataSet);
				}

				tTimer.Stop();
				lSQLLoadTime = tTimer.ElapsedMilliseconds;

				return oDataTable;

			}
			catch (Exception ex)
			{
				tempGetData = oDataTable;
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45100, sProc)
				//End If
			}


			return tempGetData;
		}
		private bool IsTableEditable(string sName)
		{

			//PURPOSE:
			//		Tests whether a link is editable
			//PARAMETERS:
			//		sName: linkname
			//RETURNS:
			//		true if editable
			//AUTHOR: RH

			string sProc = "clRowset::IsTableEditable";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sTempStr = null;

			if (sName.IndexOf("%%") + 1 == 0)
			{
				return true;
			}
			else
			{
				if (sName.Substring(sName.Length - 8).ToUpper() == "%%GID_ID")
				{
					sTempStr = sName.Substring(0, sName.Length - 8);
					if (sTempStr.IndexOf("%%") + 1 == 0)
					{
						return true;
					}
				}
			}

			return false;

		}
		public bool IsLoaded(string sName)
		{

			//PURPOSE:
			//		Tests whether a field or link is in rowset
			//PARAMETERS:
			//		sName: field, link, or linked field
			//RETURNS:
			//		true if in rowset
			//AUTHOR: RH

			string sProc = "clRowset::IsLoaded";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			try
			{

				sName = sName.ToUpper();
				if (sName.Substring(0, 4) == "DTE_")
				{
					sName = goTR.Replace(sName, "DTE_", "DTT_");
				}
				else if (sName.Substring(0, 3) == "TME_")
				{
					sName = goTR.Replace(sName, "TME_", "DTT_");
				}

				//wt 102814
				if (sName.Substring(0, 4) == "MMP_")
				{
					sName = goTR.Replace(sName, "MMP_", "MMR_");
				}

				//V_T 5/14/15
				if (sName.Substring(0, 4) == "ADV_")
				{
					sName = goTR.Replace(sName, "ADV_", "ADR_");
				}

				if (sName.Substring(0, 3) == "LNK")
				{

					int i = 0;
					if (sName.IndexOf("%%") + 1 == 0)
					{

						if (cLoadedLinks.Contains((sName + "%%GID_ID").ToUpper()))
						{
							i = (int)cLoadedLinks[(sName + "%%GID_ID").ToUpper()];
						}
						else
						{
							if (sName.Substring(0, 3) == "LNK")
							{
								if (sName.IndexOf("%%") + 1 == 0 || (sName.IndexOf("%%GID_ID") + 1) != 0)
								{
									return GetMissingLinkorLinks(sName);
								}
							}
							//we expect an exception when link not in collection
							return false;
						}

					}
					else
					{
						if (sName.IndexOf("%%DTE_") + 1 > 0)
						{
							sName = goTR.Replace(sName, "%%DTE_", "%%DTT_");
						}
						else if (sName.IndexOf("%%TME_") + 1 > 0)
						{
							sName = goTR.Replace(sName, "%%TME_", "%%DTT_");
						}

						if (cLoadedLinks.Contains(sName.ToUpper()))
						{
							i = (int)cLoadedLinks[sName.ToUpper()];
						}
						else
						{

							if (sName.Substring(0, 3) == "LNK")
							{
								if (sName.IndexOf("%%") + 1 == 0 || (sName.IndexOf("%%GID_ID") + 1) != 0)
								{
									return GetMissingLinkorLinks(sName);
								}
							}
							//we expect an exception when link not in collection
							return false;

						}



					}

					return true;

				}
				else if (sName.Substring(0, 3) == "GEN")
				{
					return true;
				}
				else
				{

					if (cFields.Contains(sName))
					{
						return true;
					}
					else
					{
						return false;
					}

				}

			}
			catch (Exception ex)
			{

				if (sName.Substring(0, 3) == "LNK")
				{
					if (sName.IndexOf("%%") + 1 == 0 || (sName.IndexOf("%%GID_ID") + 1) != 0)
					{
						return GetMissingLinkorLinks(sName);
					}
				}
				//we expect an exception when link not in collection
				return false;


			}




		}
		public bool IsLoadedNoLoad(string sName)
		{

			//PURPOSE:
			//		Tests whether a field or link is in rowset
			//PARAMETERS:
			//		sName: field, link, or linked field
			//RETURNS:
			//		true if in rowset
			//AUTHOR: RH

			string sProc = "clRowset::IsLoadedNoLoad";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			try
			{

				sName = sName.ToUpper();
				if (sName.Substring(0, 4) == "DTE_")
				{
					sName = goTR.Replace(sName, "DTE_", "DTT_");
				}
				else if (sName.Substring(0, 3) == "TME_")
				{
					sName = goTR.Replace(sName, "TME_", "DTT_");
				}

				if (sName.Substring(0, 3) == "LNK")
				{

					int i = 0;
					if (sName.IndexOf("%%") + 1 == 0)
					{
						if (cLoadedLinks.Contains((sName + "%%GID_ID").ToUpper()))
						{
							i = (int)cLoadedLinks[(sName + "%%GID_ID").ToUpper()];
						}
						else
						{
							return false;
						}

					}
					else
					{
						if (sName.IndexOf("%%DTE_") + 1 > 0)
						{
							sName = goTR.Replace(sName, "%%DTE_", "%%DTT_");
						}
						else if (sName.IndexOf("%%TME_") + 1 > 0)
						{
							sName = goTR.Replace(sName, "%%TME_", "%%DTT_");
						}

						if (cLoadedLinks.Contains(sName.ToUpper()))
						{
							i = (int)cLoadedLinks[sName.ToUpper()];
						}
						else
						{
							return false;
						}



					}

					return true;

				}
				else if (sName.Substring(0, 3) == "GEN")
				{
					return true;
				}
				else
				{

					if (cFields.Contains(sName))
					{
						return true;
					}
					else
					{
						return false;
					}

				}

			}
			catch (Exception ex)
			{

				//we expect an exception when link not in collection
				return false;

			}




		}


		private int SeedRecord()
		{

			//PURPOSE:
			//		Creates new record and establishes any required or default field values
			//PARAMETERS:
			//		None
			//RETURNS:
			//		1 on success
			//AUTHOR: RH

			string sProc = "clRowset::SeedRecord";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//Try

			DataRow workRow = oDataTable.NewRow();
			workRow["GID_ID"] = goData.GenSUID(sRSFile);

			workRow["SI__ShareState"] = 2;
			//***********************************************
			//Optional
			//Try
			workRow["dtt_creationtime"] = goTR.NowUTC(); //*** MI 9/27/07
			//Catch ex As Exception
			//End Try

			oDataTable.Rows.Add(workRow);
			if (IsLoaded("LNK_CreatedBy_US"))
			{
				SetFieldVal("LNK_CreatedBy_US", goP.GetUserTID());
			}
			//***********************************************
			LoadMetaDefinedDefaultVals();
			LoadCreateVals();

			return 1;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If
			//End Try


		}
		private void LoadDefaultVals(ref DataTable oTable)
		{
			//MI 11/5/09 Added DTY DTQ DTM DTD.
			//MI 9/29/08 Fix: DBNull for MLS fields is now the default MLS value, not just 0.
			//PURPOSE:
			//		Sets default field values 
			//PARAMETERS:
			//		None
			//RETURNS:
			//		None
			//AUTHOR: RH

			string sProc = "clRowset::LoadDefaultVals";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			int i = 0;
			object oValue = null;
			clList oList = new clList();

			//Try

			if (oTable.Rows[0].RowState == DataRowState.Deleted)
			{
				return;
			}

			for (i = 0; i < oTable.Columns.Count; i++)
			{

				oValue = oTable.Rows[0][i];
				if (oValue.GetType().ToString() == "System.DBNull")
				{

					switch (oTable.Columns[i].ColumnName.Substring(0, 3).ToUpper())
					{

						case "CHK":
						case "CMB":
						case "CUR":
						case "DR_":
						case "INT":
						case "LI_":
						case "BI_":
						case "LST":
						case "SEL":
						case "SI_":
						case "SR_": //MI 9/29/08 Removed "MLS"
							oTable.Rows[0][i] = 0;
							break;
						case "MLS": //MI 9/29/08 Added Case "MLS"
							oTable.Rows[0][i] = oList.GetDefaultIndex(sRSFile, goTR.RemovePrefix(oTable.Columns[i].ColumnName)); //MI 9/29/08 Added Case "MLS"
							break;
						case "DTE":
						case "TME":
						case "TML":
						case "DTT":
						case "DTY":
						case "DTQ":
						case "DTM":
						case "DTD":
							oTable.Rows[0][i] = clC.SELL_BLANK_DTDATETIME;
							break;
						case "GID":
						case "LNK":
						break;
							//Do nothing
						default:
							oTable.Rows[0][i] = "";
							break;
					}

				}

			}

			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If

			//End Try

		}
		private void LoadMetaDefinedDefaultVals()
		{

			//PURPOSE:
			//		Sets default field values as defined in FLD_ metadata.
			//PARAMETERS:
			//		None
			//RETURNS:
			//		None
			//AUTHOR: RH

			string sProc = "clRowset::LoadMetaDefinedDefaultVals";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//Try

			if (sDefaults == "")
			{
				sDefaults = goMeta.PageRead("GLOBAL", "FLD_" + sRSFile, "", true);
			}

			string[] sDefFields = null;
			int i = 0;
			object oValue = null;

			//Set default values
			sDefFields = goTR.StrRead(sDefaults, "FIELDSWDEFS", "", false).Split('|');

			for (i = sDefFields.GetLowerBound(0); i <= sDefFields.GetUpperBound(0); i++)
			{
				if (goTR.StrRead(sDefaults, sDefFields[i] + "_DEF", "") != "")
				{

					if (sDefFields[i].Substring(0, 3).ToUpper() == "MLS")
					{
						SetFieldVal(sDefFields[i], goTR.StrRead(sDefaults, sDefFields[i] + "_DEF", ""), clC.SELL_SYSTEM);
					}
					else
					{
						oValue = goTR.StrRead(sDefaults, sDefFields[i] + "_DEF", "");
						string tempVar = Convert.ToString(oValue);
						oValue = goTR.GetLineValue(ref tempVar);
							oValue = tempVar;
						SetFieldVal(sDefFields[i], oValue, clC.SELL_FRIENDLY);
					}
				}
			}

			//Set LinkMeLinks
			sDefFields = goTR.StrRead(sDefaults, "LINKMELINKS", "", false).Split('|');

			for (i = sDefFields.GetLowerBound(0); i <= sDefFields.GetUpperBound(0); i++)
			{
				if (sDefFields[i] != "")
				{
					SetFieldVal(sDefFields[i], goP.GetUserTID());
				}
			}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If
			//End Try

		}

		private void LoadCreateVals()
		{
			//MI 3/23/10 fixed bug: MLS values defined in CRL and CRU metadata after an MLS value that contained <%FieldCode%> were not set.
			//MI 9/16/08 Fixed parsing algorithm to catch '<%FieldCode%>', then '<%FieldCode '. Previously
			//           was looking for '<%FieldCode', which would catch <%MMO_Notes... when <%MMO_Note existed.
			//MI 9/15/08 Fixed removing field codes from sCreateDefaults that include parameter=value.
			//           until now, <%MMO_Notes%> was removed, but <%MMO_Notes ParseLine='First name: '%> wasn't.
			//MI 4/14/08 Changed MLS evaluation to friendly, added support for evaluating a system value when defined explicitly.
			//PURPOSE:
			//		Sets create field values as defined in CRL or CRU metadata.
			//HOW MLS_ FIELDS ARE EVALUATED:
			//       When an MLS value is defined in CRL MD explicitly:
			//           It is first evaluated as a 'friendly' value
			//          (by label). If the MLS field in the record being created doesn't have an element
			//           with a matching label, we attempt to set the MLS field based on the 'system' format,
			//           treating the defined value as an index number of the list element. If that fails, 
			//          the master default value is set, if defined, or the 0 element is set.
			//       When an MLS value is defined in CRL MD as a field or system code (<%...%>):
			//           It is only evaluated as a 'friendly' value (by label). If the MLS field in the record being
			//           created doesn't have an element with a matching label, the master default value is set, 
			//           if defined, else the 0 element is set.
			//PARAMETERS:
			//		None
			//RETURNS:
			//		None
			//AUTHOR: RH

			string sProc = "clRowset::LoadCreateVals";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);
			
			clRowSet rsBaseRec = null;
			string sTable = "";
			string sCreateDefaults = "";
			string[] sCreateFields = null;
			string[] sDumpArray = null;
			int i = 0;
			string sGetFields = "";
			string sDumpFields = "";
			string sLine = "";
			int iPos = 0;
			int iPos2 = 0;
			int iPos3 = 0;

			//Try

			sCreateDefaults = goMeta.PageRead("GLOBAL", sCreateType, "", true);
			if (sCreateDefaults == "")
			{
				return;
			}
			sCreateFields = goTR.StrRead(sCreateDefaults, "FIELDS", "", false).Split('|');

			if (sCreateType.Substring(0, 3).ToUpper() == "CRL")
			{
				if (sCreateFromID == "")
				{
					sCreateFromID = goData.LastSelected.SelectedRecordID;
				}
				if (sCreateFromID != "")
				{

					sTable = goTR.GetTableNameFromSUID(goTR.StringToGuid(sCreateFromID));

					for (i = sCreateFields.GetLowerBound(0); i <= sCreateFields.GetUpperBound(0); i++)
					{
						sGetFields = sGetFields + "," + goTR.GetFieldsFromLine(sTable, goTR.StrRead(sCreateDefaults, sCreateFields[i], ""));
					}

					for (i = sCreateFields.GetLowerBound(0); i <= sCreateFields.GetUpperBound(0); i++)
					{
						sDumpFields = sDumpFields + "," + goTR.GetFieldsFromLine(sTable, goTR.StrRead(sCreateDefaults, sCreateFields[i], ""), true);
					}
					sGetFields = sGetFields.ToUpper();

					rsBaseRec = new clRowSet(sTable, 3, "GID_ID='" + sCreateFromID + "'", "", sGetFields);
					if (rsBaseRec.Count() != 1)
					{
						goErr.SetError(46167, sProc, "", sTable, sCreateFromID);
						return;
					}
				}
				else
				{
					goErr.SetError(46167, sProc, "", sTable, sCreateFromID);
				}
			}

			sDumpArray = sDumpFields.Split(',');
			for (i = sDumpArray.GetLowerBound(0); i <= sDumpArray.GetUpperBound(0); i++)
			{
				if (sDumpArray[i] != "")
				{
					//MI 9/15/08 Changed removing only whole field codes like <%MMO_Notes%> with
					//code that removes parameter=value as well, i.e. <%MMO_Notes ParseLine='Web: '%>
					//OLD CODE:
					//sCreateDefaults = goTR.Replace(sCreateDefaults, "<%" & Trim(sDumpArray(i)) & "%>", "")
					//NEW CODE:
					iPos = 0;
					do
					{
						iPos = (int)goTR.Position(sCreateDefaults.ToUpper(), "<%" + sDumpArray[i].Trim(' ').ToUpper() + "%>", iPos);
						if (iPos < 1)
						{
							//<%FieldCode%> not found (without parameters)
							iPos = (int)goTR.Position(sCreateDefaults.ToUpper(), "<%" + sDumpArray[i].Trim(' ').ToUpper() + " ", iPos);
							if (iPos < 1)
							{
								//<%FieldCode ' not found (with parameters), the code is not in the string
								break;
							}
						}
						iPos2 = (int)goTR.Position(sCreateDefaults, "%>", iPos + 2);
						if (iPos2 < 1)
						{
							//No closing '%>' found - nothing to remove
							//There can be no more <%%> codes since there is no closing %> in the string.
							break;
						}
						//Is there a hard return before the closing '%>'?
						iPos3 = (int)goTR.Position(sCreateDefaults, "\r\n", iPos + 1);
						if (iPos3 < iPos2 + 2)
						{
							//CR found before the closing '%>' - continue searching from after the CR
							iPos = iPos3 + 2;
							goto GetNextField;
						}
						sCreateDefaults = goTR.FromTo(sCreateDefaults, 1, iPos - 1) + goTR.FromTo(sCreateDefaults, iPos2 + 2, -1);
	GetNextField: ;
					} while (true);
				}
			}

			sCreateFields = goTR.StrRead(sCreateDefaults, "FIELDS", "", false).Split('|');
			string sCF = "";
            object temp_rsBaseRec = null;
            // INSTANT C# NOTE: There is no C# equivalent to VB's implicit 'once only' variable initialization within loops, so the following variable declaration has been placed prior to the loop:
            object iValue = null;
// INSTANT C# NOTE: There is no C# equivalent to VB's implicit 'once only' variable initialization within loops, so the following variable declaration has been placed prior to the loop:
			string sValue = null; //*** MI 4/14/08
// INSTANT C# NOTE: There is no C# equivalent to VB's implicit 'once only' variable initialization within loops, so the following variable declaration has been placed prior to the loop:
			string sDef = null; //*** MI 4/14/08 Definition of the default value
// INSTANT C# NOTE: There is no C# equivalent to VB's implicit 'once only' variable initialization within loops, so the following variable declaration has been placed prior to the loop:
			int iCodeStart = 0; //*** MI 4/14/08
// INSTANT C# NOTE: There is no C# equivalent to VB's implicit 'once only' variable initialization within loops, so the following variable declaration has been placed prior to the loop:
			int iCodeEnd = 0; //*** MI 4/14/08
// INSTANT C# NOTE: There is no C# equivalent to VB's implicit 'once only' variable initialization within loops, so the following variable declaration has been placed prior to the loop:
			bool bForceLabelMatching = false; //*** MI 4/14/08
			for (i = sCreateFields.GetLowerBound(0); i <= sCreateFields.GetUpperBound(0); i++)
			{
				if (goTR.StrRead(sCreateDefaults, sCreateFields[i], "") != "")
				{

					

                    sCF = sCreateFields[i];
					switch (sCreateFields[i].Substring(0, 3).ToUpper())
					{

						case "MLS":
							//mls set as system
	//						Dim iValue As Object
	//						Dim sValue As String //*** MI 4/14/08
	//						Dim sDef As String //*** MI 4/14/08 Definition of the default value
	//						Dim iCodeStart As Integer //*** MI 4/14/08
	//						Dim iCodeEnd As Integer //*** MI 4/14/08
	//						Dim bForceLabelMatching As Boolean //*** MI 4/14/08

							if (sCreateType.Substring(0, 3).ToUpper() == "CRL")
							{
								//with rowset

								//*** MI 4/14/08 commented out this original code
								//iValue = goTR.GetLineValue(goTR.StrRead(sCreateDefaults, sCreateFields(i), ""), _
								//, , , rsBaseRec, , clC.SELL_SYSTEM, True)
								//*** MI 4/14/08 added
								sDef = goTR.StrRead(sCreateDefaults, sCreateFields[i], "");
								string tempVar = "";
								object temp_rsBaseRec1 = rsBaseRec;
								sValue = goTR.GetLineValue(ref sDef, ref tempVar, "", false, ref temp_rsBaseRec1, "", clC.SELL_FRIENDLY, true);
									rsBaseRec = (Selltis.BusinessLogic.clRowSet)(object)temp_rsBaseRec1;

								//*** MI 4/14/08 added this block
								//First test whether there are any field or system codes in the definition
								iCodeStart = sDef.IndexOf("<%") + 1;
								if (iCodeStart > 0)
								{
									iCodeEnd = sDef.IndexOf("%>", iCodeStart - 1) + 1;
								}
								else //MI 3/23/10 added resetting to 0
								{
									iCodeEnd = 0; //MI 3/23/10 added resetting to 0
								}
								if (iCodeEnd > 0)
								{
									//At least one field code or system code (<%...%>) is defined.
									//In this case we only try to process by friendly value (label of the list element).
									//If that fails, we fail over gracefully.
									bForceLabelMatching = true;
								}
								else
								{
									//No field or system code is defined - the value is a straight label or index number.
									//In this case, if the friendly evaluation yields no results, we assume the value
									//is system and set it regardless whether the source/new record labels match.
									bForceLabelMatching = false;
								}
								//Test whether an element with the same label exists in the record we are creating
								if (goData.goList.GetList(sRSFile, sCreateFields[i], "LABEL").SeekInfo(sValue, true, false) == 0)
								{
									//List element with a matching label not found
									if (bForceLabelMatching)
									{
										//Since the definition contains a field/system code, we don't set the value
									}
									else
									{
										//Since the value is defined explicitly (not through field/system code),
										//we try to set the value by an index number
										string tempVar2 = "";
										object temp_rsBaseRec2= rsBaseRec;
										iValue = goTR.GetLineValue(ref sDef, ref tempVar2, "", false, ref temp_rsBaseRec2, "", clC.SELL_SYSTEM, true);
											rsBaseRec = (Selltis.BusinessLogic.clRowSet)temp_rsBaseRec2;
										if (Convert.ToString(iValue) == "")
										{
											iValue = 0;
										}
										iValue = Convert.ToInt16(iValue);
										SetFieldVal(sCreateFields[i], iValue, clC.SELL_SYSTEM);
									}
								}
								else
								{
									//List element with a matching label found - set it
									SetFieldVal(sCreateFields[i], sValue, clC.SELL_FRIENDLY);
								}


								//*** MI 4/14/08 commented out this original code
								//If iValue = "" Then
								//    iValue = 0
								//Else
								//    iValue = Convert.ToInt16(iValue)
								//End If
								//SetFieldVal(sCreateFields(i), iValue, clC.SELL_SYSTEM)

							}
							else
							{
								//without rowset (CRU_ case)      '*** MI 4/14/08 edited

								//*** MI 4/14/08 commented out this original code
								//iValue = goTR.GetLineValue(goTR.StrRead(sCreateDefaults, sCreateFields(i), ""), _
								//, , , , , clC.SELL_SYSTEM, True)
								sDef = goTR.StrRead(sCreateDefaults, sCreateFields[i], ""); //*** MI 4/14/08 added
								string tempVar3 = "";
								object tempVar4 = null;
								sValue = goTR.GetLineValue(ref sDef, ref tempVar3, "", false, ref tempVar4, "", clC.SELL_FRIENDLY, true); //*** MI 4/14/08 added

								//*** MI 4/14/08 added this block
								//First test whether there are any field or system codes in the definition
								iCodeStart = sDef.IndexOf("<%") + 1;
								if (iCodeStart > 0)
								{
									iCodeEnd = sDef.IndexOf("%>", iCodeStart - 1) + 1;
								}
								else //MI 3/23/10 added resetting to 0
								{
									iCodeEnd = 0; //MI 3/23/10 added resetting to 0
								}
								if (iCodeEnd > 0)
								{
									//At least one field code or system code (<%...%>) is defined.
									//In this case we only try to process by friendly value (label of the list element).
									//If that fails, we fail over gracefully.
									bForceLabelMatching = true;
								}
								else
								{
									//No field or system code is defined - the value is a straight label or index number.
									//In this case, if the friendly evaluation yields no results, we assume the value
									//is system and set it regardless whether the source/new record labels match.
									bForceLabelMatching = false;
								}
								//Test whether an element with the same label exists in the record we are creating
								if (goData.goList.GetList(sRSFile, sCreateFields[i], "LABEL").SeekInfo(sValue, true, false) == 0)
								{
									//List element with a matching label not found
									if (bForceLabelMatching)
									{
										//Since the definition contains a field/system code, we don't set the value
									}
									else
									{
										//Since the value is defined explicitly (not through field/system code),
										//we try to set the value by an index number
										string tempVar5 = "";
										object tempVar6 = null;
										iValue = goTR.GetLineValue(ref sDef, ref tempVar5, "", false, ref tempVar6, "", clC.SELL_SYSTEM, true);
										if (Convert.ToString(iValue) == "")
										{
											iValue = 0;
										}
										iValue = Convert.ToInt16(iValue);
										SetFieldVal(sCreateFields[i], iValue, clC.SELL_SYSTEM);
									}
								}
								else
								{
									//List element with a matching label found - set it
									SetFieldVal(sCreateFields[i], sValue, clC.SELL_FRIENDLY);
								}

								//*** MI 4/14/08 commented out this original code
								//If iValue = "" Then
								//    iValue = 0
								//Else
								//    iValue = Convert.ToInt16(iValue)
								//End If
								//SetFieldVal(sCreateFields(i), iValue, clC.SELL_SYSTEM)
							}
							break;
						case "LNK":
							sLine = goTR.StrRead(sCreateDefaults, sCreateFields[i]);
							sLine = goTR.Replace(sLine, "><", ">" + "\r\n" + "<");
							sLine = goTR.Replace(sLine, "> <", ">" + "\r\n" + "<");
							sLine = sLine.ToUpper();
							//non mls set as friendly
							if (sCreateType.Substring(0, 3).ToUpper() == "CRL")
							{
								//with rowset
								string tempVar7 = "";
								object temp_rsBaseRec3 = rsBaseRec;
								SetFieldVal(sCreateFields[i], goTR.GetLineValue(ref sLine, ref tempVar7, "", false, ref temp_rsBaseRec3), clC.SELL_FRIENDLY);
									rsBaseRec = (Selltis.BusinessLogic.clRowSet)temp_rsBaseRec3;
							}
							else
							{
								//without rowset
								string tempVar8 = "";
								SetFieldVal(sCreateFields[i], goTR.GetLineValue(ref sLine, ref tempVar8), clC.SELL_FRIENDLY);
							}
							break;

						case "NDB":

							sLine = goTR.StrRead(sCreateDefaults, sCreateFields[i]);

							if (sCreateFields[i].StartsWith("NDB_LNK"))
							{
								sLine = goTR.Replace(sLine, "><", ">" + "\r\n" + "<");
								sLine = goTR.Replace(sLine, "> <", ">" + "\r\n" + "<");
								sLine = sLine.ToUpper();
							}


							//set values to SetVar().. to be picked up and set on for
							string sNDBField = "CRL|CRU_" + sCreateFields[i].ToUpper();
							if (sCreateType.Substring(0, 3).ToUpper() == "CRL")
							{
								//with rowset

								string tempVar9 = "";
								object temp_rsBaseRec4 = rsBaseRec;
								this.oVar.SetVar(sNDBField, goTR.GetLineValue(ref sLine, ref tempVar9, "", false, ref temp_rsBaseRec4));
									rsBaseRec = (Selltis.BusinessLogic.clRowSet)temp_rsBaseRec4;
							}
							else
							{
								//without rowset
								string tempVar10 = "";
								this.oVar.SetVar(sNDBField, goTR.GetLineValue(ref sLine, ref tempVar10));
							}
							break;

								//V_T , below code is to copy the adr_attachments field value and files to lined entity
						case "ADR":

							//non mls set as friendly
							if (sCreateType.Substring(0, 3).ToUpper() == "CRL")
							{
								//with rowset
								string tempVar11 = goTR.StrRead(sCreateDefaults, sCreateFields[i], "");
								string tempVar12 = "";
								object temp_rsBaseRec5 = rsBaseRec;
								SetFieldVal(sCreateFields[i], goTR.GetLineValue(ref tempVar11, ref tempVar12, "", false, ref temp_rsBaseRec5), clC.SELL_FRIENDLY);
									rsBaseRec = (Selltis.BusinessLogic.clRowSet)temp_rsBaseRec5;
							}
							else
							{
								//without rowset
								string tempVar13 = goTR.StrRead(sCreateDefaults, sCreateFields[i], "");
								SetFieldVal(sCreateFields[i], goTR.GetLineValue(ref tempVar13), clC.SELL_FRIENDLY);
							}

							clAttachments objclAttachments = new clAttachments();
							string tempVar14 = goTR.StrRead(sCreateDefaults, sCreateFields[i], "");
							string tempVar15 = "";
							object temp_rsBaseRec6 = rsBaseRec;
							objclAttachments.CopyTempAttachments(sCreateFields[i], sCreateFromID, goTR.GetLineValue(ref tempVar14, ref tempVar15, "", false, ref temp_rsBaseRec6));
								rsBaseRec = (Selltis.BusinessLogic.clRowSet)temp_rsBaseRec6;
								break;

						default:
							//non mls set as friendly
							if (sCreateType.Substring(0, 3).ToUpper() == "CRL")
							{
								//with rowset
								string tempVar16 = goTR.StrRead(sCreateDefaults, sCreateFields[i], "");
								string tempVar17 = "";
								object temp_rsBaseRec7 = rsBaseRec;
								SetFieldVal(sCreateFields[i], goTR.GetLineValue(ref tempVar16, ref tempVar17, "", false, ref temp_rsBaseRec7), clC.SELL_FRIENDLY);
									rsBaseRec = (Selltis.BusinessLogic.clRowSet)temp_rsBaseRec7;
							}
							else
							{
								//without rowset
								string tempVar18 = goTR.StrRead(sCreateDefaults, sCreateFields[i], "");
								SetFieldVal(sCreateFields[i], goTR.GetLineValue(ref tempVar18), clC.SELL_FRIENDLY);
							}
							break;
					}
				}
			}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If

			//End Try



		}

		private bool DeleteLink(int iTable, string sLinkName, string sID)
		{

			//PURPOSE:
			//		Marks a link record to be deleted on Commit
			//PARAMETERS:
			//		iTable: Table number in DataSet
			//       sLinkName: Name of link
			//       sID: ID of linked record to delete
			//RETURNS:
			//		True on success
			//AUTHOR: RH

			string sProc = "clRowset::DeleteLink";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//Try
			if (iTable == 0) //Link is one to one or one side of one to many, therefore in base table
			{
				if (sID == "")
				{
					oDataSet.Tables[iTable].Rows[(int)lCurrentRow][sLinkName + "%%GID_ID"] = System.DBNull.Value;
					oDataSet.Tables[iTable].Rows[(int)lCurrentRow][sLinkName + "%%SYS_Name"] = "";
				}
				else
				{
					if (oDataSet.Tables[iTable].Rows[(int)lCurrentRow][sLinkName + "%%GID_ID"].ToString() == sID)
					{
						oDataSet.Tables[iTable].Rows[(int)lCurrentRow][sLinkName + "%%GID_ID"] = System.DBNull.Value;
						oDataSet.Tables[iTable].Rows[(int)lCurrentRow][sLinkName + "%%SYS_Name"] = "";
					}
				}

			}
			else
			{

				DataTable table = oDataSet.Tables[iTable];
				string expression = null;

				if (sID != "")
				{
					expression = "[" + sLinkName + "%%GID_ID]='" + sID + "' AND [BASE%%GID_ID]='" + oDataSet.Tables[0].Rows[(int)lCurrentRow]["GID_ID"].ToString() + "'";
				}
				else
				{
					expression = "[BASE%%GID_ID]='" + oDataSet.Tables[0].Rows[(int)lCurrentRow]["GID_ID"].ToString() + "'";
				}

				DataRow[] foundRows;

				// Use the Select method to find all rows matching the filter.
				foundRows = table.Select(expression);

				int i = 0;
				// Delete each found row.
				for (i = 0; i <= foundRows.GetUpperBound(0); i++)
				{
					foundRows[i].Delete();
				}
			}

			return true;

			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If

			//End Try

		}
		private bool AddLink(int iTable, string sLinkName, string sID)
		{

			//PURPOSE:
			//		Adds a link record to be added on Commit
			//PARAMETERS:
			//		iTable: Table number in DataSet
			//       sLinkName: Name of link
			//       sID: ID of linked record to add
			//RETURNS:
			//		True on success
			//AUTHOR: RH




			string sProc = "clRowset::AddLink";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);


			if (sID.Length < 5)
			{
				return false;
			}

			string expression = "";
			DataTable table = oDataSet.Tables[iTable];
			//Try


			if (sLinkName.IndexOf("%%") + 1 > 0)
			{
				sLinkName = goTR.ExtractString(sLinkName, 1, "%%");
			}
			LinkDataStructure oLinkData = (LinkDataStructure)cEditableLinks[sLinkName];
			DataSet ds = this.oDataSet;
			if (iTable == 0)
			{

				oDataSet.Tables[iTable].Rows[(int)lCurrentRow][sLinkName + "%%GID_ID"] = sID;
				oDataSet.Tables[iTable].Rows[(int)lCurrentRow][sLinkName + "%%SYS_Name"] = "";
			}
			else
			{
				expression = "[" + sLinkName + "%%GID_ID]='" + sID + "' AND [BASE%%GID_ID]='" + oDataSet.Tables[0].Rows[(int)lCurrentRow]["GID_ID"].ToString() + "'";

				DataRow[] foundRows;

				// Use the Select method to find all rows matching the filter.
				foundRows = table.Select(expression);
				if (foundRows.GetUpperBound(0) == 0) //Record already exists in datatable
				{
					return true;
				}
				else
				{
					DataRow row;
					row = oDataSet.Tables[iTable].NewRow();
					row["BASE%%GID_ID"] = oDataSet.Tables[0].Rows[(int)lCurrentRow]["GID_ID"];
					row[sLinkName + "%%GID_ID"] = sID;
					string sTableName = goTR.ExtractString(oLinkData.LinkID, 1, ",") + "_" + goTR.ExtractString(oLinkData.LinkID, 2, ",") + "_" + goTR.ExtractString(oLinkData.LinkID, 3, ",");
					row["GID_ID"] = goData.GenSUID(sTableName);
					oDataSet.Tables[iTable].Rows.Add(row);

				}

			}

			return true;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If
			//End Try

		}
		private bool CommitLinks(string par_sBaseGID, bool par_bClearTable = false)
		{

			//PURPOSE:
			//		Commits link changes for current record to database
			//PARAMETERS:
			//		None
			//RETURNS:
			//		True on success
			//AUTHOR: RH

			string sProc = "clRowset::CommitLinks";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			DataSet ds = this.oDataSet;



			//Try

			int i = 0;
			int r = 0;


			if (oDataSet.Tables.Count > 1)
			{
				for (i = 0; i <= oDataSet.Tables.Count - 3; i++)
				{
					if (IsTableEditable(oDataSet.Tables[1].Rows[i]["LinkName"].ToString()))
					{

						oDataSet.Tables[i + 2].DefaultView.RowFilter = "[BASE%%GID_ID]='" + par_sBaseGID + "'";
						oDataSet.Tables[i + 2].DefaultView.RowStateFilter = DataViewRowState.ModifiedCurrent | DataViewRowState.Added | DataViewRowState.Deleted;

						//Dim oTempTable As DataTable = oDataSet.Tables(i + 2).DefaultView.ToTable

						DataTable oTempTable = oDataSet.Tables[i + 2].Clone();

// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of oDataSet.Tables(i + 2).DefaultView.Count for every iteration:
						int tempVar = oDataSet.Tables[i + 2].DefaultView.Count;
						for (r = 0; r < tempVar; r++)
						{
							//oTempTable.ImportRow(oDataSet.Tables(i + 2).Rows(r))
							oTempTable.ImportRow(oDataSet.Tables[i + 2].DefaultView[r].Row);
						}

						if (oTempTable.Rows.Count > 0)
						{
							//Dim ds As DataSet = oDataSet
							CommitLinksToDatabase(oTempTable, i);
							oTempTable.AcceptChanges();

							for (r = (oDataSet.Tables[i + 2].DefaultView.Count - 1); r >= 0; r--)
							{
								oDataSet.Tables[i + 2].Rows[r].AcceptChanges();
							}

							//oDataSet.Tables(i + 2).Merge(oTempTable)
							if (par_bClearTable == true)
							{
								oDataSet.Tables[i + 2].Clear();
							}


						}
					}
				}
			}


			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If

			//End Try




// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}
		private bool CommitLinksToDatabase(DataTable oTempTable, int i)
		{

			//Get LinkData object for link table i
			LinkDataStructure oLinkData = (LinkDataStructure)cEditableLinks[goTR.Replace(oDataSet.Tables[1].Rows[i]["LinkName"].ToString(), "%%GID_ID", "")];
			//Variable to enumerate items in linktable
			long j = 0;

			string sID = "";
			string sBaseID = "";
			object oVal = null;
			bool bDel = false;
			bool bBackSideSameFile = false;

			DataTable tt = oTempTable;


			string sProc = "clRowset::CommitLinksToDatabase";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//Try

			//*******************************************************
			//LinkID stored in record on other side
			if (oLinkData.LinkType == "N1" && oLinkData.LinkDirection == 2)
			{

				for (j = 0; j < oTempTable.Rows.Count; j++)
				{

					if (oTempTable.Rows[(int)j].RowState == DataRowState.Deleted)
					{
						bDel = true;
						oTempTable.Rows[(int)j].RejectChanges();
					}

					oVal = oTempTable.Rows[(int)j][oDataSet.Tables[1].Rows[i]["LinkName"].ToString()];
					sID = oVal.ToString();
					sBaseID = oTempTable.Rows[(int)j]["BASE%%GID_ID"].ToString();

					if (bDel == true)
					{
						bDel = false;
						oTempTable.Rows[(int)j].Delete();
					}

					if (oTempTable.Rows[(int)j].RowState == DataRowState.Added)
					{

						goData.UpdateN1LinkRemote(sID, goTR.ExtractString(oLinkData.LinkID, 1, ","), "GID_" + goTR.ExtractString(oLinkData.LinkID, 3, ",") + "_" + goTR.ExtractString(oLinkData.LinkID, 2, ","), oTempTable.Rows[(int)j]["BASE%%GID_ID"].ToString());

					}

					if (oTempTable.Rows[(int)j].RowState == DataRowState.Deleted)
					{

						goData.UpdateN1LinkRemote(sID, goTR.ExtractString(oLinkData.LinkID, 1, ","), "GID_" + goTR.ExtractString(oLinkData.LinkID, 3, ",") + "_" + goTR.ExtractString(oLinkData.LinkID, 2, ","), "");

					}

				}


				oTempTable.AcceptChanges();


			}
			else if (oLinkData.LinkType == "NN") //LinkIDs stored in link table
			{
				//*******************************************************
				//Derive name of Link Table from oLinkData.LinkID
				string sTableName = goTR.ExtractString(oLinkData.LinkID, 1, ",") + "_" + goTR.ExtractString(oLinkData.LinkID, 2, ",") + "_" + goTR.ExtractString(oLinkData.LinkID, 3, ",");

				//*******************************************************
				//Rename columns to match db names
				string sSuffix = "";
				if (oLinkData.FromFile == oLinkData.ToFile)
				{
					sSuffix = "2";

					//test bBackSideSameFile
					if (oLinkData.LinkDirection == 2)
					{
						bBackSideSameFile = true;
					}
				}


				DataSet rs = oDataSet;

				oTempTable.Columns["BASE%%GID_ID"].ColumnName = "GID_" + oLinkData.FromFile;
				oTempTable.Columns[oDataSet.Tables[1].Rows[i]["LinkName"].ToString()].ColumnName = "GID_" + oLinkData.ToFile + sSuffix;
				//*******************************************************
				//Delete sys_name column
				string sSysName;
				sSysName = goTR.Replace(oDataSet.Tables[1].Rows[i]["LinkName"].ToString(), "GID_ID", "SYS_NAME");
				oTempTable.Columns.Remove(sSysName);
				//*******************************************************
				System.Data.SqlClient.SqlConnection connection = goData.GetConnection();
				System.Data.SqlClient.SqlDataAdapter adapter = new System.Data.SqlClient.SqlDataAdapter("Select Top 0 * From " + GetLinkFile(oLinkData.LinkID), connection);
				System.Data.SqlClient.SqlCommandBuilder builder = new System.Data.SqlClient.SqlCommandBuilder(adapter);

				builder.QuotePrefix = "[";
				builder.QuoteSuffix = "]";

				adapter.Fill(oTempTable);


				//adapter.Update(oTempTable)
				//******************************************************************************

				//Try
				//    adapter.Update(oTempTable)

				//Catch ex As DBConcurrencyException

				int k = 0;
				string sFields = null;
				string sValues = null;

				if (bBackSideSameFile == true)
				{
					sFields = oTempTable.Columns[2].ColumnName + "," + oTempTable.Columns[1].ColumnName + "," + oTempTable.Columns[0].ColumnName;
					bBackSideSameFile = false;
				}
				else
				{
					sFields = oTempTable.Columns[0].ColumnName + "," + oTempTable.Columns[1].ColumnName + "," + oTempTable.Columns[2].ColumnName;
				}

				for (k = 0; k < oTempTable.Rows.Count; k++)
				{

					if (oTempTable.Rows[k].RowState == DataRowState.Deleted)
					{

						goData.DeleteRecord(GetLinkFile(oLinkData.LinkID), oTempTable.Rows[k]["GID_ID", DataRowVersion.Original].ToString());

					}
					else if (oTempTable.Rows[k].RowState == DataRowState.Added)
					{

						sValues = "'" + oTempTable.Rows[k][0].ToString() + "','" + oTempTable.Rows[k][1].ToString() + "','" + oTempTable.Rows[k][2].ToString() + "'";

						goData.AddNNLink(GetLinkFile(oLinkData.LinkID), sFields, sValues, ref connection);

					}



				}
				//Catch ex As Exception
				//End Try


				//******************************************************************************



				connection.Close();

				oTempTable.Columns["GID_" + oLinkData.FromFile].ColumnName = "BASE%%GID_ID";
				oTempTable.Columns["GID_" + oLinkData.ToFile + sSuffix].ColumnName = oDataSet.Tables[1].Rows[i]["LinkName"].ToString();

			}

			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If

			//End Try

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		private string GetLinkFile(string sLinkID)
		{

			return goTR.ExtractString(sLinkID, 1, ",") + "_" + goTR.ExtractString(sLinkID, 3, ",") + "_" + goTR.ExtractString(sLinkID, 2, ",");

		}
		private object GetFriendly(string sFile, string sFieldName, object oValue)
		{
				object tempGetFriendly = null;
			//MI 11/5/09 Enabled date grouping fields DTY DTQ DTM DTD.
			//MI 9/26/08 Modified DBNull Case MLS.
			//MI 3/20/08 Fixed formatting for numerics and currency in case System.DBNull.
			//MI 10/5/07 Added support for formatting to currency.
			//MI 9/27/07. Modified comment.
			//PURPOSE:
			//		Returns friendly value for provided system value. Datetimes are NOT
			//       converted from/to UTC. Be sure the value is properly converted in
			//       GetFieldVal before it is passed to this method.
			//PARAMETERS:
			//		sFile: File
			//       sFieldName: Field
			//       oValue: System value
			//RETURNS:
			//		Friendly value
			//AUTHOR: RH

			string sProc = "clRowset::GetFriendly";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			tempGetFriendly = "";

			string sTempFieldName = sFieldName;
			string sP = goTR.GetPrefix(sFieldName);
			string sPrefix = sFieldName.Substring(0, 3).ToUpper();
			clList oList = new clList(); //MI 9/26/08 added

			//Try
			if (sP == "DTE_" || sP == "TME_" || sP == "TML_")
			{
				sTempFieldName = goTR.Replace(sFieldName, goTR.GetPrefix(sFieldName), "DTT_");
			}

			//*************************************************************
			//Replace Null value with default value for Selltis field type
			//*************************************************************
			//MI 3/20/08 Replaced one call to goTr.FieldValToString with
			//separate Select Cases for each num and curr type. This is to deal with
			//formatting, which FieldValToString currently doesn't support.
			if (oValue.GetType().ToString() == "System.DBNull")
			{
				switch (sPrefix)
				{
					case "CHK":
					case "CMB":
					case "LST":
					case "SEL": //*** MI 3/20/08 removed cases "CUR", "DR_", "SR_", "INT", "LI_", "BI_", "SI_"
						tempGetFriendly = goTR.FieldValToString("0", sPrefix);
						tempGetFriendly = Convert.ToString(tempGetFriendly);
						break;
					case "CUR":
						tempGetFriendly = goTR.CurrToString(0M, goData.GetFieldFormat(sFile, sFieldName)); //*** MI 3/20/08
						tempGetFriendly = Convert.ToString(tempGetFriendly);
						break;
					case "DR_":
					case "SR_":
					case "INT":
					case "LI_":
					case "BI_":
					case "SI_": //*** MI 3/20/08
						tempGetFriendly = goTR.NumToString(0, goData.GetFieldFormat(sFile, sFieldName)); //*** MI 3/20/08
						tempGetFriendly = Convert.ToString(tempGetFriendly);
						break;
					case "MLS":
						if (this.iRSType == clC.SELL_GROUPBY || this.iRSType == clC.SELL_GROUPBYWITHROLLUP) //MI 12/23/09 added
						{
							tempGetFriendly = "0";
						}
						else
						{
							tempGetFriendly = goTR.MLSToString(sFile, sFieldName, oList.GetDefaultIndex(sFile, goTR.RemovePrefix(sFieldName))); //MI 9/26/08 changed 0 to defaultIndex
						}
						break;
					default:
						tempGetFriendly = "";
						tempGetFriendly = Convert.ToString(tempGetFriendly);
						break;
				}
				//*************************************************************
			}
			else
			{

				switch (sPrefix)
				{
					case "DTE":
						sFieldName = sTempFieldName;
						tempGetFriendly = goTR.DateToString(Convert.ToDateTime(oValue));
						break;
					case "TME":
					case "TML":
						sFieldName = sTempFieldName;
						tempGetFriendly = goTR.TimeToString(Convert.ToDateTime(oValue));
						break;
					case "DTY":
						tempGetFriendly = goTR.DateToYearString(Convert.ToDateTime(oValue));
						break;
					case "DTQ":
						tempGetFriendly = goTR.DateToQuarterString(Convert.ToDateTime(oValue));
						break;
					case "DTM":
						tempGetFriendly = goTR.DateToMonthString(Convert.ToDateTime(oValue));
						break;
					case "DTD":
						tempGetFriendly = goTR.DateToString(Convert.ToDateTime(oValue));
						break;
					case "DTT":
						tempGetFriendly = goTR.DateTimeToString(Convert.ToDateTime(oValue));
						break;
					case "CUR":
						tempGetFriendly = goTR.CurrToString(Convert.ToDecimal(oValue), goData.GetFieldFormat(sFile, sFieldName)); //*** MI 10/5/07
						break;
					case "INT":
					case "LI_":
					case "BI_":
					case "SI_":
						tempGetFriendly = goTR.NumToString(Convert.ToInt64(oValue), goData.GetFieldFormat(sFile, sFieldName)); //*** MI 3/20/08 added GetFieldFormat
						break;
					case "SR_":
					case "DR_":
						tempGetFriendly = goTR.NumToString(Convert.ToInt64(oValue), goData.GetFieldFormat(sFile, sFieldName));
						break;
					case "MLS":
						tempGetFriendly = goTR.MLSToString(sFile, sFieldName, Convert.ToInt32(oValue));
						break;
					case "CHK":
						tempGetFriendly = goTR.CheckboxToString(Convert.ToInt32(oValue));
						break;
					case "SYS":
					case "TXT":
					case "TEL":
					case "FIL":
					case "EML":
					case "MMO":
					case "GID":
						tempGetFriendly = oValue.ToString();
						break;
					case "MMR":
						//do we have mime or plain text
						//if plain text, let's not convert, and give to user the value stored in the db
						//if mime, it will start with "MIME-Version:"
						//If InStr(Convert.ToString(oValue), "MIME-Version:") = 0 Then
						//    'text
						//    Dim sToHtml As String = Replace(oValue.ToString, Chr(13) & Chr(10), "<br/>")
						//    sToHtml = Replace(sToHtml, Chr(13), "<br/>")
						//    sToHtml = Replace(sToHtml, Chr(10), "<br/>")
						//    sToHtml = Replace(sToHtml, vbTab, "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;")
						//    GetFriendly = sToHtml ' Replace(oValue.ToString, vbCrLf, sToHtml)
						//Else
						//    'mime
						//    Dim oMessage As New MailBee.Mime.MailMessage
						//    oMessage.LoadMessage(System.Text.Encoding.ASCII.GetBytes(oValue))
						//    GetFriendly = oMessage.BodyHtmlText
						//End If
						//V_T:2/13/15
						tempGetFriendly = oValue.ToString();
						break;
					case "MMP":
						//do we have mime or plain text
						//if plain text, let's not convert, and give to user the value stored in the db
						//if mime, it will start with "MIME-Version:"
						//If InStr(Convert.ToString(oValue), "MIME-Version:") = 0 Then
						//    'text
						//    GetFriendly = oValue.ToString
						//Else
						//    'mime
						//    Dim oMessage As New MailBee.Mime.MailMessage
						//    oMessage.LoadMessage(System.Text.Encoding.ASCII.GetBytes(oValue))
						//    GetFriendly = oMessage.BodyPlainText
						//End If
						//V_T:2/13/15
						//GetFriendly = oValue.ToString
						//If InStr(Convert.ToString(oValue), "MIME-Version:") = 0 Then
						tempGetFriendly = goUt.StripHTML(oValue.ToString());
						break;
							//End If
					case "ADR":
						tempGetFriendly = oValue;
						break;
					case "ADV": //V_T to show the attachments as link in the view
						string sGid = Convert.ToString(GetFieldVal("GID_ID"));
						tempGetFriendly = goTR.ADR_to_Link(Convert.ToString(oValue), sGid, sFieldName, GetFileName());
						break;
					default:
						tempGetFriendly = oValue;
						break;
				}
			}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If
			//End Try
			return tempGetFriendly;
		}
		private bool UpdateMissingSysNames(string par_sExpression, int par_iTable, string par_sSysName, string par_sGID)
		{

			DataTable table = oDataSet.Tables[par_iTable];
			DataRow[] foundRows = null;

			if (bUpdateSysNames == true)
			{
				//update all sysnames
			}
			else
			{
				//update missing sysnames
				par_sExpression += " AND [" + par_sSysName + "] IS NULL";
			}

			// Use the Select method to find all rows matching the filter.
			foundRows = table.Select(par_sExpression);

			if (foundRows.GetUpperBound(0) > 49)
			{
				if (bBypassSysNameUpdateLimit == false)
				{
					bSysNameUpdateLimitExceeded = true;
					return true;
				}
			}

			int i = 0;
			// Write sys_name for each found row.
			for (i = 0; i <= foundRows.GetUpperBound(0); i++)
			{
				foundRows[i][par_sSysName] = goData.GetSysNameForGID(foundRows[i][par_sGID].ToString());
			}


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}


		//V_T 10/19/2015 -- Holds all the dirty fields info
		private void AddDirtyFieldToCollection(clRowSet oRS, string sField, string sOldValue, string sNewValue)
		{
			//Try
			if (oRS.gsDirtyFields.ContainsKey(sField))
			{
				oRS.gsDirtyFields.Remove(sField);
			}
			oRS.gsDirtyFields.Add(sField, new Tuple<object, object>(sOldValue, sNewValue));
			//Catch ex As Exception
			//    Throw ex
			//End Try
		}

#endregion



		~clRowSet()
		{
// INSTANT C# NOTE: The base class Finalize method is automatically called from the destructor:
			//base.Finalize();
		}
	}

}

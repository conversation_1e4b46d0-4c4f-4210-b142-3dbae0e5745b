﻿using Microsoft.VisualBasic;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Selltis.Core
{
    public class SessionViewInfo : ICloneable 
    {

        public object Clone()
        {
            return this.MemberwiseClone();
        }

        //public View View { get; set; }
        public string ViewCondition { get; set; }
        public string ViewMetaData { get; set; }
        public string ViewTitle { get; set; }
        public string SortText { get; set; }
        public string Toprec { get; set; }
        public string Bottomrec { get; set; }
        public string BrowseInstruct { get; set; }
        public int LastPageNumber { get; set; }
        public int PreviousPageno { get; set; }
        public string TableName { get; set; }
        public bool IsSorted { get; set; }
        public string MasterViewKey { get; set; }
        public string Fields { get; set; }
        public int PageSize { get; set; }
        public int LinksTop { get; set; }
        public bool IsMasterView { get; set; }
        public int ViewDataCount { get; set; }
        public string GroupedColumns { get; set; }
        public string PercentageColumns { get; set; }
        public string AggregateColumns { get; set; }
        public IList<GridColumn> Columns { get; set; }

        public Collection FieldDefs = new Collection();
        public IList<string> Sorts { get; set; }
        public string ViewType { get; set; }
        public string AutoCount { get; set; }
        public string AutoLoad { get; set; }
        public bool DefaultAutoLoad { get; set; }
        public string RawViewId { get; set; }
        public string Section { get; set; }
        public bool IsInvalid { get; set; }
        public bool IsActive { get; set; }
        public bool IsParent { get; set; }

        public string DependencyViewIds { get; set; }
        public string DependencyViewIdsOnFocus { get; set; }

        public Dictionary<string,string> HashContainedColumns { get; set; }
        public int Index { get; set; }
        public int DisplayIndex { get; set; }


        //To Persist the last selected record to load even after save or cancel
        public string LastSelectedRecord { get; set; }
        public int LastSelectedRowIndex { get; set; }

        //Chart properties
        public string Chartname { get; set; }
        public string CTitle { get; set; }
        public string Title { get; set; }
        public List<Selltis.Core.Chart.clSeries> Series { get; set; }
        public string Format { get; set; }
        public bool VisibleLegend { get; set; }
        public ArrayList Xaxis { get; set; }
        public ArrayList XaxisValuesUsedInToolTip { get; set; }
        public ArrayList SeriesNames { get; set; }
        public string YaxisLabel { get; set; }
        public string XaxisLabel { get; set; }
        public ArrayList SeriesColors { get; set; }
        public bool ShowGoleLine { get; set; }
        public double GoleLineValue { get; set; }
        public double MaxValue { get; set; }
        public string ViewID { get; set; }
        public string FilterText_With_Values { get; set; }
        public List<Selltis.Core.Chart.clPieSeries> PieSeries { get; set; }
              

        public Collection ChartSorts = new Collection();         
        public string GraphXLabel { get; set; }
        public Collection gcSorts = new Collection();
        public Collection gcChartData = new Collection();
        public Collection gcChartFilters = new Collection();
        public int RollupTotal { get; set; }

        public bool oChartVisible { get; set; }

        public string ViewRecordOpen { get; set; }


        public int CurrentPageNumber { get; set; }
        public int TopViewCount { get; set; }
        public int TabViewCount { get; set; }

        public string MapLatitudeField { get; set; }
        public string MapLongitudeField { get; set; }
        public string MapIconField { get; set; }
        public string ShowLines_Markers { get; set; }

        //Filter text box Text
        public string QS_Condition { get; set; }


        public bool IsSelectSpecificRecord { get; set; }
        public string SelectSpecificRecordId { get; set; }

        public bool HasFirst { get; set; }

        public bool HasLast { get; set; }

        public bool HasNext { get; set; }

        public bool HasPrevious { get; set; }


    }

    

}

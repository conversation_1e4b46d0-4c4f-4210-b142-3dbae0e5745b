﻿using Selltis.BusinessLogic;
using Selltis.Core;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Web.Http;
using System.Web.Http.Cors;
using System.EnterpriseServices.CompensatingResourceManager;
using System.Xml.Linq;
using System.Text;

namespace Selltis.WebApi.Controllers
{
    //[Authorize]
    [EnableCors(origins: "*", headers: "*", methods: "*")]
    public class SelltisApiController : ApiController
    {
        [HttpPost]
        public HttpResponseMessage GetUsers(string hostName,string userName="")
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;          

            if (!hostName.EndsWith(".selltis.com"))
                hostName += ".selltis.com";

            if (Logon("", hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }
            HttpContext.Current.Session["HostName"] = hostName;
            clInit _clint = new clInit();

            string sFilter = "CHK_ACTIVEFIELD=1 AND CHK_ONNETWORK=1 AND EML_EMAIL<[]>''";
            if(!string.IsNullOrEmpty(userName))
            {
                sFilter = "CHK_ACTIVEFIELD=1 AND EML_EMAIL='" + userName +"'";
            }

            //to-do : the field names may not be same for all the clients so it shouldn't be hardcoded. ex: Business unit
            clRowSet doRS = new clRowSet("US", clC.SELL_READONLY, sFilter, "LNK_PRIMARY_BC,TXT_FULLNAME", "TXT_SXNO,TXT_FULLNAME,TXT_NAMEFIRST,TXT_NAMELAST,TXT_CODE,MLS_TYPE,LNK_RELATED_JF%%TXT_JobFuncName,LNK_RELATED_BU%%TXT_BusinessUnitName,LNK_PRIMARY_BC%%TXT_BUCompanyName,LNK_RELATED_BC%%TXT_BUCompanyName,EML_EMAIL,CHK_SSOADMIN", 5000, "", "", "", "", "", true);
            doRS.ToTable();
            DataTable dtData = doRS.dtTransTable;
            UpdateColumnLabels(ref dtData, "US");
            response = Request.CreateResponse(HttpStatusCode.OK, dtData); 
            return response;
        }       

        private void UpdateColumnLabels(ref DataTable dt, string sFileName)
        {
            clData _godata = (clData)Util.GetInstance("data");
            int iValid = 0;
            foreach (DataColumn _col in dt.Columns)
            {
                _col.ColumnName = _godata.GetFieldFullLabelFromName(sFileName, _col.ColumnName, ref iValid);
            }
        }    

        private bool Logon(string userName, string hostName, string pwd = "")
        {
            string sHostName = (hostName == "localhost" || string.IsNullOrEmpty(hostName)) ? "default" : hostName;
            //if (string.IsNullOrEmpty(userName))
            //    userName = "system";
            //Load Settings
            DataSet ds = new DataSet();
            string myXMLfile = System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString() + sHostName + "/SiteSettings.xml";
            FileStream fsReadXml = new FileStream(myXMLfile, FileMode.Open, FileAccess.Read);
            ds.ReadXml(fsReadXml);
            fsReadXml.Close();
            fsReadXml.Dispose();
            HttpContext.Current.Session[hostName + "_SiteSettings"] = ds.Tables[0];

            //Login Process 
            string connStr = ds.Tables[0].Rows[0]["ConnectionString"].ToString();
            HttpContext.Current.Session[sHostName + "_" + "ConnString"] = connStr;
            SqlConnection sqlConnection = new SqlConnection(connStr);
            if (sqlConnection.State == ConnectionState.Closed)
                sqlConnection.Open();
            string sql = string.Empty;

            string IsSSoEnabled = "false";
            try
            {
                if (ds.Tables[0].Columns.Contains("UseSSO"))
                {
                    IsSSoEnabled = ds.Tables[0].Rows[0]["UseSSO"].ToString();
                }
                else
                {
                    IsSSoEnabled = "false";
                }
            }
            catch
            {
            }

            if (string.IsNullOrEmpty(userName))
            {
                userName = IsSSoEnabled == "true" ? "<EMAIL>" : "system";
            }              

            if (string.IsNullOrEmpty(pwd))
            {
                if (IsSSoEnabled == "true")
                {
                    sql = "Select b.* from US a Join XU b on b.GID_UserID = a.GID_ID WHERE a.CHK_ActiveField=1 AND cast(a.EML_Email as nvarchar(250))='" + userName + "'";
                }
                else if (IsSSoEnabled == "false")
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'";
                }
                else
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'";
                }

            }
            else
            {
                if (IsSSoEnabled == "true")
                {
                    sql = "Select b.* from US a Join XU b on b.GID_UserID = a.GID_ID WHERE a.CHK_ActiveField=1 AND cast(a.EML_Email as nvarchar(250))='" + pwd + "'";
                }
                else if (IsSSoEnabled == "false")
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'  and [TXT_Password]='" + pwd + "'";
                }
                else
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "' and [TXT_Password]='" + pwd + "'";
                }
            }

            SqlCommand comm = new SqlCommand(sql, sqlConnection);
            //comm.Parameters.AddWithValue("@username", userName);
            //comm.Parameters.AddWithValue("@pwd", pwd);

            SqlDataReader reader = comm.ExecuteReader();
            if (reader.HasRows)
            {
                DataTable dt = new DataTable();
                dt.Load(reader);
                if (dt.Rows.Count == 0)
                {
                    return false;
                }
                else
                {
                    HttpContext.Current.Session["USERID"] = dt.Rows[0]["GID_UserID"].ToString();
                    HttpContext.Current.Session["LOGINID"] = dt.Rows[0]["GID_ID"].ToString();
                    HttpContext.Current.Session[sHostName + "_" + "LOGINNAME"] = dt.Rows[0]["TXT_LogonName"].ToString();
                    return true;
                }
            }
            else
                return false;


        }

        [HttpPost]
        public HttpResponseMessage DeactivateSelltisUser(clUser _user)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response=null;
            //string hostName,string modifiedBy,string userEmailId

            try
            {
                if(string.IsNullOrEmpty(_user.modifiedBy))
                {
                    response = Request.CreateResponse(HttpStatusCode.BadRequest, "User has been deactivated in Selltis successfully");
                    return response;
                }

                if (!_user.hostName.EndsWith(".selltis.com"))
                    _user.hostName += ".selltis.com";

                if (Logon("", _user.hostName, ""))
                {
                    HttpContext.Current.Session["HostName"] = _user.hostName;
                    clInit _clint = new clInit();

                    //update Active and OnNetwork to false in the user table
                    clRowSet doRS = new clRowSet("US", clC.SELL_EDIT, "EML_EMAIL='" + _user.userEmailId + "'","", "GID_ID,CHK_ActiveField,CHK_OnNetwork,MMO_Note,Txt_FullName", par_bBypassValidation:true,par_bNoRecordOnSave:true);
                    if (doRS.GetFirst() == 1)
                    {
                        string sUserGID = Convert.ToString(doRS.GetFieldVal("GID_ID"));
                        doRS.SetFieldVal("CHK_ActiveField", 0, 2);
                        doRS.SetFieldVal("CHK_OnNetwork", 0, 2);
                        doRS.SetFieldVal("MMO_Note", "Admin Portal - user login has been deactivated by " + _user.modifiedBy + " on " + DateTime.Now.ToString());

                        if (doRS.Commit() == 1)
                        {
                            //update Enable to false in the logins table
                            //string sSQL = "update b Set b.CHK_Enabled = 0 from US a Join XU b on b.GID_UserID = a.GID_ID Where a.Gid_id='" + sUserGID + "'";
                            string sSQL = "Delete From XU b Where GID_UserID = '" + sUserGID + "'";
                            clData _godata = (clData)Util.GetInstance("data");
                            if (_godata.RunSQLQuery(sSQL))
                            {
                                response = Request.CreateResponse(HttpStatusCode.OK, "User has been deactivated in Selltis successfully");

                                clEmail _email = new clEmail();
                                string suserName = Convert.ToString(doRS.GetFieldVal("Txt_FullName"));
                                string sMailBody = "Please deactivate the following user license in Selltis ( "+ _user.hostName + " ) <br/><br/> User Name : " + suserName + "<br/> Email : " + _user.userEmailId + "<br/> Requested By : " + _user.modifiedBy;
                                _email.SendSMTPEmailNew("OTC-Deactivate user license request", sMailBody, "<EMAIL>","","","",bHTML:true);
                                return response;
                            }
                        }
                        else
                        {
                            response = Request.CreateResponse(HttpStatusCode.OK, "Commit failed in selltis");
                            return response;
                        }
                    }
                    else
                    {
                        response = Request.CreateResponse(HttpStatusCode.OK, "User is not existed in Selltis");
                        return response;
                    }
                }
            }
            catch (Exception ex)
            {
                response = Request.CreateResponse(HttpStatusCode.InternalServerError, ex.Message);
                return response;
            }           
          
            return response;
        }

        [HttpPost]
        public HttpResponseMessage AddSelltisUser(AddUserRequest _user)
        
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response = null;
           
            try
            {
                if (string.IsNullOrEmpty(_user.RequestedBy))
                {
                    response = Request.CreateResponse(HttpStatusCode.BadRequest, "Requested by cannot be empty");
                    return response;
                }

                if (!_user.HostName.EndsWith(".selltis.com"))
                    _user.HostName += ".selltis.com";

                if (Logon("", _user.HostName, ""))
                {
                    HttpContext.Current.Session["HostName"] = _user.HostName;
                    clInit _clint = new clInit();

                    clEmail _email = new clEmail();
                    string sSubject = "New Selltis User: " + _user.FirstName + " " + _user.LastName;
                    string sTo = "<EMAIL>";
                    StringBuilder sBody = new StringBuilder();
                    //sBody.AppendLine("Please create Selltis profile for <b>" + _user.FirstName + " " + _user.LastName + "</b>");
                    //sBody.AppendLine(" ");
                    //sBody.AppendLine("<b> First Name: </b>" + _user.FirstName);
                    //sBody.AppendLine("<b> Last Name: </b>" + _user.LastName);
                    //sBody.AppendLine("<b> Title: </b>" + _user.Title);
                    //sBody.AppendLine("<b> Type: </b>" + _user.Type);
                    //sBody.AppendLine("<b> Job Function: </b>" + _user.JobFunction);
                    //sBody.AppendLine("<b> E-Mail: </b>" + _user.Email);
                    //sBody.AppendLine("<b> Segment: </b>" + _user.Segment);
                    //sBody.AppendLine("<b> Business Unit: </b>" + _user.BusinessUnit);
                    //sBody.AppendLine("<b> Branch: </b>" + _user.Branch);
                    //sBody.AppendLine("<b> Primary Business Unit: </b>" + _user.PrimaryBusinessUnit);
                    //sBody.AppendLine("<b> Supervisor: </b>" + _user.Supervisor);
                    //sBody.AppendLine("<b> User Permissions Group: </b>" + _user.UserPermissionsGroup);
                    //sBody.AppendLine("<b> CSD Sales Rep#: </b>" + _user.CSDSalesRepNumber);
                    //sBody.AppendLine("<b> Requested By: </b>" + _user.RequestedBy);
                    //sBody.AppendLine("<b> Host Name: </b>" + _user.HostName);

                    sBody.AppendLine("Please create Selltis profile for <b>" + _user.FirstName + " " + _user.LastName + "</b><br/><br/>");
                    sBody.AppendLine("<table border='1' cellspacing='0' cellpadding='5'>");
                    sBody.AppendLine("<tr><td><b>First Name:</b></td><td>" + _user.FirstName + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>Last Name:</b></td><td>" + _user.LastName + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>Title:</b></td><td>" + _user.Title + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>Type:</b></td><td>" + _user.Type + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>Job Function:</b></td><td>" + _user.JobFunction + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>E-Mail:</b></td><td>" + _user.Email + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>Segment:</b></td><td>" + _user.Segment + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>Business Unit:</b></td><td>" + _user.BusinessUnit + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>Branch:</b></td><td>" + _user.Branch + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>Primary Business Unit:</b></td><td>" + _user.PrimaryBusinessUnit + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>Supervisor:</b></td><td>" + _user.Supervisor + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>User Permissions Group:</b></td><td>" + _user.UserPermissionsGroup + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>CSD Sales Rep#:</b></td><td>" + _user.CSDSalesRepNumber + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>Requested By:</b></td><td>" + _user.RequestedBy + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>Host Name:</b></td><td>" + _user.HostName + "</td></tr>");
                    sBody.AppendLine("<tr><td><b>Notes:</b></td><td>" + _user.Notes + "</td></tr>");
                    sBody.AppendLine("</table>");
                   
                    if (_email.SendSMTPEmailNew(sSubject, sBody.ToString(), sTo, "", "", "", "", "", "", true))
                    {
                        response = Request.CreateResponse(HttpStatusCode.OK, "Add user request has been submitted successfully in Selltis");
                        return response;
                    }
                    else
                    {
                        response = Request.CreateResponse(HttpStatusCode.InternalServerError, "Add user request failed");
                        return response;
                    }                    
                }
            }
            catch (Exception ex)
            {
                response = Request.CreateResponse(HttpStatusCode.InternalServerError, ex.Message);
                return response;
            }

            return response;
        }
    }

    public class clUser
    {
        public string hostName { get; set;}
        public string modifiedBy { get; set; }
        public string userEmailId { get; set; }
    }

    public class AddUserRequest
    {
        public string LastName { get; set; }
        public string FirstName { get; set; }
        public string Title { get; set; }
        public string Type { get; set; }
        public string JobFunction { get; set; }
        public string Email { get; set; }
        public string Segment { get; set; }
        public string BusinessUnit { get; set; }
        public string Branch { get; set; }
        public string PrimaryBusinessUnit { get; set; }
        public string Supervisor { get; set; }
        public string UserPermissionsGroup { get; set; }
        public string CSDSalesRepNumber { get; set; }
        public string RequestedBy { get; set; }
        public string HostName { get; set; }
        public string Notes { get; set; }
    }
}

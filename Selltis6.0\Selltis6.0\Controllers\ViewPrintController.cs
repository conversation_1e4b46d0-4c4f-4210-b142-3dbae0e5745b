﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Selltis.MVC.Models;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Data;
using Kendo.Mvc.UI;
using Kendo.Mvc.Extensions;
using System.Text;
using System.Reflection;
using System.IO;
using Microsoft.VisualBasic;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using System.Drawing;
using System.Net.Mail;
using System.Net.Mime;
using System.Configuration;
using Newtonsoft.Json;
using Ads.Soa.DomainObject.Logistics;

namespace Selltis.MVC.Controllers
{
    public class ViewPrintController : Controller
    {
        //
        // GET: /ViewPrint/
        private clProject goP;
        private clData goData;
        private clTransform goTR;
        private clMetaData goMeta;
        private clError goErr;
        public JsonResult Print(string Mode, string Target, string ViewKey, int Index, string MasterViewSelId, string Key)
        {
            goP = (clProject)Util.GetInstance("p");
            ViewPrint _viewPrint = new ViewPrint();
            ViewKey = ViewKey.Replace(" ", "");

            if (Util.GetSessionValue("Print" + ViewKey) != null)
            {
                _viewPrint = (ViewPrint)Util.GetSessionValue("Print" + ViewKey);
            }

            if (!goP.IsUserAdmin() & goP.IsUserAuthor() < 2)
                _viewPrint.ViewMetaButton = false;

            _viewPrint = GetViewPrintModel(Mode, Target, _viewPrint, ViewKey, Key);

            Util.SetSessionValue("TargettedAction_PRINT", Target);

            if (goP.IsPCLinkSupported())
            {
                _viewPrint.NoPCLinkWarning = true;
            }

            _viewPrint.Index = Index;

            Util.SetSessionValue("MasterViewSelId", MasterViewSelId);
            Util.SetSessionValue("Print" + ViewKey, _viewPrint);

            if (Mode != null && Util.GetSessionValue("DesktopId") != null)
            {
                if (Mode.ToUpper() == "DESKTOP")
                {
                    ViewPrint _viewPrintTemp = new ViewPrint();
                    DesktopModel _desktopModel = new DesktopModel("GLOBAL", Util.GetSessionValue("DesktopId").ToString(), true, Key);
                    IList<View> _ViewsCollection = _desktopModel.Views;
                    foreach (View _view in _ViewsCollection)
                    {
                        _viewPrintTemp = GetViewPrintModel(Mode, Target, _viewPrintTemp, _view.ViewId, Key);
                        _viewPrintTemp.ViewId = _view.ViewId; _viewPrintTemp.ViewKey = _view.ViewKey; _viewPrintTemp.ViewType = _view.ViewType; _viewPrintTemp.Mode = "DESKTOP";
                        Util.SetSessionValue("Print" + _view.ViewId.Replace(" ", ""), _viewPrintTemp);
                    }
                }
            }

            return Json(_viewPrint, JsonRequestBehavior.AllowGet);
        }
        public ViewPrint GetViewPrintModel(string Mode, string Target, ViewPrint _model, string ViewKey, string Key)
        {
            goData = (clData)Util.GetInstance("data");
            goTR = (clTransform)Util.GetInstance("tr");
            goMeta = (clMetaData)Util.GetInstance("meta");

            if (Mode != null)
            {
                if (Mode == "")
                    _model.Mode = "VIEW";
                else
                    _model.Mode = Mode.ToUpper();
            }

            //_model.Target = "PRINT";

            _model.Target = Target;

            switch (_model.Mode.ToUpper())
            {
                case "VIEW":
                    _model.CurrentDesktopId = Util.GetSessionValue("DesktopId").ToString();
                    if (_model.CurrentDesktopId != "")
                    {
                        _model.ViewKey = ViewKey.Replace(" ", "");
                        _model.CurrentViewId = ViewKey.Replace(" ", "");
                        if (_model.CurrentViewId != "")
                        {
                            SessionViewInfo _sessionViewInfo = new SessionViewInfo();
                            if (Util.GetSessionValue(Key + "_" + _model.CurrentViewId) != null)
                            {
                                _sessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + _model.CurrentViewId);
                            }
                            _model.MetaData = _sessionViewInfo.ViewMetaData;
                            _model.ViewId = _model.CurrentViewId;
                            _model.ViewName = _sessionViewInfo.ViewTitle;

                            if (goTR != null)
                            {
                                _model.ViewType = goTR.StrRead(_model.MetaData, "TYPE", "", false);
                            }
                            else
                            {
                                _model.ViewType = "";
                            }

                            _model.ViewOption = "LoadedPage";
                            _model.ViewOption1_Enabled = true;
                            _model.ViewOption2_Enabled = true;
                            _model.ViewOption3_Enabled = true;


                            switch (_model.Target.ToUpper())
                            {
                                case "HTMLEMAIL":
                                    _model.Title = "Send View by E-mail";
                                    _model.LabelTitle = "Send view '" + _model.ViewName + "' by e-mail";
                                    _model.TXT_SubjectText = _model.ViewName;
                                    _model.PNL_EmailVisible = true;
                                    _model.LBL_WarningVisible = true;
                                    _model.LBL_NoPCLinkWarningVisible = false;
                                    _model.divOptionsVisible = true;
                                    //MI 4/12/11 Sending an e-mail is supported, goes through the server

                                    break;
                                case "PDF":
                                    _model.Title = "Send View to PDF";
                                    _model.LabelTitle = "Create PDF file from view '" + _model.ViewName + "'";
                                    _model.PNL_EmailVisible = false;
                                    _model.LBL_WarningVisible = true;
                                    _model.divOptionsVisible = true;
                                    if (!goP.IsPCLinkSupported())
                                        _model.LBL_NoPCLinkWarningVisible = true;
                                    break;
                                case "EXCEL":
                                    _model.Title = "Send View to Excel";
                                    _model.LabelTitle = "Send to Microsoft Excel view '" + _model.ViewName + "'";
                                    _model.PNL_EmailVisible = false;
                                    _model.LBL_WarningVisible = false;
                                    if (!goP.IsPCLinkSupported())
                                        _model.LBL_NoPCLinkWarningVisible = true;
                                    //this.EDT_SPLITLINESText = "3";

                                    _model.ViewOption = "LoadedPage";
                                    _model.divOptionsVisible = true;
                                    break;
                                default:
                                    _model.Title = "Print View";
                                    _model.LabelTitle = "Print view '" + _model.ViewName + "'";
                                    _model.divOptionsVisible = true;
                                    _model.LBL_WarningVisible = false;
                                    _model.LBL_NoPCLinkWarningVisible = false;
                                    _model.PNL_EmailVisible = false;
                                    _model.LBL_WarningVisible = false;
                                    break;
                            }

                            //Hide the radio button options
                            if (_model.ViewType.ToUpper() == "LIST" && goTR != null && goTR.StrRead(_model.MetaData, "SECTIONSGROUPED", "0") == "1")
                            {
                                //Report views                                        
                                _model.ViewOption = "AllRecords";
                                _model.divOptionsVisible = false;
                            }
                            if (Microsoft.VisualBasic.Strings.Left(_model.ViewType.ToUpper(), 3) == "CAL")
                            {
                                //Calendar views                                        
                                _model.ViewOption = "LoadedPage";
                                _model.divOptionsVisible = false;
                            }


                            if (_model.ViewType.ToUpper() == "CHART")
                            {
                                //Chart views                                        
                                _model.ViewOption = "AllRecords";
                                _model.divOptionsVisible = false;
                            }
                            if (Target.ToUpper() == "PRINT" || Target.ToUpper() == "PDF")
                            {
                                if (_model.ViewType.ToUpper() == "LIST" && goTR != null && (goTR.StrRead(_model.MetaData, "SECTIONSGROUPED", "0") == "1"))
                                {
                                    _model.ViewOption2_Enabled = false;
                                    _model.ViewOption3_Enabled = false;
                                    _model.ViewOption = "AllRecords";
                                    _model.IsChartView = false;
                                    return _model;
                                }
                                if (_model.ViewType.ToUpper().Substring(0, 3) == "CAL")
                                {
                                    _model.ViewOption1_Enabled = false;
                                    _model.ViewOption3_Enabled = false;
                                    _model.ViewOption = "LoadedPage";
                                    _model.IsChartView = false;
                                    return _model;
                                }
                                if (_model.ViewType.ToUpper() == "CHART")
                                {
                                    _model.ViewOption2_Enabled = false;
                                    _model.ViewOption3_Enabled = false;
                                    _model.ViewOption = "AllRecords";
                                    _model.IsChartView = true;
                                    return _model;
                                }
                            }
                        }
                    }
                    return _model;
                case "DESKTOP":
                    _model.CurrentDesktopId = Util.GetSessionValue("DesktopId").ToString();
                    _model.MetaData = goMeta.PageRead("GLOBAL", _model.CurrentDesktopId, "", true);
                    _model.ViewOptionsLabel = "From list views in this desktop, include:";
                    _model.NonListViewLabel = "Note: Only the loaded pages of records will be included from report, calendar and chart views.";
                    _model.ViewOption1_Enabled = true;
                    _model.ViewOption2_Enabled = true;
                    _model.ViewOption3_Enabled = true;
                    switch (Target.ToUpper())
                    {
                        case "HTMLEMAIL":
                            _model.Title = "Send Desktop to e-mail";

                            if (goTR != null)
                            {
                                _model.LabelTitle = "Send all views in desktop '" + goTR.StrRead(_model.MetaData, "US_WCLABEL", "", true) + "' by e-mail";
                                _model.TXT_SubjectText = goTR.StrRead(_model.MetaData, "US_WCLABEL", "", true);
                            }
                            else
                            {
                                _model.LabelTitle = "";
                                _model.TXT_SubjectText = "";
                            }

                            _model.PNL_EmailVisible = true;
                            _model.LBL_WarningVisible = true;
                            _model.LBL_NoPCLinkWarningVisible = false;
                            break;

                        case "PDF":
                            _model.Title = "Send View to PDF";

                            if (goTR != null)
                            {
                                _model.LabelTitle = "Create PDF file of all views in desktop '" + goTR.StrRead(_model.MetaData, "US_WCLABEL", "", true) + "'";
                            }
                            else
                            {
                                _model.LabelTitle = "";
                            }

                            _model.PNL_EmailVisible = false;
                            _model.LBL_WarningVisible = true;
                            _model.divOptionsVisible = true;
                            if (!goP.IsPCLinkSupported())
                                _model.LBL_NoPCLinkWarningVisible = true;
                            break;
                        default:
                            _model.Title = "Print Desktop";
                            _model.divOptionsVisible = true;
                            if (goTR != null)
                            {
                                _model.LabelTitle = "Print all views in desktop '" + goTR.StrRead(_model.MetaData, "US_WCLABEL", "", true) + "'";
                            }
                            else
                            {
                                _model.LabelTitle = "";
                            }
                            break;
                    }
                    _model.ViewOption = "LoadedPage";
                    return _model;
                default:
                    return _model;
            }


        }
        public string ShowMetaData(string ViewKey)
        {
            ViewPrint _model = new ViewPrint();
            ViewKey = ViewKey.Replace(" ", "");
            if (Util.GetSessionValue("Print" + ViewKey) != null)
            {
                _model = (ViewPrint)Util.GetSessionValue("Print" + ViewKey);
            }
            return _model.MetaData;
        }
        public ActionResult DisplayViewPrint(string ViewOption, int MaxRecords, int Index, string ViewKey, string Mode, string Key)
        {
            IList<View> _ViewsCollection = new List<View>();
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");

            ViewPrint _model = new ViewPrint();
            PrintModel _printModel = new PrintModel();
            ViewKey = ViewKey.Replace(" ", "");

            if (Util.GetSessionValue("Print" + ViewKey) != null)
            {
                _model = (ViewPrint)Util.GetSessionValue("Print" + ViewKey);
            }

            int FirstNRecords = 0;
            if (!string.IsNullOrEmpty(ViewOption))
            {
                switch (ViewOption)
                {
                    case "AllRecords":
                        //FirstNRecords = 10000;
                        FirstNRecords = clC.SELL_VIEWPRINT_MAXRECORDS;
                        break;
                    case "LoadedPage":
                        string Showtop = "";

                        if (goTR != null)
                        {
                            Showtop = goTR.StrRead(_model.MetaData, "SHOWTOP", "10", false);
                        }

                        if (Showtop.ToUpper() == "ALL")
                            FirstNRecords = -1;
                        else
                        {
                            if (goTR != null)
                            {
                                FirstNRecords = Convert.ToInt32(goTR.StrRead(_model.MetaData, "SHOWTOP", "10", false));
                            }
                        }

                        break;
                    case "RecordsFrom1toN":
                        FirstNRecords = MaxRecords;
                        break;
                }
                Util.SetSessionValue("POPUP_ViewOption", ViewOption);
            }
            Util.SetSessionValue("FirstNRecords", FirstNRecords);
            switch (Mode.ToUpper())
            {
                case "VIEW":

                    string _desktopMetaData = "";

                    if (goMeta != null)
                    {
                        _desktopMetaData = goMeta.PageRead("GLOBAL", Util.GetSessionValue("DesktopId").ToString(), "", true);
                    }

                    if (_model.ViewType != null && _model.ViewType.ToUpper() == "LIST" && (goTR.StrRead(_model.MetaData, "SECTIONSGROUPED", "0") == "1"))
                    {
                        Util.SetSessionValue("IsFromPrint" + _model.ViewId.Replace(" ", ""), true);
                        Report _viewReport = new Report(_model.ViewKey.Replace(" ", ""), false, "GLOBAL", _desktopMetaData, Index, true, 0, Key);
                        _viewReport.ViewType = "report";
                        if (!_viewReport.IsMasterView)
                        {
                            _viewReport.MasterSelID = Util.GetSessionValue("MasterViewSelId").ToString();
                        }
                        _ViewsCollection.Add(_viewReport);
                    }
                    else if (_model.ViewType.ToUpper() == "LIST")
                    {
                        Grid _viewGrid = new Grid(_model.ViewKey.Replace(" ", ""), false, "GLOBAL", _desktopMetaData, Index, true, 0, Key);
                        _viewGrid.ViewType = "LIST";
                        if (!_viewGrid.IsMasterView)
                        {
                            _viewGrid.MasterSelID = Util.GetSessionValue("MasterViewSelId").ToString();
                        }
                        _ViewsCollection.Add(_viewGrid);
                    }
                    else if (_model.ViewType.ToUpper() == "CHART")
                    {
                        Chart _viewChart = new Chart(_model.ViewKey.Replace(" ", ""), false, "GLOBAL", _desktopMetaData, Index, true, 0, "", Key);
                        _viewChart.ViewType = "CHART";
                        int tempint = 0;
                        int.TryParse(goTR.StrRead(_desktopMetaData, "VIEW" + Index.ToString() + "HEIGHT", "NONE", false), out tempint);
                        _viewChart.ViewHeight = tempint;
                        _viewChart.ViewHeight = 200 + _viewChart.ViewHeight;
                        _ViewsCollection.Add(_viewChart);
                    }
                    else if (_model.ViewType.ToUpper() == "CALWEEK" || _model.ViewType.ToUpper() == "CALDAY" || _model.ViewType.ToUpper() == "CALMONTH")
                    {
                        Util.SetSessionValue("IsFromPrint" + _model.ViewId.Replace(" ", ""), true);
                        Selltis.Core.Calendar _newCalendar = new Selltis.Core.Calendar(_model.ViewKey.Replace(" ", ""), false, "GLOBAL", _desktopMetaData, Index, true, 0, Key);
                        if (!_newCalendar.IsMasterView)
                        {
                            _newCalendar.MasterSelID = Util.GetSessionValue("MasterViewSelId").ToString();
                        }
                        _ViewsCollection.Add(_newCalendar);
                    }
                    _printModel.PrintTitle = goTR.StrRead(_desktopMetaData, "US_NAME", "");
                    break;
                case "DESKTOP":
                    string _desktopMetaData2 = goMeta.PageRead("GLOBAL", Util.GetSessionValue("DesktopId").ToString(), "", true);
                    DesktopModel _desktopModel = new DesktopModel("GLOBAL", Util.GetSessionValue("DesktopId").ToString(), true, Key);
                    if (_desktopModel.ViewCount == 0)
                    {
                        _desktopModel = (DesktopModel)Util.GetSessionValue("Curr_Desktop" + Key);
                    }
                    _ViewsCollection = _desktopModel.Views;
                    Util.SetSessionValue("_desktopViewCount", _ViewsCollection.Count);
                    foreach (View _view in _ViewsCollection)
                    {
                        if (!_view.IsMasterView)
                        {
                            _view.MasterSelID = Util.GetSessionValue("MasterViewSelId").ToString();
                        }
                    }
                    _printModel.PrintTitle = _desktopModel.Title;
                    break;
                default:
                    break;
            }

            _printModel.ViewsCollection = _ViewsCollection;
            return View(_printModel);
        }
        public JsonResult ReadData([DataSourceRequest] DataSourceRequest request, string ViewKey, string MasterSelID = "", string Key = "")//string FilterText, 
        {
            string TopRecordId = string.Empty;
            SessionViewInfo _SessionViewInfo = new SessionViewInfo();
            string FilterText = string.Empty;
            ViewKey = ViewKey.Replace(" ", "");
            if (Util.GetSessionValue(Key + "_" + ViewKey) != null)
            {
                _SessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + ViewKey);
            }
            string SortText = _SessionViewInfo.SortText;

            if (goTR == null)
            {
                goTR = (clTransform)Util.GetInstance("tr");
            }

            //if (_SessionViewInfo.IsMasterView == true)
            //    MasterSelID = "";
            //else
            //    MasterSelID = Session["MasterViewSelId"].ToString();

            DataTable dt = ReadDataTable(ViewKey, MasterSelID, SortText, Key, 1);

            string _Onelineperrecord = goTR.StrRead(_SessionViewInfo.ViewMetaData, "LIST_ONELINEPERRECORD"); //Util.GetPropertyValueFromMetaData(_SessionViewInfo.ViewMetaData, "LIST_ONELINEPERRECORD");

            request.Page = 1;
            _SessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + ViewKey);
            var result = dt.ToDataSourceResult(request);
            result.Total = int.Parse(_SessionViewInfo.ViewDataCount.ToString());
            return Json(result, JsonRequestBehavior.AllowGet);
        }
        public static DataTable ReadDataTable(string ViewKey, string MasterViewSelID, string SortText, string Key, int SEL_VIEWOPTIONSSelectedIndex, int iFirstNRecords = 10, string Target = "")
        {
            string FilterText = string.Empty;
            string TopRecordId = string.Empty;
            string _sort = SortText;//use to get top rec
            bool _ReverseSort = false;
            clData goData = (clData)Util.GetInstance("Data");
            clTransform goTR = (clTransform)Util.GetInstance("tr");

            DataTable dt = null;
            ViewKey = ViewKey.Replace(" ", "");
            ////Loading from session if its View Loaded Grid data.. S1
            //if (Util.GetSessionValue("POPUP_ViewOption") != null)
            //{
            //    if (Util.GetSessionValue("POPUP_ViewOption").ToString() == "LoadedPage")
            //    {
            //        if (Util.GetSessionValue("Data" + ViewKey) != null)
            //        {
            //            dt = (DataTable)Util.GetSessionValue("Data" + ViewKey);
            //            return dt;
            //        }
            //    }
            //}

            SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + ViewKey);

            string TableName = (_SessionViewInfo.TableName == null) ? "" : _SessionViewInfo.TableName;

            SortText = (_SessionViewInfo.SortText == null) ? "" : _SessionViewInfo.SortText;

            //FilterText = (_SessionViewInfo.ViewCondition == null) ? "" : _SessionViewInfo.ViewCondition;



            //if (!_SessionViewInfo.IsMasterView)
            //{
            //    string sCondtext = Util.GetBetweenString(FilterText, "<%", "%>");
            //    //List<string> lsCondtext = Util.GetBetweenStringInCond(FilterText, "<%", "%>");
            //    string _MasterViewID = MasterViewSelID;
            //    if (string.IsNullOrEmpty(_MasterViewID))
            //    {
            //        if (((SessionViewInfo)Util.GetSessionValue(_SessionViewInfo.MasterViewKey.Replace(" ", ""))) != null)
            //            _MasterViewID = ((SessionViewInfo)Util.GetSessionValue(_SessionViewInfo.MasterViewKey.Replace(" ", ""))).Toprec;
            //    }
            //    FilterText = FilterText.Replace("<%" + sCondtext + "%>", _MasterViewID);
            //}

            FilterText = _SessionViewInfo.ViewCondition;

            // check for DRS Date Range Selector Enabled  (Filter -KA)
            // check for Between Date Range Selector (Filter -KA)
            if (FilterText.Contains("<%StartDate%>"))
            {
                string _FilterText = Util.GetDRSFilterCondition(FilterText, Key);

                FilterText = _FilterText;
            }

            if (FilterText.ToLower().Contains("<%selectedrecordid") || FilterText.ToLower().Contains("<%selectedviewrecordid"))
            {
                Dictionary<string, string> _viewSelectedRecordIds = (Dictionary<string, string>)Util.GetSessionValue(Key + "_" + "ViewSelectedRecordIds");

                do
                {
                    if (FilterText.ToLower().Contains("selectedrecordid"))
                    {
                        int iPos = Strings.InStr(FilterText.ToUpper(), Strings.UCase("<%SelectedRecordID FILE="));
                        string sFile = Strings.Mid(FilterText.ToUpper(), iPos + Strings.Len("<%SelectedRecordID FILE="), 2);
                        string sId = "";
                        if (_viewSelectedRecordIds != null && _viewSelectedRecordIds.ContainsKey(sFile) && !string.IsNullOrEmpty(_viewSelectedRecordIds[sFile].ToString()))
                        {
                            sId = _viewSelectedRecordIds[sFile].ToString();
                        }
                        else
                        {
                            sId = goData.GenSUIDFake(sFile);
                        }
                        //FilterText = FilterText.ToUpper().Replace("<%SELECTEDRECORDID FILE=" + sFile + "%>", sId);
                        FilterText = goTR.Replace(FilterText.ToUpper(), "<%SELECTEDRECORDID FILE=" + sFile + "%>", sId);
                    }
                    else if (FilterText.ToLower().Contains("selectedviewrecordid"))
                    {
                        //if (!string.IsNullOrEmpty(FirstViewTableName))
                        //{
                        //    if (!FilterText.Contains(FirstViewTableName))
                        //    {
                        //        return new DataTable();
                        //    }
                        //}

                        int iPos = Strings.InStr(FilterText.ToUpper(), Strings.UCase("<%SELECTEDVIEWRECORDID FILE="));
                        string sFile = Strings.Mid(FilterText.ToUpper(), iPos + Strings.Len("<%SELECTEDVIEWRECORDID FILE="), 2);
                        string sId = "";
                        if (_viewSelectedRecordIds.ContainsKey(sFile))
                        {
                            sId = _viewSelectedRecordIds[sFile].ToString();
                        }
                        else
                        {
                            sId = goData.GenSUIDFake(sFile);
                        }
                        //FilterText = FilterText.ToUpper().Replace("<%SELECTEDVIEWRECORDID FILE=" + sFile + "%>", sId);
                        FilterText = goTR.Replace(FilterText.ToUpper(), "<%SELECTEDVIEWRECORDID FILE=" + sFile + "%>", sId);
                    }


                } while (FilterText.ToLower().Contains("selectedviewrecordid") || FilterText.ToLower().Contains("selectedrecordid"));

            }

            //SB checking user chosen no of records to load and assigning to iFirstNRecords
            _SessionViewInfo.PageSize = (_SessionViewInfo.PageSize == 0) ? _SessionViewInfo.ViewDataCount : _SessionViewInfo.PageSize;
            iFirstNRecords = (SEL_VIEWOPTIONSSelectedIndex == 1) ? _SessionViewInfo.PageSize : iFirstNRecords;

            //Toprec with format to get data
            string _TopRec = "";
            if (!string.IsNullOrEmpty(TopRecordId))
            {
                _TopRec = Util.GetTopRec(TableName, _sort, TopRecordId);
            }

            //previously uncommented now whatever changes have done in Util Readdata those must be done here also..J
            //TopRecordId = _SessionViewInfo.Toprec;
            //var _TopRec = Util.GetTopRec(TableName, SortText, TopRecordId);

            string GenFieldList = string.Empty;

            string GenFieldDef = string.Empty;

            var gcSorts = _SessionViewInfo.Sorts;
            var gcColumns = _SessionViewInfo.Columns;

            //SB Chart does not load excel records.. Just returning the "Selltis Chart" text that user understands..
            if (Target == "EXCEL" && (_SessionViewInfo.ViewType == "CHART"))
            {
                dt = new DataTable();
                dt.Columns.Add("Selltis Chart", typeof(string));
                //dt.Columns[0].ColumnName = "Selltis Chart";
                return dt;
            }
            if (gcColumns != null)
            {
                for (int i = 0; i < gcColumns.Count; i++)
                {
                    string sFieldFormatted = gcColumns[i].NameOrg;
                    if (string.IsNullOrEmpty(GenFieldList))
                    {
                        GenFieldList = "GEN_" + i.ToString();
                    }
                    else
                    {
                        GenFieldList = GenFieldList + ", " + "GEN_" + i.ToString();
                    }
                    goTR.StrWrite(ref GenFieldDef, "GEN_" + i.ToString(), sFieldFormatted);
                }
            }
            if (gcSorts != null)
            {
                for (int i = 0; i < gcSorts.Count; i++)
                {
                    string sFieldFormatted = gcSorts[i];
                    if (string.IsNullOrEmpty(GenFieldList))
                    {
                        GenFieldList = "GEN_SORT_" + i.ToString();
                    }
                    else
                    {
                        GenFieldList = GenFieldList + ", " + "GEN_SORT_" + i.ToString();
                    }
                    goTR.StrWrite(ref GenFieldDef, "GEN_SORT_" + i.ToString(), sFieldFormatted);
                }
            }
            if (!GenFieldList.Contains("GID_ID"))
            {
                GenFieldList = GenFieldList + ",GID_ID";
            }


            //get records that depends on user selection through radio buttons..J
            int iViewTop = 0;
            //int iFirstNRecords = Convert.ToInt32(Util.GetSessionValue("FirstNRecords"));

            if (iFirstNRecords == -2)
            {
                iViewTop = clC.SELL_VIEWPRINT_MAXRECORDS;
            }
            else if (iFirstNRecords == -1)
            {
                if (goTR.StrRead(_SessionViewInfo.ViewMetaData, "SECTIONSGROUPED", "", false) != "1")
                {
                    string showTop_Var = goTR.StrRead(_SessionViewInfo.ViewMetaData, "SHOWTOP", "10", false);
                    if (!string.IsNullOrEmpty(showTop_Var))     //when its not null.. PC Link S1
                    {
                        if (showTop_Var.ToUpper() != "ALL")
                            iViewTop = Convert.ToInt32(showTop_Var);
                        else
                            iViewTop = 10000;
                    }
                }
                else
                {
                    iViewTop = clC.SELL_VIEWPRINT_MAXRECORDS;
                }
            }
            else if (iFirstNRecords == 0)
            {
                iViewTop = 0;
            }
            else if (iFirstNRecords > 0)
            {
                iViewTop = iFirstNRecords;
            }

            clRowSet rs = new clRowSet(TableName, 3, FilterText, SortText, GenFieldList, iViewTop, "", GenFieldDef, "", "", _TopRec, false, false, false, _ReverseSort, _SessionViewInfo.LinksTop, "", false, false, 1800);
            rs.SetDataFormat(clC.SELL_FRIENDLY);
            rs.ToTable();

            dt = rs.dtTransTable;
            int _slocalColCount = 0;
            int _localColCount = 0;
            if (gcColumns != null)
            {
                for (int i = 0; i < dt.Columns.Count; i++)
                {
                    if (dt.Columns[i].ColumnName.Contains("GEN_SORT_"))
                    {
                        string _lsortname = gcSorts[_slocalColCount].Replace(",", "").Replace(" ", "").Replace("<%", string.Empty).Replace("%>", string.Empty).Replace("%%", "__");
                        var data = (from s in gcColumns
                                    where s.Name.Equals(_lsortname)
                                    select s).ToList();
                        if (data.Count == 0)
                        {
                            dt.Columns[i].ColumnName = gcSorts[_slocalColCount].Replace(",", "").Replace(" ", "").Replace("<%", string.Empty).Replace("%>", string.Empty).Replace("%%", "__");
                        }
                        _slocalColCount++;
                    }
                    else if (!dt.Columns[i].ColumnName.Contains("GEN_SORT_") && !dt.Columns[i].ColumnName.Contains("GID_ID"))
                    {
                        if (gcColumns[_localColCount].Name.Contains("GID_ID"))
                        {
                            if (gcColumns[_localColCount].IsVisible == true)
                            {
                                if (!dt.Columns.Contains(gcColumns[_localColCount].Name))
                                {
                                    dt.Columns[i].ColumnName = gcColumns[_localColCount].Name.Replace(",", "").Replace(" ", "");
                                }
                                _localColCount++;
                            }
                        }
                        else
                        {
                            dt.Columns[i].ColumnName = gcColumns[_localColCount].Name.Replace(",", "").Replace(" ", "");
                            _localColCount++;
                        }
                    }
                }
            }

            //SB Setting Mapped Icons(Display as Icon, Button, IconButton)CHK/MLS as View Grids
            if (Target != "EXCEL")
                Util.SetMappedIcons(TableName, _SessionViewInfo.Columns, dt);
            else
            {
                //SB 10-16-2017 tckt#1840 Reformatting into normal text which is readable
                int numberOfColumns = dt.Columns.Count;

                // go through each row
                foreach (DataRow dr in dt.Rows)
                {
                    // go through each column in the row
                    for (int i = 0; i < numberOfColumns; i++)
                    {
                        string cell = dr[i].ToString();

                        Regex r = new Regex(@"<[^>].+?>");
                        // removing html markup and getting only text 
                        string result1 = r.Replace(cell, "");
                        //string result2 = Regex.Replace(cell, @"<[^>].+?>", "");
                        result1 = result1.Replace(".gif", "").Replace(".png", "").Replace(".txt", "").Replace(".jpg", "");
                        dr[i] = result1;
                    }
                }
            }

            return dt;
        }




        public JsonResult btn_OK_Click(string EmailGridHtml, string Target, string ViewKey, string EDT_FIRSTNRECORDSText, int SEL_VIEWOPTIONSSelectedIndex, string TXT_ToText = "", string TXT_CcText = "", string TXT_SubjectText = "", string _viewCount = "0", string siteName = "", string Key = "", string View_Mode = "view")
        {

            goData = (clData)Util.GetInstance("data");
            goTR = (clTransform)Util.GetInstance("tr");
            goMeta = (clMetaData)Util.GetInstance("meta");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");

            EmailGridHtml = HttpUtility.UrlDecode(EmailGridHtml);

            string sGUID = null;
            //int iFirstNRecords = -2;

            //SB checking user selected option to load no of records..
            int iFirstNRecords = 10;
            if (SEL_VIEWOPTIONSSelectedIndex == 2)
            {
                iFirstNRecords = (string.IsNullOrEmpty(EDT_FIRSTNRECORDSText)) ? 0 : int.Parse(EDT_FIRSTNRECORDSText);
            }
            else if (SEL_VIEWOPTIONSSelectedIndex == 0)
            {
                iFirstNRecords = 10000;
            }
            else
            {
                iFirstNRecords = (Util.GetSessionValue("FirstNRecords") == null) ? 10 : int.Parse(Util.GetSessionValue("FirstNRecords").ToString());
            }
            string sReplyName = null;
            string sReplyAddress = null;
            string sHTML = null;
            clEmail oEmail = default(clEmail);
            clRowSet oRS = default(clRowSet);

            string sRange = null;
            string sFileName = "";
            string sMisc = "";
            string sSNQ = "";
            string sPageID = null;
            string sTitle = null;

            //Excel varialbles
            string sDiskFileName = "";
            int iSplitLines = 1;
            string sFilename = "";
            ViewKey = ViewKey.Replace(" ", "");
            ViewPrint _viewPrint = new ViewPrint();
            if (Util.GetSessionValue("Print" + ViewKey) != null)
            {
                _viewPrint = (ViewPrint)Util.GetSessionValue("Print" + ViewKey);
            }
            else
            {
                //SB assigning view data when its session has null data
                _viewPrint = GetViewPrintModel(View_Mode, Target, _viewPrint, ViewKey, Key);
                Util.SetSessionValue("Print" + ViewKey, _viewPrint);
            }

            MessageBoxForPrintEmailPdf messageBoxForPrintEmailPdf = new MessageBoxForPrintEmailPdf();
            Desktop desktop = new Desktop("GLOBAL", _viewPrint.CurrentDesktopId, false);
            if (desktop.ViewCount == 0)
            {
                desktop = (DesktopModel)Util.GetSessionValue("Curr_Desktop" + Key);
            }
            //SB when the print mode is null.. filling from parameter
            if (_viewPrint.Mode == null)
            {
                _viewPrint.Mode = View_Mode.ToUpper();
            }
            //_viewPrint.Index = Index;

            //SB getting site name.. if parameter returns null taking from url(query string)
            if (string.IsNullOrEmpty(siteName))
            {
                siteName = Request.Url.Scheme + "://" + Request.Url.Authority;
            }
            //try
            //{
            switch (_viewPrint.Mode)
            {
                case "VIEW":

                    if (SEL_VIEWOPTIONSSelectedIndex == 2)
                    {
                        EDT_FIRSTNRECORDSText = (string.IsNullOrEmpty(EDT_FIRSTNRECORDSText)) ? "0" : EDT_FIRSTNRECORDSText;
                        iFirstNRecords = Convert.ToInt32(EDT_FIRSTNRECORDSText);
                        if (iFirstNRecords < 1 | iFirstNRecords > clC.SELL_VIEWPRINT_MAXRECORDS)
                        {
                            MessageBox(messageBoxForPrintEmailPdf, "Please enter a number between 1 and " + goTR.NumToString(clC.SELL_VIEWPRINT_MAXRECORDS) + " for the number of records to print or select a different option.", "FirstNRecordsValidation");
                            return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                        }
                    }

                    //switch (SEL_VIEWOPTIONSSelectedIndex)
                    //{
                    //    case 0:
                    //        iFirstNRecords = -2;
                    //        break;
                    //    case 1:
                    //        iFirstNRecords = -1;
                    //        break;
                    //    case 2:
                    //        break;
                    //    //iFirstNRecords is set during validation above
                    //}
                    Util.SetSessionValue("FirstNRecords", iFirstNRecords);
                    sGUID = _viewPrint.CurrentDesktopId;
                    if (sGUID == null | string.IsNullOrEmpty(sGUID))
                    {
                        //goErr.SetError(35000, sProc, "Open desktop couldn't be found in history: goHist.GetLastOpenDesktopGUID returned ''.");
                        return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                    }
                    else
                    {
                        switch (Target)
                        {
                            case "HTMLEMAIL":
                                //sHTML = SelectedViewToHTML(ref EmailGridHtml, _viewPrint.ViewName, false);                                    

                                //SB Getting Mapped html data
                                string EmailPageHtml = GetDataIntoHTML(desktop, _viewPrint, iFirstNRecords, siteName, Key, SEL_VIEWOPTIONSSelectedIndex);
                                oEmail = new clEmail();

                                oRS = new clRowSet("US", 3, "GID_ID='" + goP.GetUserTID() + "'", "", "TXT_NameLast,TXT_NameFirst,EML_email");
                                if (oRS.Count() < 1)
                                {
                                    //Record may have been deleted - do not raise error so the calling page can display the following 'friendly' error
                                    MessageBox(messageBoxForPrintEmailPdf, "Sending e-mail via SMTP failed because your User record ('" + goP.GetUserTID() + "') cannot be found. Please contact your administrator.");
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                //MI 5/14/09 Changed SYS_Name to TXT_NameFirst & " " & TXT_NameLast
                                sReplyName = oRS.GetFieldVal("TXT_NameFirst") + " " + oRS.GetFieldVal("TXT_NameLast");
                                sReplyAddress = oRS.GetFieldVal("EML_EMail").ToString();
                                if (string.IsNullOrEmpty(sReplyAddress))
                                {
                                    //MessageBox(messageBoxForPrintEmailPdf, "Email address for this user should not be empty.");
                                    //return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                    sReplyAddress = "<EMAIL>";
                                }


                                if (!oEmail.SendSMTPEmailNew(TXT_SubjectText, EmailPageHtml, TXT_ToText, TXT_CcText, "", "", sReplyAddress, sReplyName, "", true))
                                //if (!oEmail.SendSMTPEmail(TXT_SubjectText, EmailGridHtml, TXT_ToText, TXT_CcText, "", "", sReplyName, sReplyAddress, "", true, sHTML))
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "Error sending e-mail via SMTP: " + goErr.GetLastError() + " " + goErr.GetLastError("MESSAGE"));
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                if (goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "VIEWORDESKTOPSENDEMAILSHOWSUCCESS", "1") == "1")
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "The view was sent successfully via server.", "FinalOKMessage", "", clC.SELL_MB_YESNO, "  Close  ", "Don't show again");
                                    messageBoxForPrintEmailPdf.PNL_BodyDisible = true;
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                break;
                            case "EXCEL":
                                string gsAutoLoadView = goTR.StrRead(_viewPrint.MetaData, "AUTOLOADVIEWDATA", "");
                                bool gbDoNotAutoLoadMode;

                                if (gsAutoLoadView == "0")
                                {
                                    gbDoNotAutoLoadMode = true;
                                }
                                else
                                {
                                    gbDoNotAutoLoadMode = false;
                                }

                                // _viewPrint.ViewName = goTR.StripIllegalChars(_viewPrint.Title, "REMOVE");
                                //_viewPrint.ViewName = desktop.Title;        //Title for Excel..S1
                                sFilename = SelectedViewToTextFile(_viewPrint.CurrentViewId, _viewPrint.ViewType, gbDoNotAutoLoadMode, iFirstNRecords, SEL_VIEWOPTIONSSelectedIndex, iSplitLines, 800, Key, Target);
                                string Result = "";
                                goTR.StrWrite(ref Result, "US_NAME", "Send to Excel Queue Item");
                                goTR.StrWrite(ref Result, "MODE", "EXCEL");
                                goTR.StrWrite(ref Result, "OBJECTTOSEND", "View");
                                goTR.StrWrite(ref Result, "SENDNAME", "View to Excel '" + goTR.StripIllegalChars(_viewPrint.ViewName) + "'");
                                goTR.StrWrite(ref Result, "VIEWNAME", goTR.StripIllegalChars(_viewPrint.ViewName));
                                goTR.StrWrite(ref Result, "FILE", sFilename);
                                sPageID = "SNQ_" + goData.GenSUID("XX");
                                if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, Result))
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "An error occurred queuing this send job.");
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                else
                                {
                                    if (goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EXCELSENDSHOWSUCCESS", "1") == "1")
                                    {
                                        //goUI.ExecuteSendPopup = true;
                                        MessageBox(messageBoxForPrintEmailPdf, "The data was prepared sucessfully. If you have PC Link configured to 'Auto process', MS Excel will start shortly. If not, double-click the PC Link icon in the Tray and process the job from there.", "FinalOKMessage", "", clC.SELL_MB_YESNO, "  OK  ", "Don't show again");
                                        return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                    }
                                    else
                                    {
                                        //goUI.ExecuteSendPopup = true;
                                        //Close();
                                        return Json("success", JsonRequestBehavior.AllowGet);
                                    }
                                }
                                break;

                            case "PDF":
                                EmailPageHtml = GetDataIntoHTML(desktop, _viewPrint, iFirstNRecords, siteName, Key, SEL_VIEWOPTIONSSelectedIndex);
                                //Append current datetime and user code to unique the filename
                                int par_iValid = 4;
                                string par_sDelim = "";
                                sHTML = EmailPageHtml;
                                sFileName += "_" + goTR.StripIllegalChars(Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(System.DateTime.Now, ref par_iValid, ref par_sDelim), 18), "REMOVE") + "_" + goP.GetUserCode() + ".pdf";

                                int iPageWidth = ((Convert.ToInt32(Util.GetSessionValue("htmlcolwidth")) / 2) + 50);
                                if (iPageWidth <= 1150)
                                {
                                    iPageWidth = 1150;
                                }

                                clPDF oPDF = new clPDF();
                                if (!oPDF.HTMLToPDF(sFileName, sHTML, "A4", "LANDSCAPE", true, sTitle, par_iPageWidth: iPageWidth))
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "Error creating a PDF file.");
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                else
                                {
                                    goTR.StrWrite(ref sMisc, "SourceFolder", "temp");
                                    goTR.StrWrite(ref sMisc, "Delete", "True");
                                    goTR.StrWrite(ref sMisc, "Open", "True");
                                    goTR.StrWrite(ref sSNQ, "MISCINFO", sMisc);
                                    goTR.StrWrite(ref sSNQ, "MODE", "DOWNLOADFILE");
                                    goTR.StrWrite(ref sSNQ, "FILE", sFileName);
                                    goTR.StrWrite(ref sSNQ, "US_NAME", "Send to PDF queue item");
                                    goTR.StrWrite(ref sSNQ, "OBJECTTOSEND", "None");
                                    goTR.StrWrite(ref sSNQ, "SENDNAME", "To PDF '" + sFileName + "'");
                                    sPageID = "SNQ_" + goData.GenSUID("XX");
                                    if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, sSNQ))
                                    {
                                        MessageBox(messageBoxForPrintEmailPdf, "An error occurred queuing this send job.");
                                        return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                    }
                                    //goUI.ExecuteSendPopup = true;
                                    return Json("success", JsonRequestBehavior.AllowGet);
                                }
                                break;
                            default:
                                //PRINT

                                break;
                        }
                    }
                    break;

                case "DESKTOP":

                    //---------- Validate ------------
                    if (SEL_VIEWOPTIONSSelectedIndex == 2)
                    {
                        EDT_FIRSTNRECORDSText = (string.IsNullOrEmpty(EDT_FIRSTNRECORDSText)) ? "0" : EDT_FIRSTNRECORDSText;
                        iFirstNRecords = Convert.ToInt32(EDT_FIRSTNRECORDSText);
                        if (iFirstNRecords < 1 | iFirstNRecords > clC.SELL_VIEWPRINT_MAXRECORDS)
                        {
                            MessageBox(messageBoxForPrintEmailPdf, "Please enter a number between 1 and " + goTR.NumToString(clC.SELL_VIEWPRINT_MAXRECORDS) + " for the number of records to print or select a different option.", "FirstNRecordsValidation");
                            return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                        }
                    }

                    //switch (SEL_VIEWOPTIONSSelectedIndex)
                    //{
                    //    case 0:
                    //        iFirstNRecords = -2;
                    //        break;
                    //    case 1:
                    //        iFirstNRecords = -1;
                    //        break;
                    //    case 2:
                    //        break;
                    //    //iFirstNRecords is set during validation above
                    //}
                    Util.SetSessionValue("FirstNRecords", iFirstNRecords);

                    sGUID = _viewPrint.CurrentDesktopId;
                    if (sGUID == null | string.IsNullOrEmpty(sGUID))
                    {
                        //goErr.SetError(35000, sProc, "Open desktop couldn't be found in history: goHist.GetLastOpenDesktopGUID returned ''.");
                        return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                    }
                    else
                    {
                        switch (Target)
                        {
                            case "HTMLEMAIL":
                                string EmailPageHtml = GetDesktopDataIntoHtml(desktop, _viewCount, _viewPrint, siteName, Key, SEL_VIEWOPTIONSSelectedIndex, iFirstNRecords);

                                oEmail = new clEmail();
                                oRS = new clRowSet("US", 3, "GID_ID='" + goP.GetUserTID() + "'", "", "TXT_NameLast,TXT_NameFirst,EML_email");
                                if (oRS.Count() < 1)
                                {
                                    //Record may have been deleted - do not raise error so the calling page can display the following 'friendly' error
                                    //goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP failed because current user's User record cannot be found. GID_ID: '" + goP.GetUserTID + "'.");
                                    MessageBox(messageBoxForPrintEmailPdf, "Sending via SMTP failed because your User record ('" + goP.GetUserTID() + "') cannot be found. Please contact your administrator.");
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                //MI 5/14/09 Changed SYS_Name to TXT_NameFirst & " " & TXT_NameLast
                                sReplyName = oRS.GetFieldVal("TXT_NameFirst") + " " + oRS.GetFieldVal("TXT_NameLast");
                                sReplyAddress = oRS.GetFieldVal("EML_EMail").ToString();

                                TXT_SubjectText = (string.IsNullOrEmpty(TXT_SubjectText)) ? desktop.Title : TXT_SubjectText;
                                if (!oEmail.SendSMTPEmailNew(TXT_SubjectText, EmailPageHtml, TXT_ToText, TXT_CcText, "", "", sReplyAddress, sReplyName, "", true, sHTML))
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "Error sending e-mail via SMTP: " + goErr.GetLastError() + " " + goErr.GetLastError("MESSAGE"));
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                if (goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "VIEWORDESKTOPSENDEMAILSHOWSUCCESS", "1") == "1")
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "The desktop was sent successfully via server.", "FinalOKMessage", "", clC.SELL_MB_YESNO, "  Close  ", "Don't show again");
                                    messageBoxForPrintEmailPdf.PNL_BodyDisible = true;
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                return Json("desktop", JsonRequestBehavior.AllowGet);
                            //break;
                            case "PDF":

                                string PDFPageHtml = GetDesktopDataIntoHtml(desktop, _viewCount, _viewPrint, siteName, Key, SEL_VIEWOPTIONSSelectedIndex, iFirstNRecords);

                                //Generate the file name from the view's title
                                //if desktop has DRS, append this info to view name
                                sFileName = goTR.StripIllegalChars(goTR.StrRead(_viewPrint.MetaData, "US_WCLABEL", "", true), "REMOVE");
                                sTitle = desktop.Title;

                                //Append current datetime and user code to unique the filename
                                int par_iValid = 4; string par_sDelim = "";
                                sFileName += "_" + goTR.StripIllegalChars(Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.Now, ref par_iValid, ref par_sDelim), 18), "REMOVE") + "_" + goP.GetUserCode() + ".pdf";
                                clPDF oPDF = new clPDF();

                                int iPageWidth = ((Convert.ToInt32(Util.GetSessionValue("htmlcolwidth")) / 2) + 50);
                                if (iPageWidth <= 1150)
                                {
                                    iPageWidth = 1150;
                                }

                                //Set desktop title including date range if any as PDF file name
                                if (!oPDF.HTMLToPDF(sFileName, PDFPageHtml, "A4", "LANDSCAPE", true, goTR.StripIllegalChars(goTR.StrRead(_viewPrint.MetaData, "US_WCLABEL", "", true)), par_iPageWidth: iPageWidth))
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "Error creating a PDF file.");
                                    messageBoxForPrintEmailPdf.PNL_BodyDisible = true;
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                else
                                {
                                    goTR.StrWrite(ref sMisc, "SourceFolder", "temp");
                                    goTR.StrWrite(ref sMisc, "Delete", "True");
                                    goTR.StrWrite(ref sMisc, "Open", "True");
                                    goTR.StrWrite(ref sSNQ, "MISCINFO", sMisc);
                                    goTR.StrWrite(ref sSNQ, "MODE", "DOWNLOADFILE");
                                    goTR.StrWrite(ref sSNQ, "FILE", sFileName);
                                    goTR.StrWrite(ref sSNQ, "US_NAME", "Send to PDF queue item");
                                    goTR.StrWrite(ref sSNQ, "OBJECTTOSEND", "None");
                                    goTR.StrWrite(ref sSNQ, "SENDNAME", "To PDF '" + sFileName + "'");
                                    sPageID = "SNQ_" + goData.GenSUID("XX");
                                    if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, sSNQ))
                                    {
                                        MessageBox(messageBoxForPrintEmailPdf, "An error occurred queuing this send job.");
                                        messageBoxForPrintEmailPdf.PNL_BodyDisible = true;
                                        return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                    }
                                    //goUI.ExecuteSendPopup = true;
                                }
                                return Json("desktop", JsonRequestBehavior.AllowGet);

                            case "EXCEL":

                                //gw.sResult = "";
                                //string sFile = "";
                                string sViewName = "";
                                //sGUID = goHist.GetLastOpenDesktopGUID();

                                //string _desktopMetaData2 = goMeta.PageRead("GLOBAL", Util.GetSessionValue("DesktopId").ToString(), "", true);
                                //DesktopModel _desktopModel = new DesktopModel("GLOBAL", Util.GetSessionValue("DesktopId").ToString(), true);
                                //IList<View> _ViewsCollection = _desktopModel.Views;
                                //foreach (View _view in _ViewsCollection)
                                //{
                                //if (!_view.IsMasterView)
                                //{
                                //    _view.MasterSelID = Util.GetSessionValue("MasterViewSelId").ToString();
                                //}


                                if (sGUID == null | string.IsNullOrEmpty(sGUID))
                                {
                                    //goErr.SetError(35000, sProc, "The current desktop couldn't be found in history: goHist.GetLastOpenDesktopGUID returned ''.");
                                    //return;
                                }
                                else
                                {

                                    if (desktop == null)
                                    {
                                        //goErr.SetError(30012, sProc, , "oDesktop");
                                        //return;
                                    }
                                    else
                                    {
                                        string gsAutoLoadView = goTR.StrRead(_viewPrint.MetaData, "AUTOLOADVIEWDATA", "");
                                        bool gbDoNotAutoLoadMode;

                                        if (gsAutoLoadView == "0")
                                        {
                                            gbDoNotAutoLoadMode = true;
                                        }
                                        else
                                        {
                                            gbDoNotAutoLoadMode = false;
                                        }

                                        IList<View> _ViewCollection = desktop.Views;
                                        foreach (View _view in _ViewCollection)
                                        {
                                            _viewPrint.ViewName = _view.ViewTitle;        //Title for Excel..S1
                                            _viewPrint.ViewType = (_viewPrint.ViewType == null) ? "" : _viewPrint.ViewType;
                                            sFilename += SelectedViewToTextFile(_view.ViewId.Replace(" ", ""), _view.ViewType, gbDoNotAutoLoadMode, iFirstNRecords, SEL_VIEWOPTIONSSelectedIndex, iSplitLines, 800, Key, Target) + "|~{";
                                            sViewName += _view.ViewTitle + "|~{";
                                        }
                                        sFilename = sFilename.Substring(0, sFilename.Length - 3);
                                        sViewName = sViewName.Substring(0, sViewName.Length - 3);
                                        if (string.IsNullOrEmpty(sFilename))
                                        {
                                            MessageBox(messageBoxForPrintEmailPdf, "Either the desktop has no views or an error occurred sending the desktop to Excel.");
                                            messageBoxForPrintEmailPdf.PNL_BodyDisible = true;
                                            return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                        }
                                        else
                                        {
                                            //clArray aFiles = new clArray();
                                            //aFiles = (clArray)_ViewCollection;
                                            //for (int i = 1; i < _ViewCollection.Count; i++)
                                            //{
                                            //    sDiskFileName = aFiles.GetInfo(i);
                                            //    sFile += sDiskFileName + "|";
                                            //}
                                            // //Remove trailing delimiter
                                            if (Microsoft.VisualBasic.Strings.Right(sFilename, 3) == "|")
                                                sFilename = Microsoft.VisualBasic.Strings.Left(sFilename, Microsoft.VisualBasic.Strings.Len(sFilename) - 3);
                                            if (Microsoft.VisualBasic.Strings.Right(sViewName, 3) == "|")
                                                sViewName = Microsoft.VisualBasic.Strings.Left(sViewName, Microsoft.VisualBasic.Strings.Len(sViewName) - 3);

                                            //if (desktop.DateRangeSelectorEnabled)
                                            //{
                                            //    sRange = goTR.GetDateRangeLabel(oDesktop.DateRangeSelectorSelectedRange, oDesktop.StartDate, oDesktop.EndDate, 0, false);
                                            //    switch (gw.sDesktopName)
                                            //    {
                                            //        case "":
                                            //            gw.sDesktopName = sRange;
                                            //            break;
                                            //        default:
                                            //            if (!string.IsNullOrEmpty(sRange))
                                            //            {
                                            //                gw.sDesktopName = gw.sDesktopName + "_" + sRange;
                                            //            }
                                            //            else
                                            //            {
                                            //                gw.sDesktopName = gw.sDesktopName;
                                            //            }
                                            //            break;
                                            //    }
                                            //}

                                            string Result = "";
                                            goTR.StrWrite(ref Result, "US_NAME", "Send to Excel Queue Item");
                                            goTR.StrWrite(ref Result, "MODE", "EXCEL");
                                            goTR.StrWrite(ref Result, "OBJECTTOSEND", "Desktop");
                                            goTR.StrWrite(ref Result, "SENDNAME", "Desktop to Excel '" + goTR.StripIllegalChars(goTR.StrRead(_viewPrint.MetaData, "US_WCLABEL", "", true)) + "'");
                                            goTR.StrWrite(ref Result, "DESKTOPNAME", goTR.StripIllegalChars(_viewPrint.ViewName));
                                            goTR.StrWrite(ref Result, "FILE", sFilename);
                                            goTR.StrWrite(ref Result, "VIEWNAME", sViewName);
                                            sPageID = "SNQ_" + goData.GenSUID("XX");

                                            if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, Result))
                                            {
                                                MessageBox(messageBoxForPrintEmailPdf, "An error occurred queuing this send job.");
                                                return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                            }
                                            else
                                            {
                                                if (goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EXCELSENDSHOWSUCCESS", "1") == "1")
                                                {
                                                    //goUI.ExecuteSendPopup = true;
                                                    MessageBox(messageBoxForPrintEmailPdf, "The data was prepared sucessfully. If you have PC Link configured to 'Auto process', MS Excel will start shortly. If not, double-click the PC Link icon in the Tray and process the job from there.", "FinalOKMessage", "", clC.SELL_MB_YESNO, "  OK  ", "Don't show again");
                                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                                }
                                                else
                                                {
                                                    //goUI.ExecuteSendPopup = true;
                                                    //Close();
                                                    return Json("desktop", JsonRequestBehavior.AllowGet);
                                                }
                                            }
                                        }
                                    }
                                }

                                break;

                            default:
                                ////PRINT
                                return Json("", JsonRequestBehavior.AllowGet);
                                break;
                        }
                    }

                    break;
                //     CASE "RECORD"

                default:
                    //goErr.SetError(35000, sProc, "Unsupported value in gw.sMode: '" + gw.sMode + "'.");
                    break;
            }

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, 45105, sProc);
            //    }
            //}
            return Json("", JsonRequestBehavior.AllowGet);

        }

        private static string GetDTintoTableinHTML(View view, string siteName, int SEL_VIEWOPTIONSSelectedIndex, string Key = "", int iFirstNRecords = 10)
        {
            string EmailPageHtml = "";
            //add header row
            //EmailPageHtml += "<p style=\"text-align:center; font-size:20px;\"><u>" + view.ViewTitle + "</u></p>";
            EmailPageHtml += "<h2 style='margin-bottom: 0px;margin-top: 0px;border: 1px #bfbfbf solid;text-align: center;'>" + view.ViewTitle + "</h2>";
            EmailPageHtml += "<table style=\"border: 1px solid silver; max-width:100%;width: 100%;\" cellpadding=\"3\" cellspacing=\"0\" align=\"center\" valign=\"top\" align=\"center\">";
            SessionViewInfo _SessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + view.ViewId.Replace(" ", ""));
            //var gcSorts = _SessionViewInfo.Sorts;
            var gcColumns = _SessionViewInfo.Columns;
            var SortText = _SessionViewInfo.SortText;

            int htmlcolwidth = 0;
            foreach (var cw in gcColumns)
            {
                htmlcolwidth += cw.Width;
            }
            Util.SetSessionValue("htmlcolwidth", htmlcolwidth);

            //EmailPageHtml += "<caption style='background-color: #8c8c8c !important;color: #ffffff;border: 1px #bfbfbf solid;height: 25px;font-size: 20px;'><b>" + view.ViewTitle + "</b></caption>";
            //EmailPageHtml += "<caption style='border: 1px #bfbfbf solid;height: 25px;font-size: 20px;'><b>" + view.ViewTitle + "</b></caption>";

            DataTable dt = ReadDataTable(view.ViewId.Replace(" ", ""), Util.GetSessionValue("MasterViewSelId").ToString(), SortText, Key, SEL_VIEWOPTIONSSelectedIndex, iFirstNRecords);

            if (view.ViewType == "REPORT")
            {
                if (dt != null && dt.Rows.Count > 0)
                {
                    ReportController _Report = new ReportController();
                    dt = _Report.GetGroupedData(dt, _SessionViewInfo);

                    //Ids
                    string sGroupIds = "";
                    for (int idx = 0; idx < dt.Rows.Count; idx++)
                    {
                        sGroupIds += dt.Rows[idx]["id"].ToString() + "|" + idx.ToString() + ",";
                    }
                    sGroupIds = sGroupIds.Substring(0, sGroupIds.Length - 1);
                    string[] sGroupList = sGroupIds.Split(',');

                    //Parent Ids
                    string sGroupParentIds = "";
                    for (int idx = 0; idx < dt.Rows.Count; idx++)
                    {
                        sGroupParentIds += dt.Rows[idx]["parentId"].ToString() + "|" + idx.ToString() + ",";
                    }
                    sGroupParentIds = sGroupParentIds.Substring(0, sGroupParentIds.Length - 1);
                    string[] sGroupParentList = sGroupParentIds.Split(',');

                    //Compare Ids with Parent Ids
                    bool bTop = false;
                    for (int index = 0; index < sGroupList.Length; index++)
                    {
                        for (int parentIndex = 0; parentIndex < sGroupParentList.Length; parentIndex++)
                        {

                            if (sGroupList[index].Contains(dt.Rows[parentIndex]["parentId"].ToString()))
                            {
                                if (!string.IsNullOrEmpty(dt.Rows[parentIndex]["parentId"].ToString()))
                                {
                                    string[] sSingleRecord = sGroupList[index].Split('|');
                                    //int RowIndx = int.Parse(sSingleRecord[1]);

                                    DataRow[] rows = dt.Select("id = '" + sSingleRecord[0] + "'");
                                    int SelectedIndex = 0;
                                    if (rows.Length > 0)
                                    {
                                        SelectedIndex = dt.Rows.IndexOf(rows[0]);
                                    }
                                    DataRow selectedRow = dt.Rows[SelectedIndex];
                                    DataRow newRow = dt.NewRow();
                                    newRow.ItemArray = selectedRow.ItemArray;
                                    dt.Rows.Remove(selectedRow);

                                    //string[] sSingleParentRecord = sGroupParentList[parentIndex].Split('|');
                                    //int RowParentIndx = int.Parse(sSingleParentRecord[1]);

                                    dt.Rows.InsertAt(newRow, parentIndex);
                                    goto GoToNext;
                                }
                            }

                            if (!bTop)
                            {
                                string Id = dt.Rows[index]["id"].ToString();
                                string ParentId = dt.Rows[index]["parentId"].ToString();

                                if (!string.IsNullOrEmpty(Id) && string.IsNullOrEmpty(ParentId))
                                {
                                    string[] sSingleRecord = sGroupList[index].Split('|');
                                    int RowIndx = int.Parse(sSingleRecord[1]);

                                    DataRow selectedRow = dt.Rows[RowIndx - 1];
                                    DataRow newRow = dt.NewRow();
                                    newRow.ItemArray = selectedRow.ItemArray;
                                    dt.Rows.Remove(selectedRow);

                                    string[] sSingleParentRecord = sGroupParentList[parentIndex].Split('|');
                                    int RowParentIndx = int.Parse(sSingleParentRecord[1]);

                                    dt.Rows.InsertAt(newRow, 0);
                                    bTop = true;
                                }
                            }
                        }
                        GoToNext:
                        {

                        }
                    }
                }
            }

            if (dt.Rows.Count == 0)
            {
                EmailPageHtml += "<tr style='background-color: #E2E2E2;'><td>No records to display.</td></tr>";
                goto goTableEnd;
            }

            EmailPageHtml += "<thead><tr>";
            for (int i = 0; i < dt.Columns.Count; i++)
            {
                if (dt.Columns[i].ColumnName == gcColumns[i].Name.Replace(", ", ""))
                    EmailPageHtml += "<td><b>" + gcColumns[i].Title + "</b></td>";
                else
                    goto RowsHtml;
            }
            RowsHtml:
            EmailPageHtml += "</thead><tbody>";
            string fileUrl = "";
            //if (string.IsNullOrEmpty(siteName))
            //{
            //    string sHostingEnvironment = ConfigurationManager.AppSettings["HostingEnvironment"].ToString().ToLower();
            //    if (sHostingEnvironment == "debugging")
            //    {
            //        fileUrl = "/Content/themes/Selltis/images/";
            //    }
            //    else
            //    {
            //        string sSiteId = ((DataTable)Util.GetSessionValue("SiteSettings")).Rows[0]["SiteId"].ToString();
            //        fileUrl = "/PublicFiles/" + sSiteId + "/Images/";
            //        fileUrl = System.Web.HttpContext.Current.Server.MapPath(fileUrl);
            //    }
            //}
            //else
            //{
            fileUrl = siteName + "/Content/themes/Selltis/images/";
            //}
            string tempStr = "";
            //add rows
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                string cusStyleVal = "<tr>";
                if ((i + 1) % 2 == 0)
                    cusStyleVal = "<tr style='background-color: #E2E2E2;' >";

                EmailPageHtml += cusStyleVal;
                for (int j = 0; j < dt.Columns.Count; j++)
                {
                    if (dt.Columns[j].ColumnName == gcColumns[j].Name.Replace(", ", ""))
                    {
                        if (dt.Columns[j].ColumnName.Contains("ADV") || dt.Columns[j].ColumnName.Contains("ADR") || dt.Columns[j].ColumnName.Contains("FIL"))
                        {
                            string AttData = dt.Rows[i][j].ToString();
                            if (!string.IsNullOrEmpty(AttData))
                            {
                                string[] sAttList = Regex.Split(AttData, "<BR>");
                                AttData = "";
                                foreach (string sItem in sAttList)
                                {
                                    int startIndx = sItem.IndexOf("'>") + 2;
                                    int lastIndx = sItem.Length - startIndx - 4;
                                    AttData += sItem.Substring(startIndx, lastIndx) + "<BR>";
                                }
                                AttData = AttData.Substring(0, AttData.Length - 4);
                                EmailPageHtml += "<td>" + AttData + "</td>";
                            }
                            else
                            {
                                EmailPageHtml += "<td><span>&nbsp;</span></td>";
                            }
                        }
                        else if (dt.Rows[i][j].ToString().Contains("gif"))
                        {
                            EmailPageHtml += "<td><img src='" + fileUrl + dt.Rows[i][j].ToString() + "'/></td>";
                        }
                        //else if (dt.Rows[i][j].ToString().Contains("..."))
                        //{
                        //    tempStr = dt.Rows[i][j].ToString();
                        //    tempStr = tempStr.Substring(0, tempStr.IndexOf("...") + 2);
                        //    EmailPageHtml += "<td>" + tempStr + "</td>";
                        //}
                        else if (dt.Rows[i][j].ToString().Contains("#$%^&*"))
                        {
                            tempStr = dt.Rows[i][j].ToString();
                            tempStr = tempStr.Substring(0, tempStr.IndexOf("#$%^&*"));
                            EmailPageHtml += "<td>" + tempStr + "</td>";
                        }
                        else if (dt.Rows[i][j].ToString() == "OpenLink")
                        {
                            EmailPageHtml += "<td><span>&nbsp;</span></td>";
                        }
                        else if (dt.Rows[i][j].ToString().Length > 300)
                        {
                            tempStr = dt.Rows[i][j].ToString();
                            tempStr = tempStr.Substring(0, 300);
                            EmailPageHtml += "<td>" + tempStr + "...</td>";
                        }
                        else if (dt.Rows[i][j].ToString().Contains("==") || dt.Rows[i][j].ToString().Contains("\r") || dt.Rows[i][j].ToString().Contains("\n") || dt.Rows[i][j].ToString().Contains("<br>") || dt.Rows[i][j].ToString().Contains("<br />") || dt.Rows[i][j].ToString().Contains("<p>") || dt.Rows[i][j].ToString().Contains("</p>"))
                        {
                            tempStr = dt.Rows[i][j].ToString();
                            tempStr = tempStr.Replace("==", " ").Replace("\r", " ").Replace("\n", " ").Replace("<br>", " ").Replace("<br />", " ").Replace("<p>", " ").Replace("</p>", " ");
                            EmailPageHtml += "<td>" + dt.Rows[i][j].ToString() + "</td>";
                        }
                        else if (dt.Rows[i][j] == null || dt.Rows[i][j].ToString() == "")
                        {
                            EmailPageHtml += "<td><span>&nbsp;</span></td>";
                        }

                        else
                        {
                            EmailPageHtml += "<td>" + dt.Rows[i][j].ToString() + "</td>";
                        }
                    }
                    else if (gcColumns[j].Name.Replace(", ", "") != "GID_ID")
                    {
                        if (dt.Rows[i][j] == null || dt.Rows[i][j].ToString() == "")
                            EmailPageHtml += "<td><span>&nbsp;</span></td>";
                        else
                            EmailPageHtml += "<td>" + dt.Rows[i][j].ToString() + "</td>";
                    }
                    else
                        goto EndTableHtml;
                }
                EndTableHtml:
                EmailPageHtml += "</tr>";
            }

            goTableEnd:
            EmailPageHtml += "</tbody></table><br/>";

            //string rAnchor = @"<a [^>]+>(.*?)<\/a>";
            //EmailPageHtml = Regex.Replace(EmailPageHtml, rAnchor, "$1");
            EmailPageHtml = EmailPageHtml.Replace("#$%^&*", "");
            return EmailPageHtml;
        }

        private void MessageBox(MessageBoxForPrintEmailPdf messageBoxForPrintEmailPdf, string par_sMessage, string par_sPurpose = "", string par_sTitle = "Selltis", int par_iType = clC.SELL_MB_OK, string par_sButton1Label = "", string par_sButton2Label = "", string par_sButton3Label = "")
        {

            //PURPOSE:
            //       Displays a message box that looks similar to a standard windows
            //       "modal" MsgBox. Icon is not supported. The first button is set in focus.
            //       To override button in focus, code .Focus for the desired button after
            //       the MessageBox() call, for example write: Me.BTN_MsgBox2.Focus().
            //NOTES:
            //       Initially this was implemented as a panel positioned in the middle
            //       of the screen. This technique was abandoned for two reasons:
            //       - Listbox and combo controls were showing through. This appears to
            //           be a universal problem because the z order of listbox controls is
            //           "infinity".
            //       - The box was not modal. A modal solution exists (MS Atlas), but 
            //           is not universally browser-compatible.
            //       It was decided to use a static yellow panel in the top area of the window.
            //PARAMETERS:
            //       par_sMessage: Message to display.
            //       par_sPurpose: This is the value of gw.sMessageBoxPurpose variable,
            //           based on which each message box button's event runs particular 
            //           select case code.
            //       par_sTitle: Title to display in the "title bar".
            //       par_iType: clC.SELL_MB_YESNO, clC.SELL_MB_YESNOCANCEL, clC.SELL_MB_OK
            //       par_sButton1Label: Label of button 1 (yes or OK button)
            //       par_sButton2Label: Label of button 2 (no or cancel button)
            //       par_sButton3Label: Label of button 3 (Cancel button)

            //string sProc = gw.fenenexecution + "::MessageBox";


            //try
            //{
            string sMessage = par_sMessage;
            string sTitle = par_sTitle;
            //gw.sMessageBoxPurpose = par_sPurpose;
            sMessage = HttpUtility.HtmlEncode(sMessage);
            sMessage = goTR.Replace(sMessage, Microsoft.VisualBasic.Constants.vbCrLf, "<BR>");
            sMessage = goTR.Replace(sMessage, Microsoft.VisualBasic.Constants.vbTab, "&nbsp;&nbsp;&nbsp;&nbsp;");
            sTitle = HttpUtility.HtmlEncode(sTitle);

            messageBoxForPrintEmailPdf.LBL_MsgBoxMessageText = sMessage;
            messageBoxForPrintEmailPdf.LBL_MsgBoxTitleText = sTitle;

            switch (par_iType)
            {
                case clC.SELL_MB_YESNO:
                    if (string.IsNullOrEmpty(par_sButton1Label))
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox1Text = "  Yes  ";
                        //MessTranslate
                    }
                    else
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox1Text = par_sButton1Label;
                        //HttpUtility.HtmlEncode(par_sButton1Label)
                    }
                    if (string.IsNullOrEmpty(par_sButton2Label))
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox2Text = "  No ";
                        //MessTranslate
                    }
                    else
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox2Text = par_sButton2Label;
                        //HttpUtility.HtmlEncode(par_sButton2Label)
                    }
                    messageBoxForPrintEmailPdf.BTN_MsgBox1Visible = true;
                    messageBoxForPrintEmailPdf.BTN_MsgBox2Visible = true;
                    messageBoxForPrintEmailPdf.BTN_MsgBox3Visible = false;
                    //this.BTN_MsgBox1Focus();
                    break;
                case clC.SELL_MB_YESNOCANCEL:
                    if (string.IsNullOrEmpty(par_sButton1Label))
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox1Text = "  Yes  ";
                        //MessTranslate
                    }
                    else
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox1Text = par_sButton1Label;
                        //HttpUtility.HtmlEncode(par_sButton1Label)
                    }
                    if (string.IsNullOrEmpty(par_sButton2Label))
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox2Text = "  No  ";
                        //MessTranslate
                    }
                    else
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox2Text = par_sButton2Label;
                        // HttpUtility.HtmlEncode(par_sButton2Label)
                    }
                    if (string.IsNullOrEmpty(par_sButton3Label))
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox3Text = "Cancel";
                        //MessTranslate
                    }
                    else
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox3Text = par_sButton3Label;
                        //HttpUtility.HtmlEncode(par_sButton3Label)
                    }
                    messageBoxForPrintEmailPdf.BTN_MsgBox1Visible = true;
                    messageBoxForPrintEmailPdf.BTN_MsgBox2Visible = true;
                    messageBoxForPrintEmailPdf.BTN_MsgBox3Visible = true;
                    //this.BTN_MsgBox1.Focus();
                    break;
                case clC.SELL_MB_OK:
                    if (string.IsNullOrEmpty(par_sButton1Label))
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox1Text = "  OK  ";
                        //MessTranslate
                    }
                    else
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox1Text = par_sButton1Label;
                        //HttpUtility.HtmlEncode(par_sButton1Label)
                    }
                    messageBoxForPrintEmailPdf.BTN_MsgBox1Visible = true;
                    messageBoxForPrintEmailPdf.BTN_MsgBox2Visible = false;
                    messageBoxForPrintEmailPdf.BTN_MsgBox3Visible = false;
                    //this.BTN_MsgBox1.Focus();
                    break;
                default:
                    //clC.SELL_MB_YESNO
                    if (string.IsNullOrEmpty(par_sButton1Label))
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox1Text = "  Yes  ";
                        //MessTranslate
                    }
                    else
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox1Text = par_sButton1Label;
                        //HttpUtility.HtmlEncode(par_sButton1Label)
                    }
                    if (string.IsNullOrEmpty(par_sButton2Label))
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox2Text = "  No  ";
                        //MessTranslate
                    }
                    else
                    {
                        messageBoxForPrintEmailPdf.BTN_MsgBox2Text = par_sButton2Label;
                        //HttpUtility.HtmlEncode(par_sButton2Label)
                    }
                    messageBoxForPrintEmailPdf.BTN_MsgBox1Visible = true;
                    messageBoxForPrintEmailPdf.BTN_MsgBox2Visible = true;
                    messageBoxForPrintEmailPdf.BTN_MsgBox3Visible = false;
                    //this.BTN_MsgBox1.Focus();
                    break;
            }
            messageBoxForPrintEmailPdf.PNL_MessageBoxVisible = true;
            //ms-help:'MS.VSCC.v80/MS.MSDN.v80/MS.WEBDEV.v10.en/dhtml/workshop/author/dhtml/overview/recalc.htm
            //Use this line to position the panel. DISPLAY: block is a way of making the panel visible
            //Me.PNL_MessageBox.Attributes.Add("style", "DISPLAY: block; Z-INDEX: 102; LEFT: 200px; POSITION: absolute; TOP: 200px")

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, 45105, sProc);
            //    }
            //}

        }

        private string SelectedViewToHTML(ref string sViewHtml, string ViewTitle, bool par_bAsString = false, string par_sMessage = "")
        {

            System.IO.StringWriter SW = new System.IO.StringWriter();
            string CSS = "";
            switch (par_bAsString)
            {

                case false:
                    //let's test for directory
                    if (System.IO.Directory.Exists(System.Web.HttpContext.Current.Server.MapPath("~\\Temp\\")))
                    {
                        //do nothing
                    }
                    else
                    {
                        try
                        {
                            //try to create directory
                            System.IO.Directory.CreateDirectory(System.Web.HttpContext.Current.Server.MapPath("..") + "\\Temp");
                        }
                        catch (Exception ex)
                        {
                            //goErr.SetError(ex, 45105, sProc);
                            return "";
                        }
                    }

                    string sPath = System.Web.Hosting.HostingEnvironment.MapPath("~/Temp/");
                    int par_iValid = 4;
                    string par_sDelim = "";
                    string sFilename = goTR.StripIllegalChars(ViewTitle, "REMOVE") + "_" + goTR.StripIllegalChars(goTR.DateTimeToSysString(System.DateTime.Now, ref par_iValid, ref par_sDelim), "REMOVE") + "_" + goP.GetUserCode() + ".html";
                    if (System.IO.File.Exists(sPath + sFilename))
                        System.IO.File.Delete(sPath + sFilename);
                    System.IO.StreamWriter oStream = default(System.IO.StreamWriter);
                    oStream = System.IO.File.AppendText(sPath + sFilename);

                    System.IO.DirectoryInfo oDr = new System.IO.DirectoryInfo(System.Web.Hosting.HostingEnvironment.MapPath("~/App_Themes/SellSQL/"));
                    foreach (var oFile in oDr.GetFiles("*.css"))
                    {
                        System.IO.StreamReader SR = default(System.IO.StreamReader);
                        SR = new System.IO.StreamReader(oFile.FullName);
                        CSS = CSS + SR.ReadToEnd();
                        SR.Close();
                    }

                    oStream.WriteLine("<html><style>" + Microsoft.VisualBasic.Constants.vbCrLf + CSS + Microsoft.VisualBasic.Constants.vbCrLf + "</style><body>" + Microsoft.VisualBasic.Constants.vbCrLf);

                    //message
                    if (!string.IsNullOrEmpty(par_sMessage))
                        oStream.WriteLine("<span style=\"padding: 6px 2px 6px 2px; font-family: Verdana; font-size: 10pt;\">" + par_sMessage + "</span><br /><br />");

                    oStream.WriteLine("<span style=\"padding: 6px 2px 6px 2px; font-family: Tahoma; font-weight: bold; font-size: medium;\">" + ViewTitle + "</span><br />" + Microsoft.VisualBasic.Constants.vbCrLf + sViewHtml + Microsoft.VisualBasic.Constants.vbCrLf + "</body></html>");
                    oStream.Close();

                    StringBuilder sbViewHtml = new StringBuilder();
                    sbViewHtml = sbViewHtml.Append("<html><style>" + Microsoft.VisualBasic.Constants.vbCrLf + CSS + Microsoft.VisualBasic.Constants.vbCrLf + "</style><body>" + Microsoft.VisualBasic.Constants.vbCrLf);
                    sbViewHtml = sbViewHtml.Append("<span style=\"padding: 6px 2px 6px 2px; font-family: Tahoma; font-weight: bold; font-size: medium;\">" + ViewTitle + "</span><br />" + Microsoft.VisualBasic.Constants.vbCrLf + sViewHtml + Microsoft.VisualBasic.Constants.vbCrLf + "</body></html>");
                    sViewHtml = sbViewHtml.ToString();

                    return sPath + sFilename;
                default:
                    // True

                    System.IO.DirectoryInfo Dr = new System.IO.DirectoryInfo(System.Web.Hosting.HostingEnvironment.MapPath("~/App_Themes/SellSQL/"));
                    foreach (var oFile in Dr.GetFiles("*.css"))
                    {
                        System.IO.StreamReader SR = default(System.IO.StreamReader);
                        SR = new System.IO.StreamReader(oFile.FullName);
                        CSS = SR.ReadToEnd();
                        SR.Close();
                    }

                    string sHTML = "<html><style>" + Microsoft.VisualBasic.Constants.vbCrLf + CSS + Microsoft.VisualBasic.Constants.vbCrLf + "</style><body>";

                    //message
                    if (!string.IsNullOrEmpty(par_sMessage))
                        sHTML = sHTML + "<span style=\"padding: 6px 2px 6px 2px; font-family: Verdana; font-size: 10pt;\">" + par_sMessage + "</span><br /><br />";

                    sHTML = sHTML + "<span style=\"padding: 6px 2px 6px 2px; font-family: Tahoma; font-weight: bold; font-size: medium;\">" + ViewTitle + "</span><br />" + Microsoft.VisualBasic.Constants.vbCrLf + sViewHtml + Microsoft.VisualBasic.Constants.vbCrLf + "</body></html>";

                    return sHTML;

            }

        }


        public string SelectedViewToTextFile(string ViewKey, string ViewType, bool gbDoNotAutoLoadMode, int iFirstNRecords, int SEL_VIEWOPTIONSSelectedIndex, int par_iAddressColumns = 1, int par_iMaxCharsPerCell = 200, string Key = "", string Target = "")
        {

            int i = 0;
            int j = 0;
            int k = 0;

            string TopRecordId = string.Empty;
            SessionViewInfo _SessionViewInfo = new SessionViewInfo();
            string FilterText = string.Empty;
            string MasterSelID;

            if (Util.GetSessionValue(Key + "_" + ViewKey) != null)
            {
                _SessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + ViewKey);
            }
            string SortText = _SessionViewInfo.SortText;

            if (_SessionViewInfo.IsMasterView == true)
                MasterSelID = "";
            else
                MasterSelID = Util.GetSessionValue("MasterViewSelId").ToString();

            DataTable goDataTable = ReadDataTable(ViewKey, MasterSelID, SortText, Key, SEL_VIEWOPTIONSSelectedIndex, iFirstNRecords, Target);


            //goDataTable.Columns.Remove("GID_ID");

            int iColumnCount;
            if (Target == "EXCEL" && (_SessionViewInfo.ViewType == "CHART" || _SessionViewInfo.ViewType.Contains("CAL")))
            {
                iColumnCount = 1;
            }
            else
            {
                //goDataTable.Columns.Remove("GID_ID");
                //info counters
                iColumnCount = (goDataTable.Columns.Count - (1 + _SessionViewInfo.Sorts.Count)) - 1;
            }
            //remove last column as it is a gid_id column for other purposes, and number of columns used for sorting/grouping
            int iRowcount = goDataTable.Rows.Count;
            int iCharCount = par_iMaxCharsPerCell;
            if (iCharCount > 800)
                iCharCount = 800;
            if (iCharCount < 0)
                iCharCount = 0;

            //let's test for directory
            if (System.IO.Directory.Exists(System.Web.HttpContext.Current.Server.MapPath("~\\Temp\\")))
            {
                //do nothing
            }
            else
            {
                try
                {
                    //try to create directory
                    System.IO.Directory.CreateDirectory(System.Web.HttpContext.Current.Server.MapPath("..") + "\\Temp");
                }
                catch (Exception ex)
                {
                    //goErr.SetError(ex, 45105, sProc);
                    return "";
                }
            }

            string sPath = System.Web.HttpContext.Current.Server.MapPath("~/Temp/");
            string sFilename = "XLS_" + goP.GetUserCode() + "_" + goData.GenGuid() + ".txt";
            string sHeader = "";
            string sText = "";
            string sField = "";
            string sFirstChar = "";
            //string sColDef = "";
            string sFieldname = "";

            System.IO.StreamWriter oStream = default(System.IO.StreamWriter);
            oStream = System.IO.File.AppendText(sPath + sFilename);

            //oStream.WriteLine("\xEF\xBB\xBF")

            //excel doesn't accept more than 256 columns in a spreadsheet
            if (iColumnCount > 256)
            {
                //use wa messaging
                //goUI.NewWorkareaMessage("Microsoft Excel does not support spreadsheets with more than 256 columns.");
                return "";
            }

            //excel doesn't support more than 65536 rows
            if (iRowcount > 65535)
            {
                //use wa messaging
                //goUI.NewWorkareaMessage("Microsoft Excel does not support spreadsheets with more than 65535 rows.");
                return "";
            }


            if (!gbDoNotAutoLoadMode)
            {
                switch (ViewType)
                {
                    case "CALDAY":
                    case "CALWEEK":
                    case "CALMONTH":

                        iColumnCount = 1;
                        List<CalendarInfo> CalInfo = (List<CalendarInfo>)Util.GetSessionValue("CalendarPrint_DATA_" + Key + "_" + ViewKey);

                        sText = "Appointments" + Microsoft.VisualBasic.Strings.Chr(13).ToString();
                        foreach (var calendar in CalInfo)
                        {
                            sText += calendar.EventName + Microsoft.VisualBasic.Strings.Chr(13).ToString();
                        }

                        oStream.WriteLine(sText);
                        //oStream.WriteLine(goData.GetFileLabelPlurFrName(_SessionViewInfo.TableName));

                        break;
                    default:
                        if (Target == "EXCEL" && _SessionViewInfo.ViewType == "CHART")
                        {
                            sFieldname = goDataTable.Columns[0].ToString();
                            sText = sFieldname;
                        }
                        else
                        {
                            //get column headers and add to text string
                            for (i = 0; i < _SessionViewInfo.Columns.Count - 1; i++)        // subtracting columnn count to elemenate the "GID_ID"
                            {
                                //sColDef = _SessionViewInfo.Columns[i].ToString();
                                //sFieldname = goTR.ExtractString(sColDef, 1, Microsoft.VisualBasic.Strings.Chr(1).ToString());
                                //sHeader = goTR.ExtractString(sColDef, 2, Microsoft.VisualBasic.Strings.Chr(1).ToString());

                                sFieldname = _SessionViewInfo.Columns[i].Name;
                                sHeader = _SessionViewInfo.Columns[i].Title;

                                if (par_iAddressColumns > 1)
                                {
                                    if ((Microsoft.VisualBasic.Strings.Left(sFieldname, 8)).ToUpper() == "TXT_ADDR")
                                    {
                                        string sNewHeader = "";
                                        for (k = 1; k <= par_iAddressColumns; k++)
                                        {
                                            if (k > 1)
                                            {
                                                sNewHeader = sNewHeader + Microsoft.VisualBasic.Strings.Chr(9).ToString() + sHeader + " " + k.ToString();
                                            }
                                            else
                                            {
                                                sNewHeader = sHeader;
                                            }
                                        }
                                        if (i == _SessionViewInfo.Columns.Count)
                                        {
                                            sText = sText + sNewHeader;
                                        }
                                        else
                                        {
                                            sText = sText + sNewHeader + Microsoft.VisualBasic.Strings.Chr(9).ToString();
                                        }

                                    }
                                    else
                                    {
                                        if (i == _SessionViewInfo.Columns.Count)
                                        {
                                            sText = sText + sHeader;
                                        }
                                        else
                                        {
                                            sText = sText + sHeader + Microsoft.VisualBasic.Strings.Chr(9).ToString();
                                        }
                                    }
                                }
                                else
                                {
                                    if (i == _SessionViewInfo.Columns.Count)
                                    {
                                        sText = sText + sHeader;
                                    }
                                    else
                                    {
                                        sText = sText + sHeader + Microsoft.VisualBasic.Strings.Chr(9).ToString();
                                    }
                                }
                            }
                        }


                        switch (Microsoft.VisualBasic.Strings.Left(sText, 2))
                        {
                            case "ID":
                                sText = "'" + sText;
                                break;
                            default:
                                sText = sText;
                                break;
                        }

                        oStream.WriteLine(sText);

                        break;
                }


                for (i = 0; i <= iRowcount - 1; i++)
                {
                    sText = "";
                    DataRow oDataRow = goDataTable.Rows[i];

                    if (object.ReferenceEquals(oDataRow[0], System.DBNull.Value) == false)
                    {
                        switch (oDataRow[0].ToString())
                        {
                            case "DISPLAYMAKESELECTION":
                                sText = "(Make selection)";
                                oStream.WriteLine(sText);
                                goto GetNextDataRow;
                                break;
                            case "DISPLAYNONE":
                                sText = "(None)";
                                oStream.WriteLine(sText);
                                goto GetNextDataRow;
                                break;
                            case "DISPLAYANY":
                                sText = "(Unlink views)";
                                oStream.WriteLine(sText);
                                goto GetNextDataRow;
                                break;
                            case "ANYNONEOFF":
                                goto GetNextDataRow;
                                break;
                            default:

                                break;
                        }
                    }

                    for (j = 0; j <= iColumnCount - 1; j++)
                    {
                        //if (object.ReferenceEquals(oDataRow[j + _SessionViewInfo.Sorts.Count], System.DBNull.Value) == true)
                        //{
                        //    sField = "";
                        //}
                        //else
                        //{
                        //    sField = oDataRow[j + _SessionViewInfo.Sorts.Count].ToString();
                        //}
                        if (object.ReferenceEquals(oDataRow[j], System.DBNull.Value) == true)
                        {
                            sField = "";
                        }
                        else
                        {
                            sField = oDataRow[j].ToString();
                        }
                        if (par_iAddressColumns > 1)
                        {
                            string sTemp = "";
                            //sColDef = _SessionViewInfo.Columns[j + 1].ToString();
                            //sFieldname = goTR.ExtractString(sColDef, 1, Microsoft.VisualBasic.Strings.Chr(1).ToString());
                            sFieldname = _SessionViewInfo.Columns[j + 1].Name;

                            if ((Microsoft.VisualBasic.Strings.Left(sFieldname, 8)).ToUpper() == "TXT_ADDR")
                            {
                                int iAddressHeight = Microsoft.VisualBasic.Strings.Split(sField, Microsoft.VisualBasic.Constants.vbCrLf).Length;
                                for (k = 1; k <= iAddressHeight; k++)
                                {
                                    string sAddr = null;
                                    sAddr = goTR.ExtractString(sField, k, Microsoft.VisualBasic.Constants.vbCrLf);
                                    if (sAddr == clC.EOT.ToString())
                                        sAddr = "";
                                    sField = goTR.Replace(sField, Microsoft.VisualBasic.Strings.Chr(9).ToString(), "     ");
                                    if (Microsoft.VisualBasic.Strings.Len(sAddr) > iCharCount)
                                    {
                                        //Excel allows 32,767 characters in a cell, only 1024 visible on the screen or printout
                                        //Limitations of the Replace function = will only handle a string up to approximately 850 characters
                                        //sAddr = goTR.Replace(sAddr, Chr(9), "     ")
                                        sAddr = Microsoft.VisualBasic.Strings.Left(sAddr, iCharCount) + " <<more>>";
                                    }
                                    //escape first character is string could be construed as possible excel function
                                    sFirstChar = Microsoft.VisualBasic.Strings.Left(sAddr, 1);
                                    switch (sFirstChar)
                                    {
                                        case "*":
                                        case "+":
                                        case "-":
                                        case "&":
                                        case "#":
                                        case "'":
                                        case "!":
                                        case "=":
                                        case "/":
                                        case "%":
                                        case "@":
                                            sAddr = "'" + sAddr;
                                            break;
                                        default:
                                            sAddr = sAddr;
                                            break;
                                    }
                                    //This problem occurs when the file that you’re trying to open has uppercase letters “I” and “D” as the first two characters.
                                    switch (Microsoft.VisualBasic.Strings.Left(sField, 2))
                                    {
                                        case "ID":
                                            sAddr = "'" + sAddr;
                                            break;
                                        default:
                                            sAddr = sAddr;
                                            break;
                                    }

                                    //replace &nbsp; characters - they do not render correctly in excel
                                    sAddr = goTR.Replace(sAddr, Microsoft.VisualBasic.Strings.Chr(160).ToString(), " ");

                                    //Excel breaks records on hard returns (and tabs due to the method we are using) so lets replace in data
                                    sAddr = goTR.Replace(sAddr, Microsoft.VisualBasic.Constants.vbCrLf, "<CR>");
                                    sAddr = goTR.Replace(sAddr, Microsoft.VisualBasic.Strings.Chr(10).ToString(), "<CR>");
                                    sAddr = goTR.Replace(sAddr, Microsoft.VisualBasic.Strings.Chr(13).ToString(), "<CR>");
                                    if (k > par_iAddressColumns)
                                    {
                                        sTemp = sTemp + "<CR>" + sAddr;
                                    }
                                    else if (k == 1)
                                    {
                                        sTemp = sAddr;
                                    }
                                    else
                                    {
                                        sTemp = sTemp + sAddr;
                                    }
                                }
                                for (k = iAddressHeight + 1; k <= par_iAddressColumns; k++)
                                {
                                    sTemp = sTemp + "";
                                }
                                sField = sTemp;
                            }
                            else
                            {
                                sField = goTR.Replace(sField, Microsoft.VisualBasic.Strings.Chr(9).ToString(), "     ");

                                if (sField == "#$%^&*")
                                {
                                    sField = "";
                                }
                                else if (Microsoft.VisualBasic.Strings.InStr(sField, "#$%^&*") > 0)
                                {
                                    sField = Microsoft.VisualBasic.Strings.Split(sField, "#$%^&*")[0];
                                }
                                else
                                {
                                    //no change
                                }

                                if (Microsoft.VisualBasic.Strings.Len(sField) > iCharCount)
                                {
                                    //Excel allows 32,767 characters in a cell, only 1024 visible on the screen or printout
                                    //Limitations of the Replace function = will only handle a string up to approximately 850 characters
                                    //sField = goTR.Replace(sField, Chr(9), "     ")
                                    sField = Microsoft.VisualBasic.Strings.Left(sField, iCharCount) + " <<more>>";
                                }
                                //escape first character is string could be construed as possible excel function
                                sFirstChar = Microsoft.VisualBasic.Strings.Left(sField, 1);
                                switch (sFirstChar)
                                {
                                    case "*":
                                    case "+":
                                    case "-":
                                    case "&":
                                    case "#":
                                    case "'":
                                    case "!":
                                    case "=":
                                    case "/":
                                    case "%":
                                    case "@":
                                        sField = "'" + sField;
                                        break;
                                    default:
                                        sField = sField;
                                        break;
                                }
                                //This problem occurs when the file that you’re trying to open has uppercase letters “I” and “D” as the first two characters.
                                switch (Microsoft.VisualBasic.Strings.Left(sField, 2))
                                {
                                    case "ID":
                                        sField = "'" + sField;
                                        break;
                                    default:
                                        sField = sField;
                                        break;
                                }

                                //replace &nbsp; characters - they do not render correctly in excel
                                sField = goTR.Replace(sField, Microsoft.VisualBasic.Strings.Chr(160).ToString(), " ");

                                //Excel breaks records on hard returns (and tabs due to the method we are using) so lets replace in data
                                sField = goTR.Replace(sField, Microsoft.VisualBasic.Constants.vbCrLf, "<CR>");
                                sField = goTR.Replace(sField, Microsoft.VisualBasic.Strings.Chr(10).ToString(), "<CR>");
                                sField = goTR.Replace(sField, Microsoft.VisualBasic.Strings.Chr(13).ToString(), "<CR>");
                            }
                        }
                        else
                        {
                            sField = goTR.Replace(sField, Microsoft.VisualBasic.Strings.Chr(9).ToString(), "     ");

                            if (sField == "#$%^&*")
                            {
                                sField = "";
                            }
                            else if (Microsoft.VisualBasic.Strings.InStr(sField, "#$%^&*") > 0)
                            {
                                sField = Microsoft.VisualBasic.Strings.Split(sField, "#$%^&*")[0];
                            }
                            else
                            {
                                //no change
                            }

                            if (Microsoft.VisualBasic.Strings.Len(sField) > iCharCount)
                            {
                                //Excel allows 32,767 characters in a cell, only 1024 visible on the screen or printout
                                //Limitations of the Replace function = will only handle a string up to approximately 850 characters
                                //sField = goTR.Replace(sField, Chr(9), "     ")
                                sField = Microsoft.VisualBasic.Strings.Left(sField, iCharCount) + " <<more>>";
                            }
                            //escape first character is string could be construed as possible excel function
                            sFirstChar = Microsoft.VisualBasic.Strings.Left(sField, 1);
                            switch (sFirstChar)
                            {
                                case "*":
                                case "+":
                                case "-":
                                case "&":
                                case "#":
                                case "'":
                                case "!":
                                case "=":
                                case "/":
                                case "%":
                                case "@":
                                    sField = "'" + sField;
                                    break;
                                default:
                                    sField = sField;
                                    break;
                            }
                            //This problem occurs when the file that you’re trying to open has uppercase letters “I” and “D” as the first two characters.
                            switch (Microsoft.VisualBasic.Strings.Left(sField, 2))
                            {
                                case "ID":
                                    sField = "'" + sField;
                                    break;
                                default:
                                    sField = sField;
                                    break;
                            }

                            //replace &nbsp; characters - they do not render correctly in excel
                            sField = goTR.Replace(sField, Microsoft.VisualBasic.Strings.Chr(160).ToString(), " ");

                            //Excel breaks records on hard returns (and tabs due to the method we are using) so lets replace in data
                            sField = goTR.Replace(sField, Microsoft.VisualBasic.Constants.vbCrLf, "<CR>");
                            sField = goTR.Replace(sField, Microsoft.VisualBasic.Strings.Chr(10).ToString(), "<CR>");
                            sField = goTR.Replace(sField, Microsoft.VisualBasic.Strings.Chr(13).ToString(), "<CR>");
                        }
                        //concatenate
                        if (j == iColumnCount - 1)
                        {
                            sText = sText + sField;
                        }
                        else
                        {
                            sText = sText + sField + Microsoft.VisualBasic.Strings.Chr(9);
                        }
                    }
                    oStream.WriteLine(sText);
                    GetNextDataRow:
                    continue;

                }

            }

            oStream.Close();
            return sFilename;
        }
        public CalendarInfo GetCalendarModel(View _view)
        {
            goTR = (clTransform)Util.GetInstance("tr");
            CalendarInfo _clview = new CalendarInfo();
            string _POPData = Util.GetSessionValue("PersonalOptions").ToString();

            string _FIRSTDAYOFWEEK = goTR.StrRead(_POPData, "FIRSTDAYOFWEEK", "0", false);

            if (_FIRSTDAYOFWEEK == "1")
            {
                _clview.FIRSTDAYOFWEEK = 1;
                _clview.LASTDAYOFWEEK = 7;
            }
            else
            {
                _clview.FIRSTDAYOFWEEK = 0;
                _clview.LASTDAYOFWEEK = 6;
            }
            _clview.MasterSelID = _view.MasterSelID;
            _clview.ViewKey = _view.ViewKey;
            _clview.TableName = _view.TableName;
            _clview.ViewRecordOpen = _view.ViewRecordOpen;
            _clview.IsActive = true;
            _clview.IsMasterView = _view.IsMasterView;
            _clview.IsTabView = _view.IsTabView;
            _clview.OrderIndex = _view.OrderIndex;
            _clview.Key = _view.Key;

            if (_view.ViewType.ToLower() == "calweek")
            {
                _clview.ViewType = "workWeek";
            }
            else if (_view.ViewType.ToLower() == "calday")
            {
                _clview.ViewType = "day";
            }
            else if (_view.ViewType.ToLower() == "calmonth")
            {
                _clview.ViewType = "month";
            }

            SessionViewInfo _SessionViewInfo = new SessionViewInfo();
            string ViewKey = _view.ViewKey.Replace(" ", "");

            if (Util.GetSessionValue(_view.Key + "_" + ViewKey) != null)
            {
                _SessionViewInfo = Util.SessionViewInfo(_view.Key + "_" + ViewKey);
            }

            string _viewMetaData = _SessionViewInfo.ViewMetaData;
            string currentDate = goTR.StrRead(_viewMetaData, "CALDATE");

            if (!string.IsNullOrEmpty(currentDate))
            {
                _clview.CurrentDate = currentDate;
            }
            else
            {
                _clview.CurrentDate = DateTime.Now.ToString("yyyy/MM/dd");
            }
            return _clview;
        }

        //SB Mapping view data into html markup..
        public string GetDataIntoHTML(Desktop desktop, ViewPrint _viewPrint, int iFirstNRecords, string siteName, string Key, int SEL_VIEWOPTIONSSelectedIndex)
        {
            //string EmailPageHtml = "<html><head><style> td {border: 1px #bfbfbf solid;}thead {color: #272727;}tbody{background-color:#f2f2f2;} tr { border: 1px #bfbfbf solid; }</style></head><body style='font-family: monospace;'>";
            //string EmailPageHtml = "<html><head><style> td {border: 1px #bfbfbf solid;}thead { background-color:#e6f2ff; color: #272727;} tr { border: 1px #bfbfbf solid; }</style></head><body style='font-family: monospace;'>";

            string EmailPageHtml = "<html><head><style> thead { background-color:#bfbfbf; color: #272727;} tr { border: 1px #bfbfbf solid; }</style></head><body style='font-family: monospace;'>";
            string imageURL = "";
            foreach (var vie in desktop.Views)
            {
                //if (vie.ViewKey == _viewPrint.ViewKey)
                //{
                if (vie.ViewType == "LIST")
                {
                    iFirstNRecords = (Util.GetSessionValue("FirstNRecords") == null) ? 10 : int.Parse(Util.GetSessionValue("FirstNRecords").ToString());
                    EmailPageHtml += GetDTintoTableinHTML(vie, siteName, SEL_VIEWOPTIONSSelectedIndex, Key, iFirstNRecords);
                }
                else if (vie.ViewType == "REPORT")
                {
                    SEL_VIEWOPTIONSSelectedIndex = 1;
                    iFirstNRecords = (Util.GetSessionValue("FirstNRecords") == null) ? 10 : int.Parse(Util.GetSessionValue("FirstNRecords").ToString());
                    EmailPageHtml += GetDTintoTableinHTML(vie, siteName, SEL_VIEWOPTIONSSelectedIndex, Key, iFirstNRecords);
                }
                else
                {
                    if (Util.GetSessionValue("PrintChartNames") != null)
                    {
                        imageURL = Util.GetSessionValue("PrintChartNames").ToString();
                        Util.ClearSessionValue("PrintChartNames");
                        //EmailPageHtml += "<h2 style='margin-bottom: 0px;margin-top: 0px;bgcolor: #8c8c8c !important;color: #ffffff;border: 1px #bfbfbf solid;text-align: center;'>" + _viewPrint.ViewName + "</h2><div><img style='min-height:350px; width:100%;' src='" + imageURL + "' alt='View Image' /></div>";
                        EmailPageHtml += "<h2 style='margin-bottom: 0px;margin-top: 0px;border: 1px #bfbfbf solid;text-align: center;'>" + _viewPrint.ViewName + "</h2><div><img style='min-height:350px; width:100%;' src='" + imageURL + "' alt='View Image' /></div>";
                    }
                }
                //}
            }
            EmailPageHtml += "</body></html>";

            return EmailPageHtml;
        }

        //SB Mapping desktop data into html markup..
        public string GetDesktopDataIntoHtml(Desktop desktop, string _viewCount, ViewPrint _viewPrint, string siteName, string Key, int SEL_VIEWOPTIONSSelectedIndex, int iFirstNRecords = 10)
        {
            //string _viewType = desktop.Views[1].ViewType;
            string imageURL = "";
            if (Util.GetSessionValue("PrintChartNames") != null)
            {
                imageURL = Util.GetSessionValue("PrintChartNames").ToString();
                Util.ClearSessionValue("PrintChartNames");
            }
            string desktopTitle = goTR.StripIllegalChars(goTR.StrRead(_viewPrint.MetaData, "US_WCLABEL", "", true));
            if (string.IsNullOrEmpty(desktopTitle))
            {
                desktopTitle = desktop.Title;
            }
            string[] urlImages = imageURL.Split('|');
            //string EmailPageHtml = "<html><head><style> td {border: 1px #bfbfbf solid;}thead { background-color:#e6f2ff; color: #272727;} tr { border: 1px #bfbfbf solid; }</style></head><body style='font-family: monospace;'>";
            string EmailPageHtml = "<html><head><style> thead { background-color:#bfbfbf; color: #272727;} tr { border: 1px #bfbfbf solid; }</style></head><body style='font-family: monospace;'>";

            EmailPageHtml += "<h1 style=\"text-align:center; font-size:25px;\"><u><b>" + desktopTitle + "<b></u></h1>";
            int imgCount = 0;
            for (int curView = 0; curView < desktop.Views.Count; curView++)
            {
                if (desktop.Views[curView].ViewType.ToUpper() == "LIST")
                {
                    EmailPageHtml += GetDTintoTableinHTML(desktop.Views[curView], siteName, SEL_VIEWOPTIONSSelectedIndex, Key, iFirstNRecords);
                }
                else if (desktop.Views[curView].ViewType.ToUpper() == "REPORT")
                {
                    SEL_VIEWOPTIONSSelectedIndex = 1;
                    iFirstNRecords = (Util.GetSessionValue("FirstNRecords") == null) ? 10 : int.Parse(Util.GetSessionValue("FirstNRecords").ToString());
                    EmailPageHtml += GetDTintoTableinHTML(desktop.Views[curView], siteName, SEL_VIEWOPTIONSSelectedIndex, Key, iFirstNRecords);
                }
                else
                {
                    if (urlImages.Length > imgCount)
                    {
                        //EmailPageHtml += "<h2 style='margin-bottom: 0px;margin-top: 0px;background-color: #8c8c8c !important;color: #ffffff;border: 1px #bfbfbf solid;text-align: center;'>" + desktop.Views[curView].ViewTitle + "</h2>";
                        EmailPageHtml += "<h2 style='margin-bottom: 0px;margin-top: 0px;border: 1px #bfbfbf solid;text-align: center;'>" + desktop.Views[curView].ViewTitle + "</h2>";
                        EmailPageHtml += "<img style='min-height:350px; width:100%;' src='" + urlImages[imgCount] + "' alt='View Image' /><br/>";
                        imgCount++;
                    }
                }
                EmailPageHtml += "</br>";
            }
            EmailPageHtml += "</body></html>";

            return EmailPageHtml;
        }


        //SB saving chart/Calendar image in site temp folder to retrieve into html(email/pdf)
        [HttpPost]
        public ActionResult LoadImage(string dt, string siteName)
        {
            //data:image/gif;base64,
            //this image is a single pixel (black)
            dt = dt.Replace("!", "+");
            dt = dt.Substring(dt.IndexOf(",") + 1);
            //byte[] bytes = Convert.FromBase64String(dt);

            string sPath = System.Web.Hosting.HostingEnvironment.MapPath("~/Temp/");
            string filePath = "image" + Guid.NewGuid().ToString();
            filePath = filePath.Replace("/", "").Replace(":", "").Replace(" ", "") + ".jpeg";

            //To create file
            if (System.IO.File.Exists(sPath + filePath))
                System.IO.File.Delete(sPath + filePath);
            System.IO.StreamWriter oStream = default(System.IO.StreamWriter);
            oStream = System.IO.File.AppendText(sPath + filePath);
            oStream.Close();


            var bytes = Convert.FromBase64String(dt);
            //Write encodded image data into image file
            using (var imageFile = new FileStream(sPath + filePath, FileMode.Create))
            {
                imageFile.Write(bytes, 0, bytes.Length);
                imageFile.Flush();
            }

            if (Util.GetSessionValue("PrintChartNames") != null)
                Util.SetSessionValue("PrintChartNames", Util.GetSessionValue("PrintChartNames").ToString() + "|" + siteName + "/Temp/" + filePath);
            else
                Util.SetSessionValue("PrintChartNames", siteName + "/Temp/" + filePath);
            return Json(siteName + "/Temp/" + filePath, JsonRequestBehavior.AllowGet);
        }

        public JsonResult SendJob(string Page, string Target, string ViewKey, string EDT_FIRSTNRECORDSText, int SEL_VIEWOPTIONSSelectedIndex, string TXT_ToText = "", string TXT_CcText = "", string TXT_SubjectText = "", string _viewCount = "0", string siteName = "", string Key = "", string View_Mode = "view", string SiteName = "")
        {
            goData = (clData)Util.GetInstance("data");
            goTR = (clTransform)Util.GetInstance("tr");
            goMeta = (clMetaData)Util.GetInstance("meta");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");

            string sGUID = null;
            //int iFirstNRecords = -2;

            //SB checking user selected option to load no of records..
            int iFirstNRecords = 10;
            if (SEL_VIEWOPTIONSSelectedIndex == 2)
            {
                iFirstNRecords = (string.IsNullOrEmpty(EDT_FIRSTNRECORDSText)) ? 0 : int.Parse(EDT_FIRSTNRECORDSText);
            }
            else if (SEL_VIEWOPTIONSSelectedIndex == 0)
            {
                iFirstNRecords = 10000;
            }
            else
            {
                iFirstNRecords = (Util.GetSessionValue("FirstNRecords") == null) ? 10 : int.Parse(Util.GetSessionValue("FirstNRecords").ToString());
            }
            string sReplyName = null;
            string sReplyAddress = null;
            string sHTML = null;
            clEmail oEmail = default(clEmail);
            clRowSet oRS = default(clRowSet);

            string sRange = null;
            string sFileName = "";
            string sMisc = "";
            string sSNQ = "";
            string sPageID = null;
            string sTitle = null;

            //Excel varialbles
            string sDiskFileName = "";
            int iSplitLines = 1;
            string sFilename = "";
            ViewKey = ViewKey.Replace(" ", "");

            Page = HttpUtility.UrlDecode(Page);

            if (string.IsNullOrEmpty(siteName))
            {
                siteName = Request.Url.Scheme + "://" + Request.Url.Authority;
            }

            ViewPrint _viewPrint = new ViewPrint();
            if (Util.GetSessionValue("Print" + ViewKey) != null)
            {
                _viewPrint = (ViewPrint)Util.GetSessionValue("Print" + ViewKey);
            }
            else
            {
                //SB assigning view data when its session has null data
                _viewPrint = GetViewPrintModel(View_Mode, Target, _viewPrint, ViewKey, Key);
                Util.SetSessionValue("Print" + ViewKey, _viewPrint);
            }

            MessageBoxForPrintEmailPdf messageBoxForPrintEmailPdf = new MessageBoxForPrintEmailPdf();
            Desktop desktop = new Desktop("GLOBAL", _viewPrint.CurrentDesktopId, false);
            if (desktop.ViewCount == 0)
            {
                desktop = (DesktopModel)Util.GetSessionValue("Curr_Desktop" + Key);
            }
            //SB when the print mode is null.. filling from parameter
            if (_viewPrint.Mode == null)
            {
                _viewPrint.Mode = View_Mode.ToUpper();
            }

            //try
            //{
            //switch (View_Mode.ToUpper())
            switch (_viewPrint.Mode.ToUpper())
            {
                case "VIEW":

                    if (SEL_VIEWOPTIONSSelectedIndex == 2)
                    {
                        EDT_FIRSTNRECORDSText = (string.IsNullOrEmpty(EDT_FIRSTNRECORDSText)) ? "0" : EDT_FIRSTNRECORDSText;
                        iFirstNRecords = Convert.ToInt32(EDT_FIRSTNRECORDSText);
                        if (iFirstNRecords < 1 | iFirstNRecords > clC.SELL_VIEWPRINT_MAXRECORDS)
                        {
                            MessageBox(messageBoxForPrintEmailPdf, "Please enter a number between 1 and " + goTR.NumToString(clC.SELL_VIEWPRINT_MAXRECORDS) + " for the number of records to print or select a different option.", "FirstNRecordsValidation");
                            return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                        }
                    }

                    Util.SetSessionValue("FirstNRecords", iFirstNRecords);
                    sGUID = _viewPrint.CurrentDesktopId;
                    if (sGUID == null | string.IsNullOrEmpty(sGUID))
                    {
                        //goErr.SetError(35000, sProc, "Open desktop couldn't be found in history: goHist.GetLastOpenDesktopGUID returned ''.");
                        return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                    }
                    else
                    {
                        switch (Target)
                        {
                            case "HTMLEMAIL":
                                oEmail = new clEmail();
                                oRS = new clRowSet("US", 3, "GID_ID='" + goP.GetUserTID() + "'", "", "TXT_NameLast,TXT_NameFirst,EML_email");
                                if (oRS.Count() < 1)
                                {
                                    //Record may have been deleted - do not raise error so the calling page can display the following 'friendly' error
                                    //goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP failed because current user's User record cannot be found. GID_ID: '" + goP.GetUserTID + "'.");
                                    MessageBox(messageBoxForPrintEmailPdf, "Sending via SMTP failed because your User record ('" + goP.GetUserTID() + "') cannot be found. Please contact your administrator.");
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                //MI 5/14/09 Changed SYS_Name to TXT_NameFirst & " " & TXT_NameLast
                                sReplyName = oRS.GetFieldVal("TXT_NameFirst") + " " + oRS.GetFieldVal("TXT_NameLast");
                                sReplyAddress = oRS.GetFieldVal("EML_EMail").ToString();
                                if (string.IsNullOrEmpty(sReplyAddress))
                                {
                                    //MessageBox(messageBoxForPrintEmailPdf, "Email address for this user should not be empty.");
                                    //return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                    sReplyAddress = "<EMAIL>";
                                }

                                //Page = Page.Replace("\"/Content", "\"" + SiteName + "/Content");
                                //Page = Page.Replace("#$%^&*", "");
                                //string rAnchor = @"<a [^>]+>(.*?)<\/a>";
                                //Page = Regex.Replace(Page, rAnchor, "$1");

                                //if (Util.GetSessionValue("PrintChartNames") != null)
                                //{
                                //    Page = GetDataIntoHTML(desktop, _viewPrint, iFirstNRecords, siteName, Key, SEL_VIEWOPTIONSSelectedIndex);
                                //}

                                Page = GetDataIntoHTML(desktop, _viewPrint, iFirstNRecords, siteName, Key, SEL_VIEWOPTIONSSelectedIndex);

                                if (!oEmail.SendSMTPEmailNew(TXT_SubjectText, Page, TXT_ToText, TXT_CcText, "", "", sReplyName, sReplyAddress, "", true))
                                //if (!oEmail.SendSMTPEmail(TXT_SubjectText, EmailGridHtml, TXT_ToText, TXT_CcText, "", "", sReplyName, sReplyAddress, "", true, sHTML))
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "Error sending e-mail via SMTP: " + goErr.GetLastError() + " " + goErr.GetLastError("MESSAGE"));
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                if (goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "VIEWORDESKTOPSENDEMAILSHOWSUCCESS", "1") == "1")
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "The view was sent successfully via server.", "FinalOKMessage", "", clC.SELL_MB_YESNO, "  Close  ", "Don't show again");
                                    messageBoxForPrintEmailPdf.PNL_BodyDisible = true;
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                break;
                            case "EXCEL":
                                string gsAutoLoadView = goTR.StrRead(_viewPrint.MetaData, "AUTOLOADVIEWDATA", "");
                                bool gbDoNotAutoLoadMode;

                                if (gsAutoLoadView == "0")
                                {
                                    gbDoNotAutoLoadMode = true;
                                }
                                else
                                {
                                    gbDoNotAutoLoadMode = false;
                                }

                                // _viewPrint.ViewName = goTR.StripIllegalChars(_viewPrint.Title, "REMOVE");
                                //_viewPrint.ViewName = desktop.Title;        //Title for Excel..S1
                                sFilename = SelectedViewToTextFile(_viewPrint.CurrentViewId, _viewPrint.ViewType, gbDoNotAutoLoadMode, iFirstNRecords, SEL_VIEWOPTIONSSelectedIndex, iSplitLines, 800, Key, Target);
                                string Result = "";
                                goTR.StrWrite(ref Result, "US_NAME", "Send to Excel Queue Item");
                                goTR.StrWrite(ref Result, "MODE", "EXCEL");
                                goTR.StrWrite(ref Result, "OBJECTTOSEND", "View");
                                goTR.StrWrite(ref Result, "SENDNAME", "View to Excel '" + goTR.StripIllegalChars(_viewPrint.ViewName) + "'");
                                goTR.StrWrite(ref Result, "VIEWNAME", goTR.StripIllegalChars(_viewPrint.ViewName));
                                goTR.StrWrite(ref Result, "FILE", sFilename);
                                sPageID = "SNQ_" + goData.GenSUID("XX");
                                if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, Result))
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "An error occurred queuing this send job.");
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                else
                                {
                                    if (goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EXCELSENDSHOWSUCCESS", "1") == "1")
                                    {
                                        //goUI.ExecuteSendPopup = true;
                                        MessageBox(messageBoxForPrintEmailPdf, "The data was prepared sucessfully. If you have PC Link configured to 'Auto process', MS Excel will start shortly. If not, double-click the PC Link icon in the Tray and process the job from there.", "FinalOKMessage", "", clC.SELL_MB_YESNO, "  OK  ", "Don't show again");
                                        return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                    }
                                    else
                                    {
                                        //goUI.ExecuteSendPopup = true;
                                        //Close();
                                        return Json("success", JsonRequestBehavior.AllowGet);
                                    }
                                }
                                break;

                            case "PDF":
                                //EmailPageHtml = GetDataIntoHTML(desktop, _viewPrint, iFirstNRecords, siteName, Key, SEL_VIEWOPTIONSSelectedIndex);
                                //Append current datetime and user code to unique the filename
                                int par_iValid = 4;
                                string par_sDelim = "";
                                //Page = Page.Replace("\"/Content", "\"" + SiteName + "/Content");
                                //Page = Page.Replace("#$%^&*", "");
                                //rAnchor = @"<a [^>]+>(.*?)<\/a>";
                                //Page = Regex.Replace(Page, rAnchor, "$1");

                                //if (Util.GetSessionValue("PrintChartNames") != null)
                                //{
                                //    Page = GetDataIntoHTML(desktop, _viewPrint, iFirstNRecords, siteName, Key, SEL_VIEWOPTIONSSelectedIndex);
                                //}
                                Page = GetDataIntoHTML(desktop, _viewPrint, iFirstNRecords, siteName, Key, SEL_VIEWOPTIONSSelectedIndex);

                                sHTML = Page;
                                sFileName += "_" + goTR.StripIllegalChars(Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(System.DateTime.Now, ref par_iValid, ref par_sDelim), 18), "REMOVE") + "_" + goP.GetUserCode() + ".pdf";

                                int iPageWidth = ((Convert.ToInt32(Util.GetSessionValue("htmlcolwidth")) / 2) + 50);

                                if(_viewPrint.ViewType  == "REPORT")
                                {
                                    if (iPageWidth <= 1250)
                                    {
                                        iPageWidth = 1500;
                                    }
                                }
                                else
                                {
                                    if (iPageWidth <= 1250)
                                    {
                                        iPageWidth = 1400;
                                    }
                                }

                                Util.ClearSessionValue("htmlcolwidth");

                                clPDF oPDF = new clPDF();
                                if (!oPDF.HTMLToPDF(sFileName, sHTML, "A4", "LANDSCAPE", true, sTitle, par_iPageWidth: iPageWidth))
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "Error creating a PDF file.");
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                else
                                {
                                    goTR.StrWrite(ref sMisc, "SourceFolder", "temp");
                                    goTR.StrWrite(ref sMisc, "Delete", "True");
                                    goTR.StrWrite(ref sMisc, "Open", "True");
                                    goTR.StrWrite(ref sSNQ, "MISCINFO", sMisc);
                                    goTR.StrWrite(ref sSNQ, "MODE", "DOWNLOADFILE");
                                    goTR.StrWrite(ref sSNQ, "FILE", sFileName);
                                    goTR.StrWrite(ref sSNQ, "US_NAME", "Send to PDF queue item");
                                    goTR.StrWrite(ref sSNQ, "OBJECTTOSEND", "None");
                                    goTR.StrWrite(ref sSNQ, "SENDNAME", "To PDF '" + sFileName + "'");
                                    sPageID = "SNQ_" + goData.GenSUID("XX");
                                    if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, sSNQ))
                                    {
                                        MessageBox(messageBoxForPrintEmailPdf, "An error occurred queuing this send job.");
                                        return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                    }
                                    //goUI.ExecuteSendPopup = true;
                                    return Json("success", JsonRequestBehavior.AllowGet);
                                }
                                break;
                            default:
                                //PRINT

                                break;
                        }
                    }
                    break;

                case "DESKTOP":

                    //---------- Validate ------------
                    if (SEL_VIEWOPTIONSSelectedIndex == 2)
                    {
                        EDT_FIRSTNRECORDSText = (string.IsNullOrEmpty(EDT_FIRSTNRECORDSText)) ? "0" : EDT_FIRSTNRECORDSText;
                        iFirstNRecords = Convert.ToInt32(EDT_FIRSTNRECORDSText);
                        if (iFirstNRecords < 1 | iFirstNRecords > clC.SELL_VIEWPRINT_MAXRECORDS)
                        {
                            MessageBox(messageBoxForPrintEmailPdf, "Please enter a number between 1 and " + goTR.NumToString(clC.SELL_VIEWPRINT_MAXRECORDS) + " for the number of records to print or select a different option.", "FirstNRecordsValidation");
                            return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                        }
                    }

                    //switch (SEL_VIEWOPTIONSSelectedIndex)
                    //{
                    //    case 0:
                    //        iFirstNRecords = -2;
                    //        break;
                    //    case 1:
                    //        iFirstNRecords = -1;
                    //        break;
                    //    case 2:
                    //        break;
                    //    //iFirstNRecords is set during validation above
                    //}
                    Util.SetSessionValue("FirstNRecords", iFirstNRecords);

                    sGUID = _viewPrint.CurrentDesktopId;
                    if (sGUID == null | string.IsNullOrEmpty(sGUID))
                    {
                        //goErr.SetError(35000, sProc, "Open desktop couldn't be found in history: goHist.GetLastOpenDesktopGUID returned ''.");
                        return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                    }
                    else
                    {
                        switch (Target)
                        {
                            case "HTMLEMAIL":
                                oEmail = new clEmail();
                                oRS = new clRowSet("US", 3, "GID_ID='" + goP.GetUserTID() + "'", "", "TXT_NameLast,TXT_NameFirst,EML_email");
                                if (oRS.Count() < 1)
                                {
                                    //Record may have been deleted - do not raise error so the calling page can display the following 'friendly' error
                                    //goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP failed because current user's User record cannot be found. GID_ID: '" + goP.GetUserTID + "'.");
                                    MessageBox(messageBoxForPrintEmailPdf, "Sending via SMTP failed because your User record ('" + goP.GetUserTID() + "') cannot be found. Please contact your administrator.");
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                //MI 5/14/09 Changed SYS_Name to TXT_NameFirst & " " & TXT_NameLast
                                sReplyName = oRS.GetFieldVal("TXT_NameFirst") + " " + oRS.GetFieldVal("TXT_NameLast");
                                sReplyAddress = oRS.GetFieldVal("EML_EMail").ToString();
                                if (string.IsNullOrEmpty(sReplyAddress))
                                {
                                    //MessageBox(messageBoxForPrintEmailPdf, "Email address for this user should not be empty.");
                                    //return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                    sReplyAddress = "<EMAIL>";
                                }

                                //Page = Page.Replace("\"/Content", "\"" + SiteName + "/Content");
                                //Page = Page.Replace("#$%^&*", "");
                                //string rAnchor = @"<a [^>]+>(.*?)<\/a>";
                                //Page = Regex.Replace(Page, rAnchor, "$1");

                                //if (Util.GetSessionValue("PrintChartNames") != null)
                                //{
                                //    Page = GetDesktopDataIntoHtml(desktop, _viewCount, _viewPrint, siteName, Key, SEL_VIEWOPTIONSSelectedIndex, iFirstNRecords);
                                //}

                                Page = GetDesktopDataIntoHtml(desktop, _viewCount, _viewPrint, siteName, Key, SEL_VIEWOPTIONSSelectedIndex, iFirstNRecords);

                                TXT_SubjectText = (string.IsNullOrEmpty(TXT_SubjectText)) ? desktop.Title : TXT_SubjectText;
                                if (!oEmail.SendSMTPEmailNew(TXT_SubjectText, Page, TXT_ToText, TXT_CcText, "", "", sReplyName, sReplyAddress, "", true, sHTML))
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "Error sending e-mail via SMTP: " + goErr.GetLastError() + " " + goErr.GetLastError("MESSAGE"));
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                if (goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "VIEWORDESKTOPSENDEMAILSHOWSUCCESS", "1") == "1")
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "The desktop was sent successfully via server.", "FinalOKMessage", "", clC.SELL_MB_YESNO, "  Close  ", "Don't show again");
                                    messageBoxForPrintEmailPdf.PNL_BodyDisible = true;
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                return Json("desktop", JsonRequestBehavior.AllowGet);
                            //break;
                            case "PDF":

                                //string PDFPageHtml = GetDesktopDataIntoHtml(desktop, _viewCount, _viewPrint, siteName, Key, SEL_VIEWOPTIONSSelectedIndex, iFirstNRecords);

                                //Page = Page.Replace("\"/Content", "\"" + SiteName + "/Content");
                                //Page = Page.Replace("#$%^&*", "");
                                //rAnchor = @"<a [^>]+>(.*?)<\/a>";
                                //Page = Regex.Replace(Page, rAnchor, "$1");

                                //if (Util.GetSessionValue("PrintChartNames") != null)
                                //{
                                //    Page = GetDesktopDataIntoHtml(desktop, _viewCount, _viewPrint, siteName, Key, SEL_VIEWOPTIONSSelectedIndex, iFirstNRecords);
                                //}
                                Page = GetDataIntoHTML(desktop, _viewPrint, iFirstNRecords, siteName, Key, SEL_VIEWOPTIONSSelectedIndex);

                                //Generate the file name from the view's title
                                //if desktop has DRS, append this info to view name
                                sFileName = goTR.StripIllegalChars(goTR.StrRead(_viewPrint.MetaData, "US_WCLABEL", "", true), "REMOVE");
                                sTitle = desktop.Title;

                                //Append current datetime and user code to unique the filename
                                int par_iValid = 4; string par_sDelim = "";
                                sFileName += "_" + goTR.StripIllegalChars(Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.Now, ref par_iValid, ref par_sDelim), 18), "REMOVE") + "_" + goP.GetUserCode() + ".pdf";
                                clPDF oPDF = new clPDF();

                                int iPageWidth = ((Convert.ToInt32(Util.GetSessionValue("htmlcolwidth")) / 2) + 50);
                                if (iPageWidth <= 1150)
                                {
                                    iPageWidth = 1150;
                                }

                                //Set desktop title including date range if any as PDF file name
                                if (!oPDF.HTMLToPDF(sFileName, Page, "A4", "LANDSCAPE", true, goTR.StripIllegalChars(goTR.StrRead(_viewPrint.MetaData, "US_WCLABEL", "", true)), par_iPageWidth: iPageWidth))
                                {
                                    MessageBox(messageBoxForPrintEmailPdf, "Error creating a PDF file.");
                                    messageBoxForPrintEmailPdf.PNL_BodyDisible = true;
                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                }
                                else
                                {
                                    goTR.StrWrite(ref sMisc, "SourceFolder", "temp");
                                    goTR.StrWrite(ref sMisc, "Delete", "True");
                                    goTR.StrWrite(ref sMisc, "Open", "True");
                                    goTR.StrWrite(ref sSNQ, "MISCINFO", sMisc);
                                    goTR.StrWrite(ref sSNQ, "MODE", "DOWNLOADFILE");
                                    goTR.StrWrite(ref sSNQ, "FILE", sFileName);
                                    goTR.StrWrite(ref sSNQ, "US_NAME", "Send to PDF queue item");
                                    goTR.StrWrite(ref sSNQ, "OBJECTTOSEND", "None");
                                    goTR.StrWrite(ref sSNQ, "SENDNAME", "To PDF '" + sFileName + "'");
                                    sPageID = "SNQ_" + goData.GenSUID("XX");
                                    if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, sSNQ))
                                    {
                                        MessageBox(messageBoxForPrintEmailPdf, "An error occurred queuing this send job.");
                                        messageBoxForPrintEmailPdf.PNL_BodyDisible = true;
                                        return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                    }
                                    //goUI.ExecuteSendPopup = true;
                                }
                                return Json("desktop", JsonRequestBehavior.AllowGet);

                            case "EXCEL":

                                //gw.sResult = "";
                                //string sFile = "";
                                string sViewName = "";
                                //sGUID = goHist.GetLastOpenDesktopGUID();

                                //string _desktopMetaData2 = goMeta.PageRead("GLOBAL", Util.GetSessionValue("DesktopId").ToString(), "", true);
                                //DesktopModel _desktopModel = new DesktopModel("GLOBAL", Util.GetSessionValue("DesktopId").ToString(), true);
                                //IList<View> _ViewsCollection = _desktopModel.Views;
                                //foreach (View _view in _ViewsCollection)
                                //{
                                //if (!_view.IsMasterView)
                                //{
                                //    _view.MasterSelID = Util.GetSessionValue("MasterViewSelId").ToString();
                                //}


                                if (sGUID == null | string.IsNullOrEmpty(sGUID))
                                {
                                    //goErr.SetError(35000, sProc, "The current desktop couldn't be found in history: goHist.GetLastOpenDesktopGUID returned ''.");
                                    //return;
                                }
                                else
                                {

                                    if (desktop == null)
                                    {
                                        //goErr.SetError(30012, sProc, , "oDesktop");
                                        //return;
                                    }
                                    else
                                    {
                                        string gsAutoLoadView = goTR.StrRead(_viewPrint.MetaData, "AUTOLOADVIEWDATA", "");
                                        bool gbDoNotAutoLoadMode;

                                        if (gsAutoLoadView == "0")
                                        {
                                            gbDoNotAutoLoadMode = true;
                                        }
                                        else
                                        {
                                            gbDoNotAutoLoadMode = false;
                                        }

                                        IList<View> _ViewCollection = desktop.Views;
                                        foreach (View _view in _ViewCollection)
                                        {
                                            _viewPrint.ViewName = _view.ViewTitle;        //Title for Excel..S1
                                            _viewPrint.ViewType = (_viewPrint.ViewType == null) ? "" : _viewPrint.ViewType;
                                            sFilename += SelectedViewToTextFile(_view.ViewId.Replace(" ", ""), _view.ViewType, gbDoNotAutoLoadMode, iFirstNRecords, SEL_VIEWOPTIONSSelectedIndex, iSplitLines, 800, Key, Target) + "|~{";
                                            sViewName += _view.ViewTitle + "|~{";
                                        }
                                        sFilename = sFilename.Substring(0, sFilename.Length - 3);
                                        sViewName = sViewName.Substring(0, sViewName.Length - 3);
                                        if (string.IsNullOrEmpty(sFilename))
                                        {
                                            MessageBox(messageBoxForPrintEmailPdf, "Either the desktop has no views or an error occurred sending the desktop to Excel.");
                                            messageBoxForPrintEmailPdf.PNL_BodyDisible = true;
                                            return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                        }
                                        else
                                        {
                                            //clArray aFiles = new clArray();
                                            //aFiles = (clArray)_ViewCollection;
                                            //for (int i = 1; i < _ViewCollection.Count; i++)
                                            //{
                                            //    sDiskFileName = aFiles.GetInfo(i);
                                            //    sFile += sDiskFileName + "|";
                                            //}
                                            // //Remove trailing delimiter
                                            if (Microsoft.VisualBasic.Strings.Right(sFilename, 3) == "|")
                                                sFilename = Microsoft.VisualBasic.Strings.Left(sFilename, Microsoft.VisualBasic.Strings.Len(sFilename) - 3);
                                            if (Microsoft.VisualBasic.Strings.Right(sViewName, 3) == "|")
                                                sViewName = Microsoft.VisualBasic.Strings.Left(sViewName, Microsoft.VisualBasic.Strings.Len(sViewName) - 3);

                                            //if (desktop.DateRangeSelectorEnabled)
                                            //{
                                            //    sRange = goTR.GetDateRangeLabel(oDesktop.DateRangeSelectorSelectedRange, oDesktop.StartDate, oDesktop.EndDate, 0, false);
                                            //    switch (gw.sDesktopName)
                                            //    {
                                            //        case "":
                                            //            gw.sDesktopName = sRange;
                                            //            break;
                                            //        default:
                                            //            if (!string.IsNullOrEmpty(sRange))
                                            //            {
                                            //                gw.sDesktopName = gw.sDesktopName + "_" + sRange;
                                            //            }
                                            //            else
                                            //            {
                                            //                gw.sDesktopName = gw.sDesktopName;
                                            //            }
                                            //            break;
                                            //    }
                                            //}

                                            string Result = "";
                                            goTR.StrWrite(ref Result, "US_NAME", "Send to Excel Queue Item");
                                            goTR.StrWrite(ref Result, "MODE", "EXCEL");
                                            goTR.StrWrite(ref Result, "OBJECTTOSEND", "Desktop");
                                            goTR.StrWrite(ref Result, "SENDNAME", "Desktop to Excel '" + goTR.StripIllegalChars(goTR.StrRead(_viewPrint.MetaData, "US_WCLABEL", "", true)) + "'");
                                            goTR.StrWrite(ref Result, "DESKTOPNAME", goTR.StripIllegalChars(_viewPrint.ViewName));
                                            goTR.StrWrite(ref Result, "FILE", sFilename);
                                            goTR.StrWrite(ref Result, "VIEWNAME", sViewName);
                                            sPageID = "SNQ_" + goData.GenSUID("XX");

                                            if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, Result))
                                            {
                                                MessageBox(messageBoxForPrintEmailPdf, "An error occurred queuing this send job.");
                                                return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                            }
                                            else
                                            {
                                                if (goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EXCELSENDSHOWSUCCESS", "1") == "1")
                                                {
                                                    //goUI.ExecuteSendPopup = true;
                                                    MessageBox(messageBoxForPrintEmailPdf, "The data was prepared sucessfully. If you have PC Link configured to 'Auto process', MS Excel will start shortly. If not, double-click the PC Link icon in the Tray and process the job from there.", "FinalOKMessage", "", clC.SELL_MB_YESNO, "  OK  ", "Don't show again");
                                                    return Json(messageBoxForPrintEmailPdf, JsonRequestBehavior.AllowGet);
                                                }
                                                else
                                                {
                                                    //goUI.ExecuteSendPopup = true;
                                                    //Close();
                                                    return Json("desktop", JsonRequestBehavior.AllowGet);
                                                }
                                            }
                                        }
                                    }
                                }

                                break;

                            default:
                                ////PRINT
                                return Json("", JsonRequestBehavior.AllowGet);
                                break;
                        }
                    }

                    break;
                //     CASE "RECORD"

                default:
                    //goErr.SetError(35000, sProc, "Unsupported value in gw.sMode: '" + gw.sMode + "'.");
                    break;
            }

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, "SendJob");
            //    }
            //}

            return Json("", JsonRequestBehavior.AllowGet);
        }


        public ExcelExportClass ExportToExcel(string ViewKey, string Key)
        {
            int iFirstNRecords;
            string FilterText = string.Empty;
            string TopRecordId = string.Empty;

            bool _ReverseSort = false;
            clData goData = (clData)Util.GetInstance("Data");
            clTransform goTR = (clTransform)Util.GetInstance("tr");
            clProject goP = (clProject)Util.GetInstance("p");
            DataTable dt = null;
            ViewKey = ViewKey.Replace(" ", "");
            SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + ViewKey);

            string TableName = (_SessionViewInfo.TableName == null) ? "" : _SessionViewInfo.TableName;

            string SortText = _SessionViewInfo.SortText;
            string _sort = SortText;//use to get top rec

            FilterText = _SessionViewInfo.ViewCondition;

            if (FilterText.Contains("<%StartDate%>"))
            {
                string _FilterText = Util.GetDRSFilterCondition(FilterText, Key);

                FilterText = _FilterText;
            }

            if (FilterText.ToLower().Contains("<%selectedrecordid") || FilterText.ToLower().Contains("<%selectedviewrecordid"))
            {
                Dictionary<string, string> _viewSelectedRecordIds = (Dictionary<string, string>)Util.GetSessionValue(Key + "_" + "ViewSelectedRecordIds");

                do
                {
                    if (FilterText.ToLower().Contains("selectedrecordid"))
                    {
                        int iPos = Strings.InStr(FilterText.ToUpper(), Strings.UCase("<%SelectedRecordID FILE="));
                        string sFile = Strings.Mid(FilterText.ToUpper(), iPos + Strings.Len("<%SelectedRecordID FILE="), 2);
                        string sId = "";
                        if (_viewSelectedRecordIds != null && _viewSelectedRecordIds.ContainsKey(sFile) && !string.IsNullOrEmpty(_viewSelectedRecordIds[sFile].ToString()))
                        {
                            sId = _viewSelectedRecordIds[sFile].ToString();
                        }
                        else
                        {
                            //sId = goData.GenSUIDFake(sFile);
                            if (!string.IsNullOrEmpty(Convert.ToString(Util.GetSessionValue(sFile + "_" + "selectedprofilerecid"))))
                            {
                                sId = Util.GetSessionValue(sFile + "_" + "selectedprofilerecid").ToString();
                            }
                            else
                            {
                                sId = goData.GenSUIDFake(sFile);
                            }
                        }
                        //FilterText = FilterText.ToUpper().Replace("<%SELECTEDRECORDID FILE=" + sFile + "%>", sId);
                        FilterText = goTR.Replace(FilterText.ToUpper(), "<%SELECTEDRECORDID FILE=" + sFile + "%>", sId);
                    }
                    else if (FilterText.ToLower().Contains("selectedviewrecordid"))
                    {

                        int iPos = Strings.InStr(FilterText.ToUpper(), Strings.UCase("<%SELECTEDVIEWRECORDID FILE="));
                        string sFile = Strings.Mid(FilterText.ToUpper(), iPos + Strings.Len("<%SELECTEDVIEWRECORDID FILE="), 2);
                        string sId = "";
                        if (_viewSelectedRecordIds.ContainsKey(sFile))
                        {
                            sId = _viewSelectedRecordIds[sFile].ToString();
                        }
                        else
                        {
                            sId = goData.GenSUIDFake(sFile);
                        }
                        //FilterText = FilterText.ToUpper().Replace("<%SELECTEDVIEWRECORDID FILE=" + sFile + "%>", sId);
                        FilterText = goTR.Replace(FilterText.ToUpper(), "<%SELECTEDVIEWRECORDID FILE=" + sFile + "%>", sId);
                    }


                } while (FilterText.ToLower().Contains("selectedviewrecordid") || FilterText.ToLower().Contains("selectedrecordid"));

            }

            //SB checking user chosen no of records to load and assigning to iFirstNRecords
            _SessionViewInfo.PageSize = (_SessionViewInfo.PageSize == 0) ? _SessionViewInfo.ViewDataCount : _SessionViewInfo.PageSize;
            iFirstNRecords = 20000;// (SEL_VIEWOPTIONSSelectedIndex == 1) ? _SessionViewInfo.PageSize : iFirstNRecords;

            //Toprec with format to get data
            string _TopRec = "";
            if (!string.IsNullOrEmpty(TopRecordId))
            {
                _TopRec = Util.GetTopRec(TableName, _sort, TopRecordId);
            }

            //previously uncommented now whatever changes have done in Util Readdata those must be done here also..J
            //TopRecordId = _SessionViewInfo.Toprec;
            //var _TopRec = Util.GetTopRec(TableName, SortText, TopRecordId);

            string GenFieldList = string.Empty;

            string GenFieldDef = string.Empty;

            var gcSorts = _SessionViewInfo.Sorts;
            var gcColumns = _SessionViewInfo.Columns;
            Dictionary<string, GridColumn> columnMap = new Dictionary<string, GridColumn>();



            if (gcColumns != null)
            {
                for (int i = 0; i < gcColumns.Count; i++)
                {
                    string sFieldFormatted = gcColumns[i].NameOrg;
                    if (string.IsNullOrEmpty(GenFieldList))
                    {
                        GenFieldList = "GEN_" + i.ToString();
                    }
                    else
                    {
                        GenFieldList = GenFieldList + ", " + "GEN_" + i.ToString();
                    }
                    goTR.StrWrite(ref GenFieldDef, "GEN_" + i.ToString(), sFieldFormatted);
                    columnMap.Add("GEN_" + i.ToString(), gcColumns[i]);
                }
            }
            if (gcSorts != null)
            {
                for (int i = 0; i < gcSorts.Count; i++)
                {
                    string sFieldFormatted = gcSorts[i];
                    if (string.IsNullOrEmpty(GenFieldList))
                    {
                        GenFieldList = "GEN_SORT_" + i.ToString();
                    }
                    else
                    {
                        GenFieldList = GenFieldList + ", " + "GEN_SORT_" + i.ToString();
                    }
                    goTR.StrWrite(ref GenFieldDef, "GEN_SORT_" + i.ToString(), sFieldFormatted);
                }
            }
            if (!GenFieldList.Contains("GID_ID"))
            {
                GenFieldList = GenFieldList + ",GID_ID";
            }


            //get records that depends on user selection through radio buttons..J
            int iViewTop = 0;
            //int iFirstNRecords = Convert.ToInt32(Util.GetSessionValue("FirstNRecords"));

            if (iFirstNRecords == -2)
            {
                iViewTop = clC.SELL_VIEWPRINT_MAXRECORDS;
            }
            else if (iFirstNRecords == -1)
            {
                if (goTR.StrRead(_SessionViewInfo.ViewMetaData, "SECTIONSGROUPED", "0") != "1")
                {
                    string showTop_Var = goTR.StrRead(_SessionViewInfo.ViewMetaData, "SHOWTOP", "10");
                    if (!string.IsNullOrEmpty(showTop_Var))     //when its not null.. PC Link S1
                    {
                        if (showTop_Var.ToUpper() != "ALL")
                            iViewTop = Convert.ToInt32(showTop_Var);
                        else
                            iViewTop = 10000;
                    }
                }
                else
                {
                    iViewTop = clC.SELL_VIEWPRINT_MAXRECORDS;
                }
            }
            else if (iFirstNRecords == 0)
            {
                iViewTop = 0;
            }
            else if (iFirstNRecords > 0)
            {
                iViewTop = iFirstNRecords;
            }

            ExcelExportClass viewExport = new ExcelExportClass();

            string sUseSSO = clSettings.GetUseSSO();
            bool bSSOEnabled = false;
            if (!string.IsNullOrEmpty(sUseSSO))
            {
                bSSOEnabled = Convert.ToBoolean(sUseSSO);
            }
            if (bSSOEnabled)
            {
                viewExport.SystemuserName = Convert.ToString(Util.GetSessionValue("UserEmail"));

            }
            else
            {
                viewExport.SystemuserName = Convert.ToString(Util.GetSessionValue("username"));
            }

            viewExport.userName = goP.GetMe("ID");
            viewExport.genFieldList = GenFieldList;
            viewExport.sINI = "";
            viewExport.sFile = TableName;
            viewExport.iType = 7;
            viewExport.sCondition = FilterText;
            viewExport.sSort = SortText;
            viewExport.iTop = iViewTop.ToString();
            viewExport.par_sGenFieldsDefs = GenFieldDef;
            viewExport.Title = _SessionViewInfo.ViewTitle;
            viewExport.Columns = columnMap;
            



            //   clRowSet rs = new clRowSet(TableName, 3, FilterText, SortText, GenFieldList, iViewTop, "", GenFieldDef, "", "", _TopRec, false, false, false, _ReverseSort, _SessionViewInfo.LinksTop, "", false, false, 1800);

            return viewExport;
        }

        public JsonResult SendToExcel(string Mode, string Target, string ViewKey, int Index, string MasterViewSelId, string Key)
        {
            goData = (clData)Util.GetInstance("data");
            goTR = (clTransform)Util.GetInstance("tr");
            goMeta = (clMetaData)Util.GetInstance("meta");
            List<ExcelExportClass> myList = new List<ExcelExportClass>();
           
            if (Mode.ToUpper() == "DESKTOP")
            {
                ViewPrint _viewPrintTemp = new ViewPrint();
                DesktopModel _desktopModel = new DesktopModel("GLOBAL", Util.GetSessionValue("DesktopId").ToString(), true, Key);
                IList<View> _ViewsCollection = _desktopModel.Views;
                foreach (View _view in _ViewsCollection)
                {
                     _viewPrintTemp = GetViewPrintModel(Mode, Target, _viewPrintTemp, _view.ViewId, Key);
                    _viewPrintTemp.ViewId = _view.ViewId; _viewPrintTemp.ViewKey = _view.ViewKey; _viewPrintTemp.ViewType = _view.ViewType; _viewPrintTemp.Mode = "DESKTOP";
                    if (_viewPrintTemp.ViewType.ToUpper() == "LIST" || _viewPrintTemp.ViewType.ToUpper() == "REPORT")
                    {
                        myList.Add(ExportToExcel(_view.ViewKey, Key));
                    }
                }
            }
            else if (Mode.ToUpper() == "VIEW")
            { 
                SessionViewInfo _sessionViewInfo = new SessionViewInfo();
                string ViewTypeFromMD = "";
                _sessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + ViewKey.Replace(" ", ""));
                ViewTypeFromMD = _sessionViewInfo.ViewType;
                if (ViewTypeFromMD.ToUpper() == "LIST" || ViewTypeFromMD.ToUpper() == "REPORT")
                {
                    myList.Add(ExportToExcel(ViewKey, Key));
                }
                else
                {
                    return Json("DOESNOTSUPPORTED", JsonRequestBehavior.AllowGet);
                }

            }
            else if (Mode.ToUpper() == "EMAIL")
            {

                string sDesktopID = Convert.ToString(Util.GetSessionValue("DesktopId"));

                string sDskHTML = Util.GetDesktopAsEmailBody(sDesktopID);
                string sTo = Util.GetLogedinUserEmail();

                if (!string.IsNullOrEmpty(sTo))
                {
                    clEmail oEmail = new clEmail();
                    string sThisDesktopMeta = goMeta.PageRead("GLOBAL", sDesktopID, "", true);
                    string sThisDesktopName = goTR.StrRead(sThisDesktopMeta, "NAME");
                    oEmail.SendSMTPEmailNew(sThisDesktopName, sDskHTML, sTo, "", "", "", "Do Not Reply", "<EMAIL>", "", true);

                    return Json("EMAILSENT", JsonRequestBehavior.AllowGet);
                }
                else
                {
                    return Json("EMAILSENTFAILED", JsonRequestBehavior.AllowGet);
                }

            }
            string hostName = HttpContext.Request.Url.Host.ToString().ToLower();
            //make sure _viewPrint is serializable.
            bool sRetError = false;
            StringBuilder sData = new StringBuilder();
            sData.Append(JsonConvert.SerializeObject(myList));
            Scripts srcPost = new Scripts();
            srcPost.httpPost2(ref sRetError, System.Configuration.ConfigurationManager.AppSettings["autoHostName"].ToString()  + "&clientId=default&host=" + hostName, sData, "");

            return Json(myList, JsonRequestBehavior.AllowGet);
        }

    }
}
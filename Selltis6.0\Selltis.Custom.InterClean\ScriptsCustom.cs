﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;
        //private clScrMng goScr;
        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();

        }
        public ScriptsCustom()
        {
            Initialize();
        }


        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }

        public bool AC_FormControlOnChange_BTN_InsertStamp_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/13/2014 Prepend Date/Time stamp with user code to Notes field.

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)
            Form doForm = (Form)par_doCallingObject;
            // Dim sWork As String
            string sParams = "";
            string sDateStamp = "";

            // GetDateTimeStamp parameters:
            // par_s1: Format of the date and time. Supported values:
            // NEUTRAL		'Default: Ex: '2004-09-24 16:37 GMT' if par_s4 is 'UTC'.
            // REGIONAL	'Same as NEUTRAL until regional formatting is implemented.
            // par_s2: Name of the variable in which to return the date/time stamp.
            // par_s3: Format of the user code to include. Supported values:
            // NONE		'Default. Add no user code.
            // CODE		'User Code.
            // par_s4: Time zone (UTC (GMT) is the default). Supported values:
            // UTC         'Default
            // UTCNOOFFSETLABEL
            // USER        
            // USERNOOFFSETLABEL
            // SERVER  
            // SERVERNOOFFSETLABEL

            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", ""); // returns var sDateStamp

            doForm.doRS.SetFieldVal("MMO_Notes", sDateStamp + Constants.vbCrLf + doForm.doRS.GetFieldVal("MMO_Notes"));
            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_FormControlOnChange_MLS_PURPOSE_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // CS 6/14/07: Commented this out in _GetFormScripts

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            // TLD 1/15/2014 if purpose changes to Lead, set Type to Lead
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Purpose", 2)) == 8)
            {
                doForm.doRS.SetFieldVal("MLS_Type", 39, 2);
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_FormControlOnChange_MLS_TYPE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/15/2014 Stay on Notes tab
            par_bRunNext = false;

            try
            {

                // goP.TraceLine("", "", sProc)
                // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

                Form doForm = (Form)par_doCallingObject;

                scriptManager.RunScript("Activity_ManageControlState", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
                scriptManager.RunScript("Activity_FillEmail", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
                scriptManager.RunScript("Activity_FillFax", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
                scriptManager.RunScript("Activity_FillAddress", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
                scriptManager.RunScript("Activity_FillRe", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
                scriptManager.RunScript("Activity_FillSignature", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");

                // TLD 1/15/2014 Stay on Notes tab
                // 'Move to Corr tab if type is Corr
                // Select Case doForm.doRS.GetFieldVal("MLS_Type", 2)
                // Case 3, 4, 5, 6, 7, 8 ' CS 1/19/09 Removing WP Types , 9, 10
                // doForm.MoveToTab(1)
                // End Select

                doForm.MoveToTab(2); // Notes
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    goErr.SetError(ex, 45105, sProc);
                }
            }
            

            return true;
        }

        public bool AC_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 1/3/08 Edited Dim sproc line.
            // MI 12/13/07 Added tests to prevent tab from bouncing when browsing the form.
            // MI 12/3/07 Removed setting <meid> in Credited To US and Involves US.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/15/2014 Stay on Notes tab, regardless of correspondence types
            par_bRunNext = false;

            // PORTING FROM COMMENCE DONE. MI.

            Form doForm = (Form)par_doCallingObject;

            // for now using a variable to determine if Saving is allowed b/c I can't gray out the current
            // Save toolbar item
            // doForm.oVar.SetVar("AC_BTN_SAVE", "")

            // goP.TraceLine("Type: " & doForm.doRS.GetFieldVal("MLS_Type", 2) & " '" & doForm.doRS.GetFieldVal("MLS_Type", 1) & "'", "", sProc)
            // goP.TraceLine("Credited to User upon loading the record is: '" & doForm.doRS.GetFieldVal("LNK_CreditedTo_US") & "'", "", sProc)

            doForm.oVar.SetVar("sEmplOrigID", doForm.doRS.GetFieldVal("LNK_CreditedTo_US"));
            // doForm.oVar.SetVar("sEmplOrig", doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%SYS_Name"))
            // CS 8/25/08: Change from SYS NAME to Last, First
            doForm.oVar.SetVar("sEmplOrig", doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%TXT_NameLast") + ", " + doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%TXT_NameFirst"));
            doForm.oVar.SetVar("lLenJournal", Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")));


            // ----------- Set color of mandatory fields. --------------
            string sColor = Convert.ToString(goP.GetVar("sMandatoryFieldColor"));
            doForm.SetFieldProperty("DTE_StartTime", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("TME_StartTime", "LABELCOLOR", sColor);
            // doForm.SetFieldProperty("DTE_EndTime", "LABELCOLOR", sColor)
            // doForm.SetfieldProperty("TME_EndTime", "LABELCOLOR", sColor)
            doForm.SetFieldProperty("MLS_Type", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_CreditedTo_US", "LABELCOLOR", sColor);
            // CS 5/30: Not always mandatory doForm.SetfieldProperty("MMO_Notes", "LABELCOLOR", sColor)

            // Set button tooltips
            doForm.SetFieldProperties("BTN_LINKCOMPANIES", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Link Companies of the linked Contacts");
            doForm.SetFieldProperties("BTN_INSERTDATE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend Date and Time in Notes field");
            doForm.SetFieldProperties("BTN_LEADPROMPTS_2", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend lead prompts defined in Workgroup Options");
            doForm.SetFieldProperties("BTN_LEADPROMPTS", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend lead prompts defined in Workgroup Options");
            doForm.SetFieldProperties("BTN_INSERTTEXTBLOCK", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend text from linked template");
            doForm.SetFieldProperties("BTN_INSERTPRODQS_2", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend product questions from linked Product");
            doForm.SetFieldProperties("BTN_INSERTDATE_1", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend Date and Time in Notes field");
            doForm.SetFieldProperties("BTN_INSERTLINE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend a line in the Journal field");
            doForm.SetFieldProperties("BTN_LINKCOMPANIES_1", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Link Companies of the linked Contacts");
            doForm.SetFieldProperties("BTN_CALCBILLING", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Recalculate billing totals");
            doForm.SetFieldProperties("CHK_CREATEOP", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Create and open a linked Opportunity upon save");
            doForm.SetFieldProperties("CHK_CREATETODO", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Create and open a linked To Do upon save");
            // doForm.SetFieldProperties("BTN_SENDEMAIL", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Send an e-mail via your e-mail client")


            scriptManager.RunScript("Activity_ManageControlState", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");

            // '8/29/07: Enable 2nd direction links on Actions tab, per PJ
            // 'MI 10/26/07 Commented because now done in FLD metadata.
            // doForm.Setcontrolstate("LNK_RELATED_AP", 0)
            // doForm.Setcontrolstate("LNK_CONNECTED_EX", 0)
            // doForm.Setcontrolstate("LNK_CONNECTED_MS", 0)
            // doForm.Setcontrolstate("LNK_RELATED_AP", 0)
            // doForm.Setcontrolstate("LNK_RELATED_TD", 0)

            // Set the proper tab and move the cursor to the proper field
            // *** MI 12/13/07 Since this fires when the user is browsing the form
            // with |< < > >| buttons, the tab is set only when not set (this is the
            // case when the form is loaded initially) or when Corr/Notes tabs need
            // to be switched. Journal tab is selected only when opening a lead AC,
            // not when browsing to it.
            switch (doForm.doRS.GetFieldVal("MLS_Type", 2))
            {
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                case 8 // CS 1/19/09 Removing WP types, 9, 10
               :
                    {
                        if (doForm.GetTab() == 0)
                            // TLD 1/15/2014 Stay on Notes tab
                            // No tab selected
                            // doForm.MoveToTab(1)     'Correspondence
                            doForm.MoveToTab(2);// Notes
                                                // goP.TraceLine("Opening mode: '" & doForm.GetOpeningMode() & "'", "", sProc)
                        if (Strings.InStr(doForm.GetOpeningMode(), "REPLY") > 0)
                        // goP.TraceLine("Opening mode is reply", "", sProc)
                        {
                            doForm.MoveToField("MMO_Letter");
                        }
                        else
                        // goP.TraceLine("Opening mode is not reply", "", sProc)
                        {
                            doForm.MoveToField("LNK_RELATED_CN");
                        }
                        break;
                    }

                default:
                    {
                        // CS 7/27/09: The mls purpose list is often modified such that the purpose types below do not apply. I am sectionalizing
                        // the code so that it can be skipped if needed. If it is skipped, it should be re-coded in custom formOnLoadRecord so that
                        // the proper tab is set based on purpose.
                        if (scriptManager.IsSectionEnabled(sProc, par_sSections, "SetTabFromPurpose"))
                        {
                            switch (doForm.doRS.GetFieldVal("MLS_PURPOSE", 2))
                            {
                                case 8:
                                case 21:
                                case 22:
                                case 23:
                                case 24:
                                case 25      // 8=Lead, 21-24=Request, 25=Submit Promoter Lead
                               :
                                    {
                                        if (doForm.GetMode() == "CREATION")
                                        {
                                            if (doForm.GetTab() < 2)
                                                // No tab, corr tab selected
                                                doForm.MoveToTab(2);// Notes
                                            doForm.MoveToField("MMO_NOTES");
                                        }
                                        else
                                        {
                                            // Modif mode
                                            if (doForm.GetTab() == 0)
                                                // No tab selected (this won't happen when opening a new record with |< < > >| buttons on the form)
                                                doForm.MoveToTab(3);// Journal
                                            doForm.MoveToField("MMO_JOURNAL");
                                        }

                                        break;
                                    }

                                default:
                                    {
                                        if (doForm.GetTab() < 2)
                                            // Tab not selected or Corr tab selected
                                            doForm.MoveToTab(2);// Notes
                                        doForm.MoveToField("MMO_Notes");
                                        break;
                                    }
                            }
                        }

                        break;
                    }
            }

            // CS:If you create a linked email from an Activity that does have the email filled,
            // the email field in the AC will not be filled. This fixes that.
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Type", 2)) == 6)
            {
                // Type is email
                if (doForm.doRS.GetFieldVal("Eml_Email") == "")
                {
                    // Email address is empty, means CRL failed to fill it
                    if (doForm.doRS.GetLinkCount("LNK_Related_CN") == 1)
                        // 1 Contact is linked – it wouldn’t be if we did Create Unlinked or plain New...
                        doForm.doRS.SetFieldVal("eml_Email", doForm.doRS.GetFieldVal("LNK_Related_CN%%eml_email"));
                }
            }

            scriptManager.RunScript("Activity_FillAddress", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");

            // goScr.RunScript("Activity_ManageExtraFields", doForm)
            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 3/14/2014 Prevent main from running
            // Always Next Actions Date if Status is Open
            goTR.StrWrite(ref par_sSections, "EnforceNextActionDateIfOpenLead", "0");
            // ----- Next Action Date must be a valid date if Purpose=Lead and Status=Open
            // If goScr.IsSectionEnabled(sProc, par_sSections, "EnforceNextActionDateIfOpenLead") Then
            // goP.TraceLine("IsDate of Next Action Date (should be 1 if date): '" & IsDate(doForm.GetFieldVal("DTE_NEXTACTIONDATE", 3)) & "'", "", sProc)
            // ==> Retest: IsDate was coded wrong.
            // CS: The below line allows as blank NA date.
            // If goTR.IsDate(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 2)) <> True Then
            if (Convert.ToString(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1)) == "")
            {
                // goP.TraceLine("Purpose (should be 8): '" & doForm.GetFieldVal("MLS_PURPOSE", 2) & "'" & vbCrLf & _
                // "Status (should be 0): '" & doForm.GetFieldVal("MLS_STATUS", 2) & "'", "", sProc)
                // If doForm.doRS.GetFieldVal("MLS_PURPOSE", 2) = 8 And doForm.doRS.GetFieldVal("MLS_STATUS", 2) = 0 Then
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    // Purpose 8 = Lead; Status 0 = Open
                    // doForm.MoveToTab(3)     'Journal
                    doForm.MoveToField("DTE_NEXTACTIONDATE");
                    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE");
                    return false;
                }
            }
            // End If
            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sWork = "";

            // ----- TLD 1/14/2014 Write an entry in the History field if credited to user changes
            // if edit ONLY
            if (doForm.GetMode() != "CREATION")
            {
                if (doForm.oVar.GetVar("WriteUserToHistory") != "1")
                {
                    // goP.TraceLine("Considering whether to write in History.", "", sProc)
                    sWork = Convert.ToString(doForm.doRS.GetFieldVal("MMO_HISTORY"));
                    if (doForm.doRS.GetFieldVal("LNK_CreditedTo_US") != doForm.oVar.GetVar("sEmplOrigID"))
                    {
                        // CS 8/25/08: Changing from US SYS NAMe to Last, First b/c SYS Name can change
                        // sWork = goTR.WriteLogLine(sWork, "Lead transferred from " & doForm.oVar.GetVar("sEmplOrig") & " to " & doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%SYS_Name") & ".")
                        sWork = goTR.WriteLogLine(sWork, "Credited to User changed from " + doForm.oVar.GetVar("sEmplOrig") + " to " + doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%TXT_NameLast") + ", " + doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%TXT_NAMEFIRST") + ".");
                        // Write the History log
                        doForm.doRS.SetFieldVal("MMO_HISTORY", sWork);
                    }
                    doForm.oVar.SetVar("WriteUserToHistory", "1");
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormControlOnChange_BTN_COSTATUS_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/16/2014 Call script to display info
            scriptManager.RunScript("DisplayInfo", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormControlOnChange_BTN_INSERTLINE_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/8/07 Changed time stamp to local time, no label.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS 12122016 TKT#1364 : Add journal Functionality
            Form doForm = (Form)par_doCallingObject;
            string sParams = "";

            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", ""); // returns var sDateStamp

            doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm.oVar.GetVar("sDateStamp") + " ", "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CO_FormControlOnChange_BTN_INSERTLINE");
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // VS 12122016 TKT#1364 : Add journal Functionality
            // Set journal field locked
            doForm.SetControlState("MMO_Journal", 1);
            doForm.SetControlState("MMR_Journal", 1);
            doForm.oVar.SetVar("lLenJournal", Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")));
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO Ticket#125 If LNK_RELATED_CS is "OSB" or "OOSB" make CUR_QUOTATIONAMOUNT and DTE_SHIPFORECAST mandatory on save.
            if (doForm.doRS.GetFieldVal("LNK_RELATED_CS%%txt_companystatusname") == "OSB" | doForm.doRS.GetFieldVal("LNK_RELATED_CS%%txt_companystatusname") == "OOSB")
            {
                if (doForm.doRS.GetFieldVal("CUR_QUOTATIONAMOUNT", 1) == "$0.00")
                {
                    doForm.MoveToField("CUR_QUOTATIONAMOUNT");
                    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("CO", "CUR_QUOTATIONAMOUNT"), "", "", "", "", "", "", "", "", "CUR_QUOTATIONAMOUNT");
                    return false;
                }
                if (doForm.doRS.GetFieldVal("DTE_SHIPFORECAST", 1) == "")
                {
                    doForm.MoveToField("DTE_SHIPFORECAST");
                    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("CO", "DTE_SHIPFORECAST"), "", "", "", "", "", "", "", "", "DTE_SHIPFORECAST");
                    return false;
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormAfterSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // VS 12122016 TKT#1364 : Add journal Activity Functionality
            // -----	CreateActLog
            if (doForm.oVar.GetVar("CO_CreateActLog_Ran") != "1")
            {
                scriptManager.RunScript("CO_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");

            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_CreateActLog_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // 12/22/2004 RAH: turned over to MI
            // 2004/12/22 10:29:34 MAR Edited. SetLinkVals cause an error on line 37 of SetLinkVal: incorrect type.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            // PURPOSE:
            // PJ 5/30/02 Adds Act Log with new notes of Opportunity.
            // Run from enforce only in non-CREATION (MODIF) mode.
            // If it is run in CREATION mode, the new Activity will not be linked to the original Opp.
            // RETURNS:
            // 1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.

            Form doForm = (Form)par_doCallingObject;
            string sNotes = "";
            string sWork = "";
            long lWork = 0;
            string sMessage;

            par_bRunNext = false;

            if (Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")) <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))

                return true;

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Company is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);
                doForm.oVar.SetVar("CO_CreateActLog_Ran", "1");
                // Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                return true;
            }

            doForm.oVar.SetVar("CO_CreateActLog_Ran", "1");

            sWork = Convert.ToString(doForm.oVar.GetVar("JournalWithHardReturns"));
            // CS 2/4/10
            if (sWork == "")
                return true; // We didn't hit MessageBoxEvent from entering a journal note.

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, "", "", "", -1, "", "", "", "", "", false, false, false, false, -1, "", false, false, 1800);


            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, Convert.ToInt32(lWork));
            sNotes = sNotes + "== Created from Company '" + doForm.doRS.GetFieldVal("SYS_Name") + "'";
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            doNew.SetFieldVal("MLS_Status", 1, 2);     // Completed
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");

            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));

            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_CONNECTED_CN", ref par_doArray, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_CN", doLink);

            // doLink = doForm.doRS.GetLinkVal("LNK_FOR_PD", doLink)
            // doNew.SetLinkVal("LNK_Related_PD", doLink)

            // doLink = doForm.doRS.GetLinkVal("LNK_Related_GR", doLink)
            // doNew.SetLinkVal("LNK_Related_GR", doLink)

            doNew.SetFieldVal("MLS_TYPE", 31, 2);      // Journal
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(Convert.ToString(doNew.GetFieldVal("MMO_HISTORY")), "Created."));
            doNew.SetFieldVal("LNK_RELATED_CO", doForm.GetRecordID());

            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;
                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);

                goLog.Log("MessageBoxEvent", Convert.ToString(doForm.oVar.GetVar("ScriptMessages")), 1, false, false, 0, 0);
                return false;
            }
            else
                doForm.doRS.SetFieldVal("LNK_CONNECTED_AC", doNew.GetFieldVal("GID_ID"));

            doNew = null;
            doLink = null;

            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRs = (clRowSet)par_doCallingObject;

            // TLD 9/18/2014 Copy LNK_Related_SE to TXT_StateMailing
            doRs.SetFieldVal("TXT_StateMailing", "");
            if (doRs.IsLinkEmpty("LNK_Related_SE") == false)
            {
                doRs.SetFieldVal("TXT_StateMailing", doRs.GetFieldVal("LNK_Related_SE%%TXT_StateName"));

            }
            par_doCallingObject = doRs;
            return true;
        }

        public bool DisplayInfo_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: WOP Fieldname.
            // par_s2: From form
            // par_s3: For field
            // par_s4: Unused.
            // par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;

            // Purpose:  TLD 6/16/2014 Display info from WOP when user clicks info button on certain forms

            Form doForm = (Form)par_doCallingObject;
            string sFieldName = par_s1;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS");
            string sInfo = goTR.StrRead(sWOP, sFieldName);

            doForm.MessageBox(par_s3 + " Information" + Constants.vbCrLf + Constants.vbCrLf + sInfo);

            sWOP = "";
            sInfo = "";
            par_doCallingObject = doForm;
            return true;
        }

        public bool FIND_FormControlOnChange_BTN_COSearch_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            // TLD 8/25/2014 Added for custom State search
            // CS 8/17/09: Do NOT consider already saved filter of the desktop.
            // CS 10/5/12: Added goTr.PrepareForSQL

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 8/25/2014
            par_bRunNext = false;

            Form oForm = (Form)par_doCallingObject;

            // Find dialog; Company tab Search button
            string sName;
            string sCode;
            string sZip;
            string sCity;
            string sState;
            string sCountry;
            string sPhone;
            string sFilterPhone = "";
            string sFilterPhone2 = "";
            string sCustNo;
            int n;
            // TLD 9/18/2014 Added
            string sStatus = "";

            string sView;
            int iCondCount = 0;
            string sViewCondition;
            string sNewCondition;
            int iOrigCondCount = 0;
            int i;
            string sFilter = "";

            // Get values from form
            sName = Strings.Trim(oForm.GetControlVal("NDB_TXT_COMPANYNAME"));
            if (sName != "")
            {

                sName = goTR.ConvertStringForQS(goTR.PrepareForSQL(sName), "TXT_COMPANYNAME", "CO", true);
            }
            sCode = Strings.Trim(oForm.GetControlVal("NDB_TXT_CODE"));

            if (sCode != "")
            {
                sCode = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCode), "TXT_CUSTCODE", "CO", true);
            }
            sZip = Strings.Trim(oForm.GetControlVal("NDB_TXT_ZIPBUSINESS"));

            if (sZip != "")
            {
                sZip = goTR.ConvertStringForQS(goTR.PrepareForSQL(sZip), "TXT_ZIPBUSINESS", "CO", true);

            }
            sCity = Strings.Trim(oForm.GetControlVal("NDB_TXT_CITYBUSINESS"));
            if (sCity != "")
            {
                sCity = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCity), "TXT_CITYBUSINESS", "CO", true);

            }
            sState = Strings.Trim(oForm.GetControlVal("NDB_TXT_STATEBUSINESS"));
            if (sState != "")
            {
                sState = goTR.ConvertStringForQS(goTR.PrepareForSQL(sState), "TXT_STATEBUSINESS", "CO", true);

            }
            sCountry = Strings.Trim(oForm.GetControlVal("NDB_TXT_COUNTRYBUSINESS"));
            if (sCountry != "")
            {
                sCountry = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCountry), "TXT_COUNTRYBUSINESS", "CO", true);

            }
            sPhone = Strings.Trim(oForm.GetControlVal("NDB_TXT_PHONE"));
            if (sPhone != "")
            {
                sPhone = goTR.ConvertStringForQS(goTR.PrepareForSQL(sPhone), "TEL_PHONE", "CO", true);

            }
            sCustNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_CUSTNO"));
            if (sCustNo != "")
            {
                sCustNo = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCustNo), "TXT_CUSTNO", "CO", true);

            }
            // TLD 9/18/2014 Added
            sStatus = Strings.Trim(oForm.GetControlVal("NDB_TXT_Status"));
            if (sStatus != "")
            {
                sStatus = goTR.ConvertStringForQS(goTR.PrepareForSQL(sStatus), "LNK_Related_CS%%TXT_CompanyStatusName", "CO", true);

            }

            // Use values to filter Company - Search Results desktop if it exists
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_CDC43A16-6CC5-4D73-5858-9AF1013E4F05");
            // Edit views in DT

            // View 1:Companies - Search Results
            sView = oDesktop.GetViewMetadata("VIE_14D622C2-C038-4D39-5858-9AF1013E4F05");
            // iCondCount = goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"))


            // 'If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            // If iCondCount = 1 Then
            // If goTR.StrRead(sView, "C1FIELDNAME") = "<%ALL%>" Then
            // iCondCount = 0 'Will overwrite these values
            // End If
            // End If
            // Original condition count
            iOrigCondCount = iCondCount;

            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (sName != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sCode != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sZip != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sCity != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sPhone != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sCustNo != "")
            {
                iCondCount = iCondCount + 1;

            }
            // TLD 8/25/2014 changed to 2, searches for TXT_StateBusiness AND LNK_Related_SE
            if (sState != "")
            {
                iCondCount = iCondCount + 2;

            }
            if (sCountry != "")
            {
                iCondCount = iCondCount + 1;

            }
            // TLD 9/18/2014 Added
            if (sStatus != "")
            {
                iCondCount += 1;
            }
                

            // Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (sName != "")
            {
                // Add 'Company Name' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_COMPANYNAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sName);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_COMPANYNAME[" + sName + "";

                }
                else
                {
                    sFilter = "TXT_COMPANYNAME[" + sName + "";

                }
            }
            if (sCode != "")
            {
                // Add 'Company Code' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_CUSTCODE%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCode);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_CUSTCODE[" + sCode + "";

                }
                else
                {
                    sFilter = "TXT_CUSTCODE[" + sCode + "";

                }
            }
            if (sZip != "")
            {
                // Add 'Zip Mailing' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_ZIPMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sZip);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_ZIPMAILING[" + sZip + "";
                else
                    sFilter = "TXT_ZIPMAILING[" + sZip + "";
            }
            if (sCity != "")
            {
                // Add 'City Mailing' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_CITYMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCity);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_CITYMAILING[" + sCity + "";
                else
                    sFilter = "TXT_CITYMAILING[" + sCity + "";
            }
            // CS 12/1/08
            if (sState != "")
            {
                // Add 'State Mailing' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_STATEMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sState);
                goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND (TXT_STATEMAILING[" + sState + "";

                }
                else
                {
                    sFilter = "(TXT_STATEMAILING[" + sState + "";

                }
                // TLD 8/25/2014 Add LNK_Related_SE condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_Related_SE%><%TXT_STATENAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sState);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " OR LNK_Related_SE%%TXT_StateName[" + sState + ")";

                }
                else
                {
                    sFilter = "LNK_Related_SE%%TXT_StateName[" + sState + ")";

                }
            }
            // CS 12/1/08
            if (sCountry != "")
            {
                // Add 'Country Mailing' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_COUNTRYMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCountry);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_COUNTRYMAILING[" + sCountry + "";

                }
                else
                {
                    sFilter = "TXT_COUNTRYMAILING[" + sCountry + "";

                }
            }

            if (sCustNo != "")
            {
                // Add 'Cust No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_CUSTNO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCustNo);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_CUSTNO[" + sCustNo + "";

                }
                else
                {
                    sFilter = "TXT_CUSTNO[" + sCustNo + "";

                }
            }

            // CS 9/17/13
            if (sPhone != "")
            {
                // Add 'Phone' condition
                // Remove all numeric characters and add % to front and end of #
                // This will make sure we pick up all possible
                // phone number formats. Loop thru all characters in Bus Phone
                for (n = 1; n <= Strings.Len(sPhone); n++)
                {
                    if (!goTR.IsNumeric(Strings.Mid(sPhone, n, 1)))
                    {
                        sFilterPhone = sFilterPhone; // don't include this character

                    }
                    else
                    {
                        sFilterPhone = sFilterPhone + Strings.Mid(sPhone, n, 1);

                    }
                }

                // CS 9/13/13: Check multiple formats as stored in records
                if (Strings.Len(sFilterPhone) == 1)
                {
                    // Add % in front and end of phone #
                    sFilterPhone2 = "%" + sFilterPhone + "%";
                    goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                    goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                    goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                    if (sFilter != "")
                    {
                        sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";

                    }
                    else
                    {
                        sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";

                    }
                }

                if (Strings.Len(sFilterPhone) == 2)
                {
                    n = 1;
                    for (n = 1; n <= 2; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 1) + "_";  // format: #-#'
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: ##  
                            sFilterPhone2 = sFilterPhone;// format: ##
                                                         // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if ((sFilter != "" & n == 2))
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 1);
                            goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 3)
                {
                    n = 1;
                    for (n = 1; n <= 2; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 2) + "_";  // format: #-##'
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: ###                   
                            sFilterPhone2 = sFilterPhone;// format: ###
                                                         // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if ((sFilter != "" & n == 2))
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 1);
                            goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 4)
                {
                    n = 1;
                    for (n = 1; n <= 3; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###'
                        }
                        else if (n == 2)
                        {
                            // sFilterPhone2 = "_" & sFilterPhone & "_" 'format: ####
                            sFilterPhone2 = sFilterPhone; // format: ####
                        }
                           
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-#
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 1);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if ((sFilter != "" & n == 2) | (sFilter != "" & n == 3))
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 2);
                            if (n == 2)
                            {
                            }
                            if (n == 3)
                            {
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");

                            }
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 5)
                {
                    n = 1;
                    for (n = 1; n <= 3; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-#'
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 1);
                        }
                        else if (n == 2)
                        {
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: #####
                            sFilterPhone2 = sFilterPhone; // format: #####
                        }
                            
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 2);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if ((sFilter != "" & n == 2) | (sFilter != "" & n == 3))
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 2);
                            if (n == 2)
                            {
                            }
                            if (n == 3)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 6)
                {
                    n = 1;
                    for (n = 1; n <= 3; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-#'
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 2);
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: #####
                            sFilterPhone2 = sFilterPhone; // format: #####
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 3);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if ((sFilter != "" & n == 2) | (sFilter != "" & n == 3))
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 2);
                            if (n == 2)
                            {
                            }
                            if (n == 3)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 7)
                {
                    n = 1;
                    for (n = 1; n <= 4; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-###
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 3);
                        }
                        else if (n == 2)
                        {
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: #######
                            sFilterPhone2 = sFilterPhone; // format: #######
                        }
                           
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 4);
                        }
                        else if (n == 4)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-###-#
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 1);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if (sFilter != "" & n > 1)
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 3);
                            if (n == 2)
                            {
                            }
                            if (n == 4)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 8)
                {
                    n = 1;
                    for (n = 1; n <= 5; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 4);
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: ########
                            sFilterPhone2 = sFilterPhone; // format: ########
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-#####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 5);
                        }
                        else if (n == 4)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 2);
                        }
                        else if (n == 5)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-###-#
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 5, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 1);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if (sFilter != "" & n > 1)
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 4);
                            if (n == 2)
                            {
                            }
                            if (n == 5)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 9)
                {
                    n = 1;
                    for (n = 1; n <= 5; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-#####
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 5);
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: #########
                            sFilterPhone2 = sFilterPhone; // format: #########
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-######
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 6);
                        }
                        else if (n == 4)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-###-###
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 3);
                        }
                        else if (n == 5)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-###-##
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 5, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 2);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if (sFilter != "" & n > 1)
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 4);
                            if (n == 2)
                            {
                            }
                            if (n == 5)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 10)
                {
                    n = 1;
                    for (n = 1; n <= 5; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-######
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 6);
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: ##########
                            sFilterPhone2 = sFilterPhone;  // format: ##########
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-#######
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 7);
                        }
                        else if (n == 4)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-###-####
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 4);
                        }
                        else if (n == 5)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-###-###
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 5, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 3);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if (sFilter != "" & n > 1)
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 4);
                            if (n == 2)
                            {
                            }
                            if (n == 5)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                if (Strings.Len(sFilterPhone) == 11)
                {
                    n = 1;
                    for (n = 1; n <= 5; n++)
                    {
                        if (n == 1)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-#######
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 7);
                        }
                        else if (n == 2)
                            // 9/24/13 sFilterPhone2 = "_" & sFilterPhone & "_" 'format: ###########
                            sFilterPhone2 = sFilterPhone;  // format: ###########
                        else if (n == 3)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-########
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 8);
                        }
                        else if (n == 4)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 3) + "_"; // format: ###-###-#####
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 4, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 5);
                        }
                        else if (n == 5)
                        {
                            sFilterPhone2 = Strings.Left(sFilterPhone, 1) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 2, 3) + "_";  // format: #-###-###-####
                            sFilterPhone2 = sFilterPhone2 + Strings.Mid(sFilterPhone, 5, 3) + "_";
                            sFilterPhone2 = sFilterPhone2 + Strings.Right(sFilterPhone, 4);
                        }
                        // Add % in front and end of phone #
                        sFilterPhone2 = "%" + sFilterPhone2 + "%";
                        goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                        goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                        goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                        if (sFilter != "" & n == 1)
                        {
                            sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        else if (sFilter != "" & n > 1)
                        {
                            sFilter = sFilter + " OR TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                            goTR.StrWrite(ref sView, "CCOUNT", iCondCount + 4);
                            if (n == 2)
                            {
                            }
                            if (n == 5)
                                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                        }
                        else if (sFilter == "" & n == 1)
                        {
                            sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                            goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                            goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                        }
                        i = i + 1;
                    }
                }

                // If phone number is greater than 11 chars unknown format; just check for 
                // straight chars for now
                if (Strings.Len(sFilterPhone) > 11)
                {
                    // Add % in front and end of phone #
                    sFilterPhone2 = "%" + sFilterPhone + "%";
                    goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                    goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                    goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone2);
                    if (sFilter != "")
                        sFilter = sFilter + " AND TEL_PHONENO[" + sFilterPhone2 + "";
                    else
                        sFilter = "TEL_PHONENO[" + sFilterPhone2 + "";
                }
            }

            // TLD 9/18/2014 Added Company Status
            if (sStatus != "")
            {
                // Add 'Company Status' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_Related_CS%><%TXT_CompanyStatusName%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sStatus);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND LNK_Related_CS%%TXT_CompanyStatusName[" + sStatus + "";
                else
                    sFilter = "LNK_Related_CS%%TXT_CompanyStatusName[" + sStatus + "";
            }

            // Edit CONDITION= line in view MD
            // sViewCondition = goTR.StrRead(sView, "CONDITION")
            // If sViewCondition = "" Then
            sNewCondition = sFilter; // No filter in view already
                                     // Else
                                     // sNewCondition = sViewCondition & " AND " & sFilter
                                     // End If
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_14D622C2-C038-4D39-5858-9AF1013E4F05", sView);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;


            // Que Company Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
            goUI.Queue("", "");
            par_doCallingObject = oForm;
            return true;
        }

        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }

        public bool OP_FillFromCO_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // goP.TraceLine("In " & sProc, "", sProc)

            Form doForm = (Form)par_doCallingObject;

            // TLD 12/17/2013 Fill fields from CO
            doForm.doRS.ClearLinkAll("LNK_Related_RL");
            doForm.doRS.SetFieldVal("LNK_Related_RL", "LNK_For_CO%%LNK_Related_RL");
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormControlOnChange_LNK_FOR_CO_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 12/17/2013 Fill fields from CO
            scriptManager.RunScript("OP_FillFromCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 12/17/2013 Fill fields from CO if New
            if (doForm.GetMode() == "CREATION")
                scriptManager.RunScript("OP_FillFromCO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            double rProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__Probability", 2));

            // TLD 1/14/2014 Enforce DTE_ExpShipDate HERE if SI__Probability is 80 or 100
            // enforce here because SI__Probability is SET in OPP_CalcProbability
            if (rProb == 80 | rProb == 100)
            {
                if (doForm.doRS.GetFieldVal("DTE_ExpShipDate", 1) == "")
                {
                    doForm.MoveToField("DTE_ExpShipDate");
                    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_ExpShipDate"), "", "", "", "", "", "", "", "", "DTE_ExpShipDate");
                    return false;
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

            }
            // TLD 12/17/2013 Adjust setting CHK_Open
            // for custom statuses
            // ------------ AUTO-FILLED FIELDS ------------
            // Fill CHK_Open when Status is Open, On Hold or Quoted
            goTR.StrWrite(ref par_sSections, "AutoFillOpenCheckbox", "0");
            switch (doRS.GetFieldVal("MLS_STATUS", 2))
            {
                case 0        // Open
               :
                    {
                        doRS.SetFieldVal("CHK_Open", 1, 2);
                        break;
                    }

                default:
                    {
                        doRS.SetFieldVal("CHK_Open", 0, 2);
                        break;
                    }
            }
            par_bRunNext = false;
            par_doCallingObject = doRS;
            return true;
        }

        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // TLD 1/15/2014 Send alert to Users where New Opp Alert
            // is checked
            if (doRS.iRSType == 2)
            {
                if (goP.GetVar("OP_NewOppAlertSent") != "1")
                {
                    goP.SetVar("OP_NewOppAlertsent", "1");
                    // Loop thru users
                    clRowSet doUSRS = new clRowSet("US", 3, "CHK_ActiveField=1 AND CHK_NewOppAlert=1", "GID_ID");
                    if (doUSRS.GetFirst() == 1)
                    {
                        do
                        {
                            goUI.AddAlert("New Opportunity", clC.SELL_ALT_OPENRECORD, Convert.ToString(doRS.GetFieldVal("GID_ID")), Convert.ToString(doUSRS.GetFieldVal("GID_ID")), "opp16.gif");
                            if (doUSRS.GetNext() == 0)
                                break;
                        }
                        while (true);
                        doUSRS = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                }
            }
            par_doCallingObject = doRS;
            return true;
        }

        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sSalesProcess = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_sSalesProcess: 1 or 0 (default): if 1, use checkboxes in the Sales Process tab
            // to calculate probability %, else just calculate value and value index.
            // 2 to calc both
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 12/17/2013
            par_bRunNext = false;

            Form doForm = null;
            clRowSet doRS1 = null;


            // Check if we passed doForm or doRs to the script. We pass doRs from OP_RecordOnsave. This 
            // allows calculating value/prob on save of the form. In all other cases it is a form (clicking Calculate
            // buttons for example).
            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;

            }
            else
            {
                doForm = (Form)par_doCallingObject;

            }
            // goP.TraceLine("", "", sProc)

            // Dim lStatus As Long
            double rProb = default(double);
            decimal cValueFld = default(decimal);
            double rQty = default(double);
            double rNewValue = default(double);
            long lProb;

            // TLD 12/17/2013 Not using these?
            // Dim rQ03Worth As Double = 6.25  ' Leverage                      CHK_Q01
            // Dim rQ07Worth As Double = 6.25  ' Exec Buy-In             CHK_Q02
            // Dim rQ10Worth As Double = 12.5  ' CEO Contacted           CHK_Q03
            // Dim rQ20Worth As Double = 6.25  ' Front-End Buy In        CHK_Q04
            // Dim rQ30Worth As Double = 6.25  ' Influences IDd          CHK_Q05
            // Dim rQ35Worth As Double = 6.25  ' Needs Assessed          CHK_Q06
            // Dim rQ37Worth As Double = 6.25  ' Approved/Funded         CHK_Q07
            // Dim rQ40Worth As Double = 6.25  ' Competition IDd         CHK_Q08
            // Dim rQ50Worth As Double = 6.25  ' Champion Built          CHK_Q09
            // Dim rQ60Worth As Double = 6.25  ' Decision Process        CHK_Q10
            // Dim rQ65Worth As Double = 6.25  ' Timing Estimate         CHK_Q11
            // Dim rQ70Worth As Double = 6.25  ' Key Questions           CHK_Q12
            // Dim rQ75Worth As Double = 6.25  ' Present/Demo                  CHK_Q13
            // Dim rQ80Worth As Double = 6.25  ' Quote                   CHK_Q14
            // Dim rQ85Worth As Double = 6.25  ' Mark Status             CHK_Q15

            // TLD 12/17/2013 Not using these?
            // Dim bQ03 As Boolean
            // Dim bQ07 As Boolean
            // Dim bQ10 As Boolean
            // Dim bQ20 As Boolean
            // Dim bQ30 As Boolean
            // Dim bQ35 As Boolean
            // Dim bQ37 As Boolean
            // Dim bQ40 As Boolean
            // Dim bQ50 As Boolean
            // Dim bQ60 As Boolean
            // Dim bQ65 As Boolean
            // Dim bQ70 As Boolean
            // Dim bQ75 As Boolean
            // Dim bQ80 As Boolean
            // Dim bQ85 As Boolean

            // TLD 12/17/2013 MLS_Probability is:
            // US_0=<Make selection>
            // US_1 = 100%
            // US_2=91-100% Highly Likely to be Ours
            // US_3=80-90% Going After with Serious Competition -- 50/50 Shot
            // US_4=51-79% Order Likely to Happen within the Next 2 years
            // US_5=1-50% Cannot Estimate
            // US_6=0% Do Not Call Again
            // DEFAULT=0
            // US_NAME=List OPP:PROBABILITY
            // SORT = NUMERIC

            // TLD 12/17/2013 MLS_Status is:
            // US_0 = Open
            // US_1=On Hold
            // US_10 = Shipped
            // US_12 = Installed
            // US_2 = Won
            // US_3 = Lost
            // US_4 = Cancelled
            // US_5 = Delete
            // US_6 = Quoted
            // US_8 = Ordered
            // DEFAULT=0
            // US_NAME=List OPP:STATUS
            // ORDER=0,8,10,12,3
            // SORT = FIXED

            if (par_s2 == "doRS")
            {
                // -----      Calculate Value and Value Index
                rProb = Convert.ToDouble(doRS1.GetFieldVal("SI__PROBABILITY", 2));
                cValueFld = Convert.ToDecimal(doRS1.GetFieldVal("CUR_UNITVALUE", 2));
                if (!(goTR.IsNumber(cValueFld.ToString())))
                {
                    cValueFld = 0;
                }
                rQty = Convert.ToDouble(doRS1.GetFieldVal("SR__QTY", 2));
                // CS 10/13/08: Changed to IsNumber because the qty could be set to a varying number of decimals such as 2.15 and then
                // IsNumeric returns False
                // If Not goTR.IsNumeric(rQty) Then rQty = 0
                if (!goTR.IsNumber(rQty.ToString()))
                    rQty = 0;
                rNewValue = Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty);
                doRS1.SetFieldVal("CUR_VALUE", rNewValue, 2);
                doRS1.SetFieldVal("CUR_VALUEINDEX", Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty) * (Math.Round(rProb, 0) / 100), 2);   // Value Index
            }
            else
            {
                // Working on a form
                // Get Probability % value from form
                // rProb = doForm.doRS.GetFieldVal("SI__PROBABILITY", 2)
                // Calc probability and value index
                // TLD 1/15/2014 Statuses changed from these,
                // so ONLY set SI__Probability based on MLS_Probability
                // lStatus = doForm.doRS.GetFieldVal("MLS_STATUS", 2)
                // goP.TraceLine("lStatus is " & lStatus, "", sProc)
                // TLD 1/14/2014 Changed to set based on MLS_Probability, not status
                // TLD 12/17/2013
                lProb = Convert.ToInt64(doForm.doRS.GetFieldVal("MLS_Probability", 2));

                // Select lStatus
                // Case 0    'Status = Open
                // goP.TraceLine("par_sSalesProcess: '" & par_sSalesProcess & "'", "", sProc)

                // TLD 12/17/2013 Not using this?
                // If par_sSalesProcess = "1" Then
                // 'Calc based on sales process prompts
                // bQ03 = doForm.doRS.GetFieldVal("CHK_Q01", 2)
                // bQ07 = doForm.doRS.GetFieldVal("CHK_Q02", 2)
                // bQ10 = doForm.doRS.GetFieldVal("CHK_Q03", 2)
                // bQ20 = doForm.doRS.GetFieldVal("CHK_Q04", 2)
                // bQ30 = doForm.doRS.GetFieldVal("CHK_Q05", 2)
                // bQ35 = doForm.doRS.GetFieldVal("CHK_Q06", 2)
                // bQ37 = doForm.doRS.GetFieldVal("CHK_Q07", 2)
                // bQ40 = doForm.doRS.GetFieldVal("CHK_Q08", 2)
                // bQ50 = doForm.doRS.GetFieldVal("CHK_Q09", 2)
                // bQ60 = doForm.doRS.GetFieldVal("CHK_Q10", 2)
                // bQ65 = doForm.doRS.GetFieldVal("CHK_Q11", 2)
                // bQ70 = doForm.doRS.GetFieldVal("CHK_Q12", 2)
                // bQ75 = doForm.doRS.GetFieldVal("CHK_Q13", 2)
                // bQ80 = doForm.doRS.GetFieldVal("CHK_Q14", 2)
                // bQ85 = doForm.doRS.GetFieldVal("CHK_Q15", 2)

                // rProb = 0

                // If bQ03 Then rProb += rQ03Worth
                // If bQ07 Then rProb += rQ07Worth
                // If bQ10 Then rProb += rQ10Worth
                // If bQ20 Then rProb += rQ20Worth
                // If bQ30 Then rProb += rQ30Worth
                // If bQ35 Then rProb += rQ35Worth
                // If bQ37 Then rProb += rQ37Worth
                // If bQ40 Then rProb += rQ40Worth
                // If bQ50 Then rProb += rQ50Worth
                // If bQ60 Then rProb += rQ60Worth
                // If bQ65 Then rProb += rQ65Worth
                // If bQ70 Then rProb += rQ70Worth
                // If bQ75 Then rProb += rQ75Worth
                // If bQ80 Then rProb += rQ80Worth
                // If bQ85 Then rProb += rQ85Worth
                // ' Leaving the top 10% for the 'Won' Status
                // ' Leaving the bottom 10% when no check-boxes are checked
                // rProb = 10 + (rProb * 0.8)
                // End If
                // Case 2      'Status = Won
                // '' Set probability to 100%
                // rProb = 100
                // Case Else
                // TLD 1/14/2014 changed Probs
                // Check Probability
                switch (lProb)
                {
                    case 1:
                    case 2 // 100%, 91-100%
                   :
                        {
                            // Case 2 '91-100%
                            // rProb = 70
                            rProb = 100;
                            break;
                        }

                    case 3 // 80-90%
             :
                        {
                            rProb = 80;
                            break;
                        }

                    case 4 // 51-79%
             :
                        {
                            rProb = 60;
                            break;
                        }

                    case 5 // 1-50%
             :
                        {
                            rProb = 20;
                            break;
                        }

                    case 0:
                    case 6 // <Make selection>, 0%
             :
                        {
                            rProb = 0;
                            break;
                        }
                }

                // End Select
                // goP.TraceLine("Value of SI__PROBABILITY: '" & doForm.doRS.GetFieldVal("SI__PROBABILITY", 2) & "'", "", sProc)
                // goP.TraceLine("rProb: '" & Math.Round(rProb, 0) & "'", "", sProc)
                // TLD 1/14/2014 Always set
                // If doForm.doRS.GetFieldVal("SI__PROBABILITY", 2) <> Math.Round(rProb, 0) Then
                // goP.TraceLine("Setting SI__PROBABILITY to '" & Math.Round(rProb, 0) & "'", "", sProc)
                doForm.doRS.SetFieldVal("SI__PROBABILITY", Math.Round(rProb, 0), 2);
                // End If

                // -----      Calculate Value and Value Index
                cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2));
                if (!goTR.IsNumber(cValueFld.ToString()))
                    cValueFld = 0;
                rQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));
                // CS: 10/13/08: Changed to IsNumber b/c Qty could contain decimals
                // If Not goTR.IsNumeric(rQty) Then rQty = 0
                if (!goTR.IsNumber(rQty.ToString()))
                    rQty = 0;
                rNewValue = Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty);
                doForm.doRS.SetFieldVal("CUR_VALUE", rNewValue, 2);
                doForm.doRS.SetFieldVal("CUR_VALUEINDEX", Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty) * (Math.Round(rProb, 0) / 100), 2);   // Value Index
            }

            if (par_s2 == "doRS")
            {
                par_doCallingObject = doRS1;
            }
            else
            {
                par_doCallingObject = doForm;
            }

            return true;

        }

        public bool Opp_ManageControlState_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PURPOSE:
            // TLD 1/14/2014 Manage the state, position, and size of form controls for different 
            // Types and modes.

            Form doForm = (Form)par_doCallingObject;

            // Grayed fields
            doForm.SetControlState("SI__Probability", 4);
            par_doCallingObject = doForm;
            return true;
        }

        public bool Utility_RunImportUtility(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 1/6/2014 Added

            try
            {
                goUI.OpenURLExternal("../Pages/cus_diaImportMan.aspx", "Selltis", "height=840,width=1250,left=100,top=100,status=yes,location=no,toolbar=no,resizable=yes,titlebar=no,dependent=yes");
            }

            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }

        public bool XW_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/16/2016: Custom Workgroup Options

            Form doForm = (Form)par_doCallingObject;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS");

            // TLD 10/14/2013 CO Info buttons
            doForm.SetControlVal("NDB_MMO_COSTATUS", goTR.StrRead(sWOP, "COSTATUS"));
            par_doCallingObject = doForm;
            return true;
        }

        public bool XW_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/16/2014 Custom Workgroup options
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "", false);

            // TLD 10/14/2013 CO Info buttons
            goTR.StrWrite(ref sWOP, "COStatus", doForm.GetControlVal("NDB_MMO_COSTATUS"));

            // TLD Write to MD Page
            goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "XX");

            doForm.CloseOnReturn = true;
            doForm.CancelSave();
            par_doCallingObject = doForm;
            return true;
        }

        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sJournal = "";
            string sWork = "";
            string sSolution = "";
            string sClosenotes = "";
            string sCorrectiveaction = "";

            switch (Strings.UCase(par_s5))
            {
                case "CO_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                    {
                        par_bRunNext = false;
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    par_bRunNext = false;
                                    sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMO_Journal"));
                                    sWork = par_s2;
                                    doForm.oVar.SetVar("JournalWithHardReturns", sWork + Constants.vbCrLf + sJournal);
                                    if (sWork != "")
                                    {
                                        if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS") != "0")
                                        {
                                            sWork = goTR.Replace(sWork, Constants.vbCrLf, Strings.Chr(32) + Strings.Chr(32) + Strings.Chr(32).ToString());
                                            doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                        }
                                        else
                                            doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                        doForm.MoveToField("MMO_JOURNAL");
                                    }

                                    break;
                                }
                        }

                        break;
                    }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sGidId + "' And LNK_FOR_MO%%BI__ID<1 ", "LNK_IN_QT", "LNK_FOR_MO");

            if ((rsQL.GetFirst() == 1))
            {
                goErr.SetWarning(35000, sProc, "Please fill Model before saving the Quote  ");
                doForm.FieldInFocus = "LNK_FOR_MO";
                par_doCallingObject = doForm;
                return false;
            }

            return true;
        }
        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "Interclean_standard_Quote_draft.docx";
                }
            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "Interclean_standard_Quote.docx";
                }
            }
            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                       
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));
            string sModelText = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_MODELNAME"));            

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);

            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);
            rsQL.SetFieldVal("TXT_MODEL", sModelText);
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0); 
                doForm.doRS.SetFieldVal("SR__QTY", 0);
            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }

        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);

            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_FOR_MO,LNK_RELATED_PD,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);
            rsOL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            iLineCount = iLineCount + 1;

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");

            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }
           
            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }
        public bool OL_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.oVar.SetVar("OppID", doRS.GetFieldVal("LNK_IN_OP"));

            par_doCallingObject = doRS;

            return true;
        }

        public bool OL_RecordAfterDelete_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_s2 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sID = doRS.oVar.GetVar("OppID").ToString();

            if (!string.IsNullOrEmpty(sID))
            {
                clRowSet doOPLines = default(clRowSet);

                //Create a rowset of quote lines linked to the deleted quote.
                doOPLines = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo,Gid_id", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    StringBuilder sbQuery = new StringBuilder();
                    for (int i = 1; i <= doOPLines.Count(); i++)
                    {
                        double iLineno = Convert.ToDouble(doOPLines.GetFieldVal("SR__LineNo"));
                        string sGid_id = Convert.ToString(doOPLines.GetFieldVal("Gid_ID"));
                        string sTemp = "#" + goTR.Pad(i.ToString(), 6, " ", "L", true, "R");
                        sbQuery.AppendLine("Update OL Set SR__LineNo='" + i.ToString() + "',sys_name='" + sTemp + "' where Gid_id='" + sGid_id + "' AND SR__LineNo = '" + iLineno.ToString() + "'");

                        if (doOPLines.GetNext() == 0)
                        {
                            break;
                        }

                    }
                    if (!string.IsNullOrEmpty(sbQuery.ToString()))
                    {
                        bool bretval = goData.RunSQLQuery(sbQuery.ToString());
                    }

                }

                doOPLines = null;

            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);


            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    iLineCount = doOPLines.Count();
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 9/12/2008 Modified to ONLY fill when quote is new
            // TLD 9/5/2008 When new quote Fill inside rep (LNK_PEER_US) with creator
            // Fll outside rep (LNK_CREDITEDTO_US) with Team Leader
            Form doForm = (Form)par_doCallingObject;
            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool Quote_FillAddress_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //goP.TraceLine("", "", sProc)

            //PURPOSE:
            //		Fill the address field, first checking whether it is empty.
            //RETURNS:
            //		True

            string sContactName = "";
            string sMailingAddr = null;
            string sFirstName = null;
            string sLastName = null;
            //Dim sCompName as string
            string sAddrMail = null;
            string sCityMail = null;
            string sStateMail = null;
            string sZipMail = null;
            string sCountryMail = null;
            string scompany = null;

            //VS 03262018 TKT#2151 : Refill data whenever Contact has changed even if not empty.
            //if (!string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_ADDRESSMAILING").ToString()))
            //    return true;
            if (doForm.doRS.GetLinkCount("LNK_ORIGINATEDBY_CN") < 1)
                return true;

            //CS 6/22/09: Create CN rowset to get CN fields
            clRowSet doRSContact = new clRowSet("CN", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN") + "'", "", "LNK_RELATED_CO,TXT_NAMEFIRST,TXT_NAMELAST,TXT_ADDRBUSINESS,TXT_CITYBUSINESS,TXT_STATEBUSINESS,TXT_ZIPBUSINESS,TXT_COUNTRYBUSINESS");
            if (doRSContact.GetFirst() == 1)
            {
                scompany = Convert.ToString(doRSContact.GetFieldVal("LNK_RELATED_CO%%TXT_COMPANYNAME"));
                sFirstName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMEFIRST"));
                sLastName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMELAST"));


                if (!string.IsNullOrEmpty(sFirstName))
                {
                    sContactName = sFirstName + " ";
                }
                sContactName += sLastName;

                sAddrMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ADDRBUSINESS"));
                sCityMail = Convert.ToString(doRSContact.GetFieldVal("TXT_CITYBUSINESS"));
                sStateMail = Convert.ToString(doRSContact.GetFieldVal("TXT_STATEBUSINESS"));
                sZipMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ZIPBUSINESS"));
                sCountryMail = Convert.ToString(doRSContact.GetFieldVal("TXT_COUNTRYBUSINESS"));

                //Start building the mailing address
                //sMailingAddr = scompany;
                //if (!string.IsNullOrEmpty(scompany))
                //{
                //    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                //}
                //sMailingAddr = scompany;
                sMailingAddr = sContactName;
                if (!sAddrMail.Contains(scompany))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                }
                if (!string.IsNullOrEmpty(sAddrMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sAddrMail;
                }
                if (!string.IsNullOrEmpty(sCityMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCityMail;
                }
                if (!string.IsNullOrEmpty(sStateMail))
                {
                    sMailingAddr = sMailingAddr + ", " + sStateMail;
                }
                if (!string.IsNullOrEmpty(sZipMail))
                {
                    sMailingAddr = sMailingAddr + " " + sZipMail;
                }
                if (!string.IsNullOrEmpty(sCountryMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCountryMail;
                }
                doForm.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailingAddr);
                doForm.doRS.SetFieldVal("MMO_ADDRMAILING", sMailingAddr);
            }


            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;

        }
        //public bool GenerateSysName_post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //{

        //    string sProc = "clScripts:GenerateSysName";
        //    ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    clRowSet doRS = (clRowSet)par_doCallingObject;
        //    string sTemp = "";
        //    string sTemp2 = "";
        //    string sTemp3 = "";
        //    string sTemp4 = "";
        //    string sFileName = doRS.GetFileName();
        //    string sResult = "";
        //    clRowSet doLink = default(clRowSet);
        //    int iLen = 0;
        //    string par_sDelim = " ";

        //    //We assume that sFileName is valid. If this is a problem, test it here and SetError.

        //    switch (Microsoft.VisualBasic.Strings.UCase(sFileName))
        //    {

        //        case "OP":
        //            //==> OPP NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+LNK_For_Company%%TXT_CompanyName+" "+...
        //            //				LNK_For_Product%%TXT_ProductName+" "+CUR_Value
        //            //OPP		<For - Company - TXT_CompanyName> <For - Product - TXT_ProductName> CUR_ValueIndex (MLS_Status)  
        //            //			OPP-COMPANY-0						OPP-PRODUCT-0

        //            if (!doRS.IsLoaded("LNK_CreditedTo_US"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("LNK_For_CO"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".LNK_For_CO");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("LNK_For_PD"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".LNK_For_PD");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("DTT_Time"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".DTT_Time");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("CUR_Value"))
        //            {

        //                goErr.SetError(35103, sProc, "", sFileName + ".CUR_Value");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("MLS_Status"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".MLS_Status");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }

        //            //LNK_CreditedTo_US%%TXT_Code
        //            sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
        //            if (string.IsNullOrEmpty(sTemp))
        //            {
        //                //No records linked
        //                sTemp = "?";
        //            }
        //            else
        //            {
        //                //Find the field value in the linked record
        //                doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
        //                if (doLink.Count() > 0)
        //                {
        //                    sTemp = doLink.GetFieldVal("TXT_Code").ToString();
        //                }
        //                else
        //                {
        //                    sTemp = "?";
        //                }
        //            }

        //            //LNK_For_CO%%TXT_CompanyName
        //            sTemp2 = doRS.GetFieldVal("LNK_For_CO", 0, -1, false, 1).ToString();
        //            if (string.IsNullOrEmpty(sTemp2))
        //            {
        //                //No records linked
        //                sTemp2 = "";
        //            }
        //            else
        //            {
        //                //Find the field value in the linked record
        //                doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CompanyName", 1);
        //                if (doLink.Count() > 0)
        //                {
        //                    sTemp2 = doLink.GetFieldVal("TXT_CompanyName", 0, 22).ToString();
        //                }
        //                else
        //                {
        //                    sTemp2 = "";
        //                }
        //            }

        //            //LNK_For_Product%%TXT_ProductName
        //            sTemp3 = doRS.GetFieldVal("LNK_For_PD", 0, -1, false, 1).ToString();
        //            if (string.IsNullOrEmpty(sTemp3))
        //            {
        //                //No records linked
        //                sTemp3 = "";
        //            }
        //            else
        //            {
        //                //Find the field value in the linked record
        //                doLink = new clRowSet("PD", 3, "GID_ID='" + sTemp3 + "'", "", "TXT_ProductName", 1);
        //                if (doLink.Count() > 0)
        //                {
        //                    sTemp3 = doLink.GetFieldVal("TXT_ProductName", 0, 14).ToString();
        //                }
        //                else
        //                {
        //                    sTemp3 = "";
        //                }
        //            }

        //            //Company (23)   '25
        //            //Date (15)      '11
        //            //Credited To User (5)
        //            //Product (15)   '17
        //            //Value (13)
        //            //Status (9)
        //            //*** MI 10/4/07 Added LocalToUTC conversion
        //            //sResult = sTemp2 & " " & _
        //            //    goTR.DateToString(doRS.GetFieldVal("DTE_Time", clC.SELL_SYSTEM), "YYYY-MM-DD") & " " & _
        //            //    sTemp & " " & _
        //            //    sTemp3 & " " & _
        //            //    goTR.Pad(doRS.GetFieldVal("CUR_Value"), 11, " ", "L")
        //            DateTime dttttime = Convert.ToDateTime(doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM));
        //            DateTime gotrdatedtttiem = Convert.ToDateTime(goTR.UTC_LocalToUTC(ref dttttime));
        //            par_iValid = 4;
        //            par_sDelim = " ";

        //            sResult = sTemp2 + " " + Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(gotrdatedtttiem, ref par_iValid, ref par_sDelim), 10) + " GMT " + sTemp + " " + sTemp3 + " " + goTR.Pad(doRS.GetFieldVal("CUR_Value").ToString(), 11, " ", "L");

        //            sResult += " [" + doRS.GetFieldVal("MLS_STATUS", 0, 8).ToString() + "]";

        //            break;





        //        case "QT":
        //            //==> QUOTE NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+
        //            //					LNK_To_Company%%TXT_CompanyName+" "+CUR_Total

        //            if (!doRS.IsLoaded("LNK_CreditedTo_US"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("LNK_To_CO"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".LNK_To_CO");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("DTT_Time"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".DTT_Time");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("TXT_QuoteNo"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".TXT_QuoteNo");
        //                ///35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("CUR_Total"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".CUR_Total");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }
        //            if (!doRS.IsLoaded("MLS_STATUS"))
        //            {
        //                goErr.SetError(35103, sProc, "", sFileName + ".MLS_STATUS");
        //                ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
        //            }

        //            //LNK_CreditedTo_US%%TXT_Code
        //            sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
        //            if (string.IsNullOrEmpty(sTemp))
        //            {
        //                //No records linked
        //                sTemp = "?";
        //            }
        //            else
        //            {
        //                //Find the field value in the linked record
        //                doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
        //                if (doLink.Count() > 0)
        //                {
        //                    sTemp = doLink.GetFieldVal("TXT_Code").ToString();
        //                }
        //                else
        //                {
        //                    sTemp = "?";
        //                }
        //            }

        //            //LNK_To_CO%%TXT_CompanyName
        //            sTemp2 = doRS.GetFieldVal("LNK_To_CO", 0, -1, false, 1).ToString();
        //            if (string.IsNullOrEmpty(sTemp2))
        //            {
        //                //No records linked
        //                sTemp2 = "?";
        //            }
        //            else
        //            {
        //                //Find the field value in the linked record
        //                doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CompanyName", 1);
        //                if (doLink.Count() > 0)
        //                {
        //                    sTemp2 = doLink.GetFieldVal("TXT_CompanyName").ToString();
        //                }
        //                else
        //                {
        //                    sTemp2 = "?";
        //                }
        //            }


        //            //Company 17 '21
        //            //Date 15    '11
        //            //Cred User 6
        //            //Quote No 16
        //            //Total 15
        //            //Status 11
        //            //Total: 80
        //            sResult = goTR.Pad(sTemp2, 16, "", "R", true) + " " + goTR.Pad(sTemp3, 14, "", "R", true) + " " + goTR.Pad(sTemp, 4, "", "R", true) + " [" + goTR.Pad(doRS.GetFieldVal("TXT_QuoteNo").ToString(), 14, "", "R", true) + "] " + goTR.Pad(doRS.GetFieldVal("CUR_Total").ToString(), 13, "", "L", true) + " [" + doRS.GetFieldVal("MLS_STATUS", 0, 10).ToString() + "]";

        //            break;




        //    }



        //    par_oReturn = sResult;

        //    return true;

        //}

        public bool GenerateSysName_Pre(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            // MI 3/12/10 Added CASE "DF", modified Case Else to generate Name based on TXT_<FileName>Name field if exists.
            // CS 6/2/09 Modified QL case
            // CS 8/25/08 Case US: Added Title text
            // CS 8/20/08 Case CN: added Title Text
            // MI 11/14/07 Case CN: added TXT_ContactCode.
            // MI 10/15/07 Appended ' UTC' to all datetimes
            // MI 6/22/07 Changes to QT, OP, PR, PD, MO
            // MI 4/17/07 Added Phone Co to CN; Phone to CO ; removed padding from and to to 4 in MS; Status to OP, PR, QT, TD(?)
            // MI 3/9/07 Removed ellipsis from Co name in AC name.
            // MI 3/1/07 Added Contact, Company to AC Name.
            // MI 2/1/07 Added Date, Originator to Project name
            // MI 9/15/06 Updated QL, WT SYS_Name formats.
            // MI 7/25/06 Added raising error when field is not in the rowset.
            // MI 7/24/06 Mods. 
            // MI 7/20/06 Created in clScripts.
            // MI 7/20/06 Finished, tested, moved from clData to clScripts and renamed from GetCurrentRecordName to GetSysName.
            // MI 7/17/06 Started making this work in SellSQL.

            // AUTHOR: MI
            // PURPOSE:
            // Send back via the par_oReturn parameter the 'User Friendly' Name of the current record in par_oRowset.
            // This is to be called from each RecordOnSave script, but can be called
            // from any other code to generate a SYS_Name value. Do NOT set the name in 
            // the par_doCallingObject rowset or the script won't be usable simply for evaluating the returned
            // string.
            // IMPORTANT: Keep this "in sync" with clScripts.GetDefaultSort(), which is called from
            // clData.GetDefaultSort.
            // PARAMETERS:
            // par_doCallingObject: rowset object (for example, 'doRS'). The rowset must contain
            // all links and fields being referenced in the code below or error 45163 will be
            // raised. This can be achieved by putting '**' in the FIELDS parameter of the rowset, but 
            // avoid this when possible for performance reasons. The object, declared ByRef to conserve
            // resources by avoiding duplicating the object in memory, should not be altered directly
            // by this method (the purpose of the method is to return the name, not set it), but check
            // the code below to be sure.
            // par_doArray: not used
            // par_s1 - 5: not used
            // par_oReturn: String containing the generated SysName.
            // RETURNS:
            // True as a result. Returns friendly name or an empty string if the
            // filename is invalid via par_oReturn parameter.
            // EXAMPLE:
            // 'From a RecordOnSave script (not tested):
            // Dim sName as string = goScr.RunScript("GenerateSysName", doRS)
            // NOTES:
            // When a "Name" that is built with this method
            // is displayed in a View or linkbox and the same Name field is used
            // to sort the View or linkbox, at least the first field should match
            // the first field defined in clData::LKGetSortValue(). Otherwise what's
            // displayed will appear to be sorted arbitrarily.
            // NOTE 2:  
            // Links will not be tested because they are loaded automatically, but 
            // currently there is a bug in clRowset. RH working on this.

            string sProc = "clScripts:GenerateSysName";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS 11152016
            // par_bRunNext = False
            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";
            clRowSet doLink;
            int iLen;

            // We assume that sFileName is valid. If this is a problem, test it here and SetError.

            switch (Strings.UCase(sFileName))
            {
                case "CT"     // ==> Country
               :
                    {
                        par_bRunNext = false;

                        sTemp = doRS.GetFieldVal("TXT_COUNTRYNAME").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_COUNTRYCODE").ToString();
                        if (sTemp2 == "")
                            sTemp2 = "?";

                        sResult = sTemp + " (" + sTemp2 + ")";
                        break;
                    }

                case "SA"   // ==> State
         :
                    {
                        par_bRunNext = false;

                        sTemp = doRS.GetFieldVal("TXT_STATENAME").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_CODE").ToString();
                        if (sTemp2 == "")
                            sTemp2 = "?";

                        sResult = sTemp + " (" + sTemp2 + ")";
                        break;
                    }

                case "CY"   // ==> Customer Type
         :
                    {
                        par_bRunNext = false;

                        sTemp = doRS.GetFieldVal("TXT_CUSTOMERTYPENAME").ToString();
                        sTemp2 = doRS.GetFieldVal("TXT_CODE").ToString();
                        if (sTemp2 == "")
                            sTemp2 = "?";

                        sResult = sTemp + " (" + sTemp2 + ")";
                        break;
                    }

                case "OP":
                    {
                        // ==> OPP NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+LNK_For_Company%%TXT_CompanyName+" "+...
                        // LNK_For_Product%%TXT_ProductName+" "+CUR_Value
                        // OPP		<For - Company - TXT_CompanyName> <For - Product - TXT_ProductName> CUR_ValueIndex (MLS_Status)  
                        // OPP-COMPANY-0						OPP-PRODUCT-0

                        par_bRunNext = false;
                        if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_CreditedTo_US");

                        }
                        if (!doRS.IsLoaded("LNK_For_CO"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_For_CO");

                        }
                        if (!doRS.IsLoaded("LNK_For_PD"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_For_PD");

                        }
                        if (!doRS.IsLoaded("DTT_Time"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".DTT_Time");

                        }
                        if (!doRS.IsLoaded("CUR_Value"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".CUR_Value");

                        }
                        if (!doRS.IsLoaded("MLS_Status"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".MLS_Status");

                        }
                        if (!doRS.IsLoaded("MLS_MAINBLANKET"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".MLS_Status");


                        }

                        // LNK_CreditedTo_US%%TXT_Code
                        sTemp = Convert.ToString(doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, true, 1));
                        if (sTemp == "")
                            // No records linked
                            sTemp = "?";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", null, "TXT_Code", 1);
                            if (doLink.Count() > 0)
                                sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                            else
                                sTemp = "?";
                        }

                        // LNK_For_CO%%TXT_CompanyName
                        sTemp2 = Convert.ToString(doRS.GetFieldVal("LNK_For_CO", 1, 1, false, 1));
                        if (sTemp2 == "")
                            // No records linked
                            sTemp2 = "";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", null, "TXT_CompanyName", 1);
                            if (doLink.Count() > 0)
                                sTemp2 = doLink.GetFieldVal("TXT_CompanyName", 1, 22).ToString();
                            else
                                sTemp2 = "";
                        }

                        // LNK_For_Product%%TXT_ProductName
                        sTemp3 = doRS.GetFieldVal("LNK_For_PD", 1, 1, false, 1).ToString();
                        if (sTemp3 == "")
                            // No records linked
                            sTemp3 = "";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("PD", 3, "GID_ID='" + sTemp3 + "'", null, "TXT_ProductName", 1);
                            if (doLink.Count() > 0)
                                sTemp3 = doLink.GetFieldVal("TXT_ProductName", 1, 14).ToString();
                            else
                                sTemp3 = "";
                        }

                        // Company (23)   '25
                        // Date (15)      '11
                        // Credited To User (5)
                        // Product (15)   '17
                        // Value (13)
                        // Status (9)
                        // *** MI 10/4/07 Added LocalToUTC conversion
                        // sResult = sTemp2 & " " & _
                        // goTR.DateToString(doRS.GetFieldVal("DTE_Time", clC.SELL_SYSTEM), "YYYY-MM-DD") & " " & _
                        // sTemp & " " & _
                        // sTemp3 & " " & _
                        // goTR.Pad(doRS.GetFieldVal("CUR_Value"), 11, " ", "L")
                        int par_iValid = 0;
                        string par_sdlim = "";
                        DateTime sDate = Convert.ToDateTime(doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM));
                        sResult = sTemp2 + " " + Strings.Left(goTR.DateTimeToSysString(goTR.UTC_LocalToUTC(ref sDate), ref par_iValid, ref par_sdlim), 10) + " GMT " + sTemp + " " + sTemp3 + " " + goTR.Pad(doRS.GetFieldVal("CUR_Value").ToString(), 11, " ", "L");

                        //sResult += " " + doRS.GetFieldVal("MLS_MAINBLANKET", 1, 7);

                        sResult += " [" + doRS.GetFieldVal("MLS_STATUS", 1, 8) + "]";
                        break;
                    }
                case "QT":
                    //==> QUOTE NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+
                    //					LNK_To_Company%%TXT_CompanyName+" "+CUR_Total

                    if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("LNK_To_CO"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".LNK_To_CO");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("DTT_Time"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".DTT_Time");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("TXT_QuoteNo"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_QuoteNo");
                        ///35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("CUR_Total"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".CUR_Total");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("MLS_STATUS"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".MLS_STATUS");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    //LNK_CreditedTo_US%%TXT_Code
                    sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp))
                    {
                        //No records linked
                        sTemp = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                        }
                        else
                        {
                            sTemp = "?";
                        }
                    }

                    //LNK_To_CO%%TXT_CompanyName
                    sTemp2 = doRS.GetFieldVal("LNK_To_CO", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp2))
                    {
                        //No records linked
                        sTemp2 = "?";
                    }
                    else
                    {
                        //Find the field value in the linked record
                        doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CompanyName", 1);
                        if (doLink.Count() > 0)
                        {
                            sTemp2 = doLink.GetFieldVal("TXT_CompanyName").ToString();
                        }
                        else
                        {
                            sTemp2 = "?";
                        }
                    }


                    //Company 17 '21
                    //Date 15    '11
                    //Cred User 6
                    //Quote No 16
                    //Total 15
                    //Status 11
                    //Total: 80
                    sResult = goTR.Pad(sTemp2, 16, "", "R", true) + " " + goTR.Pad(sTemp3, 14, "", "R", true) + " " + goTR.Pad(sTemp, 4, "", "R", true) + " [" + goTR.Pad(doRS.GetFieldVal("TXT_QuoteNo").ToString(), 14, "", "R", true) + "] " + goTR.Pad(doRS.GetFieldVal("CUR_Total").ToString(), 13, "", "L", true) + " [" + doRS.GetFieldVal("MLS_STATUS", 0, 10).ToString() + "]";

                    break;

            }

            sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
            sResult = goTR.Replace(sResult, Strings.Chr(10).ToString(), " ");
            sResult = goTR.Replace(sResult, Strings.Chr(13).ToString(), " ");
            sResult = goTR.Replace(sResult, Constants.vbTab, " ");

            // 1/28/15 Manmeet added replace for |
            sResult = goTR.Replace(sResult, "|", " ");

            par_oReturn = sResult;
            par_doCallingObject = doRS;
            return true;
        }
        public bool OP_FormControlOnChange_BTN_CALCPROBABILITY_1_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_OPPTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            par_doCallingObject = doForm;
            return true;

        }
        private static void Refresh_OPPTotal(clRowSet doForm)
        {
            string sGidId = Convert.ToString(doForm.GetFieldVal("Gid_id"));

            clRowSet rsOL = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + sGidId + "' ", "LNK_IN_OP", "CUR_VALUE|SUM,CUR_ValueIndex|SUM");

            double curTotalAmt = 0.0;
            double ValueIndex = 0.0;

            if ((rsOL.GetFirst() == 1))
            {
                curTotalAmt = Convert.ToDouble(rsOL.GetFieldVal("CUR_VALUE|SUM", 2));
                ValueIndex = Convert.ToDouble(rsOL.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doForm.SetFieldVal("CUR_VALUE", curTotalAmt, 2);
                doForm.SetFieldVal("CUR_ValueIndex", ValueIndex, 2);
            }
            else
            {
                doForm.SetFieldVal("CUR_VALUE", 0.0);
                doForm.SetFieldVal("CUR_ValueIndex", 0.0);

            }
        }
        public bool QT_FormControlOnChange_BTN_RECALC_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_QouteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            par_doCallingObject = doForm;
            return true;

        }
        private static void Refresh_QouteTotal(clRowSet doQuote)
        {
            string sGidId = Convert.ToString(doQuote.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_GROUPBY, "LNK_IN_QT='" + sGidId + "' ", "LNK_IN_QT", "CUR_SUBTOTAL|SUM");

            double curTotalAmt = 0.0;

            if ((rsQL.GetFirst() == 1))
            {

                curTotalAmt = Convert.ToDouble(rsQL.GetFieldVal("CUR_SUBTOTAL|SUM", 2));


                doQuote.SetFieldVal("CUR_SUBTOTAL", curTotalAmt, 2);
                doQuote.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);

            }
            else
            {
                doQuote.SetFieldVal("CUR_SUBTOTAL", 0.0);
                doQuote.SetFieldVal("CUR_TOTAL", 0.0);

            }


        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));
            doFormQT.doRS.SetFieldVal("LNK_FOR_MO", rsOP.GetFieldVal("LNK_FORLINE_MO"));
            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_ENDUSER_CO", rsOP.GetFieldVal("LNK_ENGINEERING_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_DISTREP_CO", rsOP.GetFieldVal("LNK_DISTRIBUTOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECOMPLETED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }



        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }
        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

    }
}

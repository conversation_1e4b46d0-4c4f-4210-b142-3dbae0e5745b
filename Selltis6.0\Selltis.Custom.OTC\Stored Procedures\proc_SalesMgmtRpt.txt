﻿Create  procedure proc_SalesMgmtRpt  
  
As  
Begin   
  
 Declare @FirstDate_In_Curr_Year DateTime  
 Declare @FirstDate_In_Last_Year DateTime  
 Declare @LastDate_In_Last_Year DateTime  
 Declare @CurrDate_In_Last_Year DateTime  
 Declare @CurrDate DateTime=getutcdate()  
  
 select @FirstDate_In_Last_Year= dateadd(yy, -1, dateadd(YY,datediff(yy,0,getutcdate()),0)) -- first date in last year  
 select @LastDate_In_Last_Year=dateadd(DD, -1, dateadd(YY,datediff(yy,0,getutcdate()),0)) --last date of last year  
 select @CurrDate_In_Last_Year=dateadd(yy, -1, getutcdate()) -- curr date in last year  
  
 select @FirstDate_In_Curr_Year=dateadd(YY,datediff(yy,0,getutcdate()),0) -- first date in curr year  
  
 --print @FirstDate_In_Last_Year  
 --print @LastDate_In_Last_Year  
 --print @CurrDate_In_Last_Year  
 --print @FirstDate_In_Curr_Year  
 --print @CurrDate  
  
 --Alter Table US add CUR_YTDSales money  
 --Alter Table US add CUR_LastYTDSales money  
 --Alter Table US add CUR_LastYearSales money  
 --Alter Table US add SR__PercentToPYTD decimal  
 --Alter Table US add INT_SalesVisitsLast2wks int  
 --Alter Table US add INT_SalesVisitsLast30days int  
 --Alter Table US add INT_SalesVisitsLast90days int  
 --Alter Table US add INT_SalesVisitsYTD int  
   
   
  
 --Current YTD Sales  
 Select sum(CUR_TOTALAMOUNT) as Cur_YTDAmount ,GID_CREDITEDTO_US into #tmp1 from QO  
 where DTT_Time>= @FirstDate_In_Curr_Year AND dtt_time <= @CurrDate  
 group By GID_CREDITEDTO_US  
  
 --Prev YTD Sales  
 Select sum(CUR_TOTALAMOUNT) as Prev_YTDAmount,GID_CREDITEDTO_US into #tmp2 from QO  
 where DTT_Time>= @FirstDate_In_Last_Year AND dtt_time <= @CurrDate_In_Last_Year   
 group By GID_CREDITEDTO_US  
  
 --Prev Year Sales  
 Select sum(CUR_TOTALAMOUNT) As Prev_YearSales,GID_CREDITEDTO_US into #tmp3 from QO  
 where DTT_Time>= @FirstDate_In_Last_Year AND dtt_time <= @LastDate_In_Last_Year  
 group By GID_CREDITEDTO_US  
  
 --select * from #tmp1  
 --select * from #tmp2  
 --select * from #tmp3  
  
 EXEC pInitSession  
 @par_uUserID = 'F0272467-3EC5-48F7-5553-987900B57A11',  
 @par_sUserCode = 'SYST',  
 @par_sProduct = 'SA',  
 @par_sTZOffset = '-300'  
  
   
 update b set b.CUR_YTDSales=a.Cur_YTDAmount  
 from #tmp1 a  
 Join US b on b.Gid_id = a.GID_CREDITEDTO_US  
  
 update b set b.CUR_LastYTDSales=a.Prev_YTDAmount  
 from #tmp2 a  
 Join US b on b.Gid_id = a.GID_CREDITEDTO_US  
  
 update b set b.CUR_LastYearSales=a.Prev_YearSales  
 from #tmp3 a  
 Join US b on b.Gid_id = a.GID_CREDITEDTO_US  
  
 update US Set SR__PercentToPYTD= ROUND(CUR_YTDSales * 100.0 / CUR_LastYTDSales, 1) where CUR_LastYTDSales > 0  
   
 ---Sales Visits logged  
  
 --Last 2 wks,    
 select count(Gid_ID) as SalesVisitsLogged_2wks,GID_CreatedBy_US into #tmp4 from AC   
 where MLS_Type=11 AND (DTT_StartTime between DateAdd(week,-2,Getutcdate()) AND Getutcdate())  
 group by GID_CreatedBy_US  
  
 --Last 30 days  
 select count(Gid_ID) as SalesVisitsLogged_30days,GID_CreatedBy_US  into #tmp5  from AC   
 where MLS_Type=11 AND (DTT_StartTime between DateAdd(day,-30,Getutcdate()) AND Getutcdate())  
 group by GID_CreatedBy_US  
  
 --Last 90 days  
 select count(Gid_ID) as SalesVisitsLogged_90days,GID_CreatedBy_US into #tmp6  from AC   
 where MLS_Type=11 AND (DTT_StartTime between DateAdd(day,-90,Getutcdate()) AND Getutcdate())  
 group by GID_CreatedBy_US  
  
  --YTD  
 select count(Gid_ID) as SalesVisitsLogged_Ytd,GID_CreatedBy_US into #tmp7  from AC   
 where MLS_Type=11 AND (DTT_StartTime between @FirstDate_In_Curr_Year AND Getutcdate())  
 group by GID_CreatedBy_US  
  
  
 update b set b.INT_SalesVisitsLast2wks=a.SalesVisitsLogged_2wks  
 from #tmp4 a  
 Join US b on b.Gid_id = a.GID_CreatedBy_US  
  
 update b set b.INT_SalesVisitsLast30days=a.SalesVisitsLogged_30days  
 from #tmp5 a  
 Join US b on b.Gid_id = a.GID_CreatedBy_US  
  
 update b set b.INT_SalesVisitsLast90days=a.SalesVisitsLogged_90days  
 from #tmp6 a  
 Join US b on b.Gid_id = a.GID_CreatedBy_US  
   
 update b set b.INT_SalesVisitsYTD=a.SalesVisitsLogged_Ytd  
 from #tmp7 a  
 Join US b on b.Gid_id = a.GID_CreatedBy_US  
   
   
   
 End
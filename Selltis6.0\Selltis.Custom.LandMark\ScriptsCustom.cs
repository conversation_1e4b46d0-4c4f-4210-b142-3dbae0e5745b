﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;
using System.Data.SqlClient;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;
        //private clScrMng goScr;
        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";


        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();
            Util.SetSessionValue("SkipQLSpecificLogic", "Y");


        }


        public ScriptsCustom()
        {
            Initialize();
        }

        // SGR 25102016 



        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }
        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // TLD 10/4/2010 Added for type
            long lType = (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Type", 2)));

            // TLD 12/9/2008 Calls Function FillCOCN Instead
            scriptManager.RunScript("FILLCOCN", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray);

            // TLD Moved to Function FillCOCN
            // If doForm.GetMode() = "CREATION" Then

            // Dim prRelCN As New clArray
            // Dim prRelCO As New clArray
            // Dim prInvCO As New clArray
            // Dim prInvCN As New clArray
            // Dim sID As String
            // Dim sFileName As String

            // prInvCN = doForm.doRS.GetLinkVal("LNK_RELATED_PR%%LNK_INVOLVED_CN", prInvCN)
            // prInvCO = doForm.doRS.GetLinkVal("LNK_RELATED_PR%%LNK_INVOLVED_CO", prInvCO)
            // prRelCN = doForm.doRS.GetLinkVal("LNK_RELATED_PR%%LNK_OriginatedBy_CN", prRelCN)
            // prRelCO = doForm.doRS.GetLinkVal("LNK_RELATED_PR%%LNK_For_CO", prRelCO)

            // 'TLD 11/6/2008 Uncommented clearing CN fields when last selected record is PR
            // sID = goUI.GetLastSelected("SELECTEDRECORDID")
            // sFileName = goTR.GetFileFromSUID(sID)
            // If UCase(sFileName) = "PR" Then
            // 'TLD 11/10/2008 Customer changed their mind -- wants to ONLY fill LNK_RELATED_CN and LNK_RELATED_CO if Project
            // 'from Involved CN and Involved CO
            // doForm.doRS.ClearLinkAll("lnk_related_cn")
            // doForm.doRS.SetLinkVal("LNK_RELATED_CN", prInvCN)
            // doForm.doRS.ClearLinkAll("lnk_related_co")
            // doForm.doRS.SetLinkVal("LNK_RELATED_CO", prInvCO)
            // doForm.doRS.ClearLinkAll("lnk_involves_cn")
            // doForm.doRS.ClearLinkAll("lnk_involves_co")
            // Else
            // 'TLD 10/17/2008 Commented per customer -- they want create linked to fill from CN and CO when creating AC
            // 'doForm.doRS.ClearLinkAll("lnk_related_cn")
            // 'doForm.doRS.ClearLinkAll("lnk_related_co")
            // 'doForm.doRS.ClearLinkAll("lnk_involves_cn")
            // 'doForm.doRS.ClearLinkAll("lnk_involves_co")
            // doForm.doRS.SetLinkVal("LNK_RELATED_CN", prInvCN)
            // doForm.doRS.SetLinkVal("LNK_RELATED_CO", prInvCO)
            // doForm.doRS.SetLinkVal("LNK_Involves_CN", prRelCN)
            // doForm.doRS.SetLinkVal("LNK_Involves_CO", prRelCO)
            // End If
            // End If

            // TLD 10/4/2010 Changed to use var above
            // TLD 8/12/2008 Changed Case Else to default to Notes Tab
            switch (lType)
            {
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                case 8:
                case 9:
                case 10 // correspondence
               :
                    {
                        doForm.MoveToTab(1);
                        break;
                    }

                default:
                    {
                        // doForm.MoveToTab(4) 'Details
                        doForm.MoveToTab(2); // Notes
                        break;
                    }
            }

            // TLD 07/30/2008 this post basically wipes out related CN and email never fills
            // TLD 07/30/2008 Copied filling email from main script
            // CS:If you create a linked email from an Activity that does have the email filled,
            // the email field in the AC will not be filled. This fixes that.
            // TLD 10/4/2010 Changed to use var above
            // If doForm.doRS.GetFieldVal("MLS_Type", 2) = 6 Then   '6 = Email Sent
            if (lType == 6)
            {
                // Type is email
                if (Convert.ToString(doForm.doRS.GetFieldVal("Eml_Email")) == "")
                {
                    // TLD 10/4/2010 Changed to use islinkempty
                    // Email address is empty, means CRL failed to fill it
                    // If doForm.doRS.GetLinkCount("LNK_Related_CN") = 1 Then
                    if (doForm.doRS.IsLinkEmpty("LNK_Related_CN") == false)
                        // 1 Contact is linked – it wouldn’t be if we did Create Unlinked or plain New...
                        doForm.doRS.SetFieldVal("eml_Email", doForm.doRS.GetFieldVal("LNK_Related_CN%%eml_email", 1, 1, false, 1));
                }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // Dim dBidDate As DateTime

            // Only offer to update dates if only 1 Project linked, 
            if (doForm.doRS.GetLinkCount("LNK_RELATED_PR") == 1)
            {

                // TLD 10/4/2010 Mod to create RS once here
                clRowSet doPRRS = new clRowSet("PR", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_RELATED_PR") + "'", null/* Conversion error: Set to default value for this argument */, "*");

                if (Convert.ToString(doForm.oVar.GetVar("UpdateDates")) != "1")
                {
                    doForm.oVar.SetVar("UpdateDates", "1");
                    // Check if dates have changed and ask to update on project if so.
                    // dBidDate = doForm.doRS.GetFieldVal("LNK_RELATED_PR%%DTT_Q50", 2)
                    // TLD 10/4/2010 Mod to use RS
                    // If doForm.doRS.GetFieldVal("DTT_BIDDATE", 1) <> "" And doForm.doRS.GetFieldVal("DTT_BIDDATE", 1) <> doForm.doRS.GetFieldVal("LNK_RELATED_PR%%DTT_Q50", 1) Then
                    if (Convert.ToString(doForm.doRS.GetFieldVal("DTT_BIDDATE", 1)) != "" & doForm.doRS.GetFieldVal("DTT_BIDDATE", 1) != doPRRS.GetFieldVal("DTT_Q50", 1))
                    {
                        // doForm.MessageBox("Would you like to update the bid date, next action date, and last modified date for the linked project " & doForm.doRS.GetFieldVal("LNK_RELATED_PR%%SYS_NAME") & "?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "AC_FormOnSave_Post_ProjectDates")
                        doForm.MessageBox("Would you like to update the bid date, next action date, and last modified date for the linked project " + doPRRS.GetFieldVal("SYS_NAME") + "?", clC.SELL_MB_YESNOCANCEL, null, null, null, null, null, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", null, "AC_FormOnSave_Post_ProjectDates");
                        return true;
                    }
                    // TLD 10/4/2010 Mod to use RS
                    // If doForm.doRS.GetFieldVal("DTT_NextContactDate", 1) <> "" And doForm.doRS.GetFieldVal("DTT_NEXTCONTACTDATE", 1) <> doForm.doRS.GetFieldVal("LNK_RELATED_PR%%DTT_NEXTACTIONDATE", 1) Then
                    if (Convert.ToString(doForm.doRS.GetFieldVal("DTT_NextContactDate", 1)) != "" & doForm.doRS.GetFieldVal("DTT_NEXTCONTACTDATE", 1) != doPRRS.GetFieldVal("DTT_NEXTACTIONDATE", 1))
                    {
                        // doForm.MessageBox("Would you like to update the bid date, next action date, and last modified date for the linked project " & doForm.doRS.GetFieldVal("LNK_RELATED_PR%%SYS_NAME") & "?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "AC_FormOnSave_Post_ProjectDates")
                        doForm.MessageBox("Would you like to update the bid date, next action date, and last modified date for the linked project " + doPRRS.GetFieldVal("SYS_NAME") + "?", clC.SELL_MB_YESNOCANCEL, null, null, null, null, null, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", null, "AC_FormOnSave_Post_ProjectDates");
                        return true;
                    }
                    // TLD 10/4/2010 Mod to use RS
                    // Only compare date portion b/c last mod time is updated with date/time everytime a project is saved
                    // If doForm.doRS.GetFieldVal("DTE_LastContactDate", 1) <> "" And doForm.doRS.GetFieldVal("DTE_LASTCONTACTDATE", 1) <> doForm.doRS.GetFieldVal("LNK_RELATED_PR%%DTE_LastUpdated", 1) Then
                    if (Convert.ToString(doForm.doRS.GetFieldVal("DTE_LastContactDate", 1)) != "" & doForm.doRS.GetFieldVal("DTE_LASTCONTACTDATE", 1) != doPRRS.GetFieldVal("DTE_LastUpdated", 1))
                    {
                        // doForm.MessageBox("Would you like to update the bid date, next action date, and last modified date for the linked project " & doForm.doRS.GetFieldVal("LNK_RELATED_PR%%SYS_NAME") & "?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "AC_FormOnSave_Post_ProjectDates")
                        doForm.MessageBox("Would you like to update the bid date, next action date, and last modified date for the linked project " + doPRRS.GetFieldVal("SYS_NAME") + "?", clC.SELL_MB_YESNOCANCEL, null, null, null, null, null, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", null, "AC_FormOnSave_Post_ProjectDates");
                        return true;
                    }
                }
                doPRRS = null;
            }

            doForm.oVar.SetVar("UpdateDates", "");

            // TLD 8/12/2008 Copied from main, customer wants NO subject in Notes field
            // ----- Subject in correspondence ---------
            // Subject can be blank, but if it's not, the first line of the Notes field
            // must be the same as the subject or the subject is here prepended to the Notes.
            // This is so that the Notes field accurately reflects the Subject for correspondence
            // Activities.
            string sNotes;
            string sSubject;
            long lType;
            bool bIsCorr = true;
            string sTemp;
            string sWork;
            lType = Convert.ToInt64(doForm.doRS.GetFieldVal("MLS_Type", 2));
            switch (lType)
            {
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                case 8:
                case 9:
                case 10        // Correspondence
               :
                    {
                        bIsCorr = true;
                        break;
                    }
            }
            sNotes = Strings.Trim(doForm.doRS.GetFieldVal("MMO_Notes").ToString());
            sSubject = Strings.Trim(doForm.doRS.GetFieldVal("TXT_SUBJ").ToString());
            if (bIsCorr)
            {
                if (sSubject != "")
                {
                    // If subject = notes, set notes to nothing
                    if (sNotes == sSubject)
                    {
                        sNotes = "";
                        doForm.doRS.SetFieldVal("MMO_NOTES", sNotes);
                    }
                    else
                    {
                        // removes subject from notes
                        if (sNotes != "")
                        {
                            sTemp = Strings.InStr(sNotes, Constants.vbCrLf).ToString();
                            if (Convert.ToInt32(sTemp) > 0)
                            {
                                sWork = Strings.Mid(sNotes, 1, Convert.ToInt32(sTemp) - 1);
                                if (sWork == sSubject)
                                    // TLD 8/14/2008 Changed from sTemp + 2 to STemp + 3 to eliminate blank line
                                    // seems to always get here with fixed number of vbcrlfs
                                    sNotes = goTR.FromTo(sNotes, Convert.ToInt32(sTemp) + 3);
                            }
                        }
                        if (Convert.ToString(goP.GetVar("lMemoLimit")) != "")
                        {
                            if (Convert.ToInt32(Strings.Len(sNotes)) > Convert.ToInt32(goP.GetVar("lMemoLimit")))
                            {
                                doForm.MoveToTab(2);     // Notes
                                doForm.MoveToField("MMO_Notes");
                                goErr.SetWarning(30200, sProc, "", "Please remove some text from the Notes field so that the Subject can be prepended to it.", "", "", "", "", "", "", "", "", "MMO_notes");
                                return false;
                            }
                        }
                        else
                            doForm.doRS.SetFieldVal("MMO_NOTES", sNotes);
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool Activity_FillAddress_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Fill Address from Company if linked Contact does not have an Address OR
            // if there is no linked contact
            Form doForm = (Form)par_doCallingObject;

            try
            {

                string sWork;
                string sAddress = "";
                int lType = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Type", 2));
                string sWork1 = "";

                // Check if linked Contact has an address; if not, fill from Company

                if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_Address")) != "")
                    return true;

                // TLD 9/2/2009 Store AC CO info in VAR to use instead of creating rowsets throughout.
                // But not sure if they use it -- address field is hidden on form
                // If no CN and 1 CO, fill VAR from CO
                if (doForm.doRS.GetLinkCount("LNK_RELATED_CN") == 0 & doForm.doRS.GetLinkCount("LNK_RELATED_CO") == 1)
                {
                    clRowSet doCO = new clRowSet("CO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_RELATED_CO") + "'", null, "TXT_ADDRMAILING, TXT_CITYMAILING, TXT_STATEMAILING, TXT_ZIPMAILING, TXT_COUNTRYMAILING");
                    if (doCO.GetFirst() == 1)
                    {
                        goTR.StrWrite(ref sWork1, "CO_TXT_ADDRMAILING", doCO.GetFieldVal("TXT_ADDRMAILING"));
                        goTR.StrWrite(ref sWork1, "CO_TXT_CITYMAILING", doCO.GetFieldVal("TXT_CITYMAILING"));
                        goTR.StrWrite(ref sWork1, "CO_TXT_STATEMAILING", doCO.GetFieldVal("TXT_STATEMAILING"));
                        goTR.StrWrite(ref sWork1, "CO_TXT_ZIPMAILING", doCO.GetFieldVal("TXT_ZIPMAILING"));
                        goTR.StrWrite(ref sWork1, "CO_TXT_COUNTRYMAILING", doCO.GetFieldVal("TXT_COUNTRYMAILING"));
                    }
                }
                else if (doForm.doRS.GetLinkCount("LNK_RELATED_CN") == 1 & Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_CN%%TXT_ADDRBUSINESS")) == "" & doForm.doRS.GetLinkCount("LNK_RELATED_CO") == 1)
                {
                    clRowSet doCO = new clRowSet("CO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_RELATED_CO") + "'", null, "TXT_ADDRMAILING, TXT_CITYMAILING, TXT_STATEMAILING, TXT_ZIPMAILING, TXT_COUNTRYMAILING");
                    if (doCO.GetFirst() == 1)
                    {
                        goTR.StrWrite(ref sWork1, "CO_TXT_ADDRMAILING", doCO.GetFieldVal("TXT_ADDRMAILING"));
                        goTR.StrWrite(ref sWork1, "CO_TXT_CITYMAILING", doCO.GetFieldVal("TXT_CITYMAILING"));
                        goTR.StrWrite(ref sWork1, "CO_TXT_STATEMAILING", doCO.GetFieldVal("TXT_STATEMAILING"));
                        goTR.StrWrite(ref sWork1, "CO_TXT_ZIPMAILING", doCO.GetFieldVal("TXT_ZIPMAILING"));
                        goTR.StrWrite(ref sWork1, "CO_TXT_COUNTRYMAILING", doCO.GetFieldVal("TXT_COUNTRYMAILING"));
                    }
                }

                switch (lType)
                {
                    case 4:
                    case 8 // Letter Sent, Fax Sent
                   :
                        {
                            // If no CN and 1 CO, fill from Company
                            if (doForm.doRS.GetLinkCount("LNK_RELATED_CN") == 0 & doForm.doRS.GetLinkCount("LNK_RELATED_CO") == 1)
                            {
                                sWork = "";
                                // TLD 9/2/2009 Uses VAR
                                if (sWork1 == "")
                                {
                                    sWork = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_ADDRMAILING"));

                                }
                                else
                                {
                                    sWork = goTR.StrRead(sWork1, "CO_TXT_ADDRMAILING");

                                }
                                if (sWork != "")
                                {
                                    sAddress = sWork;

                                }
                                if (sWork1 == "")
                                {
                                    sWork = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_CITYMAILING"));

                                }
                                else
                                {
                                    sWork = goTR.StrRead(sWork1, "CO_TXT_CITYMAILING");

                                }
                                if (sWork != "")
                                {
                                    sAddress = sAddress + Constants.vbCrLf + sWork;

                                }
                                if (sWork1 == "")
                                {
                                    sWork = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_STATEMAILING"));

                                }
                                else
                                {
                                    sWork = goTR.StrRead(sWork1, "CO_TXT_STATEMAILING");

                                }
                                if (sWork != "")
                                {
                                    sAddress = sAddress + ", " + sWork;

                                }
                                if (sWork1 == "")
                                {
                                    sWork = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_ZIPMAILING"));

                                }
                                else
                                {
                                    sWork = goTR.StrRead(sWork1, "CO_TXT_ZIPMAILING");

                                }
                                if (sWork != "")
                                {
                                    sAddress = sAddress + " " + sWork;

                                }
                                if (sWork1 == "")
                                {
                                    sWork = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_COUNTRYMAILING"));

                                }
                                else
                                {
                                    sWork = goTR.StrRead(sWork1, "CO_TXT_COUNTRYMAILING");

                                }
                                if (sWork != "")
                                {
                                    sAddress = sAddress + Constants.vbCrLf + sWork;

                                }
                                doForm.doRS.SetFieldVal("TXT_Address", sAddress);
                            }
                            else if (doForm.doRS.GetLinkCount("LNK_RELATED_CN") == 1 & Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_CN%%TXT_ADDRBUSINESS")) == "" & doForm.doRS.GetLinkCount("LNK_RELATED_CO") == 1)
                            {
                                // If 1 CN (w/out an Address) and 1 CO
                                sWork = "";
                                // TLD 9/2/2009 Uses VAR
                                if (sWork1 == "")
                                {
                                    sWork = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_ADDRMAILING"));

                                }
                                else
                                {
                                    sWork = goTR.StrRead(sWork1, "CO_TXT_ADDRMAILING");

                                }
                                if (sWork != "")
                                {
                                    sAddress = sWork;

                                }
                                if (sWork1 == "")
                                {
                                    sWork = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_CITYMAILING"));

                                }
                                else
                                {
                                    sWork = goTR.StrRead(sWork1, "CO_TXT_CITYMAILING");

                                }
                                if (sWork != "")
                                {
                                    sAddress = sAddress + Constants.vbCrLf + sWork;

                                }
                                if (sWork1 == "")
                                {
                                    sWork = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_STATEMAILING"));

                                }
                                else
                                {
                                    sWork = goTR.StrRead(sWork1, "CO_TXT_STATEMAILING");

                                }
                                if (sWork != "")
                                {
                                    sAddress = sAddress + ", " + sWork;

                                }
                                if (sWork1 == "")
                                {
                                    sWork = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_ZIPMAILING"));

                                }
                                else
                                {
                                    sWork = goTR.StrRead(sWork1, "CO_TXT_ZIPMAILING");

                                }
                                if (sWork != "")
                                {
                                    sAddress = sAddress + " " + sWork;

                                }
                                if (sWork1 == "")
                                {
                                    sWork = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_COUNTRYMAILING"));

                                }
                                else
                                {
                                    sWork = goTR.StrRead(sWork1, "CO_TXT_COUNTRYMAILING");

                                }
                                if (sWork != "")
                                {
                                    sAddress = sAddress + Constants.vbCrLf + sWork;

                                }
                                doForm.doRS.SetFieldVal("TXT_Address", sAddress);
                            }
                            else
                            {
                            }

                            break;
                        }
                }


            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool Activity_FillFax_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm
            // par_doArray: Unused
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sFaxNo;

            try
            {

                // Try to fill the Fax field with the linked Company's Fax field if it is
                // blank. This happens if core function failed to fill it.
                if (Convert.ToString(doForm.doRS.GetFieldVal("TEL_FAX")) == "")
                {
                    if (doForm.doRS.GetLinkCount("LNK_Related_CO") == 1)
                    {
                        switch (doForm.doRS.GetFieldVal("MLS_Type", 2))
                        {
                            case 8      // Fax Sent
                           :
                                {
                                    sFaxNo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_CO%%TEL_FAXNO"));
                                    if (sFaxNo != "")
                                    {
                                        doForm.doRS.SetFieldVal("TEL_Fax", sFaxNo);
                                        return true;
                                    }

                                    break;
                                }
                        }
                    }
                }


            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool Activity_FillRe_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 8/14/2008 Prevents Activity_FillRe from running in main
            // Custom does NOT want subject copied to Notes field for correspondence
            par_bRunNext = false;

            return true;
        }
        public bool BC_FormControlOnChange_BTN_CalcMargin(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sReturn = "";

            try
            {
                scriptManager.RunScript("BidCost_CalcMargin", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray, "", "", "", "", "");
                if (sReturn != "")
                    doForm.doRS.SetFieldVal("CUR_PROFIT", sReturn);
            }

            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool BC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            try
            {

                // If user doesn't have permissions to edit Bid Item Costs, gray out save.
                clRowSet doRSUs = new clRowSet("US", 3, "GID_ID=" + goP.GetMe("ID") + "", null, "CHK_BIDCOSTPERMISSION");
                if (doRSUs.GetFirst() == 1)
                {
                    if (Convert.ToInt32(doRSUs.GetFieldVal("CHK_BIDCOSTPERMISSION", 2)) != 1)
                    {
                        doForm.SetControlState("BTN_Save", 4);

                    }
                }
            }

            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }

            // TLD 9/5/2008 Fill Bid Item from previous record if new record
            string sID;
            string sFile;

            if (doForm.GetMode() == "CREATION")
            {
                sID = goUI.GetLastSelected("SELECTEDRECORDID");
                sFile = goUI.GetLastSelected("SELECTEDRECORDFILE");
                if (sID != "" | sID != null)
                {
                    if (sFile == "BI")
                        doForm.doRS.SetFieldVal("LNK_RELATED_BI", sID);
                }
            }
            par_doCallingObject = doForm;

            return true;
        }

        public bool BC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sReturn = "";

            try
            {
                doRS.SetFieldVal("TXT_ProjectID", doRS.GetFieldVal("LNK_RELATED_BI%%TXT_ProjectID"));

                // Calc margin
                scriptManager.RunScript("BidCost_CalcMargin", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray, "", "", "", "", "");
                doRS.SetFieldVal("CUR_PROFIT", sReturn);
            }
            // TLD 09/17/2008 Somehow this was causing the CUR_PROFIT line to calc
            // to 0 here and in the BTN_CALCMarg, so added line above and tested
            // doRS.SetFieldVal("CUR_PROFIT", goTR.StringToNum(sReturn))


            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool BI_FormControlOnChange_LNK_RELATED_PR(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            try
            {
                if (doForm.GetMode() != "CREATION")
                    return true;
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("SR__SIZE")) == 0)
                {
                    doForm.doRS.SetFieldVal("SR__SIZE", doForm.doRS.GetFieldVal("LNK_RELATEd_PR%%SR__SIZE"));
                    doForm.doRS.SetFieldVal("MLS_UNITS", doForm.doRS.GetFieldVal("LNK_RELATED_PR%%MLS_UNITS"));
                }
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool BI_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // Dim doRS As clRowSet

            try
            {

                // If user doesn't have permissions to edit Bid Items, gray out save.
                clRowSet doRSUs = new clRowSet("US", 3, "GID_ID=" + goP.GetMe("ID") + "", null, "CHK_BIDITEMPERMISSION");
                if (doRSUs.GetFirst() == 1)
                {
                    if (Convert.ToInt32(doRSUs.GetFieldVal("CHK_BIDITEMPERMISSION", 2)) != 1)
                    {
                        doForm.SetControlState("BTN_Save", 4);

                    }
                }
            }

            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }

            // TLD 8/12/2008 if new and last record is project, copy project
            string sID;
            string sFile;

            if (doForm.GetMode() == "CREATION")
            {
                sID = goUI.GetLastSelected("SELECTEDRECORDID");
                sFile = goUI.GetLastSelected("SELECTEDRECORDFILE");
                if (sID != "" | sID != null)
                {
                    if (sFile == "PR")
                    {
                        doForm.doRS.SetFieldVal("LNK_RELATED_PR", sID);

                    }
                }
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool BI_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            try
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_AWARDED", 2)) == 1)
                {
                    if (Convert.ToString(doForm.doRS.GetFieldVal("DTT_AWARDEDDATE")) == "")
                    {
                        doForm.MoveToField("DTE_AWARDEDDATE");
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("BI", "DTT_AWARDEDDATE"), "", "", "", "", "", "", "", "", "DTT_AWARDEDDATE");
                        return false;
                    }
                }
            }

            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool BidCost_CalcMargin(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            decimal dProfit = default(decimal);
            decimal dPrice;
            decimal dCost;

            // Calcs Margin (CUR_PROFIT)

            try
            {
                if (doRS.GetLinkCount("LNK_RELATED_BI") == 1)
                {
                    dPrice = Convert.ToDecimal(doRS.GetFieldVal("LNK_RELATED_BI%%CUR_PRICE"));
                    dCost = Convert.ToDecimal(doRS.GetFieldVal("CUR_COST"));
                    if (dPrice > 0)
                        dProfit = dPrice - dCost;
                    else
                        dProfit = 0;
                }

                par_oReturn = goTR.CurrToString(dProfit, "", ref par_iValid, "");
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doRS;

            return true;
        }

        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "0", string par_s4 = "0", string par_s5 = "0")
        {
            // par_doCallingObject: Optional: editable clRowset of the Quote header record. If passed, 
            // this rowset is updated and the record on disk is not touched. If not passed,
            // the record on disk is updated. in this case par_s1 (Quote ID) is mandatory.
            // par_doArray: Unused.
            // par_s1: GID_ID of the Quote to calculate. Mandatory even when par_doCallingObject is passed.
            // par_s2: Optional: ID of the Quote Line for which to take amounts from par_s3 and par_s4
            // instead of from disk. This is to allow recalculating the quote from a Quote Line
            // which hasn't been saved yet. If "", all quote lines are read from disk and par_s3 and par_s4
            // are ignored.
            // par_s3: Mandatory only if par_s2 <> "". CHK_TAXABLE value (1 or 0) from Quote Line (ID passed via par_s2). 
            // Use goTr.CheckboxToString(doRS.GetFieldVal("CHK_TAXABLE", 2)) to get it in this format.
            // par_s4: mandatory only is par_s2 <> "". CUR_SUBTOTAL as unformatted number from Quote Line (ID passed via par_s2).
            // Use goTr.CurrToString(doRS.GetFieldVal("CUR_SUBTOTAL", 2)) to get it in this format.
            // par_s5: Unused.

            // Old NGP parameters:
            // par_s1: ID of the Quote. If blank, all parameters are read from global variables named QUOTE_<FieldName>
            // and the Quote is saved on disk.
            // par_s2: System-format value of Quote's CHK_INCLUDETAXCHARGES as boolean: 1 (default) or 0.
            // par_s3: System-format value of Quote's SR__SALESTAXPERCENT as single real. Default is '0'.
            // par_s4: System-format value of Quote's CUR_OTHERCHARGE as currency. Default is '0'.
            // par_s5: System-format value of Quote's CUR_SHIPPING as currency. Default is '0'.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/10/2014 Added
            par_bRunNext = false;

            // MI 2/23/07 Mods
            // PURPOSE:
            // Calculate totals of a Quote from Quote Lines independent of the form context.
            // RETURNS:
            // True if successful, False if not with SetError. Returns calculation results
            // via gop:SetVar() in the following variables:
            // CUR_SUBTOTAL
            // CUR_SUBTOTALT
            // CUR_SALESTAX
            // CUR_TOTAL	

            clRowSet doLines;   // 
            int lI;
            decimal cWork = default(decimal);        // Non-taxable subtotals
            decimal cWorkT = default(decimal);       // Taxable subtotals only
            double rTaxPercent;
            decimal cTaxAmount;
            decimal cOtherCharge;
            decimal cTotalAmount;
            decimal cShipAmount;
            // Dim s1 As String = par_s1   'Quote GID_ID
            // Dim s2 As String = par_s2   'Quote Line GID_ID 
            // Dim s3 As String = par_s3
            // Dim s4 As String = par_s4
            // Dim s5 As String = par_s5
            clRowSet doRS = (clRowSet)par_doCallingObject;      // Quote rowset
            bool bCommit = false;
            bool bQLTaxable = true;
            decimal cQLSubtotal = default(decimal);
            bool bQLFound = false;
            string sFiles = ""; // FIL_INCLUSIONS from Quote Line Models
            bool bUpdInclusions = false;
            // TLD 6/10/2014 Added
            decimal cAmountThisPeriod = 0;
            decimal cAmountPaidPrevious = 0;
            double rHoldBack = default(double);
            decimal cGrossAmountDue = default(decimal);
            decimal cHoldBackAmount = 0;
            // TLD 6/12/2014 Added
            decimal cBudget = 0;

            // Vars are:
            // s1 = QT GID_ID
            // s2 = QL GID_ID
            // s3 = QL CHK_TAXABLE
            // s4 = QL CUR_Subtotal

            // Vars used to be:
            // s1 = goP.GetVar("QUOTE_GID_ID")
            // s2 = goP.GetVar("QUOTE_CHK_INCLUDETAXCHARGES")
            // s3 = goP.GetVar("QUOTE_SR__SALESTAXPERCENT")
            // s4 = goP.GetVar("QUOTE_CUR_OTHERCHARGE")
            // s5 = goP.GetVar("QUOTE_CUR_SHIPPING")
            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)



            // ------------- Validate parameters ----------------
            if (!goData.IsFileValid(goTR.GetFileFromSUID(par_s1.ToString())))
            {
                goErr.SetError(10100, sProc, null, goTR.GetFileFromSUID(par_s1), "File extracted from SUID in par_s1: '" + par_s1 + "'. Be sure to pass the GID_ID value of the Quote to recalculate.");
                // 10100: Invalid file name '[1]'. [2]
                return false;
            }
            if (par_s2.ToString() == "")
            {
                // Override QL ID not provided - ignore the rest of QL parameters
                par_s3 = "";
                par_s4 = "";
            }
            else
            {
                // Quote Line GID_ID was passed
                // QL's CHK_Taxable value
                if (par_s3.ToString() == "")
                {
                    goErr.SetError(35000, sProc, "par_s3 is blank. QL's CHK_Taxable value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
                    return false;
                }
                else
                    bQLTaxable = goTR.StringToCheckbox(par_s3, false, ref par_iValid);
                // QL's CUR_Subtotal value
                if (par_s4.ToString() == "")
                {
                    goErr.SetError(35000, sProc, "par_s4 is blank. QL's CUR_Subtotal value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
                    return false;
                }
                else
                    cQLSubtotal = Convert.ToDecimal(par_s4);
            }


            // -------------- Read Lines and calculate their amounts ------------
            // TLD 6/12/2014 Added
            // TLD 6/10/2014 Added CUR_Cost to RS
            // CS 12/2/08: Check if MO.FIL_INCLUSIONS exists. If so need to get it in the QL RS below
            if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                doLines = new clRowSet("QL", 3, "LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS, CUR_AmountThisPeriod, CUR_AmountPaidPrevious, MLS_Status, CUR_Cost");
            else
                doLines = new clRowSet("QL", 3, "LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, CUR_AmountThisPeriod, CUR_AmountPaidPrevious, MLS_Status, CUR_Cost");
            // Browse through the rowset
            lI = 1;
            if (doLines.GetFirst() == 1)
            {
                do
                {
                    // Add up Quote Lines. Skip the one for which GID_ID is passed via par_s2
                    if (par_s2.ToString() == "" | Strings.UCase(par_s2) != Strings.UCase(doLines.GetFieldVal("GID_ID", 1).ToString()))
                    {
                        if (Convert.ToInt32(doLines.GetFieldVal("CHK_TAXABLE", 2)) == 1)
                        {
                            cWorkT += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));

                        }
                        else
                        {
                            cWork += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));

                        }
                        // CS 12/2/08: Get value from QL%%MO FIL_INCLUSIONS field
                        if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                        {
                            // Check if the file has already been added
                            if (goTR.Position(sFiles, Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"))) == 0)
                            {
                                // If this is the first file don't add a vbcrlf in front of it
                                if (sFiles == "")
                                    sFiles = Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"));
                                else
                                    sFiles = sFiles + Constants.vbCrLf + doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS");
                            }
                        }
                    }
                    // TLD 6/10/2014 Added to sum fields when status is Won
                    if (Convert.ToInt32(doLines.GetFieldVal("MLS_Status", 2)) == 2)
                    {
                        cAmountThisPeriod += Convert.ToDecimal(doLines.GetFieldVal("CUR_AmountThisPeriod", 2));
                        cAmountPaidPrevious += Convert.ToDecimal(doLines.GetFieldVal("CUR_AmountPaidPrevious", 2));
                        // TLD 6/12/2014 Added to sum cost for QT Budget
                        cBudget += Convert.ToDecimal(doLines.GetFieldVal("CUR_Cost", 2));
                    }

                    if (doLines.GetNext() == 0)
                        break;
                    lI += 1;
                }
                while (true);
            }
            // delete(doLines)
            doLines = null;

            // Add the Quote Line passed via parameters
            if (par_s2 != "")
            {
                // CS 12/31/08: Get Fil_Inclusions of QL passed via parameter
                // This code needs to be run if you open a QL directly from
                // a QL view and it has file inclusions
                // CS 1/8/09: Check if FIL_INCLUSIONS exist in db first.
                if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                {
                    doLines = new clRowSet("QL", 3, "GID_ID='" + par_s2.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS");
                    if (doLines.GetFirst() == 1)
                    {
                        // If goData.IsFieldValid("MO", "FIL_INCLUSIONS") Then
                        // Check if the file has already been added
                        if (goTR.Position(sFiles, Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"))) == 0)
                        {
                            // If this is the first file don't add a vbcrlf in front of it
                            if (sFiles == "")
                                sFiles = Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"));
                            else
                                sFiles = sFiles + Constants.vbCrLf + doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS");
                        }
                    }
                }

                if (bQLTaxable == true)
                {
                    cWorkT += cQLSubtotal;

                }
                else
                {
                    cWork += cQLSubtotal;

                }
            }

            // Subtotal = cWork + cWorkT
            // SubtotalT = cWorkT

            // ---------- Pull up the Quote -----------
            if (doRS == null)
            {
                // Get the quote from disk
                bCommit = true;
                // doRS = New clRowSet("QT", 1, _
                // "GID_ID='" & par_s1 & "'", _
                // "DTT_TIME ASC", _
                // "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL")
                // CS 7/26/07: Currently if you open a QT that has a required field missing
                // such as NA date, edit a QL and save the QL you get an error. Trying to avoid
                // that by bypassing validation.
                doRS = new clRowSet("QT", 1, "GID_ID='" + par_s1 + "'", "DTT_TIME ASC", "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL", 1, null, null, null, null, null, true);
            }
            else
                // Validate the passed Rowset object
                if (Strings.UCase(doRS.GetFileName()) != "QT")
            {
                goErr.SetError(35000, sProc, "The file of the rowset in par_doCallingObject parameter is not QT (Quote). Either pass a Quote rowset or only pass the Quote GID_ID in par_s1 parameter.");
                return false;
            }

            // ----------- Read Quote data and calculate -------------
            if (doRS.GetFirst() == 1)
            {
                // CS 12/2/08: Get the value of the 'Do not update inclusions' on QT
                // If checked, do not update FIL_INCLUSIONS field on QT
                if (goData.IsFieldValid("QT", "CHK_NOUPDINCLUSIONS"))
                {
                    if (Convert.ToInt32(doRS.GetFieldVal("CHK_NOUPDINCLUSIONS", 2)) == 0)
                    {
                        bUpdInclusions = true;

                    }
                }
                rTaxPercent = Convert.ToDouble(doRS.GetFieldVal("SR__SALESTAXPERCENT", clC.SELL_SYSTEM));    // s3
                cTaxAmount = Convert.ToDecimal(cWorkT) * Convert.ToDecimal(rTaxPercent) / 100;     // cTaxAmount goes into CUR_SALESTAX
                                                                                                   // If the 'Include Tax/Charges' check-box is not checked, do not add tax,
                                                                                                   // other charge and shipping to Total. 
                if (Convert.ToInt32(doRS.GetFieldVal("CHK_INCLUDETAXCHARGES", clC.SELL_SYSTEM)) == 1)
                {
                    cOtherCharge = Convert.ToDecimal(doRS.GetFieldVal("CUR_OTHERCHARGE", clC.SELL_SYSTEM));   // s4
                    cShipAmount = Convert.ToDecimal(doRS.GetFieldVal("CUR_SHIPPING", clC.SELL_SYSTEM));   // s5
                                                                                                          // cTotalAmount goes to CUR_TOTAL
                    cTotalAmount = cWork + cWorkT + cTaxAmount + cOtherCharge + cShipAmount;
                }
                else
                    // cTotalAmount goes to CUR_TOTAL
                    cTotalAmount = cWork + cWorkT;
            }
            else
            {
                // goP.TraceLine("doRS GetFirst not found", "", sProc)
                doRS = null/* TODO Change to default(_) if this is not a reference type */;
                goErr.SetError(30032, sProc, "", "Quote");
                // The linked [1] can't be updated because it can't be found. 
                // goP.TraceLine("Return False", "", sProc)
                return false;
            }

            // --------------- Update calculated fields ----------------
            doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cWork + cWorkT), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_SUBTOTALT", goTR.RoundCurr(cWorkT), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_SALESTAX", goTR.RoundCurr(cTaxAmount), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_TOTAL", goTR.RoundCurr(cTotalAmount), clC.SELL_SYSTEM);
            // TLD 6/10/2014 Added
            doRS.SetFieldVal("CUR_AmountThisPeriod", goTR.RoundCurr(cAmountThisPeriod), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_AmountPaidPrevious", goTR.RoundCurr(cAmountPaidPrevious), clC.SELL_SYSTEM);
            cGrossAmountDue = cAmountThisPeriod + cAmountPaidPrevious;
            doRS.SetFieldVal("CUR_GrossAmountDue", goTR.RoundCurr(cGrossAmountDue), clC.SELL_SYSTEM);
            rHoldBack = Convert.ToDouble(doRS.GetFieldVal("SR__HoldBack", 2));
            //decimal _do = (Convert.ToDecimal(rHoldBack)) * (Convert.ToDecimal(cGrossAmountDue));
            cHoldBackAmount = (Convert.ToDecimal(rHoldBack)) * (Convert.ToDecimal(cGrossAmountDue)) * Convert.ToDecimal(0.01);
            doRS.SetFieldVal("CUR_HoldBackAmount", goTR.RoundCurr(cHoldBackAmount), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_AmountDueToDate", goTR.RoundCurr(cGrossAmountDue - cHoldBackAmount), clC.SELL_SYSTEM);
            // TLD 6/12/2014 Added
            doRS.SetFieldVal("CUR_Budget", goTR.RoundCurr(cBudget), clC.SELL_SYSTEM);

            // --------------Update FIL_Inclusions
            if (bUpdInclusions == true)
                doRS.SetFieldVal("FIL_INCLUSIONS", sFiles);

            // --------------- Save the Quote ---------------
            if (bCommit)
            {
                goP.SetVar("bDoNotUpdateQuoteLines", "1");
                // CS 11/5/09: Setting a variable here to let me know NOT to try to update the Qt total again in QT_RecOnSave. Issue was that if
                // you open a QT, add a Quote Line and then cancel out of the QT, the QT total did not reflect the actual total. This
                // was because CalcQuotetotal was being called here and then again in QT_RecordOnSave. We do NOT want it to be called in QT
                // RecOnSave if it was called here.
                goP.SetVar("bDoNotRecalcQuoteTotal", "1");
                // Save to disk
                if (doRS.Commit() != 1)
                {
                    // goP.TraceLine("Commit failed, raising error", "", sProc)
                    goP.DeleteVar("bDoNotUpdateQuoteLines");
                    // CS 11/6/09: Set in Ql_RecordOnSave
                    goP.DeleteVar("bDoNotRecalcQuoteTotal");
                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                    goErr.SetError(35000, sProc, "Error updating the Quote '" + par_s1 + "'."); // CS
                                                                                                // goP.TraceLine("Return False", "", sProc)
                    return false;
                }
                else
                {
                    goP.DeleteVar("bDoNotUpdateQuoteLines");
                    goP.DeleteVar("bDoNotRecalcQuoteTotal"); // CS 11/6/09
                    if (!(doRS == null))
                        doRS = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)
            par_doCallingObject = doRS;
            return true;
        }
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/19/2014 Disable Merged
            doForm.SetControlState("CHK_Merged", 4);
            par_doCallingObject = doForm;
            return true;
        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/19/2014 Click cancel on merge
            if (doForm.oVar.GetVar("CancelSave") == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/19/2014 Merge Functionality - run at end of CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CN_Merge")) != "1")
                    {
                        // Don't allow merge of contact to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID"))
                        {
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null, null, null, null, null, "MessageBoxEvent", null, null, doForm, null, "OK", null, null, "CN", "MergeFail");

                        }
                        else
                        {
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null, null, null, null, null, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CN", "Merge");

                        }
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.MoveToTab(2);
            par_doCallingObject = doForm;

            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 12/9/2008 Defaults to Addresses Tab
            doForm.MoveToTab(3); // Addresses

            // TLD 6/19/2014 Disable Merged
            doForm.SetControlState("CHK_Merged", 4);
            par_doCallingObject = doForm;

            return true;
        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;


            // TLD 6/19/2014 Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/19/2014 Merge Functionality - run at end of CO_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CO_Merge")) != "1")
                    {
                        // Don't allow merge of company to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID"))
                        {
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null, null, null, null, null, "MessageBoxEvent", null, null, doForm, null, "OK", null, null, "CO", "MergeFail");

                        }
                        else
                        {
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null, null, null, null, null, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CO", "Merge");

                        }
                    }
                }
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool COMPANYFIND_FormControlOnChange_BTN_FIND(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form oForm = (Form)par_doCallingObject;

            string sCompName;
            string sCompCity;
            string sCompState;
            string sType;
            int iType = default(int);
            string sTypeIndex;

            string sView;
            int iCondCount;
            string sViewCondition;
            string sNewCondition;
            int iOrigCondCount;
            int i;
            string sFilter = "";

            // Get values from form
            // TLD 2/27/2013 Error searching for A.V. Anderson, so added SQL
            sCompName = Strings.Trim(oForm.GetControlVal("NDB_TXT_COMPANYNAME"));
            if (sCompName != "")
            {
                sCompName = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCompName), "TXT_COMPANYNAME", "CO", true);

            }
            sCompCity = Strings.Trim(oForm.GetControlVal("NDB_TXT_CITY"));
            if (sCompCity != "")
            {
                sCompCity = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCompCity), "TXT_CITYMAILING", "CO", true);

            }
            sCompState = Strings.Trim(oForm.GetControlVal("NDB_TXT_STATE"));
            if (sCompState != "")
            {
                sCompState = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCompState), "TXT_STATEMAILING", "CO", true);

            }
            sType = Strings.Trim(oForm.GetControlVal("NDB_TXT_TYPE"));
            // Get the integer value of the status
            if (Strings.UCase(sType) != "ANY")
            {
                clList oList = new clList();
                sTypeIndex = oList.LReadSeek("CO:TYPE", "VALUE", sType);
                iType = Convert.ToInt32(goTR.StringToNum(sTypeIndex, "", ref par_iValid, ""));
            }

            // Use values to filter Companies - Find desktop view if it exists
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_BE46EE5F-63B8-47F9-5858-9AC0014C7865");
            // Edit views in DT

            // View 1:Companies - Find Results
            sView = oDesktop.GetViewMetadata("VIE_E7BD039E-3855-4314-5858-9AC0014C7865");
            iCondCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"), "", ref par_iValid, ""));


            // If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            if (iCondCount == 1)
            {
                if (goTR.StrRead(sView, "C1FIELDNAME") == "<%ALL%>")
                    iCondCount = 0;// Will overwrite these values
            }
            // Original condition count
            iOrigCondCount = iCondCount;

            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (sCompName != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sCompCity != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sCompState != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (Strings.UCase(sType) != "ANY")
            {
                iCondCount = iCondCount + 1;

            }


            // Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (sCompName != "")
            {
                // Add project name condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_COMPANYNAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCompName);
                i = i + 1;
                sFilter = "TXT_COMPANYNAME[" + sCompName + "";
            }
            if (sCompCity != "")
            {
                // Add City condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_CITYMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCompCity);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_CITYMAILING[" + sCompCity + "";
                else
                    sFilter = "TXT_CITYMAILING[" + sCompCity + "";
            }
            if (sCompState != "")
            {
                // Add State condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_STATEMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCompState);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_STATEMAILING[" + sCompState + "";
                else
                    sFilter = "TXT_STATEMAILING[" + sCompState + "";
            }
            if (Strings.UCase(sType) != "ANY")
            {
                // Add type condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MLS_TYPE%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "="); // equals
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", iType);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND MLS_TYPE=" + iType + "";
                else
                    sFilter = "MLS_TYPE=" + iType + "";
            }

            // Edit CONDITION= line in view MD
            sViewCondition = goTR.StrRead(sView, "CONDITION");
            if (sViewCondition == "")
                sNewCondition = sFilter; // No filter in view already
            else
                sNewCondition = sViewCondition + "AND " + sFilter;
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_E7BD039E-3855-4314-5858-9AC0014C7865", sView);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;

            // Que Companies Find desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
            goUI.Queue("", "");
            par_doCallingObject = oForm;

            return true;
        }
        public bool COMPANYFIND_FormOnLoad_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Set the value of 'ANY' in the Type NDB field by default
            // This is typically a MLS field but we don't support NDB MLS fields
            // If I didn't do this I wouldn't be sure if they were trying to find a
            // blank status OR wanted to search for Companies with ANY status

            Form doForm = (Form)par_doCallingObject;

            doForm.SetControlVal("NDB_TXT_TYPE", "ANY");
            par_doCallingObject = doForm;

            return true;
        }
        public bool CONTACTFIND_FormControlOnChange_BTN_FIND(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form oForm = (Form)par_doCallingObject;

            string sLastName;
            string sFirstName;

            string sView;
            int iCondCount;
            string sViewCondition;
            string sNewCondition;
            int iOrigCondCount;
            int i;
            string sFilter = "";

            // Get values from form
            // TLD 2/27/2013 Added SQL
            sFirstName = Strings.Trim(oForm.GetControlVal("NDB_TXT_FIRSTNAME"));
            if (sFirstName != "")
            {
                sFirstName = goTR.ConvertStringForQS(goTR.PrepareForSQL(sFirstName), "TXT_NAMEFIRST", "CN", true);

            }
            sLastName = Strings.Trim(oForm.GetControlVal("NDB_TXT_LASTNAME"));
            if (sLastName != "")
            {
                sLastName = goTR.ConvertStringForQS(goTR.PrepareForSQL(sLastName), "TXT_NAMELAST", "CN", true);

            }

            // Use values to filter Contacts - Find desktop view if it exists
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_B0EEC3C8-439B-4CB7-5858-9AC001536F87");
            // Edit views in DT

            // View 1:Contacts - Find Results
            sView = oDesktop.GetViewMetadata("VIE_F8A62ECA-10BE-462C-5858-9AC001536F83");
            iCondCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"), "", ref par_iValid, ""));


            // If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            if (iCondCount == 1)
            {
                if (goTR.StrRead(sView, "C1FIELDNAME") == "<%ALL%>")
                    iCondCount = 0;// Will overwrite these values
            }
            // Original condition count
            iOrigCondCount = iCondCount;

            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (sFirstName != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sLastName != "")
            {
                iCondCount = iCondCount + 1;

            }

            // Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (sFirstName != "")
            {
                // Add first name condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_NAMEFIRST%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFirstName);
                i = i + 1;
                sFilter = "TXT_NAMEFIRST[" + sFirstName + "";
            }
            if (sLastName != "")
            {
                // Add last name condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_NAMELAST%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sLastName);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_NAMELAST[" + sLastName + "";

                }
                else
                {
                    sFilter = "TXT_NAMELAST[" + sLastName + "";

                }
            }

            // Edit CONDITION= line in view MD
            sViewCondition = goTR.StrRead(sView, "CONDITION");
            if (sViewCondition == "")
            {
                sNewCondition = sFilter; // No filter in view already

            }
            else
            {
                sNewCondition = sViewCondition + "AND " + sFilter;

            }
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_F8A62ECA-10BE-462C-5858-9AC001536F83", sView);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;

            // Que Contacts Find desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
            goUI.Queue("", "");
            par_doCallingObject = oForm;
            return true;
        }
        public bool FillCOCN(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 12/9/2008 Moved from AC_formonloadrecord_post and td_formonloadrecord_post
            // Since they do the same thing

            if (doForm.GetMode() == "CREATION")
            {
                clArray prRelCN = new clArray();
                clArray prRelCO = new clArray();
                clArray prInvCO = new clArray();
                clArray prInvCN = new clArray();
                string sID;
                string sFileName;

                prInvCN = doForm.doRS.GetLinkVal("LNK_RELATED_PR%%LNK_INVOLVED_CN", ref prInvCN, true, 0, -1, "A_a", ref oTable);
                prInvCO = doForm.doRS.GetLinkVal("LNK_RELATED_PR%%LNK_INVOLVED_CO", ref prInvCN, true, 0, -1, "A_a", ref oTable);
                prRelCN = doForm.doRS.GetLinkVal("LNK_RELATED_PR%%LNK_OriginatedBy_CN", ref prInvCN, true, 0, -1, "A_a", ref oTable);
                prRelCO = doForm.doRS.GetLinkVal("LNK_RELATED_PR%%LNK_For_CO", ref prInvCN, true, 0, -1, "A_a", ref oTable);

                sID = goUI.GetLastSelected("SELECTEDRECORDID");
                sFileName = goTR.GetFileFromSUID(sID);
                if (Strings.UCase(sFileName) == "PR")
                {
                    // TLD 11/10/2008 Customer changed their mind -- wants to ONLY fill LNK_RELATED_CN and LNK_RELATED_CO if Project
                    // from Involved CN and Involved CO
                    doForm.doRS.ClearLinkAll("lnk_related_cn");
                    doForm.doRS.SetLinkVal("LNK_RELATED_CN", prInvCN);
                    doForm.doRS.ClearLinkAll("lnk_related_co");
                    doForm.doRS.SetLinkVal("LNK_RELATED_CO", prInvCO);
                    doForm.doRS.ClearLinkAll("lnk_involves_cn");
                    doForm.doRS.ClearLinkAll("lnk_involves_co");
                }
                else
                {
                    // TLD 10/17/2008 Commented per customer -- they want create linked to fill from CN and CO when creating from other than PR
                    // doForm.doRS.ClearLinkAll("lnk_related_cn")
                    // doForm.doRS.ClearLinkAll("lnk_related_co")
                    // doForm.doRS.ClearLinkAll("lnk_involves_cn")
                    // doForm.doRS.ClearLinkAll("lnk_involves_co")
                    doForm.doRS.SetLinkVal("LNK_RELATED_CN", prInvCN);
                    doForm.doRS.SetLinkVal("LNK_RELATED_CO", prInvCO);
                    doForm.doRS.SetLinkVal("LNK_Involves_CN", prRelCN);
                    doForm.doRS.SetLinkVal("LNK_Involves_CO", prRelCO);
                }
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool FIND_FormControlOnChange_BTN_TKCancel_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/20/2014 Added for Tank
            par_bRunNext = false;

            Form oForm = (Form)par_doCallingObject;

            // Find dialog; Activity tab Cancel button
            // If click Cancel, close the form
            oForm.CloseOnReturn = true;
            par_doCallingObject = oForm;

            return true;
        }
        public bool FIND_FormControlOnChange_BTN_TKSearch_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            // CS 8/17/09: Do NOT consider already saved filter of the desktop.
            // CS 10/5/12: Added goTR.PrepareForSQL around string values
            // CS 10/10/12: ConvertStringForQS puts single quotes around the value, EVEN when the value is blank.
            // Because of this I need to check if the value is not "" and not "''" before assuming their is a value in the field.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/20/2014 Added for Tank
            par_bRunNext = false;

            Form oForm = (Form)par_doCallingObject;

            // Find dialog; Activity tab Search button
            string sName;
            string sOwner;
            string sLocation;
            string sType;

            string sView;
            int iCondCount = 0; // Cs 8/17/09
            string sNewCondition;
            int i = 0;
            string sFilter = "";

            // Get values from form
            sName = Strings.Trim(oForm.GetControlVal("NDB_TXT_TKName"));
            if (sName != "")
            {
                sName = goTR.ConvertStringForQS(goTR.PrepareForSQL(sName), "TXT_TankName", "TK", true);

            }
            sOwner = Strings.Trim(oForm.GetControlVal("NDB_TXT_TKOwner"));
            if (sOwner != "")
            {
                sOwner = goTR.ConvertStringForQS(goTR.PrepareForSQL(sOwner), "LNK_Owner_CO", "TK", true);

            }
            sLocation = Strings.Trim(oForm.GetControlVal("NDB_TXT_TKLocation"));
            if (sLocation != "")
            {
                sLocation = goTR.ConvertStringForQS(goTR.PrepareForSQL(sLocation), "MMO_Location", "TK", true);

            }
            sType = oForm.GetControlVal("NDB_MLS_TKType");

            // Use values to filter Actvity - Search Results desktop if it exists
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_516850A8-CDD9-41B8-5858-A35000CB655A");
            // Edit views in DT

            // View 1:Activities - Search Results
            sView = oDesktop.GetViewMetadata("VIE_5B9616D6-538D-4932-5858-A35000CB9421");
            // iCondCount = goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"))


            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (sName != "" & sName != "''")
            {
                iCondCount = iCondCount + 1;

            }
            if (sOwner != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sLocation != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sType != "0")
            {
                iCondCount = iCondCount + 1;

            }

            // Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            if (sName != "")
            {
                // Add 'Notes' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_TankName%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sName);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_TankName[" + sName + "";
                else
                    sFilter = "TXT_TankName[" + sName + "";
            }
            if (sOwner != "")
            {
                // Add Credited to User Sys Name condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_Owner_CO%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sOwner);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND LNK_Owner_CO%%SYS_NAME[" + sOwner + "";
                else
                    sFilter = "LNK_Owner_CO%%SYS_NAME[" + sOwner + "";
            }
            if (sLocation != "")
            {
                // Add MMO_Location
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MMO_Location%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sLocation);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND MMO_Location[" + sLocation + "";
                else
                    sFilter = "MMO_Location[" + sLocation + "";
            }
            if (sType != "0")
            {
                // Add Type condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MLS_TYPE%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "="); // Equals
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", goTR.StringToNum(sType, "", ref par_iValid, ""));
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND MLS_TYPE=" + goTR.StringToNum(sType, "", ref par_iValid, "") + "";
                else
                    sFilter = "MLS_TYPE=" + goTR.StringToNum(sType, "", ref par_iValid, "") + "";
            }

            sNewCondition = sFilter;
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_5B9616D6-538D-4932-5858-A35000CB9421", sView);
            sView = "";
            sNewCondition = "";
            iCondCount = 0;

            // Que Activity Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
            goUI.Queue("", "");
            par_doCallingObject = oForm;
            return true;
        }
        public bool GenerateSysName_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sFileName = doRS.GetFileName();
            string sResult = "";
            string sTemp;
            clRowSet doLink;
            string sTemp2;
            string sTemp3;
            string sTemp4 = "";
            string sTemp5;
            string sTemp6;
            string Left;
            string par_sDelim = "";

            try
            {
                switch (Strings.UCase(sFileName))
                {
                    case "BI":
                        {
                            if (!doRS.IsLoaded("LNK_Competing_VE"))
                            {
                                goErr.SetError(35103, sProc, null, sFileName + ".LNK_Competing_VE");

                            }
                            if (!doRS.IsLoaded("SR__SIZE"))
                            {
                                goErr.SetError(35103, sProc, null, sFileName + ".SR__SIZE");

                            }
                            if (!doRS.IsLoaded("MLS_UNITS"))
                            {
                                goErr.SetError(35103, sProc, null, sFileName + ".MLS_UNITS");

                            }
                            if (!doRS.IsLoaded("LNK_BIDFOR_SY"))
                            {
                                goErr.SetError(35103, sProc, null, sFileName + ".LNK_BIDFOR_SY");

                            }
                            // TLD 8/11/2008 Changed LNK_RELATED_PR to CUR_PRICE
                            if (!doRS.IsLoaded("CUR_PRICE"))
                            {
                                goErr.SetError(35103, sProc, null, sFileName + ".CUR_PRICE");

                            }
                            // Vendor
                            sTemp = Convert.ToString(doRS.GetFieldVal("LNK_Competing_VE", 0, 0, false, 1));
                            if (sTemp == "")
                            {
                            }
                            else
                                // Find the field value in the linked record
                                sTemp = Convert.ToString(doRS.GetFieldVal("LNK_Competing_VE%%SYS_Name", 0, 13));
                            // Bid For Style
                            // TLD 8/11/2008 Changed to use TXT_STYLENAME instead of sysname
                            sTemp2 = Convert.ToString(doRS.GetFieldVal("LNK_BidFor_SY%%TXT_STYLENAME", 0, -1, false, 1));
                            if (sTemp2 == "")
                            {
                            }
                            else
                                // Find the field value in the linked record
                                sTemp2 = Convert.ToString(doRS.GetFieldVal("LNK_BIDFOR_SY%%TXT_STYLENAME", 0, 12));
                            // Related Project: Region and ID Number
                            sTemp3 = Convert.ToString(doRS.GetFieldVal("LNK_Related_PR", 0, 0, false, 1));
                            if (sTemp3 == "")
                            {
                                // No records linked
                                sTemp3 = "";
                                sTemp4 = "";
                            }
                            else
                            {
                                // Find the field value in the linked record
                                doLink = new clRowSet("PR", 3, "GID_ID='" + sTemp3 + "'", null, "TXT_Region,TXT_IDNumber", 1);
                                if (doLink.Count() > 0)
                                {
                                    sTemp3 = Convert.ToString(doLink.GetFieldVal("TXT_Region"));
                                    sTemp4 = Convert.ToString(doLink.GetFieldVal("TXT_IDNumber"));
                                }
                            }

                            // TLD 12/17/2008 Add "AWD" to end if Awarded is checked
                            if (Convert.ToInt32(doRS.GetFieldVal("CHK_AWARDED", 2)) == 1)
                                sTemp5 = " AWD";
                            else
                                sTemp5 = "";

                            // TLD 8/11/2008 Removed space after size; removed project, added price
                            sResult = Strings.Left(sTemp + " " + doRS.GetFieldVal("SR__SIZE") + doRS.GetFieldVal("MLS_UNITS", 1) + " " + sTemp2 + " " + sTemp3 + " " + sTemp4 + " " + doRS.GetFieldVal("CUR_PRICE") + sTemp5, 80);
                            sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
                            sResult = goTR.Replace(sResult, Constants.vbTab, " ");
                            par_oReturn = sResult;
                            break;
                        }

                    case "BC":
                        {
                            // TLD 9/19/2008 Modified to include more fields
                            if (!doRS.IsLoaded("LNK_Related_BI"))
                                goErr.SetError(35103, sProc, null, sFileName + ".LNK_Related_BI");

                            // TLD 9/2/2009 Store BC BI info in var to use instead of creating rowsets throughout.
                            string sWork = "";
                            clRowSet doBI = new clRowSet("BI", 3, "GID_ID='" + doRS.GetFieldVal("LNK_RELATED_BI") + "'", null, "LNK_COMPETING_VE%%TXT_VENDORNAME, SR__SIZE, MLS_UNITS, LNK_BIDFOR_SY%%TXT_STYLENAME, LNK_RELATED_PR%%TXT_REGION, LNK_RELATED_PR%%TXT_IDNUMBER");
                            if (doBI.GetFirst() == 1)
                            {
                                goTR.StrWrite(ref sWork, "BI_LNK_COMPETING_VE_TXT_VENDORNAME", doBI.GetFieldVal("LNK_COMPETING_VE%%TXT_VENDORNAME", 0, 23));
                                goTR.StrWrite(ref sWork, "BI_SR__SIZE", doBI.GetFieldVal("SR__SIZE", 0, 5));
                                goTR.StrWrite(ref sWork, "BI_MLS_UNITS", doBI.GetFieldVal("MLS_UNITS", 1, 14));
                                goTR.StrWrite(ref sWork, "BI_LNK_BIDFOR_SY_TXT_STYLENAME", doBI.GetFieldVal("LNK_BIDFOR_SY%%TXT_STYLENAME", 0, 20));
                                goTR.StrWrite(ref sWork, "BI_LNK_RELATED_PR_TXT_REGION", doBI.GetFieldVal("LNK_RELATED_PR%%TXT_REGION", 0, 4));
                                goTR.StrWrite(ref sWork, "BI_LNK_RELATED_PR_TXT_IDNUMBER", doBI.GetFieldVal("LNK_RELATED_PR%%TXT_IDNUMBER", 0, 4));
                            }

                            // TLD 9/2/2009 Uses Var
                            if (sWork == "")
                            {
                                sTemp = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_BI%%LNK_COMPETING_VE%%TXT_VENDORNAME", 0, 23));

                            }
                            else
                            {
                                sTemp = goTR.StrRead(sWork, "BI_LNK_COMPETING_VE_TXT_VENDORNAME");

                            }
                            if (sTemp == "")
                            {
                                sTemp = "?";

                            }
                            // sResult = Left(sTemp & " " & doRS.GetFieldVal("CUR_COST") & " " & doRS.GetFieldVal("CUR_PROFIT"), 80)
                            // sResult = goTR.Replace(sResult, vbCrLf, " ")
                            // sResult = goTR.Replace(sResult, vbTab, " ")

                            if (sWork == "")
                            {
                                sTemp2 = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_BI%%SR__SIZE", 0, 5));

                            }
                            else
                            {
                                sTemp2 = goTR.StrRead(sWork, "BI_SR__SIZE");

                            }
                            if (sTemp2 == "")
                            {
                                sTemp2 = "?";

                            }

                            if (sWork == "")
                            {
                                sTemp3 = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_BI%%MLS_UNITS", 1, 14));

                            }
                            else
                            {
                                sTemp3 = goTR.StrRead(sWork, "BI_MLS_UNITS");

                            }
                            if (sTemp3 == "")
                            {
                                sTemp3 = "?";

                            }

                            if (sWork == "")
                            {
                                sTemp4 = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_BI%%LNK_BIDFOR_SY%%TXT_STYLENAME", 0, 20));

                            }
                            else
                            {
                                sTemp4 = goTR.StrRead(sWork, "BI_LNK_BIDFOR_SY_TXT_STYLENAME");

                            }
                            if (sTemp4 == "")
                                sTemp4 = "?";

                            if (sWork == "")
                                sTemp5 = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_BI%%LNK_RELATED_PR%%TXT_REGION", 0, 4));
                            else
                                sTemp5 = goTR.StrRead(sWork, "BI_LNK_RELATED_PR_TXT_REGION");
                            if (sTemp5 == "")
                                sTemp5 = "?";

                            if (sWork == "")
                                sTemp6 = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_BI%%LNK_RELATED_PR%%TXT_IDNUMBER", 0, 4));
                            else
                                sTemp6 = goTR.StrRead(sWork, "BI_LNK_RELATED_PR_TXT_IDNUMBER");
                            if (sTemp6 == "")
                                sTemp6 = "?";

                            // sTemp4 = doRS.GetFieldVal("CUR_COST", , 15)
                            // If sTemp4 = "" Then sTemp4 = "?"

                            // sTemp5 = doRS.GetFieldVal("CUR_PROFIT", , 15)
                            // If sTemp5 = "" Then sTemp5 = "?"

                            sResult = sTemp + " " + sTemp2 + " " + sTemp3 + " " + sTemp4 + " " + sTemp5 + " " + sTemp6 + " " + "BCI";
                            par_oReturn = sResult;
                            break;
                        }

                    case "PR":
                        {
                            if (!doRS.IsLoaded("TXT_ProjectName"))
                            {
                                goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_ProjectName");

                            }
                            // TLD 8/11/2008 Changed from DTT_Time to DTT_Q50
                            if (!doRS.IsLoaded("DTT_Q50"))
                            {
                                goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".DTT_Q50");

                            }
                            if (!doRS.IsLoaded("TXT_Region"))
                            {
                                goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_Region");

                            }
                            if (!doRS.IsLoaded("TXT_IdNumber"))
                            {
                                goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_IdNumber");

                            }
                            if (!doRS.IsLoaded("MLS_Phase"))
                            {
                                goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".MLS_Phase");

                            }

                            sTemp = Convert.ToString(doRS.GetFieldVal("MLS_PHASE", 2));
                            if (Convert.ToInt32(sTemp) == 0)
                                sTemp = "";
                            else
                                // TLD 8/11/2008 removed [] -- customer wants space only
                                sTemp = Convert.ToString(doRS.GetFieldVal("MLS_Phase", 1, 18));

                            // project Name (25)
                            // Date (15)
                            // Region (7)
                            // ID Number (10)
                            // Phase (13)

                            // Total: 79 or 80

                            // TLD 8/11/2008 Removed "," after ProjectName
                            // Added space after sTemp (MLS_Phase)
                            // Changed DTT_Time to DTT_Q50 -- customers wants bid date
                            // Removed GMT from end of DTT_Q50
                            par_sDelim = "";
                            DateTime _DT = Convert.ToDateTime(doRS.GetFieldVal("DTT_Q50", clC.SELL_SYSTEM));
                            sResult = doRS.GetFieldVal("TXT_ProjectName", 0, 23) + " " + doRS.GetFieldVal("TXT_Region", 0, 7) + " " + doRS.GetFieldVal("TXT_IdNumber", 0, 10) + " " + sTemp + " " + Strings.Left(goTR.DateTimeToSysString(Convert.ToDateTime(goTR.UTC_LocalToUTC(ref _DT)), ref par_iValid, ref par_sDelim), 2);
                            sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
                            sResult = goTR.Replace(sResult, Constants.vbTab, " ");
                            par_oReturn = sResult;
                            break;
                        }

                    case "SY":
                        {
                            if (!doRS.IsLoaded("TXT_StyleName"))
                                goErr.SetError(35103, sProc, null, sFileName + ".TXT_StyleName");

                            sResult = Convert.ToString(doRS.GetFieldVal("TXT_StyleName", 0, 80));
                            sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
                            sResult = goTR.Replace(sResult, Constants.vbTab, " ");
                            par_oReturn = sResult;
                            break;
                        }
                    case "OP":
                        //==> OPP NEW:	DTE_Time+" "+LNK_CreditedTo_User%%TXT_Code+" "+LNK_For_Company%%TXT_CompanyName+" "+...
                        //				LNK_For_Product%%TXT_ProductName+" "+CUR_Value
                        //OPP		<For - Company - TXT_CompanyName> <For - Product - TXT_ProductName> CUR_ValueIndex (MLS_Status)  
                        //			OPP-COMPANY-0						OPP-PRODUCT-0

                        if (!doRS.IsLoaded("LNK_CreditedTo_US"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".LNK_CreditedTo_US");
                            ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                        }
                        if (!doRS.IsLoaded("LNK_For_CO"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".LNK_For_CO");
                            ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                        }
                        if (!doRS.IsLoaded("LNK_For_PD"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".LNK_For_PD");
                            ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                        }
                        if (!doRS.IsLoaded("DTT_Time"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".DTT_Time");
                            ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                        }
                        if (!doRS.IsLoaded("CUR_Value"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".CUR_Value");
                            ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                        }
                        if (!doRS.IsLoaded("MLS_Status"))
                        {
                            goErr.SetError(35103, sProc, "", sFileName + ".MLS_Status");
                            ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                        }

                        //LNK_CreditedTo_US%%TXT_Code
                        sTemp = doRS.GetFieldVal("LNK_CreditedTo_US", 0, -1, false, 1).ToString();
                        if (string.IsNullOrEmpty(sTemp))
                        {
                            //No records linked
                            sTemp = "?";
                        }
                        else
                        {
                            //Find the field value in the linked record
                            doLink = new clRowSet("US", 3, "GID_ID='" + sTemp + "'", "", "TXT_Code", 1);
                            if (doLink.Count() > 0)
                            {
                                sTemp = doLink.GetFieldVal("TXT_Code").ToString();
                            }
                            else
                            {
                                sTemp = "?";
                            }
                        }

                        //LNK_For_CO%%TXT_CompanyName
                        sTemp2 = doRS.GetFieldVal("LNK_For_CO", 0, -1, false, 1).ToString();
                        if (string.IsNullOrEmpty(sTemp2))
                        {
                            //No records linked
                            sTemp2 = "";
                        }
                        else
                        {
                            //Find the field value in the linked record
                            doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp2 + "'", "", "TXT_CompanyName", 1);
                            if (doLink.Count() > 0)
                            {
                                sTemp2 = doLink.GetFieldVal("TXT_CompanyName", 0, 22).ToString();
                            }
                            else
                            {
                                sTemp2 = "";
                            }
                        }

                        //LNK_For_Product%%TXT_ProductName
                        sTemp3 = doRS.GetFieldVal("LNK_For_PD", 0, -1, false, 1).ToString();
                        if (string.IsNullOrEmpty(sTemp3))
                        {
                            //No records linked
                            sTemp3 = "";
                        }
                        else
                        {
                            //Find the field value in the linked record
                            doLink = new clRowSet("PD", 3, "GID_ID='" + sTemp3 + "'", "", "TXT_ProductName", 1);
                            if (doLink.Count() > 0)
                            {
                                sTemp3 = doLink.GetFieldVal("TXT_ProductName", 0, 14).ToString();
                            }
                            else
                            {
                                sTemp3 = "";
                            }
                        }

                        //Company (23)   '25
                        //Date (15)      '11
                        //Credited To User (5)
                        //Product (15)   '17
                        //Value (13)
                        //Status (9)
                        //*** MI 10/4/07 Added LocalToUTC conversion
                        //sResult = sTemp2 & " " & _
                        //    goTR.DateToString(doRS.GetFieldVal("DTE_Time", clC.SELL_SYSTEM), "YYYY-MM-DD") & " " & _
                        //    sTemp & " " & _
                        //    sTemp3 & " " & _
                        //    goTR.Pad(doRS.GetFieldVal("CUR_Value"), 11, " ", "L")
                        DateTime dttttime = Convert.ToDateTime(doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM));
                        DateTime gotrdatedtttiem = Convert.ToDateTime(goTR.UTC_LocalToUTC(ref dttttime));
                        par_iValid = 4;
                        par_sDelim = " ";

                        sResult = sTemp2 + " " + Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(gotrdatedtttiem, ref par_iValid, ref par_sDelim), 10) + " GMT " + sTemp + " " + sTemp3 + " " + goTR.Pad(doRS.GetFieldVal("CUR_Value").ToString(), 11, " ", "L");

                        sResult += " [" + doRS.GetFieldVal("MLS_STATUS", 0, 8).ToString() + "]";
                        //par_bRunNext = false;
                        break;
                }
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_bRunNext = false;
            par_doCallingObject = doRS;
            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }
        public bool MergeRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/19/2014 Added for merge
            par_bRunNext = false;

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName()) + "'", null, "**", 0, null, null, null, null, null, true, true, false, false, 0, null, true);
                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Microsoft.VisualBasic.Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField).ToString() == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    break;
                                }

                            case "MMO":
                                {
                                    // Append
                                    if (doRSMergeTo.GetFieldVal(sField) == "")
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));

                                    }
                                    else
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMergeTo.GetFieldVal(sField) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + doRSMerge.GetFieldVal("SYS_Name") + " ==" + Constants.vbCrLf + doRSMerge.GetFieldVal(sField));

                                    }
                                    break;
                                }

                            case "CHK":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);

                                    }
                                    break;
                                }

                            case "MLS":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);

                                    }
                                    break;
                                }
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9).ToString());
                        if (sLinkType[4] == "NN" | sLinkType[1] == "2")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (doRSMergeTo.GetFieldVal(aLinks.GetItem(i)) == "")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    // Check Active if exists
                    if (goData.IsFieldValid(doRSMerge.GetFileName(), "CHK_ACTIVEFIELD") == true)
                    {
                        doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);

                    }

                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;
            par_doCallingObject = doRSMerge;
            return true;
        }
        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/30/2014 Changed to Pre

            Form doObject = (Form)par_doCallingObject;
            // TLD 5/30/2014
            string sJournal = "";
            string sWork = "";
            string sDateStamp = "";

            try
            {
                switch (Strings.UCase(par_s5))
                {
                    case "MERGE":
                        {
                            doObject.oVar.SetVar(par_s4 + "_Merge", "1");
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        // run merge script, continue save
                                        scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                                        break;
                                    }

                                case "NO":
                                    {
                                        // Clear merged to co linkbox, continue save
                                        doObject.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                        break;
                                    }

                                case "CANCEL":
                                    {
                                        // Clear merged to co linkbox, cancel save
                                        doObject.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                        doObject.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "MERGEFAIL":
                        {
                            doObject.oVar.SetVar(par_s4 + "_Merge", "1");
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        // Clear merged to co linkbox, cancel save
                                        doObject.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                        doObject.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "OP_FORMCONTROLONCHANGE_BTN_INSERTLINE_PRE":
                        {
                            par_bRunNext = false;
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        sJournal = Convert.ToString(doObject.doRS.GetFieldVal("MMO_Journal"));
                                        // CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                        // Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                        sWork = par_s2;
                                        doObject.oVar.SetVar("JournalWithHardReturns", sWork + Constants.vbCrLf + sJournal);
                                        if (sWork != "")
                                        {
                                            // CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                            // If not defined in WOP, default is 1
                                            if (doObject.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS") != "0")
                                            {
                                                sWork = goTR.Replace(sWork, Constants.vbCrLf, Strings.Chr(32) + Strings.Chr(32) + Strings.Chr(32).ToString());
                                                doObject.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                            }
                                            else
                                                doObject.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                            doObject.MoveToField("MMO_JOURNAL");
                                            // Need to record Journal Type and Issues to var
                                            // for use when creating activity log (Opp_CreateActLog), so we can clear it after
                                            // journal creation on the form
                                            // Reset Journal Type and Issue to Make selection
                                            doObject.oVar.SetVar("OP_JournalType", doObject.doRS.GetFieldVal("MLS_JournalType", 1));
                                            doObject.doRS.SetFieldVal("MLS_JournalType", 0, 2);
                                        }

                                        break;
                                    }
                            }

                            break;
                        }

                    case "AC_FORMONSAVE_POST_PROJECTDATES":
                        {
                            // TLD 5/30/2014 Added runnext
                            par_bRunNext = false;
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        // Update Project dates
                                        // TLD 10/4/2010 Mod RS to optimize
                                        // Dim doRSPR As New clRowSet("PR", 1, "GID_ID=" & doObject.doRS.getfieldval("LNK_RELATED_PR") & "")
                                        clRowSet doRSPR = new clRowSet("PR", 1, "GID_ID=" + doObject.doRS.GetFieldVal("LNK_RELATED_PR") + "", null, "*", 1);
                                        if (doRSPR.GetFirst() == 1)
                                        {
                                            if (Convert.ToString(doObject.doRS.GetFieldVal("DTT_BIDDATE")) != "")
                                            {
                                                doRSPR.SetFieldVal("DTT_Q50", doObject.doRS.GetFieldVal("DTT_BIDDATE"));

                                            }
                                            if (Convert.ToString(doObject.doRS.GetFieldVal("DTT_NEXTCONTACTDATE")) != "")
                                            {
                                                doRSPR.SetFieldVal("DTT_NEXTACTIONDATE", doObject.doRS.GetFieldVal("DTT_NEXTCONTACTDATE"));

                                            }
                                            if (Convert.ToString(doObject.doRS.GetFieldVal("DTT_LASTCONTACTDATE")) != "")
                                            {
                                                doRSPR.SetFieldVal("DTT_LastUpdated", doObject.doRS.GetFieldVal("DTT_LASTCONTACTDATE"));

                                            }
                                            if (doRSPR.Commit() != 1)
                                            {
                                                if (goErr.GetLastError("NUMBER") == "E47250")
                                                    // display normal message box regarding permissions
                                                    goUI.NewWorkareaMessage("You do not have permission to edit the project " + doRSPR.GetFieldVal("SYS_NAME") + ".  The bid date, next action date or last modified date for this project will not be updated.", 0, "selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                                                else
                                                {
                                                    // display msg to user that updating CN failed.
                                                    goErr.SetError(35000, sProc, "Error updating project dates.");
                                                    return false;
                                                }
                                            }
                                        }

                                        break;
                                    }

                                case "NO":
                                    {
                                        break;
                                    }

                                case "CANCEL":
                                    {
                                        doObject.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "PR_FORMONSAVE_POST_AWARDED":
                        {
                            // TLD 5/30/2014 Added runnext
                            par_bRunNext = false;
                            switch (par_s1)
                            {
                                case "OK":
                                    {
                                        doObject.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }
                }
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool Opp_CreateActLog_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // 12/22/2004 RAH: turned over to MI
            // 2004/12/22 10:29:34 MAR Edited. SetLinkVals cause an error on line 37 of SetLinkVal: incorrect type.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/30/2014 Prevent main from running
            par_bRunNext = false;

            // PURPOSE:
            // PJ 5/30/02 Adds Act Log with new notes of Opportunity.
            // Run from enforce only in non-CREATION (MODIF) mode.
            // If it is run in CREATION mode, the new Activity will not be linked to the original Opp.
            // RETURNS:
            // 1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.

            Form doForm = (Form)par_doCallingObject;
            string sMessage;
            string MMO_JOURNAL = (doForm.doRS.GetFieldVal("MMO_JOURNAL") == null) ? "" : doForm.doRS.GetFieldVal("MMO_JOURNAL").ToString();
            int lLenJournal = (doForm.oVar.GetVar("lLenJournal") == null) ? -1 : Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));

            if (Strings.Len(MMO_JOURNAL) <= lLenJournal)
                //if (Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")) <= doForm.oVar.GetVar("lLenJournal"))
                return true;
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Opportunity is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);
                doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");
                // Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");

                return true;
            }

            string sNotes = "";
            string sWork = "";
            long lWork = 0;
            int Iissue = 0;
            string sPurpose = "";
            // Grab journal type from var so journal type field
            // on OP form can be reset prior to AC journal creation
            string sJournalType = Convert.ToString(doForm.oVar.GetVar("OP_JournalType"));
            clList goACType = new clList(); // Checks AC Type list for that journal type

            doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");

            // sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            // CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            // hard returns in the journal field
            sWork = Convert.ToString(doForm.oVar.GetVar("JournalWithHardReturns"));
            // CS 2/4/10
            if (sWork == "")
                return true; // We didn't hit MessageBoxEvent from entering a journal note.

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, null, null, null, 1, null, null, null, null, null, doForm.doRS.bBypassValidation);

            // 'sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            // 'CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            // 'hard returns in the journal field
            // sWork = doForm.oVar.GetVar("JournalWithHardReturns")

            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, Convert.ToInt32(lWork));
            sNotes = sNotes + "== Created from Opportunity '" + doForm.doRS.GetFieldVal("SYS_Name") + "'";
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            doNew.SetFieldVal("MLS_Status", 1, 2);     // Completed
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            // CS doNew.SetFieldVal("TME_ENDTIME", doForm.doRS.GetFieldVal("TME_Time"))
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");

            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));

            // In the code below Paul added IsObjectAssigned tests because SetLinkVals weren't working in some cases.
            // ==> Remove the IsObjectAssigned tests.
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_ORIGINATEDBY_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (10).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If

            doNew.SetLinkVal("LNK_Related_CN", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (11).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_FOR_PD", ref doLink, true, 0, -1, "A_a", ref oTable);    // PJ Changed link name from 'LNK_RELATED_PRODUCT' to 'LNK_FOR_PRODUCT'
                                                                                                          // If Not goP.IsObjectAssigned(doLink) Then
                                                                                                          // goP.TraceLine("doLink (LNK_Related_PD) is not assigned (12).", "", sProc)
                                                                                                          // '	goErr.DisplayLastError()
                                                                                                          // End If
            doNew.SetLinkVal("LNK_Related_PD", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_PD) is not assigned (13).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Related_GR", ref doLink, true, 0, -1, "A_a", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_GR) is not assigned.", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_Related_GR", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_GROUP) is not assigned (2).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_FOR_CO", ref doLink, true, 0, -1, "A_a", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_FOR_CO) is not assigned (3).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_RELATED_CO", doLink);
            // if goErr.GetLastError()<>"E00000" then
            // goErr.DisplayLastError()
            // End If

            // Sets type to MLS_JOURNALTYPE if not 0
            if (sJournalType != "<Make selection>")
            {
                if (Convert.ToInt32(goACType.LReadSeek("AC:TYPE", "VALUE", sJournalType)) != 0)
                    doNew.SetFieldVal("MLS_TYPE", sJournalType, 1);
                else
                    doNew.SetFieldVal("MLS_TYPE", 31, 2);// Journal
            }
            else
                doNew.SetFieldVal("MLS_TYPE", 31, 2);// Journal

            if (sPurpose != "<Make selection>")
            {
                clList goACPurpose = new clList();
                Iissue = (Convert.ToInt32(goACPurpose.LReadSeek("AC:PURPOSE", "VALUE", sPurpose)));
                doNew.SetFieldVal("MLS_Purpose", Iissue, 2);
            }

            // Reset vars here?
            doForm.oVar.SetVar("OP_JournalType", "");

            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(doNew.GetFieldVal("MMO_HISTORY").ToString(), "Created."));
            doNew.SetFieldVal("LNK_RELATED_OP", doForm.GetRecordID());

            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;
                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);

                goLog.Log("MessageBoxEvent", doForm.oVar.GetVar("ScriptMessages").ToString(), -1, true, true);
                return false;
            }

            doNew = null;
            doLink = null;
            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormControlOnChange_BTN_INSERTLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/8/07 Changed time stamp to local time, no label.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/30/2014 Prevent main from running
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sParams = "";

            // Need to enforce Journal Type BEFORE allowing user to enter
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_JournalType", 2)) == 0)
            {
                doForm.MessageBox("Please select a Journal Type.");
                return true;
            }
            else
            {
                // Allow user to enter journal
                scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "sDateStamp", "CODE", "USERNOOFFSETLABEL"); // returns var sDateStamp

                // goTR.StrWrite(sParams, "TITLE", "Add Note")
                // CS:Inputbox must be replaced in web context.
                // sWork = InputBox(doForm.oVar.GetVar("sDateStamp") & " ", sParams, doForm)
                // next line wt mod for release
                doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", null, null, doForm.oVar.GetVar("sDateStamp") + " ", "MessageBoxEvent", null, null, null, null, "OK", null, null, null, System.Reflection.MethodInfo.GetCurrentMethod().Name);
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UNITPRICE|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UNITPRICE|SUM", 2));

                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doRS.SetFieldVal("CUR_UnitValue", curValue);
            }
            return true;
        }

        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sSalesProcess = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_sSalesProcess: 1 or 0 (default): if 1, use checkboxes in the Sales Process tab
            // to calculate probability %, else just calculate value and value index.
            // 2 to calc both
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/30/2014 Added
            par_bRunNext = false;

            Form doForm = null;
            clRowSet doRS1 = null;
            // TLD 5/30/2014 Added
            string sSalesPipeline = "";

            // Check if we passed doForm or doRs to the script. We pass doRs from OP_RecordOnsave. This 
            // allows calculating value/prob on save of the form. In all other cases it is a form (clicking Calculate
            // buttons for example).
            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;

            }
            else
            {
                doForm = (Form)par_doCallingObject;

            }

            // goP.TraceLine("", "", sProc)

            long lStatus;
            double rProb;
            decimal cValueFld;
            double rQty;
            double rNewValue;
            // TLD 5/30/2014 Commented, don't need?
            // Dim rQ03Worth As Double = 6.25  ' Leverage                      CHK_Q01
            // Dim rQ07Worth As Double = 6.25  ' Exec Buy-In             CHK_Q02
            // Dim rQ10Worth As Double = 12.5  ' CEO Contacted           CHK_Q03
            // Dim rQ20Worth As Double = 6.25  ' Front-End Buy In        CHK_Q04
            // Dim rQ30Worth As Double = 6.25  ' Influences IDd          CHK_Q05
            // Dim rQ35Worth As Double = 6.25  ' Needs Assessed          CHK_Q06
            // Dim rQ37Worth As Double = 6.25  ' Approved/Funded         CHK_Q07
            // Dim rQ40Worth As Double = 6.25  ' Competition IDd         CHK_Q08
            // Dim rQ50Worth As Double = 6.25  ' Champion Built          CHK_Q09
            // Dim rQ60Worth As Double = 6.25  ' Decision Process        CHK_Q10
            // Dim rQ65Worth As Double = 6.25  ' Timing Estimate         CHK_Q11
            // Dim rQ70Worth As Double = 6.25  ' Key Questions           CHK_Q12
            // Dim rQ75Worth As Double = 6.25  ' Present/Demo                  CHK_Q13
            // Dim rQ80Worth As Double = 6.25  ' Quote                   CHK_Q14
            // Dim rQ85Worth As Double = 6.25  ' Mark Status             CHK_Q15

            // TLD 5/30/2014 Commented, don't need?
            // Dim bQ03 As Boolean
            // Dim bQ07 As Boolean
            // Dim bQ10 As Boolean
            // Dim bQ20 As Boolean
            // Dim bQ30 As Boolean
            // Dim bQ35 As Boolean
            // Dim bQ37 As Boolean
            // Dim bQ40 As Boolean
            // Dim bQ50 As Boolean
            // Dim bQ60 As Boolean
            // Dim bQ65 As Boolean
            // Dim bQ70 As Boolean
            // Dim bQ75 As Boolean
            // Dim bQ80 As Boolean
            // Dim bQ85 As Boolean


            if (par_s2 == "doRS")
            {
                // -----      Calculate Value and Value Index
                // rProb = doRS1.GetFieldVal("SI__PROBABILITY", 2)
                // TLD 5/30/2014 Set SI__Probability based on MLS_SalesPipeline
                sSalesPipeline = Convert.ToString(doRS1.GetFieldVal("MLS_SalesPipeline", 1));
                // Get # after dash -- format is Imminent-90%
                int iPos = Strings.InStr(sSalesPipeline, "-");
                if (iPos == 0)
                    rProb = 0;
                else
                {
                    sSalesPipeline = Strings.Right(sSalesPipeline, Strings.Len(sSalesPipeline) - iPos);
                    sSalesPipeline = goTR.Replace(sSalesPipeline, "%", "");
                    sSalesPipeline = Strings.Trim(sSalesPipeline);
                    if (Information.IsNumeric(sSalesPipeline))
                    {
                        rProb = goTR.StringToNum(sSalesPipeline, "", ref par_iValid, "");

                    }
                    else
                    {
                        rProb = 0;

                    }
                }
                doRS1.SetFieldVal("SI__Probability", rProb, 2);

                cValueFld = Convert.ToDecimal(doRS1.GetFieldVal("CUR_UNITVALUE", 2));
                if (!goTR.IsNumber(Convert.ToString(cValueFld)))
                {
                    cValueFld = 0;

                }
                rQty = Convert.ToDouble(doRS1.GetFieldVal("SR__QTY", 2));
                // CS 10/13/08: Changed to IsNumber because the qty could be set to a varying number of decimals such as 2.15 and then
                // IsNumeric returns False
                // If Not goTR.IsNumeric(rQty) Then rQty = 0
                if (!goTR.IsNumber(Convert.ToString(rQty)))
                    rQty = 0;
                rNewValue = Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty);
                doRS1.SetFieldVal("CUR_VALUE", rNewValue, 2);
                doRS1.SetFieldVal("CUR_VALUEINDEX", Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty) * (Math.Round(rProb, 0) / 100), 2);   // Value Index
            }
            else
            {
                // Working on a form
                // Get Probability % value from form
                // rProb = doForm.doRS.GetFieldVal("SI__PROBABILITY", 2)

                // TLD 5/30/2014 Set SI__Probability based on MLS_SalesPipeline
                sSalesPipeline = Convert.ToString(doForm.doRS.GetFieldVal("MLS_SalesPipeline", 1));
                // Get # after dash -- format is Imminent-90%
                int iPos = Strings.InStr(sSalesPipeline, "-");
                if (iPos == 0)
                    rProb = 0;
                else
                {
                    sSalesPipeline = Strings.Right(sSalesPipeline, Strings.Len(sSalesPipeline) - iPos);
                    sSalesPipeline = goTR.Replace(sSalesPipeline, "%", "");
                    sSalesPipeline = Strings.Trim(sSalesPipeline);
                    if (Information.IsNumeric(sSalesPipeline))
                        rProb = goTR.StringToNum(sSalesPipeline, "", ref par_iValid, "");
                    else
                        rProb = 0;
                }
                doForm.doRS.SetFieldVal("SI__Probability", rProb, 2);

                // Calc probability and value index
                lStatus = Convert.ToInt64(doForm.doRS.GetFieldVal("MLS_STATUS", 2));
                // goP.TraceLine("lStatus is " & lStatus, "", sProc)
                // TLD 5/30/2014 -----Don't set based on Status or Sales Process
                // Select Case lStatus
                // Case 0, 1, 6    'Status = Open, On-hold, Quoted
                // 'goP.TraceLine("par_sSalesProcess: '" & par_sSalesProcess & "'", "", sProc)
                // If par_sSalesProcess = "1" Then
                // 'Calc based on sales process prompts
                // bQ03 = doForm.doRS.GetFieldVal("CHK_Q01", 2)
                // bQ07 = doForm.doRS.GetFieldVal("CHK_Q02", 2)
                // bQ10 = doForm.doRS.GetFieldVal("CHK_Q03", 2)
                // bQ20 = doForm.doRS.GetFieldVal("CHK_Q04", 2)
                // bQ30 = doForm.doRS.GetFieldVal("CHK_Q05", 2)
                // bQ35 = doForm.doRS.GetFieldVal("CHK_Q06", 2)
                // bQ37 = doForm.doRS.GetFieldVal("CHK_Q07", 2)
                // bQ40 = doForm.doRS.GetFieldVal("CHK_Q08", 2)
                // bQ50 = doForm.doRS.GetFieldVal("CHK_Q09", 2)
                // bQ60 = doForm.doRS.GetFieldVal("CHK_Q10", 2)
                // bQ65 = doForm.doRS.GetFieldVal("CHK_Q11", 2)
                // bQ70 = doForm.doRS.GetFieldVal("CHK_Q12", 2)
                // bQ75 = doForm.doRS.GetFieldVal("CHK_Q13", 2)
                // bQ80 = doForm.doRS.GetFieldVal("CHK_Q14", 2)
                // bQ85 = doForm.doRS.GetFieldVal("CHK_Q15", 2)

                // rProb = 0

                // If bQ03 Then rProb += rQ03Worth
                // If bQ07 Then rProb += rQ07Worth
                // If bQ10 Then rProb += rQ10Worth
                // If bQ20 Then rProb += rQ20Worth
                // If bQ30 Then rProb += rQ30Worth
                // If bQ35 Then rProb += rQ35Worth
                // If bQ37 Then rProb += rQ37Worth
                // If bQ40 Then rProb += rQ40Worth
                // If bQ50 Then rProb += rQ50Worth
                // If bQ60 Then rProb += rQ60Worth
                // If bQ65 Then rProb += rQ65Worth
                // If bQ70 Then rProb += rQ70Worth
                // If bQ75 Then rProb += rQ75Worth
                // If bQ80 Then rProb += rQ80Worth
                // If bQ85 Then rProb += rQ85Worth
                // ' Leaving the top 10% for the 'Won' Status
                // ' Leaving the bottom 10% when no check-boxes are checked
                // rProb = 10 + (rProb * 0.8)
                // End If
                // Case 2      'Status = Won
                // ' Set probability to 100%
                // rProb = 100
                // Case 3, 4, 5    'Status = Lost, Cancelled, Delete
                // rProb = 0
                // End Select
                // 'goP.TraceLine("Value of SI__PROBABILITY: '" & doForm.doRS.GetFieldVal("SI__PROBABILITY", 2) & "'", "", sProc)
                // 'goP.TraceLine("rProb: '" & Math.Round(rProb, 0) & "'", "", sProc)
                // If doForm.doRS.GetFieldVal("SI__PROBABILITY", 2) <> Math.Round(rProb, 0) Then
                // 'goP.TraceLine("Setting SI__PROBABILITY to '" & Math.Round(rProb, 0) & "'", "", sProc)
                // doForm.doRS.SetFieldVal("SI__PROBABILITY", Math.Round(rProb, 0), 2)
                // End If


                // -----      Calculate Value and Value Index
                cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2));
                if (!goTR.IsNumber(Convert.ToString(cValueFld)))
                    cValueFld = 0;
                rQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));
                // CS: 10/13/08: Changed to IsNumber b/c Qty could contain decimals
                // If Not goTR.IsNumeric(rQty) Then rQty = 0
                if (!goTR.IsNumber(Convert.ToString(rQty)))
                    rQty = 0;
                rNewValue = Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty);
                doForm.doRS.SetFieldVal("CUR_VALUE", rNewValue, 2);
                doForm.doRS.SetFieldVal("CUR_VALUEINDEX", Convert.ToDouble(cValueFld) * Convert.ToDouble(rQty) * (Math.Round(rProb, 0) / 100), 2);   // Value Index
            }

            if (par_s2 == "doRS")
            {
                par_doCallingObject = doRS1;

            }
            else
            {
                par_doCallingObject = doForm;

            }
            return true;
        }
        public bool PR_CopyStyles_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            Form doForm = (Form)par_doCallingObject;
            clArray doLink = new clArray();
            string sStyleForView = "";
            int i = 0;

            // TLD 1/14/2010 Clears Related Style for View
            // then copies LNK_RELATED_SY contents
            // used to display list in view from other records
            doForm.doRS.SetFieldVal("TXT_STYLEFORVIEW", "");
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_RELATED_SY", ref doLink, true, 0, -1, "A_a", ref oTable);
            for (i = 1; i <= doLink.GetDimension(); i++)
            {
                if (sStyleForView == "")
                    sStyleForView = (Convert.ToString(goData.GetFieldValueFromRec(doLink.GetItem(i), "TXT_STYLENAME")));
                else
                    sStyleForView = sStyleForView + ", " + goData.GetFieldValueFromRec(doLink.GetItem(i), "TXT_STYLENAME");
            }
            sStyleForView = Strings.Left(sStyleForView, 300); // Limit to 300 characters as field is limited
            doForm.doRS.SetFieldVal("TXT_STYLEFORVIEW", sStyleForView);
            par_doCallingObject = doForm;
            return true;
        }
        public bool PR_FormAfterSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // After the PR form is saved need to re-display it in the case of adding a new BI.

            Form doForm = (Form)par_doCallingObject;

            if (doForm.GetMode() == "CREATION")
            {
                if (Convert.ToString(doForm.oVar.GetVar("PR_AddBidItem")) == "1")
                {
                    // Redisplay the same form so the user thinks the form never went away
                    Form doFormSame = new Form("PR", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), "");
                    goUI.Queue("FORM", doFormSame);
                }
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool PR_FormControlOnChange_BTN_ADDBI(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            object doNewForm;

            try
            {
                string sRecID;
                if (doForm.GetMode() == "CREATION")
                {
                    // Save the Project and add a Bid Item.
                    // Set var to let us know that in PR_FormAfterSave we need to add a new Bid Item. When
                    // the user just creates a new Project and clicks Save we don't want a new BI to be
                    // created.
                    doForm.oVar.SetVar("PR_AddBidItem", "1");
                    if (doForm.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                        return false;
                }
                else if (doForm.Save(3, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                {
                }

                sRecID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                // Create a new BI record and open it
                doNewForm = doForm.CreateForm("BI", sRecID);

                // Open the form
                //doNewForm.openForm();
                goUI.Queue("FORM", doNewForm);
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool PR_FormControlOnChange_BTN_LINKCOMPANIES_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/10/2009 Prevents main from running
            // They do NOT want to clear the LNK_INVOLVED_CO field
            par_bRunNext = false;
            // doForm.doRS.ClearLinkAll("LNK_Involved_CO")
            scriptManager.RunScript("ConnectCompanies", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null/* Conversion error: Set to default value for this argument */, "LNK_INVOLVED_CN", "LNK_INVOLVED_CO");
            par_doCallingObject = doForm;

            return true;
        }
        public bool PR_FormControlOnChange_BTN_LINKCOMPANIES_1_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/10/2009 Prevents main from running
            // They do NOT want to clear the LNK_FOR_CO field
            par_bRunNext = false;
            // doForm.doRS.ClearLinkAll("LNK_for_CO")
            scriptManager.RunScript("ConnectCompanies", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "LNK_ORIGINATEDBY_CN", "LNK_FOR_CO");
            par_doCallingObject = doForm;
            return true;
        }
        public bool PR_FormControlOnChange_BTN_SendTOHTML(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID;
            string sName = "";

            try
            {
                // Call Save first; See BTN_corr code
                sID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));
                if (doForm.Save(2, true, "PR_FormControlOnChange_BTN_SENDTOHTML") == 0)
                    return false;
                doForm.CloseOnReturn = true;
                scriptManager.RunScript("GenerateSysName", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, sName);

                clSend oSend = new clSend();
                oSend.AddSendJob("Project " + sName + " to HTML", sID, "cus_project_to_html.htm", "CORR", "FILE", "PR", true, true);
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool PR_FormControlOnChange_LNK_INVOLVED_CN_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/10/2009 Prevents main from running
            // Do not want to autofill LNK_INVOLVED_CO based on
            // CN selection
            par_bRunNext = false;
            par_doCallingObject = doForm;

            return true;
        }
        public bool PR_FormControlOnChange_LNK_ORIGINATEDBY_CN_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/10/2009 Prevents main from running
            // Do not want to autofill LNK_FOR_CO based on
            // CN selection
            par_bRunNext = false;
            par_doCallingObject = doForm;

            return true;
        }
        public bool PR_FormControlOnChange_LNK_RELATED_SY_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            // TLD 1/14/2010 Clears Related Style for View
            // then copies LNK_RELATED_SY contents
            // used to display list in view from other records
            scriptManager.RunScript("PR_CopyStyles", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doForm;

            return true;
        }
        public bool PR_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.MoveToTab(5);

            // -----------------Enable/Disable fields-----------------
            // TLD 1/14/2010 Disable Related Style For view
            // Used to copy Related Styles to text box for viewing
            doForm.SetControlState("TXT_SYLEFORVIEW", 1);

            // Enable Bid Item linkbox
            doForm.SetControlState("LNK_RELATED_BI", 0);

            // Gray out fields on Bid Results tab
            // doForm.SetControlState("MMO_Description", 4)
            // doForm.SetControlState("TXT_IdNumber", 4)
            // doForm.SetControlState("TXT_Country", 4)
            // doForm.SetControlState("TXT_Region", 4)
            // doForm.SetControlState("MLS_Type", 4)
            // doForm.SetControlState("LNK_Related_SY", 4)
            // doForm.SetControlState("SR__Size", 4)
            // doForm.SetControlState("MLS_Units", 4)
            // doForm.SetControlState("TXT_AwardedStyle", 4)
            // -----------------End Enable/Disable fields--------------
            par_doCallingObject = doForm;

            return true;
        }
        public bool PR_FormOnRedisplay_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.doRS.UpdateLinkState("LNK_Related_BI");
            // doForm.RefreshLinkNames("LNK_Related_PI")
            doForm.RefreshAllLinkNames = false;
            par_doCallingObject = doForm;

            return true;
        }
        public bool PR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.oVar.GetVar("CancelSave") == "1")
            {
                doForm.oVar.SetVar("CancelSave", "");
                doForm.oVar.SetVar("CheckedBidItems", "");
                return false;
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool PR_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            clArray aBidItems;

            int iStatus;
            int iReason;
            int iType;
            string sStyle;
            iStatus = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2));
            iType = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_TYPE", 2));

            int i;
            bool bAwarded = false;

            // Fill Project fields from Related Bid Items
            // Get Bid Items and make sure only one bid item with 'awarded' checked.
            if (doForm.oVar.GetVar("CheckedBidItems") != "1")
            {
                doForm.oVar.SetVar("CheckedBidItems", "1");
                aBidItems = (clArray)doForm.doRS.GetFieldVal("LNK_RELATED_BI%%CHK_AWARDED", 2);
                // Check which was awarded
                for (i = 1; i <= aBidItems.GetDimension(); i++)
                {
                    if (bAwarded == true & Convert.ToInt32(aBidItems.GetItem(i)) == 1)
                    {
                        doForm.MessageBox("Warning! More than one bid has been awarded for this project.", clC.SELL_MB_OK, null, null, null, null, null, "MessageBoxEvent", null, null, doForm, null, "OK", null, null, null, "PR_FormOnSave_Post_Awarded");
                        return true;
                    }
                    else if (Convert.ToInt32(aBidItems.GetItem(i)) == 1)
                        bAwarded = true;
                }
            }

            if (iStatus == 3)
            {
                iReason = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_REASONWONLOST", 2));
                if (iReason != 1 & iReason != 2)
                {
                    doForm.MoveToField("MLS_REASONWONLOST");
                    goErr.SetWarning(30200, sProc, "Please select either 'Price' or 'Preference' in the 'Reason' field.");
                    return false;
                }
            }

            if (iStatus == 4)
            {
                iReason = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_REASONWONLOST", 2));
                if (iReason != 1 & iReason != 2 & iReason != 3)
                {
                    doForm.MoveToField("MLS_REASONWONLOST");
                    goErr.SetWarning(30200, sProc, "Please select either 'Price', 'Preference' or 'Alternate' in the 'Reason' field.");
                    return false;
                }
            }

            if (iStatus == 5)
            {
                iReason = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_REASONWONLOST", 2));
                if (iReason != 4 & iReason != 5 & iReason != 6 & iReason != 7)
                {
                    doForm.MoveToField("MLS_REASONWONLOST");
                    goErr.SetWarning(30200, sProc, "Please select either 'Multi-Leg', 'Sphere/Flute', 'All Steel' or 'Ground' in the 'Reason' field.");
                    return false;
                }
            }

            if (iStatus == 1 | iStatus == 2)
            {
                if (doForm.doRS.GetFieldVal("DTT_Q50") == "")
                {
                    doForm.MoveToField("DTT_Q50");
                    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("PR", "DTT_Q50"), "", "", "", "", "", "", "", "", "DTT_Q50");
                    return false;
                }
            }

            if (iType == 2)
            {
                // Check that linked Style is not 'Standpipe', 'Steel' or 'Reservoir'
                sStyle = Strings.UCase(doForm.doRS.GetFieldVal("LNK_RELATED_SY%%SYS_NAME").ToString());
                if (sStyle == "STANDPIPE" | sStyle == "STEEL" | sStyle == "RESEVOIR" | sStyle == "RESERVOIR")
                {
                    doForm.MoveToField("LNK_RELATED_SY");
                    goErr.SetWarning(30200, sProc, sStyle + " is not a valid style. Please unlink this Style.");
                    return false;
                }
            }

            if (iType == 1)
            {
                sStyle = Strings.UCase(doForm.doRS.GetFieldVal("LNK_RELATED_SY%%SYS_NAME").ToString());
                if (sStyle == "MULTI-LEG" | sStyle == "SPHERE" | sStyle == "COMPOSITE" | sStyle == "FLUTE")
                {
                    doForm.MoveToField("LNK_RELATED_SY");
                    goErr.SetWarning(30200, sProc, sStyle + " is not a valid style. Please unlink this Style.");
                    return false;
                }
            }

            // TLD 1/14/2010 Clears Related Style for View
            // then copies LNK_RELATED_SY contents
            // used to display list in view from other records
            scriptManager.RunScript("PR_CopyStyles", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doForm;

            return true;
        }
        public bool PR_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            clArray aStyle;
            int i;
            clArray aBidItems;
            // Dim bAwarded As Boolean = False
            string sStyle;

            // TLD 5/14/2014 Updated, need to also fill LNK_Awarded_SY, LNK_Awarded_VE
            // Fill Project Style (LNK_RELATED_SY) with awarded bid item style (GID_Bidfor_SY)
            aBidItems = (clArray)doRS.GetFieldVal("LNK_RELATED_BI", 2);
            for (i = 1; i <= aBidItems.GetDimension(); i++)
            {
                clRowSet doRSBI = new clRowSet("BI", 3, "GID_ID=" + aBidItems.GetItem(i) + "", null/* Conversion error: Set to default value for this argument */, "LNK_BIDFOR_SY%%SYS_NAME, LNK_BidFor_SY, LNK_Competing_VE, CHK_AWARDED");
                if (doRSBI.GetFirst() == 1)
                {
                    if (Convert.ToInt32(doRSBI.GetFieldVal("CHK_AWARDED", 2)) == 1)
                    {
                        sStyle = Convert.ToString(doRSBI.GetFieldVal("LNK_BIDFOR_SY%%SYS_NAME"));
                        doRS.SetFieldVal("TXT_AwardedStyle", sStyle);
                        // TLD 5/14/2014 Clear, then update?
                        doRS.ClearLinkAll("LNK_Awarded_SY");
                        doRS.ClearLinkAll("LNK_Awarded_VE");
                        doRS.SetFieldVal("LNK_Awarded_SY", doRSBI.GetFieldVal("LNK_BidFor_SY", 2), 2);
                        doRS.SetFieldVal("LNK_Awarded_VE", doRSBI.GetFieldVal("LNK_Competing_VE", 2), 2);
                        break;
                    }
                }
            }

            // Fill MLS_CompAlt
            if (doRS.GetLinkCount("LNK_RELATED_SY") == 0)
                doRS.SetFieldVal("MLS_CompAlt", 0, 2);
            else
            {
                // If is 'composite' set comp alt=yes
                aStyle = (clArray)doRS.GetFieldVal("LNK_RELATED_SY%%SYS_NAME", 2);
                for (i = 1; i <= aStyle.GetDimension(); i++)
                {
                    if (Strings.UCase(aStyle.GetItem(i)) == "COMPOSITE")
                    {
                        doRS.SetFieldVal("MLS_CompAlt", 1, 2);
                        break;
                    }
                    else
                        doRS.SetFieldVal("MLS_compalt", 0, 2);
                }

            }
            par_doCallingObject = doRS; ;
            return true;
        }
        public bool PROJECTFIND_FormControlOnChange_BTN_FIND(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form oForm = (Form)par_doCallingObject;

            string sProjName;
            string sProjNumber;
            string sRegion;
            string sStatus;
            int iStatus = default(int);
            string sStatusIndex;

            string sView;
            int iCondCount;
            string sViewCondition;
            string sNewCondition = "";
            int iOrigCondCount;
            int i;
            string sFilter = "";

            // Get values from form
            sProjName = Strings.Trim(oForm.GetControlVal("NDB_TXT_PROJNAME"));
            sProjNumber = Strings.Trim(oForm.GetControlVal("NDB_TXT_IDNUMBER"));
            sRegion = Strings.Trim(oForm.GetControlVal("NDB_TXT_REGION"));
            sStatus = Strings.Trim(oForm.GetControlVal("NDB_TXT_STATUS"));
            // Get the integer value of the status
            if (Strings.UCase(sStatus) != "ANY")
            {
                clList oList = new clList();
                sStatusIndex = oList.LReadSeek("PR:STATUS", "VALUE", sStatus);
                iStatus = Convert.ToInt32(goTR.StringToNum(sStatusIndex, "", ref par_iValid, ""));
            }

            // Use values to filter Projects-All view if it exists
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_2003012009473955013MAI 30119XX");
            // Edit views in DT

            // View 1:Projects All
            sView = oDesktop.GetViewMetadata("VIE_2003012009481260198MAI 30119XX");
            iCondCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sView, "CCOUNT", "", true), "", ref par_iValid, ""));


            // If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            if (iCondCount == 1)
            {
                if (goTR.StrRead(sView, "C1FIELDNAME") == "<%ALL%>")
                    iCondCount = 0;// Will overwrite these values
            }
            // Original condition count
            iOrigCondCount = iCondCount;

            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (sProjName != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sProjNumber != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sRegion != "")
            {
                iCondCount = iCondCount + 1;

            }
            if (sStatus != "ANY")
            {
                iCondCount = iCondCount + 1;

            }


            // Edit view properties dialog lines
            // goTR.StrWrite(sView, "CCOUNT", iCondCount + 4)
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (sProjName != "")
            {
                // Add project name condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_PROJECTNAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "[");
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sProjName);
                i = i + 1;
                // Dim sFilter As String = "TXT_PROJECTNAME[" & sProjName & " AND TXT_IDNUMBER[" & sProjNumber & " AND TXT_REGION[" & sRegion & " AND MLS_STATUS=" & iStatus & ""
                sFilter = "TXT_PROJECTNAME[" + sProjName + "";
            }
            if (sProjNumber != "")
            {
                // Add project number condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_IDNUMBER%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "[");
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sProjNumber);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_IDNUMBER[" + sProjNumber + "";

                }
                else
                {
                    sFilter = "TXT_IDNUMBER[" + sProjNumber + "";

                }
            }
            if (sRegion != "")
            {
                // Add Region condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_REGION%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "[");
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sRegion);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_REGION[" + sRegion + "";

                }
                else
                {
                    sFilter = "TXT_REGION[" + sRegion + "";

                }
            }
            if (Strings.UCase(sStatus) != "ANY")
            {
                // Add Status condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MLS_STATUS%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "=");
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", iStatus);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND MLS_STATUS=" + iStatus + "";

                }
                else
                {
                    sFilter = "MLS_STATUS=" + iStatus + "";

                }
            }

            // TLD 11/4/2010 -- this doesn't work if they
            // don't use any other filters other than ANY
            // so added a check to see if sFilter is blank
            // Edit CONDITION= line in view MD
            sViewCondition = goTR.StrRead(sView, "CONDITION");
            if (sViewCondition == "")
            {
                sNewCondition = sFilter; // No filter in view already

            }
            else if (sFilter == "")
            {
                // no additional filter chosen by user
                sNewCondition = sViewCondition;
            }

            else
            {
                sNewCondition = sViewCondition + " AND " + sFilter;

            }
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_2003012009481260198MAI 30119XX", sView);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;

            // Que Projects-All
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
            goUI.Queue("", "");
            par_doCallingObject = oForm;
            return true;
        }
        public bool PROJECTFIND_FormOnLoad_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Set the value of 'ANY' in the STatus NDB field by default
            // This is typically a MLS field but we don't support NDB MLS fields
            // If I didn't do this I wouldn't be sure if they were trying to find a
            // blank status OR wanted to search for Projects with ANY status

            Form doForm = (Form)par_doCallingObject;

            doForm.SetControlVal("NDB_TXT_STATUS", "ANY");
            par_doCallingObject = doForm;

            return true;
        }
        public bool PROJECTOPEN(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            Form doForm = (Form)par_doCallingObject;
            string sID;
            string sFileName;
            string sView;
            string sFilter;

            // TLD 11/6/2008 Added Custom Icon/Button to open the Project Folder URL of selected project
            sID = goUI.GetLastSelected("SELECTEDRECORDID");
            sFileName = goTR.GetFileFromSUID(sID);
            if (Strings.UCase(sFileName) != "PR")
                goUI.NewWorkareaMessage("Please select a Project first.", 0, "selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
            else
            {

                // Use record id to filter Projects-All view if it exists
                Desktop oDesktop = new Desktop("GLOBAL", "DSK_2003012009473955013MAI 30119XX");

                // Edit Projects All view in DT
                // View 1:Projects All
                sView = oDesktop.GetViewMetadata("VIE_2003012009481260198MAI 30119XX");

                // Write filter condition to view -- only want to filter for this record
                // So can overwrite existing conditions
                goTR.StrWrite(ref sView, "CONDITION", "GID_ID='" + sID + "'");
                goTR.StrWrite(ref sView, "CCOUNT", "1");
                // Add record ID condition
                goTR.StrWrite(ref sView, "C1" + "FIELDNAME", "<%GID_ID%>");
                goTR.StrWrite(ref sView, "C1" + "CONDITION", "=");
                goTR.StrWrite(ref sView, "C1" + "VALUE1", sID);

                oDesktop.SetViewMetadata("VIE_2003012009481260198MAI 30119XX", sView);
                sView = "";
                sFilter = "";

                // Que Projects-All
                goUI.Queue("DESKTOP", oDesktop);
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool PROJECTURL(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Dim doForm As clForm = par_doCallingObject
            string sURL = "";
            string sFileName = "";
            clRowSet doNewRS;
            string sID = "";
            int iStart;

            // TLD 7/24/2014 Modified to open Project & Opportunity URLs
            // TLD 8/13/2008 Added Custom Icon/Button to open the Project Folder URL of selected project
            sID = goUI.GetLastSelected("SELECTEDRECORDID");
            sFileName = goTR.GetFileFromSUID(sID);
            if (goData.IsFieldValid(sFileName, "URL_URLS") == true)
            {
                doNewRS = new clRowSet(sFileName, 3, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "URL_URLS");
                if (doNewRS.GetFirst() == 1)
                {
                    sURL = Strings.Trim(doNewRS.GetFieldVal("URL_URLS").ToString());
                    if (sURL != "")
                    {
                        iStart = Strings.InStr(sURL, Constants.vbCrLf);
                        if (iStart > 0)
                            sURL = Strings.Mid(sURL, 1, iStart - 1);
                        // Page.Title = prs.GetFieldVal("TXT_PROJECTNAME") & " " & prs.GetFieldVal("TXT_REGION") & " " & prs.GetFieldVal("TXT_IDNUMBER") & " - Project URL"
                        HttpContext.Current.Response.Write("<script>window.open('" + sURL + "','SelltisURL', '');<" + "/" + "script>");
                    }
                    else
                        goUI.NewWorkareaMessage("The Project Folder field on the selected " + sFileName + " is blank.", 0, "selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
                }
                else
                    goUI.NewWorkareaMessage("No valid record is selected.  Please select a record that contains a Project Folder field.", 0, "selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");
            }
            else
                goUI.NewWorkareaMessage("The selected record does not contain a Project Folder field.", 0, "selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null, "", "", "", "", "");

            // If UCase(sFileName) <> "PR" Then
            // goUI.NewWorkareaMessage("Please select a Project first.", , )
            // Else
            // prs = New clRowSet("PR", 3, "GID_ID='" & sID & "'", , "URL_URLS", , , , , , , True, True)
            // sProjectURL = Trim(prs.GetFieldVal("URL_URLS"))
            // If sProjectURL <> "" Then
            // iStart = InStr(sProjectURL, vbCrLf)
            // If iStart > 0 Then
            // sProjectURL = Mid(sProjectURL, 1, iStart - 1)
            // End If
            // 'Page.Title = prs.GetFieldVal("TXT_PROJECTNAME") & " " & prs.GetFieldVal("TXT_REGION") & " " & prs.GetFieldVal("TXT_IDNUMBER") & " - Project URL"
            // HttpContext.Current.Response.Write("<script>window.open('" & sProjectURL & "','SelltisURL', '');<" & "/" & "script>")
            // Else
            // goUI.NewWorkareaMessage("The Project Folder field on the selected project is blank.", , )
            // End If
            // End If
            //par_doCallingObject = doNewRS;
            return true;
        }
        public bool QL_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3)

            // TLD 8/6/2014 Added
            par_bRunNext = false;

            // PORTING FROM COMMENCE IN PROGRESS. MI.

            // ******************************
            // DIFFERS FROM SHIPPING DATABASE
            // ******************************

            // --- HISTORY ---
            // 2005/08/17 10:46:47 MAR Added traces to diagnose MLS_REASONWONLOST not being pulled properly from Quote

            // goP.TraceLine("", "", sProc)

            Form doForm = (Form)par_doCallingObject;
            long lLine;
            long lHighestLine;
            string sWork;
            string sColor = Convert.ToString(goP.GetVar("sMandatoryFieldColor"));

            // goP.TraceLine("GetFieldVal('LNK_IN_QT%%LNK_CREDITEDTO_US',2): '" & doForm.dors.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2) & "'", "", sProc)
            // goP.TraceLine("GetFieldVal('LNK_IN_QT%%LNK_CREDITEDTO_US',1): '" & doForm.dors.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 1) & "'", "", sProc)

            // Set mandatory field color
            // doForm.SetFieldProperty("LNK_FOR_MO", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("SR__QTY", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("CUR_COST", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("SR__LINENO", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("LNK_CreditedTo_US", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("LNK_Peer_US", "LABELCOLOR", sColor)
            doForm.SetFieldProperty("LNK_IN_QT", "LABELCOLOR", sColor);

            // Set button tooltips
            doForm.SetFieldProperties("BTN_RecalcTotals", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Recalculate Quote Line totals");
            doForm.SetFieldProperties("BTN_UseModelPrice", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Fill unit price and cost from the linked Model");
            doForm.SetFieldProperties("BTN_INSERTSPEC", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend specifications from the linked Model");

            // CS: Set status and reason fields grayed out
            // 7/3/07: Can set status independnet of Quote Status doForm.setcontrolstate("MLS_STATUS", 4)
            // 5/28/10: Allow setting reason independent of Quote status
            // doForm.SetControlState("MLS_REASONWONLOST", 4)

            // TLD 6/12/2014 CUR_Cost (Budget) calculated or user entered
            // do not disable
            // CS: 5/21/09: Gray out CUR_Cost field on the QL. The Cost will be calculated based on Model Cost * Qty
            // If a customer needs to manually enter costs, this can be customized to ungray the field
            // doForm.SetControlState("CUR_COST", 4)

            // If save button is disabled, disable Save and Create unlinked button
            if (doForm.SaveEnabled() == false)
                doForm.SetControlState("BTN_SAVECRU", 4);

            // Grayed fields
            doForm.SetControlState("MMO_ImportData", 4);
            doForm.SetControlState("cur_subtotal", 4);
            doForm.SetControlState("sr__salestaxpercent", 4);
            doForm.SetControlState("cur_salestax", 4);

            // TLD 5/30/2014 Disable calculated fields
            doForm.SetControlState("CUR_InvoiceAmount", 4);
            doForm.SetControlState("DTE_DueDate", 1);
            // TLD 6/4/2014 Added
            doForm.SetControlState("CUR_ToBePaid", 4);

            // locked
            doForm.SetControlState("lnk_for_pd", 1);
            doForm.SetControlState("lnk_related_dv", 1);
            doForm.SetControlState("lnk_involves_us", 1);
            doForm.SetControlState("lnk_related_ve", 1);

            // grayed button
            doForm.SetControlState("LNK_IN_QT", 5);
            doForm.SetControlState("LNK_TO_CO", 5);

            // ----------- DEFAULT VALUES ------------
            if (doForm.GetMode() == "CREATION")
            {
                if (doForm.doRS.GetLinkCount("LNK_IN_QT") < 1)
                {
                    doForm.MessageBox("You are creating a Quote Line that is not linked to a Quote." + Constants.vbCrLf + Constants.vbCrLf + "To create a new Quote Line, open an existing Quote or create a new Quote and click New on its Details tab.");
                    return true;
                }
                else
                {
                    // CS 6/22/09: Create rowset for getting all Quote values instead of all double hops below (creating multiple rowsets)
                    clRowSet doRSQT = new clRowSet("QT", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_IN_QT") + "'", null/* Conversion error: Set to default value for this argument */, "DTT_TIME,DTE_EXPCLOSEDATE,LNK_CREDITEDTO_US,LNK_PEER_US,LNK_RELATED_PR,LNK_TO_CO,SR__SALESTAXPERCENT,LNK_Related_TK");
                    // doForm.doRS.SetFieldVal("DTT_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTT_TIME"))
                    // doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_EXPCLOSEDATE"))
                    // doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2), 2)
                    // doForm.doRS.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_PEER_US", 2), 2)
                    // doForm.doRS.SetFieldVal("LNK_RELATED_PR", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_RELATED_PR", 2), 2)
                    // doForm.doRS.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_TO_CO", 2), 2)
                    // doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"))
                    if (doRSQT.GetFirst() == 1)
                    {
                        doForm.doRS.SetFieldVal("DTT_QTETIME", doRSQT.GetFieldVal("DTT_TIME"));
                        doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doRSQT.GetFieldVal("DTE_EXPCLOSEDATE"));
                        doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doRSQT.GetFieldVal("LNK_CREDITEDTO_US", 2), 2);
                        doForm.doRS.SetFieldVal("LNK_PEER_US", doRSQT.GetFieldVal("LNK_PEER_US", 2), 2);
                        doForm.doRS.SetFieldVal("LNK_RELATED_PR", doRSQT.GetFieldVal("LNK_RELATED_PR", 2), 2);
                        doForm.doRS.SetFieldVal("LNK_TO_CO", doRSQT.GetFieldVal("LNK_TO_CO", 2), 2);
                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", doRSQT.GetFieldVal("SR__SALESTAXPERCENT"));
                        // TLD 8/6/2014 Added to copy LNK_Related_TK
                        doForm.doRS.SetFieldVal("LNK_Related_TK", doRSQT.GetFieldVal("LNK_Related_TK", 2), 2);
                    }

                    // CS 8/27/08 If WOP to manage users independent of QT is off, always gray out fields on QL
                    if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_USERMGMT")) != "1")
                    {
                        doForm.SetControlState("LNK_CREDITEDTO_US", 4);
                        doForm.SetControlState("LNK_PEER_US", 4);
                    }

                    // -------- Status, Reason Won/Lost -------
                    // ******************************
                    // In Selltis DB, Status and Reason Won/Lost are the same as in Quote
                    // goP.TraceLine("Setting MLS_STATUS from Quote: '" & doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2) & "'", "", sProc)
                    // goLog.Log("QL_FormOnLoadRecord", doForm.dors.getfieldval("LNK_IN_QT%%MLS_status", 2), , , True)
                    clArray oArray = new clArray();
                    string sVal = "";
                    clArray oArrayReason = new clArray(); // CS 5/28/10: Allow setting reason for each QL if can set status ind of QT
                    string sReason = "";
                    // CS 7/3/07: Allow setting QL status independent of QT
                    // oArray = doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2)
                    // If oArray.GetDimension > 0 Then sVal = oArray.GetItem(1)
                    // If sVal <> "" Then
                    // doForm.doRS.SetFieldVal("MLS_Status", sVal, 2)
                    // End If
                    // oArray = Nothing
                    // sVal = ""


                    // If quote set to set status of QL from QT, set it here.
                    // CS 6/12/08: Changed code from setting status using friendly value.
                    // This caused issue b/c the friendly names did not match (ie On Hold in QT vs 20 On Hold in QL)

                    // CS 6/20/08 If WOP is off, always gray out status and set to
                    // same as QT
                    // CS 5/28/10: Do same for Reason Won/Lost
                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_STATUSMGMT") != "1")
                    {
                        doForm.SetControlState("MLS_STATUS", 4);
                        doForm.SetControlState("MLS_REASONWONLOST", 4);
                        oArray = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2);
                        oArrayReason = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2);
                        sVal = oArray.GetItem(1);
                        sReason = oArrayReason.GetItem(1);
                        if (sVal != "")
                        {
                            doForm.doRS.SetFieldVal("MLS_STATUS", goTR.StringToNum(sVal, "", ref par_iValid, ""), 2);
                            doForm.SetControlState("MLS_STATUS", 4);
                        }
                        if (sReason != "")
                        {
                            doForm.doRS.SetFieldVal("MLS_REASONWONLOST", goTR.StringToNum(sReason, "", ref par_iValid, ""), 2);
                            doForm.SetControlState("MLS_REASONWONLOST", 4);
                        }
                        sReason = "";
                        oArrayReason = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                    else if (Strings.UCase(doForm.doRS.GetFieldVal("LNK_IN_QT%%CHK_UPDQLSTATUS").ToString()) == "CHECKED")
                    {
                        oArray = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2);
                        oArrayReason = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2);
                        sVal = oArray.GetItem(1);
                        sReason = oArrayReason.GetItem(1);
                        if (sVal != "")
                        {
                            // doForm.doRS.SetFieldVal("MLS_STATUS", doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 1), 1)
                            doForm.doRS.SetFieldVal("MLS_STATUS", goTR.StringToNum(sVal, "", ref par_iValid, ""), 2);
                            doForm.SetControlState("MLS_STATUS", 4);
                        }
                        else
                            // status active
                            doForm.SetControlState("MLS_STATUS", 0);
                        sVal = "";
                        oArray = null/* TODO Change to default(_) if this is not a reference type */;
                        if (sReason != "")
                        {
                            doForm.doRS.SetFieldVal("MLS_reasonwonlost", goTR.StringToNum(sReason, "", ref par_iValid, ""), 2);
                            doForm.SetControlState("MLS_REASONWONLOST", 4);
                        }
                        else
                            // status active
                            doForm.SetControlState("MLS_REASONWONLOST", 0);
                        sReason = "";
                        oArrayReason = null/* TODO Change to default(_) if this is not a reference type */;
                    }

                    // CS 5/28/10 commented
                    // oArray = doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2)
                    // If oArray.GetDimension > 0 Then sVal = oArray.GetItem(1)
                    // If sVal <> "" Then
                    // doForm.doRS.SetFieldVal("MLS_REASONWONLOST", sVal, 2)
                    // End If


                    // doForm.dors.SetFieldVal("MLS_STATUS", doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2), 2)
                    // goP.TraceLine("Setting MLS_REASONWONLOST from Quote: '" & doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2) & "'", "", sProc)
                    // doForm.dors.SetFieldVal("MLS_REASONWONLOST", doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2), 2)
                    // ******************************
                    // ----------------------------------------
                    scriptManager.RunScript("Quotline_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);      // Runs Quotline_CalcTotal
                }
                // ------- Set Line number to highest used line + 1 ----------
                clRowSet doRS;
                lHighestLine = 0;
                // goP.TraceLine("Starting a rowset on all Quote's Quote Lines", "", sProc)


                // CS: The code below would get quote lines that existed with a null lnk_in_qt. There
                // are 8 in the db with no linked quote. I am changing this to only run if lnk_in_qt
                // is not blank.

                if (doForm.doRS.GetFieldVal("LNK_In_QT", 1) != "")
                {

                    // doRS = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("LNK_In_QT") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    // *** MI 11/14/07 Optimization
                    // doRS = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("LNK_In_QT") & "'", , "SR__LINENO", , , , , , , doForm.doRS.bBypassValidation)
                    // *** MI 11/21/07 Optimization: use a read-only rowset
                    doRS = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("LNK_In_QT") + "'", null/* Conversion error: Set to default value for this argument */, "SR__LINENO");
                    if (doRS.GetFirst() == 1)
                    {
                        do
                        {
                            // goP.TraceLine("Reading Quote Line '" & doRS.GetFieldVal("SR__LINENO", 2) & "'", "", sProc)
                            lLine = Convert.ToInt64(doRS.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doRS.GetNext() == 0)
                                break;
                        }
                        while (true);
                    }
                    else
                    {
                    }
                    // delete(doRS)
                    doRS = null;
                }
                // goP.TraceLine("Highest line: '" & lHighestLine & "'", "", sProc)
                sWork = Convert.ToString(lHighestLine);
                sWork = goTR.ExtractString(sWork, 1, ".");
                if (sWork == clC.EOT.ToString())
                    sWork = "";
                // goP.TraceLine("Integer part of highest line: '" & sWork & "'", "", sProc)
                lHighestLine = Convert.ToInt32(sWork) + 1;
                // goP.TraceLine("Highest line after '+1': '" & lHighestLine & "'", "", sProc)
                doForm.doRS.SetFieldVal("SR__LINENO", lHighestLine, 2);
            }
            else
                // goP.TraceLine("LNK_IN_QT link count: " & doForm.dors.GetLinkCount("LNK_IN_QT"), "", sProc)
                if (doForm.doRS.GetLinkCount("LNK_IN_QT") > 0)
            {
                // CS 6/22/09: Create rowset for getting all Quote values instead of all double hops below (creating multiple rowsets)
                clRowSet doRSQT = new clRowSet("QT", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_IN_QT") + "'", null/* Conversion error: Set to default value for this argument */, "DTE_TIME,TME_TIME,DTE_EXPCLOSEDATE,LNK_CREDITEDTO_US,LNK_PEER_US,LNK_RELATED_PR,LNK_TO_CO,MLS_REASONWONLOST");
                if (doRSQT.GetFirst() == 1)
                {
                    doForm.doRS.SetFieldVal("DTE_QTETIME", doRSQT.GetFieldVal("DTE_TIME"));
                    doForm.doRS.SetFieldVal("TME_QTETIME", doRSQT.GetFieldVal("TME_TIME"));

                    if (goTR.IsDate(Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1))) != true)
                        doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doRSQT.GetFieldVal("DTE_EXPCLOSEDATE"));
                    if (doForm.doRS.GetLinkCount("LNK_CREDITEDTO_US") < 1)
                        doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doRSQT.GetFieldVal("LNK_CREDITEDTO_US", 2), 2);
                    if (doForm.doRS.GetLinkCount("LNK_PEER_US") < 1)
                        doForm.doRS.SetFieldVal("LNK_PEER_US", doRSQT.GetFieldVal("LNK_PEER_US", 2), 2);
                    doForm.doRS.SetFieldVal("LNK_RELATED_PR", doRSQT.GetFieldVal("LNK_RELATED_PR", 2), 2);
                    doForm.doRS.SetFieldVal("LNK_TO_CO", doRSQT.GetFieldVal("LNK_TO_CO", 2), 2);
                }
                // CS 6/22/09---------
                // doForm.doRS.SetFieldVal("DTE_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_TIME"))
                // doForm.doRS.SetFieldVal("TME_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%TME_TIME"))

                // If goTR.IsDate(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1)) <> True Then
                // doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_EXPCLOSEDATE"))
                // End If
                // If doForm.doRS.GetLinkCount("LNK_CREDITEDTO_US") < 1 Then
                // doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2), 2)
                // End If
                // If doForm.doRS.GetLinkCount("LNK_PEER_US") < 1 Then
                // doForm.doRS.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_PEER_US", 2), 2)
                // End If
                // doForm.doRS.SetFieldVal("LNK_RELATED_PR", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_RELATED_PR", 2), 2)
                // doForm.doRS.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_TO_CO", 2), 2)
                // CS -------------


                // -------- Status, Reason Won/Lost -------
                // ******************************
                // In Selltis DB, Status and Reason Won/Lost are the same as in Quote
                // goP.TraceLine("Setting MLS_STATUS from Quote: '" & doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2) & "'", "", sProc)
                // CS 7/3/07: Allow setting status of QL independent of QT
                // doForm.doRS.SetFieldVal("MLS_STATUS", doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 1), 1)
                // goP.TraceLine("Setting MLS_REASONWONLOST from Quote: '" & doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2) + "'", "", sProc)
                // doForm.doRS.SetFieldVal("MLS_REASONWONLOST", doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 1), 1)
                // 'In Selltis DB, Status and Reason Won/Lost are the same as in Quote
                // If doForm.GetFieldVal("MLS_STATUS",2) <> doForm.GetFieldVal("LNK_IN_QT%%MLS_STATUS",2) Then
                // doForm.SetFieldVal("MLS_STATUS",doForm.GetFieldVal("LNK_IN_QT%%MLS_STATUS",2),2)
                // End If
                // If doForm.GetFieldVal("MLS_REASONWONLOST",2) <> doForm.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST",2) Then
                // doForm.SetFieldVal("MLS_REASONWONLOST",doForm.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST",2),2)
                // End If
                // ******************************
                // ----------------------------------------
                // If quote set to set status of QL from QT, gray out the Status selection on the QL.
                // QLEditStatus is set in the button click of the Edit button on the QT form.
                // CS 6/12/08: 
                // NOTE: There is an issue if you open a QT that previously had the 'Update 
                // QL status' checkbox checked and now uncheck it and click the Edit button to edit the QL.
                // In this case the code below still reports the Update QL Status as checked and therefore
                // the status on the QL you are trying to edit is still grayed out.
                // CS 6/20/08: 
                // Could be directly opening a QL record which means we need to check
                // the linked QT OR could be opening from the Edit button of the QT form
                // or double clicking the line from the LInes linkbox on Quote
                string sMOde = doForm.LinkboxAction;
                // CS 6/20/08 If WOP to manage QLs is off, always gray out status field of QL
                // During save of QT, the QL status will be updated to match the QT, even if it had 
                // previously been on.
                if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_STATUSMGMT") != "1")
                {
                    doForm.SetControlState("MLS_STATUS", 4);
                    doForm.SetControlState("MLS_REASONWONLOST", 4); // CS 5/28/10
                }
                else if (Strings.InStr(Strings.UCase(sMOde), "CLICKTHRU_QT_LNK_CONNECTED_QL") == 0 & doForm.oVar.GetVar("QLOpenFromEditButton") != "1")
                {
                    // Try to check the linkboxaction to determine if QL form is being opened
                    // as a result of a linkbox click through
                    // If QL not opened as result of click thru in Lines linkbox or from clicking edit button need to get QT status
                    // from Quote link; otherwise need from QT form in history
                    if (Strings.UCase(doForm.doRS.GetFieldVal("LNK_IN_QT%%CHK_UPDQLSTATUS").ToString()) == "CHECKED")
                    {
                        // If (UCase(doForm.doRS.GetFieldVal("LNK_IN_QT%%CHK_UPDQLSTATUS")) = "CHECKED" AND goP.GetVar(") Or goP.GetVar("QLEditStatus") <> "1" Then
                        doForm.SetControlState("MLS_STATUS", 4);
                        doForm.SetControlState("MLS_REASONWONLOST", 4); // CS 5/28/10
                    }
                    else
                    {
                        // status active
                        doForm.SetControlState("MLS_STATUS", 0);
                        doForm.SetControlState("MLS_REASONWONLOST", 0); // CS 5/28/10
                    }
                }
                //else
                //{
                //    // New: Get QT form in history for this QL
                //    string sQuoteID = Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_QT"));
                //    int i;
                //    Form oForm;
                //    string sHistory = "";
                //    clArray oHistory = goUI.GetUIObjectGUIDs;
                //    if (oHistory.GetDimension() > 0)
                //    {

                //        // CS 9/9/08: Loop thru items in UI stack and find QT for this QL
                //        for (i = 1; i <= oHistory.GetDimension(); i++)
                //        {
                //            sHistory = oHistory.GetItem(i);
                //            // Check if a form, if so, check if is the linked QT for the QL
                //            //object oObject = goUI.GetUIObject(sHistory);
                //            if (Strings.UCase(oObject.GetType().ToString()) == "CLFORM")
                //            {
                //                oForm = (Form)oObject;
                //                if (oForm.GetRecordID() == sQuoteID)
                //                {
                //                    if (Strings.UCase(oForm.doRS.GetFieldVal("CHK_UPDQLSTATUS").ToString()) == "CHECKED")
                //                    {
                //                        doForm.SetControlState("MLS_STATUS", 4);
                //                        doForm.SetControlState("MLS_REASONWONLOST", 4); // CS 5/28/10
                //                    }
                //                    else
                //                    {
                //                        // status active
                //                        doForm.SetControlState("MLS_STATUS", 0);
                //                        doForm.SetControlState("MLS_REASONWONLOST", 0); // CS 5/28/10
                //                    }
                //                    break;
                //                }
                //            }
                //            else
                //            {
                //            }
                //        }
                //    }
                //}


                // CS 8/27/08: Quote line users mgmt
                if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_USERMGMT") != "1")
                {
                    doForm.SetControlState("LNK_CREDITEDTO_US", 4);
                    doForm.SetControlState("LNK_PEER_US", 4);
                }
                else
                {
                    doForm.SetControlState("LNK_CREDITEDTO_US", 0);
                    doForm.SetControlState("LNK_PEER_US", 0);
                }
            }

            // CS: think not needed---------- VARIABLES -----------
            // doForm.oVar.SetVar("sForSKUOrigVal", doForm.dors.GetFieldVal("LNK_FOR_MO", 2))
            // doForm.oVar.SetVar("srQtyEnterVal", doForm.dors.GetFieldVal("SR__QTY", 2))

            // ----------- STATES ----------
            // ******************************
            // In Selltis DB, Status and Reason Won/Lost are the same as in Quote
            // CS: Commenting b/c SetFieldProperty causes syntax error
            // doForm.SetFieldProperty("MLS_STATUS", "STATE", Grayed)
            // doForm.SetFieldProperty("MLS_REASONWONLOST", "STATE", Grayed)
            // ******************************

            goP.SetVar("QLEditStatus", "");

            return true;
            par_doCallingObject = doForm;
            return true;
        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/6/2014 Unheck Include when Status is Lost
            // Do here & Recordonsave for calcs in case called
            // from QT?
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Status", 2)) == 3)
            {
                doForm.doRS.SetFieldVal("CHK_Include", 0, 2);

            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // TLD 8/6/2014 Uncheck Include when Status is Lost
            if (Convert.ToInt32(doRS.GetFieldVal("MLS_Status", 2)) == 3)
            {
                doRS.SetFieldVal("CHK_Include", 0, 2);

            }

            return true;
        }
        public bool QT_FormControlOnChange_BTN_RECALCNOW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/4/2014 Copy SR__CompletedPerc to connected Quote Lines where Status is Won
            // This is ONLY done when CHK_ApplyPercComplete is checked AND QL status is Won

            // TLD 6/9/2014 Added a check for Status is Won, don't want to recalc for QLs
            // where status is Won if QT is not won?
            // Save if new or changed?
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_ApplyPercComplete", 2)) == 1 & (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Status", 2))) == 2)
            {
                if (doForm.GetMode() == "CREATION" | doForm.IsDirty)
                {
                    if (doForm.Save(3, false, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                    {
                    }
                }
                scriptManager.RunScript("Quote_CopyCompletedPerc", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool QL_FormControlOnChange_CUR_CompleteToDate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/4/2014 Call QuotLine_CalcTotal to recalc
            // to Be Paid

            Form doForm = (Form)par_doCallingObject;

            scriptManager.RunScript("Quotline_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            par_doCallingObject = doForm;

            return true;
        }
        public bool QT_FormControlOnChange_LNK_RELATED_ST_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 5/30/2014 Fill SR__SalesTaxPercent
            doForm.doRS.SetFieldVal("SR__SalesTaxPercent", 0, 2);
            if (doForm.doRS.IsLinkEmpty("LNK_Related_ST") != true)
            {
                doForm.doRS.SetFieldVal("SR__SalesTaxPercent", doForm.doRS.GetFieldVal("LNK_Related_ST%%SR__RateTotal"));

            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 6/4/2014 Set Status to Var
            // used in determining whether to renumber Quote Lines
            goP.SetVar("Quote_Status", doForm.doRS.GetFieldVal("MLS_Status", 2));

            // TLD 6/10/2014 Disable calculated fields
            doForm.SetControlState("CUR_AmountThisPeriod", 4);
            doForm.SetControlState("CUR_AmountPaidPrevious", 4);
            doForm.SetControlState("CUR_GrossAmountDue", 4);
            doForm.SetControlState("CUR_AmountDuetoDate", 4);
            doForm.SetControlState("CUR_HoldBackAmount", 4);
            // TLD 6/30/2014 Disable filled date
            doForm.SetControlState("DTE_JobCreation", 4);
            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);
            doForm.SetFieldProperty("LNK_PEER_US", "LABELCOLOR", "#666666");
            doForm.SetFieldProperty("LNK_TAKENAT_LO", "LABELCOLOR", "#666666");

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }

            par_doCallingObject = doForm;

            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sInvoiceNo = "";
            string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("Gid_id"));

            // TLD 6/9/2014 Assign sequential Invoice # when Stage is Invoice
            // and Invoice # is blank
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Stage", 2)) == 3 & Convert.ToString(doForm.doRS.GetFieldVal("TXT_InvoiceNo")) == "")
            {
                // TLD 8/6/2014 Commented, they no longer want it generated
                // If goScr.RunScript("Quote_GenerateInvoiceNo", doForm, , , , , , , sInvoiceNo) <> True Then
                // 'do nothing?
                // Else
                // doForm.doRS.SetFieldVal("TXT_InvoiceNo", sInvoiceNo)
                // End If

                // TLD 6/9/2014 Enforce Invoice # if it is blank for some reason
                if (sInvoiceNo == "")
                {
                    doForm.MoveToField("TXT_InvoiceNo");
                    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("TXT_InvoiceNo"), "", "", "", "", "", "", "", "", "TXT_InvoiceNo");
                    return false;
                }
            }
            //for templates fields from QL to Qt
            clRowSet rsQL1 = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sGidId + "'", "LNK_IN_QT", "**");

            StringBuilder sb = new StringBuilder();
            StringBuilder sb1 = new StringBuilder();

            int rowCount = Convert.ToInt32(rsQL1.Count()); // Get the total number of rows

            for (int i = 0; i < rowCount; i++)
            {
                sb.Append(Convert.ToString(rsQL1.GetFieldVal("TXT_MODEL")) + Environment.NewLine + "<br />");
                sb1.Append(Convert.ToString(rsQL1.GetFieldVal("MMO_Details")) + Environment.NewLine + "<br />");
                rsQL1.GetNext();
            }

            doForm.doRS.SetFieldVal("MMO_QUOTELINEMODEL", sb.ToString().Trim());
            doForm.doRS.SetFieldVal("MMO_QUOTELINEDETAILS", sb1.ToString().Trim());


            par_doCallingObject = doForm;

            return true;
        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            DateTime dtInvoiceDate;
            string sInvoiceDate = "";
            int iDays = 0;
            string par_sDelim = "|";

            // TLD 6/12/2014 Fill Invoice Date with today if Invoiced is checked
            // and Invoice Date is blank
            dtInvoiceDate = Convert.ToDateTime(doForm.doRS.GetFieldVal("DTT_InvoiceDate", 2));
            sInvoiceDate = goTR.DateTimeToSysString(dtInvoiceDate, ref par_iValid, ref par_sDelim);
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Invoiced", 2)) == 1 & sInvoiceDate == "")
            {
                dtInvoiceDate = goTR.NowUTC();
                sInvoiceDate = goTR.DateTimeToSysString(dtInvoiceDate, ref par_iValid, ref par_sDelim);
                doForm.doRS.SetFieldVal("DTT_InvoiceDate", dtInvoiceDate, 2);
            }

            // TLD 6/12/2014 Fille Due Date when Invoice Date is not blank
            if (Convert.ToString(doForm.doRS.GetFieldVal("DTT_DueDate", 1)) == "" & sInvoiceDate != "")
            {
                // Calc Due Date
                if (doForm.doRS.IsLinkEmpty("LNK_Related_TR") != true)
                {
                    iDays = Convert.ToInt32(doForm.doRS.GetFieldVal("LNK_Related_TR%%INT_DueInDays", 2));

                }
                doForm.doRS.SetFieldVal("DTT_DueDate", goTR.AddDay(dtInvoiceDate, iDays), 2);
            }

            // TLD 6/30/2014 Fill DTT_JobCreation when blank and if Job Creation Sent is checked
            if (Convert.ToString(doForm.doRS.GetFieldVal("DTT_JobCreation", 1)) == "" & Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_JobCreationSent", 2)) == 1)
            {
                doForm.doRS.SetFieldVal("DTT_JobCreation", "Today|Now");

            }
            par_doCallingObject = doForm;

            return true;
        }
        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/28/2014 copied from main, customize to copy fields
            par_bRunNext = false;

            // MI 2/25/07 Added bDoNotUpdateQuote

            // PURPOSE:
            // Update all linked Quote Lines with information from the Quote.

            // goP.TraceLine("", "", sProc)

            clRowSet doQuote;
            clRowSet doRS;
            clArray doLink;
            // Dim sWork As String
            string sSysName = "";
            clRowSet doCO;
            string sTerr;
            clRowSet doMO;
            string sVendor;
            // Dim sWork2 As String
            DateTime dtDateTime;
            // Dim sVend As String
            string sCustCode;
            // TLD 5/30/2014 Added
            int iStage = 0;
            // TLD 6/4/2014
            string sStatus = "0"; // Current status
            string sOrigStatus = goP.GetVar("Quote_Status").ToString();
            bool bRenumberLine = false;
            int iLineNo = 0;

            doQuote = (clRowSet)par_doCallingObject;

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


            // If bDoNotUpdateQuote was not reset earlier due to an error, reset it now
            goP.SetVar("bDoNotUpdateQuote", "0");

            // ------------------ ENFORCE --------------
            // If goP.GetRunMode <> "Import" Then
            if (doQuote.bBypassValidation != true)
            {
                // Enforce mandatory fields
                // CS 7/6/07: In MD
                // If doQuote.GetLinkCount("LNK_CREDITEDTO_US") < 1 Then
                // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "lnk_creditedto_us"), "", "", "", "", "", "", "", "", "lnk_creditedto_us")
                // Return False
                // End If
                // If doQuote.GetLinkCount("LNK_PEER_US") < 1 Then
                // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "lnk_peer_us"), "", "", "", "", "", "", "", "", "lnk_peer_us")
                // Return False
                // End If
                // If doQuote.GetLinkCount("LNK_ORIGINATEDBY_CN") < 1 Then
                // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "lnk_originatedby_cn"), "", "", "", "", "", "", "", "", "lnk_originatedby_cn")
                // Return False
                // End If
                // If doQuote.GetLinkCount("LNK_TO_CO") < 1 Then
                // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "lnk_to_co"), "", "", "", "", "", "", "", "", "lnk_to_co")
                // Return False
                // End If
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceReasonWonLostAndDateClosed"))
                {
                    switch (doQuote.GetFieldVal("MLS_STATUS", 2))
                    {
                        case 2      // Won
                       :
                            {
                                if (Convert.ToInt32(doQuote.GetFieldVal("MLS_REASONWONLOST", 2)) == 0)
                                {
                                    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "mls_reasonwonlost"), "", "", "", "", "", "", "", "", "mls_reasonwonlost");
                                    return false;
                                }
                                else if (goTR.IsDate(Convert.ToString(doQuote.GetFieldVal("DTE_DATECLOSED", 1))) == false | doQuote.GetFieldVal("DTE_DateClosed") == "")
                                    doQuote.SetFieldVal("DTE_DATECLOSED", goTR.NowLocal(), 2);
                                break;
                            }

                        case 3      // Lost
                 :
                            {
                                if (Convert.ToInt32(doQuote.GetFieldVal("MLS_REASONWONLOST", 2)) == 0)
                                {
                                    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", "mls_reasonwonlost"), "", "", "", "", "", "", "", "", "mls_reasonwonlost");
                                    return false;
                                }
                                else if (goTR.IsDate(Convert.ToString(doQuote.GetFieldVal("DTE_DATECLOSED", 1))) == false | doQuote.GetFieldVal("DTE_DateClosed") == "")
                                    doQuote.SetFieldVal("DTE_DATECLOSED", goTR.NowLocal(), 2);
                                break;
                            }
                    }
                }
            }


            // ------------ AUTO-FILLED FIELDS ------------
            // Fill CHK_Closed when Status is Open or On Hold
            switch (doQuote.GetFieldVal("MLS_STATUS", 2))
            {
                case 0:
                case 1       // Open, On Hold
               :
                    {
                        doQuote.SetFieldVal("CHK_OPEN", 1, 2);
                        break;
                    }

                default:
                    {
                        doQuote.SetFieldVal("CHK_OPEN", 0, 2);
                        break;
                    }
            }

            // CS: Commented out. Only fill in controlonchange of DM
            // 'Fill Shipping from 'Related Delivery Method' only if something is linked
            // If doQuote.GetLinkCount("LNK_RELATED_DM") > 0 And doQuote.GetFieldVal("CUR_SHIPPING", 2) = 0 Then
            // doQuote.SetFieldVal("CUR_SHIPPING", doQuote.GetFieldVal("LNK_RELATED_DM%%CUR_SHIPPINGCHARGE", 1))
            // End If


            // Fill Month/Year fields used for grouping/totaling in reports
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillYearMonthDayFields"))
            {
                // 'Old non-UTC-aware code
                // doQuote.SetFieldVal("TXT_YEAR", goTR.GetYear(doQuote.GetFieldVal("DTT_TIME", 2)))
                // doQuote.SetFieldVal("SI__MONTH", goTR.StringToNum(goTR.GetMonth(doQuote.GetFieldVal("DTT_TIME", 2))))

                DateTime _dt = Convert.ToDateTime(doQuote.GetFieldVal("DTT_TIME", 2));
                dtDateTime = goTR.UTC_LocalToUTC(ref _dt);
                doQuote.SetFieldVal("TXT_YEAR", goTR.GetYear(dtDateTime));
                doQuote.SetFieldVal("SI__MONTH", goTR.StringToNum(goTR.GetMonth(dtDateTime), "", ref par_iValid, ""));
                if (goData.IsFieldValid("QT", "SI__Day"))
                    doQuote.SetFieldVal("SI__Day", goTR.StringToNum(goTR.GetDay(dtDateTime), "", ref par_iValid, ""));
            }


            // ---------------- FILL FIELDS -------------------
            if (goP.GetRunMode() != "Import")
            {
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillSignature"))
                    scriptManager.RunScript("FillSignature", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillSendTo"))
                    scriptManager.RunScript("Quote_FillSendTo", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                // is below: goScr.RunScript("Quote_FillInvolvesContact", doForm)

                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillRFQNo"))
                {
                    // CS: this was in controlonleave
                    if (doQuote.GetFieldVal("TXT_RFQNO") == "")
                        doQuote.SetFieldVal("TXT_RFQNO", "Verbal");
                }

                // CS Debug
                // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before fill involves links" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

                // LNK_INVOLVES_COMPANY
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillInvolvesCompany"))
                {
                    doLink = new clArray();
                    oTable = null;
                    doLink = doQuote.GetLinkVal("LNK_TO_CO", ref doLink, true, 0, -1, "A_a", ref oTable);
                    // If doLink <> vbNull Then
                    doQuote.SetLinkVal("LNK_INVOLVES_CO", doLink);
                    // delete(doLink)
                    doLink = null/* TODO Change to default(_) if this is not a reference type */;
                }

                // LNK_INVOLVES_CONTACT
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillInvolvesContact"))
                {
                    doLink = new clArray();
                    oTable = null;

                    doLink = doQuote.GetLinkVal("LNK_TO_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
                    doLink = doQuote.GetLinkVal("LNK_CC_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
                    doLink = doQuote.GetLinkVal("LNK_BC_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
                    // If doLink <> vbNull Then
                    doQuote.SetLinkVal("LNK_INVOLVES_CN", doLink);
                    // delete(doLink)
                    doLink = null/* TODO Change to default(_) if this is not a reference type */;
                }

                // LNK_INVOLVES_USER
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillInvolvesUser"))
                {
                    doLink = new clArray();
                    oTable = null;

                    doLink = doQuote.GetLinkVal("LNK_CREDITEDTO_US", ref doLink, true, 0, -1, "A_a", ref oTable);
                    doLink = doQuote.GetLinkVal("LNK_PEER_US", ref doLink, true, 0, -1, "A_a", ref oTable);
                    // If doLink <> vbNull Then
                    doQuote.SetLinkVal("LNK_INVOLVES_US", doLink);
                    // delete(doLink)
                    doLink = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }

            // TLD 6/10/2014 Set Payment Period
            doQuote.SetFieldVal("DTT_PaymentPeriod", "End of this month|Now");

            // --------- UPDATE QUOTE LINES ----------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "UpdateQuoteLines"))
            {
                // Never update Quote Lines in import mode
                if (goP.GetRunMode() != "Import")
                {
                    // Update Quote Lines only if this isn't running as a result of Quote Line recalcing the Quote
                    if (Convert.ToString(goP.GetVar("bDoNotUpdateQuoteLines")) != "1")
                    {
                        // Bypass validation i Quote Lines

                        // CS Debug
                        // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before Quote Line rowset-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

                        // TLD 6/4/2014 If QT status changed from anything to Won or Lost
                        // need to renumber Quote Lines based on status
                        int iStatus = Convert.ToInt32(doQuote.GetFieldVal("MLS_STATUS", 2));
                        sOrigStatus = Convert.ToString(goP.GetVar("Quote_Status"));
                        if (Convert.ToString(iStatus) != Convert.ToString(sOrigStatus) & (iStatus == 2 | iStatus == 3))
                        {
                            bRenumberLine = true;
                            doRS = new clRowSet("QL", 1, "LNK_IN_QT='" + doQuote.GetFieldVal("GID_ID") + "'", "MLS_Status ASC", "*", 1, null, null, null, null, null, true);
                            // prepend history
                            doQuote.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(doQuote.GetFieldVal("MMO_HISTORY").ToString(), "QL Line Numbers reset."));
                        }
                        else
                            doRS = new clRowSet("QL", 1, "LNK_IN_QT='" + doQuote.GetFieldVal("GID_ID") + "'", "DTT_QTETIME D, SR__LINENO ASC", "*", 1, null, null, null, null, null, true);

                        // CS Debug
                        // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("After Quote Line rowset-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                        // CS:See line 12250. This causes an error b/c I can't pass a multi-level link in doQuote rowset.Sent
                        // this as a To Do to MI.

                        if (doRS.GetFirst() == 1)
                        {
                            // CS Debug
                            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before doQuote GetFieldVals-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                            // CS 6/1/09 Get values to set in the QLS once and then set vars in loop below
                            DateTime dtDate = Convert.ToDateTime(doQuote.GetFieldVal("DTE_TIME", 2));
                            DateTime dtTime = Convert.ToDateTime(doQuote.GetFieldVal("TME_TIME", 2));
                            DateTime dtExpClose = Convert.ToDateTime(doQuote.GetFieldVal("DTE_EXPCLOSEDATE", 2));
                            clArray aCompany = (clArray)doQuote.GetFieldVal("LNK_TO_CO", 2);
                            clArray aPeer = (clArray)doQuote.GetFieldVal("LNK_PEER_US", 2);
                            clArray aProject = (clArray)doQuote.GetFieldVal("LNK_RELATED_PR", 2);
                            string sProject = Convert.ToString(doQuote.GetFieldVal("LNK_RELATED_PR", 1)); // get as string to set in project var below
                            clArray aInvolves = (clArray)doQuote.GetFieldVal("LNK_InVOLVES_US", 2);
                            // TLD 6/4/2014 Moved this to above
                            // Dim iStatus As Integer = doQuote.GetFieldVal("MLS_STATUS", 2)
                            clArray aCreditedTo = (clArray)doQuote.GetFieldVal("LNK_CREDITEDTO_US", 2);
                            int iOpen = Convert.ToInt32(doQuote.GetFieldVal("CHK_OPEN", 2));
                            int iReason = Convert.ToInt32(doQuote.GetFieldVal("MLS_REASONWONLOST", 2));
                            // TLD 5/28/2014 Added 4 fields
                            DateTime dtCompleted = Convert.ToDateTime(doQuote.GetFieldVal("DTT_Completed", 2));
                            string sInvoiceNo = Convert.ToString(doQuote.GetFieldVal("TXT_InvoiceNo"));
                            string sJobNo = Convert.ToString(doQuote.GetFieldVal("TXT_JobNo"));
                            int iInvoiced = Convert.ToInt32(doQuote.GetFieldVal("CHK_Invoiced", 2));
                            // TLD 5/30/2014 Update QT Stage
                            if (iStatus == 2)
                            {
                                iStage = 2; // Job order
                                doQuote.SetFieldVal("MLS_Stage", 2, 2);

                                // TLD 5/30/2014 Set DTT_DueDate when blank to DTT_Completed + 30 days
                                if (doQuote.GetFieldVal("DTE_Completed", 1) != "")
                                {
                                    if (goTR.IsDate(Convert.ToString(dtCompleted)))
                                        doQuote.SetFieldVal("DTT_DueDate", goTR.AddDay(dtCompleted, 30), 2);
                                }
                            }
                            else
                                iStage = Convert.ToInt32(doQuote.GetFieldVal("MLS_Stage", 2));
                            // TLD 6/4/2014 Moved this to Recalc Now button ONLY, not ON save
                            // Dim rCompletePerc As Double = doQuote.GetFieldVal("SR__CompletedPerc", 2)

                            // CS Debug
                            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("After doQuote GetfieldVals-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

                            // CS 8/8/11: No longer need to get the Company Vendor. It will not be set in linked QLs. QL Vendor will be
                            // filled from MO Vendor only.
                            // doCO = New clRowSet("CO", 3, "GID_ID = '" & doQuote.GetFieldVal("LNK_TO_CO") & "'", , "LNK_IN_TE,LNK_RELATED_VE,TXT_CUSTCODE")
                            doCO = new clRowSet("CO", 3, "GID_ID = '" + doQuote.GetFieldVal("LNK_TO_CO") + "'", null, "LNK_IN_TE,TXT_CUSTCODE");
                            if (doCO.Count() > 0)
                            {
                                sTerr = Convert.ToString(doCO.GetFieldVal("LNK_In_TE"));
                                // CS 6/2/09: Assuming that every QL will have the same 'to company' so can get these values here and use
                                // on all QLs                            
                                // CS 8/8/11 sVend = doCO.GetFieldVal("LNK_Related_VE")
                                sCustCode = Convert.ToString(doCO.GetFieldVal("TXT_CustCode"));
                            }
                            else
                            {
                                sTerr = "";
                                // sVend = ""
                                sCustCode = "";
                            }

                            // CS 6/2/09 Create a string to be used in QL_RecOnSave so that we don't have to get double hops
                            // each time
                            string sWork = "";
                            goTR.StrWrite(ref sWork, "QT_LNK_TO_CO", aCompany.GetItem(1));
                            goTR.StrWrite(ref sWork, "QT_DTT_TIME", dtTime);
                            goTR.StrWrite(ref sWork, "QT_LNK_PEER_US", aPeer.GetItem(1));
                            goTR.StrWrite(ref sWork, "QT_LNK_CREDITEDTO_US", aCreditedTo.GetItem(1));
                            goTR.StrWrite(ref sWork, "QT_LNK_RELATED_PR", sProject);
                            goTR.StrWrite(ref sWork, "CO_LNK_IN_TE", sTerr);
                            // CS 8/8/11: No longer fill QL Related VE with CO's VE. It will be filled
                            // from MO's VE only.
                            // goTR.StrWrite(sWork, "CO_LNK_RELATED_VE", sVend)
                            goTR.StrWrite(ref sWork, "CO_TXT_CUSTCODE", sCustCode);
                            goTR.StrWrite(ref sWork, "QT_SALESTAXPERCENT", doRS.GetFieldVal("SR__SALESTAXPERCENT"));

                            goP.SetVar("QuoteInfo_" + doQuote.GetFieldVal("GID_ID"), sWork);

                            // goP.TraceLine("First found, setting bDoNotUpdateQuote to 1", "", sProc)
                            goP.SetVar("bDoNotUpdateQuote", "1");
                            do
                            {
                                // TLD 6/4/2014
                                if (bRenumberLine)
                                {
                                    if (iLineNo == 0)
                                        iLineNo = 1;
                                    else
                                        iLineNo += 1;
                                    doRS.SetFieldVal("SR__LineNo", iLineNo, 2);
                                }

                                // CS Added: Have to get LNK_TO_CO%%LNK_IN_TE and LNK_FOR_MO%%LNK_RELATED_VE b/c 
                                // multihop links are not allowed in the FIELDS line of doQuote.
                                doRS.SetFieldVal("DTE_QTETIME", dtDate, 2);
                                doRS.SetFieldVal("TME_QTETIME", dtTime, 2);
                                doRS.SetFieldVal("DTE_EXPCLOSEDATE", dtExpClose, 2);
                                doRS.ClearLinkAll("LNK_TO_CO");
                                doRS.SetFieldVal("LNK_TO_CO", aCompany, 2);
                                // doRS.SetFieldVal("LNK_FOR_PD",doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD"))
                                doRS.ClearLinkAll("LNK_PEER_US");
                                doRS.SetFieldVal("LNK_PEER_US", aPeer, 2);
                                doRS.ClearLinkAll("LNK_RELATED_PR");
                                doRS.SetFieldVal("LNK_RELATED_PR", aProject, 2);
                                doRS.ClearLinkAll("LNK_RELATED_TE");
                                doRS.SetFieldVal("LNK_RELATED_TE", sTerr);
                                doRS.ClearLinkAll("LNK_RELATED_VE");

                                // CS Added
                                // doMO = New clRowSet("MO", 3, "GID_ID = '" & doRS.GetFieldVal("LNK_FOR_MO") & "'", , "LNK_RELATED_VE")
                                // If doMO.Count() > 0 Then
                                // sVendor = doMO.GetFieldVal("LNK_Related_VE")
                                // doRS.SetFieldVal("LNK_RELATED_VE", sVendor)
                                // End If
                                // CS 6/2/09: commented above and use variable if set
                                if (goP.GetVar("QuoteInfo_" + doQuote.GetFieldVal("GID_ID")) == "")
                                {
                                    doMO = new clRowSet("MO", 3, "GID_ID = '" + doRS.GetFieldVal("LNK_FOR_MO") + "'", null, "LNK_RELATED_VE");
                                    if (doMO.Count() > 0)
                                    {
                                        sVendor = Convert.ToString(doMO.GetFieldVal("LNK_Related_VE"));
                                        doRS.SetFieldVal("LNK_RELATED_VE", sVendor);
                                    }
                                }


                                // doRS.SetFieldVal("LNK_RELATED_VE", doRS.GetFieldVal("LNK_FOR_MO%%LNK_RELATED_VE"))
                                doRS.ClearLinkAll("LNK_INVOLVES_US");
                                doRS.SetFieldVal("LNK_INVOLVES_US", doRS.GetFieldVal("LNK_CREDITEDTO_US", 2), 2);
                                // doRS.SetFieldVal("LNK_INVOLVES_US",doRS.GetFieldVal("LNK_CREATEDBY_US"))
                                doRS.SetFieldVal("LNK_INVOLVES_US", doRS.GetFieldVal("LNK_PEER_US", 2), 2);
                                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "SetInvolvesUsFromQT"))
                                    // CS 4/20/09
                                    // Set Involves User from QT
                                    // Per DF, this needed b/c users are setting Inv User on QT and this
                                    // is not carried over to the QL and they are using the Inv User
                                    // field for permissions.
                                    doRS.SetFieldVal("LNK_INVOLVES_US", aInvolves, 2);
                                // -------- Status, Reason Won/Lost ----------
                                // In Selltis DB, status and reason won/lost are the same as in the Quote
                                // Remove the following line to manage Status independently from Quote
                                // CS: 7/3/07: Per PJ we are allowing managing QL status independently from QT depending upon
                                // a checkbox.
                                if (Convert.ToString(goP.GetVar("USEQTSTATUS")) == "1")
                                {
                                    doRS.SetFieldVal("MLS_STATUS", iStatus, 2);
                                    doRS.SetFieldVal("MLS_REASONWONLOST", iReason, 2);
                                }
                                // CS 8/27/08: 
                                if (Convert.ToString(goP.GetVar("USEQTUSERS")) == "1")
                                {
                                    doRS.SetFieldVal("LNK_CREDITEDTO_US", aCreditedTo, 2);
                                    doRS.SetFieldVal("LNK_PEER_US", aPeer, 2);
                                }

                                doRS.SetFieldVal("CHK_OPEN", iOpen, 2);
                                // Remove the following line to manage Reason Won/Lost independently from Quote
                                // CS 5/28/10 doRS.SetFieldVal("MLS_REASONWONLOST", iReason, 2)
                                // -------------------------------------------

                                // TLD 5/28/2014 Copy custom fields
                                doRS.SetFieldVal("TXT_JobNo", sJobNo);
                                doRS.SetFieldVal("TXT_InvoiceNo", sInvoiceNo);
                                doRS.SetFieldVal("DTT_TimeCompleted", dtCompleted, 2);
                                doRS.SetFieldVal("CHK_Invoiced", iInvoiced, 2);
                                // TLD 5/30/2014 Added
                                doRS.SetFieldVal("MLS_Stage", iStage, 2);
                                // TLD 6/4/2014 Moved this to Recalc Now button ONLY, not ON save
                                // doRS.SetFieldVal("SR__CompletedPerc", rCompletePerc, 2)

                                // CS Debug
                                // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before Quote Line commit-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

                                if (doRS.Commit() != 1)
                                {
                                    goErr.SetWarning(30200, sProc, "", "An error '" + goErr.GetLastError("NUMBER") + "' occurred in procedure '" + goErr.GetLastError("PROCEDURE") + "'." + Constants.vbCrLf + Constants.vbCrLf + "Quote Lines could not be updated." + Constants.vbCrLf + Constants.vbCrLf + goErr.GetLastError("MESSAGE"), "", "", "", "", "", "", "", "", "");
                                    // Exit the script
                                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                                    goP.SetVar("bDoNotUpdateQuote", "0");
                                    return false;
                                }
                                else
                                {
                                }

                                if (doRS.GetNext() == 0)
                                    break;
                            }
                            while (true);
                            goP.SetVar("bDoNotUpdateQuote", "0");
                        }
                        doRS = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                }
            }

            // CS 6/2/09: Reset var
            goP.DeleteVar("QuoteInfo_" + doQuote.GetFieldVal("GID_ID"));

            // CS 7/29/09: SHOULD I RECALC THE QT TOTAL HERE? THIS IS ONLY REALLY NEEDED IN THE CASE THAT A QT ROWSET IS CREATED, A VALUE IS
            // UPDATED ON THE QT AND THE QT IS COMMITTED. IF a QL ROWSET IS CREATED AND EDITED, THEN QT TOTAL DOES GET UPDATED. IF A QL IS 
            // DELETED, QT TOTAL DOES GETS UPDATED. THIS ISSUE IS THAT YOU MIGHT CREATE A QT ROWSET, UPDATE SHIPP CHG AND COMMIT. IN THIS CASE QT TOTAL WILL BE WRONG.
            // IN THE FORM CONTEXT, CALCTOTAL IS CALLED IN QT FORMONSAVE AS WELL AS SOME OF THE BUTTON EVENTS, LIKE LINEDUPLICATE.
            // SETTING A VARIABLE IN QT_FORMONLOADRECORD TO LET ME KNOW THAT WE HIT THAT SCRIPT. IN THAT CASE WE DON'T NEED TO RECALC QT TOTALS HERE AGAIN
            // B/C THEY WILL BE CALCED IN FORMONSAVE.
            // ----------- Recalc Quote totals -------------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "RecalcQuoteTotals"))
            {
                // Cs 11/6/09: Added checking for var(bDoNotRecalcQuoteTotal) set in CalcQuoteTotal. See note there.
                if (goP.GetVar("OpenQTForm") != "1" & goP.GetVar("bDoNotRecalcQuoteTotal") != "1")
                    // CS Debug
                    // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Before Recalc Quote Totals-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


                    scriptManager.RunScript("CalcQuoteTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doQuote.GetFieldVal("GID_ID")));
            }

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


            // Reset var
            // CS 6/23/08 Need to set this in FormAfterSave b/c it is being reset
            // before the Quote is finished all of its processing. Qt saving runs through
            // RecOnSave multiple times.
            // goP.SetVar("USEQTSTATUS", "")
            par_doCallingObject = doQuote;
            return true;
        }
        public bool Quote_CopyCompletedPerc_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/4/2014 Copy SR__CompletedPerc to connected Quote Lines where Status is Won
            // This is ONLY done when CHK_ApplyPercComplete is checked AND QL status is Won
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            double rCompletedPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__CompletedPerc", 2));

            try
            {
                // Get QL RS where status is Won
                clRowSet doQLRS = new clRowSet("QL", 1, "LNK_In_QT='" + doForm.doRS.GetCurrentRecID() + "' AND MLS_Status=2", "SR__LineNo ASC", "SR__CompletedPerc", 1, null, null, null, null, null, true, true, true, true, 1, null, true);
                if (doQLRS.GetFirst() == 1)
                {
                    // Get QT Completed Perc
                    do
                    {
                        doQLRS.SetFieldVal("SR__CompletedPerc", rCompletedPerc, 2);
                        if (doQLRS.Commit() == 0)
                            goLog.Log(sProc, "update for QL " + doQLRS.GetCurrentRecID() + " failed with error " + goErr.GetLastError());
                        if (doQLRS.GetNext() == 0)
                            break;
                    }
                    while (true);
                }
            }
            catch (Exception ex)
            {
                goLog.Log(sProc, "failed with error " + goErr.GetLastError());
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool Quote_GenerateInvoiceNoInactive(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 10/1/07 Changed now to goTR.NowUTC().
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PURPOSE:
            // TLD 8/6/2014 Marked inactive, they no longer want this
            // TLD 6/9/2014 generate invoice # -- YYYYMMDD-X (x is sequential number stored in OTH_QTINVOICEPOINTERS
            // RETURNS:
            // Invoice number via return parameter.

            string s = "";

            try
            {
                string sSeq = ""; // TXT_InvoiceNo
                string sDate = ""; // YYYYMMDD
                string sInvWOP = goMeta.PageRead("GLOBAL", "OTH_QTINVOICEPOINTERS", null, true, "XX"); // WOP for QT Invoice #
                int iPos = 0; // Position of Dash
                int iInvoiceNo = 0; // Invoice Sequential #
                DateTime dtDateTime; // today's Date

                // Read WOP seqeuntial #
                sSeq = goTR.StrRead(sInvWOP, "INVOICENO", "", false);
                if (sSeq == "")
                {
                    // Try to find the latest Invoice #?
                    clRowSet doQTRS = new clRowSet("QT", 3, "TXT_InvoiceNo<>'' AND TXT_InvoiceNo[-", "TXT_InvoiceNo ASC", "TXT_InvoiceNo", 1);
                    if (doQTRS.GetFirst() == 1)
                    {
                        sSeq = Convert.ToString(doQTRS.GetFieldVal("TXT_InvoiceNo"));
                        // Get position of dash, sequential # is behind dash
                        iPos = Strings.InStr(sSeq, "-");
                        sSeq = Strings.Right(sSeq, Strings.Len(sSeq) - iPos);
                        // If number, add 1 to existing
                        if (goTR.IsNumber(sSeq))
                        {
                            iInvoiceNo = Convert.ToInt32(goTR.StringToNum(sSeq, "", ref par_iValid, "")) + 1;

                        }
                    }
                    else
                        // no quotes with dashes?
                        iInvoiceNo = 1;
                }
                else
                    // Not blank, is number
                    if (goTR.IsNumber(sSeq))
                {
                    iInvoiceNo = Convert.ToInt32(goTR.StringToNum(sSeq, "", ref par_iValid, ""));

                }

                if (iInvoiceNo == 0)
                {
                    // Set to 1?
                    iInvoiceNo = 1;
                }


                // Capture 4 digit year
                dtDateTime = goTR.NowUTC().Date;
                sDate = goTR.GetYear(dtDateTime) + goTR.GetMonth(dtDateTime) + goTR.GetDay(dtDateTime);

                // Write Next Sequential # to WOP
                goMeta.LineWrite("GLOBAL", "OTH_QTINVOICEPOINTERS", "INVOICENO", iInvoiceNo + 1, ref par_oConnection, null, "XX");

                s = sDate + "-" + iInvoiceNo;
            }
            catch (Exception ex)
            {
                goLog.Log(sProc, "failed with error " + goErr.GetLastError());
            }

            par_oReturn = s;

            return true;
        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/30/2014 Added
            par_bRunNext = false;

            Form doForm = null;
            clRowSet doRS1 = null;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3)
            if (par_s1 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;

            }
            else
            {
                doForm = (Form)par_doCallingObject;

            }

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

            decimal cPriceUnit;
            double rQtyFld;
            double rDiscPerc;
            decimal cDiscAddAmt;
            decimal cCostVal;
            decimal cWork;
            decimal cSubtotal;
            double rSalesTaxPerc;
            // Dim cSKUCost As Decimal
            // Dim sSKUCost As String
            // TLD 5/30/2014 Added
            double rCompletedPerc = 0;
            // TLD 6/4/2014 Added
            int iOverrideAmount = 0;
            decimal cAmountPaidPrevious = 0;
            decimal cCompleteToDate = 0;

            // PURPOSE:
            // Calc Subtotal if Include is checked, otherwise enter 0 as subtotal
            // Field 'Price Unit No Disc' = Unit Price before discount
            // Field 'Price Unit' = Unit Price after discount
            // RETURNS:
            // True.

            // goP.TraceLine("", "", sProc)

            // CS Need to check if coming from RecOnSave b/c in that case we are working with a rowset and 
            // otherwise we are on a form.
            if (par_s1 != "doRS")
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_INCLUDE", 2)) != 1)
                {
                    // Set calculated fields to 0
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                    // TLD 6/12/2014 Don't set Cost to 0, calculated or entered by user
                    // doForm.doRS.SetFieldVal("CUR_COST", 0, 2)
                    doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_SALESTAX", 0, 2);
                    // TLD 5/30/2014
                    doForm.doRS.SetFieldVal("CUR_InvoiceAmount", 0, 2);
                }
                else
                {
                    cPriceUnit = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2));
                    rQtyFld = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));
                    rDiscPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__DISCPERCENT", 2));
                    cDiscAddAmt = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_DISCADDLAMT", 2));
                    // TLD 6/12/2014 Don't pull from model, calculated
                    // or entered by user, so moved below subtotal calc
                    // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                    // cCostVal = doForm.doRS.GetFieldVal("CUR_COST", 2)
                    // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                    // If sSKUCost <> "" Then
                    // cSKUCost = goTR.StringToCurr(sSKUCost)
                    // Else
                    // cSKUCost = 0
                    // End If
                    // cCostVal = cSKUCost * rQtyFld

                    // TLD 5/30/2014 Added
                    rCompletedPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__CompletedPerc", 2));
                    // TLD 6/4/2014 Added
                    iOverrideAmount = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_OverrideAmountThisPeriod", 2));
                    cAmountPaidPrevious = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_AmountPaidPrevious", 2));
                    cCompleteToDate = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_CompleteToDate", 2));

                    // Copy Cost from SKU and multiply it by Qty if the user edited Qty or if Qty is 0
                    // (we set it to 0 above if it is blank)
                    // bUpdCostVal is set to 1 in ControlOnLeave script for 'For Model'.

                    // Check if form variable has been set otherwise get an error comparing srQtyEnterVal as 
                    // a string ("") to a double (rQtyFld)
                    // Per MI, we can get rid of the form var dealing with changing the qty. We will always
                    // recalc regardless of it.
                    // If doForm.oVar.GetVar("srQtyEnterVal") <> "" Then
                    // If (rQtyFld <> doForm.oVar.GetVar("srQtyEnterVal")) Or (doForm.oVar.GetVar("bUpdCostVal")) = 1 Then
                    // If doForm.oVar.Getvar("bUpdCostVal") <> "" Then
                    // If (doForm.oVar.GetVar("bUpdCostVal")) = 1 Then
                    // CS: Moving this to QL_FormControlOnChange_USEMODELPRICE. When the user clicks this button the linked model's cost
                    // will be used.
                    // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                    // If sSKUCost <> "" Then 'CS added this If b/c if coming from QL_FormOnLoad the value
                    // 'is blank for cost and get error
                    // cSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")

                    // cCostVal = cSKUCost * rQtyFld
                    // doForm.doRS.SetFieldVal("CUR_COST", cCostVal)
                    // End If
                    // End If

                    // Calculate unit price after discount
                    cWork = cPriceUnit - Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rDiscPerc) / 100;
                    doForm.doRS.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                    // Calculate Subtotal
                    cSubtotal = Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld) - Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld) * Convert.ToDecimal(rDiscPerc) / 100 + cDiscAddAmt;
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                    // Calc/Manual Entry for CUR_Cost (Budget)
                    if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_OverrideCost", 2)) == 1)
                    {
                        // Override, so grab value
                        cCostVal = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_Cost", 2));
                    }

                    else
                    {
                        // Do not override, calculate
                        cCostVal = goTR.RoundCurr(Convert.ToDecimal(cSubtotal) * Convert.ToDecimal(rDiscPerc)) * Convert.ToDecimal(0.01);
                    }

                    doForm.doRS.SetFieldVal("CUR_COST", cCostVal);

                    // Calc Gross Profit
                    // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                    // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS 6/13/07: Added rQtyField per DF
                    cWork = cSubtotal - cCostVal;
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));

                    // Sales tax
                    if (Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_TAXABLE", 2)))
                    {
                        // CS 6/2/09: Get value from variable if set
                        if (goP.GetVar("QuoteInfo") == "")
                            rSalesTaxPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));
                        else
                            rSalesTaxPerc = goTR.StringToNum(goTR.StrRead(goP.GetVar("QuoteInfo").ToString(), "QT_SALESTAXPERCENT", null, false), "", ref par_iValid, "");


                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", Convert.ToDouble(cSubtotal) * Convert.ToDouble(rSalesTaxPerc) / 100);
                    }
                    else
                    {
                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", 0);
                    }
                    // doForm.oVar.SetVar("bUpdCostVal", "0")

                    // TLD 5/30/2014 Invoice Amount
                    doForm.doRS.SetFieldVal("CUR_InvoiceAmount", Convert.ToDecimal(rCompletedPerc) * Convert.ToDecimal(cSubtotal) * Convert.ToDecimal(0.01), 2);

                    // TLD 6/4/2014 Amount This Period
                    if (iOverrideAmount != 1)
                    {
                        doForm.doRS.SetFieldVal("CUR_AmountThisPeriod", Convert.ToDecimal(rCompletedPerc) * Convert.ToDecimal(cSubtotal) * Convert.ToDecimal(0.01) - cAmountPaidPrevious, 2);

                    }

                    // TLD 6/4/2014 To Be Paid
                    doForm.doRS.SetFieldVal("CUR_ToBePaid", cSubtotal - cCompleteToDate, 2);
                }
            }
            else if (Convert.ToInt32(doRS1.GetFieldVal("CHK_INCLUDE", 2)) != 1)
            {
                // Set calculated fields to 0
                doRS1.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                doRS1.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                // TLD 6/12/2014 Don't set Cost to 0, calculated or entered by user
                // doRS1.SetFieldVal("CUR_COST", 0, 2)
                doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                doRS1.SetFieldVal("CUR_SALESTAX", 0, 2);
                // TLD 5/30/2014
                doRS1.SetFieldVal("CUR_InvoiceAmount", 0, 2);
            }
            else
            {
                cPriceUnit = Convert.ToDecimal(doRS1.GetFieldVal("CUR_PRICEUNIT", 2));
                rQtyFld = Convert.ToDouble(doRS1.GetFieldVal("SR__QTY", 2));
                rDiscPerc = Convert.ToDouble(doRS1.GetFieldVal("SR__DISCPERCENT", 2));
                cDiscAddAmt = Convert.ToDecimal(doRS1.GetFieldVal("CUR_DISCADDLAMT", 2));
                // TLD 6/12/2014 Don't pull from model, calculated
                // or entered by user, so moved below subtotal calc
                // cCostVal = doRS1.GetFieldVal("CUR_COST", 2)
                // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                // Dim sModel As String = goP.GetVar("QuoteLineInfo_" & doRS1.getfieldval("GID_ID"))
                // If sModel = "" Then
                // sSKUCost = doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                // Else
                // sSKUCost = goTR.StrRead(sModel, "MO_CUR_COST", , False)
                // End If
                // If sSKUCost <> "" Then
                // cSKUCost = goTR.StringToCurr(sSKUCost)
                // Else
                // cSKUCost = 0
                // End If
                // cCostVal = cSKUCost * rQtyFld
                // doRS1.SetFieldVal("CUR_COST", cCostVal)

                // TLD 5/30/2014 Added
                rCompletedPerc = Convert.ToDouble(doRS1.GetFieldVal("SR__CompletedPerc", 2));
                // TLD 6/4/2014 Added
                iOverrideAmount = Convert.ToInt32(doRS1.GetFieldVal("CHK_OverrideAmountThisPeriod", 2));
                cAmountPaidPrevious = Convert.ToDecimal(doRS1.GetFieldVal("CUR_AmountPaidPrevious", 2));
                cCompleteToDate = Convert.ToDecimal(doRS1.GetFieldVal("CUR_CompleteToDate", 2));

                // 'CS: only set the cost if it is 0
                // If cCostVal = 0 Then
                // cSKUCost = goTR.StringToCurr(doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST"))
                // cCostVal = cSKUCost * rQtyFld
                // doRS1.SetFieldVal("CUR_COST", goTR.RoundCurr(cCostVal))
                // End If

                // Calculate unit price after discount
                cWork = cPriceUnit - Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rDiscPerc) / 100;
                doRS1.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                // Calculate Subtotal
                cSubtotal = Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld) - Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld) * Convert.ToDecimal(rDiscPerc) / 100 + (cDiscAddAmt);
                doRS1.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                // Calc/Manual Entry for CUR_Cost (Budget)
                if (Convert.ToInt32(doRS1.GetFieldVal("CHK_OverrideCost", 2)) == 1)
                {
                    // Override, so grab value
                    cCostVal = Convert.ToDecimal(doRS1.GetFieldVal("CUR_Cost", 2));
                }

                else
                {
                    // Do not override, calculate
                    cCostVal = goTR.RoundCurr(Convert.ToDecimal(cSubtotal)) * Convert.ToDecimal(rDiscPerc) * Convert.ToDecimal(0.01);
                }

                doRS1.SetFieldVal("CUR_COST", cCostVal);

                // Calc Gross Profit
                cWork = cSubtotal - cCostVal;
                // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS Added rQtyField 
                doRS1.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));

                // Sales tax
                if (Convert.ToBoolean(doRS1.GetFieldVal("CHK_TAXABLE", 2)))
                {
                    // CS 6/2/09:
                    if (goP.GetVar("QuoteInfo") == "")
                    {
                        // CS 10/19/10: Check if have a linked QT. Should always, but causes error if not.
                        if (doRS1.GetLinkCount("LNK_IN_QT") > 0)
                        {
                            rSalesTaxPerc = Convert.ToDouble(doRS1.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));

                        }
                        else
                        {
                            rSalesTaxPerc = 0;

                        }
                    }
                    else
                    {
                        rSalesTaxPerc = goTR.StringToNum(goTR.StrRead(goP.GetVar("QuoteInfo").ToString(), "QT_SALESTAXPERCENT", null, false), "", ref par_iValid, "");

                    }

                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                    doRS1.SetFieldVal("CUR_SALESTAX", Convert.ToDecimal(cSubtotal) * Convert.ToDecimal(rSalesTaxPerc) / 100);
                }
                else
                {
                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0);
                    doRS1.SetFieldVal("CUR_SALESTAX", 0);
                }

                // TLD 5/30/2014 Invoice Amount
                rSalesTaxPerc = 0;
                doRS1.SetFieldVal("CUR_InvoiceAmount", Convert.ToDecimal(rCompletedPerc) * Convert.ToDecimal(cSubtotal) * Convert.ToDecimal(0.01), 2);

                // TLD 6/4/2014 Amount This Period
                if (iOverrideAmount != 1)
                {
                    doRS1.SetFieldVal("CUR_AmountThisPeriod", Convert.ToDecimal(rCompletedPerc) * Convert.ToDecimal(cSubtotal) * Convert.ToDecimal(0.01) - cAmountPaidPrevious, 2);

                }

                // TLD 6/4/2014 To Be Paid
                doRS1.SetFieldVal("CUR_ToBePaid", cSubtotal - cCompleteToDate, 2);
            }
            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

            if (par_s1 == "doRS")
            {
                par_doCallingObject = doRS1;

            }
            else
            {
                par_doCallingObject = doForm;

            }
            return true;
        }
        public bool Quotline_FillItem_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/12/2014 
            par_bRunNext = false;

            // goP.TraceLine("", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            // PURPOSE:
            // TLD 6/12/2014 Don't fill CUR_Cost, calculated or user can override
            // Fill the TXT_MODEL field
            // RETURNS:
            // True.

            // CS: Original code 10/10/07
            // If Trim(doForm.dors.GetFieldVal("TXT_MOdel")) = "" Then
            // If doForm.dors.GetLinkCount("LNK_FOR_MO") > 0 Then
            // 'PJ 10/12/01 Remmed end of line per BKG request
            // 'MI 4/2/08: MMO_Description is replaced with TXT_Description as of 4/3/08.
            // 'If you reenable this code, you MUST test:
            // If goData.IsFieldValid("MO", "TXT_Description") Then
            // '-> Use TXT_Description field's value 
            // Else
            // '-> Use MMO_Description
            // End If
            // doForm.dors.SetFieldVal("TXT_MODEL", doForm.dors.GetFieldVal("LNK_FOR_MO%%MMO_DESCRIPTION")) '& " - " & doForm.GetFieldVal("LNK_FOR_MO%%TXT_MODELNAME")
            // End If
            // End If

            string sSKUPrice;
            decimal cSKUPrice = default(decimal);
            // Dim sSKUCost As String
            // Dim cSKUCost As Decimal
            // Dim rQtyFld As Decimal
            // Dim cCostVal As Decimal
            string sModel;
            string sLinkedModel = "";
            string sUnit;
            clRowSet doRSModel;

            // If no model selected, return
            if (doForm.doRS.GetLinkCount("LNK_FOR_MO") == 0)
                return true;

            // TLD 6/12/2014 Removed CUR_Cost from RS
            if (goData.IsFieldValid("MO", "TXT_Description"))
            {
                // Cs 6/22/09

                doRSModel = new clRowSet("MO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_FOR_MO") + "'", null, "TXT_Description,txt_unittext,lnk_of_pd,cur_price");

            }
            else
            {
                doRSModel = new clRowSet("MO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_FOR_MO") + "'", null, "MMO_Description,txt_unittext,lnk_of_pd,cur_price");

            }

            if (doRSModel.GetFirst() != 1)
                return true;

            // Set Unit price to linked model's price
            // sSKUPrice = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_PRICE", 1) '6/22/09
            sSKUPrice = Convert.ToString(doRSModel.GetFieldVal("CUR_PRICE", 1));
            if (sSKUPrice != "")
            {
                sSKUPrice = sSKUPrice.Replace("$", "");
                cSKUPrice = Convert.ToDecimal(sSKUPrice);

            }

            doForm.doRS.SetFieldVal("CUR_PRICEUNIT", cSKUPrice, 2);

            // TLD 6/12/2014 Don't fill Cost
            // 'Set Cost to linked model's cost
            // 'sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST", 1) 'Cs 6/22/09
            // sSKUCost = doRSModel.GetFieldVal("CUR_COST", 1)
            // If sSKUCost <> "" Then
            // cSKUCost = sSKUCost
            // rQtyFld = doForm.doRS.GetFieldVal("SR__QTY")
            // cCostVal = cSKUCost * rQtyFld
            // doForm.doRS.SetFieldVal("CUR_COST", goTR.RoundCurr(cCostVal))
            // End If

            // If txt_model is blank set to linked model's description
            sModel = Convert.ToString(doForm.doRS.GetFieldVal("TXT_Model"));
            if (sModel == "")
            {
                // MI 4/2/08 MO.TXT_Description replaces MMO_Description as of 4/3/08, but MMO_Description remains in existing DBs.
                if (goData.IsFieldValid("MO", "TXT_Description"))
                {
                    // Cs 6/22/09                
                    sLinkedModel = Convert.ToString(doRSModel.GetFieldVal("TXT_Description", 1));
                }

                else
                {

                    // sLinkedModel = doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_DESCRIPTION", 1)
                    sLinkedModel = Convert.ToString(doRSModel.GetFieldVal("MMO_DESCRIPTION", 1));
                }
                doForm.doRS.SetFieldVal("TXT_Model", sLinkedModel);
            }

            // Fill unit with linked model's unit
            // sUnit = doForm.doRS.GetFieldVal("LNK_FOR_MO%%txt_unittext") '6/22/09
            sUnit = Convert.ToString(doRSModel.GetFieldVal("txt_unittext"));
            doForm.doRS.SetFieldVal("TXT_Unit", sUnit);

            scriptManager.RunScript("Quotline_CheckTaxable", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray);
            scriptManager.RunScript("Quotline_ConnectVendors", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray);
            scriptManager.RunScript("Quotline_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray);        // runs CalcTotal
                                                                                                                                                              // goScr.RunScript("Quotline_FillItem", doForm)

            // Set Product link to Model's Product
            doForm.doRS.ClearLinkAll("LNK_FOR_PD");
            // doForm.doRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2) 'cs 6/22/09
            doForm.doRS.SetFieldVal("LNK_FOR_PD", doRSModel.GetFieldVal("LNK_OF_PD", 2), 2);
            par_doCallingObject = doForm;
            return true;
        }
        public bool TD_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 12/9/2008 Calls Function FillCOCN Instead
            scriptManager.RunScript("FILLCOCN", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray);
            par_doCallingObject = doForm;

            return true;
        }
        public bool TK_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // PJ 7/12/2014: For tank image on form
            doForm.SetControlVal("NDB_IFR_Image", "cus_dbImage.aspx?ID=" + doForm.doRS.GetFieldVal("GID_ID"));
            par_doCallingObject = doForm;

            return true;
        }
        public bool TK_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRs = (clRowSet)par_doCallingObject;

            // TLD 4/28/2014 Copy links to LNK_Involves_CO, LNK_Involves_CN
            doRs.SetFieldVal("LNK_Involves_CO", doRs.GetFieldVal("LNK_Owner_CO", 2), 2);
            doRs.SetFieldVal("LNK_Involves_CO", doRs.GetFieldVal("LNK_Contractor_CO", 2), 2);
            doRs.SetFieldVal("LNK_Involves_CN", doRs.GetFieldVal("LNK_Main_CN", 2), 2);
            doRs.SetFieldVal("LNK_Involves_CN", doRs.GetFieldVal("LNK_Maintenance_CN", 2), 2);

            par_doCallingObject = doRs;

            return true;
        }
        public bool TK_FormControlOnChange_BTN_UploadImage(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // PJ 7/12/2014: Upload tank image files. 
            // Check permission on user form
            if (Convert.ToInt32(goData.GetFieldValueFromRec(goP.GetMe("ID"), "CHK_PermUploadTankImage", 2)) == 0)
            {
                doForm.MessageBox("You do not have permission to upload images. Please contact your Selltis administrator.");
                return true;
            }

            goUI.OpenURLExternal("../Pages/cus_FileUpload.aspx?ID=" + doForm.doRS.GetFieldVal("GID_ID"), "Selltis", "height=170,width=360,left=400,top=400,status=no,location=no,toolbar=no,resizable=no,titlebar=no,dependent=yes,scrollbars=no");

            par_doCallingObject = doForm;

            return true;
        }
        public bool Tank_SaveImageToDatabase(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: GID_ID of Tank record
            // par_s2: Name of saved image file in \UploadedFiles
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PJ 7/15/2014: Convert image file to binary string stored in MMO_TankImage

            byte[] pict;
            string sImageFilePath = HttpContext.Current.Server.MapPath("~/UploadedFiles/" + par_s2);

            try
            {
                if (string.IsNullOrEmpty(sImageFilePath) == true)
                    return default(Boolean);

                System.IO.FileInfo fileInfo = new System.IO.FileInfo(sImageFilePath);
                long iNumBytes = fileInfo.Length;
                System.IO.FileStream FStream = new System.IO.FileStream(sImageFilePath, System.IO.FileMode.Open, System.IO.FileAccess.Read);
                System.IO.BinaryReader oReader = new System.IO.BinaryReader(FStream);
                pict = oReader.ReadBytes(Convert.ToInt32(iNumBytes));
                fileInfo = null;
                FStream.Close();
                FStream.Dispose();
                oReader.Close();

                string sImage = Convert.ToBase64String(pict);

                clRowSet oRS = new clRowSet("TK", 1, "GID_ID='" + par_s1 + "'", null, null, 1, null, null, null, null, null, true, true);
                if (oRS.GetFirst() == 1)
                {
                    oRS.SetFieldVal("MMO_TankImage", sImage);
                    if (oRS.Commit() != 1)
                    {
                        string sErr = "";
                    }

                }
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }

        public bool Utility_RunImportUtility(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 5/14/2014 Added

            try
            {
                goUI.OpenURLExternal("../Pages/cus_diaImportMan.aspx", "Selltis", "height=840,width=1250,left=100,top=100,status=yes,location=no,toolbar=no,resizable=yes,titlebar=no,dependent=yes");
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote_Draft.docx";
                }
                else if (sQTTemplate == "Job Creation Form")
                {
                    return "cus_Job_Creation_Form_draft.docx";
                }
                else if (sQTTemplate == "Invoice Template")
                {
                    return "cus_LMS_Invoice_Master_Template_draft.docx";
                }
                else if (sQTTemplate == "Work Order Form")
                {
                    return "cus_Work_Order_Form_draft.docx";
                }


            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote.docx";
                }
                else if (sQTTemplate == "Job Creation Form")
                {
                    return "cus_Job_Creation_Form.docx";
                }
                else if (sQTTemplate == "Invoice Template")
                {
                    return "cus_LMS_Invoice_Master_Template.docx";
                }
                else if (sQTTemplate == "Work Order Form")
                {
                    return "cus_Work_Order_Form.docx";
                }


            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);

                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));
            string sModelText = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_MODELNAME"));
            string sModelDesc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%MMO_DESCRIPTION"));

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_ORIGINATEDBY_CN,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("TXT_MODEL", sModelText);
            rsQL.SetFieldVal("MMO_DETAILS", sModelDesc);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);

            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);

            //rsQL.SetFieldVal("TXT_Model", sModelText);      
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}
            Refresh_QouteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";
            par_doCallingObject = doForm;
            return true;

        }
        public bool QT_FormControlOnChange_BTN_RECALC_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_QouteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            par_doCallingObject = doForm;
            return true;

        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                //doForm.doRS.SetFieldVal("CUR_UnitPrice", curValue);

            }
            //convert to quote button
            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }

            par_doCallingObject = doForm;
            return true;
        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");

            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }

        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_FOR_MO,LNK_RELATED_PD,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");
            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curValue);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";
            Refresh_OPPTotal(doForm.doRS);
            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);

            //This will generate line no's in mobile.
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }
            par_doCallingObject = doRS;

            return true;
        }

        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_ENDUSER_CO", rsOP.GetFieldVal("LNK_ENGINEERING_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_DISTREP_CO", rsOP.GetFieldVal("LNK_DISTRIBUTOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECOMPLETED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    //doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }
        public bool OP_FormControlOnChange_BTN_CALCPROBABILITY_1_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_OPPTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            par_doCallingObject = doForm;
            return true;

        }
        private static void Refresh_OPPTotal(clRowSet doForm)
        {
            string sGidId = Convert.ToString(doForm.GetFieldVal("Gid_id"));

            clRowSet rsOL = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + sGidId + "' ", "LNK_IN_OP", "CUR_VALUE|SUM");

            double curTotalAmt = 0.0;

            if ((rsOL.GetFirst() == 1))
            {

                curTotalAmt = Convert.ToDouble(rsOL.GetFieldVal("CUR_VALUE|SUM", 2));


                doForm.SetFieldVal("CUR_VALUE", curTotalAmt, 2);
                //doForm.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);

            }
            else
            {
                doForm.SetFieldVal("CUR_VALUE", 0.0);
                //doForm.SetFieldVal("CUR_TOTAL", 0.0);

            }


        }
        public bool Quotline_CalcTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        private static void Refresh_QouteTotal(clRowSet doQuote)
        {
            string sGidId = Convert.ToString(doQuote.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_GROUPBY, "LNK_IN_QT='" + sGidId + "' ", "LNK_IN_QT", "CUR_SUBTOTAL|SUM");

            double curTotalAmt = 0.0;

            if ((rsQL.GetFirst() == 1))
            {

                curTotalAmt = Convert.ToDouble(rsQL.GetFieldVal("CUR_SUBTOTAL|SUM", 2));


                doQuote.SetFieldVal("CUR_SUBTOTAL", curTotalAmt, 2);
                doQuote.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);

            }
            else
            {
                doQuote.SetFieldVal("CUR_SUBTOTAL", 0.0);
                doQuote.SetFieldVal("CUR_TOTAL", 0.0);

            }


        }
        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }
        public bool Quote_FillAddress_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //goP.TraceLine("", "", sProc)

            //PURPOSE:
            //		Fill the address field, first checking whether it is empty.
            //RETURNS:
            //		True

            string sContactName = "";
            string sMailingAddr = null;
            string sFirstName = null;
            string sLastName = null;
            //Dim sCompName as string
            string sAddrMail = null;
            string sCityMail = null;
            string sStateMail = null;
            string sZipMail = null;
            string sCountryMail = null;
            string scompany = null;

            //VS 03262018 TKT#2151 : Refill data whenever Contact has changed even if not empty.
            //if (!string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_ADDRESSMAILING").ToString()))
            //    return true;
            if (doForm.doRS.GetLinkCount("LNK_ORIGINATEDBY_CN") < 1)
                return true;

            //CS 6/22/09: Create CN rowset to get CN fields
            clRowSet doRSContact = new clRowSet("CN", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN") + "'", "", "LNK_RELATED_CO,TXT_NAMEFIRST,TXT_NAMELAST,TXT_ADDRBUSINESS,TXT_CITYBUSINESS,TXT_STATEBUSINESS,TXT_ZIPBUSINESS,TXT_COUNTRYBUSINESS");
            if (doRSContact.GetFirst() == 1)
            {
                scompany = Convert.ToString(doRSContact.GetFieldVal("LNK_RELATED_CO%%TXT_COMPANYNAME"));
                sFirstName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMEFIRST"));
                sLastName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMELAST"));


                if (!string.IsNullOrEmpty(sFirstName))
                {
                    sContactName = sFirstName + " ";
                }
                sContactName += sLastName;

                sAddrMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ADDRBUSINESS"));
                sCityMail = Convert.ToString(doRSContact.GetFieldVal("TXT_CITYBUSINESS"));
                sStateMail = Convert.ToString(doRSContact.GetFieldVal("TXT_STATEBUSINESS"));
                sZipMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ZIPBUSINESS"));
                sCountryMail = Convert.ToString(doRSContact.GetFieldVal("TXT_COUNTRYBUSINESS"));

                //Start building the mailing address
                //sMailingAddr = scompany;
                //if (!string.IsNullOrEmpty(scompany))
                //{
                //    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                //}
                //sMailingAddr = scompany;
                sMailingAddr = sContactName;
                if (!sAddrMail.Contains(scompany))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                }
                if (!string.IsNullOrEmpty(sAddrMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sAddrMail;
                }
                if (!string.IsNullOrEmpty(sCityMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCityMail;
                }
                if (!string.IsNullOrEmpty(sStateMail))
                {
                    sMailingAddr = sMailingAddr + ", " + sStateMail;
                }
                if (!string.IsNullOrEmpty(sZipMail))
                {
                    sMailingAddr = sMailingAddr + " " + sZipMail;
                }
                if (!string.IsNullOrEmpty(sCountryMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCountryMail;
                }
                doForm.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailingAddr);
                doForm.doRS.SetFieldVal("MMO_ADDRMAILING", sMailingAddr);
            }


            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;

        }
    }
}

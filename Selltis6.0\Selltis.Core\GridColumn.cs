﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Selltis.Core
{
    public class GridColumn
    {
        public string Name { get; set; }
        public string NameOrg { get; set; }
        public string Title { get; set; } //Header text
        public string Alignment { get; set; }
        public int Width { get; set; }
        public bool IsIcon { get; set; }
        public bool IsButton { get; set; }
        public bool IsIconButton { get; set; }
        public bool IsLink { get; set; }
        public bool IsSortable { get; set; }
        public bool IsVisible { get; set; }
        public bool IsTotal { get; set; }
        public bool IsAverage { get; set; }
        public bool IsMedian { get; set; }
        public bool IsMaximum { get; set; }
        public bool IsMinimum { get; set; }
        public bool IsPercent { get; set; }
        public bool AllowEdit { get; set; }
    }
}

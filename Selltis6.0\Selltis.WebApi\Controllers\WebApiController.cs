﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Selltis.BusinessLogic;
using Selltis.Core;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web;
using System.Web.Http;
using System.Web.Http.Cors;
using System.Web.Http.Results;

namespace Selltis.WebApi.Controllers
{
    [Authorize]
    [EnableCors(origins: "*", headers: "*", methods: "*")]
    public class WebApiController : ApiController
    {
        public com.selltis.selltis.WebService_RowSet wsSelltisCheckIn = new com.selltis.selltis.WebService_RowSet();
        [HttpGet]
        [ActionName("quotes")]
        public HttpResponseMessage GetQuotes(string FromDate, string ToDate, int iNoofRecords=int.MaxValue, string TopRecord="")
        {
            HttpResponseMessage response= Request.CreateResponse(HttpStatusCode.OK, GetData("QT", FromDate, ToDate, iNoofRecords, TopRecord));
            return response;
            
        }

        [HttpGet]
        [ActionName("opportunities")]
        public HttpResponseMessage GetOpportunities(string FromDate, string ToDate, int iNoofRecords = int.MaxValue, string TopRecord = "")
        {
            HttpResponseMessage response = Request.CreateResponse(HttpStatusCode.OK, GetData("OP", FromDate, ToDate, iNoofRecords, TopRecord));
            return response;

        }


        [HttpGet]
        [ActionName("leads")]
        public HttpResponseMessage GetLeads(string FromDate, string ToDate, int iNoofRecords = int.MaxValue, string TopRecord = "")
        {
            HttpResponseMessage response = Request.CreateResponse(HttpStatusCode.OK, GetData("WL", FromDate, ToDate, iNoofRecords, TopRecord));
            return response;
        }

        [HttpGet]
        [ActionName("activities")]
        public HttpResponseMessage GetActivities(string FromDate, string ToDate, int iNoofRecords = int.MaxValue, string TopRecord = "")
        {
            HttpResponseMessage response = Request.CreateResponse(HttpStatusCode.OK, GetData("AC", FromDate, ToDate, iNoofRecords, TopRecord));
            return response;
        }

        [HttpGet]
        [ActionName("accounts")]
        public HttpResponseMessage GetAccounts(string FromDate, string ToDate, int iNoofRecords = int.MaxValue, string TopRecord = "")
        {
            HttpResponseMessage response = Request.CreateResponse(HttpStatusCode.OK, GetData("CO", FromDate, ToDate, iNoofRecords, TopRecord));
            return response;
        }

        [HttpGet]
        [ActionName("contacts")]
        public HttpResponseMessage GetContacts(string FromDate, string ToDate, int iNoofRecords = int.MaxValue, string TopRecord = "")
        {
            HttpResponseMessage response = Request.CreateResponse(HttpStatusCode.OK, GetData("CN", FromDate, ToDate, iNoofRecords, TopRecord));
            return response;
        }

        [HttpGet]
        [ActionName("getusers")]
        public HttpResponseMessage GetUsers()
        {
            HttpResponseMessage response = Request.CreateResponse(HttpStatusCode.OK, GetData("US"));
            return response;
        }

        //[HttpPost]
        //[ActionName("AddUser")]
        //public HttpResponseMessage AddUser(UserDetails _userDetails)
        //{
        //    bool retval = Add_Edit_User(_userDetails, 1);
        //    string sResponceMeassage = retval == true ? "Success" : "Fail";
        //    HttpResponseMessage response = Request.CreateResponse(HttpStatusCode.OK, sResponceMeassage);
        //    return response;

        //}

        //[HttpPost]
        //[ActionName("UpdateUser")]
        //public HttpResponseMessage UpdateUser(UserDetails _userDetails)
        //{

        //    bool retval = Add_Edit_User(_userDetails, 2);
        //    string sResponceMeassage = retval == true ? "Success":"Fail";
        //    HttpResponseMessage response = Request.CreateResponse(HttpStatusCode.OK, sResponceMeassage);
        //    return response;

        //}

        //[HttpPost]
        //[ActionName("DeactivateUser")]
        //public HttpResponseMessage DeactivateUser(UserDetails _userDetails)
        //{
        //    bool retval = Add_Edit_User(_userDetails, 3);
        //    string sResponceMeassage = retval == true ? "Success" : "Fail";
        //    HttpResponseMessage response = Request.CreateResponse(HttpStatusCode.OK, sResponceMeassage);
        //    return response;

        //}

        private bool Logon(string userName, string hostName, string pwd = "")
        {
            string sHostName = (hostName == "localhost" || string.IsNullOrEmpty(hostName)) ? "default" : hostName;
            //if (string.IsNullOrEmpty(userName))
            //    userName = "system";
            //Load Settings
            DataSet ds = new DataSet();
            string myXMLfile = System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString() + sHostName + "/SiteSettings.xml";
            FileStream fsReadXml = new FileStream(myXMLfile, FileMode.Open, FileAccess.Read);
            ds.ReadXml(fsReadXml);
            fsReadXml.Close();
            fsReadXml.Dispose();
            HttpContext.Current.Session[hostName + "_SiteSettings"] = ds.Tables[0];

            //Login Process 
            string connStr = ds.Tables[0].Rows[0]["ConnectionString"].ToString();
            HttpContext.Current.Session[sHostName + "_" + "ConnString"] = connStr;
            SqlConnection sqlConnection = new SqlConnection(connStr);
            if (sqlConnection.State == ConnectionState.Closed)
                sqlConnection.Open();
            string sql = string.Empty;

            string IsSSoEnabled = "false";
            try
            {
                if (ds.Tables[0].Columns.Contains("UseSSO"))
                {
                    IsSSoEnabled = ds.Tables[0].Rows[0]["UseSSO"].ToString();
                }
                else
                {
                    IsSSoEnabled = "false";
                }
            }
            catch
            {
            }

            if (string.IsNullOrEmpty(userName))
            {
                userName = IsSSoEnabled == "true" ? "<EMAIL>" : "system";
            }

            if (string.IsNullOrEmpty(pwd))
            {
                if (IsSSoEnabled == "true")
                {
                    sql = "Select b.* from US a Join XU b on b.GID_UserID = a.GID_ID WHERE a.CHK_ActiveField=1 AND cast(a.EML_Email as nvarchar(250))='" + userName + "'";
                }
                else if (IsSSoEnabled == "false")
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'";
                }
                else
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'";
                }

            }
            else
            {
                if (IsSSoEnabled == "true")
                {
                    sql = "Select b.* from US a Join XU b on b.GID_UserID = a.GID_ID WHERE a.CHK_ActiveField=1 AND cast(a.EML_Email as nvarchar(250))='" + pwd + "'";
                }
                else if (IsSSoEnabled == "false")
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'  and [TXT_Password]='" + pwd + "'";
                }
                else
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "' and [TXT_Password]='" + pwd + "'";
                }
            }

            SqlCommand comm = new SqlCommand(sql, sqlConnection);
            //comm.Parameters.AddWithValue("@username", userName);
            //comm.Parameters.AddWithValue("@pwd", pwd);

            SqlDataReader reader = comm.ExecuteReader();
            if (reader.HasRows)
            {
                DataTable dt = new DataTable();
                dt.Load(reader);
                if (dt.Rows.Count == 0)
                {
                    return false;
                }
                else
                {
                    HttpContext.Current.Session["USERID"] = dt.Rows[0]["GID_UserID"].ToString();
                    HttpContext.Current.Session["LOGINID"] = dt.Rows[0]["GID_ID"].ToString();
                    HttpContext.Current.Session[sHostName + "_" + "LOGINNAME"] = dt.Rows[0]["TXT_LogonName"].ToString();
                    return true;
                }
            }
            else
                return false;


        }

        private DataTable GetData(string sFileName, string FromDate = "", string Todate = "", int iNoofRecords = 10000, string TopRecord = "")
        {
            DataTable dtData = new DataTable();
            try
            {
                HttpContext.Current.Session.Clear();
                clCache.ClearCache();
                bool _sb = Logon("system", "hcs.selltis.com", "S0lesIndT3ch");
               

                // HttpContext.Current.Session["HostName"] = "hcs.selltis.com";
                //clSelltisMembershipProviderNew _membership = new clSelltisMembershipProviderNew();
                //bool _sb = false;
                //_sb = _membership.ValidateUser("system", "S0lesIndT3ch");

                if (_sb)
                {
                    HttpContext.Current.Session["HostName"] = "hcs.selltis.com";
                    clInit _clint = new clInit();
                    string sFilter = "";
                    //"(DTT_CREATIONTIME>='2022-08-01' AND DTT_CREATIONTIME<='2022-09-01')"
                    if (sFileName.ToLower() == "us")
                    {
                        sFilter = "CHK_ACTIVEFIELD=1 AND CHK_ONNETWORK=1";
                    }
                    else
                    {
                         sFilter = "(DTT_CREATIONTIME>='" + FromDate + "' AND DTT_CREATIONTIME<='" + Todate + "')";
                    }
                    clRowSet doRS = new clRowSet(sFileName, clC.SELL_READONLY, sFilter, "DTT_CREATIONTIME desc", GetFields(sFileName), iNoofRecords, "", "", "", "", TopRecord, true);
                    doRS.ToTable();
                    dtData = doRS.dtTransTable;
                    UpdateColumnLabels(ref dtData, sFileName);
                }
            }
            catch (Exception ex)
            {
            }
            return dtData;
        }

        private void UpdateColumnLabels(ref DataTable dt, string sFileName)
        {
            clData _godata = (clData)Util.GetInstance("data");
            int iValid = 0;
            foreach (DataColumn _col in dt.Columns)
            {
                _col.ColumnName = _godata.GetFieldFullLabelFromName(sFileName, _col.ColumnName, ref iValid);
            }
        }

        private string GetFields(string sFileName)
        {
            string sFields = "*";
            if (sFileName == "WL")
            {
                sFields = "LNK_LINKED_PE%%SYS_NAME,MLS_TYPEOFREQUEST,LNK_RELATED_SO%%SYS_NAME,TXT_WLNO,LNK_ASSIGNEDTO_US%%TXT_FULLNAME,LNK_RELATED_CN%%TXT_FULLNAME,LNK_RELATED_CN%%TXT_CONTACTCODE,LNK_RELATED_CO%%TXT_COMPANYNAME,MLS_STAGE,MLS_STATUS,CHK_ContactValidated,TXT_FIRSTNAME,TXT_LASTNAME,TXT_COMPANYNAME,EML_EMAIL,LNK_COUNTRY_CY%%SYS_NAME,DTT_CreationDateTimeInHubSpot,DTT_CreationTime,DTT_MODTIME,LNK_RELATED_IU%%SYS_NAME,MMO_COMMENTS,GID_ID";
            }
            else if (sFileName == "QT")
            {
                sFields = "LNK_CREDITEDTO_US%%TXT_FULLNAME,DTT_CreationTime,DTT_MODTIME,DTT_TIME,DTT_EXPCLOSEDATE,MLS_STATUS,TXT_OPPORTUNITYTYPE,CUR_TOTALAMOUNTUSD,LNK_FOR_CO%%SYS_NAME,TXT_DESCRIPTION,TXT_QUOTENO,TXT_LinkedOppNo,CHK_PRIMARYQUOTE,MLS_LOB,GID_ID";
            }
            else if (sFileName == "OP")
            {
               sFields = "TXT_OPPORTUNITYNAME,TXT_OPPNO,MLS_OPPORTUNITYTYPE,MLS_REGION,MLS_LOB,LNK_CREDITEDTO_US%%TXT_FULLNAME,LNK_FOR_CO%%TXT_COMPANYNAME,LNK_ORIGINATEDBY_CN%%TXT_FULLNAME,LNK_ORIGINATEDBY_CN%%TXT_CONTACTCODE,MLS_Stage,MLS_Currency,CUR_AMOUNT,CUR_RENTALAMOUNT,CUR_TOTALAMOUNT,CUR_TOTALAMOUNTUSD,DTT_ExpCloseDate,DTT_CREATIONTIME,DTT_MODTIME,MLS_TYPEOFBID,LNK_LINKED_WL%%TXT_WLNO,GID_ID";              
            }
            else if (sFileName == "AC")
            {
                sFields = "LNK_CONNECTED_WL%%TXT_WLNO,LNK_CREATEDBY_US%%TXT_FULLNAME,LNK_CREDITEDTO_US%%TXT_FULLNAME,DTT_CreationTime,DTT_MODTIME,DTT_STARTTIME,LNK_RELATED_CO%%TXT_COMPANYNAME,LNK_RELATED_CN%%TXT_FULLNAME,LNK_RELATED_OP%%TXT_OPPNO,LNK_RELATED_QT%%TXT_QUOTENO,MLS_TYPE,TXT_SUBJECT,MLS_STATUS,MMO_NOTES,MMO_NEXTACTION,DTT_NEXTACTIONDATE,TXT_NEXTACTIONSUBJECT,MLS_NEXTACTIONTYPE,MLS_NEXTACTIONSTATUS,GID_ID,SYS_NAME";
            }
            else if(sFileName =="CO")
            {
                sFields = "TXT_CODE,TXT_COMPANYNAME,MLS_CURRENCY,MLS_ACCOUNTTYPE,MLS_REGION,LNK_CREATEDBY_US%%TXT_FULLNAME,LNK_TEAMLEADER_US%%TXT_FULLNAME,LNK_RELATED_IU%%TXT_INDUSTRYNAME,CHK_PARENTACCOUNT,LNK_PARENT_CO%%TXT_COMPANYNAME,TXT_NAVID,TEL_PHONENO,TXT_ADDRMAILING,TXT_CITYMAILING,TXT_STATEMAILING,TXT_ZIPMAILING,TXT_CountryMailing,CHK_ActiveField,DTT_CREATIONTIME,DTT_MODTIME";
            }
            else if (sFileName == "CN")
            {
                sFields = "TXT_CONTACTCODE,TXT_NAMEFIRST,TXT_NAMELAST,TEL_BUSPHONE,EML_EMAIL,LNK_RELATED_CO%%TXT_COMPANYNAME,LNK_RELATED_CO%%TXT_CODE,LNK_RELATED_US%%TXT_FULLNAME,LNK_RELATED_IU%%TXT_INDUSTRYNAME,TXT_TITLETEXT,TXT_ADDRMAILING,TXT_MAILINGCITY,TXT_MAILINGSTATE,TXT_MAILINGZIP,LNK_MAILINGCOUNTRY_CY%%TXT_COUNTRYNAME,MLS_BOUGHTFROMUSBEFORE,MLS_APPETITEFORVALUE,MLS_PERCEPTIONOFUS,SI__SCORECARDPOINTS,DTT_CREATIONTIME,DTT_MODTIME,LNK_CREATEDBY_US%%TXT_FULLNAME,LNK_CONNECTED_WL%%TXT_WLNO";
            }
            else if (sFileName == "US")
            {
                sFields = "TXT_SXNO,TXT_FULLNAME,TXT_NAMEFIRST,TXT_NAMELAST,TXT_CODE,MLS_TYPE,LNK_RELATED_JF%%TXT_JobFuncName,LNK_RELATED_BU%%TXT_BusinessUnitName,LNK_PRIMARY_BC%%TXT_BUCompanyName,LNK_RELATED_BC%%TXT_BUCompanyName,EML_EMAIL";
            }

            return sFields;
        }

        //public bool Add_Edit_User(UserDetails _userDetails,int Tran_type)
        //{
        //    ManageWebserviceCheckIn();

            

        //    DateTime dateTime = DateTime.Now;
        //    string sStratDate = dateTime.ToString("yyyy-MM-dd H:mm:ss");
        //    string sDueDate = dateTime.AddDays(1).ToString("yyyy-MM-dd H:mm:ss");
        //    string sDescription = "";

        //    if (!wsSelltisCheckIn.NewRS("TD", 2, "", "", "", 1, "", ""))
        //        return false;

        //    if (wsSelltisCheckIn.Count() == 0)
        //        return false;

        //   // string sDefaultnote = "First Name : " + _userDetails.Firstname  + "<br/> Last Name : " + _userDetails.Lastname + "<br/> E-Mail : " + _userDetails.Email;

        //    StringBuilder sNotes = new StringBuilder();
        //    if (Tran_type == 1)
        //    {
        //        sNotes.AppendLine("<p>Please add user record in OTC</p>");
        //        sDescription = "OTC - add user record";
        //    }
        //    else if (Tran_type == 2)
        //    {
        //        sNotes.AppendLine("<p>Please update user record in OTC</p>");
        //        sDescription = "OTC - update user record";
        //    }
        //    else if (Tran_type == 3)
        //    {
        //        sNotes.AppendLine("<p>Please deactivate  user record in OTC </p>");
        //        sDescription = "OTC - deactivate user record";
        //    }

            
        //    sNotes.AppendLine("<p>&nbsp;</p>");
        //    sNotes.AppendLine("<p>&nbsp;</p>");
        //    sNotes.AppendLine("<p>First Name :"+ _userDetails.Firstname + " </p>");
        //    sNotes.AppendLine("<p>Last Name :" + _userDetails.Lastname + " </p>");
        //    sNotes.AppendLine("<p>Email :" + _userDetails.Email + " </p>");

            

        //    wsSelltisCheckIn.SetFieldVal("DTE_STARTDATE", sStratDate, 2);
        //    wsSelltisCheckIn.SetFieldVal("LNK_ASSIGNEDTO_US", "3ee84c10-f68d-4d1f-5553-98e801721a59", 1);
        //    wsSelltisCheckIn.SetFieldVal("LNK_RELATED_CO", "30626434-3138-6530-434f-31322f322f32", 1);
        //    wsSelltisCheckIn.SetFieldVal("DTE_DUETIME", sDueDate, 2);
        //    wsSelltisCheckIn.SetFieldVal("TXT_DESCRIPTION", sDescription, 1);
        //    wsSelltisCheckIn.SetFieldVal("MMR_NOTES", sNotes.ToString(), 1);
        //    wsSelltisCheckIn.SetFieldVal("MLS_TYPE", 14, 2);


        //    if (wsSelltisCheckIn.Commit() == 0)
        //    {
        //        return false;
        //    }

        //    return true;

        //}
        //public bool ManageWebserviceCheckIn()
        //{

        //    // For alert system. Reports status to selltis.selltis.com site

        //    try
        //    {
        //        wsSelltisCheckIn.Url = "https://selltis70.selltis.com/webservice/rowset.asmx";

        //        wsSelltisCheckIn.Timeout = 3600000;

        //        bool bCookieIsNew = false;

        //        if (wsSelltisCheckIn.CookieContainer == null)
        //        {
        //            wsSelltisCheckIn.CookieContainer = new System.Net.CookieContainer();
        //            bCookieIsNew = true;
        //        }

        //        if (wsSelltisCheckIn.ValidateSession() == false)
        //        {
        //            if (bCookieIsNew == false)
        //                wsSelltisCheckIn.CookieContainer = new System.Net.CookieContainer();

        //            bool sReturn = wsSelltisCheckIn.Logon("Sysuser", "windev.55");
        //            return sReturn;
        //        }
        //        else
        //            return true;
        //    }
        //    catch (Exception ex)
        //    {
        //        return false;
        //    }
        //}

        //public class UserDetails
        //{
        //    public string Firstname { get; set; }
        //    public string Lastname { get; set; }
        //    public string Email { get; set; }

        //    //public int Tran_type { get; set; } // 1 = Add , 2 = Update , 3 = Deactivate

        //}

    }

}

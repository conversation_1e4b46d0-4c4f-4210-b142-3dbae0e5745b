﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.IO;
using System.Net;
using System.Xml;
using System.Text.RegularExpressions;
using System.Diagnostics;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Data.SqlClient;
using System.Xml.Linq;

namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clProject goP;
        private clLog goLog;
        private clError goErr;
        private clPerm goPerm;
        private ClUI goUI;
        ScriptManager scriptManager = new ScriptManager();

        int par_iValid = 4;
        string par_sDelim = "|";
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";
        SqlConnection par_oConnection = null;
        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();

            Util.SetSessionValue("SkipQLSpecificLogic", "Y");
        }
        public ScriptsCustom()
        {
            Initialize();
        }

        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            return true;
        }
        public bool AC_FormAfterSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/19/2011 Added On Save: Create QT
            if (doForm.GetControlVal("CHK_CREATEQT") == "CHECKED")
            {
                Form doFormQT = new Form("QT", doForm.doRS.GetFieldVal("GID_ID").ToString(), "CRL_QT");
                goUI.Queue("FORM", doFormQT);
            }

            // TLD 8/31/2011 Added On Save: Send Inquiry
            if (doForm.GetControlVal("CHK_SENDINQUIRY") == "CHECKED")
            {
                Form doFormAC = new Form("AC", doForm.doRS.GetFieldVal("GID_ID").ToString(), "CRL_AC:EMAILSENT");
                doFormAC.doRS.SetFieldVal("MLS_Status", 0, 2); // Open
                doFormAC.doRS.SetFieldVal("MLS_Purpose", 1, 2); // Inquiry
                goUI.Queue("FORM", doFormAC);
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool AddItemVerbalQuote(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused. 
            // par_doArray: Unused. 
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process 
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running. 
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3) 

            // PURPOSE: 
            // Called from AddItem:VerbalQuote automator, this creates a new activity log populated for verbal quote

            Form doForm = (Form)par_doCallingObject;
            Form oForm = new Form("AC", "", "CRL_AC:VerbalQuote");
            goUI.Queue("FORM", oForm);
            // goP.TraceLine("Return True", "", sProc) 

            par_doCallingObject = doForm;
            return true;
        }
        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 1/3/08 Edited Dim sproc line.
            // MI 12/13/07 Added tests to prevent tab from bouncing when browsing the form.
            // MI 12/3/07 Removed setting <meid> in Credited To US and Involves US.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 7/29/2011 Open to Service tab if purpose is Serivce
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Purpose", 2)) == 6)
                doForm.MoveToTab(13);// Service

            par_doCallingObject = doForm;
            return true;
        }
        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            int iPurpose = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_PURPOSE", 2));
            int iStatus = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2));
            bool bEnforceNextActionDate = false;
            int iTab = 0;

            // 'TLD 8/31/2011 Changed below to accommodate new enforcement
            // 'TLD 7/29/2011 Next Action Date mandatory if Status is Open
            // 'and purpose is Service
            // '----- Next Action Date must be a valid date if Purpose=Service and Status=Open
            // If goScr.IsSectionEnabled(sProc, par_sSections, "EnforceNextActionDateIfOpenService") Then
            // 'goP.TraceLine("IsDate of Next Action Date (should be 1 if date): '" & IsDate(doForm.GetFieldVal("DTE_NEXTACTIONDATE", 3)) & "'", "", sProc)
            // '==> Retest: IsDate was coded wrong.
            // 'CS: The below line allows as blank NA date.
            // 'If goTR.IsDate(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 2)) <> True Then
            // If doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1) = "" Then
            // 'goP.TraceLine("Purpose (should be 8): '" & doForm.GetFieldVal("MLS_PURPOSE", 2) & "'" & vbCrLf & _
            // '  "Status (should be 0): '" & doForm.GetFieldVal("MLS_STATUS", 2) & "'", "", sProc)
            // 'If doForm.doRS.GetFieldVal("MLS_PURPOSE", 2) = 6 And doForm.doRS.GetFieldVal("MLS_STATUS", 2) = 0 Then
            // If iPurpose = 6 And iStatus = 0 Then
            // 'Purpose 8 = Lead; Status 0 = Open
            // doForm.MoveToTab(13)     'Service
            // 'goScr.RunScript("Activity_ManageExtraFields", doForm)
            // doForm.MoveToField("DTE_NEXTACTIONDATE")
            // 'goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE")
            // goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE")
            // Return False
            // End If
            // End If
            // End If

            // TLD 8/31/2011 -------Enforce fields based on Purpose
            switch (iPurpose)
            {
                case 1: // Inquiry -- Enforce Next Action Date, copy letter to notes
                    {
                        // Enforce Next Action Date
                        bEnforceNextActionDate = true;
                        iTab = 2; // Notes

                        // If new, copy letter to notes
                        if (doForm.GetMode() == "CREATION")
                        {
                            // If not already ran
                            if (Convert.ToString(goP.GetVar("AC_CopyLetterToNotes")) != "1")
                            {
                                goP.SetVar("AC_CopyLetterToNotes", "1");

                                //goScr.RunScript("AddTextToField", doForm, , "MMO_Notes", doForm.doRS.GetFieldVal("MMO_Letter"), "PREPEND");
                                par_doCallingObject = doForm;
                                scriptManager.RunScript("AddTextToField", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "MMO_Notes", Convert.ToString(doForm.doRS.GetFieldVal("MMO_Letter")), "PREPEND");
                                doForm = (Form)par_doCallingObject;
                            }
                        }

                        // TLD 9/2/2011 Need to move to NOtes tab instead......
                        // Prevent main from running
                        goTR.StrWrite(ref par_sSections, "EnforceProductIfPurposeIsInquiry", "0");
                        // ----- Enforce Product if Purpose is Inquiry
                        // If goScr.IsSectionEnabled(sProc, par_sSections, "EnforceProductIfPurposeIsInquiry") Then
                        if (doForm.doRS.IsLinkEmpty("LNK_Related_PD") == true)
                        {
                            doForm.MoveToTab(2, "LNK_Related_PD");     // Notes
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_RELATED_PD"), "", "", "", "", "", "", "", "", "LNK_RELATED_PD");
                            par_doCallingObject = doForm;
                            return false;
                        }

                        break;
                    }

                case 6: // Service
                    {
                        if (iStatus == 0)
                        {
                            // Enforce Next Action Date
                            bEnforceNextActionDate = true;
                            iTab = 13; // Service Agreement

                            // TLD 8/31/2011 Moved from below
                            // TLD 8/3/2011 Update Review fields
                            // if status is open, purpose is service and review is checked
                            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Review", 2)) == 1)
                            {
                                // Set LastContactDate to now
                                doForm.doRS.SetFieldVal("DTT_LastContactDate", "Today|Now");
                                // Recalc Next Contact Date
                                doForm.doRS.SetFieldVal("TXT_LastContactBy", goP.GetMe("CODE"));
                                doForm.doRS.SetFieldVal("DTE_NextContactDate", goTR.NumToString(Convert.ToInt64(doForm.doRS.GetFieldVal("INT_ReviewInterval", 2))) + " days from today", 1);
                            }
                        }

                        break;
                    }
            }

            // Enforce Next Action Date
            if (bEnforceNextActionDate == true)
            {
                if (Convert.ToString(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1)) == "")
                {
                    doForm.MoveToTab(iTab, "DTE_NEXTACTIONDATE"); // Tab is set above
                    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE");
                    par_doCallingObject = doForm;
                    return false;
                }
            }
            // TLD 8/31/2011 -------Enforce fields based on Purpose

            // 'TLD 8/31/2011 Moved to above
            // 'TLD 8/3/2011 Update Review fields
            // 'if status is open, purpose is service and review is checked
            // If iPurpose = 6 And iStatus = 0 And doForm.doRS.GetFieldVal("CHK_Review", 2) = 1 Then
            // 'Set LastContactDate to now
            // doForm.doRS.SetFieldVal("DTT_LastContactDate", "Today|Now")
            // 'Recalc Next Contact Date
            // doForm.doRS.SetFieldVal("TXT_LastContactBy", goP.GetMe("CODE"))
            // doForm.doRS.SetFieldVal("DTE_NextContactDate", goTR.NumToString(doForm.doRS.GetFieldVal("INT_ReviewInterval", 2)) & " days from today", 1)
            // End If

            // TLD 8/31/2011
            // ----- Enforce Product if Type is Sales Visit
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceProductIfTypeIsSalesvisit"))
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Type", 2)) == 11 & doForm.doRS.IsLinkEmpty("LNK_Related_PD") == true)
                {
                    doForm.MoveToTab(4, "LNK_Related_PD");     // Details
                    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_RELATED_PD"), "", "", "", "", "", "", "", "", "LNK_RELATED_PD");
                    par_doCallingObject = doForm;
                    return false;
                }
            }
            // ----- Enforce Product if Type is Sales Visit

            par_doCallingObject = doForm;
            return true;
        }
        public bool Activity_ManageControlState_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/3/2011 --------Disable fields
            doForm.SetControlState("DTE_LastContactDate", 4);
            doForm.SetControlState("TXT_LastContactBy", 4);
            doForm.SetControlState("DTE_NextContactDate", 4);

            par_doCallingObject = doForm;
            return true;
        }
        public bool AddItemOrder(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused. 
            // par_doArray: Unused. 
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process 
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running. 
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3) 

            // PURPOSE: 
            // Called from AddItem:VerbalQuote automator, this creates a new activity log populated for verbal quote

            Form doForm = (Form)par_doCallingObject;
            Form oForm = new Form("QT", "", "CRL_QT:Order");
            goUI.Queue("FORM", oForm);
            // goP.TraceLine("Return True", "", sProc) 

            par_doCallingObject = doForm;
            return true;
        }
        public bool AutoAlertEveryDay_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 10/29/07 Replaced code with goTr.UTC_GetUserTimeZone().
            // MI 10/23/07 Cleaned up the script, implemented UTC.
            // MI 10/1/07 Change this script from overriding the main script to only running our customer
            // functionality and then running the main script. Commented out all the code that was identical
            // as in the main script.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: In a _Pre script set to False to prevent the main clScripts script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS;
            string sResult;
            bool bMore = true;
            string sCurrentUser = "";
            PublicDomain.TzTimeZone zone;
            DateTime dtUsersToday;
            DateTime dtUsersNow;
            DateTime dtDateTime;
            // Dim dtYesterday As DateTime
            string sPointers;
            string sPointerDateTime;
            DateTime dtPointerDate;
            string sDateTime;
            // Dim sTomorrow As String
            // Dim dtNextReview As DateTime
            // Dim doActivities As New clArray
            int i = 0;
            // Dim dtToday As DateTime
            // Dim sDailyPointer As String
            // Dim sDailyPointerDateTime As String
            // Dim sYearNow As String
            // Dim sYearYesterday As String
            // Dim sDiv As String
            // TLD 3/22/2010 Site Mod vars
            // Dim sBirthdayPointer As String
            // Dim sBirthdayPointerDateTime As String


            // --------------------- User-dependent updates -----------------------
            var rsUsers = new clRowSet("US", clC.SELL_READONLY, "CHK_ACTIVEFIELD=1", "", "EML_EMAIL");
            // 'No active users                        '*** MI 10/1/07 added checking the count and not running the loop if no active users found
            if (rsUsers.Count() < 1)
                bMore = false; // *** MI 10/1/07 changed from = 0 to < 1.

            // TLD 7/21/2010 Pointers for custom autoalert stuff
            sPointers = goMeta.PageRead("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED");

            while (bMore == true)
            {
                sCurrentUser = Convert.ToString(rsUsers.GetFieldVal("GID_ID", clC.SELL_FRIENDLY));

                // DEBUG ==> Remove
                // goLog.Log(sProc, "  Processing user '" & goData.GetRecordNameByID(sCurrentUser) & "' [" & sCurrentUser & "]", clC.SELL_LOGLEVEL_DETAILS)
                // END DEBUG

                zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
                // Get user's 'now' and 'today at midnight' in the user's time zone
                dtUsersNow = zone.ToLocalTime(goTR.NowUTC());   // User's current datetime in his/her time zone
                dtUsersToday = zone.ToLocalTime(goTR.NowUTC()).Date;                  // Midnight on user's current date in his/her time zone

                // TLD 7/21/2010 ------------Added normal autoalert pointer check here
                // since we added normal autoalert customizations here
                // Get the 'last processed' date (no time) as a local datetime
                // Pointers are written as local datetimes in user's last login time zone.
                sPointerDateTime = Strings.Left(goTR.StrRead(sPointers, sCurrentUser, "", false), 10);
                par_iValid = 3;
                if (sPointerDateTime == "")
                    // Leaving one whole day ahead of 'blank' datetime: '1753-01-02 23:59:59.000'.
                    dtPointerDate = goTR.StringToDate("1753-01-04", "", ref par_iValid);
                else
                    dtPointerDate = goTR.StringToDate(sPointerDateTime, clC.SELL_FORMAT_DATEDEF, ref par_iValid).Date;
                dtPointerDate = DateTime.SpecifyKind(dtPointerDate, DateTimeKind.Local);
                // dtPointerDate = goTR.AddDay(dtPointerDate, 1) Testing

                // *** MI 10/23/07 Replaced >= with just = to avoid not processing for days or months in case 
                // the server clock was accidentally set ahead and the processing occurred... In that case, 
                // pointer datetimes set far in the future would preclude running this.
                // If dtPointerDate >= dtUsersToday Then GoTo ProcessNextUser

                // DEBUG
                // goLog.Log(sProc, "    Pointer date: '" & goTR.DateTimeToSysString(dtPointerDate) & "' User's date: '" & goTR.DateTimeToSysString(dtUsersToday) & "'", clC.SELL_LOGLEVEL_DETAILS)
                // END DEBUG

                if (dtPointerDate == dtUsersToday)
                    goto ProcessNextUserNow;

                // --------- Set 'tomorrow' -----------
                // Set user's 'tomorrow at midnight' as local datetime for filtering
                dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));
                dtDateTime = goTR.AddDay(dtDateTime, 1);
                par_iValid = 3;
                par_sDelim = "|";
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sDelim);

                // TLD 8/2/2011
                // ------- Service followup -----------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertOverdueService", true))
                {
                    // Activity Log:Overdue Service (Desktop Service Calls to Review)
                    // sDateTime: tomorrow
                    // *** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    // Status is Open and Purpose is Service and Review is Checked and Next Contact Date is before tomorrow
                    doRS = new clRowSet("AC", 3, "MLS_STATUS=0 AND MLS_PURPOSE=6 AND CHK_REVIEW=1 AND DTT_NEXTCONTACTDATE<'" + sDateTime + "' AND LNK_CreditedTo_US='" + sCurrentUser + "'", "", "GID_ID", 1, "", "", "", "", "", false, false, true);
                    if (doRS.GetFirst() == 1)
                        sResult = goUI.AddAlert("Overdue Serivce", "OPENDESKTOP", "DSK_3FB7B33A-CB51-4A11-5858-9F320110300D", sCurrentUser, "Servic16.gif").ToString();
                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                }

            // 'TLD 7/21/2010 Shouldn't have to do this here, should be done in main
            // 'Update the daily processing pointer with current datetime in the processed user's time zone
            // goMeta.LineWrite("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED", sCurrentUser, goTR.DateTimeToSysString(dtUsersNow))

            ProcessNextUserNow:
                ;
                if (rsUsers.GetNext() == 0)
                    bMore = false;
            }

            rsUsers = null/* TODO Change to default(_) if this is not a reference type */;

            // --------------------- End User-dependent updates -----------------------


            // --------------------- Non-user-dependent updates -----------------------
            // '--------- Set 'today UTC' -------------
            // 'sDateTime replaces the Selltis keyword 'Today' with the datetime value that corresponds to midnight
            // dtDateTime = goTR.NowUTC().Date 'now date updated to tomorrow below
            // sDateTime = goTR.DateTimeToSysString(dtDateTime, , "|") 'string date

            // --------- Set 'tomorrow UTC' -------------
            // dtDateTime = goTR.AddDay(dtDateTime, 1)
            // sTomorrow = goTR.DateTimeToSysString(goTR.AddDay(dtDateTime, 1))
            // sTomorrow = goTR.DateTimeToSysString(dtDateTime, 1)

            // --------------------- End Non-user-dependent updates -----------------------

            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            if (doForm.GetMode() == "CREATION")
            {
                string division = Convert.ToString(doForm.doRS.GetFieldVal("LNK_TEAMLEADER_US%%LNK_RELATED_DV%%GID_ID"));
                doForm.doRS.SetFieldVal("LNK_RELATED_DV", division);
            }
            // TLD 9/6/2013 Disable fields for Target Account
            doForm.SetControlState("TXT_CURRANDPOT", 4);

            // SGR TKT:#987  Disable chk_merged
            doForm.SetControlState("CHK_Merged", 4);

            doForm.oVar.SetVar("Address", Convert.ToString(doForm.doRS.GetFieldVal("TXT_ADDRMAILING")));
            doForm.oVar.SetVar("State", Convert.ToString(doForm.doRS.GetFieldVal("TXT_STATEMAILING")));
            doForm.oVar.SetVar("City", Convert.ToString(doForm.doRS.GetFieldVal("TXT_CITYMAILING")));
            doForm.oVar.SetVar("Zip", Convert.ToString(doForm.doRS.GetFieldVal("TXT_ZIPMAILING")));
            doForm.oVar.SetVar("Division", Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_DV%%GID_ID")));
            doForm.oVar.SetVar("Coname", Convert.ToString(doForm.doRS.GetFieldVal("TXT_COMPANYNAME")));

            par_doCallingObject = doForm;
            return true;
        }
        // SGR TKT:#987 
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PORTING FROM COMMENCE DONE. MI.

            Form doForm = (Form)par_doCallingObject;

            // goP.TraceLine("", "", sProc)
            // Mandatory fields
            string sColor = Convert.ToString(goP.GetVar("sMandatoryfieldColor"));
            doForm.SetFieldProperty("TXT_NameLast", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_Related_US", "LABELCOLOR", sColor);
            // DEBUG
            // doForm.SetFieldProperties("BTN_EXTRA001",Active,447,183,94,22,"Test")
            // Set button tooltips
            doForm.SetFieldProperties("BTN_FILLFROMCO", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Fill phone, address, e-mail and web fields from the linked company");
            doForm.SetFieldProperties("BTN_DUPLICATE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Check for duplicate Contacts");
            doForm.SetFieldProperties("BTN_MAPBUSINESS_1", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Open a map of the business address");
            // CS 2/11/13
            doForm.SetFieldProperties("BTN_SHOWBUSINESS", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Show entire address");
            doForm.SetFieldProperties("BTN_UrlView", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Open the web page");
            doForm.SetFieldProperties("BTN_SENDEMAIL", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Send an e-mail via your e-mail client");
            doForm.SetFieldProperties("BTN_INSERTDATE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend the date and time in the Notes field");
            doForm.SetFieldProperties("BTN_HOMESAME", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Insert the business address as the home address");
            doForm.SetFieldProperties("BTN_MAPHOME", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Open a map of the home address");
            doForm.SetFieldProperties("BTN_MAPOTHER", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Open a map of the other address");
            // CS 2/11/13
            doForm.SetFieldProperties("BTN_SHOWHOME", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Show entire address");
            doForm.SetFieldProperties("BTN_SHOWOTHER", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Show entire address");
            doForm.SetFieldProperties("BTN_OTHERSAME", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Use the business address as the other address");
            doForm.SetFieldProperties("BTN_LINKCOMPANIES", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Link Companies of the linked Contacts");
            doForm.SetFieldProperties("BTN_LINKCOMPANIES_1", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Link Companies of the linked Contacts");


            // CS 8/29/08: Per MI, parsing values will be purely MD driven (CRL)
            // Automatically parse field values from the linked Activity Log
            // If doForm.GetMode() = "CREATION" Then
            // 'Process fields only in creation mode if 1 Activity is linked
            // If doForm.doRS.GetLinkCount("LNK_CONNECTED_AC") = 1 Then
            // goScr.RunScript("ParseIntoFields", doForm, , doForm.doRS.GetFieldVal("LNK_CONNECTED_AC%%MMO_NOTES"), "CN", "FORM", "1")
            // goScr.RunScript("ParseIntoFields", doForm, , doForm.doRS.GetFieldVal("LNK_CONNECTED_AC%%MMO_LETTER"), "CN", "FORM", "1")
            // Else
            // goScr.RunScript("ParseIntoFields", doForm, , doForm.doRS.GetFieldVal("MMO_NOTE"), "CN", "FORM", "1")
            // End If
            // End If

            doForm.oVar.SetVar("ContactNameLastOnLoad", doForm.doRS.GetFieldVal("TXT_NAMELAST"));
            doForm.oVar.SetVar("ContactNameFirstOnLoad", doForm.doRS.GetFieldVal("TXT_NAMEFIRST"));


            // Grayed fields
            doForm.SetControlState("DTE_lastcontactdate", 4);
            doForm.SetControlState("txt_lastcontactedby", 4);
            doForm.SetControlState("MMO_ImportData", 4);
            doForm.SetControlState("MMO_palmData", 4);
            doForm.SetControlState("MMO_outlookdata", 4);

            // SGR TKT:#987  Disable chk_merged
            doForm.SetControlState("CHK_Merged", 4);

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // TLD 9/6/2013 ----------Target account calcs
            string sCurVol = "";
            string sPotVol = "";

            // Copy LNK_CURRENT_PD to LNK_POTENTIAL_PD
            doRS.SetFieldVal("LNK_POTENTIAL_PD", doRS.GetFieldVal("LNK_CURRENT_PD"));

            // Fill Current & Potential Quadrant
            sCurVol = Strings.Left(Convert.ToString(doRS.GetFieldVal("MLS_CURVOLUME")), 1);
            sPotVol = Strings.Left(Convert.ToString(doRS.GetFieldVal("MLS_POTVOLUME")), 1);
            if (sCurVol == "<")
                sCurVol = "Z";
            if (sPotVol == "<")
                sPotVol = "Z";

            // set field to cur & pot
            doRS.SetFieldVal("TXT_CURRANDPOT", sCurVol + sPotVol);
            // TLD 9/6/2013 ----------End Target account calcs

            // TLD 9/10/2013 Add Competing Product's Related Vendor to Competing Vendors
            doRS.SetFieldVal("LNK_Competing_VE", doRS.GetFieldVal("LNK_Competing_PD%%LNK_Related_VE"));

            par_doCallingObject = doRS;
            return true;
        }
        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here.
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return false;
        }
        public bool QL_FormControlOnChange_BTN_SAVECRLMO_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.


            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            // IF gbWriteLog THEN oLog is clLogObj(sProc, "Start", 3)

            Form doForm = (Form)par_doCallingObject;

            if (doForm.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
            {
                par_doCallingObject = doForm;
                return false;
            }
            Form doNewForm;
            string sID;
            string sToCo;
            string SInQT;
            string SRelPR;
            string SStat;
            string sReason;
            string SCredUs;
            string SPeerUs;
            string SRelOp;
            string SRelTer;
            string sLinkedModel = "";

            sID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));
            sToCo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_TO_CO"));
            SInQT = Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_QT"));
            SRelPR = Convert.ToString(doForm.doRS.GetFieldVal("LNK_Related_PR"));
            SStat = Convert.ToString(doForm.doRS.GetFieldVal("MLS_STATUS"));
            sReason = Convert.ToString(doForm.doRS.GetFieldVal("MLS_REASONWONLOST"));
            SCredUs = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            SPeerUs = Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US"));
            SRelOp = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_OP"));
            SRelTer = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_TE"));

            // Create new QL

            doNewForm = new Form("QL", "", "CRU_QL");

            // Set values in new form
            doNewForm.doRS.SetFieldVal("LNK_TO_CO", sToCo);
            doNewForm.doRS.SetFieldVal("LNK_IN_QT", SInQT);
            doNewForm.doRS.SetFieldVal("LNK_Related_PR", SRelPR);
            doNewForm.doRS.SetFieldVal("MLS_STATUS", SStat);
            doNewForm.doRS.SetFieldVal("MLS_REASONWONLOST", sReason);
            doNewForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", SCredUs);
            doNewForm.doRS.SetFieldVal("LNK_PEER_US", SPeerUs);
            doNewForm.doRS.SetFieldVal("LNK_RELATED_OP", SRelOp);
            doNewForm.doRS.SetFieldVal("LNK_RELATED_TE", SRelTer);

            // Set MO values
            doNewForm.doRS.SetFieldVal("LNK_For_MO", doForm.doRS.GetFieldVal("LNK_FOR_MO", 2), 2);
            sLinkedModel = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ModelVE_VE%%TXT_VendorName", 1));
            doNewForm.doRS.SetFieldVal("TXT_Model", sLinkedModel);

            // 'Call Quotline_FillItem?  No, only want to fill model and txt_model
            // goScr.RunScript("Quotline_FillItem", doNewForm)

            doNewForm.OpenForm();

            par_doCallingObject = doForm;
            return true;
        }
        public bool QL_FormControlOnChange_LNK_FOR_MO_preInactive(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // 'TLD 9/2/2011 Commented.....moved to Quotline_FillItem_Pre

            // Dim doForm As Object = par_doCallingObject

            // 'CS 11/2/07: Changing this b/c QL functions were changed in clScripts.

            // 'Dim sSKUPrice As String
            // 'Dim cSKUPrice As Decimal
            // 'Dim sSKUCost As String
            // 'Dim cSKUCost As Decimal
            // 'Dim rQtyFld As Decimal
            // 'Dim cCostVal As Decimal
            // Dim sModel As String
            // Dim sLinkedModel As String
            // 'Dim sUnit As String


            // 'If no model selected, display messagebox
            // If doForm.dors.getlinkcount("LNK_FOR_MO") = 0 Then
            // Return True
            // End If

            // ''Set Unit price to linked model's price
            // 'sSKUPrice = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_PRICE", 1)
            // 'If sSKUPrice <> "" Then
            // '    cSKUPrice = sSKUPrice
            // 'End If

            // 'doForm.dors.SetFieldVal("CUR_PRICEUNIT", cSKUPrice, 2)

            // ''Set Cost to linked model's cost
            // 'sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST", 1)
            // 'If sSKUCost <> "" Then
            // '    cSKUCost = sSKUCost
            // '    rQtyFld = doForm.dors.getfieldval("SR__QTY")
            // '    cCostVal = cSKUCost * rQtyFld
            // '    doForm.doRS.SetFieldVal("CUR_COST", cCostVal)
            // 'End If

            // 'If txt_model is blank set to linked model's description
            // sModel = doForm.dors.getfieldval("TXT_Model")
            // If sModel = "" Then
            // 'sLinkedModel = doForm.dors.getfieldval("LNK_FOR_MO%%TXT_ModelName", 1)
            // 'sLinkedModel = doForm.dors.getfieldval("LNK_FOR_MO%%TXT_ModelName", 1)
            // 'sLinkedModel = sLinkedModel & " - " & doForm.dors.getfieldval("LNK_FOR_MO%%MMO_DESCRIPTION", 1)
            // sLinkedModel = doForm.dors.getfieldval("LNK_FOR_MO%%LNK_Related_VE%%TXT_VendorName", 1)
            // doForm.dors.setfieldval("TXT_Model", sLinkedModel)
            // End If

            // ''Fill unit with linked model's unit
            // 'sUnit = doForm.dors.getfieldval("LNK_FOR_MO%%txt_unittext")
            // 'doForm.dors.setfieldval("TXT_Unit", sUnit)

            // 'goScr.RunScript("Quotline_CheckTaxable", doForm)
            // 'goScr.RunScript("Quotline_ConnectVendors", doForm.doRS)
            // 'goScr.RunScript("Quotline_CalcTotal", doForm)        'runs CalcTotal
            // 'goScr.RunScript("Quotline_FillItem", doForm)


            // ''Set Product link to Model's Product
            // 'doForm.doRS.ClearLinkAll("LNK_FOR_PD")
            // 'doForm.doRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2)
            // 'par_bRunNext = True

            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.MoveToTab(0);
            string date = Convert.ToString(doForm.doRS.GetFieldVal("DTE_TIME"));
            string datefield = Convert.ToString(doForm.doRS.GetFieldVal("TXT_DATE"));

            if (string.IsNullOrEmpty(datefield))
            {
                if (DateTime.TryParse(date, out DateTime parsedDate))
                {
                    datefield = parsedDate.ToString("MM-dd-yyyy");
                    doForm.doRS.SetFieldVal("TXT_DATE", datefield);
                }
            }
            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            //doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("LNK_RELATED_VE", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);
            doForm.SetFieldProperty("TXT_PARTNO", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }

            return true;

        }
        public bool Quote_FillAddress_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //goP.TraceLine("", "", sProc)

            //PURPOSE:
            //		Fill the address field, first checking whether it is empty.
            //RETURNS:
            //		True

            string sContactName = "";
            string sMailingAddr = null;
            string sFirstName = null;
            string sLastName = null;
            //Dim sCompName as string
            string sAddrMail = null;
            string sCityMail = null;
            string sStateMail = null;
            string sZipMail = null;
            string sCountryMail = null;
            string scompany = null;

            //VS 03262018 TKT#2151 : Refill data whenever Contact has changed even if not empty.
            //if (!string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_ADDRESSMAILING").ToString()))
            //    return true;
            if (doForm.doRS.GetLinkCount("LNK_ORIGINATEDBY_CN") < 1)
                return true;

            //CS 6/22/09: Create CN rowset to get CN fields
            clRowSet doRSContact = new clRowSet("CN", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN") + "'", "", "LNK_RELATED_CO,TXT_NAMEFIRST,TXT_NAMELAST,TXT_ADDRBUSINESS,TXT_CITYBUSINESS,TXT_STATEBUSINESS,TXT_ZIPBUSINESS,TXT_COUNTRYBUSINESS");
            if (doRSContact.GetFirst() == 1)
            {
                scompany = Convert.ToString(doRSContact.GetFieldVal("LNK_RELATED_CO%%TXT_COMPANYNAME"));
                sFirstName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMEFIRST"));
                sLastName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMELAST"));


                if (!string.IsNullOrEmpty(sFirstName))
                {
                    sContactName = sFirstName + " ";
                }
                sContactName += sLastName;

                sAddrMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ADDRBUSINESS"));
                sCityMail = Convert.ToString(doRSContact.GetFieldVal("TXT_CITYBUSINESS"));
                sStateMail = Convert.ToString(doRSContact.GetFieldVal("TXT_STATEBUSINESS"));
                sZipMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ZIPBUSINESS"));
                sCountryMail = Convert.ToString(doRSContact.GetFieldVal("TXT_COUNTRYBUSINESS"));

                //Start building the mailing address
                //sMailingAddr = scompany;
                //if (!string.IsNullOrEmpty(scompany))
                //{
                //    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                //}
                //sMailingAddr = scompany;
                sMailingAddr = sContactName;
                if (!sAddrMail.Contains(scompany))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                }
                if (!string.IsNullOrEmpty(sAddrMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sAddrMail;
                }
                if (!string.IsNullOrEmpty(sCityMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCityMail;
                }
                if (!string.IsNullOrEmpty(sStateMail))
                {
                    sMailingAddr = sMailingAddr + ", " + sStateMail;
                }
                if (!string.IsNullOrEmpty(sZipMail))
                {
                    sMailingAddr = sMailingAddr + " " + sZipMail;
                }
                if (!string.IsNullOrEmpty(sCountryMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCountryMail;
                }
                doForm.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailingAddr);
                doForm.doRS.SetFieldVal("MMO_ADDRMAILING", sMailingAddr);
            }


            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;

        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Rowset object containing the record to be saved.
            // par_doArray: Unused.
            // par_sMode: 'CREATION' or 'MODIF'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            clRowSet rsQT = null;
            clArray doLink = new clArray();
            // TLD 8/30/2011 Autofill Date Closed if Status is Delete
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceReasonWonLostAndDateClosed"))
            {
                switch (Convert.ToInt32(doRS.GetFieldVal("MLS_STATUS", 2)))
                {
                    case 5:      // Delete
                        {
                            if (goTR.IsDate(Convert.ToString(doRS.GetFieldVal("DTE_DATECLOSED", 1))) == false | Convert.ToString(doRS.GetFieldVal("DTE_DateClosed")) == "")
                                doRS.SetFieldVal("DTE_DATECLOSED", goTR.NowLocal(), 2);
                            break;
                        }
                }
            }
            string convtrt = Convert.ToString(doRS.oVar.GetVar("BTN_CLICK"));
            if(convtrt =="1")
            {
            string sGid_QT = Convert.ToString(doRS.GetFieldVal("Gid_Id"));
            clAttachments objclAttachments = new clAttachments();
                if (!doRS.IsLinkEmpty("LNK_RELATED_OP"))
                {
                    DataTable oTable = new DataTable();
                    oTable = null;
                    doLink = doRS.GetLinkVal("LNK_RELATED_OP", ref doLink, true, 0, -1, "A_a", ref oTable);
                    StringBuilder sAttachments = new StringBuilder();

                    for (int i = 1; i <= doLink.GetDimension(); i++)
                    {
                        string sGid = doLink.GetItem(i);
                        rsQT = new clRowSet("OP", clC.SELL_READONLY, "GID_ID='" + sGid + "'", "", "ADR_Attachments");
                        if (rsQT.GetFirst() == 1)
                        {
                            sAttachments.Append(Convert.ToString(rsQT.GetFieldVal("ADR_Attachments")) + "|");
                            objclAttachments.CopyAttachments2(sGid, sGid_QT, "OP", "QT", "ADR_Attachments", "ADR_Attachments");
                        }
                    }
                    doRS.SetFieldVal("ADR_Attachments", sAttachments.ToString());
                }
            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool Quotline_FillItem_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/2/2011 Changed to _Post, prevent main from running
            par_bRunNext = false;

            // PURPOSE:
            // Fill the TXT_MODEL field
            // RETURNS:
            // True.

            // CS: Original code 10/10/07
            // If Trim(doForm.dors.GetFieldVal("TXT_MOdel")) = "" Then
            // If doForm.dors.GetLinkCount("LNK_FOR_MO") > 0 Then
            // 'PJ 10/12/01 Remmed end of line per BKG request
            // 'MI 4/2/08: MMO_Description is replaced with TXT_Description as of 4/3/08.
            // 'If you reenable this code, you MUST test:
            // If goData.IsFieldValid("MO", "TXT_Description") Then
            // '-> Use TXT_Description field's value 
            // Else
            // '-> Use MMO_Description
            // End If
            // doForm.dors.SetFieldVal("TXT_MODEL", doForm.dors.GetFieldVal("LNK_FOR_MO%%MMO_DESCRIPTION")) '& " - " & doForm.GetFieldVal("LNK_FOR_MO%%TXT_MODELNAME")
            // End If
            // End If

            string sSKUPrice;
            decimal cSKUPrice = 0;
            string sSKUCost;
            decimal cSKUCost;
            decimal rQtyFld;
            decimal cCostVal;
            string sModel;
            string sLinkedModel = "";
            string sUnit;
            clRowSet doRSModel;


            // If no model selected, return
            if (doForm.doRS.GetLinkCount("LNK_FOR_MO") == 0)
            {
                par_doCallingObject = doForm;
                return true;
            }

            if (goData.IsFieldValid("MO", "TXT_Description"))
                // Cs 6/22/09
                doRSModel = new clRowSet("MO", 3, "GID_ID='" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO")) + "'", "", "TXT_Description,txt_unittext,lnk_of_pd,cur_price,cur_cost");
            else
                doRSModel = new clRowSet("MO", 3, "GID_ID='" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO")) + "'", "", "MMO_Description,txt_unittext,lnk_of_pd,cur_price,cur_cost");

            if (doRSModel.GetFirst() != 1)
            {
                par_doCallingObject = doForm;
                return true;
            }

            // Set Unit price to linked model's price
            // sSKUPrice = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_PRICE", 1) '6/22/09
            sSKUPrice = Convert.ToString(doRSModel.GetFieldVal("CUR_PRICE", 2));
            if (sSKUPrice != "")
                cSKUPrice = decimal.Parse(sSKUPrice);

            doForm.doRS.SetFieldVal("CUR_PRICEUNIT", cSKUPrice, 2);

            // Set Cost to linked model's cost
            // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST", 1) 'Cs 6/22/09
            sSKUCost = Convert.ToString(doRSModel.GetFieldVal("CUR_COST", 2));
            if (sSKUCost != "")
            {
                cSKUCost = decimal.Parse(sSKUCost);
                rQtyFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("SR__QTY", 2));
                cCostVal = cSKUCost * rQtyFld;
                doForm.doRS.SetFieldVal("CUR_COST", goTR.RoundCurr(cCostVal));
            }

            // TLD 9/2/1011 Fill ONLY from lnk_modelve_ve%%TXT_VENDORNAME
            // If txt_model is blank set to linked model's description
            sModel = Convert.ToString(doForm.doRS.GetFieldVal("TXT_Model"));
            if (sModel == "")
            {
                // sLinkedModel = doForm.dors.getfieldval("LNK_FOR_MO%%TXT_ModelName", 1)
                // sLinkedModel = doForm.dors.getfieldval("LNK_FOR_MO%%TXT_ModelName", 1)
                // sLinkedModel = sLinkedModel & " - " & doForm.dors.getfieldval("LNK_FOR_MO%%MMO_DESCRIPTION", 1)
                sLinkedModel = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ModelVE_VE%%TXT_VendorName", 1));
                doForm.doRS.SetFieldVal("TXT_Model", sLinkedModel);
            }

            // 'If txt_model is blank set to linked model's description
            // sModel = doForm.doRS.GetFieldVal("TXT_Model")
            // If sModel = "" Then
            // 'MI 4/2/08 MO.TXT_Description replaces MMO_Description as of 4/3/08, but MMO_Description remains in existing DBs.
            // If goData.IsFieldValid("MO", "TXT_Description") Then
            // 'Cs 6/22/09                
            // sLinkedModel = doRSModel.GetFieldVal("TXT_Description", 1)
            // Else
            // 'sLinkedModel = doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_DESCRIPTION", 1)
            // sLinkedModel = doRSModel.GetFieldVal("MMO_DESCRIPTION", 1)
            // End If
            // doForm.doRS.SetFieldVal("TXT_Model", sLinkedModel)
            // End If

            // Fill unit with linked model's unit
            // sUnit = doForm.doRS.GetFieldVal("LNK_FOR_MO%%txt_unittext") '6/22/09
            sUnit = Convert.ToString(doRSModel.GetFieldVal("txt_unittext"));
            doForm.doRS.SetFieldVal("TXT_Unit", sUnit);

            //goScr.RunScript("Quotline_CheckTaxable", doForm);

            par_doCallingObject = doForm;
            scriptManager.RunScript("Quotline_CheckTaxable", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            doForm = (Form)par_doCallingObject;

            //goScr.RunScript("Quotline_ConnectVendors", doForm.doRS);

            par_doCallingObject = doForm.doRS;
            scriptManager.RunScript("Quotline_ConnectVendors", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            doForm.doRS = (clRowSet)par_doCallingObject;

            //goScr.RunScript("Quotline_CalcTotal", doForm);

            par_doCallingObject = doForm;
            scriptManager.RunScript("Quotline_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            doForm = (Form)par_doCallingObject;

            // runs CalcTotal
            // goScr.RunScript("Quotline_FillItem", doForm)


            // Set Product link to Model's Product
            doForm.doRS.ClearLinkAll("LNK_FOR_PD");
            // doForm.doRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2) 'cs 6/22/09
            doForm.doRS.SetFieldVal("LNK_FOR_PD", doRSModel.GetFieldVal("LNK_OF_PD", 2), 2);


            // TLD 7/29/2011 Added to fill custom vendor form
            // Clear Vendor in case no MO selected OR MO's Vendor is not selected
            doForm.doRS.ClearLinkAll("LNK_ModelVE_VE");
            doForm.doRS.SetFieldVal("LNK_ModelVE_VE", doForm.doRS.GetFieldVal("LNK_For_MO%%LNK_Related_VE", 2), 2);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("Gid_id"));

            if ((doForm.doRS.IsLinkEmpty("LNK_FOR_VE")))
            {
                goErr.SetWarning(35000, sProc, "Please fill Vendor before saving the Quote");
                doForm.FieldInFocus = "LNK_FOR_VE";
                par_doCallingObject = doForm;
                return false;
            }
            if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_FOB")) == "")
            {
                goErr.SetWarning(35000, sProc, "Please fill FOB before saving the Quote");
                doForm.FieldInFocus = "TXT_FOB";
                par_doCallingObject = doForm;
                return false;
            }
            clRowSet rsQL = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sGidId + "' And TXT_PARTNO[] ", "LNK_IN_QT", "LNK_FOR_MO");

            if ((rsQL.GetFirst() == 1))
            {
                goErr.SetWarning(35000, sProc, "Please fill Part# before saving the Quote  ");
                doForm.FieldInFocus = "TXT_PARTNO";
                par_doCallingObject = doForm;
                return false;
            }
            return true;
        }
        public bool RHTESTSCRIPT(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            // rh test script for toolbar automater and alerts
            ClUI goui = new ClUI();
            //goui.Initialize();
            goui.AddAlert("Process billing reports", clC.SELL_ALT_OPENDESKTOP, "DSK_2005042614562492255MAR 00002XX", clC.SELL_USER_ALL);
            goui.AddAlert("New Message from 'Rick'", clC.SELL_ALT_OPENRECORD, "c0023d42-55b3-473d-4d53-9752008b6c04", clC.SELL_USER_ALL);

            return true;
        }


        // SGR TKT#:987 03042016
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/20/07 Added offering to link the Team Leader to all connected Contacts. Optionally, unlink the old Team Leader.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // SGR TKT#:987 03042016
            par_bRunNext = false;
            // --- HISTORY ---
            // 2005/08/25 10:01:59 MAR Removed filling Involves User with <me>.

            Form doForm = (Form)par_doCallingObject;
            string sWork = "";
            string sCompanyName;
            // Dim sCity As String
            // Dim doRS As Object
            // Dim lWork As Long
            // Dim sWork2 As String
            bool bCheckDup;
            // Dim sMessage As String
            string sScriptVar;
            string sOldTeamLeaderID;
            string sNewTeamLeaderID;
            string sTeamLeaders;

            // goP.TraceLine("", "", sProc)

            // Want to abort the save after the user answered answered that way on a MessageBoxEvent 
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CancelSave"))
            {
                if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
                {
                    // Reset variables so that save will run next time it is clicked.
                    doForm.oVar.SetVar("Company_DupCheck", "");
                    doForm.oVar.SetVar("CancelSave", "");
                    par_doCallingObject = doForm;
                    return false;
                }
            }

            // ---------------- ENFORCE -----------------
            sCompanyName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_COMPANYNAME"));
            // CS: 7/6/07 In MD
            // If sCompanyName = "" Then
            // doForm.MoveToField("TXT_COMPANYNAME")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("CO", "TXT_CompanyName"), "", "", "", "", "", "", "", "", "TXT_CompanyName")
            // Return False
            // End If


            // CS: Move to RecOnSave
            // Uppercase the code
            // doForm.doRS.SetFieldVal("TXT_CUSTCODE", UCase(doForm.doRS.GetFieldVal("TXT_CUSTCODE")))

            // THIS IS IN RECORDONSAVE
            // '--------------- CHECK DUPLICATE CODES --------------------
            // 'Company Code
            // goscr.runscript("Company_ValidateName",doForm,NULL,doForm.GetFieldVal("TXT_CUSTCODE"))
            // If goProject.GetVar("sDuplCustCodeCount") <> "0" Then
            // doForm.MoveToField("TXT_CUSTCODE")
            // MsgBox("This Company Code is already in use. Please edit the code to make it unique.",64,"Selltis")
            // Return False
            // End If

            // ------------- DUPLICATES ---------------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CheckDuplicates"))
            {
                bCheckDup = false;
                // goP.TraceLine("Mode: " & doForm.GetMode(), "", sProc)
                if (doForm.GetMode() == "CREATION")
                    bCheckDup = true;
                else
                    // Check duplicates if the user changed the name or city
                    // goP.TraceLine("Company Name: '" & UCase(doForm.doRS.GetFieldVal("TXT_COMPANYNAME")) & "'", "", sProc)
                    // goP.TraceLine("Var CompanyNameOnLoad: '" & UCase(doForm.oVar.GetVar("CompanyNameOnLoad")) & "'", "", sProc)
                    if (Strings.UCase(sCompanyName) != Convert.ToString(doForm.oVar.GetVar("CompanyNameOnLoad")).ToUpper())
                    // goP.TraceLine("Company name changed", "", sProc)
                    bCheckDup = true;
                else
                        // goP.TraceLine("City mailing: '" & UCase(doForm.doRS.GetFieldVal("TXT_CITYMAILING")) & "'", "", sProc)
                        // goP.TraceLine("Var CompanyCityOnLoad '" & UCase(doForm.oVar.GetVar("CompanyCityOnLoad")) & "'", "", sProc)
                        if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_CITYMAILING")).ToUpper() != Convert.ToString(doForm.oVar.GetVar("CompanyCityOnLoad")).ToUpper())
                    // goP.TraceLine("City changed", "", sProc)
                    bCheckDup = true;
                // goP.TraceLine("bCheckDup: '" & bCheckDup & "'", "", sProc)

                if (Convert.ToString(doForm.oVar.GetVar("Company_DupCheck")) != "1")
                {
                    if (bCheckDup)
                    {
                        par_doCallingObject = doForm;
                        switch (scriptManager.RunScript("Company_DupCheck", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "CO_FormOnSave"))
                        {
                            case true:
                                // Returned true but check if it is b/c we need to display a messagebox
                                // Cs 8/5/09: Check if we are in 'Save and leave open mode'. Previously the form would close
                                // after clicking yes on the mb to save the dup.
                                doForm = (Form)par_doCallingObject;

                                if (doForm.MessageBoxFormMode == "SAVEANDKEEPOPEN")
                                {
                                }
                                else if (Convert.ToString(doForm.oVar.GetVar("Company_DupCheck")) == "1")
                                {
                                    par_doCallingObject = doForm;
                                    return false;
                                }

                                break;
                            case false:
                                doForm = (Form)par_doCallingObject;
                                break;
                        }
                    }
                }
            }


            // Now done above
            // If bCheckDup Then
            // goP.TraceLine("Checking duplicates", "", sProc)
            // sCity = doForm.GetFieldVal("TXT_CITYMAILING")
            // If goData.IsDuplicate(doForm.GetFileName(), doForm.GetRecordID(), sCompanyName, sCity) Then
            // doRS = goData.GetDuplicates(doForm.GetFileName(), doForm.GetRecordID(), sCompanyName, sCity)
            // 'CS: Per MI, IsObjAss commentd out
            // 'If Not IsObjectAssigned(doRS) Then
            // '    goErr.DisplayLastError()
            // '    goErr.SetError()
            // '    GoTo AfterDuplicates
            // 'End If
            // If Not doRS.GetFirst() Then
            // goErr.DisplayLastError()
            // goErr.SetError()
            // 'delete(doRS)
            // doRS = Nothing
            // GoTo AfterDuplicates
            // Else
            // lWork = 1
            // Do
            // sWork &= doRS.GetFieldVal("TXT_COMPANYNAME") & " "
            // sWork &= doRS.GetFieldVal("TXT_CUSTCODE") & "   "
            // sWork &= doRS.GetFieldVal("TXT_CITYMAILING") & " "
            // sWork &= doRS.GetFieldVal("TXT_STATEMAILING") & " "
            // sWork &= doRS.GetFieldVal("TXT_ZIPMAILING") & vbCrLf
            // If Not doRS.GetNext() Then Exit Do
            // lWork += 1
            // If lWork > 20 Then
            // sWork &= "..."
            // Exit Do
            // End If
            // Loop
            // End If
            // 'delete(doRS)
            // doRS = Nothing
            // If sCity = "" Then
            // sMessage = "One or more potential duplicates exist for '" & sCompanyName & "':" & vbCrLf & vbCrLf & _
            // sWork & vbCrLf & _
            // "Save the company anyway? Click No to edit the company."
            // Else
            // sMessage = "One or more potential duplicates exist for '" & sCompanyName & "' in '" & sCity & "':" & vbCrLf & vbCrLf & _
            // sWork & vbCrLf & _
            // "Save the company anyway? Click No to edit the company."
            // End If
            // 'CS: Msgbox must be replaced in web context.
            // 'If MsgBox(sMessage, 64 + 4, "Selltis") <> 6 Then
            // '    'No
            // '    doForm.MoveToField("TXT_COMPANYNAME")
            // '    Return False
            // 'End If
            // End If
            // End If
            // AfterDuplicates:


            // CS: 7/6/07 In MD
            // If doForm.doRS.GetLinkCount("LNK_TEAMLEADER_US") = 0 Then
            // doForm.MoveToField("LNK_TEAMLEADER_US")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("CO", "LNK_TeamLeader_US"), "", "", "", "", "", "", "", "", "LNK_TeamLeader_US")
            // Return False
            // End If

            // ==> Finish
            // 'if partner company, check logon uniqueness
            // If doForm.Field("Tax ID") <> "" And doForm.Field("SIC 8 Name") <> "" Then
            // Dim objConn
            // Set objConn = doForm.Connection("Related", "Group")
            // For i = 1 To objConn.ConnectedItemCount
            // objConn.CurrentSelection = i
            // If objConn.FieldValue("Name") = "Selltis Channel Partners" Then
            // If Not IsLogonUnique(Field("Tax ID")) Then
            // Abort
            // doForm.MoveToTab("Partners")
            // goscr.runscript("Company_ManageExtraFields",doForm)
            // doForm.MoveToField("Tax ID")
            // MsgBox "Username '"  &  doForm.Field("Tax ID")  &  "' is in assigned to another Partner."  &  vbCrLf  &  vbCrLf  &  _
            // "Please make this unique before saving this record.", vbOKOnly  &  vbInformation, "Selltis"
            // Return false
            // End If
            // Break
            // End If
            // Next i
            // End If


            // CS: Move below to RecOnSave
            // '--------- FROM FILL NAME FUNCTION ----------------
            // sWork = doForm.GetFieldVal("TXT_STATEMAILING")
            // If sWork <> UCase(sWork) Then doForm.SetFieldVal("TXT_STATEMAILING", UCase(sWork))

            // sWork = doForm.GetFieldVal("TXT_STATEBILLING")
            // If sWork <> UCase(sWork) Then doForm.SetFieldVal("TXT_STATEBILLING", UCase(sWork))

            // sWork = doForm.GetFieldVal("TXT_STATESHIPPING")
            // If sWork <> UCase(sWork) Then doForm.SetFieldVal("TXT_STATESHIPPING", UCase(sWork))

            // doForm.SetFieldVal("LNK_INVOLVES_US", doForm.GetFieldVal("LNK_TEAMLEADER_US"))

            // CS: Move to RecOnSave
            // goScr.RunScript("Company_ConnectSource", doForm)
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FixAddresses"))
            {
                //goScr.RunScript("Company_FixAddresses", doForm);
                par_doCallingObject = doForm;
                scriptManager.RunScript("Company_FixAddresses", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                doForm = (Form)par_doCallingObject;
            }

            // Customer Code
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillCustomerCode"))
            {
                if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_CUSTCODE")) == "")
                {
                    //goScr.RunScript("Company_FillCustCode", doForm.doRS);// runs CheckDup
                    par_doCallingObject = doForm.doRS;
                    scriptManager.RunScript("Company_FillCustCode", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                    doForm.doRS = (clRowSet)par_doCallingObject;
                }
                if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_CUSTCODE")).Trim() == "")
                {
                    doForm.MoveToField("TXT_CUSTCODE");
                    goErr.SetWarning(30200, sProc, "", "The Customer Code could not be generated automatically. Please enter it manually.", "", "", "", "", "", "", "", "", "TXT_CUSTCODE");
                    par_doCallingObject = doForm;
                    return false;
                }
            }

            // ----- Uncheck CHK_EXTDEVREVIEW
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "UncheckExternalDeviceReview"))
            {
                if (doForm.GetMode() != "CREATION")
                {
                    if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_EXTDEVREVIEW", 2)) == 1)
                        doForm.doRS.SetFieldVal("CHK_EXTDEVREVIEW", 0, 2);
                }
            }

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "OfferToLinkTeamLeaderToLinkedContacts"))
            {
                // If the form is being edited and the team leader changed, offer to
                // link the team leader to all contacts linked to this company and, optionally,
                // to unlink the old team leader from the linked contacts.
                // Check whether the user already clicked a button in the messagebox
                if (Convert.ToString(doForm.oVar.GetVar("CO_FormOnSave_LinkTeamLeaderMsgBox")) != "1")
                {
                    if (doForm.GetMode() != "CREATION")
                    {
                        sOldTeamLeaderID = Convert.ToString(doForm.oVar.GetVar("TeamLeaderIDOnLoad"));
                        sNewTeamLeaderID = Convert.ToString(doForm.doRS.GetFieldVal("LNK_TeamLeader_US"));
                        if (sOldTeamLeaderID != sNewTeamLeaderID)
                        {
                            // CS 6/13/11 now set in messagebox call :doForm.oVar.SetVar("CO_FormOnSave_LinkTeamLeaderMsgBox", "1")
                            sTeamLeaders = sNewTeamLeaderID + "|" + sOldTeamLeaderID;
                            // doForm.MessageBox("You changed the Team Leader from '" & doForm.oVar.GetVar("TeamLeaderNameOnLoad") & "' to '" & doForm.doRS.GetFieldVal("LNK_TeamLeader_US%%SYS_Name") & "'. Would you like to link the new Team Leader to the Contacts of this Company?" & vbCrLf & vbCrLf & "This may take a few minutes.", clC.SELL_MB_YESNOCANCEL + clC.SELL_MB_DEFBUTTON1, "Link Team Leader to Contacts", "Yes", "Yes, and unlink old Team Leader", "No", , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "1", "2", "3", sTeamLeaders, "CO_FormOnSave_LinkTeamLeaderMsgBox")
                            // CS 8/25/08: Change from US SYS Name to Last, First b/c SYS Name can change
                            // CS 6/19/09: Create one rowset on US instead of 2 double hops (2 rowsets)
                            clRowSet doRSUS = new clRowSet("US", 3, "GID_ID='" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_TEAMLEADER_US")) + "'", "", "TXT_NAMELAST,TXT_NAMEFIRST");
                            doForm.MessageBox("You changed the Team Leader from '" + Convert.ToString(doForm.oVar.GetVar("TeamLeaderNameOnLoad")) + "' to '" + Convert.ToString(doRSUS.GetFieldVal("TXT_NameLAst")) + ", " + Convert.ToString(doRSUS.GetFieldVal("TXT_NameFirst")) + "'. Would you like to link the new Team Leader to the Contacts of this Company?" + Constants.vbCrLf + Constants.vbCrLf + "This may take a few minutes.", clC.SELL_MB_YESNOCANCEL + clC.SELL_MB_DEFBUTTON1, "Link Team Leader to Contacts", "Yes", "Yes, and unlink old Team Leader", "No", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "1", "2", "3", sTeamLeaders, "CO_FormOnSave_LinkTeamLeaderMsgBox", "CO_FormOnSave_LinkTeamLeaderMsgBox");
                            par_doCallingObject = doForm;
                            return true;
                        }
                        // Maybe some other form code will need this information, so let's not delete it.
                        // doForm.oVar.DeleteVar("TeamLeaderIDOnLoad")
                        // doForm.oVar.DeleteVar("TeamLeaderNameOnLoad")
                        // doForm.MessageBox("Below the 'Link Team Leader' messagebox.")
                        //sNewTeamLeaderID = sNewTeamLeaderID;
                    }
                }
            }
            if (Convert.ToString(doForm.oVar.GetVar("CO_Linked_CN")) != "1")
            {
                if (doForm.GetMode() != "CREATION")
                {
                    string Address = Convert.ToString(doForm.oVar.GetVar("Address"));
                    string State = Convert.ToString(doForm.oVar.GetVar("State"));
                    string City = Convert.ToString(doForm.oVar.GetVar("City"));
                    string Zip = Convert.ToString(doForm.oVar.GetVar("Zip"));
                    string Division = Convert.ToString(doForm.oVar.GetVar("Division"));
                    string Coname = Convert.ToString(doForm.oVar.GetVar("Coname"));

                    string AddressNew = Convert.ToString(doForm.doRS.GetFieldVal("TXT_ADDRMAILING"));
                    string StateNew = Convert.ToString(doForm.doRS.GetFieldVal("TXT_STATEMAILING"));
                    string CityNew = Convert.ToString(doForm.doRS.GetFieldVal("TXT_CITYMAILING"));
                    string ZipNew = Convert.ToString(doForm.doRS.GetFieldVal("TXT_ZIPMAILING"));
                    string DivisionNew = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_DV%%GID_ID"));
                    string ConameNew = Convert.ToString(doForm.doRS.GetFieldVal("TXT_COMPANYNAME"));

                    StringBuilder changeMsg = new StringBuilder();
                    if (Address != AddressNew)
                        changeMsg.AppendLine($"You changed the Address from '{Address}' to '{AddressNew}'.");
                    if (State != StateNew)
                        changeMsg.AppendLine($"You changed the State from '{State}' to '{StateNew}'.");
                    if (City != CityNew)
                        changeMsg.AppendLine($"You changed the City from '{City}' to '{CityNew}'.");
                    if (Zip != ZipNew)
                        changeMsg.AppendLine($"You changed the Zip from '{Zip}' to '{ZipNew}'.");
                    if (Division != DivisionNew)
                        changeMsg.AppendLine($"You changed the Division from '{Division}' to '{DivisionNew}'.");
                    if (Coname != ConameNew)
                        changeMsg.AppendLine($"You changed the Company Name from '{Coname}' to '{ConameNew}'.");

                    if (changeMsg.Length > 0)
                    {
                        changeMsg.AppendLine();
                        changeMsg.AppendLine("Would you like to change the new information to the Contacts of this Company?");
                        changeMsg.AppendLine("This may take a few minutes.");
                        doForm.MessageBox(changeMsg.ToString(),clC.SELL_MB_YESNO,"Selltis","Yes","No","","","MessageBoxEvent","MessageBoxEvent","MessageBoxEvent",doForm,null,"Yes","No","","CO_Linked_CN","CO_Linked_CN");
                        par_doCallingObject = doForm;
                        return true;
                    }

                }
            }
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "ConnectContactsAsCustomers"))
            {
                if (Convert.ToString(doForm.oVar.GetVar("Company_ConnCntctsAsCustomers")) != "1")
                {
                    //goScr.RunScript("Company_ConnCntctsAsCustomers", doForm);
                    par_doCallingObject = doForm;
                    scriptManager.RunScript("Company_ConnCntctsAsCustomers", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                    doForm = (Form)par_doCallingObject;
                }
            }

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "DisplayErrorMessages"))
            {
                sScriptVar = Convert.ToString(doForm.oVar.GetVar("ScriptMessages"));
                if (sScriptVar != "")
                {
                    if (Strings.Len(sScriptVar) < 500)
                        doForm.MessageBox(sScriptVar, 0, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", "CO_FormOnSave_ScriptMessages");
                    else
                        doForm.MessageBox(Strings.Left(sScriptVar, 497) + "...", 0, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", "CO_FormOnSave_ScriptMessages");
                    par_doCallingObject = doForm;
                    return true;
                }
            }

            // SGR TKT#:987 03042016
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CO_Merge")) != "1")
                    {
                        // Don't allow merge of company to itself
                        if (Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")) == Convert.ToString(doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID")))
                            // doForm.MessageBox("You cannot merge a company to itself.  Please select a different merge to company.", clC.SELL_MB_OK, , , , , , "MessageBoxEvent", , , doForm, , "OK", , , , "MergeCOFail")
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CO", "MergeFail");
                        else
                            // doForm.MessageBox("This company will be merged to the target company, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") & "'. Blank fields on the target company will be filled from this company record and all links will be copied to the target company. Are you sure you want to merge this company?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "MergeCO")
                            doForm.MessageBox("This record will be merged to the target record, '" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name")) + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CO", "Merge");
                    }
                }
            }

            par_doCallingObject = doForm;
            return true;
        }

        // SGR TKT#:987 03042016
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // SGR TKT#:987 03042016
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sNameLast;
            string sNameFirst;
            string sWork = "";
            // Dim doRS As Object
            // Dim lWork As Long
            bool bCheckDup;

            goErr.SetError();

            // goP.TraceLine("", "", sProc)

            // Want to abort the save after the user answered answered that way on a MessageBoxEvent 
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                // Reset variables so that save will run next time it is clicked.
                // doForm.ovar.setvar("Contact_NotesLength", "")
                doForm.oVar.SetVar("Contact_CountPhoneFields", "");
                doForm.oVar.SetVar("Contact_DupCheck", "");
                doForm.oVar.SetVar("CancelSave", "");
                par_doCallingObject = doForm;
                return false;
            }

            // ------------- ENFORCE ------------
            // Offer to share records created externally. 
            // Call this as the first thing in the OnSave script!
            if (Convert.ToString(doForm.oVar.GetVar("ShareItem_Ran")) != "1")
            {
                //goScr.RunScript("ShareItem", doForm);
                par_doCallingObject = doForm;
                scriptManager.RunScript("ShareItem", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                doForm = (Form)par_doCallingObject;
            }

            // CS: 7/6/07 In MD
            // Enforce Name Last field
            sNameLast = Convert.ToString(doForm.doRS.GetFieldVal("TXT_NAMELAST"));
            sNameFirst = Convert.ToString(doForm.doRS.GetFieldVal("TXT_NAMEFIRST"));
            // If sNameLast = "" Then
            // doForm.MoveToField("TXT_NAMELAST")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("CN", "TXT_NameLast"), "", "", "", "", "", "", "", "", "TXT_Namelast")
            // Return False
            // End If

            // -------------- DUPLICATES ---------------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CheckDuplicates"))
            {
                bCheckDup = false;
                if (doForm.GetMode() == "CREATION")
                    bCheckDup = true;
                else
                    // Check duplicates if the user changed the name 
                    if (Strings.UCase(sNameLast) != Convert.ToString(doForm.oVar.GetVar("ContactNameLastOnLoad")).Trim())
                    bCheckDup = true;
                else if (Strings.UCase(sNameFirst) != Convert.ToString(doForm.oVar.GetVar("ContactNameFirstOnLoad")).Trim())
                    bCheckDup = true;

                if (Convert.ToString(doForm.oVar.GetVar("Contact_DupCheck")) != "1")
                {
                    if (bCheckDup)
                    {
                        par_doCallingObject = doForm;
                        switch (scriptManager.RunScript("Contact_DupCheck", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "CN_FormOnSave"))
                        {
                            case true:
                                doForm = (Form)par_doCallingObject;
                                // Returned true but check if it is b/c we need to display a messagebox
                                if (Convert.ToString(doForm.oVar.GetVar("Contact_DupCheck")) == "1")
                                    // CS Return False
                                    // CS 2/4/07: Added Return True instead of False. This keeps the CN in 'save mode' so
                                    // that if the user clicks Yes on the mb, it automatically tries to save the CN again.
                                    par_doCallingObject = doForm;
                                return true;
                                break;
                            case false:
                                doForm = (Form)par_doCallingObject;
                                break;

                        }
                    }
                }
                else
                {
                    // If we already checked for dups and the user clicked yes to save the CN
                    // need to make sure they entered a unique code.
                    // *** MI 11/14/07 This code displayed the message when duplicates with substitute names
                    // (John/Jon/Jonathan/J.) were discovered by the DupCheck algorithm. 
                    // If doForm.doRS.GetFieldVal("TXT_Contactcode") = "" Then
                    // doForm.Messagebox("A Contact with the same first and last name already exists." & vbCrLf & _
                    // "Please enter a Unique Code for this Contact.")
                    // End If
                    // This checks for exact duplicates and displays the prompt for Unique code
                    // only in this case
                    clRowSet doRS = new clRowSet("CN", 3, "TXT_NameLast='" + goTR.PrepareForSQL(sNameLast) + "' and TXT_NameFirst='" + goTR.PrepareForSQL(sNameFirst) + "' and TXT_ContactCode='" + goTR.PrepareForSQL(Convert.ToString(doForm.doRS.GetFieldVal("TXT_ContactCode"))) + "'", "", "GID_ID", 1);  // MI 11/6/08 Added goTr.PrepareForSQL
                    if (doRS.Count() > 0)
                    {
                        doForm.MessageBox("A Contact exists with the same First Name, Last Name, and Unique Code." + Constants.vbCrLf + "Please modify one of these three fields.");
                        // doForm.MoveToField("TXT_UniqueCode")
                        // Return False
                        par_doCallingObject = doForm;
                        return false;
                    }
                }
            }

            // CS: 7/6/07 In MD
            // If doForm.doRS.GetLinkCount("LNK_RELATED_US") = 0 Then
            // doForm.MoveToTab(1)
            // 'CS goScr.RunScript("Contact_ManageExtraFields", doForm)
            // doForm.MoveToField("LNK_RELATED_US")
            // goErr.SetWarning(30200, sProc, "", "Please select one or more users who call on this Contact.", "", "", "", "", "", "", "", "", "LNK_Related_US")
            // Return False
            // End If

            // 'CS: Move to RecOnSave
            // 'Fill the Company Name Text if empty
            // If doForm.doRS.GetFieldVal("TXT_COMPANYNAMETEXT") = "" Then
            // doForm.doRS.SetFieldVal("TXT_COMPANYNAMETEXT", doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_COMPANYNAME"))
            // End If
            // 'Uppercase the State fields
            // doForm.doRS.SetFieldVal("TXT_STATEBUSINESS", UCase(doForm.doRS.GetFieldVal("TXT_STATEBUSINESS")))
            // doForm.doRS.SetFieldVal("TXT_STATEHOME", UCase(doForm.doRS.GetFieldVal("TXT_STATEHOME")))
            // doForm.doRS.SetFieldVal("TXT_STATEOTHER", UCase(doForm.doRS.GetFieldVal("TXT_STATEOTHER")))

            // CS: Per MI,  Few people use Palm anymore. When they do, they may corrupt long notes. tough. 
            // We'd have to build in a way to know whether there is at least 1 user who uses the Palm Link 
            // and uses it in the two way mode in order to display this. it's a nuisance to 99% of the 
            // users for .001 % of cases.
            // Warn about Note exceeding 8k (Palm limit)
            // Only check if didn't already warn.
            // If doForm.ovar.getvar("Contact_Noteslength") <> "1" Then
            // Select Case goScr.RunScript("Contact_NotesLength", doForm)
            // Case "True"
            // 'Returned true but check if it is b/c we need to display a messagebox
            // If doForm.ovar.getvar("Contact_NotesLength") = "1" Then
            // Return False
            // End If

            // 'Continue
            // Case "False"
            // Return False
            // 'Case Else
            // '    goErr.DisplayLastError()
            // '    goErr.SetError()
            // End Select
            // End If

            // CS:Per MI, remove
            // Warn about inability to sync > 5 telephone type fields
            // If doForm.ovar.getvar("Contact_CountPhoneFields") <> "1" Then
            // Select Case goScr.RunScript("Contact_CountPhoneFields", doForm)
            // Case "True"
            // 'Returned true but check if it is b/c we need to display a messagebox
            // If doForm.ovar.getvar("Contact_CountPhoneFields") = "1" Then
            // Return False
            // End If
            // 'Continue
            // Case "False"
            // Return False
            // 'Case Else
            // '    goErr.DisplayLastError()
            // '    goErr.SetError()
            // End Select
            // End If

            // CS: Move to RecOnSave
            // goScr.RunScript("Contact_ConnectVendors", doForm)
            // goScr.RunScript("Contact_ConnectSource", doForm)
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "PropertizeAddress"))
            {
                //goScr.RunScript("PropertizeAddress", doForm, , "TXT_ADDRBUSINESS");
                par_doCallingObject = doForm;
                scriptManager.RunScript("PropertizeAddress", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "TXT_ADDRBUSINESS");
                //goScr.RunScript("PropertizeAddress", doForm, , "TXT_ADDRHOME");
                scriptManager.RunScript("PropertizeAddress", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "TXT_ADDRHOME");
                //goScr.RunScript("PropertizeAddress", doForm, , "TXT_ADDROTHER");
                scriptManager.RunScript("PropertizeAddress", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "TXT_ADDROTHER");
                doForm = (Form)par_doCallingObject;
            }

            // ----- Uncheck CHK_EXTDEVREVIEW
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "UncheckCHK_ExtDevReview"))
            {
                if (doForm.GetMode() != "CREATION")
                {
                    if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_EXTDEVREVIEW", 2)) == 1)
                        doForm.doRS.SetFieldVal("CHK_EXTDEVREVIEW", 0, 2);
                }
            }
            // SGR TKT#:987 03042016
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CN_Merge")) != "1")
                    {
                        // Don't allow merge of contact to itself
                        if (Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")) == Convert.ToString(doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID")))
                            // doForm.MessageBox("You cannot merge a contact to itself.  Please select a different merge to contact.", clC.SELL_MB_OK, , , , , , "MessageBoxEvent", , , doForm, , "OK", , , , "MergeCNFail")
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CN", "MergeFail");
                        else
                            // doForm.MessageBox("This contact will be merged to the target contact, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") & "'. Blank fields on the target contact will be filled from this contact record and all links will be copied to the target contact. Are you sure you want to merge this contact?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "MergeCN")
                            doForm.MessageBox("This record will be merged to the target record, '" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name")) + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CN", "Merge");
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        // SGR TKT#:987 03042016
        public bool MergeRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS 12222014 Added for merge
            par_bRunNext = false;

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + Convert.ToString(doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName())) + "'", "", "**", -1, "", "", "", "", "", true, true, false, false, -1, "", true);
                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (Convert.ToString(doRSMergeTo.GetFieldVal(sField)) == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    break;
                                }

                            case "MMO":
                                {
                                    // Append
                                    if (Convert.ToString(doRSMergeTo.GetFieldVal(sField)) == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    else
                                        doRSMergeTo.SetFieldVal(sField, Convert.ToString(doRSMergeTo.GetFieldVal(sField)) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + Convert.ToString(doRSMerge.GetFieldVal("SYS_Name")) + " ==" + Constants.vbCrLf + Convert.ToString(doRSMerge.GetFieldVal(sField)));
                                    break;
                                }

                            case "CHK":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }

                            case "MLS":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9).ToString());
                        if (sLinkType[4] == "NN" | int.Parse(sLinkType[1]) == 2)
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (Convert.ToString(doRSMergeTo.GetFieldVal(aLinks.GetItem(i))) == "")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);
                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;

            par_doCallingObject = doRSMerge;
            return true;
        }
        // SGR TKT#:987 03042016
        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/27/07 Changed Dim doForm As Form to Dim doForm As Object.
            // MI 11/21/07 Updated comments.
            // MI 10/8/07 Added Return True per CS.
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // par_doCallingObject is always the doForm under which MessageBox was executed.
            // par_doArray is an array of strings through which you can pass multiple strings to this method.
            // Par_s1 is the identifier of the button clicked. usually "YES", "NO", "CANCEL", or "1", "2", or "3". It is
            // one of the values from par_s1-3 passed to doForm.MessageBox itself, depending on the button clicked.
            // For example, if you click button 2, the value from par_s2 will be passed here. 
            // par_s2 is the value the user entered in an input box, if the type is input box. Else, blank.
            // par_s3 is the identifier of the third button clicked. usually "CANCEL" or "3".
            // Par_s4 can be whatever you want to pass to this method.
            // Par_s5 is the name of the script that called doform.MessageBox plus a description of what it's doing, e.g. "CO_FormOnSave_LinkTeamLeaderMsgBox".

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // SGR TKT#:987 03042016
            par_bRunNext = false;
            // DEBUG - MI experimenting with declaring a variable to be Form or clDesktop
            // Dim oType As System.Type = Nothing
            // oType = par_doCallingObject.GetType.ToString
            // Dim doForm As oType

            Form doForm = (Form)par_doCallingObject;
            string sWork; // input value
            string sWork2;
            string sJournal; // original value in journal field
            clRowSet doRS;
            bool bUpdateFailed;
            bool bNoPerm = false;
            bool bError = false;
            bool bReqMissing = false;
            // Dim sView As String


            try
            {
                switch (Strings.UCase(par_s5))
                {
                    case "MERGE":
                        {
                            doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        // run merge script, continue save
                                        par_doCallingObject = doForm.doRS;
                                        scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                                        doForm.doRS = (clRowSet)par_doCallingObject;
                                        break;
                                    }

                                case "NO":
                                    {
                                        // Clear merged to co linkbox, continue save
                                        doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                        break;
                                    }

                                case "CANCEL":
                                    {
                                        // Clear merged to co linkbox, cancel save
                                        doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "MERGEFAIL":
                        {
                            doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        // Clear merged to co linkbox, cancel save
                                        doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "TEST":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        break;
                                    }

                                case "NO":
                                    {
                                        break;
                                    }

                                case "CANCEL":
                                    {
                                        //par_doCallingObject.Cancel = true;
                                        break;
                                    }
                            }

                            break;
                        }

                    case "AC_VIEWCONTROLONCHANGE_BTN_COMPLETEALL":
                        {
                            bUpdateFailed = false;
                            doRS = new clRowSet("AC", 1, "LNK_CreditedTo_US='<%MEID%>' AND CHK_CORRRECEIVED=1 and MLS_STATUS=0", goData.GetDefaultSort("AC") + " a", "*");

                            if (doRS.GetFirst() == 1)
                            {
                                do
                                {
                                    doRS.SetFieldVal("MLS_STATUS", 1, 2);
                                    if (doRS.Commit() == 0)
                                    {
                                        bUpdateFailed = true;
                                        if (goErr.GetLastError("NUMBER") == "E47250")
                                            bNoPerm = true;
                                        else if (goErr.GetLastError("NUMBER") == "E47260")
                                            bReqMissing = true;
                                        else
                                            bError = true;
                                    }

                                    if (doRS.GetNext() == 0)
                                        break;
                                }
                                while (true);// failed b/c of permissions// failed b/c of other errors
                                if (bUpdateFailed)
                                {
                                    //goUI.RefreshOpenDesktop();
                                    if (bError == true & bNoPerm == false)
                                        doForm.MessageBox("Some records couldn't be marked as completed. Please try again and contact your Selltis administrator if you are unsuccessful.");
                                    else if (bError == false & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as completed because you do not have permission to edit the record(s).");
                                    else if (bError == true & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as completed. You either do not have permission to edit the remaining records or there are required fields missing." + Constants.vbCrLf + "You should open each record " + "remaining in the view, review mandatory fields and attempt to Save.");
                                    else if (bReqMissing == true)
                                        doForm.MessageBox("Some records couldn't be marked as completed. Please open each record " + "remaining in the view, fill out all mandatory fields and Save.");
                                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                                    par_doCallingObject = doForm;
                                    return true;
                                }
                                //goUI.RefreshOpenDesktop();
                            }
                            else
                            {
                            }
                            doRS = null/* TODO Change to default(_) if this is not a reference type */;
                            break;
                        }

                    case "AC_VIEWCONTROLONCHANGE_BTN_CORR":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        //AC_ViewControlOnChange_BTN_CORR(ref par_doCallingObject, par_s1: "Yes");
                                        scriptManager.RunScript("AC_ViewControlOnChange_BTN_CORR", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, "Yes");
                                        break;
                                    }

                                case "NO":
                                    {
                                        //AC_ViewControlOnChange_BTN_CORR(ref par_doCallingObject, par_s1: "No");
                                        scriptManager.RunScript("AC_ViewControlOnChange_BTN_CORR", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, "No");
                                        break;
                                    }

                                case "CANCEL":
                                    {
                                        //AC_ViewControlOnChange_BTN_CORR(ref par_doCallingObject, par_s1: "Cancel");
                                        scriptManager.RunScript("AC_ViewControlOnChange_BTN_CORR", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, "Cancel");
                                        break;
                                    }

                                case "OK":
                                    {
                                        if (doForm.Save(1) == 0)
                                        {
                                            par_doCallingObject = doForm;
                                            return false;
                                        }
                                        break;
                                    }
                            }

                            break;
                        }

                    case "AC_FORMCONTROLONCHANGE_BTN_CORR":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        //AC_FormControlOnChange_BTN_CORR(ref par_doCallingObject, par_s1: "Yes");
                                        scriptManager.RunScript("AC_FormControlOnChange_BTN_CORR", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, "Yes");
                                        break;
                                    }

                                case "NO":
                                    {
                                        //AC_FormControlOnChange_BTN_CORR(ref par_doCallingObject, par_s1: "No");
                                        scriptManager.RunScript("AC_FormControlOnChange_BTN_CORR", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, "No");
                                        break;
                                    }

                                case "CANCEL":
                                    {
                                        //AC_FormControlOnChange_BTN_CORR(ref par_doCallingObject, par_s1: "Cancel");
                                        scriptManager.RunScript("AC_FormControlOnChange_BTN_CORR", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, "Cancel");
                                        break;
                                    }

                                case "OK":
                                    {
                                        if (doForm.Save(1) == 0)
                                        {
                                            par_doCallingObject = doForm;
                                            return false;
                                        }
                                        break;
                                    }
                            }

                            break;
                        }

                    case "AC_VIEWCONTROLONCHANGE_BTN_MARKALLASSENT":
                        {
                            bUpdateFailed = false;
                            doRS = new clRowSet("AC", 1, "LNK_CreditedTo_US='<%MEID%>' AND CHK_CORRSENT=1 and CHK_SENT=0", goData.GetDefaultSort("AC") + " a", "*");

                            if (doRS.GetFirst() == 1)
                            {
                                do
                                {
                                    // Check if have perm to edit
                                    doRS.SetFieldVal("CHK_SENT", 1, 2);
                                    if (doRS.Commit() == 0)
                                    {
                                        // Check if failed b/c of permissions or something else
                                        bUpdateFailed = true;
                                        if (goErr.GetLastError("NUMBER") == "E47250")
                                            bNoPerm = true;
                                        else if (goErr.GetLastError("NUMBER") == "E47260")
                                            bReqMissing = true;
                                        else
                                            bError = true;
                                    }

                                    if (doRS.GetNext() == 0)
                                        break;
                                }
                                while (true);// failed b/c of permissions// failed b/c of other errors
                                if (bUpdateFailed)
                                {
                                    //goUI.RefreshOpenDesktop();
                                    if (bError == true & bNoPerm == false)
                                        doForm.MessageBox("Some records couldn't be marked as sent. Please try again. If you are unsuccessful contact your Selltis administrator.");
                                    else if (bError == false & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as sent because you do not have permission to edit the record(s).");
                                    else if (bError == true & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as sent. You either do not have permission to edit the remaining records or there are required fields missing." + Constants.vbCrLf + "You should open each record " + "remaining in the view, review mandatory fields and attempt to Save.");
                                    else if (bReqMissing == true)
                                        doForm.MessageBox("Some records couldn't be marked as sent. Please open each record " + "remaining in the view, fill out all mandatory fields and Save.");
                                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                                    par_doCallingObject = doForm;
                                    return true;
                                }
                                //goUI.RefreshOpenDesktop();
                            }
                            else
                            {
                            }
                            doRS = null/* TODO Change to default(_) if this is not a reference type */;
                            break;
                        }

                    case "AC_VIEWCONTROLONCHANGE_BTN_REVIEWEDANDSHAREALL":
                        {
                            bUpdateFailed = false;
                            doRS = new clRowSet("AC", 1, "LNK_CreditedTo_US='<%MEID%>' AND CHK_EXTDEVREVIEW=1", goData.GetDefaultSort("AC") + " a", "*");

                            if (doRS.GetFirst() == 1)
                            {
                                do
                                {
                                    doRS.SetFieldVal("CHK_EXTDEVREVIEW", 0, 2);
                                    doRS.SetFieldVal("SI__SHARESTATE", 2, 2);
                                    if (doRS.Commit() == 0)
                                    {
                                        bUpdateFailed = true;
                                        if (goErr.GetLastError("NUMBER") == "E47250")
                                            bNoPerm = true;
                                        else if (goErr.GetLastError("NUMBER") == "E47260")
                                            bReqMissing = true;
                                        else
                                            bError = true;
                                    }

                                    if (doRS.GetNext() == 0)
                                        break;
                                }
                                while (true);// failed b/c of permissions// failed b/c of other errors
                                if (bUpdateFailed)
                                {
                                    //goUI.RefreshOpenDesktop();
                                    if (bError == true & bNoPerm == false)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. Please contact your Selltis administrator.");
                                    else if (bError == false & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared because you do not have permission to edit the record(s).");
                                    else if (bError == true & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. You either do not have permission to edit the remaining records or there are required fields missing." + Constants.vbCrLf + "You should open each record " + "remaining in the view, review mandatory fields and attempt to Save.");
                                    else if (bReqMissing == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. Please open each record " + "remaining in the view, fill out all mandatory fields and Save.");
                                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                                    par_doCallingObject = doForm;
                                    return true;
                                }
                                //goUI.RefreshOpenDesktop();
                            }
                            else
                            {
                            }
                            doRS = null/* TODO Change to default(_) if this is not a reference type */;
                            break;
                        }

                    case "AC_VIEWCONTROLONCHANGE_BTN_REVIEWEDANDSHAREALLCORR":
                        {
                            bUpdateFailed = false;

                            Desktop oDesktop = (Desktop)par_doCallingObject;
                            string sViewID = goUI.GetLastSelected("VIEWPAGEID");
                            string sMeta = oDesktop.GetViewMetadata(sViewID);
                            string sState = oDesktop.GetViewStateMetadata(sViewID);
                            string sViewCondition = goTR.StrRead(sState, "FILTER", "", false); // 8/1/13 Changed lang param

                            doRS = new clRowSet("AC", 1, sViewCondition, goData.GetDefaultSort("AC") + " a", "*");

                            if (doRS.GetFirst() == 1)
                            {
                                do
                                {
                                    doRS.SetFieldVal("CHK_EXTDEVREVIEW", 0, 2);
                                    doRS.SetFieldVal("SI__SHARESTATE", 2, 2);
                                    if (doRS.Commit() == 0)
                                    {
                                        bUpdateFailed = true;
                                        if (goErr.GetLastError("NUMBER") == "E47250")
                                            bNoPerm = true;
                                        else if (goErr.GetLastError("NUMBER") == "E47260")
                                            bReqMissing = true;
                                        else
                                            bError = true;
                                    }

                                    if (doRS.GetNext() == 0)
                                        break;
                                }
                                while (true);// failed b/c of permissions// failed b/c of other errors
                                if (bUpdateFailed)
                                {
                                    //goUI.RefreshOpenDesktop();
                                    if (bError == true & bNoPerm == false)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. Please contact your Selltis administrator.");
                                    else if (bError == false & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared because you do not have permission to edit the record(s).");
                                    else if (bError == true & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. You either do not have permission to edit the remaining records or there are required fields missing." + Constants.vbCrLf + "You should open each record " + "remaining in the view, review mandatory fields and attempt to Save.");
                                    else if (bReqMissing == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. Please open each record " + "remaining in the view, fill out all mandatory fields and Save.");
                                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                                    par_doCallingObject = doForm;
                                    return true;
                                }
                                //goUI.RefreshOpenDesktop();
                            }
                            else
                            {
                            }
                            doRS = null/* TODO Change to default(_) if this is not a reference type */;
                            break;
                        }

                    case "AC_FORMONSAVE_MAILINGLIST":
                        {
                            doForm.oVar.SetVar("AC_MailList_Ran", "1");

                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        // Unlink CNs not on Mailing List
                                        //goScr.RunScript("Activity_RemoveCntNotOnMailList", doForm);
                                        par_doCallingObject = doForm;
                                        scriptManager.RunScript("Activity_RemoveCntNotOnMailList", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                                        doForm = (Form)par_doCallingObject;
                                        break;
                                    }

                                case "NO":
                                    {
                                        // Do nothing; continue save
                                        par_doCallingObject = doForm;
                                        return true;
                                    }

                                case "CANCEL":
                                    {
                                        // Leave form open, cancel save
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "AC_FORMONSAVE_SCRIPTMESSAGES":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        doForm.oVar.SetVar("ScriptMessages", "");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "AP_VIEWCONTROLONCHANGE_BTN_REVIEWEDANDSHAREALL":
                        {
                            bUpdateFailed = false;
                            doRS = new clRowSet("AP", 1, "LNK_Involves_US='<%MEID%>' AND CHK_EXTDEVREVIEW=1", goData.GetDefaultSort("AP") + " a", "*");

                            if (doRS.GetFirst() == 1)
                            {
                                do
                                {
                                    doRS.SetFieldVal("CHK_EXTDEVREVIEW", 0, 2);
                                    doRS.SetFieldVal("SI__SHARESTATE", 2, 2);
                                    if (doRS.Commit() == 0)
                                    {
                                        bUpdateFailed = true;
                                        if (goErr.GetLastError("NUMBER") == "E47250")
                                            bNoPerm = true;
                                        else if (goErr.GetLastError("NUMBER") == "E47260")
                                            bReqMissing = true;
                                        else
                                            bError = true;
                                    }

                                    if (doRS.GetNext() == 0)
                                        break;
                                }
                                while (true);// failed b/c of permissions// failed b/c of other errors
                                if (bUpdateFailed)
                                {
                                    //goUI.RefreshOpenDesktop();
                                    if (bError == true & bNoPerm == false)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. Please try again and contact your Selltis administrator if you are unsuccessful.");
                                    else if (bError == false & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared because you do not have permission to edit the record(s).");
                                    else if (bError == true & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. You either do not have permission to edit the remaining records or there are required fields missing." + Constants.vbCrLf + "You should open each record " + "remaining in the view, review mandatory fields and attempt to Save.");
                                    else if (bReqMissing == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. Please open each record " + "remaining in the view, fill out all mandatory fields and Save.");
                                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                                    par_doCallingObject = doForm;
                                    return true;
                                }
                                //goUI.RefreshOpenDesktop();
                            }
                            else
                            {
                            }
                            doRS = null/* TODO Change to default(_) if this is not a reference type */;
                            break;
                        }

                    case "AP_FORMONSAVE_CHECKFORCONFLICTS":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        // Call save again...this will not check for conflicts b/c var was set
                                        // Cs 8/5/09: Check if we are in 'Save and Leave open' mode. When we were, form would close after
                                        // clicking yes on Appt conflict message.
                                        if (doForm.MessageBoxFormMode == "SAVEANDKEEPOPEN")
                                        {
                                            if (doForm.Save(5) == 0)
                                            {
                                                par_doCallingObject = doForm;
                                                return false;
                                            }
                                        }
                                        else if (doForm.Save(1) == 0)
                                        {
                                            par_doCallingObject = doForm;
                                            return false;
                                        }
                                        break;
                                    }

                                case "NO":
                                    {
                                        // cancel save and stay on form
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "AP_FORMONSAVE_SCRIPTMESSAGES":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        doForm.oVar.SetVar("ScriptMessages", "");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "AC_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMO_Journal"));
                                        // CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                        // Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                        sWork = par_s2;
                                        doForm.oVar.SetVar("JournalWithHardReturns", sWork + Constants.vbCrLf + sJournal);
                                        if (sWork != "")
                                        {
                                            // CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                            // If not defined in WOP, default is 1
                                            if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS")) != "0")
                                            {
                                                sWork = goTR.Replace(sWork, Constants.vbCrLf, (Strings.Chr(32) + Strings.Chr(32) + Strings.Chr(32)).ToString());
                                                doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                            }
                                            else
                                                doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                            doForm.MoveToField("MMO_JOURNAL");
                                        }

                                        break;
                                    }
                            }

                            break;
                        }

                    case "AC_FORMCONTROLONCHANGE_BTN_INSERTLINEMMR":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMR_Journal"));
                                        doForm.doRS.SetFieldVal("MMR_Journal", par_s2 + "<br/>" + sJournal);
                                        doForm.MoveToField("MMR_Journal");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "CN_VIEWCONTROLONCHANGE_BTN_REVIEWEDANDSHAREALL":
                        {
                            bUpdateFailed = false;
                            doRS = new clRowSet("CN", 1, "LNK_Related_US='<%MEID%>' AND CHK_EXTDEVREVIEW=1", goData.GetDefaultSort("CN") + " a", "*");

                            if (doRS.GetFirst() == 1)
                            {
                                do
                                {
                                    doRS.SetFieldVal("CHK_EXTDEVREVIEW", 0, 2);
                                    doRS.SetFieldVal("SI__SHARESTATE", 2, 2);
                                    if (doRS.Commit() == 0)
                                    {
                                        bUpdateFailed = true;
                                        if (goErr.GetLastError("NUMBER") == "E47250")
                                            bNoPerm = true;
                                        else if (goErr.GetLastError("nUMBER") == "E47260")
                                            bReqMissing = true;
                                        else
                                            bError = true;
                                    }

                                    if (doRS.GetNext() == 0)
                                        break;
                                }
                                while (true);// failed b/c of permissions// failed b/c of other errors
                                if (bUpdateFailed)
                                {
                                    //goUI.RefreshOpenDesktop();
                                    if (bError == true & bNoPerm == false)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. Please try again and contact your Selltis administrator if you are unsuccessful.");
                                    else if (bError == false & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared because you do not have permission to edit the record(s).");
                                    else if (bError == true & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. You either do not have permission to edit the remaining records or there are required fields missing." + Constants.vbCrLf + "You should open each record " + "remaining in the view, review mandatory fields and attempt to Save.");
                                    else if (bReqMissing == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. Please open each record " + "remaining in the view, fill out all mandatory fields and Save.");
                                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                                    par_doCallingObject = doForm;
                                    return true;
                                }
                                //goUI.RefreshOpenDesktop();
                            }
                            else
                            {
                            }
                            doRS = null/* TODO Change to default(_) if this is not a reference type */;
                            break;
                        }

                    case "CO_FORMONSAVE_LINKTEAMLEADERMSGBOX":
                        {
                            // The 3 buttons are an answer to the question ""You changed the Team Leader from '" & doForm.oVar.GetVar("TeamLeaderNameOnLoad") & "' to '" & doForm.doRS.GetFieldVal("LNK_TeamLeader_US%%SYS_Name") & "'. Would you like to link the new Team Leader to this Company's Contacts?"
                            // Button labels are:
                            // YES: Yes
                            // NO: Yes, and unlink old Team Leader
                            // CANCEL: No
                            switch (Strings.UCase(par_s1))
                            {
                                case "1":
                                case "2": // Yes, Yes, and unlink old Team Leader
                                    {
                                        // ==> After rowset supports dynamically fetching missing links, put this to the sFields parameter: "NOLINKS,LNK_Related_US,LNK_Related_CO". Test this!
                                        // This will speed up the Contact updates by loading only the links necessary for generateSysname and for SetFieldVals here.
                                        doRS = new clRowSet("CN", clC.SELL_EDIT, "LNK_Related_CO='" + Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")) + "'", "", "*", -1, "", "", "", "", "", true, true);
                                        if (doRS.Count() > 0)
                                        {
                                            sWork = goTR.ExtractString(par_s4, 1, "|");      // User to link (new team leader)
                                            sWork2 = goTR.ExtractString(par_s4, 2, "|");     // User to unlink (original team leader)
                                            do
                                            {
                                                if (Strings.UCase(par_s1) == "2")
                                                    // User clicked the second button: clear the original team leader
                                                    doRS.ClearLink("LNK_Related_US", sWork2);
                                                // Set the new team leader
                                                doRS.SetFieldVal("LNK_Related_US", sWork);
                                                // Try to save the change. if the user doesn't have selective edit perm on the Contact, Commit will fail.
                                                if (doRS.Commit() != 1)
                                                    goErr.SetWarning(35000, sProc, "User '" + sWork + "' couldn't be linked to Contact '" + Convert.ToString(doRS.GetFieldVal("SYS_Name")) + "'.");
                                                if (doRS.GetNext() != 1)
                                                    break;
                                            }
                                            while (true);
                                        }

                                        break;
                                    }

                                default:
                                    {
                                        break;
                                    }
                            }

                            break;
                        }
                    case "CO_LINKED_CN":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES": // Yes, Yes, and unlink old Team Leader
                                    {
                                        // ==> After rowset supports dynamically fetching missing links, put this to the sFields parameter: "NOLINKS,LNK_Related_US,LNK_Related_CO". Test this!
                                        // This will speed up the Contact updates by loading only the links necessary for generateSysname and for SetFieldVals here.
                                        doRS = new clRowSet("CN", clC.SELL_EDIT, "LNK_Related_CO='" + Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")) + "'", "", "*", -1, "", "", "", "", "", true, true);
                                        if (doRS.Count() > 0)
                                        {
                                            doRS.SetFieldVal("TXT_ADDRBUSINESS", doForm.doRS.GetFieldVal("TXT_ADDRMAILING"));
                                            doRS.SetFieldVal("TXT_STATEBUSINESS", doForm.doRS.GetFieldVal("TXT_STATEMAILING"));
                                            doRS.SetFieldVal("TXT_CITYBUSINESS", doForm.doRS.GetFieldVal("TXT_CITYMAILING"));
                                            doRS.SetFieldVal("TXT_ZIPBUSINESS", doForm.doRS.GetFieldVal("TXT_ZIPMAILING"));
                                            doRS.SetFieldVal("LNK_RELATED_DV", doForm.doRS.GetFieldVal("LNK_RELATED_DV%%GID_ID"));
                                            doRS.SetFieldVal("TXT_COMPANYNAMETEXT", doForm.doRS.GetFieldVal("TXT_COMPANYNAME"));
                                            doRS.Commit();
                                            doForm.oVar.SetVar("CO_LINKED_CN", 1);
                                         }

                                        break;
                                    }
                                default:
                                    {
                                        doForm.oVar.SetVar("CO_LINKED_CN", 1);
                                        break;
                                    }
                            }
                            break;
                        }
                    case "CO_FORMONSAVE_SCRIPTMESSAGES":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        doForm.oVar.SetVar("ScriptMessages", "");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "COMPANY_DUPCHECK":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "NO":
                                    {
                                        // doForm.oVar.SetVar("CancelSave", "1") CS 8/28/08 #2
                                        doForm.oVar.SetVar("Company_DupCheck", ""); // CS 8/28/08
                                        break;
                                    }

                                case "YES": // CS 8/28/08; call save
                                    {
                                        // Cs 8/5/09
                                        if (doForm.MessageBoxFormMode == "SAVEANDKEEPOPEN")
                                        {
                                            if (doForm.Save(5) == 0)
                                            {
                                                par_doCallingObject = doForm;
                                                return false;
                                            }
                                        }
                                        else if (doForm.Save(0, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                                        {
                                            par_doCallingObject = doForm;
                                            return false;
                                        }
                                        break;
                                    }
                            }

                            break;
                        }

                    case "CONTACT_NOTESLENGTH":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        doForm.MoveToTab(2);
                                        doForm.MoveToField("MMO_Note");
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "CONTACT_COUNTPHONEFIELDS":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "NO":
                                    {
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "CONTACT_DUPCHECK":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "NO":
                                    {
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "EX_FORMONSAVE_PERSONAL":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        doForm.doRS.SetFieldVal("CHK_REIMBURSABLE", 1, 2);
                                        break;
                                    }

                                case "CANCEL":
                                    {
                                        doForm.oVar.SetVar("bReimbursableValue", 0);
                                        doForm.MoveToField("LNK_RELATED_EA");
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "MS_FORMONSAVE":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "NO":
                                    {
                                        // Reset fields to original values and save
                                        doForm.doRS.SetFieldVal("MMO_NOTES", doForm.oVar.GetVar("MMO_NOTES"));
                                        doForm.doRS.SetFieldVal("TXT_SUBJECT", doForm.oVar.GetVar("TXT_SUBJECT"));
                                        doForm.doRS.SetFieldVal("CHK_Urgent", Convert.ToBoolean(doForm.oVar.GetVar("CHK_Urgent")), 2);
                                        doForm.doRS.SetFieldVal("CHK_FYI", Convert.ToBoolean(doForm.oVar.GetVar("CHK_FYI")), 2);
                                        doForm.doRS.SetFieldVal("CHK_PleaseCallBack", Convert.ToBoolean(doForm.oVar.GetVar("CHK_PleaseCallBack")), 2);
                                        doForm.doRS.SetFieldVal("CHK_PleaseReply", Convert.ToBoolean(doForm.oVar.GetVar("CHK_PleaseReply")), 2);
                                        doForm.doRS.SetFieldVal("CHK_ReturnedYourCall", Convert.ToBoolean(doForm.oVar.GetVar("CHK_ReturnedYourCall")), 2);
                                        doForm.doRS.SetFieldVal("CHK_DocumentAttached", Convert.ToBoolean(doForm.oVar.GetVar("CHK_DocumentAttached")), 2);
                                        if (Convert.ToString(doForm.oVar.GetVar("MS_FormControlOnChange_BTN_Reply")) == "1")
                                        {
                                            //MS_FormControlOnChange_BTN_REPLY(ref doForm);
                                            par_doCallingObject = doForm;
                                            scriptManager.RunScript("MS_FormControlOnChange_BTN_REPLY", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                                            doForm = (Form)par_doCallingObject;
                                        }
                                        break;
                                    }

                                case "YES":
                                    {
                                        // Check if we are coming from clicking the Reply button. If not then do nothing here and just let the Save
                                        // continue
                                        if (Convert.ToString(doForm.oVar.GetVar("MS_FormControlOnChange_BTN_Reply")) == "1")
                                        {
                                            //MS_FormControlOnChange_BTN_REPLY(ref doForm);
                                            par_doCallingObject = doForm;
                                            scriptManager.RunScript("MS_FormControlOnChange_BTN_REPLY", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                                            doForm = (Form)par_doCallingObject;
                                        }
                                        if (Convert.ToString(doForm.oVar.GetVar("MS_FormControlOnChange_BTN_ReplyToAll")) == "1")
                                        {
                                            //MS_FormControlOnChange_BTN_REPLYTOALL(ref doForm);
                                            par_doCallingObject = doForm;
                                            scriptManager.RunScript("MS_FormControlOnChange_BTN_ReplyToAll", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                                            doForm = (Form)par_doCallingObject;
                                        }
                                        if (Convert.ToString(doForm.oVar.GetVar("MS_FormControlOnChange_BTN_Forward")) == "1")
                                        {
                                            //MS_FormControlOnChange_BTN_FORWARD(ref doForm);
                                            par_doCallingObject = doForm;
                                            scriptManager.RunScript("MS_FormControlOnChange_BTN_Forward", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                                            doForm = (Form)par_doCallingObject;
                                        }
                                        break;
                                    }
                            }

                            break;
                        }

                    case "OP_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMO_Journal"));
                                        // CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                        // Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                        sWork = par_s2;
                                        doForm.oVar.SetVar("JournalWithHardReturns", sWork + Constants.vbCrLf + sJournal);
                                        if (sWork != "")
                                        {
                                            // CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                            // If not defined in WOP, default is 1
                                            if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS")) != "0")
                                            {
                                                sWork = goTR.Replace(sWork, Constants.vbCrLf, (Strings.Chr(32) + Strings.Chr(32) + Strings.Chr(32)).ToString());
                                                doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                            }
                                            else
                                                doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                            doForm.MoveToField("MMO_JOURNAL");
                                        }

                                        break;
                                    }
                            }

                            break;
                        }

                    case "OP_FORMCONTROLONCHANGE_BTN_INSERTLINEMMR":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMR_Journal"));
                                        doForm.doRS.SetFieldVal("MMR_Journal", par_s2 + "<br/>" + sJournal);
                                        doForm.MoveToField("MMR_Journal");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "OP_FORMCONTROLONCHANGE_BTN_INSERTPRESALE":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        //goScr.RunScript("AddTextToField", doForm, , "MMO_QUESTIONNAIRE", doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%PRESALEQUEST"), "PREPEND");
                                        par_doCallingObject = doForm;
                                        scriptManager.RunScript("AddTextToField", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "MMO_QUESTIONNAIRE", Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%PRESALEQUEST")), "PREPEND");
                                        doForm = (Form)par_doCallingObject;
                                        break;
                                    }
                            }

                            break;
                        }

                    case "OP_FORMONSAVE_NADATE":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        // doForm.MoveToTab(1)
                                        // doForm.MoveToField("DTE_NEXTACTIONDATE")
                                        doForm.doRS.SetFieldVal("DTE_NEXTACTIONDATE", "2 weeks from today");
                                        break;
                                    }

                                case "NO":
                                    {
                                        break;
                                    }
                            }

                            break;
                        }

                    case "OP_FORMONSAVE_SCRIPTMESSAGES":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        doForm.oVar.SetVar("ScriptMessages", "");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "PR_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMO_Journal"));
                                        // CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                        // Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                        sWork = par_s2;
                                        doForm.oVar.SetVar("JournalWithHardReturns", sWork + Constants.vbCrLf + sJournal);
                                        if (sWork != "")
                                        {
                                            // CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                            // If not defined in WOP, default is 1
                                            if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS")) != "0")
                                            {
                                                sWork = goTR.Replace(sWork, Constants.vbCrLf, (Strings.Chr(32) + Strings.Chr(32) + Strings.Chr(32)).ToString());
                                                doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                            }
                                            else
                                                doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                            doForm.MoveToField("MMO_JOURNAL");
                                        }

                                        break;
                                    }
                            }

                            break;
                        }

                    case "PR_FORMCONTROLONCHANGE_BTN_INSERTLINEMMR":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMR_Journal"));
                                        doForm.doRS.SetFieldVal("MMR_Journal", par_s2 + "<br/>" + sJournal);
                                        doForm.MoveToField("MMR_Journal");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "PR_FORMCONTROLONCHANGE_BTN_PROFILEPROMPTS":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        // doForm.doRS.SetFieldVal("MMO_Profile", doForm.doRS.GetFieldVal("MMO_Profile") & vbCrLf & vbCrLf & doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%PROJPROFILE"))
                                        //goScr.RunScript("AddTextToField", doForm, , "MMO_PROFILE", doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%PROJPROFILE"), "PREPEND");
                                        par_doCallingObject = doForm;
                                        scriptManager.RunScript("AddTextToField", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "MMO_PROFILE", Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%PROJPROFILE")), "PREPEND");
                                        doForm = (Form)par_doCallingObject;
                                        break;
                                    }
                            }

                            break;
                        }

                    case "PR_FORMONSAVE_SCRIPTMESSAGES":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        doForm.oVar.SetVar("ScriptMessages", "");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "QL_FORMCONTROLONCHANGE_BTN_INSERTSPEC":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        //goScr.RunScript("AddTextToField", doForm, , "MMO_DETAILS", doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_SPECIFICATIONS"), "PREPEND");
                                        par_doCallingObject = doForm;
                                        scriptManager.RunScript("AddTextToField", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "MMO_DETAILS", Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_SPECIFICATIONS")), "PREPEND");
                                        doForm = (Form)par_doCallingObject;
                                        break;
                                    }
                            }

                            break;
                        }

                    case "QL_FORMONSAVE_SHARED":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        doForm.doRS.SetFieldVal("SI__SHARESTATE", 1, 2);
                                        break;
                                    }

                                case "CANCEL":
                                    {
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "QT_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMO_Journal"));
                                        // CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                        // Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                        sWork = par_s2;
                                        doForm.oVar.SetVar("JournalWithHardReturns", sWork + Constants.vbCrLf + sJournal);
                                        if (sWork != "")
                                        {
                                            // CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                            // If not defined in WOP, default is 1
                                            if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS")) != "0")
                                            {
                                                sWork = goTR.Replace(sWork, Constants.vbCrLf, (Strings.Chr(32) + Strings.Chr(32) + Strings.Chr(32)).ToString());
                                                doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                            }
                                            else
                                                doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                            doForm.MoveToField("MMO_JOURNAL");
                                        }

                                        break;
                                    }
                            }

                            break;
                        }

                    case "QT_FORMCONTROLONCHANGE_BTN_INSERTLINEMMR":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMR_Journal"));
                                        doForm.doRS.SetFieldVal("MMR_Journal", par_s2 + "<br/>" + sJournal);
                                        doForm.MoveToField("MMR_Journal");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "QT_FORMCONTROLONCHANGE_BTN_INSERTPRESALE":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        //goScr.RunScript("AddTextToField", doForm, , "MMO_QUESTIONNAIRE", doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%PRESALEQUEST"), "PREPEND");
                                        par_doCallingObject = doForm;
                                        scriptManager.RunScript("AddTextToField", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "MMO_QUESTIONNAIRE", Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%PRESALEQUEST")), "PREPEND");
                                        doForm = (Form)par_doCallingObject;
                                        doForm.MoveToTab(9);     // Questionnaire
                                        doForm.MoveToField("MMO_QUESTIONNAIRE");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "QT_FORMCONTROLONCHANGE_BTN_INSERTTEXTBLOCK":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        //goScr.RunScript("AddTextToField", doForm, , "MMO_CoverLetter", doForm.doRS.GetFieldVal("LNK_TEMPLATE_DO%%MMO_NOTES"), "PREPEND");
                                        par_doCallingObject = doForm;
                                        scriptManager.RunScript("AddTextToField", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "MMO_CoverLetter", Convert.ToString(doForm.doRS.GetFieldVal("LNK_TEMPLATE_DO%%MMO_NOTES")), "PREPEND");
                                        doForm = (Form)par_doCallingObject;
                                        doForm.doRS.ClearLinkAll("LNK_Template_DO");
                                        doForm.MoveToTab(6);
                                        doForm.MoveToField("MMO_CoverLetter");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "QT_FORMCONTROLONCHANGE_BTN_INSERTTEXTBLOCK_1":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        //goScr.RunScript("AddTextToField", doForm, , "MMO_Closing", doForm.doRS.GetFieldVal("LNK_TEMPLATE_DO%%MMO_NOTES"), "PREPEND");
                                        par_doCallingObject = doForm;
                                        scriptManager.RunScript("AddTextToField", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "MMO_Closing", Convert.ToString(doForm.doRS.GetFieldVal("LNK_TEMPLATE_DO%%MMO_NOTES")), "PREPEND");
                                        doForm = (Form)par_doCallingObject;
                                        doForm.doRS.ClearLinkAll("LNK_Template_DO");
                                        doForm.MoveToTab(7);
                                        doForm.MoveToField("MMO_Closing");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "QT_FORMONSAVE_QLSTATUS":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        // User clicked Yes to update Quote line status with Quote status
                                        goP.SetVar("UseQTStatus", "1");
                                        doForm.oVar.SetVar("QT_CheckStatus_Ran", "1");
                                        break;
                                    }

                                case "NO":
                                    {
                                        // Uncheck checkbox that keeps QL status same as QT
                                        doForm.doRS.SetFieldVal("CHK_UPDQLSTATUS", 0, 2);
                                        goP.SetVar("USEQTStatus", "");
                                        doForm.oVar.SetVar("QT_CheckStatus_Ran", "1");
                                        break;
                                    }

                                case "CANCEL":
                                    {
                                        goP.SetVar("USEQtStatus", "");
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "QT_FORMONSAVE_QUOTESTATUS": // QL's have status of open and QT is not open
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "QT_FORMONSAVE_SCRIPTMESSAGES":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        doForm.oVar.SetVar("ScriptMessages", "");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "SHAREITEM":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        // Share the record
                                        doForm.doRS.SetFieldVal("SI__SHARESTATE", 2, 2);
                                        doForm.doRS.SetFieldVal("CHK_EXTDEVREVIEW", 0, 2);
                                        break;
                                    }
                            }

                            break;
                        }

                    case "ST_FORMONSAVE_TOTAL":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "NO":
                                    {
                                        doForm.MoveToField("SR__STATERATE");
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "TD_VIEWCONTROLONCHANGE_BTN_REVIEWEDANDSHAREALL":
                        {
                            bUpdateFailed = false;
                            doRS = new clRowSet("TD", 1, "(LNK_AssignedTo_US='<%MEID%>' OR LNK_CreatedBy_US='<%MEID%>') AND CHK_EXTDEVREVIEW=1", goData.GetDefaultSort("TD") + " a", "*");

                            if (doRS.GetFirst() == 1)
                            {
                                do
                                {
                                    doRS.SetFieldVal("CHK_EXTDEVREVIEW", 0, 2);
                                    doRS.SetFieldVal("SI__SHARESTATE", 2, 2);
                                    if (doRS.Commit() == 0)
                                    {
                                        bUpdateFailed = true;
                                        if (goErr.GetLastError("NUMBER") == "E47250")
                                            bNoPerm = true;
                                        else if (goErr.GetLastError("NUMBER") == "E47260")
                                            bReqMissing = true;
                                        else
                                            bError = true;
                                    }

                                    if (doRS.GetNext() == 0)
                                        break;
                                }
                                while (true);// failed b/c of permissions// failed b/c of other errors
                                if (bUpdateFailed)
                                {
                                    //goUI.RefreshOpenDesktop();
                                    if (bError == true & bNoPerm == false)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. Please try again and contact your Selltis administrator if you are unsuccessful.");
                                    else if (bError == false & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared because you do not have permission to edit the record(s).");
                                    else if (bError == true & bNoPerm == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. You either do not have permission to edit the remaining records or there are required fields missing." + Constants.vbCrLf + "You should open each record " + "remaining in the view, review mandatory fields and attempt to Save.");
                                    else if (bReqMissing == true)
                                        doForm.MessageBox("Some records couldn't be marked as reviewed/shared. Please open each record " + "remaining in the view, fill out all mandatory fields and Save.");
                                    doRS = null/* TODO Change to default(_) if this is not a reference type */;

                                    par_doCallingObject = doForm;
                                    return true;
                                }
                                //goUI.RefreshOpenDesktop();
                            }
                            else
                            {
                            }
                            doRS = null/* TODO Change to default(_) if this is not a reference type */;
                            break;
                        }

                    case "TD_FORMONSAVE_SCRIPTMESSAGES":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        doForm.oVar.SetVar("ScriptMessages", "");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "USERREASSIGN_FORMCONTROLONCHANGE_BTN_START":
                        {
                            switch (Strings.UCase(par_s1))
                            {
                                case "CLOSE":
                                    {
                                        doForm.CloseOnReturn = true;
                                        break;
                                    }
                            }

                            break;
                        }
                }
            }
            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                {
                    goErr.SetError(ex, 45105, sProc);
                    par_doCallingObject = doForm;
                    return false;
                }
            }

            par_doCallingObject = doForm;
            return true;
        }

        // SGR TKT#:987 03042016
        public bool MS_FormControlOnChange_BTN_REPLY(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Dim sObjectPassed As String = par_doCallingObject.GetType.ToString
            Form doObject = (Form)par_doCallingObject;
            // Dim doNewForm As Form
            string sID;
            bool bNoAddPerm = false;

            // Check if have add permissions
            if (goData.GetAddPermission("MS") == false)
                bNoAddPerm = true;

            sID = Convert.ToString(doObject.doRS.GetFieldVal("GID_ID"));
            if (bNoAddPerm == true)
            {
                doObject.MessageBox("You cannot reply to this Message because you don't have permission to create Messages.");
                par_doCallingObject = doObject;
                return false;
            }
            doObject.oVar.SetVar("MS_FormControlOnChange_BTN_Reply", "1"); // set var to know we are coming from reply button so that when
                                                                           // save is called we can run this script again if save fails for any reason
            if (doObject.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
            {
                par_doCallingObject = doObject;
                return false;
            }
            // CS 12/9/11: Changed below
            // doNewForm = doObject.CreateForm("MS", sID, "MESSAGEREPLY")
            // doNewForm.OpenForm()
            doObject.oVar.SetVar("MS_FormControlOnChange_BTN_Reply", "");
            Form doNewForm2 = new Form("MS", sID, "CRL_MS:REPLY");
            goUI.Queue("FORM", doNewForm2);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));

            par_doCallingObject = doObject;
            return true;
        }

        // SGR TKT#:987 03042016
        public bool MS_FormControlOnChange_BTN_REPLYTOALL(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Dim sObjectPassed As String = par_doCallingObject.GetType.ToString
            Form doObject = (Form)par_doCallingObject;
            // Dim doNewForm As Form
            string sID;
            // Dim doToUser As Array
            // Dim lUserCount As Long
            // Dim sUsersToLink As String
            bool bNoAddPerm = false;

            // Check if have add perm
            if (goData.GetAddPermission("MS") == false)
                bNoAddPerm = true;

            doObject.oVar.SetVar("MS_FormControlOnChange_BTN_ReplyToAll", "1"); // set var to know we are coming from reply button so that when
                                                                                // save is called we can run this script again if save fails for any reason
            sID = Convert.ToString(doObject.doRS.GetFieldVal("GID_ID"));
            if (bNoAddPerm == true)
            {
                doObject.MessageBox("You cannot reply to this message because you don't have permission to create Messages.");
                {
                    par_doCallingObject = doObject;
                    return false;
                }
            }
            if (doObject.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
            {
                par_doCallingObject = doObject;
                return false;
            }
            // **test
            Form doNewForm2 = new Form("MS", sID, "CRL_MS:REPLYTOALL");
            goUI.Queue("FORM", doNewForm2);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));


            // CS 12/9/11 doNewForm = doObject.CreateForm("MS", sID, "MESSAGEREPLYTOALL")
            // doNewForm = doObject.CreateForm("MS", sID, "REPLYTOALL")

            // CS 12/9/11: We can now set the NDB_LNK_TO_US field via the CRL_MS:ReplyToAll MD.
            // Comment fix below.
            // '4/18/07 This is a temp fix for now
            // 'If there is only 1 CC ID then set the Ndb field to the creator of the msg
            // doToUser = Split(doObject.doRS.GetFieldVal("MMO_CCIDs"), vbCrLf)
            // lUserCount = doToUser.Length 'CS 11/19/10: Don't need to subtract one. If there are 2 users in MMO CCIDs, then the len is 2. " - 1"
            // If lUserCount > 1 Then
            // sUsersToLink = doObject.doRS.GetFieldVal("LNK_Createdby_US") & vbCrLf & doObject.doRS.GetFieldVal("MMO_CCIDs")
            // doNewForm.SetControlVal("NDB_LNK_TO_US", sUsersToLink, "True") '& doForm.doRs.GetFieldVal("LNK_Createdby_US"))

            // Else
            // doNewForm.SetControlVal("NDB_LNK_TO_US", doObject.doRS.GetFieldVal("LNK_Createdby_US"))
            // End If
            // doNewForm.OpenForm()
            // doObject.oVar.SetVar("MS_FormControlOnChange_BTN_ReplyToAll", "")

            par_doCallingObject = doObject;
            return true;
        }

        // SGR TKT#:987 03042016
        public bool MS_FormControlOnChange_BTN_FORWARD(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Dim sObjectPassed As String = par_doCallingObject.GetType.ToString
            Form doObject = (Form)par_doCallingObject;
            Form doNewForm;
            string sID;
            bool bNoAddPerm = false;
            bool bNoEditPerm = false;

            // Check if have add perm
            if (goData.GetAddPermission("MS") == false)
                bNoAddPerm = true;

            // Check for add perm
            if (bNoAddPerm == true)
            {
                doObject.MessageBox("You cannot forward this Message because you don't have permission to add Messages.");
                par_doCallingObject = doObject;
                return false;
            }
            doObject.oVar.SetVar("MS_FormControlOnChange_BTN_Forward", "1"); // set var to know we are coming from reply button so that when
                                                                             // save is called we can run this script again if save fails for any reason
            sID = Convert.ToString(doObject.doRS.GetFieldVal("GID_ID"));
            if (doObject.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
            {
                par_doCallingObject = doObject;
                return false;
            }
            doNewForm = doObject.CreateForm("MS", sID, "MESSAGEFORWARD");

            // CS 07062015 Copy any attachments to the new form if they exist
            if (goData.IsFieldValid("MS", "ADR_ATTACHMENTS"))
            {
                if (Convert.ToString(doObject.doRS.GetFieldVal("ADR_ATTACHMENTS")) != "")
                {
                    clAttachments objclAttachments = new clAttachments();
                    objclAttachments.CopyAttachments(sID, Convert.ToString(doNewForm.doRS.GetFieldVal("GID_ID")));
                    doNewForm.doRS.SetFieldVal("ADR_ATTACHMENTS", doObject.doRS.GetFieldVal("ADR_ATTACHMENTS"));
                }
            }

            doNewForm.OpenForm();
            doObject.oVar.SetVar("MS_FormControlOnChange_BTN_Forward", "");

            par_doCallingObject = doObject;
            return true;
        }

        // SGR TKT#:987 03042016
        public bool AC_ViewControlOnChange_BTN_CORR(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 8/14/09 Removed sig, added vbcrlf between above sig and below sig
            // par_doCallingObject: Desktop object containing the view that called this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // MI 1/11/07 started implementing sending

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Dim sObjectPassed As String = par_doCallingObject.GetType.ToString
            Desktop doDesktop = null/* TODO Change to default(_) if this is not a reference type */;
            Form doNewForm;
            clRowSet doRS = null/* TODO Change to default(_) if this is not a reference type */;
            int iType;
            string sID;
            string sURL;
            string sName = "";
            bool bNoAddPerm = false;
            bool bNoEditPerm = false;

            // Buttons in views: 'Reply and Complete' in Corr In Box My and 'Send Now' in Corr My Unsent

            // Already clicked yes to publish/send the WP Activity
            if (Strings.UCase(par_s1) == "YES")
                goto Publish;

            // ==> There is no form in this context. What's supposed to happen here? I guess simply 'nothing'.
            if (Strings.UCase(par_s1) == "NO")
                // user said no to publish question. save and close form.
                // If doForm.Save(1) = 0 Then
                return false;

            if (Strings.UCase(par_s1) == "CANCEL")
                // user said cancel to publish question so leave form open
                return false;

            // Check for add permissions
            if (goData.GetAddPermission("AC") == false)
                bNoAddPerm = true;

            sID = goUI.GetLastSelected("SELECTEDRECORDID");
            // we don't have a form object but have a desktop object being passed
            doDesktop = (Desktop)par_doCallingObject;
            if (sID == "" | sID == null)
            {
                doDesktop.MessageBox(ref par_doCallingObject, "Please select a record.");
                return true;
            }
            doRS = new clRowSet("AC", 1, "GID_ID = '" + sID + "'", "", "*");
            // Check for edit perms
            if (goData.GetRecordPermission(Convert.ToString(doRS.GetFieldVal("GID_ID")), "E") == false)
                bNoEditPerm = true;
            iType = Convert.ToInt32(doRS.GetFieldVal("MLS_Type", 2));

            switch (iType)
            {
                case 3: // Letter Rec
                    {

                        // Check if have permission to create new AC
                        if (bNoAddPerm == true)
                        {
                            doDesktop.MessageBox(ref par_doCallingObject, "You cannot create a reply because you don't have permission to create new Activities.");
                            return false;
                        }
                        // Check if have permission to edit rec
                        if (bNoEditPerm == true)
                        {
                            doDesktop.MessageBox(ref par_doCallingObject, "You do not have permissions to edit this Activity so cannot reply to it.");
                            return false;
                        }
                        doRS.SetFieldVal("MLS_Status", 1, 2);
                        if (doRS.Commit() == 0)
                        {
                            goErr.SetError(35000, sProc, "Marking Activity completed failed.");
                            return false;
                        }
                        doNewForm = new Form("AC", sID, "CRL_AC:LETTERREPLY", goUI.GetLastSelected("LASTOPENDESKTOPGUID"));

                        // Get value of letter field after CRL values are in and will append to signature values in Letter field
                        string sLetter = Convert.ToString(doNewForm.doRS.GetFieldVal("MMO_Letter"));
                        doNewForm.doRS.SetFieldVal("MMO_Letter", Constants.vbCrLf + Constants.vbCrLf + Convert.ToString(doNewForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRABOVESIGNATURE")));
                        // Get Empl Info from the Preferences item
                        doNewForm.doRS.SetFieldVal("MMO_Letter", doNewForm.doRS.GetFieldVal("MMO_Letter") + Constants.vbCrLf + Constants.vbCrLf + Convert.ToString(doNewForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE")));
                        doNewForm.doRS.SetFieldVal("MMO_Letter", doNewForm.doRS.GetFieldVal("MMO_Letter") + sLetter);

                        if (Strings.Left(Convert.ToString(doRS.GetFieldVal("TXT_SUBJ")).ToUpper(), 3) != "RE:")
                            doNewForm.doRS.SetFieldVal("TXT_SUBJ", "RE: " + Convert.ToString(doRS.GetFieldVal("TXT_Subj")));
                        else
                            doNewForm.doRS.SetFieldVal("TXT_SUBJ", doRS.GetFieldVal("TXT_Subj"));
                        goUI.Queue("FORM", doNewForm);
                        //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
                        break;
                    }

                case 7: // Fax rec
                    {

                        // Check if have permission to create new AC
                        if (bNoAddPerm == true)
                        {
                            doDesktop.MessageBox(ref par_doCallingObject, "You cannot create a reply because you don't have permission to create new Activities.");
                        }
                        // Check if have permission to edit recs
                        if (bNoEditPerm == true)
                        {
                            doDesktop.MessageBox(ref par_doCallingObject, "You do not have permissions to edit this Activity so cannot reply to it.");
                            return false;
                        }
                        // coming from view toolbar button
                        doRS.SetFieldVal("MLS_Status", 1, 2);
                        if (doRS.Commit() == 0)
                        {
                            goErr.SetError(35000, sProc, "Marking Activity completed failed.");
                            return false;
                        }
                        doNewForm = new Form("AC", sID, "CRL_AC:FAXREPLY", goUI.GetLastSelected("LASTOPENDESKTOPGUID"));

                        // Get value of letter field after CRL values are in and will append to signature values in Letter field
                        string sLetter = Convert.ToString(doNewForm.doRS.GetFieldVal("MMO_Letter"));
                        doNewForm.doRS.SetFieldVal("MMO_Letter", Constants.vbCrLf + Constants.vbCrLf + Convert.ToString(doNewForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRABOVESIGNATURE")));
                        // Get Empl Info from the Preferences item
                        doNewForm.doRS.SetFieldVal("MMO_Letter", Convert.ToString(doNewForm.doRS.GetFieldVal("MMO_Letter")) + Constants.vbCrLf + Constants.vbCrLf + Convert.ToString(doNewForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE")));
                        doNewForm.doRS.SetFieldVal("MMO_Letter", Convert.ToString(doNewForm.doRS.GetFieldVal("MMO_Letter")) + sLetter);

                        if (Strings.Left(Convert.ToString(doRS.GetFieldVal("TXT_SUBJ")).ToUpper(), 3) != "RE:")
                            doNewForm.doRS.SetFieldVal("TXT_SUBJ", "RE: " + Convert.ToString(doRS.GetFieldVal("TXT_Subj")));
                        else
                            doNewForm.doRS.SetFieldVal("TXT_SUBJ", doRS.GetFieldVal("TXT_Subj"));
                        goUI.Queue("FORM", doNewForm);
                        //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
                        break;
                    }

                case 5: // email rec
                    {
                        // coming from view toolbar button
                        // Check if have permission to create new AC
                        if (bNoAddPerm == true)
                        {
                            doDesktop.MessageBox(ref par_doCallingObject, "You cannot create a reply because you don't have permission to create new Activities.");
                            return false;
                        }
                        // Check if have permission to edit recs
                        if (bNoEditPerm == true)
                        {
                            doDesktop.MessageBox(ref par_doCallingObject, "You do not have permissions to edit this Activity so cannot reply to it.");
                            return false;
                        }
                        doRS.SetFieldVal("MLS_Status", 1, 2);
                        if (doRS.Commit() == 0)
                        {
                            goErr.SetError(35000, sProc, "Marking Activity completed failed.");
                            return false;
                        }
                        doNewForm = new Form("AC", sID, "CRL_AC:EMAILREPLY", goUI.GetLastSelected("LASTOPENDESKTOPGUID"));

                        // Get value of letter field after CRL values are in and will append to signature values in Letter field
                        string sLetter = Convert.ToString(doNewForm.doRS.GetFieldVal("MMO_Letter"));
                        doNewForm.doRS.SetFieldVal("MMO_Letter", Constants.vbCrLf + Constants.vbCrLf + doNewForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRABOVESIGNATURE"));
                        // Get Empl Info from the Preferences item
                        doNewForm.doRS.SetFieldVal("MMO_Letter", doNewForm.doRS.GetFieldVal("MMO_Letter") + Constants.vbCrLf + Constants.vbCrLf + doNewForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                        doNewForm.doRS.SetFieldVal("MMO_Letter", doNewForm.doRS.GetFieldVal("MMO_Letter") + sLetter);

                        if (Strings.Left(Convert.ToString(doRS.GetFieldVal("TXT_SUBJ")).ToUpper(), 3) != "RE:")
                            doNewForm.doRS.SetFieldVal("TXT_SUBJ", "RE: " + Convert.ToString(doRS.GetFieldVal("TXT_Subj")));
                        else
                            doNewForm.doRS.SetFieldVal("TXT_SUBJ", doRS.GetFieldVal("TXT_Subj"));
                        goUI.Queue("FORM", doNewForm);
                        //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
                        break;
                    }

                case 4:
                case 6:
                case 8: // Letter, Email, Fax sent
                    {
                        // Open send wizard

                        if (bNoEditPerm == true)
                        {
                            doDesktop.MessageBox(ref par_doCallingObject, "You do not have permission to edit this Activity so cannot send it.");
                            return true;
                        }
                        if (goP.GetProduct() == "MB")
                            sURL = "../Pages/Mobile_DiaSend.aspx";
                        else
                            sURL = "../Pages/diaSend.aspx";
                        sName = Convert.ToString(doRS.GetFieldVal("SYS_NAME"));
                        // objectotsend: VIEW, RECORD, CORR
                        goTR.URLWrite(ref sURL, "objecttosend", "CORR");
                        // Send type: EMAIL, FAX, LETTER, or WPRESPONSE
                        switch (iType)
                        {
                            case 4:
                                {
                                    goTR.URLWrite(ref sURL, "sendtype", "LETTER");
                                    break;
                                }

                            case 6:
                                {
                                    goTR.URLWrite(ref sURL, "sendtype", "EMAIL");
                                    break;
                                }

                            case 8:
                                {
                                    goTR.URLWrite(ref sURL, "sendtype", "FAX");
                                    break;
                                }
                        }
                        goTR.URLWrite(ref sURL, "objectid", sID);
                        goTR.URLWrite(ref sURL, "objectname", sName);
                        // goUI.SetNext("DIALOG", sURL)
                        //HttpContext.Current.Response.Redirect(goUI.Navigate("DIALOG", sURL));
                        break;
                    }
            }
            return true;

        Publish:
            ;
            // WP Submission Sent Act that did not have publish checked. User clicked yes to check publish and send.

            // clicked Send view toolbar button from Corr My Unsent view
            sID = goUI.GetLastSelected("SELECTEDRECORDID");
            doRS = new clRowSet("AC", 1, "GID_ID = '" + sID + "'", "", "*");
            doRS.SetFieldVal("CHK_PUBLISH", 1, clC.SELL_SYSTEM);
            doRS.SetFieldVal("CHK_SENT", 1, clC.SELL_SYSTEM);
            if (doRS.Commit() == 0)
            {
            }

            return true;
        }

        // SGR TKT#:987 03042016
        public bool AC_FormControlOnChange_BTN_CORR(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 8/14/09 Removed sig, added vbcrlf between above sig and below sig
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // Dim sObjectPassed As String = par_doCallingObject.GetType.ToString
            Form doForm = (Form)par_doCallingObject;
            Desktop doDesktop = null/* TODO Change to default(_) if this is not a reference type */;
            Form doNewForm;
            int iType;
            string sID;
            string sURL;
            string sName = "";
            bool bNoAddPerm = false;
            bool bNoEditPerm = false;

            // This button is on the AC form. The label changes depending on the type of AC. For example it will say
            // Send' if an Email Sent type AC but 'Reply' if an Email Received.

            // Already clicked yes to publish/send the WP Activity
            if (Strings.UCase(par_s1) == "YES")
                goto Publish;

            if (Strings.UCase(par_s1) == "NO")
            {
                // user said no to publish question. save and close form.
                if (doForm.Save(1) == 0)
                {
                    par_doCallingObject = doForm;
                    return false;
                }
            }

            if (Strings.UCase(par_s1) == "CANCEL")
            {
                // user said cancel to publish question so leave form open
                par_doCallingObject = doForm;
                return false;
            }

            // Check for add permissions
            if (goData.GetAddPermission("AC") == false)
                bNoAddPerm = true;

            iType = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_TYPE", 2));
            sID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));
            sName = Convert.ToString(doForm.doRS.GetFieldVal("SYS_NAME"));

            switch (iType)
            {
                case 3: // Letter Rec
                    {

                        // Check if have permission to create new AC
                        if (bNoAddPerm == true)
                        {
                            doForm.MessageBox("You cannot create a reply because you don't have permission to create new Activities.");
                            par_doCallingObject = doForm;
                            return false;
                        }
                        // 'Check if have permission to edit recs
                        // If goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID"), "E") = 0 Then
                        // doForm.MessageBox("You do not have permissions to mark this Activity as completed." & vbCrLf & "Use the Reply without completing button instead.")
                        // Return False
                        // End If
                        doForm.doRS.SetFieldVal("MLS_Status", 1, 2);
                        if (doForm.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                        {
                            par_doCallingObject = doForm;
                            return false;
                        }
                        doNewForm = doForm.CreateForm("AC", sID, "LETTERREPLY");

                        // Get value of letter field after CRL values are in and will append to signature values in Letter field
                        string sLetter = Convert.ToString(doNewForm.doRS.GetFieldVal("MMO_Letter"));
                        doNewForm.doRS.SetFieldVal("MMO_Letter", Constants.vbCrLf + Constants.vbCrLf + Convert.ToString(doNewForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRABOVESIGNATURE")));
                        // Get Empl Info from the Preferences item
                        doNewForm.doRS.SetFieldVal("MMO_Letter", doNewForm.doRS.GetFieldVal("MMO_Letter") + Constants.vbCrLf + Constants.vbCrLf + Convert.ToString(doNewForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE")));
                        doNewForm.doRS.SetFieldVal("MMO_Letter", doNewForm.doRS.GetFieldVal("MMO_Letter") + sLetter);

                        if (Strings.Left(Convert.ToString(doForm.doRS.GetFieldVal("TXT_SUBJ")).ToUpper(), 3) != "RE:")
                            doNewForm.doRS.SetFieldVal("TXT_SUBJ", "RE: " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_Subj")));
                        else
                            doNewForm.doRS.SetFieldVal("TXT_SUBJ", Convert.ToString(doForm.doRS.GetFieldVal("TXT_Subj")));
                        doNewForm.OpenForm();
                        break;
                    }

                case 7: // Fax rec
                    {

                        // Check if have permission to create new AC
                        if (bNoAddPerm == true)
                        {
                            doForm.MessageBox("You cannot create a reply because you don't have permission to create new Activities.");
                            par_doCallingObject = doForm;
                            return false;
                        }
                        // 'Check if have permission to edit recs
                        // If goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID"), "E") = 0 Then
                        // doForm.MessageBox("You do not have permissions to mark this Activity as completed." & vbCrLf & "Use the Reply button instead.")
                        // Return False
                        // End If
                        doForm.doRS.SetFieldVal("MLS_Status", 1, 2);
                        if (doForm.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                        {
                            par_doCallingObject = doForm;
                            return false;
                        }
                        doNewForm = doForm.CreateForm("AC", sID, "FAXREPLY");

                        // Get value of letter field after CRL values are in and will append to signature values in Letter field
                        string sLetter = Convert.ToString(doNewForm.doRS.GetFieldVal("MMO_Letter"));
                        doNewForm.doRS.SetFieldVal("MMO_Letter", Constants.vbCrLf + Constants.vbCrLf + Convert.ToString(doNewForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRABOVESIGNATURE")));
                        // Get Empl Info from the Preferences item
                        doNewForm.doRS.SetFieldVal("MMO_Letter", doNewForm.doRS.GetFieldVal("MMO_Letter") + Constants.vbCrLf + Constants.vbCrLf + Convert.ToString(doNewForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE")));
                        doNewForm.doRS.SetFieldVal("MMO_Letter", doNewForm.doRS.GetFieldVal("MMO_Letter") + sLetter);

                        if (Strings.Left(Convert.ToString(doForm.doRS.GetFieldVal("TXT_SUBJ")).ToUpper(), 3) != "RE:")
                            doNewForm.doRS.SetFieldVal("TXT_SUBJ", "RE: " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_Subj")));
                        else
                            doNewForm.doRS.SetFieldVal("TXT_SUBJ", doForm.doRS.GetFieldVal("TXT_Subj"));
                        doNewForm.OpenForm();
                        break;
                    }

                case 5: // email rec
                    {
                        // Check if have permission to create new AC
                        if (bNoAddPerm == true)
                        {
                            doForm.MessageBox("You cannot create a reply because you don't have permission to create new Activities.");
                            par_doCallingObject = doForm;
                            return false;
                        }
                        // 'Check if have permission to edit recs
                        // If goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID"), "E") = 0 Then
                        // doForm.MessageBox("You do not have permissions to mark this Activity as completed." & vbCrLf & "Use the Reply button instead.")
                        // Return False
                        // End If
                        doForm.doRS.SetFieldVal("MLS_Status", 1, 2);
                        if (doForm.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                        {
                            par_doCallingObject = doForm;
                            return false;
                        }
                        doNewForm = doForm.CreateForm("AC", sID, "EMAILREPLY");

                        // Get value of letter field after CRL values are in and will append to signature values in Letter field
                        string sLetter = Convert.ToString(doNewForm.doRS.GetFieldVal("MMO_Letter"));
                        doNewForm.doRS.SetFieldVal("MMO_Letter", Constants.vbCrLf + Constants.vbCrLf + Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRABOVESIGNATURE")));
                        // Get Empl Info from the Preferences item
                        doNewForm.doRS.SetFieldVal("MMO_Letter", Convert.ToString(doNewForm.doRS.GetFieldVal("MMO_Letter")) + Constants.vbCrLf + Constants.vbCrLf + Convert.ToString(doNewForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE")));
                        doNewForm.doRS.SetFieldVal("MMO_Letter", Convert.ToString(doNewForm.doRS.GetFieldVal("MMO_Letter")) + sLetter);

                        if (Strings.Left(Convert.ToString(doForm.doRS.GetFieldVal("TXT_SUBJ")).ToUpper(), 3) != "RE:")
                            doNewForm.doRS.SetFieldVal("TXT_SUBJ", "RE: " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_Subj")));
                        else
                            doNewForm.doRS.SetFieldVal("TXT_SUBJ", doForm.doRS.GetFieldVal("TXT_Subj"));
                        doNewForm.OpenForm();
                        break;
                    }

                case 4:
                case 6:
                case 8: // Letter, Email, Fax sent
                    {
                        // Open send wizard

                        // CS: Need to always gen the sys name in the case of fields being edited in AC.
                        //goScr.RunScript("GenerateSysName", doForm.doRS, , , , , , , sName);
                        par_doCallingObject = doForm.doRS;
                        scriptManager.RunScript("GenerateSysName", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, sName);
                        doForm.doRS = (clRowSet)par_doCallingObject;
                        if (goP.GetProduct() == "MB")
                            sURL = "../Pages/Mobile_DiaSend.aspx";
                        else
                            sURL = "../Pages/diaSend.aspx";
                        // objectotsend: VIEW, RECORD, CORR
                        goTR.URLWrite(ref sURL, "objecttosend", "CORR");
                        // Send type: EMAIL, FAX, LETTER, or WPRESPONSE
                        switch (iType)
                        {
                            case 4:
                                {
                                    goTR.URLWrite(ref sURL, "sendtype", "LETTER");
                                    break;
                                }

                            case 6:
                                {
                                    goTR.URLWrite(ref sURL, "sendtype", "EMAIL");
                                    break;
                                }

                            case 8:
                                {
                                    goTR.URLWrite(ref sURL, "sendtype", "FAX");
                                    break;
                                }
                        }
                        goTR.URLWrite(ref sURL, "objectid", sID);
                        goTR.URLWrite(ref sURL, "objectname", sName);
                        // goUI.SetNext("DIALOG", sURL)
                        if (doForm.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                        {
                            par_doCallingObject = doForm;
                            return false;
                        }
                        // CS 9/13/08 Added
                        //HttpContext.Current.Response.Redirect(goUI.Navigate("DIALOG", sURL));
                        break;
                    }
            }
            par_doCallingObject = doForm;
            return true;

        Publish:
            ;
            // WP Submission Sent Act that did not have publish checked. User clicked yes to check publish and send.
            doForm.doRS.SetFieldVal("CHK_PUBLISH", 1, clC.SELL_SYSTEM);
            doForm.doRS.SetFieldVal("CHK_SENT", 1, clC.SELL_SYSTEM);
            // save form
            if (doForm.Save(1, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
            {
                // doForm.MessageBox(goErr.GetLastError("MESSAGE"))
                par_doCallingObject = doForm;
                return false;
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_MoveQuoteLine_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            string sDirection = "";
            string sLtGt = "";
            string sFirstLast = "";
            string sMoveSortDir = "";

            string sRecID = "";
            string sQuoteID = "";
            string sSelLineNO = "";
            string sMoveLineNO = "";
            double dSelLineNO = 0;
            double dMoveLineNO = 0;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot edit Quote Lines.");
                return true;
            }

            if (par_s3.ToUpper() == "UP")
            {
                sDirection = "up";
                sLtGt = "<";
                sFirstLast = "first";
                sMoveSortDir = "desc";
            }
            else if (par_s3.ToUpper() == "DOWN")
            {
                sDirection = "down";
                sLtGt = ">";
                sFirstLast = "last";
                sMoveSortDir = "asc";
            }
            else
            {
                return false;
            }

            sQuoteID = doForm.doRS.GetFieldVal("GID_ID").ToString();
            sRecID = doForm.GetLinkSelection("LNK_Connected_QL");

            if (string.IsNullOrEmpty(sRecID))
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }

            clRowSet doQLSelRS = new clRowSet("QL", clC.SELL_EDIT, "GID_ID=" + sRecID, "", "*,**", 1, par_bBypassValidation: true);
            if (doQLSelRS.GetFirst() == 0)
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }
            sSelLineNO = doQLSelRS.GetFieldVal("SR__LineNo").ToString();
            dSelLineNO = Convert.ToDouble(doQLSelRS.GetFieldVal("SR__LineNo"));

            clRowSet doQLMoveRS = new clRowSet("QL", clC.SELL_EDIT, ("LNK_IN_QT=" + sQuoteID + " AND SR__LineNo" + sLtGt + sSelLineNO), "SR__LineNo " + sMoveSortDir, "*,**", 1, par_bBypassValidation: true);
            if (doQLMoveRS.GetFirst() == 0)
            {
                doForm.MessageBox("This is the " + sFirstLast + " Qoute Line. Please select another Qoute Line to move " + sDirection + ".");
                par_doCallingObject = doForm;
                return false;
            }
            sMoveLineNO = doQLMoveRS.GetFieldVal("SR__LineNo").ToString();
            dMoveLineNO = Convert.ToDouble(doQLMoveRS.GetFieldVal("SR__LineNo"));

            doQLSelRS.SetFieldVal("SR__LineNo", dMoveLineNO, 2);
            doQLMoveRS.SetFieldVal("SR__LineNo", dSelLineNO, 2);

            if (doQLSelRS.Commit() == 0 || doQLMoveRS.Commit() == 0)
            {
                doForm.MessageBox("Could not save the Quote Lines. Please reopen the quote and try again.");
                par_doCallingObject = doForm;
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");
            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Agromag")
                {
                    return "Agromag_Draft.docx";
                }
                else if (sQTTemplate == "CanServ")
                {
                    return "CanServ_Draft.docx";
                }
                else if (sQTTemplate == "Canada Co")
                {
                    return "CanadaCo_Draft.docx";
                }
                else if (sQTTemplate == "ECD")
                {
                    return "ECD_Draft.docx";
                }
                else if (sQTTemplate == "GPEL&J")
                {
                    return "GPEL&J_Draft.docx";
                }
                else if (sQTTemplate == "L&JENG")
                {
                    return "L&JENG_Draft.docx";
                }
                //else if (sQTTemplate == "Foxboro Quote")
                //{
                //    return "cus_corr_Foxboro_quote_draft.docx";
                //}
                //else if (sQTTemplate == "Honeywell Analytics")
                //{
                //    return "cus_corr_Honeywell_quote_draft.docx";
                //}
                //else if (sQTTemplate == "L&J Quote")
                //{
                //    return "cus_corr_L&J_quote_draft.docx";
                //}
                //else if (sQTTemplate == "Lenox Quote")
                //{
                //    return "cus_corr_Lenox_quote_draft.docx";
                //}
                //else if (sQTTemplate == "Lincoln Quote")
                //{
                //    return "cus_corr_Lincoln_quote_draft.docx";
                //}
                //else if (sQTTemplate == "Meriam Quote")
                //{
                //    return "cus_corr_Meriam_quote_draft.docx";
                //}
                //else if (sQTTemplate == "Metrix Quote")
                //{
                //    return "cus_corr_Metrix_quote_draft.docx";
                //}
            }
            else
            {
                if (sQTTemplate == "Agromag")
                {
                    return "Agromag.docx";
                }
                else if (sQTTemplate == "CanServ")
                {
                    return "CanServ.docx";
                }
                else if (sQTTemplate == "Canada Co")
                {
                    return "CanadaCo.docx";
                }
                else if (sQTTemplate == "ECD")
                {
                    return "ECD.docx";
                }
                else if (sQTTemplate == "GPEL&J")
                {
                    return "GPEL&J.docx";
                }
                else if (sQTTemplate == "L&JENG")
                {
                    return "L&JENG.docx";
                }
                //else if (sQTTemplate == "Foxboro Quote")
                //{
                //    return "cus_corr_Foxboro_quote.docx";
                //}
                //else if (sQTTemplate == "Honeywell Analytics ")
                //{
                //    return "cus_corr_Honeywell_quote.docx";
                //}
                //else if (sQTTemplate == "L&J Quote")
                //{
                //    return "cus_corr_L&J_quote.docx";
                //}
                //else if (sQTTemplate == "Lenox Quote")
                //{
                //    return "cus_corr_Lenox_quote.docx";
                //}
                //else if (sQTTemplate == "Lincoln Quote")
                //{
                //    return "cus_corr_Lincoln_quote.docx";
                //}
                //else if (sQTTemplate == "Meriam Quote")
                //{
                //    return "cus_corr_Meriam_quote.docx";
                //}
                //else if (sQTTemplate == "Metrix Quote")
                //{
                //    return "cus_corr_Metrix_quote.docx";
                //}
            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")) + "<br/><br/>" + Convert.ToString(doForm.doRS.GetFieldVal("TXT_ABOVESIGNATURE")) + "<br/><br/>" + Convert.ToString(doForm.doRS.GetFieldVal("TXT_SIGNATURE"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);

                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_RELATED_VE"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Vendor");
                doForm.FieldInFocus = "LNK_RELATED_VE";
                par_doCallingObject = doForm;
                return false;
            }
            if (string.IsNullOrEmpty("TXT_PARTNO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Part#");
                doForm.FieldInFocus = "TXT_PARTNO";
                par_doCallingObject = doForm;
                return false;
            }
            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double curCost = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINECOST", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            //string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string VE_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_VE%%GID_ID"));
            string VE_name = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_VE%%TXT_VendorName"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));
            string PartNo = Convert.ToString(doForm.doRS.GetFieldVal("TXT_PARTNO"));
            //string sModelDesc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%MMO_DESCRIPTION"));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,MMO_DETAILS,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC,LNK_MODELVE_VE,TXT_PARTNO,TXT_VENDORNAME", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            //rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);
            rsQL.SetFieldVal("TXT_VENDORNAME", VE_name);
            rsQL.SetFieldVal("LNK_MODELVE_VE", VE_Gid);

            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            rsQL.SetFieldVal("CUR_Cost", curCost);
            rsQL.SetFieldVal("TXT_PARTNO", PartNo);
            //rsQL.SetFieldVal("TXT_MODEL", sModelText);
            //rsQL.SetFieldVal("MMO_DETAILS", sModelDesc);

            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_IN_VE");
                doForm.doRS.SetFieldVal("TXT_PARTNO", "");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
                doForm.doRS.SetFieldVal("TXT_PARTNO", "");
                doForm.doRS.ClearLinkAll("LNK_RELATED_VE");
            }

        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }
        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_IN_VE"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Vendor");
                doForm.FieldInFocus = "LNK_IN_VE";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string VE_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_VE%%GID_ID"));
            string Part = Convert.ToString(doForm.doRS.GetFieldVal("TXT_PARTNO"));
            string vendor = Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_VE%%TXT_VendorName"));

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_IN_VE,TXT_PARTNO,SR__QTY,CUR_UNITPRICE,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_IN_VE", VE_Gid);
            rsOL.SetFieldVal("CUR_UNITPRICE", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);
            rsOL.SetFieldVal("TXT_PARTNO", Part);
            rsOL.SetFieldVal("TXT_VENDOR", vendor);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UNITPRICE|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UNITPRICE|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curValue);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("CUR_UNITPRICE", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));
            string Vendor = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_VE%%TXT_VendorName"));

            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);
            doRS.SetFieldVal("TXT_VENDOR", Vendor);
            //This will generate line no's in mobile.
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool OP_FormControlOnChange_BTN_CALCPROBABILITY_1_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_OPPTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            par_doCallingObject = doForm;
            return true;

        }
        private static void Refresh_OPPTotal(clRowSet doForm)
        {
            string sGidId = Convert.ToString(doForm.GetFieldVal("Gid_id"));

            clRowSet rsOL = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + sGidId + "' ", "LNK_IN_OP", "CUR_VALUE|SUM");

            double curTotalAmt = 0.0;
            if ((rsOL.GetFirst() == 1))
            {
                curTotalAmt = Convert.ToDouble(rsOL.GetFieldVal("CUR_VALUE|SUM", 2));
                doForm.SetFieldVal("CUR_VALUE", curTotalAmt, 2);
                //doForm.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);
            }
            else
            {
                doForm.SetFieldVal("CUR_VALUE", 0.0);
                //doForm.SetFieldVal("CUR_TOTAL", 0.0);

            }
        }
        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);
            doForm.SetFieldProperty("LNK_IN_VE", "LABELCOLOR", color);

            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

            }

            par_doCallingObject = doRS;
            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            clRowSet doRS1 = null;


            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS1.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doRS1.SetFieldVal("CUR_VALUE", curValue);
                    doRS1.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doRS1;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                    doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doForm;
            }

            return true;

        }

        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));
                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");
            doFormQT.doRS.oVar.SetVar("BTN_CLICK", 1);
            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRBUSINESS"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_CITYBUSINESS"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_STATEBUSINESS"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ZIPBUSINESS"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_COUNTRYBUSINESS")));
            doFormQT.doRS.SetFieldVal("MMO_ADDRMAILING", sMailAdd);//primary contact address  
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address  

            doFormQT.doRS.SetFieldVal("DTE_EXPCLOSEDATE", rsOP.GetFieldVal("DTE_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));
            doFormQT.doRS.SetFieldVal("LNK_FROM_SO", rsOP.GetFieldVal("LNK_FROM_SO"));

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    doNewQL.SetFieldVal("LNK_MODELVE_VE", rsOL.GetFieldVal("LNK_IN_VE", 2), 2);
                    doNewQL.SetFieldVal("TXT_PARTNO", rsOL.GetFieldVal("TXT_PARTNO", 2), 2);
                    //doNewQL.SetFieldVal("LNK_RELATED_VE", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }

        public bool QT_FormControlOnChange_BTN_RECALC_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            Refresh_QouteTotal(doForm.doRS);
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            par_doCallingObject = doForm;
            return true;

        }
        private static void Refresh_QouteTotal(clRowSet doQuote)
        {
            string sGidId = Convert.ToString(doQuote.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_GROUPBY, "LNK_IN_QT='" + sGidId + "' ", "LNK_IN_QT", "CUR_SUBTOTAL|SUM");

            double curTotalAmt = 0.0;
            if ((rsQL.GetFirst() == 1))
            {
                curTotalAmt = Convert.ToDouble(rsQL.GetFieldVal("CUR_SUBTOTAL|SUM", 2));
                doQuote.SetFieldVal("CUR_SUBTOTAL", curTotalAmt, 2);
                doQuote.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);
            }
            else
            {
                doQuote.SetFieldVal("CUR_SUBTOTAL", 0.0);
                doQuote.SetFieldVal("CUR_TOTAL", 0.0);
            }
        }
        //public bool QT_FormControlOnChange_LNK_FORLINE_MO_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //{
        //    string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    Form doForm = (Form)par_doCallingObject;

        //    if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO")) != "")
        //    {
        //        string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));

        //        clRowSet doRSModel = default(clRowSet);

        //        doRSModel = new clRowSet("MO", 3, "GID_ID='" + MO_Gid + "'", "", "cur_price,TXT_UNITTEXT,CUR_COST");

        //        doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", doRSModel.GetFieldVal("CUR_PRICE", 1).ToString());
        //        doForm.doRS.SetFieldVal("TXT_LINEUNIT", doRSModel.GetFieldVal("TXT_UNITTEXT", 1).ToString());
        //        doForm.doRS.SetFieldVal("CUR_LINECOST", doRSModel.GetFieldVal("CUR_COST", 1).ToString());
        //    }

        //    par_doCallingObject = doForm;
        //    return true;

        //}
        public bool CN_FormControlOnChange_LNK_RELATED_CO_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string work;
            string customerNameL;
            string street;
            double workd;
            bool addressBlank = false;
            //..goErr.SetError();
            par_bRunNext = false;
            Form objForm = (Form)par_doCallingObject;

            if (objForm.FormRowSet.GetLinkCount("LNK_Related_CO") < 1)
            {
                objForm.MoveToField("LNK_Related_CO");
                return false;
            }

            clRowSet companyRowSet = new clRowSet("CO", 3, "GID_ID='" + objForm.FormRowSet.GetFieldVal("LNK_RELATED_CO") + "'", "", "TXT_COMPANYNAME,TEL_PHONENO,TEL_FAXNO,URL_WEBPAGE,TXT_ADDRMAILING,TXT_CITYMAILING,TXT_STATEMAILING,TXT_ZIPMAILING,TXT_COUNTRYMAILING");

            if (companyRowSet.GetFirst() != 1)
            {
                return false;
            }

            //if (objForm.FormRowSet.GetFieldVal("TXT_COMPANYNAMETEXT").ToString() == "")
            //{
            //    objForm.FormRowSet.SetFieldVal("TXT_COMPANYNAMETEXT", companyRowSet.GetFieldVal("TXT_COMPANYNAME"));
            //}
            if (objForm.FormRowSet.GetFieldVal("TEL_BUSPHONE").ToString() == "")
            {
                objForm.FormRowSet.SetFieldVal("TEL_BUSPHONE", companyRowSet.GetFieldVal("TEL_PHONENO"));
            }
            if (objForm.FormRowSet.GetFieldVal("TEL_FAX").ToString() == "")
            {
                objForm.FormRowSet.SetFieldVal("TEL_FAX", companyRowSet.GetFieldVal("TEL_FAXNO"));
            }
            if (objForm.FormRowSet.GetFieldVal("URL_WEB").ToString() == "")
            {
                objForm.FormRowSet.SetFieldVal("URL_WEB", companyRowSet.GetFieldVal("URL_WEBPAGE"));
            }

            customerNameL = companyRowSet.GetFieldVal("TXT_COMPANYNAME").ToString();
            street = companyRowSet.GetFieldVal("TXT_ADDRMAILING").ToString();

            if (street != "")
            {
                workd = Convert.ToDouble(street.Contains(Environment.NewLine));
                if (workd > 0)
                {
                    work = Microsoft.VisualBasic.Strings.Mid(street, 1, Convert.ToInt32(workd) - 1);
                    if (work == customerNameL)
                    {
                        street = goTR.FromTo(street, Convert.ToInt32(workd) + 2);
                    }
                }

                if (objForm.FormRowSet.GetFieldVal("TXT_ADDRBUSINESS").ToString() == "")
                {
                    addressBlank = true;
                    objForm.FormRowSet.SetFieldVal("TXT_ADDRBUSINESS", street);
                }
            }

            if (addressBlank)
            {
                objForm.FormRowSet.SetFieldVal("TXT_STATEBUSINESS", companyRowSet.GetFieldVal("TXT_STATEMAILING"));
                objForm.FormRowSet.SetFieldVal("TXT_ZIPBUSINESS", companyRowSet.GetFieldVal("TXT_ZIPMAILING"));
                objForm.FormRowSet.SetFieldVal("TXT_COUNTRYBUSINESS", companyRowSet.GetFieldVal("TXT_COUNTRYMAILING"));
                addressBlank = false;
            }

            //objForm.FormRowSet.SetFieldVal("LNK_RELATED_IU", companyRowSet.GetFieldVal("LNK_RELATED_IU"));
            par_doCallingObject = objForm;
            return true;
        }
        public bool CN_FormControlOnChange_BTN_FILLFROMCO_pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            Form objForm = (Form)par_doCallingObject;
            clRowSet doRS = objForm.doRS;
            string sWork = null;
            string sCustNameL = null;
            string sStreet = null;
            long lWork = 0;

            clRowSet doRSCO = new clRowSet("CO", 3, "GID_ID='" + doRS.GetFieldVal("LNK_RELATED_CO") + "'", "", "TXT_COMPANYNAME,TEL_PHONENO,TEL_FAXNO,URL_WEBPAGE,TXT_ADDRMAILING,TXT_CITYMAILING,TXT_STATEMAILING,TXT_COUNTRYMAILING,TXT_ZIPMAILING");
            if (doRSCO.GetFirst() != 1)
            {
                return false;
            }


            if (string.IsNullOrEmpty(Strings.Trim(doRS.GetFieldVal("TXT_COMPANYNAMETEXT").ToString())))
            {
                objForm.doRS.SetFieldVal("TXT_COMPANYNAMETEXT", doRSCO.GetFieldVal("TXT_COMPANYNAME"));
            }
            else
            {
                sWork = doRSCO.GetFieldVal("TXT_COMPANYNAME").ToString();
                if (!string.IsNullOrEmpty(sWork))
                {
                    //And doForm.ovar.getvar("CN_FormControlOnChange_BTN_FillFromCO_NameChecked") <> "1" Then
                    if (sWork != Strings.Trim(doRS.GetFieldVal("TXT_COMPANYNAMETEXT").ToString()))
                    {
                        objForm.doRS.SetFieldVal("TXT_COMPANYNAMETEXT", sWork);
                    }
                }
            }

            if (string.IsNullOrEmpty(Strings.Trim(doRS.GetFieldVal("TEL_BUSPHONE").ToString())))
            {
                objForm.doRS.SetFieldVal("TEL_BUSPHONE", doRSCO.GetFieldVal("TEL_PHONENO"));
            }
            else
            {
                sWork = doRSCO.GetFieldVal("TEL_PHONENO").ToString();
                if (!string.IsNullOrEmpty(sWork))
                {
                    //And doForm.ovar.getvar("CN_FormControlOnChange_BTN_FillFromCO_BusPhoneChecked") <> "1" Then
                    if (sWork != Strings.Trim(doRS.GetFieldVal("TEL_BUSPHONE").ToString()))
                    {
                        objForm.doRS.SetFieldVal("TEL_BUSPHONE", sWork);
                    }
                }
            }

            if (string.IsNullOrEmpty(Strings.Trim(doRS.GetFieldVal("TEL_FAX").ToString())))
            {
                objForm.doRS.SetFieldVal("TEL_FAX", doRSCO.GetFieldVal("TEL_FAXNO"));
            }
            else
            {
                sWork = doRSCO.GetFieldVal("TEL_FAXNO").ToString();
                if (!string.IsNullOrEmpty(sWork))
                {
                    //And doForm.ovar.getvar(System.Reflection.MethodInfo.GetCurrentMethod().Name & "_FaxChecked") <> "1" Then
                    if (sWork != Strings.Trim(doRS.GetFieldVal("TEL_FAX").ToString()))
                    {
                        objForm.doRS.SetFieldVal("TEL_FAX", sWork);
                    }
                }
            }
            //Filling Main Phone is disabled because the Palm Pilot link is limited to 5 phone no's
            //including e-mail. Main Phone syncing reduces the number of available phone numbers.
            //'Main Phone' is generally the same as the Contact's 'Bus Phone'.

            if (string.IsNullOrEmpty(Strings.Trim(doRS.GetFieldVal("URL_WEB").ToString())))
            {
                objForm.doRS.SetFieldVal("URL_WEB", doRSCO.GetFieldVal("URL_WEBPAGE"));
            }
            else
            {
                sWork = doRSCO.GetFieldVal("URL_WEBPAGE").ToString();
                if (!string.IsNullOrEmpty(sWork))
                {
                    //And doForm.ovar.getvar(System.Reflection.MethodInfo.GetCurrentMethod().Name & "_WebChecked") <> "1" Then
                    if (sWork != Strings.Trim(doRS.GetFieldVal("URL_WEB").ToString()))
                    {
                        objForm.doRS.SetFieldVal("URL_WEB", sWork);
                    }
                }
            }

            sCustNameL = doRSCO.GetFieldVal("TXT_COMPANYNAME").ToString();
            sStreet = doRSCO.GetFieldVal("TXT_ADDRMAILING").ToString();
            //Remove the Company name from Company's address, if on first line
            if (!string.IsNullOrEmpty(sStreet))
            {
                lWork = Strings.InStr(sStreet, Environment.NewLine);
                if (lWork > 0)
                {
                    sWork = Strings.Mid(sStreet, 1, Convert.ToInt32(lWork) - 1);
                    if (sWork == sCustNameL)
                    {
                        sStreet = goTR.FromTo(sStreet, lWork + 2);
                    }
                }
                objForm.doRS.SetFieldVal("TXT_ADDRBUSINESS", sStreet);
            }

            objForm.doRS.SetFieldVal("TXT_CITYBUSINESS", doRSCO.GetFieldVal("TXT_CITYMAILING"));
            objForm.doRS.SetFieldVal("TXT_STATEBUSINESS", doRSCO.GetFieldVal("TXT_STATEMAILING"));
            objForm.doRS.SetFieldVal("TXT_ZIPBUSINESS", doRSCO.GetFieldVal("TXT_ZIPMAILING"));
            objForm.doRS.SetFieldVal("TXT_COUNTRYBUSINESS", doRSCO.GetFieldVal("TXT_COUNTRYMAILING"));

            objForm.FieldInFocus = "TXT_ADDRBUSINESS";

            par_bRunNext = false;

            par_doCallingObject = objForm;
            return true;
        }

    }
}

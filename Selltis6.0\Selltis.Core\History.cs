﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Selltis.Core
{
    public class History
    {
        public string LastOpenedDesktopId { get; set; }
        public string CurrentViewPageId { get; set; }
        public string CurrentViewKey { get; set; }
    }

    public class HistoryItems
    {
        public string Key { get; set; }
        public string Title { get; set; }
        public string Id { get; set; }
        public string FormKey { get; set; }
        public string RecordId { get; set; }
        public string FileName { get; set; }
        public string FileType { get; set; }
        public string FormMode { get; set; }
        public DateTime CreatedDateTime { get; set; }
        public object Desktop_or_Form { get; set; }
        public object clForm { get; set; }
        public string IsDesktop_or_Form { get; set; }
        public string Url { get; set; }
    }
}

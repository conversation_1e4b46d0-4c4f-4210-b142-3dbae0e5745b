﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Selltis.BusinessLogic;
using Microsoft.VisualBasic;
using System.Web;
using System.Collections;
using System.Data;

namespace Selltis.Core
{


    public class Desktop
    {
        public IList<View> Views { get; set; }
        public string DesktopMetaData { get; set; }
        public string Title { get; set; }
        public int ViewCount { get; set; } //top views
        public int TabViewCount { get; set; } //tabbed views
        public string DesktopId { get; set; }
        public string Section { get; set; }
        public int ColumnsCount { get; set; } //always no. of columns in the desktop is 2 in the meta data
        public double Col1Width { get; set; }
        public double Col2Width { get; set; }
        public string DefaultViewId { get; set; }
        public string ViewIDs { get; set; }
        public string ViewIDsWith_IsTabView { get; set; }

        public bool ResetReportTree = false;
        public string ClientViewIds { get; set; }
        public string ClientViewIdsOnFocus { get; set; }
        public string IndependentViewIds { get; set; }

        public ArrayList ViewsInEvaluatedOrder { get; set; }
        public ArrayList ViewTables { get; set; }

        public bool MessageBoxDisplay
        {
            get { return gbMBDisplay; }
        }
        public string MessageBoxMessage
        {
            get { return gsMBMessage; }
        }
        public int MessageBoxStyle
        {
            get { return giMBStyle; }
        }
        public string MessageBoxTitle
        {
            get { return gsMBTitle; }
        }
        public string MessageBoxButton1Label
        {
            get { return gsMBButton1Label; }
        }
        public string MessageBoxButton2Label
        {
            get { return gsMBButton2Label; }
        }
        public string MessageBoxButton3Label
        {
            get { return gsMBButton3Label; }
        }
        public string MessageBoxInputDefaultValue
        {
            get { return gsMBInputDefaultValue; }
        }
        public string MessageBoxButton1Script
        {
            get { return gsMBButton1Script; }
        }
        public string MessageBoxButton2Script
        {
            get { return gsMBButton2Script; }
        }
        public string MessageBoxButton3Script
        {
            get { return gsMBButton3Script; }
        }
        public string MessageBoxPar1
        {
            get { return gsMBPar1; }
        }

        public string MessageBoxPar2
        {
            get { return gsMBPar2; }
        }

        public string MessageBoxPar3
        {
            get { return gsMBPar3; }
        }

        public string MessageBoxPar4
        {
            get { return gsMBPar4; }
        }

        public string MessageBoxPar5
        {
            get { return gsMBPar5; }
        }
        public bool MessageBoxOverrideButtonScript
        {
            get { return gbMBOverrideButtonScript; }
            set { gbMBOverrideButtonScript = value; }
        }
        private clError goErr;
        private bool gbMBDisplay = false;
        private string gsMBMessage = "";
        private int giMBStyle = clC.SELL_MB_OK;
        private string gsMBTitle = "";
        private string gsMBButton1Label = "";
        private string gsMBButton2Label = "";
        private string gsMBButton3Label = "";
        private string gsMBInputDefaultValue = "";
        private string gsMBButton1Script = "";
        private string gsMBButton2Script = "";
        private string gsMBButton3Script = "";
        private string gsMBPar1 = "";
        private string gsMBPar2 = "";
        private string gsMBPar3 = "";
        private string gsMBPar4 = "";
        private string gsMBPar5 = "";
        private bool gbMBOverrideButtonScript = false;
        private Collection gcViewMetadata = new Collection();
        private Collection gcViewState = new Collection();
        public string SelectedViewID { get; set; }
        public string DialPhoneNumber { get; set; }
        public string SelectedDesktopId { get; set; }
        private clMetaData goMeta;
        private clTransform goTR;
        private clProject goP;
        private clData goData;

        public bool _isLoadFromSession;
        public int TabHeight { get; set; }
        public bool IsValid { get; set; }

        string _MasterViewKey = string.Empty;
        public Collection ViewMetaData
        {
            get { return gcViewMetadata; }
        }
        public Collection ViewState
        {
            get { return gcViewState; }
        }

        public string gsDisplayMeta { get; set; }

        public string ViewId_and_Types { get; set; }
        public string ViewId_and_ParentIds { get; set; }
        public string TabViewIds { get; set; }
        public IList<TempView> TempViews { get; set; }

        public string MasterViewIds { get; set; }
        public string DRSEnabledViewIds { get; set; }
        public string Key { get; set; }

        public string CusDesktop { get; set; }

        //use this only for PowerBI reports
        public string WorkspaceId { get; set; } 
        //use this only for PowerBI reports
        public string ReportId { get; set; }

        public Desktop(Desktop desktop, string sKey = "")
        {
            SetDesktopPropertiesData(desktop, sKey);               
        }


        public Collection ViewDependencyHeirarchy { get; set; }

        private void SetDesktopPropertiesData(Desktop desktop,string sKey = "")
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");

            this._isLoadFromSession = desktop._isLoadFromSession;
            this.Key = desktop.Key;
            //this._MasterViewKey = desktop._MasterViewKey;
            //this.Col1Width = desktop.Col1Width;
            //this.Col2Width = desktop.Col2Width;
            //this.ColumnsCount = desktop.ColumnsCount;
            //this.DefaultViewId = desktop.DefaultViewId;
            this.DesktopId = desktop.DesktopId;
            this.DesktopMetaData = desktop.DesktopMetaData;
            this.DialPhoneNumber = desktop.DialPhoneNumber;
            //this.DRSEnabledViewIds = desktop.DRSEnabledViewIds;
            //this.gbMBDisplay = desktop.gbMBDisplay;
            //this.gbMBOverrideButtonScript = desktop.gbMBOverrideButtonScript;
            this.gcViewMetadata = desktop.gcViewMetadata;
            this.gcViewState = desktop.gcViewState;
            //this.giMBStyle = desktop.giMBStyle;
            //this.goErr = desktop.goErr;
            //this.goMeta = desktop.goMeta;
            //this.goP = desktop.goP;
            //this.goTR = desktop.goTR;
            this.gsDisplayMeta = desktop.gsDisplayMeta;
            //this.gsMBButton1Label = desktop.gsMBButton1Label;
            //this.gsMBButton1Script = desktop.gsMBButton1Script;
            //this.gsMBButton2Label = desktop.gsMBButton2Label;
            //this.gsMBButton2Script = desktop.gsMBButton2Script;
            //this.gsMBButton3Label = desktop.gsMBButton3Label;
            //this.gsMBButton3Script = desktop.gsMBButton3Script;
            //this.gsMBInputDefaultValue = desktop.gsMBInputDefaultValue;
            //this.gsMBMessage = desktop.gsMBMessage;
            //this.gsMBPar1 = desktop.gsMBPar1;
            //this.gsMBPar2 = desktop.gsMBPar2;
            //this.gsMBPar3 = desktop.gsMBPar3;
            //this.gsMBPar4 = desktop.gsMBPar4;
            //this.gsMBPar5 = desktop.gsMBPar5;
            //this.gsMBTitle = desktop.gsMBTitle;
            this.IsValid = desktop.IsValid;
            //this.MasterViewIds = desktop.MasterViewIds;
            //this.MessageBoxOverrideButtonScript = desktop.MessageBoxOverrideButtonScript;
            //this.ResetReportTree = desktop.ResetReportTree;
            this.Section = desktop.Section;
            //this.SelectedDesktopId = desktop.SelectedDesktopId;
            //this.SelectedViewID = desktop.SelectedViewID;
            //this.TabHeight = desktop.TabHeight;
            this.TabViewCount = desktop.TabViewCount;
            //this.TabViewIds = desktop.TabViewIds;
            //this.TempViews = desktop.TempViews;
            this.Title = desktop.Title;
            this.ViewCount = desktop.ViewCount;
            //this.ViewId_and_ParentIds = desktop.ViewId_and_ParentIds;
            //this.ViewId_and_Types = desktop.ViewId_and_Types;
            //this.ViewIDs = desktop.ViewIDs;
            //this.ViewIDsWith_IsTabView = desktop.ViewIDsWith_IsTabView;
            ////this.Views = desktop.Views;
            //this.ViewsInEvaluatedOrder = desktop.ViewsInEvaluatedOrder;
            //this.ViewTables = desktop.ViewTables;

            Views = new List<View>();
            InitializeViews();
            if (ViewIDs != null)
            {
                ViewIDs = ViewIDs.TrimStart(',');
                ViewIDsWith_IsTabView = ViewIDsWith_IsTabView.TrimStart(',');
            }
            SetViewSizes();
            SetViewDependencies();
        }

        public Desktop(string sSection, string sDesktopId, bool isLoadFromSession = true, string sKey = "")
        {


            if (isLoadFromSession == false)
            {
                Util.SetSessionValue(Key + "_" + "lstViews", null);
                Util.ClearSessionValue(Key + "_" + "lstViews");
                Util.ClearSessionValue(Key + "_" + "ViewSelectedRecordIds");
                Util.ClearSessionValue(Key + "_" + "ViewsLoadedWithFakeIds");
                Util.ClearSessionValue(Key + "_" + "ClientViewIds");
                Util.ClearSessionValue(Key + "_" + "ClientViewIdsOnFocus");
                Util.ClearSessionValue(Key + "_" + "SelectedTabViewId");
            }


            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");

            DesktopId = sDesktopId;
            Section = sSection;
            _isLoadFromSession = isLoadFromSession;
            Key = sKey;

            DesktopMetaData = goMeta.PageRead(sSection, sDesktopId, "", true);
            Title = goTR.StrRead(DesktopMetaData, "NAME", "");

            //powerBI report properties
            WorkspaceId = goTR.StrRead(DesktopMetaData, "WORKSPACEID", "");
            ReportId = goTR.StrRead(DesktopMetaData, "REPORTID", "");

            //GET VIEW MENU META
            gsDisplayMeta = goMeta.PageRead("GLOBAL", "MNU_VIEW");

            if (string.IsNullOrEmpty(Title))
            {
                Title = "<No Title Defined>";
            }

            CusDesktop = goTR.StrRead(DesktopMetaData, "FRFCONTROL", "");

            ViewCount = Convert.ToInt32(goTR.StrRead(DesktopMetaData, "VIEWCOUNT", "0")); //Convert.ToInt32(goMeta.LineRead(sSection, sDesktopId, "VIEWCOUNT", "0"));
            TabViewCount = Convert.ToInt32(goTR.StrRead(DesktopMetaData, "TABCOUNT", "0")); //Convert.ToInt32(goMeta.LineRead(sSection, sDesktopId, "TABCOUNT", "0"));
            Views = new List<View>();
            if (!string.IsNullOrEmpty(DesktopMetaData) && ViewCount == 0 && TabViewCount == 0)
            {
                IsValid = false;
            }
            else
            {
                IsValid = true;
                PrepareViewInfo();
                PrepareViewId_Parents();
                InitializeViews();
                if (ViewIDs != null)
                {
                    ViewIDs = ViewIDs.TrimStart(',');
                    ViewIDsWith_IsTabView = ViewIDsWith_IsTabView.TrimStart(',');
                }

                SetViewSizes();
                SetViewDependencies();

                Util.SetSessionValue("firstLoad", "yes");
                if (_isLoadFromSession == false)
                {
                    Util.SetSessionValue(Key + "_" + "ClientViewIds", ClientViewIds);
                    Util.SetSessionValue(Key + "_" + "ClientViewIdsOnFocus", ClientViewIdsOnFocus);
                    Util.SetSessionValue(Key + "_" + "lstViews", Views);
                    Util.LoadViewDataSets(ViewCount, TabViewCount, "", false, Key, true);

                    //Load views which are loaded with fake ids irrespective of evaluation order..J
                    Util.LoadViewDataSetsWithFakeIds(Key);

                }

            }
            //if (_isLoadFromSession == false)
            //{
                Util.SetSessionValue(Key + "_" + "lstViews", Views);
            //}
        }
        private void PrepareViewInfo()
        {
            //this call is required for charts
            //kendo charts do not support read action so we need to load the chart in teh chart class initialization
            //but the session view info is not available in charts class constructor so we are loading the dependecy 
            //hierarchy before Intializeviews() call
            string sViewId = "";
            string sViewTable = "";
            string sCondition = "";

            //**********************************************

            ArrayList arrFiles = new ArrayList();
            TempViews = new List<TempView>();
            int _index = 1;

            for (int i = 1; i <= ViewCount; i++)//0
            {
                string sFile = GetViewInfoFromDesktopMetaData(i, "FILE", false);

                if (arrFiles.Contains(sFile) == false)
                {
                    arrFiles.Add(sFile);
                }

                sViewId = GetViewInfoFromDesktopMetaData(i, "VIEWID", false);
                sViewTable = GetViewInfoFromDesktopMetaData(i, "FILE", false);
                sCondition = GetViewInfoFromDesktopMetaData(i, "CONDITION", false);

                TempViews.Add(new TempView { TableName = sFile, ViewId = sViewId, index = _index, Condition = sCondition, isTabView = false });
                _index++;
            }
            for (int i = 1; i <= TabViewCount; i++)
            {
                string sFile = GetViewInfoFromDesktopMetaData(i, "FILE", true);

                if (arrFiles.Contains(sFile) == false)
                {
                    arrFiles.Add(sFile);
                }

                sViewId = GetViewInfoFromDesktopMetaData(i, "VIEWID", true);
                sViewTable = GetViewInfoFromDesktopMetaData(i, "FILE", true);
                sCondition = GetViewInfoFromDesktopMetaData(i, "CONDITION", true);

                TempViews.Add(new TempView { TableName = sFile, ViewId = sViewId, index = _index, Condition = sCondition, isTabView = true });
                _index++;
            }


            foreach (var _tableName in arrFiles)
            {
                for (int i = 1; i <= (ViewCount + TabViewCount); i++)
                {
                    var _tempview = TempViews.ToList<TempView>().Find(t => t.index == i);

                    if (_tempview != null)
                    {
                        string sCondtion = _tempview.Condition;
                        string sSearchPattern1 = ("<%SelectedRecordID FILE=" + _tableName + "%>").ToLower();
                        string sSearchPattern2 = ("<%SelectedViewRecordID FILE=" + _tableName + "%>").ToLower();

                        if (sCondtion.ToLower().Contains(sSearchPattern1) || sCondtion.ToLower().Contains(sSearchPattern2))
                        {
                            var _parentViews = TempViews.ToList<TempView>().FindAll(v => v.TableName.ToLower() == _tableName.ToString().ToLower());

                            if (_parentViews != null)
                            {
                                if (_parentViews.Count > 0)
                                {
                                    foreach (var _parentView in _parentViews)
                                    {
                                        if (_parentView.ViewId.Replace(" ", "") != _tempview.ViewId.Replace(" ", "") )//&& _parentView.isTabView == false
                                        {
                                            if (string.IsNullOrEmpty(_tempview.ParentViewId))
                                            {
                                                _tempview.ParentViewId = _parentView.ViewId.Replace(" ", "");
                                            }
                                            else
                                            {
                                                _tempview.ParentViewId = _tempview.ParentViewId + "," + _parentView.ViewId.Replace(" ", "");
                                            }
                                        }
                                    }
                                }

                                //if (string.IsNullOrEmpty(_tempview.ParentViewId))
                                //{
                                //    _tempview.ParentViewId = _parentViews[0].ViewId.Replace(" ", "");
                                //}
                                //else
                                //{
                                //    _tempview.ParentViewId = _tempview.ParentViewId + "," + _parentViews[0].ViewId.Replace(" ", "");
                                //}

                            }
                        }
                    }
                }
            }

            Util.SetSessionValue("TempViews", TempViews);

        }

        private string GetViewInfoFromDesktopMetaData(int i, string sParam, bool isTabView)
        {
            switch (sParam)
            {
                case "VIEWKEY":
                    return isTabView ? "TAB" + i.ToString() + "VIEWID" : "VIEW" + i.ToString() + "ID";
                case "VIEWID":
                    return goTR.StrRead(DesktopMetaData, GetViewInfoFromDesktopMetaData(i, "VIEWKEY", isTabView), "");
                case "VIEWMETA":
                    return goMeta.PageRead(Section, GetViewInfoFromDesktopMetaData(i, "VIEWID", isTabView));
                case "FILE":
                    return Strings.UCase(goTR.StrRead(GetViewInfoFromDesktopMetaData(i, "VIEWMETA", isTabView), "FILE", ""));
                case "CONDITION":
                    return goTR.StrRead(GetViewInfoFromDesktopMetaData(i, "VIEWMETA", isTabView), "CONDITION", "");
                default:
                    return "";
            }
        }

        private void InitializeViews()
        {

            //int l = 10;
            //int j = 0;
            //int k = l / j;

            ViewsInEvaluatedOrder = new ArrayList();
            ViewTables = new ArrayList();

            //initialize top views          
            for (int i = 1; i <= ViewCount; i++)//0
            {
                AddView(false, i);
            }
            //initialize tab views          
            for (int i = 1; i <= TabViewCount; i++)
            {
                AddView(true, i);
            }

        }
        private void AddView(bool _isTabView, int i)
        {
            string _sViewKey = _isTabView ? "TAB" + i.ToString() + "VIEWID" : "VIEW" + i.ToString() + "ID";
            string _viewId = goTR.StrRead(DesktopMetaData, _sViewKey, ""); //goMeta.LineRead(Section, DesktopId, _sViewKey, "");
            string _viewType = GetViewType(_viewId, DesktopMetaData);
            if (_isTabView)
            {
                if (string.IsNullOrEmpty(TabViewIds))
                {
                    TabViewIds = _viewId.Replace(" ", "");
                }
                else
                {
                    TabViewIds = TabViewIds + "," + _viewId.Replace(" ", "");
                }
            }
            string sSelRec = "";
            if (_viewType.ToLower() == "list")
            {
                Grid _newGrid = new Grid(_viewId, _isTabView, Section, DesktopMetaData, i, _isLoadFromSession, Views.Count, Key);

                _newGrid.Key = Key;

                if (gcViewMetadata.Contains(_viewId) == false && !string.IsNullOrEmpty(_viewId))
                {
                    gcViewMetadata.Add(_newGrid.ViewMetaData, _viewId);
                    gcViewState.Add("", _viewId);
                }               
                
                if (_newGrid.IsTabView)
                {
                    _newGrid.DisplayIndex = _newGrid.OrderIndex + ViewCount;
                }
                else
                {
                    _newGrid.DisplayIndex = _newGrid.OrderIndex;
                }
                if (Util.GetSessionValue(Key + "_" + _newGrid.ViewKey) == null || _isLoadFromSession == false)
                {
                    //Assign Session variables                
                    SessionViewInfo _SessionViewInfo = new SessionViewInfo();
                    _SessionViewInfo.Toprec = string.Empty;
                    _SessionViewInfo.Bottomrec = string.Empty;
                    _SessionViewInfo.PreviousPageno = 1;

                    _SessionViewInfo.ViewCondition = _newGrid.DefaultViewCondition;
                    _SessionViewInfo.TableName = _newGrid.TableName;
                    _SessionViewInfo.SortText = _newGrid.DefaultSortCondition;
                    _SessionViewInfo.Fields = _newGrid.Fields;
                    _SessionViewInfo.PageSize = _newGrid.PageSize;
                    _SessionViewInfo.LinksTop = _newGrid.LinksTop;
                    _SessionViewInfo.IsMasterView = _newGrid.IsMasterView;
                    _SessionViewInfo.ViewMetaData = _newGrid.ViewMetaData;
                    _SessionViewInfo.ViewTitle = _newGrid.ViewTitle;
                    _SessionViewInfo.AutoCount = _newGrid.AutoCount;
                    _SessionViewInfo.AutoLoad = _newGrid.AutoLoad;
                    if (_SessionViewInfo.AutoLoad == "1")
                    {
                        _SessionViewInfo.DefaultAutoLoad = true;
                    }
                    else
                    {
                        _SessionViewInfo.DefaultAutoLoad = false;
                    }
                    _SessionViewInfo.RawViewId = _newGrid.ViewId;
                    _SessionViewInfo.IsInvalid = _newGrid.IsInvalid;

                    _SessionViewInfo.Section = Section;

                    //ViewsInEvaluatedOrder.Add(_newGrid.ViewId);

                    if (string.IsNullOrEmpty(_newGrid.ParentViewId))
                    {
                        _SessionViewInfo.MasterViewKey = _newGrid.ViewKey;
                    }
                    else
                    {
                        _SessionViewInfo.MasterViewKey = _newGrid.ParentViewId;
                    }

                    _SessionViewInfo.Columns = _newGrid.Columns;
                    _SessionViewInfo.Sorts = _newGrid.Sorts;
                    _SessionViewInfo.ViewType = _viewType;

                    _SessionViewInfo.Index = _newGrid.Index;
                    _SessionViewInfo.DisplayIndex = _newGrid.DisplayIndex;

                    
                    _SessionViewInfo.CurrentPageNumber = 1;
                    _SessionViewInfo.TopViewCount = ViewCount;
                    _SessionViewInfo.TabViewCount = TabViewCount;

                    string gsViewState = GetViewStateMetadata(_newGrid.ViewId);
                    sSelRec = goTR.StrRead(gsViewState, "SELECTEDRECORDID", "");

                    if (goTR.StrRead(gsViewState,"LOADED","0") == "0" || goTR.StrRead(gsViewState,"NEWPROPERTIES") == "1")
                    {
                        if (sSelRec == "")
                        {
                            sSelRec = goTR.StrRead(_newGrid.ViewMetaData, "AUTOSELECT", "");
                            if (sSelRec != "")
                            {
                                switch(sSelRec)
                                {
                                    case "NONELINE":
                                        sSelRec = goData.GenSUIDFake(_newGrid.TableName);
                                        break;
                                    case "ANYLINE":
                                        sSelRec = goData.GenSUIDAnyFake(_newGrid.TableName);
                                        break;
                                    default:
                                        string par_sFileName = "";
                                        object par_oRowset = null;
                                        sSelRec = goTR.GetLineValue(ref sSelRec, ref par_sFileName, "", false, ref par_oRowset);
                                        break;                                        
                                }
                            }
                        }
                    }

                    if (!string.IsNullOrEmpty(sSelRec))
                    {
                        _newGrid.IsSelectSpecificRecord = true;
                        _newGrid.SelectSpecificRecordId = sSelRec;
                        _SessionViewInfo.IsSelectSpecificRecord = true;
                        _SessionViewInfo.SelectSpecificRecordId = sSelRec;
                        _SessionViewInfo.LastSelectedRecord = sSelRec;
                    }

                    Util.SetSessionValue(Key + "_" + _newGrid.ViewKey, _SessionViewInfo);
                }
                else
                {
                    SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + _newGrid.ViewKey);
                    _SessionViewInfo.Columns = _newGrid.Columns;
                    _SessionViewInfo.Sorts = _newGrid.Sorts;

                    //sSelRec = goTR.StrRead(_SessionViewInfo.ViewMetaData, "AUTOSELECT", "");
                    //if (sSelRec != "")
                    //{
                    //    switch (sSelRec)
                    //    {
                    //        case "NONELINE":
                    //            sSelRec = goData.GenSUIDFake(_newGrid.TableName);
                    //            break;
                    //        case "ANYLINE":
                    //            sSelRec = goData.GenSUIDAnyFake(_newGrid.TableName);
                    //            break;
                    //        default:
                    //            string par_sFileName = "";
                    //            object par_oRowset = null;
                    //            sSelRec = goTR.GetLineValue(ref sSelRec, ref par_sFileName, "", false, ref par_oRowset);
                    //            break;
                    //    }
                    //}

                    //if (!string.IsNullOrEmpty(sSelRec))
                    //{
                    //    _newGrid.IsSelectSpecificRecord = true;
                    //    _newGrid.SelectSpecificRecordId = sSelRec;
                    //    _SessionViewInfo.IsSelectSpecificRecord = true;
                    //    _SessionViewInfo.SelectSpecificRecordId = sSelRec;
                    //    _SessionViewInfo.LastSelectedRecord = sSelRec;
                    //}
                    //else
                    //{
                    //    _SessionViewInfo.IsSelectSpecificRecord = false;
                    //    _SessionViewInfo.SelectSpecificRecordId = null;
                    //    _SessionViewInfo.LastSelectedRecord = null;
                    //}

                    Util.SetSessionValue(Key + "_" + _newGrid.ViewKey, _SessionViewInfo);
                }
                ViewIDs = ViewIDs + "," + _newGrid.ViewId;
                ViewIDsWith_IsTabView = ViewIDsWith_IsTabView + "," + _newGrid.ViewId + "|" + _newGrid.IsTabView;
                Views.Add(_newGrid);

                if (i == 1 && Views.Count == 1)
                {
                    DefaultViewId = _newGrid.ViewId;
                }

                if (ViewTables.Contains(_newGrid.TableName) == false)
                {
                    ViewTables.Add(_newGrid.TableName);
                }

                ViewsInEvaluatedOrder.Add(_newGrid.ViewId);

                SetViewId_and_Type(_newGrid.ViewId, "list");

                if (_isTabView == false)
                    SetClientViewIds(_newGrid.ViewId, _newGrid.DefaultViewCondition);
                else
                {
                    if (_isLoadFromSession == false && i == 1 && (_newGrid.DefaultViewCondition.ToLower().Contains("<%selectedrecordid") || _newGrid.DefaultViewCondition.ToLower().Contains("<%selectedviewrecordid")))
                    {
                        Util.SetSessionValue(Key + "_" + "SelectedTabViewId", _newGrid.ViewId.Replace(" ", ""));
                    }
                }
            }
            else if (_viewType.ToLower() == "report")
            {
                Report _newrpt = new Report(_viewId, _isTabView, Section, DesktopMetaData, i, _isLoadFromSession, Views.Count, Key);
                _newrpt.Key = Key;
                if (gcViewMetadata.Contains(_viewId) == false && !string.IsNullOrEmpty(_viewId))
                {
                    gcViewMetadata.Add(_newrpt.ViewMetaData, _viewId);
                    gcViewState.Add("", _viewId);
                }
                _newrpt.ViewType = _viewType;

                if (_newrpt.IsTabView)
                {
                    _newrpt.DisplayIndex = _newrpt.OrderIndex + ViewCount;
                }
                else
                {
                    _newrpt.DisplayIndex = _newrpt.OrderIndex;
                }

                if (Util.GetSessionValue(Key + "_" + _newrpt.ViewKey) == null || _isLoadFromSession == false)
                {
                    //Assign Session variables                
                    SessionViewInfo _SessionViewInfo = new SessionViewInfo();

                    _SessionViewInfo.Toprec = string.Empty;
                    _SessionViewInfo.Bottomrec = string.Empty;
                    _SessionViewInfo.PreviousPageno = 1;
                    _SessionViewInfo.ViewCondition = _newrpt.DefaultViewCondition;
                    _SessionViewInfo.TableName = _newrpt.TableName;
                    _SessionViewInfo.SortText = _newrpt.DefaultSortCondition;
                    _SessionViewInfo.Fields = _newrpt.Fields;
                    _SessionViewInfo.PageSize = _newrpt.PageSize;
                    _SessionViewInfo.LinksTop = _newrpt.LinksTop;
                    _SessionViewInfo.IsMasterView = _newrpt.IsMasterView;
                    _SessionViewInfo.ViewMetaData = _newrpt.ViewMetaData;
                    _SessionViewInfo.ViewTitle = _newrpt.ViewTitle;
                    _SessionViewInfo.GroupedColumns = _newrpt.GroupedColumns;
                    _SessionViewInfo.AggregateColumns = _newrpt.AggregateColumns;
                    _SessionViewInfo.AutoCount = _newrpt.AutoCount;
                    _SessionViewInfo.AutoLoad = _newrpt.AutoLoad;
                    if (_SessionViewInfo.AutoLoad == "0")
                    {
                        _SessionViewInfo.DefaultAutoLoad = false;
                    }
                    else
                    {
                        _SessionViewInfo.DefaultAutoLoad = true;
                    }
                    _SessionViewInfo.RawViewId = _newrpt.ViewId;
                    _SessionViewInfo.ViewRecordOpen = _newrpt.ViewRecordOpen;
                    _SessionViewInfo.PercentageColumns = _newrpt.PercentColumns;
                    _SessionViewInfo.Section = Section;
                    _SessionViewInfo.HashContainedColumns = _newrpt.HashContainedColumns;
                    //ViewsInEvaluatedOrder.Add(_newrpt.ViewId);
                    _SessionViewInfo.ViewType = _viewType;
                    _SessionViewInfo.Index = _newrpt.Index;
                    _SessionViewInfo.DisplayIndex = _newrpt.DisplayIndex;
                    _SessionViewInfo.CurrentPageNumber = 1;
                    _SessionViewInfo.TopViewCount = ViewCount;
                    _SessionViewInfo.TabViewCount = TabViewCount;
                    // _SessionViewInfo.MasterViewKey = _MasterViewKey;
                    //_SessionViewInfo.MasterViewKey = _newrpt.ParentViewId;
                    if (string.IsNullOrEmpty(_newrpt.ParentViewId))
                    {
                        _SessionViewInfo.MasterViewKey = _newrpt.ViewKey;
                    }
                    else
                    {
                        _SessionViewInfo.MasterViewKey = _newrpt.ParentViewId;
                    }

                    _SessionViewInfo.Columns = _newrpt.Columns;
                    _SessionViewInfo.Sorts = _newrpt.Sorts;

                    string gsViewState = GetViewStateMetadata(_newrpt.ViewId);
                    sSelRec = goTR.StrRead(gsViewState, "SELECTEDRECORDID", "");

                    if (goTR.StrRead(gsViewState, "LOADED", "0") == "0" || goTR.StrRead(gsViewState, "NEWPROPERTIES") == "1")
                    {
                        if (sSelRec == "")
                        {
                            sSelRec = goTR.StrRead(_newrpt.ViewMetaData, "AUTOSELECT", "");
                            if (sSelRec != "")
                            {
                                switch (sSelRec)
                                {
                                    case "NONELINE":
                                        sSelRec = goData.GenSUIDFake(_newrpt.TableName);
                                        break;
                                    case "ANYLINE":
                                        sSelRec = goData.GenSUIDAnyFake(_newrpt.TableName);
                                        break;
                                    default:
                                        string par_sFileName = "";
                                        object par_oRowset = null;
                                        sSelRec = goTR.GetLineValue(ref sSelRec, ref par_sFileName, "", false, ref par_oRowset);
                                        break;
                                }
                            }
                        }
                    }

                    if (!string.IsNullOrEmpty(sSelRec))
                    {
                        _newrpt.IsSelectSpecificRecord = true;
                        _newrpt.SelectSpecificRecordId = sSelRec;
                        _SessionViewInfo.IsSelectSpecificRecord = true;
                        _SessionViewInfo.SelectSpecificRecordId = sSelRec;
                        _SessionViewInfo.LastSelectedRecord = sSelRec;

                    }


                    Util.SetSessionValue(Key + "_" + "rptview_" + _newrpt.ViewKey, _newrpt);
                    //_SessionViewInfo.View = _newrpt;
                    Util.SetSessionValue(Key + "_" + _newrpt.ViewKey, _SessionViewInfo);
                }
                else
                {
                    SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + _newrpt.ViewKey);
                    _SessionViewInfo.Columns = _newrpt.Columns;
                    _SessionViewInfo.Sorts = _newrpt.Sorts;
                    //_SessionViewInfo.View = _newrpt;
                    Util.SetSessionValue(Key + "_" + "rptview_" + _newrpt.ViewKey, _newrpt);
                    _SessionViewInfo.GroupedColumns = _newrpt.GroupedColumns;
                    _SessionViewInfo.AggregateColumns = _newrpt.AggregateColumns;

                    //sSelRec = goTR.StrRead(_SessionViewInfo.ViewMetaData, "AUTOSELECT", "");
                    //if (sSelRec != "")
                    //{
                    //    switch (sSelRec)
                    //    {
                    //        case "NONELINE":
                    //            sSelRec = goData.GenSUIDFake(_newrpt.TableName);
                    //            break;
                    //        case "ANYLINE":
                    //            sSelRec = goData.GenSUIDAnyFake(_newrpt.TableName);
                    //            break;
                    //        default:
                    //            string par_sFileName = "";
                    //            object par_oRowset = null;
                    //            sSelRec = goTR.GetLineValue(ref sSelRec, ref par_sFileName, "", false, ref par_oRowset);
                    //            break;
                    //    }
                    //}

                    //if (!string.IsNullOrEmpty(sSelRec))
                    //{
                    //    _newrpt.IsSelectSpecificRecord = true;
                    //    _newrpt.SelectSpecificRecordId = sSelRec;
                    //    _SessionViewInfo.IsSelectSpecificRecord = true;
                    //    _SessionViewInfo.SelectSpecificRecordId = sSelRec;
                    //    _SessionViewInfo.LastSelectedRecord = sSelRec;
                    //}
                    //else
                    //{
                    //    _SessionViewInfo.IsSelectSpecificRecord = false;
                    //    _SessionViewInfo.SelectSpecificRecordId = null;
                    //    _SessionViewInfo.LastSelectedRecord = null;
                    //}

                    Util.SetSessionValue(Key + "_" + _newrpt.ViewKey, _SessionViewInfo);
                }

                if (ViewTables.Contains(_newrpt.TableName) == false)
                {
                    ViewTables.Add(_newrpt.TableName);
                }

                ViewsInEvaluatedOrder.Add(_newrpt.ViewId);

                ViewIDs = ViewIDs + "," + _newrpt.ViewId;
                ViewIDsWith_IsTabView = ViewIDsWith_IsTabView + "," + _newrpt.ViewId + "|" + _newrpt.IsTabView;
                Views.Add(_newrpt);

                if (i == 1 && Views.Count == 1)
                {
                    DefaultViewId = _newrpt.ViewId;
                }

                SetViewId_and_Type(_newrpt.ViewId, "report");

                if (_isTabView == false)
                    SetClientViewIds(_newrpt.ViewId, _newrpt.DefaultViewCondition);
                else
                {
                    if (_isLoadFromSession == false && i == 1 && (_newrpt.DefaultViewCondition.ToLower().Contains("<%selectedrecordid") || _newrpt.DefaultViewCondition.ToLower().Contains("<%selectedviewrecordid")))
                    {
                        Util.SetSessionValue(Key + "_" + "SelectedTabViewId", _newrpt.ViewId.Replace(" ", ""));
                    }
                }
            }

            else if (_viewType.ToLower() == "map")
            {
                Map _newmap = new Map(_viewId, _isTabView, Section, DesktopMetaData, i, _isLoadFromSession, Views.Count, Key);
                _newmap.Key = Key; 
                if (gcViewMetadata.Contains(_viewId) == false && !string.IsNullOrEmpty(_viewId))
                {
                    gcViewMetadata.Add(_newmap.ViewMetaData, _viewId);
                    gcViewState.Add("", _viewId);
                }
                _newmap.ViewType = _viewType;

                if (_newmap.IsTabView)
                {
                    _newmap.DisplayIndex = _newmap.OrderIndex + ViewCount;
                }
                else
                {
                    _newmap.DisplayIndex = _newmap.OrderIndex;
                }

                if (Util.GetSessionValue(Key + "_" + _newmap.ViewKey) == null || _isLoadFromSession == false)
                {
                    //Assign Session variables                
                    SessionViewInfo _SessionViewInfo = new SessionViewInfo();

                    _SessionViewInfo.Toprec = string.Empty;
                    _SessionViewInfo.Bottomrec = string.Empty;
                    _SessionViewInfo.PreviousPageno = 1;

                    _SessionViewInfo.ViewCondition = _newmap.DefaultViewCondition;
                    _SessionViewInfo.TableName = _newmap.TableName;
                    _SessionViewInfo.SortText = _newmap.DefaultSortCondition;
                    _SessionViewInfo.Fields = _newmap.Fields;
                    _SessionViewInfo.PageSize = _newmap.PageSize;
                    _SessionViewInfo.LinksTop = _newmap.LinksTop;
                    _SessionViewInfo.IsMasterView = _newmap.IsMasterView;
                    _SessionViewInfo.ViewMetaData = _newmap.ViewMetaData;
                    _SessionViewInfo.ViewTitle = _newmap.ViewTitle;
                    _SessionViewInfo.GroupedColumns = _newmap.GroupedColumns;
                    _SessionViewInfo.AggregateColumns = _newmap.AggregateColumns;
                    _SessionViewInfo.AutoCount = _newmap.AutoCount;
                    _SessionViewInfo.AutoLoad = _newmap.AutoLoad;
                    if (_SessionViewInfo.AutoLoad == "0")
                    {
                        _SessionViewInfo.DefaultAutoLoad = false;
                    }
                    else
                    {
                        _SessionViewInfo.DefaultAutoLoad = true;
                    }
                    _SessionViewInfo.RawViewId = _newmap.ViewId;
                    _SessionViewInfo.ViewRecordOpen = _newmap.ViewRecordOpen;
                    //_SessionViewInfo.PercentageColumns = _newmap.PercentColumns;
                    _SessionViewInfo.Section = Section;

                    //ViewsInEvaluatedOrder.Add(_newmap.ViewId);

                    _SessionViewInfo.ViewType = _viewType;

                    _SessionViewInfo.Index = _newmap.Index;
                    _SessionViewInfo.DisplayIndex = _newmap.DisplayIndex;

                    // _SessionViewInfo.MasterViewKey = _MasterViewKey;
                    if (string.IsNullOrEmpty(_newmap.ParentViewId))
                    {
                        _SessionViewInfo.MasterViewKey = _newmap.ViewKey;
                    }
                    else
                    {
                        _SessionViewInfo.MasterViewKey = _newmap.ParentViewId;
                    }

                    //_SessionViewInfo.MasterViewKey = _newmap.ParentViewId;
                    _SessionViewInfo.Columns = _newmap.Columns;
                    _SessionViewInfo.Sorts = _newmap.Sorts;
                    Util.SetSessionValue(Key + "_" + "rptview_" + _newmap.ViewKey, _newmap);
                    //_SessionViewInfo.View = _newmap;

                    _SessionViewInfo.Index = _newmap.Index;
                    _SessionViewInfo.DisplayIndex = _newmap.DisplayIndex;

                    _SessionViewInfo.MapLatitudeField = _newmap.MapLatitudeField;
                    _SessionViewInfo.MapLongitudeField = _newmap.MapLongitudeField;
                    _SessionViewInfo.MapIconField = _newmap.MapIconField; 
                    _SessionViewInfo.ShowLines_Markers = _newmap.ShowLines_Markers; 


                    Util.SetSessionValue(Key + "_" + _newmap.ViewKey, _SessionViewInfo);
                }
                else
                {
                    SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + _newmap.ViewKey);
                    _SessionViewInfo.Columns = _newmap.Columns;
                    _SessionViewInfo.Sorts = _newmap.Sorts;
                    //_SessionViewInfo.View = _newrpt;
                    System.Web.HttpContext.Current.Session[Key + "_" + "rptview_" + _newmap.ViewKey] = _newmap;
                    _SessionViewInfo.GroupedColumns = _newmap.GroupedColumns;
                    _SessionViewInfo.AggregateColumns = _newmap.AggregateColumns;
                    Util.SetSessionValue(Key + "_" + _newmap.ViewKey, _SessionViewInfo);
                }

                if (ViewTables.Contains(_newmap.TableName) == false)
                {
                    ViewTables.Add(_newmap.TableName);
                }

                ViewsInEvaluatedOrder.Add(_newmap.ViewId);

                ViewIDs = ViewIDs + "," + _newmap.ViewId;
                ViewIDsWith_IsTabView = ViewIDsWith_IsTabView + "," + _newmap.ViewId + "|" + _newmap.IsTabView;
                Views.Add(_newmap);

                if (i == 1 && Views.Count == 1)
                {
                    DefaultViewId = _newmap.ViewId;
                }
                if (_isTabView == false)
                    SetClientViewIds(_newmap.ViewId, _newmap.DefaultViewCondition);
                else
                {
                    if (_isLoadFromSession == false && i == 1 && (_newmap.DefaultViewCondition.ToLower().Contains("<%selectedrecordid") || _newmap.DefaultViewCondition.ToLower().Contains("<%selectedviewrecordid")))
                    {
                        Util.SetSessionValue(Key + "_" + "SelectedTabViewId", _newmap.ViewId.Replace(" ", ""));
                    }
                }

                SetViewId_and_Type(_newmap.ViewId, "map");

                //Report _newrpt = new Report(_viewId, _isTabView, Section, DesktopMetaData, i, _isLoadFromSession, Views.Count);
                //if (gcViewMetadata.Contains(_viewId) == false && !string.IsNullOrEmpty(_viewId))
                //{
                //    gcViewMetadata.Add(_newrpt.ViewMetaData, _viewId);
                //    gcViewState.Add("", _viewId);
                //}
                //_newrpt.ViewType = _viewType;

                //if (_newrpt.IsTabView)
                //{
                //    _newrpt.DisplayIndex = _newrpt.OrderIndex + ViewCount;
                //}
                //else
                //{
                //    _newrpt.DisplayIndex = _newrpt.OrderIndex;
                //}

                //if (System.Web.HttpContext.Current.Session[_newrpt.ViewKey] == null || _isLoadFromSession == false)
                //{
                //    //Assign Session variables                
                //    SessionViewInfo _SessionViewInfo = new SessionViewInfo();

                //    _SessionViewInfo.Toprec = string.Empty;
                //    _SessionViewInfo.Bottomrec = string.Empty;
                //    _SessionViewInfo.PreviousPageno = 1;

                //    _SessionViewInfo.ViewCondition = _newrpt.DefaultViewCondition;
                //    _SessionViewInfo.TableName = _newrpt.TableName;
                //    _SessionViewInfo.SortText = _newrpt.DefaultSortCondition;
                //    _SessionViewInfo.Fields = _newrpt.Fields;
                //    _SessionViewInfo.PageSize = _newrpt.PageSize;
                //    _SessionViewInfo.LinksTop = _newrpt.LinksTop;
                //    _SessionViewInfo.IsMasterView = _newrpt.IsMasterView;
                //    _SessionViewInfo.ViewMetaData = _newrpt.ViewMetaData;
                //    _SessionViewInfo.ViewTitle = _newrpt.ViewTitle;
                //    _SessionViewInfo.GroupedColumns = _newrpt.GroupedColumns;
                //    _SessionViewInfo.AggregateColumns = _newrpt.AggregateColumns;
                //    _SessionViewInfo.AutoCount = _newrpt.AutoCount;
                //    _SessionViewInfo.RawViewId = _newrpt.ViewId;
                //    _SessionViewInfo.ViewRecordOpen = _newrpt.ViewRecordOpen;
                //    _SessionViewInfo.PercentageColumns = _newrpt.PercentColumns;
                //    _SessionViewInfo.Section = Section;

                //    ViewsInEvaluatedOrder.Add(_newrpt.ViewId);

                //    _SessionViewInfo.ViewType = _viewType;

                //    _SessionViewInfo.Index = _newrpt.Index;
                //    _SessionViewInfo.DisplayIndex = _newrpt.DisplayIndex;

                //    // _SessionViewInfo.MasterViewKey = _MasterViewKey;
                //    if (string.IsNullOrEmpty(_newrpt.ParentViewId))
                //    {
                //        _SessionViewInfo.MasterViewKey = _newrpt.ViewKey;
                //    }
                //    else
                //    {
                //        _SessionViewInfo.MasterViewKey = _newrpt.ParentViewId;
                //    }

                //    //_SessionViewInfo.MasterViewKey = _newrpt.ParentViewId;
                //    _SessionViewInfo.Columns = _newrpt.Columns;
                //    _SessionViewInfo.Sorts = _newrpt.Sorts;
                //    System.Web.HttpContext.Current.Session["rptview_" + _newrpt.ViewKey] = _newrpt;
                //    //_SessionViewInfo.View = _newrpt;

                //    _SessionViewInfo.Index = _newrpt.Index;
                //    _SessionViewInfo.DisplayIndex = _newrpt.DisplayIndex;

                //    _SessionViewInfo.MapLatitudeField = goTR.StrRead(_newrpt.ViewMetaData, "MAP_LATITUDEFIELD", "");
                //    _SessionViewInfo.MapLongitudeField = goTR.StrRead(_newrpt.ViewMetaData, "MAP_LONGITUDEFIELD", "");

                //    System.Web.HttpContext.Current.Session[_newrpt.ViewKey] = _SessionViewInfo;
                //}
                //else
                //{
                //    SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(_newrpt.ViewKey);
                //    _SessionViewInfo.Columns = _newrpt.Columns;
                //    _SessionViewInfo.Sorts = _newrpt.Sorts;
                //    //_SessionViewInfo.View = _newrpt;
                //    System.Web.HttpContext.Current.Session["rptview_" + _newrpt.ViewKey] = _newrpt;
                //    _SessionViewInfo.GroupedColumns = _newrpt.GroupedColumns;
                //    _SessionViewInfo.AggregateColumns = _newrpt.AggregateColumns;
                //    System.Web.HttpContext.Current.Session[_newrpt.ViewKey] = _SessionViewInfo;
                //}

                //if (ViewTables.Contains(_newrpt.TableName) == false)
                //{
                //    ViewTables.Add(_newrpt.TableName);
                //}

                //ViewIDs = ViewIDs + "," + _newrpt.ViewId;
                //ViewIDsWith_IsTabView = ViewIDsWith_IsTabView + "," + _newrpt.ViewId + "|" + _newrpt.IsTabView;
                //Views.Add(_newrpt);

                //if (i == 1 && Views.Count == 1)
                //{
                //    DefaultViewId = _newrpt.ViewId;
                //}

                //SetViewId_and_Type(_newrpt.ViewId, "map");
            }

            else if (_viewType.ToLower() == "chart")
            {

                //get the parent viewId of the chart
                TempView _tempView = TempViews.ToList<TempView>().Find(t => t.ViewId.Replace(" ", "") == _viewId.Replace(" ", ""));
                if (_tempView != null)
                {
                    _MasterViewKey = _tempView.ParentViewId;
                }
                else
                {
                    _MasterViewKey = "";
                }

                Chart _newChart = new Chart(_viewId, _isTabView, Section, DesktopMetaData, i, _isLoadFromSession, Views.Count, _MasterViewKey, Key);

                _newChart.Key = Key;

                if (gcViewMetadata.Contains(_viewId) == false && !string.IsNullOrEmpty(_viewId))
                {
                    gcViewMetadata.Add(_newChart.ViewMetaData, _viewId);
                    gcViewState.Add("", _viewId);
                }
                if (_newChart.IsTabView)
                {
                    _newChart.DisplayIndex = _newChart.OrderIndex + ViewCount;
                }
                else
                {
                    _newChart.DisplayIndex = _newChart.OrderIndex;
                }

                int tempint = 0;
                int.TryParse(goTR.StrRead(DesktopMetaData, "VIEW" + i.ToString() + "HEIGHT", "NONE", false), out tempint);
                _newChart.ViewHeight = tempint;
                _newChart.ViewHeight = 200 + _newChart.ViewHeight;

                if (Util.GetSessionValue(Key + "_" + _newChart.ViewKey) == null || _isLoadFromSession == false)
                {                   
                    //Assign Session variables                
                    SessionViewInfo _SessionViewInfo = new SessionViewInfo();
                    _SessionViewInfo.Toprec = string.Empty;
                    _SessionViewInfo.Bottomrec = string.Empty;
                    _SessionViewInfo.PreviousPageno = 1;
                    _SessionViewInfo.ViewType = _viewType;
                    _SessionViewInfo.ViewCondition = _newChart.DefaultViewCondition;
                    _SessionViewInfo.TableName = _newChart.TableName;
                    _SessionViewInfo.SortText = _newChart.DefaultSortCondition;
                    _SessionViewInfo.Fields = _newChart.Fields;
                    _SessionViewInfo.PageSize = _newChart.PageSize;
                    _SessionViewInfo.LinksTop = _newChart.LinksTop;
                    _SessionViewInfo.IsMasterView = _newChart.IsMasterView;
                    _SessionViewInfo.ViewMetaData = _newChart.ViewMetaData;
                    _SessionViewInfo.ViewTitle = _newChart.ViewTitle;
                    _SessionViewInfo.AutoCount = _newChart.AutoCount;
                    _SessionViewInfo.AutoLoad = _newChart.AutoLoad;
                    if (_SessionViewInfo.AutoLoad == "0")
                    {
                        _SessionViewInfo.DefaultAutoLoad = false;
                    }
                    else
                    {
                        _SessionViewInfo.DefaultAutoLoad = true;
                    }
                    _SessionViewInfo.RawViewId = _newChart.ViewId;

                    _SessionViewInfo.Section = Section;

                    //ViewsInEvaluatedOrder.Add(_newChart.ViewId);

                    if (_newChart.IsMasterView && Views.Count == 0)
                    {
                        _MasterViewKey = _newChart.ViewKey;
                    }
                    _SessionViewInfo.MasterViewKey = _MasterViewKey;
                    //_SessionViewInfo.Columns = _newChart.Columns;
                    //_SessionViewInfo.Sorts = _newChart.Sorts;

                    //chart properties..J
                    _SessionViewInfo.ChartSorts = _newChart.Sorts;
                    _SessionViewInfo.Chartname = _newChart.Chartname;
                    _SessionViewInfo.CTitle = _newChart.CTitle;
                    _SessionViewInfo.Title = _newChart.Title;
                    _SessionViewInfo.Series = _newChart.Series;

                    _SessionViewInfo.Format = _newChart.Format;
                    _SessionViewInfo.VisibleLegend = _newChart.VisibleLegend;
                    _SessionViewInfo.Xaxis = _newChart.Xaxis;
                    _SessionViewInfo.XaxisValuesUsedInToolTip = _newChart.XaxisValuesUsedInToolTip;
                    _SessionViewInfo.SeriesNames = _newChart.SeriesNames;

                    _SessionViewInfo.YaxisLabel = _newChart.YaxisLabel;
                    _SessionViewInfo.XaxisLabel = _newChart.XaxisLabel;
                    _SessionViewInfo.SeriesColors = _newChart.SeriesColors;
                    _SessionViewInfo.ShowGoleLine = _newChart.ShowGoleLine;

                    _SessionViewInfo.GoleLineValue = _newChart.GoleLineValue;
                    _SessionViewInfo.MaxValue = _newChart.MaxValue;
                    _SessionViewInfo.ViewID = _newChart.ViewID;
                    _SessionViewInfo.PieSeries = _newChart.PieSeries;

                    _SessionViewInfo.GraphXLabel = _newChart.GraphXLabel;
                    _SessionViewInfo.gcSorts = _newChart.SortFields;
                    //_SessionViewInfo.gcSorts = _newChart.gcSorts;
                    _SessionViewInfo.gcChartData = _newChart.gcChartData;
                    _SessionViewInfo.gcChartFilters = _newChart.gcChartFilters;
                    _SessionViewInfo.RollupTotal = _newChart.RollupTotal;


                    _SessionViewInfo.Index = _newChart.Index;
                    _SessionViewInfo.DisplayIndex = _newChart.DisplayIndex;


                    Util.SetSessionValue(Key + "_" + _newChart.ViewKey, _SessionViewInfo);
                    Util.SetSessionValue(Key + "_" + "chartView_" + _newChart.ViewKey, _newChart);
                }
                else
                {
                    SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + _newChart.ViewKey);
                    
                    //_SessionViewInfo.Columns = _newChart.Columns;
                    //_SessionViewInfo.Sorts = _newChart.Sorts;
                    Util.SetSessionValue(Key + "_" + _newChart.ViewKey, _SessionViewInfo);
                    Util.SetSessionValue(Key + "_" + "chartView_" + _newChart.ViewKey, _newChart);
                }
                ViewIDs = ViewIDs + "," + _newChart.ViewId;
                ViewIDsWith_IsTabView = ViewIDsWith_IsTabView + "," + _newChart.ViewId + "|" + _newChart.IsTabView;
                Views.Add(_newChart);

                if (i == 1 && Views.Count == 1)
                {
                    DefaultViewId = _newChart.ViewId;
                }

                if (ViewTables.Contains(_newChart.TableName) == false)
                {
                    ViewTables.Add(_newChart.TableName);
                }

                ViewsInEvaluatedOrder.Add(_newChart.ViewId);

                SetViewId_and_Type(_newChart.ViewId, "chart");

                if (_isTabView == false)
                    SetClientViewIds(_newChart.ViewId, _newChart.DefaultViewCondition);
                else
                {
                    if (_isLoadFromSession == false && i == 1 && (_newChart.DefaultViewCondition.ToLower().Contains("<%selectedrecordid") || _newChart.DefaultViewCondition.ToLower().Contains("<%selectedviewrecordid")))
                    {
                        Util.SetSessionValue(Key + "_" + "SelectedTabViewId", _newChart.ViewId.Replace(" ", ""));
                    }
                }
            }
            else if (_viewType.ToLower() == "calweek" || _viewType.ToLower() == "calday" || _viewType.ToLower() == "calmonth")
            {
                Calendar _newCalendar = new Calendar(_viewId, _isTabView, Section, DesktopMetaData, i, _isLoadFromSession, Views.Count, Key);

                _newCalendar.Key = Key;

                if (gcViewMetadata.Contains(_viewId) == false && !string.IsNullOrEmpty(_viewId))
                {
                    gcViewMetadata.Add(_newCalendar.ViewMetaData, _viewId);
                    gcViewState.Add("", _viewId);
                }
                if (_newCalendar.IsTabView)
                {
                    _newCalendar.DisplayIndex = _newCalendar.OrderIndex + ViewCount;
                }
                else
                {
                    _newCalendar.DisplayIndex = _newCalendar.OrderIndex;
                }

                if (Util.GetSessionValue(Key + "_" + _newCalendar.ViewKey) == null || _isLoadFromSession == false)
                {
                    SessionViewInfo _SessionViewInfo = new SessionViewInfo();
                    _SessionViewInfo.Toprec = string.Empty;
                    _SessionViewInfo.Bottomrec = string.Empty;
                    _SessionViewInfo.PreviousPageno = 1;

                    _SessionViewInfo.ViewCondition = _newCalendar.DefaultViewCondition;
                    _SessionViewInfo.TableName = _newCalendar.TableName;
                    _SessionViewInfo.SortText = _newCalendar.DefaultSortCondition;
                    _SessionViewInfo.Fields = _newCalendar.Fields;
                    _SessionViewInfo.PageSize = _newCalendar.PageSize;
                    _SessionViewInfo.LinksTop = _newCalendar.LinksTop;
                    _SessionViewInfo.IsMasterView = _newCalendar.IsMasterView;
                    _SessionViewInfo.ViewMetaData = _newCalendar.ViewMetaData;
                    _SessionViewInfo.ViewTitle = _newCalendar.ViewTitle;
                    _SessionViewInfo.AutoCount = _newCalendar.AutoCount;
                    _SessionViewInfo.AutoLoad = _newCalendar.AutoLoad;
                    if (_SessionViewInfo.AutoLoad == "0")
                    {
                        _SessionViewInfo.DefaultAutoLoad = false;
                    }
                    else
                    {
                        _SessionViewInfo.DefaultAutoLoad = true;
                    }
                    _SessionViewInfo.RawViewId = _newCalendar.ViewId;
                    _SessionViewInfo.IsInvalid = _newCalendar.IsInvalid;

                    _SessionViewInfo.Section = Section;


                    //ViewsInEvaluatedOrder.Add(_newCalendar.ViewId);


                    if (_newCalendar.IsMasterView && Views.Count == 0)
                    {
                        _MasterViewKey = _newCalendar.ViewKey;
                    }
                    _SessionViewInfo.MasterViewKey = _MasterViewKey;
                    _SessionViewInfo.FieldDefs = _newCalendar.Columns;
                    // _SessionViewInfo.Sorts = _newCalendar.Sorts;
                    _SessionViewInfo.ViewType = _viewType;

                    _SessionViewInfo.Index = _newCalendar.Index;
                    _SessionViewInfo.DisplayIndex = _newCalendar.DisplayIndex;

                    _SessionViewInfo.TopViewCount = ViewCount;
                    _SessionViewInfo.TabViewCount = TabViewCount;


                    string gsViewState = GetViewStateMetadata(_newCalendar.ViewId);
                    sSelRec = goTR.StrRead(gsViewState, "SELECTEDRECORDID", "");

                    if (goTR.StrRead(gsViewState, "LOADED", "0") == "0" || goTR.StrRead(gsViewState, "NEWPROPERTIES") == "1")
                    {
                        if (sSelRec == "")
                        {
                            sSelRec = goTR.StrRead(_newCalendar.ViewMetaData, "AUTOSELECT", "");
                            if (sSelRec != "")
                            {
                                switch (sSelRec)
                                {
                                    case "NONELINE":
                                        sSelRec = goData.GenSUIDFake(_newCalendar.TableName);
                                        break;
                                    case "ANYLINE":
                                        sSelRec = goData.GenSUIDAnyFake(_newCalendar.TableName);
                                        break;
                                    default:
                                        string par_sFileName = "";
                                        object par_oRowset = null;
                                        sSelRec = goTR.GetLineValue(ref sSelRec, ref par_sFileName, "", false, ref par_oRowset);
                                        break;
                                }
                            }
                        }
                    }

                    if (!string.IsNullOrEmpty(sSelRec))
                    {
                        _newCalendar.IsSelectSpecificRecord = true;
                        _newCalendar.SelectSpecificRecordId = sSelRec;
                        _SessionViewInfo.IsSelectSpecificRecord = true;
                        _SessionViewInfo.SelectSpecificRecordId = sSelRec;
                        _SessionViewInfo.LastSelectedRecord = sSelRec;

                    }
                    else
                    {
                        _SessionViewInfo.IsSelectSpecificRecord = false;
                        _SessionViewInfo.SelectSpecificRecordId = null;
                        _SessionViewInfo.LastSelectedRecord = null;
                    }

                    
                    Util.SetSessionValue(Key + "_" + _newCalendar.ViewKey, _SessionViewInfo);

                }

                else
                {
                    SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + _newCalendar.ViewKey);
                    _SessionViewInfo.FieldDefs = _newCalendar.Columns;

                    //sSelRec = goTR.StrRead(_SessionViewInfo.ViewMetaData, "AUTOSELECT", "");
                    //if (sSelRec != "")
                    //{
                    //    switch (sSelRec)
                    //    {
                    //        case "NONELINE":
                    //            sSelRec = goData.GenSUIDFake(_newCalendar.TableName);
                    //            break;
                    //        case "ANYLINE":
                    //            sSelRec = goData.GenSUIDAnyFake(_newCalendar.TableName);
                    //            break;
                    //        default:
                    //            string par_sFileName = "";
                    //            object par_oRowset = null;
                    //            sSelRec = goTR.GetLineValue(ref sSelRec, ref par_sFileName, "", false, ref par_oRowset);
                    //            break;
                    //    }
                    //}

                    //if (!string.IsNullOrEmpty(sSelRec))
                    //{
                    //    _newCalendar.IsSelectSpecificRecord = true;
                    //    _newCalendar.SelectSpecificRecordId = sSelRec;
                    //    _SessionViewInfo.IsSelectSpecificRecord = true;
                    //    _SessionViewInfo.SelectSpecificRecordId = sSelRec;
                    //    _SessionViewInfo.LastSelectedRecord = sSelRec;
                    //}

                    Util.SetSessionValue(Key + "_" + "Calview_" + _newCalendar.ViewKey, _newCalendar);
                    //  _SessionViewInfo.GroupedColumns = _newCalendar.GroupedColumns;
                    //  _SessionViewInfo.AggregateColumns = _newCalendar.AggregateColumns;
                    Util.SetSessionValue(Key + "_" + _newCalendar.ViewKey, _SessionViewInfo);
                }


                ViewIDs = ViewIDs + "," + _newCalendar.ViewId;
                ViewIDsWith_IsTabView = ViewIDsWith_IsTabView + "," + _newCalendar.ViewId + "|" + _newCalendar.IsTabView;
                Views.Add(_newCalendar);

                if (i == 1 && Views.Count == 1)
                {
                    DefaultViewId = _newCalendar.ViewId;
                }

                if (ViewTables.Contains(_newCalendar.TableName) == false)
                {
                    ViewTables.Add(_newCalendar.TableName);
                }

                ViewsInEvaluatedOrder.Add(_newCalendar.ViewId);

                SetViewId_and_Type(_newCalendar.ViewId, "calendar");

                if (_isTabView == false)
                    SetClientViewIds(_newCalendar.ViewId, _newCalendar.DefaultViewCondition);
                else
                {
                    if (_isLoadFromSession == false && i == 1 && (_newCalendar.DefaultViewCondition.ToLower().Contains("<%selectedrecordid") || _newCalendar.DefaultViewCondition.ToLower().Contains("<%selectedviewrecordid")))
                    {
                        Util.SetSessionValue(Key + "_" + "SelectedTabViewId", _newCalendar.ViewId.Replace(" ", ""));
                    }
                }

            }
            else if (_viewType.ToUpper() == "DBVIEW")
            {
                DBView _newDBView = new DBView();
                _newDBView.ViewType = _viewType;
                _newDBView.ViewId = _viewId;
                _newDBView.ViewKey = _viewId.Replace(" ", "");
                _newDBView.ViewMetaData = goMeta.PageRead(Section, _viewId);
                _newDBView.StoredProcedureName = goTR.StrRead(_newDBView.ViewMetaData, "SPNAME", "");
                _newDBView.TabLabelName = goTR.StrRead(_newDBView.ViewMetaData, "TABLABEL", "");
                _newDBView.Index = i;
                _newDBView.Name = goTR.StrRead(_newDBView.ViewMetaData, "NAME", "0");

                _newDBView.ColumnNumber = Convert.ToInt32(goTR.StrRead(DesktopMetaData, "VIEW" + (i).ToString() + "COLUMN", "1"));

                ViewIDs = ViewIDs + "," + _newDBView.ViewId;
                ViewIDsWith_IsTabView = ViewIDsWith_IsTabView + "," + _newDBView.ViewId + "|" + _newDBView.IsTabView;
                Views.Add(_newDBView);

                if (i == 1 && Views.Count == 1)
                {
                    DefaultViewId = _newDBView.ViewId;
                }

                SetViewId_and_Type(_newDBView.ViewId, "dbview");
                if (Util.GetSessionValue(Key + "_" + _newDBView.ViewKey) == null || _isLoadFromSession == false)
                {
                    SessionViewInfo _SessionViewInfo = new SessionViewInfo();
                    _SessionViewInfo.Toprec = string.Empty;
                    _SessionViewInfo.Bottomrec = string.Empty;
                    _SessionViewInfo.PreviousPageno = 1;

                    _SessionViewInfo.ViewMetaData = _newDBView.ViewMetaData;
                    _SessionViewInfo.RawViewId = _newDBView.ViewId;
                    _SessionViewInfo.Section = Section;
                    ViewsInEvaluatedOrder.Add(_newDBView.ViewId);
                    _SessionViewInfo.ViewType = _viewType;

                    _SessionViewInfo.Index = _newDBView.Index;

                    Util.SetSessionValue(_newDBView.ViewKey, _SessionViewInfo);

                }
                else 
                {
                    SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + _newDBView.ViewKey);
                    Util.SetSessionValue(Key + "_" + _newDBView.ViewKey, _SessionViewInfo);
                }
            }
        }

        private void SetClientViewIds(string ViewId, string FilterText)
        {
            if (FilterText.ToLower().Contains("<%selectedrecordid"))
            {
                if (string.IsNullOrEmpty(ClientViewIds))
                {
                    ClientViewIds = ViewId.Replace(" ", "");
                }
                else
                {
                    ClientViewIds = ClientViewIds + "," + ViewId.Replace(" ", "");
                }
            }
            if (FilterText.ToLower().Contains("<%selectedviewrecordid"))
            {
                if (string.IsNullOrEmpty(ClientViewIdsOnFocus))
                {
                    ClientViewIdsOnFocus = ViewId.Replace(" ", "");
                }
                else
                {
                    ClientViewIdsOnFocus = ClientViewIdsOnFocus + "," + ViewId.Replace(" ", "");
            }
        }

            if (!FilterText.ToLower().Contains("<%selectedrecordid") && !FilterText.ToLower().Contains("<%selectedviewrecordid"))
            {
                if (string.IsNullOrEmpty(IndependentViewIds))
                {
                    IndependentViewIds = ViewId.Replace(" ", "");
                }
                else
                {
                    IndependentViewIds = IndependentViewIds + "," + ViewId.Replace(" ", "");
                }
            }
        }
        private void SetViewId_and_Type(string ViewId, string ViewType)
        {
            if (string.IsNullOrEmpty(ViewId_and_Types))
            {
                ViewId_and_Types = ViewId.Replace(" ", "") + "," + ViewType;
            }
            else
            {
                ViewId_and_Types = ViewId_and_Types + "|" + ViewId.Replace(" ", "") + "," + ViewType;
            }
        }
        private void SetViewSizes()
        {

            var col1views = from v in Views
                            where v.ColumnNumber == 1 && v.IsTabView == false
                            select v;

            var col2views = from v in Views
                            where v.ColumnNumber == 2 && v.IsTabView == false
                            select v;

            int browserheight = HttpContext.Current.Request.Browser.ScreenPixelsHeight;// -70;

            SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + "GeneralInfo");




            //if (_SessionViewInfo != null && _SessionViewInfo.BrowserHeight != 0)
            //{
            //    browserheight = _SessionViewInfo.BrowserHeight;
            //}



            //Manage the width & height of each view
            if (col1views.Count() > 0 && col2views.Count() > 0)
            {

                int col2width = Convert.ToInt32(goTR.StrRead(DesktopMetaData, "COLUMN2WIDTH", 0));
                double tempwidth = (12 * col2width) / 100;
                col2width = Convert.ToInt32(Math.Round(tempwidth));
                int col1width = 12 - col2width;

                Col1Width = col1width;
                Col2Width = col2width;

                int _TABCOUNT = Convert.ToInt32(goTR.StrRead(DesktopMetaData, "TABCOUNT", 0));
                int _TABHEIGHT = Convert.ToInt32(goTR.StrRead(DesktopMetaData, "TABHEIGHT", 0));
                int totalheight1 = 500;
                int totalheight2 = 500;

                TabHeight = _TABHEIGHT;

                totalheight1 = totalheight2 = browserheight;
                _TABHEIGHT = (totalheight1 * _TABHEIGHT / 100);

                if (_TABCOUNT > 0)
                {
                    totalheight1 = totalheight2 = totalheight1 - _TABHEIGHT;
                }

                if (col1views.Count() < col2views.Count())
                {
                    // totalheight1 = totalheight1 + ((col2views.Count() - col1views.Count()) * 50);
                    totalheight2 = totalheight2 - ((col2views.Count() - col1views.Count()) * 50);
                }

                for (int i = 0; i < col1views.Count(); i++)
                {
                    if (col1views.ElementAt(i).IsTabView)
                        col1views.ElementAt(i).Height = _TABHEIGHT;
                    else
                        col1views.ElementAt(i).Height = totalheight1 / col1views.Count();
                }

                for (int i = 0; i < col2views.Count(); i++)
                {
                    if (col2views.ElementAt(i).IsTabView)
                        col2views.ElementAt(i).Height = _TABHEIGHT;
                    else
                        col2views.ElementAt(i).Height = totalheight2 / col2views.Count();
                }

            }
            else  //single column
            {
                int _VIEWCOUNT = Convert.ToInt32(goTR.StrRead(DesktopMetaData, "VIEWCOUNT", 0));
                int _TABCOUNT = Convert.ToInt32(goTR.StrRead(DesktopMetaData, "TABCOUNT", 0));
                int _TABHEIGHT = Convert.ToInt32(goTR.StrRead(DesktopMetaData, "TABHEIGHT", 0));
               

                TabHeight = _TABHEIGHT;

                int _height = 500;
                _height = browserheight;

                int _Viewheight = _height;
                int _Tabheight = 500;

                if (_TABCOUNT > 0)
                {
                    _Tabheight = (_height * _TABHEIGHT / 100);
                    _Viewheight = _height - _Tabheight;
                }

                if (col1views.Count() > 0)
                {
                    Col1Width = 12;
                    for (int i = 0; i < col1views.Count(); i++)
                    {
                        //col1views.ElementAt(i).Height = 500 / col1views.Count();
                        if (col1views.ElementAt(i).IsTabView == false)
                        {
                            col1views.ElementAt(i).Height = _Viewheight / _VIEWCOUNT;
                        }
                        else
                        {
                            col1views.ElementAt(i).Height = _Tabheight;
                        }
                    }
                }
                else if (col2views.Count() > 0)
                {
                    Col2Width = 12;
                    for (int i = 0; i < col2views.Count(); i++)
                    {
                        //col1views.ElementAt(i).Height = 500 / col1views.Count();
                        if (col2views.ElementAt(i).IsTabView == false)
                        {
                            col2views.ElementAt(i).Height = _Viewheight / _VIEWCOUNT;
                        }
                        else
                        {
                            col2views.ElementAt(i).Height = _Tabheight;
                        }
                    }
                }

            }

        }
        private void SetViewDependencies()
        {
            foreach (var _tableName in ViewTables)
            {
                for (int i = 0; i < (ViewCount + TabViewCount); i++)
                {
                    string sCondtion = Views[i].DefaultViewCondition;
                    string sSearchPattern1 = ("<%SelectedRecordID FILE=" + _tableName + "%>").ToLower();
                    string sSearchPattern2 = ("<%SelectedViewRecordID FILE=" + _tableName + "%>").ToLower();

                    if (sCondtion.ToLower().Contains(sSearchPattern1))
                    {
                        var _parentViews = Views.ToList<View>().FindAll(v => v.TableName.ToLower() == _tableName.ToString().ToLower());

                        if (_parentViews != null)
                        {

                            //set dependecy view Ids
                            foreach (var _parentview in _parentViews)
                            {
                                if (string.IsNullOrEmpty(_parentview.DependencyViewIds))
                                {
                                    _parentview.DependencyViewIds = Views[i].ViewId.Replace(" ", "");
                                }
                                else
                                {
                                    _parentview.DependencyViewIds = _parentview.DependencyViewIds + "," + Views[i].ViewId.Replace(" ", "");
                                }
                                //_parentview.IsParent = true;

                                //Set isparent book mark it will be used in loading datasets in go function, paging, navigation and sorting..J
                                ((SessionViewInfo)Util.GetSessionValue(Key + "_" + _parentview.ViewId.Replace(" ", ""))).IsParent = true;
                                //((SessionViewInfo)Util.GetSessionValue(Key + "_" + _parentview.ViewId.Replace(" ", ""))).DependencyViewIds = _parentview.DependencyViewIds;
                            }

                           
                            //set the parent view Id

                            if (string.IsNullOrEmpty(Views[i].ParentViewId))
                            {
                                Views[i].ParentViewId = _parentViews[0].ViewId.Replace(" ", "");
                            }
                            else
                            {
                                Views[i].ParentViewId = Views[i].ParentViewId + "," + _parentViews[0].ViewId.Replace(" ", "");
                            }

                            _parentViews[0].IsParent = true;

                            ((SessionViewInfo)Util.GetSessionValue(Key + "_" + Views[i].ViewId.Replace(" ", ""))).MasterViewKey = Views[i].ParentViewId;                                                       

                        }


                    }
                    if (sCondtion.ToLower().Contains(sSearchPattern2))
                    {
                        var _parentViews = Views.ToList<View>().FindAll(v => v.TableName.ToLower() == _tableName.ToString().ToLower());

                        if (_parentViews != null)
                        {

                            //set dependecy view Ids
                            foreach (var _parentview in _parentViews)
                            {
                                if (string.IsNullOrEmpty(_parentview.DependencyViewIdsOnFocus))
                                {
                                    _parentview.DependencyViewIdsOnFocus = Views[i].ViewId.Replace(" ", "");
                                }
                                else
                                {
                                    _parentview.DependencyViewIdsOnFocus = _parentview.DependencyViewIdsOnFocus + "," + Views[i].ViewId.Replace(" ", "");
                                }
                                //_parentview.IsParent = true;

                                //Set isparent book mark it will be used in loading datasets in go function, paging, navigation and sorting..J
                                ((SessionViewInfo)Util.GetSessionValue(Key + "_" + _parentview.ViewId.Replace(" ", ""))).IsParent = true;
                                //((SessionViewInfo)Util.GetSessionValue(Key + "_" + _parentview.ViewId.Replace(" ", ""))).DependencyViewIdsOnFocus = _parentview.DependencyViewIdsOnFocus;
                            }


                            //set the parent view Id

                            if (string.IsNullOrEmpty(Views[i].ParentViewId))
                            {
                                Views[i].ParentViewId = _parentViews[0].ViewId.Replace(" ", "");
                            }
                            else
                            {
                                Views[i].ParentViewId = Views[i].ParentViewId + "," + _parentViews[0].ViewId.Replace(" ", "");
                            }

                            _parentViews[0].IsParent = true;

                            ((SessionViewInfo)Util.GetSessionValue(Key + "_" + Views[i].ViewId.Replace(" ", ""))).MasterViewKey = Views[i].ParentViewId;

                        }


                    }
                }
            }


            //foreach (View _view in Views)
            //{
            //    var _check1 = Views.ToList<View>().FindAll(v => v.ParentViewId.Contains(_view.ViewId.Replace(" ", "")));
            //    if (_check1 != null && _check1.Count > 0)
            //    {
            //        _view.IsParent = true;
            //    }
            //}

            var list = Views.ToList<View>().FindAll(v => v.ParentViewId == null || v.ParentViewId == "");
            if (list != null)
            {
                if (list.Count > 0)
                {
                    foreach (var _view in list)
                    {
                        if (string.IsNullOrEmpty(MasterViewIds))
                        {
                            MasterViewIds = _view.ViewId.Replace(" ", "");
                        }
                        else
                        {
                            MasterViewIds = MasterViewIds + ","+ _view.ViewId.Replace(" ", "");
                        }
                    }
                }
            }



            var DRSEnabledlist = Views.ToList<View>().FindAll(v => v.IsDRSEnabled == true);
            if (DRSEnabledlist != null)
            {
                if (DRSEnabledlist.Count > 0)
                {
                    foreach (var _view in DRSEnabledlist)
                    {
                        if (string.IsNullOrEmpty(DRSEnabledViewIds))
                        {
                            DRSEnabledViewIds = _view.ViewId.Replace(" ", "");
                        }
                        else
                        {
                            DRSEnabledViewIds = DRSEnabledViewIds + "," + _view.ViewId.Replace(" ", "");
                        }
                    }
                }
            }




            //V2 dependent views collection as per the legacy..J
            int iTopViews = 0;
            int iTabViews = 0;
            Collection cViewFiles = new Collection();
            Collection cViewDependencies =new Collection();
            List<DependentViewsListItem> dependentViewsListItem = new List<DependentViewsListItem>();
            Collection cKeys = new Collection();
            string sCondition;
            string sSearchString1;
            string sSearchString2;
            string sViewTable;
            string sViewID;
            string sViewMeta;

            //load views            
            iTopViews = ViewCount;
            iTabViews = TabViewCount;

            //To differntiate same file name contained views..J 
            Collection mutlipleFileName = new Collection();

            for (int i = 0; i < (iTopViews + iTabViews); i++)
            {
                sViewID = ViewsInEvaluatedOrder[i].ToString();
                sViewMeta = ((SessionViewInfo)Util.GetSessionValue(Key + "_" + sViewID.Replace(" ", ""))).ViewMetaData;//GetViewMetadata(sViewID);
                sViewTable = (goTR.StrRead(sViewMeta, "FILE", "")).ToUpper();
                sCondition = (goTR.StrRead(sViewMeta, "CONDITION", "")).ToUpper();

                //Differntiate same file name contained views..J
                if (cKeys.Contains(sViewTable))
                {
                    if (!mutlipleFileName.Contains(sViewTable))
                    {
                        mutlipleFileName.Add(sViewTable, sViewTable);
                    }                    
                }

                if (!cKeys.Contains(sViewTable))
                {
                    cKeys.Add(sViewTable, sViewTable);
                }
                if (cViewFiles.Contains(sViewTable))
                {
                    string sID = cViewFiles[sViewTable].ToString();
                    cViewFiles.Remove(sViewTable);
                    sViewID = sID + "|" + sViewID;
                }
                cViewFiles.Add(sViewID, sViewTable);              
            }

            //It will be used when selected records are being added in session dictionary..J
            Util.SetSessionValue(Key + "_" + "lstMutlipleFileNames", mutlipleFileName);

            foreach (var viewTable in cKeys)
            {
                for (int i = 0; i < (iTopViews + iTabViews); i++)
                {
                    sViewID = ViewsInEvaluatedOrder[i].ToString();
                    sViewMeta = ((SessionViewInfo)Util.GetSessionValue(Key + "_" + sViewID.Replace(" ", ""))).ViewMetaData;//GetViewMetadata(sViewID);
                    sCondition = (goTR.StrRead(sViewMeta, "CONDITION", "")).ToUpper();
                    sSearchString1 = ("<%SelectedRecordID FILE=" + viewTable + "%>").ToUpper();
                    sSearchString2 = ("<%SelectedViewRecordID FILE=" + viewTable + "%>").ToUpper();

                    if (sCondition.Contains(sSearchString1))
                    {
                        string sViews = cViewFiles[viewTable].ToString();
                        string[] aViews = sViews.Split('|');
                        string sView;
                        foreach (var sViewItem in aViews)
                        {
                            sView = sViewItem;
                            if (sView != "")
                            {
                                if (sView != sViewID) //let's not make a view dependent on itself
                                {
                                    //Avoid if view has multiple dependencies with same GID and Tablename to skip circular reference..J
                                    string _sViewMeta = ((SessionViewInfo)Util.GetSessionValue(Key + "_" + sView.Replace(" ", ""))).ViewMetaData;//GetViewMetadata(sViewID);
                                    string _sCondition = (goTR.StrRead(_sViewMeta, "CONDITION", "")).ToUpper();

                                    if (!_sCondition.Contains(sSearchString1))
                                    {
                                        if (cViewDependencies.Contains(sViewID))
                                        {
                                            string sID = cViewDependencies[sViewID].ToString();
                                            cViewDependencies.Remove(sViewID);
                                            sView = sID + "|" + sView;
                                        }
                                        cViewDependencies.Add(sView, sViewID);
                                        dependentViewsListItem.Add(new DependentViewsListItem
                                        {
                                            ChildViews = sViewID,
                                            ParentViews = sView
                                        });
                                    }                                    
                                }
                            }
                        }
                    }

                    if (sCondition.Contains(sSearchString2))
                    {
                        string sViews = cViewFiles[viewTable].ToString();
                        string[] aViews = sViews.Split('|');
                        string sView;
                        foreach (var sViewItem in aViews)
                        { 
                            sView = sViewItem;
                            if (sView != "")
                            {
                                if (sView != sViewID) //let's not make a view dependent on itself
                                {
                                    //Avoid if view has multiple dependencies with same GID and Tablename to skip circular reference..J
                                    string _sViewMeta = ((SessionViewInfo)Util.GetSessionValue(Key + "_" + sView.Replace(" ", ""))).ViewMetaData;//GetViewMetadata(sViewID);
                                    string _sCondition = (goTR.StrRead(_sViewMeta, "CONDITION", "")).ToUpper();

                                    if (!_sCondition.Contains(sSearchString2))
                                    {
                                        if (cViewDependencies.Contains(sViewID))
                                        {
                                            string sID = cViewDependencies[sViewID].ToString();
                                            cViewDependencies.Remove(sViewID);
                                            sView = sID + "|" + sView;
                                        }
                                        cViewDependencies.Add(sView, sViewID);
                                        dependentViewsListItem.Add(new DependentViewsListItem
                                        {
                                            ChildViews = sViewID,
                                            ParentViews = sView
                                        });
                                    }                                   
                                }
                            }
                        }
                    }
                }
            }

            ViewDependencyHeirarchy = cViewDependencies;            

            //Get dependent views for each view..J
            foreach (var view in Views)
            {
                string DependentViews = "";
                
                foreach (var item in dependentViewsListItem)
                {
                    //Check if view is in parent list or not..J
                    if (item.ParentViews.Contains(view.ViewId))
                    {
                        if (DependentViews != "")
                        {
                            //Skip duplicate childviews..J
                            if (!DependentViews.Contains(item.ChildViews.ToString()))
                            {
                                DependentViews = DependentViews + "," + item.ChildViews.Replace(" ","").ToString();
                            }                          
                        }
                        else
                        {
                            DependentViews = item.ChildViews.Replace(" ", "").ToString();                            
                        }
                    }
                }

                if (DependentViews != "")
                {
                    ((SessionViewInfo)Util.GetSessionValue(Key + "_" + view.ViewId.Replace(" ", ""))).DependencyViewIds = DependentViews;
                }  
            }

        }

        private void PrepareViewId_Parents()
        {
            foreach (TempView _view in TempViews)
            {
                if (string.IsNullOrEmpty(ViewId_and_ParentIds))
                {
                    ViewId_and_ParentIds = _view.ViewId.Replace(" ", "") + "~" + _view.ParentViewId;
                }
                else
                {
                    ViewId_and_ParentIds = ViewId_and_ParentIds + "|" + _view.ViewId.Replace(" ", "") + "~" + _view.ParentViewId;
                }
            }
        }
        private void LoadViewDataSets()
        {
            DataTable dt = null;
            SessionViewInfo _sessionViewInfo = null;
            int i = 0;
            for (i = 0; i < ViewCount; i++)
            {
                dt = null;
                _sessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + Views[i].ViewId.Replace(" ", ""));
                if (Views[i].IsActive && (Views[i].ViewType.ToLower() == "list" || Views[i].ViewType.ToLower() == "report"))
                {
                    dt = Util.ReadData(Views[i].ViewKey, "", _sessionViewInfo.SortText, 1, "no");
                    Views[i].dtData = dt;
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        ((SessionViewInfo)Util.GetSessionValue(Key + "_" + Views[i].ViewId.Replace(" ", ""))).Toprec = dt.Rows[0]["GID_ID"].ToString();
                    }

                }
            }

            //load for 1st tab
            if (TabViewCount > 0)
            {
                dt = null;
                _sessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + Views[i + 1].ViewId.Replace(" ", ""));
                dt = Util.ReadData(Views[i + 1].ViewKey, "", _sessionViewInfo.SortText, 1, "no");
                Views[i + 1].dtData = dt;
                if (dt != null && dt.Rows.Count > 0)
                {
                    ((SessionViewInfo)Util.GetSessionValue(Key + "_" + Views[i + 1].ViewId.Replace(" ", ""))).Toprec = dt.Rows[0]["GID_ID"].ToString();
                }
            }

        }
        private string GetViewType(string _viewId, string _desktopmetadata)
        {
            string _viewMetaData = "";
            SessionViewInfo _SessionViewInfo = new SessionViewInfo();
            if (!string.IsNullOrEmpty(_viewId))
            {
                _SessionViewInfo = Util.SessionViewInfo(Key + "_" + _viewId.Replace(" ", ""));
            }
            if (_isLoadFromSession.Equals(true))
            {
                _viewMetaData = _SessionViewInfo.ViewMetaData;

                if (string.IsNullOrEmpty(_viewMetaData))
                {
                    _viewMetaData = goMeta.PageRead(Section, _viewId);
                }
            }
            else
            {
                _viewMetaData = goMeta.PageRead(Section, _viewId);
            }

            string ViewType = goTR.StrRead(_viewMetaData, "TYPE", "");

            switch (ViewType.ToUpper())
            {
                case "LIST":
                    switch (goTR.StrRead(_viewMetaData, "SECTIONSGROUPED", "").ToUpper())
                    {
                        case "1":
                            ViewType = "REPORT";
                            break;
                        default:
                            ViewType = "LIST";
                            break;
                    }
                    break;
                case "CALDAY":
                case "CALWEEK":
                case "CALMONTH":
                    ViewType = goTR.StrRead(_viewMetaData, "TYPE", "LIST").ToUpper();
                    break;
                case "CALYEAR":
                    ViewType = "LIST";
                    break;
                case "CHART":
                    if (_SessionViewInfo.ViewType == "Report")
                    {
                        ViewType = "REPORT";
                    }
                    else
                    {
                        ViewType = "CHART";
                    }

                    break;
                case "REPORT":
                    ViewType = "REPORT";
                    break;
                case "DBVIEW":
                    ViewType = "DBVIEW";
                    break;
                case "MAP":
                    if (_SessionViewInfo.ViewType == "Report")
                    {
                        ViewType = "REPORT";
                    }
                    else
                    {
                        ViewType = "MAP";
                    }

                    //ViewType = "MAP";
                    break;
                default:
                    ViewType = "";
                    break;
            }

            return ViewType;
        }
        public void MessageBox(ref object par_doCallingObject, string par_sMessage, int par_iStyle = clC.SELL_MB_OK, string par_sTitle = "Selltis", string par_sButton1Label = "", string par_sButton2Label = "", string par_sButton3Label = "", string par_sInputDefaultValue = "", string par_sButton1Script = "", string par_sButton2Script = "", string par_sButton3Script = "",

        clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //try
            //{
                gbMBDisplay = true;
                gsMBMessage = par_sMessage;
                giMBStyle = par_iStyle;
                gsMBTitle = par_sTitle;
                gsMBButton1Label = par_sButton1Label;
                gsMBButton2Label = par_sButton2Label;
                gsMBButton3Label = par_sButton3Label;
                gsMBInputDefaultValue = par_sInputDefaultValue;
                gsMBButton1Script = par_sButton1Script;
                gsMBButton2Script = par_sButton2Script;
                gsMBButton3Script = par_sButton3Script;
                gsMBPar1 = par_s1;
                gsMBPar2 = par_s2;
                gsMBPar3 = par_s3;
                gsMBPar4 = par_s4;
                gsMBPar5 = par_s5;

                //redirect - open form
                //goUI.SetVar(GUID, Me)
                //HttpContext.Current.Response.Redirect(goUI.Navigate("FORMOBJECT", GUID))

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }
        public string GetViewMetadata(string sViewID)
        {
            if (gcViewMetadata.Contains(sViewID))
            {
                return gcViewMetadata[sViewID].ToString();
            }
            else
            {
                return "";
            }
        }
        public string GetViewStateMetadata(string sViewID)
        {
            if (gcViewState.Contains(sViewID))
            {
                return gcViewState[sViewID].ToString();
            }
            else
            {
                return "";
            }
        }
        public void SetViewMetadata(string sViewID, string sMeta)
        {

            if (gcViewMetadata.Contains(sViewID))
            {
                string sOldMetadata = gcViewMetadata[sViewID].ToString();
                string sState = gcViewState[sViewID].ToString();
                bool bChangedReportState = false;

                gcViewMetadata.Remove(sViewID);
                gcViewMetadata.Add(sMeta, sViewID);

                if (Util.GetSessionValue(sViewID.Replace(" ", "")) != null)
                {
                    goTR = (clTransform)Util.GetInstance("tr");
                    SessionViewInfo _sessionViewInfo = new SessionViewInfo();
                    _sessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + sViewID.Replace(" ", ""));
                    _sessionViewInfo.ViewMetaData = sMeta;
                    _sessionViewInfo.ViewCondition = goTR.StrRead(sMeta, "CONDITION", "");
                }

                //if condition changed, then reset selected rec and top rec info, then save current condition in state meta
                if (goTR.StrRead(sMeta, "CONDITION", "") != goTR.StrRead(sOldMetadata, "CONDITION", ""))
                {
                    ClearState(sViewID, "SELECTEDRECORDID");
                    ClearState(sViewID, "TOPREC");
                    ClearState(sViewID, "TOPREC_IFSELRECNOTINVIEW");
                    ClearState(sViewID, "SELECTEDRECORDINDEX");
                    SaveState(sViewID, "FILTER", goTR.StrRead(sMeta, "CONDITION", ""));
                    ClearState(sViewID, "BROWSED");

                    if (!bChangedReportState)
                        bChangedReportState = ClearReportViewState(sViewID);

                }

                //if sort changed, then reset selected rec and top rec info, then save current sort in state meta
                if (goTR.StrRead(sMeta, "SORT", "") != goTR.StrRead(sOldMetadata, "SORT", ""))
                {
                    ClearState(sViewID, "SELECTEDRECORDID");
                    ClearState(sViewID, "TOPREC");
                    ClearState(sViewID, "TOPREC_IFSELRECNOTINVIEW");
                    ClearState(sViewID, "SELECTEDRECORDINDEX");
                    SaveState(sViewID, "SORT", goTR.StrRead(sMeta, "SORT", ""));
                    ClearState(sViewID, "BROWSED");

                    //report view state information, clear it
                    if (!bChangedReportState)
                        bChangedReportState = ClearReportViewState(sViewID);

                }

                //if auto select changed, then reset selected rec and top rec info
                if (goTR.StrRead(sMeta, "AUTOSELECT", "") != goTR.StrRead(sOldMetadata, "AUTOSELECT", ""))
                {
                    ClearState(sViewID, "SELECTEDRECORDID");
                    ClearState(sViewID, "TOPREC");
                    ClearState(sViewID, "TOPREC_IFSELRECNOTINVIEW");
                    ClearState(sViewID, "SELECTEDRECORDINDEX");
                    ClearState(sViewID, "BROWSED");
                    SaveState(sViewID, "NEWPROPERTIES", "1");
                }

                //if view type changed, then reset VIEWTOP in state
                if (goTR.StrRead(sMeta, "TYPE", "") != goTR.StrRead(sOldMetadata, "TYPE", ""))
                {
                    ClearState(sViewID, "VIEWTOP");
                    ClearState(sViewID, "TYPE");

                    //report view state information, clear it
                    if (!bChangedReportState)
                        bChangedReportState = ClearReportViewState(sViewID);

                }


                if (goTR.StrRead(sMeta, "DISPLAYSECTIONS", "1") != goTR.StrRead(sOldMetadata, "DISPLAYSECTIONS", "1"))
                {
                    //report view state information, clear it
                    if (!bChangedReportState)
                        bChangedReportState = ClearReportViewState(sViewID);

                }

            }
            else
            {
                goErr=(clError)Util.GetInstance("err");
                goErr.SetError(0, "clDesktop::SetViewMetadata", "Cannot find view '" + sViewID + "' in desktop view collection.");
            }
        }
        public void ClearState(string sViewID, string sProperty)
        {
            if (gcViewState.Contains(sViewID))
            {
                string sMeta = gcViewState[sViewID].ToString();
                goTR.StrDelete(ref sMeta, sProperty);
                gcViewState.Remove(sViewID);
                gcViewState.Add(sMeta, sViewID);
            }
            else
            {
                //goErr.SetError(, "clDesktop::ClearState", "Cannot find view '" + sViewID + "' in desktop view collection.");
            }
        }
        public void SaveState(string sViewID, string sProperty, string sValue)
        {
            if (gcViewState.Contains(sViewID))
            {
                string sMeta = gcViewState[sViewID].ToString();
                goTR.StrWrite(ref sMeta, sProperty, sValue);
                gcViewState.Remove(sViewID);
                gcViewState.Add(sMeta, sViewID);
            }
            else
            {
                string sMeta = "";
                goTR.StrWrite(ref sMeta, sProperty, sValue);
                gcViewState.Add(sMeta, sViewID);
                //goErr.SetError(, "clDesktop::SaveState", "Cannot find view '" & sViewID & "' in desktop view collection.")
            }
        }
        public bool ClearReportViewState(string sViewID)
        {
            string sState = GetViewStateMetadata(sViewID);

            if (sState.Contains("LARGESTGROUP"))
            {
                int par_iValid = 4;
                int iLargest = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sState, "LARGESTGROUP", "0"), "", ref par_iValid));
                int i = 0;
                for (i = 0; i <= iLargest; i++)
                {
                    if (sState.Contains("GROUP" + i))
                    {
                        string sGroup = goTR.StrRead(sState, "GROUP" + i, "");
                        ClearState(sViewID, "GROUP" + sGroup + "STATE");
                        ClearState(sViewID, "GROUP" + sGroup + "COUNT");
                        ClearState(sViewID, "GROUP" + sGroup + "FILTER1");
                        ClearState(sViewID, "GROUP" + sGroup + "FILTER2");
                        ClearState(sViewID, "GROUP" + sGroup + "FILTER3");
                        ClearState(sViewID, "GROUP" + sGroup + "FILTER4");
                        ClearState(sViewID, "GROUP" + sGroup + "DATASOURCEROW");
                        ClearState(sViewID, "GROUP" + sGroup + "INDEX");
                        ClearState(sViewID, "GROUP" + i);
                    }
                }
                ClearState(sViewID, "LARGESTGROUP");
                ClearState(sViewID, "INITLOADCOMPLETE");
                ClearState(sViewID, "CHUNKSSHOWN");
                ClearState(sViewID, "GROUPCOUNT");

                //ROWINDEX???

                ResetReportTree = true;

                return true;
            }
            return false;
        }


        public void MessageBoxRemove()
        {
            gbMBDisplay = false;
        }

        //public string SelectedViewToTextFile(ref string par_sViewName, int par_iAddressColumns = 1, int par_iRecords = -1, int par_iMaxCharsPerCell = 200)
        //{        	
        //    //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
        //    try 
        //    {
        //        string sFilename = null;
        //        bool bError = false;
        //        string sViewTop = "0";
        //        string sTopRec = "";
        //        string sViewMeta = GetViewMetadata(SelectedViewID).ToString();
        //        string sViewState = GetViewStateMetadata(SelectedViewID).ToString();

        //        string sViewType = goTR.StrRead(sViewState, "TYPE", "", false);
        //        //MI 3/25/10
        //        if (string.IsNullOrEmpty(sViewType))
        //            sViewType = goTR.StrRead(sViewMeta, "TYPE", "", false);
        //        //MI 3/25/10
        //        //MI 3/25/10
        //        if (sViewType == "LIST" & goTR.StrRead(sViewMeta, "SECTIONSGROUPED", "", false) == "1") 
        //        {
        //            sViewType = "REPORT";
        //            //MI 3/25/10
        //        }
        //        //MI 3/25/10

        //        //SHOWDETAILS=1
        //        int iMode = Convert.ToInt32(goTR.StrRead(sViewState, "SHOWDETAILS", "0"));
        //        if (iMode == 1)
        //            iMode = 2;

        //        //MI 3/25/10 Adding disabling support for 'records from 1 to n' option in cal view printing/sending.
        //        if (Strings.Left(sViewType, 3) == "CAL") 
        //        {
        //            if (par_iRecords > 0)
        //                par_iRecords = -1;
        //        }

        //        if (sViewType == "CHART")
        //        {
        //            if (par_iRecords > 0)
        //                par_iRecords = -2;
        //        }

        //        switch (par_iRecords)
        //        {
        //            case -2:
        //                sViewTop = clC.SELL_VIEWPRINT_MAXRECORDS.ToString();
        //                sTopRec = "";
        //                break;
        //            case -1:
        //                if (goTR.StrRead(sViewMeta, "SECTIONSGROUPED", "", false) != "1")
        //                {
        //                    sViewTop = goTR.StrRead(sViewState, "VIEWTOP", "", false);
        //                    if (string.IsNullOrEmpty(sViewTop))
        //                        sViewTop = goTR.StrRead(sViewMeta, "SHOWTOP", "10", false);
        //                } 
        //                else 
        //                {
        //                    sViewTop = clC.SELL_VIEWPRINT_MAXRECORDS.ToString();
        //                }
        //                sTopRec = goTR.StrRead(sViewState, "TOPREC", "", false);
        //                break;
        //            case 0:
        //                sViewTop = "0";
        //                sTopRec = "";
        //                break;
        //            case  // ERROR: Case labels with binary operators are unsupported : GreaterThan  0:
        //                sViewTop = par_iRecords.ToString();
        //                sTopRec = "";
        //                break;
        //        }

        //        clView oView = default(clView);
        //        if (par_iRecords == -1)
        //        {
        //            if (HasViewObject(SelectedViewID)) 
        //            {
        //                oView = GetViewObject(SelectedViewID);
        //            } 
        //            else 
        //            {
        //                oView = new clView(this, gsMDSection, gsSelectedViewID, bError, , , true, sViewType, sViewTop, sTopRec,
        //                iMode);
        //            }
        //        } 
        //        else if (par_iRecords == -2 & (sViewType == "CHART" | sViewType == "REPORT")) 
        //        {
        //            if (HasViewObject(SelectedViewID)) 
        //            {
        //                oView = GetViewObject(SelectedViewID);
        //            } 
        //            else
        //            {
        //                oView = new clView(this, gsMDSection, gsSelectedViewID, bError, , , true, sViewType, sViewTop, sTopRec, iMode);
        //            }
        //        } 
        //        else
        //        {
        //            oView = new clView(this, gsMDSection, gsSelectedViewID, bError, , , true, sViewType, sViewTop, sTopRec,	iMode);
        //        }


        //        //Dim oView As New clView(Me, gsMDSection, gsSelectedViewID, bError, , , True, sViewType, sViewTop, sTopRec, iMode)

        //        par_sViewName = goTR.StripIllegalChars(oView.Title, "REMOVE");

        //        sFilename = oView.ToTextFile(par_iAddressColumns, par_iMaxCharsPerCell);

        //        return sFilename;
        //    } 
        //    catch (Exception ex) 
        //    {
        //        if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
        //        {
        //            //goErr.SetError(ex, , sProc);
        //        }
        //        return "";
        //    }
        //}


        //public string SelectedViewToHTML(string SelectedViewId,int Index, int par_iRecords = -1, bool par_bAsString = false, string par_sMessage = "")
        //{
        //    //MI 3/25/10 Adding disabling support for 'records from 1 to n' option in cal view printing/sending.
        //    //PURPOSE:
        //    //  sends view info to html email, via smtp
        //    //PARAMETERS:
        //    //  par_iRecords: The range of records to return. Supported values:
        //    //   -2: All records
        //    //   -1: Records curently displayed in the view (from toprec TOP= records down)
        //    //   0: No records - this is for getting the structure to Excel with no data
        //    //   1 and above: number of records from first record in the rowset (not toprec) down
        //    //  par_bAsString: determines whether to return the HTML as string or the name of a filename written to the temp directory
        //    //   False: return .html filename that is written to the temp directory
        //    //   True: return html as string
        //    // par_sMessage: prepended message
        //    //RETURNS:
        //    //  String: HTML of view

        //    //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    goMeta = (clMetaData)Util.GetInstance("meta");
        //    goTR = (clTransform)Util.GetInstance("tr");
        //    goP = (clProject)Util.GetInstance("p");


        //    try 
        //    {
        //        //bool bError = false;
        //        string sViewTop = "0";
        //        string sTopRec = "";
        //        string sViewMeta = GetViewMetadata(SelectedViewId).ToString();
        //        string sViewState = GetViewStateMetadata(SelectedViewId).ToString();
        //        string sViewType = goTR.StrRead(sViewState, "TYPE", "");
        //        if (string.IsNullOrEmpty(sViewType))
        //            sViewType = goTR.StrRead(sViewMeta, "TYPE", "");
        //        if (sViewType == "LIST" & goTR.StrRead(sViewMeta, "SECTIONSGROUPED", "") == "1") 
        //        {
        //            sViewType = "REPORT";
        //        }

        //        //SHOWDETAILS=1
        //        int iMode = Convert.ToInt32(goTR.StrRead(sViewState, "SHOWDETAILS", "0"));
        //        if (iMode == 1)
        //            iMode = 2;


        //        //MI 3/25/10 Adding disabling support for 'records from 1 to n' option in cal view printing/sending.
        //        if (Strings.Left(sViewType, 3) == "CAL") 
        //        {
        //            if (par_iRecords > 0)
        //                par_iRecords = -1;
        //        }

        //        switch (par_iRecords) 
        //        {
        //            case -2:
        //                sViewTop = clC.SELL_VIEWPRINT_MAXRECORDS.ToString();
        //                sTopRec = "";
        //                break;
        //            case -1:
        //                if (goTR.StrRead(sViewMeta, "SECTIONSGROUPED", "") != "1")
        //                {
        //                    sViewTop = goTR.StrRead(sViewState, "VIEWTOP", "");
        //                    if (string.IsNullOrEmpty(sViewTop))
        //                        sViewTop = goTR.StrRead(sViewMeta, "SHOWTOP", "10");
        //                }
        //                else 
        //                {
        //                    sViewTop = clC.SELL_VIEWPRINT_MAXRECORDS.ToString();
        //                }
        //                sTopRec = goTR.StrRead(sViewState, "TOPREC", "");
        //                break;
        //            case 0:
        //                sViewTop = "0";
        //                sTopRec = "";
        //                break;
        //            default :  // ERROR: Case labels with binary operators are unsupported : GreaterThan    0:
        //                sViewTop = par_iRecords.ToString();
        //                sTopRec = "";
        //                break;
        //        }

        //        SaveState(SelectedViewId, "TOPREC_PRINT", sTopRec);
        //        SaveState(SelectedViewId, "VIEWTOP_PRINT", sViewTop);

        //        View oView = default(View);
        //        if (par_iRecords == -1) 
        //        {
        //            if (HasViewObject(SelectedViewId)) 
        //            {
        //                oView = GetViewObject(SelectedViewId);
        //            } 
        //            else 
        //            {
        //                oView = new View(SelectedViewId, false, Section, DesktopMetaData, Index, false);
        //            }
        //        } 
        //        else if (par_iRecords == -2 & (sViewType == "CHART" | sViewType == "REPORT")) 
        //        {
        //            if (HasViewObject(SelectedViewId)) 
        //            {
        //                oView = GetViewObject(SelectedViewId);
        //            } 
        //            else 
        //            {
        //                oView = new View(SelectedViewId, false, Section, DesktopMetaData, Index, false);
        //            }
        //        } 
        //        else 
        //        {
        //            oView = new View(SelectedViewId, false, Section, DesktopMetaData, Index, false);
        //        }

        //        System.IO.StringWriter SW = new System.IO.StringWriter();
        //        string CSS = "";
        //        switch (par_bAsString) 
        //        {

        //            case false:
        //                //let's test for directory
        //                if (System.IO.Directory.Exists(System.Web.HttpContext.Current.Server.MapPath("~\\Temp\\")))
        //                {
        //                    //do nothing
        //                } 
        //                else 
        //                {
        //                    try
        //                    {
        //                        //try to create directory
        //                        System.IO.Directory.CreateDirectory(System.Web.HttpContext.Current.Server.MapPath("..") + "\\Temp");
        //                    } 
        //                    catch (Exception ex)
        //                    {
        //                        //goErr.SetError(ex, 45105, sProc);
        //                        return "";
        //                    }
        //                }

        //                string sPath = HttpContext.Current.Server.MapPath("~/Temp/");
        //                int par_iValid = 4;
        //                string par_sDelim = "";
        //                string sFilename = goTR.StripIllegalChars(oView.Title, "REMOVE") + "_" + goTR.StripIllegalChars(goTR.DateTimeToSysString(System.DateTime.Now , ref par_iValid ,ref par_sDelim), "REMOVE") + "_" + goP.GetUserCode() + ".html";
        //                if (System.IO.File.Exists(sPath + sFilename))
        //                    System.IO.File.Delete(sPath + sFilename);
        //                System.IO.StreamWriter oStream = default(System.IO.StreamWriter);
        //                oStream = System.IO.File.AppendText(sPath + sFilename);

        //                System.IO.DirectoryInfo oDr = new System.IO.DirectoryInfo(HttpContext.Current.Server.MapPath("../App_Themes/SellSQL/"));
        //                foreach (var oFile in oDr.GetFiles("*.css")) 
        //                {                           
        //                    System.IO.StreamReader SR = default(System.IO.StreamReader);
        //                    SR = new System.IO.StreamReader(oFile.FullName);
        //                    CSS = SR.ReadToEnd();
        //                    SR.Close();
        //                }

        //                //If System.IO.File.Exists(HttpContext.Current.Server.MapPath("../App_Themes/SellSQL/SellSQL.css")) Then
        //                //    Dim SR As System.IO.StreamReader
        //                //    SR = New System.IO.StreamReader(HttpContext.Current.Server.MapPath("../App_Themes/SellSQL/SellSQL.css"))
        //                //    CSS = SR.ReadToEnd
        //                //    SR.Close()
        //                //End If

        //                oStream.WriteLine("<html><style>" + Constants.vbCrLf + CSS + Constants.vbCrLf + "</style><body>" + Constants.vbCrLf);

        //                //message
        //                if (!string.IsNullOrEmpty(par_sMessage))
        //                    oStream.WriteLine("<span style=\"padding: 6px 2px 6px 2px; font-family: Verdana; font-size: 10pt;\">" + par_sMessage + "</span><br /><br />");

        //                oStream.WriteLine("<span style=\"padding: 6px 2px 6px 2px; font-family: Tahoma; font-weight: bold; font-size: medium;\">" + oView.Title + "</span><br />" + Constants.vbCrLf + oView.ToHtml() + Constants.vbCrLf + "</body></html>");
        //                oStream.Close();

        //                return sPath + sFilename;
        //            default:
        //                // True

        //                System.IO.DirectoryInfo Dr = new System.IO.DirectoryInfo(HttpContext.Current.Server.MapPath("../App_Themes/SellSQL/"));
        //                foreach (var oFile in Dr.GetFiles("*.css")) 
        //                {                            
        //                    System.IO.StreamReader SR = default(System.IO.StreamReader);
        //                    SR = new System.IO.StreamReader(oFile.FullName);
        //                    CSS = SR.ReadToEnd();
        //                    SR.Close();
        //                }

        //                //If System.IO.File.Exists(HttpContext.Current.Server.MapPath("../App_Themes/SellSQL/SellSQL.css")) Then
        //                //    Dim SR As System.IO.StreamReader
        //                //    SR = New System.IO.StreamReader(HttpContext.Current.Server.MapPath("../App_Themes/SellSQL/SellSQL.css"))
        //                //    CSS = SR.ReadToEnd
        //                //    SR.Close()
        //                //End If

        //                string sHTML = "<html><style>" + Constants.vbCrLf + CSS + Constants.vbCrLf + "</style><body>";

        //                //message
        //                if (!string.IsNullOrEmpty(par_sMessage))
        //                    sHTML = sHTML + "<span style=\"padding: 6px 2px 6px 2px; font-family: Verdana; font-size: 10pt;\">" + par_sMessage + "</span><br /><br />";

        //                sHTML = sHTML + "<span style=\"padding: 6px 2px 6px 2px; font-family: Tahoma; font-weight: bold; font-size: medium;\">" + oView.Title + "</span><br />" + Constants.vbCrLf + oView.ToHtml() + Constants.vbCrLf + "</body></html>";

        //                return sHTML;
        //            //Return "<html><style>" & vbCrLf & CSS & vbCrLf & "</style><body><span style=""padding: 6px 2px 6px 2px; font-family: Tahoma; font-weight: bold; font-size: medium;"">" & oView.Title & "</span>" & vbCrLf & oView.ToHTML & vbCrLf & "</body></html>"

        //        }

        //    } 
        //    catch (Exception ex)
        //    {
        //        if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) 
        //        {
        //            //goErr.SetError(ex, , sProc);
        //        }
        //        return "";
        //    }
        //}

        //public bool HasViewObject(string ViewID)
        //{
        //    //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
        //    try
        //    {
        //        //Views.Contains(Views.Find(p => p.Key.Contains(ViewID)))
        //        var view = from v in Views
        //                   where v.ViewId.Equals(ViewID)
        //                   select v;

        //        if (view.Count() > 0)
        //        {
        //            return true;
        //        }
        //        else
        //        {
        //            return false;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
        //        {
        //            //goErr.SetError(ex, , sProc);
        //        }
        //        return false;
        //    }
        //}

        //public View GetViewObject(string ViewID)
        //{
        //    //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
        //    try 
        //    {
        //        var view = from v in Views
        //                   where v.ViewId.Equals(ViewID)
        //                   select v;

        //        if (view.Count() > 0)
        //        {
        //            return (View)view;
        //        } 
        //        else 
        //        {
        //            return null;
        //        }
        //    } 
        //    catch (Exception ex) 
        //    {
        //        if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) 
        //        {
        //            //goErr.SetError(ex, , sProc);
        //        }
        //        return null;
        //    }
        //}



    }


    public class ToolTipData
    {
        public string ToolTip { get; set; }
        public string TableName { get; set; }
        public string FilterText { get; set; }

        public string DepencyViewIds { get; set; }
        public string QS_Condition { get; set; }

    }

    public class DependentViewsListItem
    {
        public string ChildViews { get; set; }
        public string ParentViews { get; set; }
    }
}

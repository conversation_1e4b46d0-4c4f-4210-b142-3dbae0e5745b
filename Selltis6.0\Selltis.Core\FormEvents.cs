﻿using Microsoft.VisualBasic;
using Selltis.BusinessLogic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Selltis.Core
{
    public class FormEvents
    {
        private clError goErr;
        private clData goData;
        public string ErrorMessage;
        public FormEvents()
        {
            goErr = (clError)Util.GetInstance("err");
            goData = (clData)Util.GetInstance("data");
        }
        public MessageBox SetMessageBox(Form _form, bool bAsSave = false, bool bKeepOpen = false)
        {
            clTransform goTR = (clTransform)Util.GetInstance("tr");
            MessageBox retval = new MessageBox();
            retval.MessageBoxDisplay = _form.MessageBoxDisplay;
            if (retval.MessageBoxDisplay)
            {
                if (bAsSave == true)
                {
                    retval.sEvent = "MESSAGEBOXBUTTONCLICKEDASSAVE";
                }
                else
                {
                    retval.sEvent = "MESSAGEBOXBUTTONCLICKED";
                }

                if (_form.MessageBoxFormMode == "SAVE")
                {
                    retval.sEvent = "MESSAGEBOXBUTTONCLICKEDASSAVE";
                }
                else if (_form.MessageBoxFormMode == "SAVEANDKEEPOPEN")
                {
                    retval.sEvent = "MESSAGEBOXBUTTONCLICKEDASSAVEANDKEEPOPEN";
                }

                if (bKeepOpen == true)
                {
                    retval.sEvent = "MESSAGEBOXBUTTONCLICKEDASSAVEANDKEEPOPEN";
                }

                retval.iStyle = _form.MessageBoxStyle;
                retval.PNL_MessageBoxVisible = true;
                retval.LBL_MsgBoxTitle = _form.MessageBoxTitle;
                retval.LBL_MsgBoxMessage = goTR.Replace(_form.MessageBoxMessage, Environment.NewLine, "<BR />");

                if (retval.iStyle >= clC.SELL_MB_DEFBUTTON3)
                {
                    //BTN_MsgBox3.Focus()
                    //gsMBInFocus = BTN_MsgBox3.ClientID
                    //gsMBDefault = BTN_MsgBox3.ClientID
                    retval.iStyle = retval.iStyle - clC.SELL_MB_DEFBUTTON3;
                }
                else if (retval.iStyle >= clC.SELL_MB_DEFBUTTON2)
                {
                    //BTN_MsgBox2.Focus()
                    //gsMBInFocus = BTN_MsgBox2.ClientID
                    //gsMBDefault = BTN_MsgBox2.ClientID
                    retval.iStyle = retval.iStyle - clC.SELL_MB_DEFBUTTON2;
                }
                else
                {
                    //BTN_MsgBox1.Focus()
                    //gsMBInFocus = BTN_MsgBox1.ClientID
                    //gsMBDefault = BTN_MsgBox1.ClientID
                    retval.iStyle = retval.iStyle;
                }

                switch (retval.iStyle)
                {
                    case clC.SELL_MB_INPUTBOX://'inputbox
                        //Dim iRows As Integer = CountCharacter(goForm.MessageBoxInputDefaultValue, vbCrLf)
                        //If iRows < 3 Then iRows = 3
                        //If iRows > 12 Then iRows = 12

                        if (_form.MessageBoxRichText == false)
                        {
                            //gsMBInFocus = TXT_MsgBoxInput.ClientID
                            retval.TXT_MsgBoxInputVisible = true;
                            if (Convert.ToString(_form.doRS.oVar.GetVar("IsFromImportLines")) == "1")
                            {
                                retval.FILE_MsgBoxInputVisible = true;
                                retval.TXT_MsgBoxInputVisible = false;
                            }
                            retval.TXT_MsgBoxInputText = _form.MessageBoxInputDefaultValue;
                            //TXT_MsgBoxInput.Rows = iRows
                            //TXT_MsgBoxInput.Attributes("onFocus") = "SetFocus(this.id,'True');"

                        }
                        else
                        {
                            retval.TXT_MsgBoxInputRichVisible = true;
                            retval.TXT_MsgBoxInputRichContent = _form.MessageBoxInputDefaultValue;
                            //TXT_MsgBoxInputRich.Height = Unit.Pixel(200)
                            //TXT_MsgBoxInputRich.Attributes("onFocus") = "SetFocus(this.id,'True');"
                        }
                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("OK", _form.MessageBoxButton1Label);
                        //if(retval.BTN_MsgBox1Text == "OK")
                        //{
                        //    retval.BTN_MsgBox1Width =System.Web.UI.WebControls.Unit.Pixel(60);
                        //}
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;

                        if (_form.MessageBoxRichText == false)
                        {
                            //var _onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON1',document.getElementById('TXT_MsgBoxInput').value);return false;";
                            //BTN_MsgBox1.Attributes("onClick") = "MessageBoxClick('" & sEvent & "','MESSAGEBOXBUTTON1',document.getElementById('" & Me.ClientID & "_TXT_MsgBoxInput').value);return false;"
                            var _onClick = "";
                            if (Convert.ToString(_form.doRS.oVar.GetVar("IsFromImportLines")) == "1")
                            {

                                _onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTONFILEPATH',document.getElementById('FILE_MsgBoxInput'),'','','',false);return false;";
                            }
                            else
                            {
                                _onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON1',document.getElementById('TXT_MsgBoxInput').value);return false;";
                            }
                            // var _onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON1',document.getElementById('TXT_MsgBoxInput').value);return false;";

                            retval.BTN_MsgBox1_onClick = _onClick;
                        }
                        else
                        {
                            //var _onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON1',$('TXT_MsgBoxInputRich').get_html());return false;";
                            var _onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON1',$('#TXT_MsgBoxInputRich').data('kendoEditor').value());return false;";
                            //$('#TXT_MsgBoxInputRich').data('kendoEditor')
                            //BTN_MsgBox1.Attributes("onClick") = "MessageBoxClick('" & sEvent & "','MESSAGEBOXBUTTON1',$find('" & Me.ClientID & "_TXT_MsgBoxInputRich').get_html());return false;"
                            retval.BTN_MsgBox1_onClick = _onClick;
                        }

                        retval.BTN_MsgBox2Visible = true;
                        retval.BTN_MsgBox2Text = GetButtonLabel("Cancel", _form.MessageBoxButton2Label);
                        retval.BTN_MsgBox2ToolTip = retval.BTN_MsgBox2Text;
                        var _BonClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON2','');return false;";
                        retval.BTN_MsgBox2_onClick = _BonClick;

                        retval.BTN_MsgBox3Visible = false;
                        break;
                    case clC.SELL_MB_RETRYCANCEL:

                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("Retry", _form.MessageBoxButton1Label);
                        //if (retval.BTN_MsgBox1Text == "Retry")
                        //{
                        //retval.BTN_MsgBox1Width = Unit.Pixel(60);
                        //}
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        var _1onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON1','');return false;";
                        retval.BTN_MsgBox1_onClick = _1onClick;

                        retval.BTN_MsgBox2Visible = true;
                        retval.BTN_MsgBox2Text = GetButtonLabel("Cancel", _form.MessageBoxButton2Label);
                        //If GetButtonLabel("Cancel", goForm.MessageBoxButton2Label) = "Cancel" Then BTN_MsgBox2.Width = Unit.Pixel(60)
                        retval.BTN_MsgBox2ToolTip = retval.BTN_MsgBox2Text;
                        _1onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON2','');return false;";
                        retval.BTN_MsgBox2_onClick = _1onClick;

                        retval.BTN_MsgBox3Visible = false;
                        retval.TXT_MsgBoxInputVisible = false;
                        retval.FILE_MsgBoxInputVisible = false;
                        break;
                    case clC.SELL_MB_YESNO:

                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("Yes", _form.MessageBoxButton1Label);
                        //If GetButtonLabel("Yes", goForm.MessageBoxButton1Label) = "Yes" Then BTN_MsgBox1.Width = Unit.Pixel(60)
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        var _2onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON1','');return false;";
                        retval.BTN_MsgBox1_onClick = _2onClick;

                        retval.BTN_MsgBox2Visible = true;
                        retval.BTN_MsgBox2Text = GetButtonLabel("No", _form.MessageBoxButton2Label);
                        //If GetButtonLabel("No", goForm.MessageBoxButton2Label) = "No" Then BTN_MsgBox2.Width = Unit.Pixel(60)
                        retval.BTN_MsgBox2ToolTip = retval.BTN_MsgBox2Text;

                        _2onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON2','');return false;";
                        retval.BTN_MsgBox2_onClick = _2onClick;

                        retval.BTN_MsgBox3Visible = false;
                        retval.TXT_MsgBoxInputVisible = false;
                        retval.FILE_MsgBoxInputVisible = false;
                        break;
                    case clC.SELL_MB_YESNOCANCEL:
                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("Yes", _form.MessageBoxButton1Label);
                        //If GetButtonLabel("Yes", goForm.MessageBoxButton1Label) = "Yes" Then BTN_MsgBox1.Width = Unit.Pixel(60)
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        var _3onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON1','');return false;";
                        retval.BTN_MsgBox1_onClick = _3onClick;

                        retval.BTN_MsgBox2Visible = true;
                        retval.BTN_MsgBox2Text = GetButtonLabel("No", _form.MessageBoxButton2Label);
                        //If GetButtonLabel("No", goForm.MessageBoxButton2Label) = "No" Then BTN_MsgBox2.Width = Unit.Pixel(60)
                        retval.BTN_MsgBox2ToolTip = retval.BTN_MsgBox2Text;
                        _3onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON2','');return false;";
                        retval.BTN_MsgBox2_onClick = _3onClick;

                        retval.BTN_MsgBox3Visible = true;
                        retval.BTN_MsgBox3Text = GetButtonLabel("Cancel", _form.MessageBoxButton3Label);
                        //If GetButtonLabel("No", goForm.MessageBoxButton2Label) = "No" Then BTN_MsgBox2.Width = Unit.Pixel(60)
                        retval.BTN_MsgBox3ToolTip = retval.BTN_MsgBox3Text;
                        _3onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON3','');return false;";
                        retval.BTN_MsgBox3_onClick = _3onClick;
                        retval.TXT_MsgBoxInputVisible = false;
                        retval.FILE_MsgBoxInputVisible = false;
                        break;
                    case clC.SELL_MB_ABORTRETRYIGNORE:
                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("Abort", _form.MessageBoxButton1Label);
                        //If GetButtonLabel("Abort", goForm.MessageBoxButton1Label) = "Abort" Then BTN_MsgBox1.Width = Unit.Pixel(60)
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        var _4onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON1','');return false;";
                        retval.BTN_MsgBox1_onClick = _4onClick;

                        retval.BTN_MsgBox2Visible = true;
                        retval.BTN_MsgBox2Text = GetButtonLabel("Retry", _form.MessageBoxButton2Label);
                        //If GetButtonLabel("Retry", goForm.MessageBoxButton2Label) = "Retry" Then BTN_MsgBox2.Width = Unit.Pixel(60)
                        retval.BTN_MsgBox2ToolTip = retval.BTN_MsgBox2Text;
                        _4onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON2','');return false;";
                        retval.BTN_MsgBox2_onClick = _4onClick;

                        retval.BTN_MsgBox3Visible = true;
                        retval.BTN_MsgBox3Text = GetButtonLabel("Ignore", _form.MessageBoxButton3Label);
                        //If GetButtonLabel("Ignore", goForm.MessageBoxButton2Label) = "Ignore" Then BTN_MsgBox2.Width = Unit.Pixel(60)
                        retval.BTN_MsgBox3ToolTip = retval.BTN_MsgBox3Text;
                        _4onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON3','');return false;";
                        retval.BTN_MsgBox3_onClick = _4onClick;
                        retval.TXT_MsgBoxInputVisible = false;
                        retval.FILE_MsgBoxInputVisible = false;
                        break;
                    case clC.SELL_MB_OKCANCEL:
                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("OK", _form.MessageBoxButton1Label);
                        //If GetButtonLabel("Abort", goForm.MessageBoxButton1Label) = "Abort" Then BTN_MsgBox1.Width = Unit.Pixel(60)
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        var _5onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON1','');return false;";
                        retval.BTN_MsgBox1_onClick = _5onClick;

                        retval.BTN_MsgBox2Visible = true;
                        retval.BTN_MsgBox2Text = GetButtonLabel("Cancel", _form.MessageBoxButton2Label);
                        //If GetButtonLabel("Retry", goForm.MessageBoxButton2Label) = "Retry" Then BTN_MsgBox2.Width = Unit.Pixel(60)
                        retval.BTN_MsgBox2ToolTip = retval.BTN_MsgBox2Text;
                        _5onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON2','');return false;";
                        retval.BTN_MsgBox2_onClick = _5onClick;

                        retval.BTN_MsgBox3Visible = false;
                        retval.TXT_MsgBoxInputVisible = false;
                        retval.FILE_MsgBoxInputVisible = false;
                        break;
                    case clC.SELL_MB_OK:

                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("OK", _form.MessageBoxButton1Label);
                        //If GetButtonLabel("Abort", goForm.MessageBoxButton1Label) = "Abort" Then BTN_MsgBox1.Width = Unit.Pixel(60)
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        var _6onClick = "MessageBoxClick('" + retval.sEvent + "','MESSAGEBOXBUTTON1','');return false;";
                        retval.BTN_MsgBox1_onClick = _6onClick;
                        retval.BTN_MsgBox2Visible = false;
                        retval.BTN_MsgBox3Visible = false;
                        retval.TXT_MsgBoxInputVisible = false;
                        retval.FILE_MsgBoxInputVisible = false;
                        break;
                    default:
                        break;
                }

            }
            else
            {
                clError goError = (clError)Util.GetInstance("err");
                retval.ErrorMessage = goError.GetLastError("MESSAGE");
                if (!string.IsNullOrEmpty(retval.ErrorMessage))
                {
                    retval.IsError = true;
                }
            }
            _form.oVar.SetVar(_form.MessageBoxFlag, "1");
            return retval;
        }

        public MessageBoxPanel SetMessageBoxPanel(Form _form)
        {
            MessageBoxPanel _MessageBoxPanel = new MessageBoxPanel();
            if(_form.MessagePanelDisplay)
            {
                _MessageBoxPanel.MessagePanelDisplay = true;
                _MessageBoxPanel.MessagePanelMessage =_form.MessagePanelMessage;
                _MessageBoxPanel.MessagePanelBackgroundColor = _form.MessagePanelBackgroundColor;
                _MessageBoxPanel.MessagePanelTextColor = _form.MessagePanelTextColor;
                _MessageBoxPanel.MessagePanelImage = _form.MessagePanelImage;
            }
            return _MessageBoxPanel;
        }
        public string GetButtonLabel(string sDefault, string sSetting)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                switch (sSetting)
                {
                    case "":
                        return sDefault;
                    default:
                        return sSetting;
                }
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return "";
            }
        }

        public bool DoEvent(string sEvent, ref Form goForm, string sArg, string sArg2 = "", bool lnkRefresh=false)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            try
            {
                ScriptManager goScr = new ScriptManager();
                clTransform goTR = (clTransform)Util.GetInstance("tr");
                clInit Init = new clInit();
                //Initialize();
                object par_oReturn = null;
                bool par_bRunnext = true;
                string par_sSections = "";
                bool ret = false;
                object obj = goForm;
                ErrorMessage = "";
                //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

                //HideErrorInformation();
                //HideMessageBox();
                //goform.FieldInformation = goForm.FieldInformation
                //M.DesignMode = (goForm.GetMode() == "PREVIEW");
                //M.FormObject = goForm;

                switch (sEvent)
                {
                    case "SAVE":

                        //ReconcileFieldValues(ref goForm);

                        if (goForm.Save() == 0)
                        {
                            //CreateControls();
                            //DisplayInfo("ERROR");
                            return false;
                        }
                        break;

                    case "SAVEANDLEAVEOPEN":
                        //ReconcileFieldValues(ref goForm);
                        goForm.Save(5);

                        //CreateControls();
                        //DisplayInfo("ERROR");
                        break;

                    case "CANCEL":
                        //goForm.Cancel();
                        break;
                    //***062608 commented this
                    //Response.Redirect(goUI.Navigate("CLOSE", ""))

                    case "DELETE":
                        goForm.Delete();
                        break;
                    //***062608 commented this
                    //Response.Redirect(goUI.Navigate("CLOSE", ""))

                    case "DELETEWARNING":


                        clUtil oUtil = new clUtil();
                        string sFile = goForm.Table;
                        string sID = goForm.RecordID;
                        string sName = oUtil.GetSysName(sFile, sID);
                        sFile = goData.GetFileLabelFromName(sFile);

                        if (!goData.GetRecordPermission(sID, "D"))
                        {
                            goForm.MessageBox("You don't have permission to delete the " + sFile + " '" + sName + "'.");
                        }
                        else if (goForm.Table == "AP" && Convert.ToString(goForm.doRS.GetFieldVal("GID_RecurID")) != "")
                        {
                            goForm.MessageBox("This is one Appointment in a Series. Do you want to delete entire series with its occurenses? <br> Click <b>Yes</b> to delete all Appointments in Series. <b>No</b> to delete only this Appointment.", clC.SELL_MB_YESNOCANCEL, "Recurring Appointment Conflict", "Yes", "No", "Cancel", "", "MessageBoxEvent", "MessageBoxEvent", "", goForm, null, "Yes", "No", "Cancel", "", "AP_FormOnSave_Delete_Recurring", "AP_Delete_Recurring");
                            //goForm.MessageBoxOverrideButtonScript = true;
                        }
                        else
                        {

                            goForm.MessageBox("Are you sure you want to delete " + sFile + " '" + sName + "'?", clC.SELL_MB_YESNO, "Delete Confirmation", "", "", "", "", "DELETE", "MBCANCEL");
                            goForm.MessageBoxOverrideButtonScript = true;
                        }
                        //ReconcileFieldValues(ref goForm);
                        //CreateControls();
                        //DisplayInfo("ERROR");
                        break;

                    case "STATE":
                        //ReconcileFieldValues(ref goForm);

                        //if (!string.IsNullOrEmpty(sArg2)) {
                        //    MMOCurPosInfo.Value = goForm.SetCursorPositionWithClientData(sArg2);
                        //}
                        break;

                    case "AJAXCOMPLETE":
                        //goForm.Ajax.Enabled = false;
                        //ReconcileFieldValues(ref goForm);
                        break;

                    case "TABSELECTED":
                        //ReconcileFieldValues(ref goForm);
                        obj = goForm;
                        ret = goScr.RunScript(goForm.Table + "_FormTabOnClick", ref obj, ref par_oReturn, ref par_bRunnext, ref par_sSections);
                        if (ret == false)
                        {
                        }
                        goForm = (Form)obj;
                        //CreateControls();
                        //DisplayInfo("ERROR");
                        break;

                    case "CHANGESHARESTATE":
                        //ReconcileFieldValues(ref goForm);
                        ret = goScr.RunScript(goForm.Table + "FormControlOnChange_SI__ShareState", ref obj, ref par_oReturn, ref par_bRunnext, ref par_sSections);
                        if (ret == false)
                        {
                        }
                        goForm = (Form)obj;

                        //CreateControls();
                        break;

                    case "PRINT":
                        //ReconcileFieldValues(ref goForm);
                        //CreateControls();
                        break;

                    case "REFRESH":
                        //ReconcileFieldValues(ref goForm);
                        //CreateControls();
                        break;

                    case "RUNSCRIPT":
                        if (lnkRefresh == false)
                        {
                            //ReconcileFieldValues(ref goForm);                       
                        }
                        obj = goForm;
                        if (!string.IsNullOrEmpty(sArg))
                        {
                            ret = goScr.RunScript(sArg, ref obj, ref par_oReturn, ref par_bRunnext, ref par_sSections);
                            goForm = (Form)obj;
                            if (ret == false)
                            {
                            }
                            else
                            {
                                //if (goForm.CloseOnReturn)
                                //Response.Redirect(goUI.Navigate("CLOSE", ""));
                            }
                        }

                        //CreateControls();
                        //DisplayInfo("ERROR");
                        break;

                    case "MESSAGEBOXBUTTONCLICKED":
                        //ReconcileFieldValues(ref goForm);
                        goForm.MessageBoxRemove();
                        string sScript = "";
                        string sReturn = "";
                        string sButtVal = goTR.ExtractString(sArg, 1, "|");
                        string sInptVal = goTR.ExtractString(sArg, 2, "|");
                        switch (sButtVal)
                        {
                            case "MESSAGEBOXBUTTON1":
                                sScript = goForm.MessageBoxButton1Script;
                                sReturn = goForm.MessageBoxPar1;
                                break;
                            case "MESSAGEBOXBUTTON2":
                                sScript = goForm.MessageBoxButton2Script;
                                sReturn = goForm.MessageBoxPar2;
                                break;
                            case "MESSAGEBOXBUTTON3":
                                sScript = goForm.MessageBoxButton3Script;
                                sReturn = goForm.MessageBoxPar3;
                                break;
                            case "MESSAGEBOXBUTTONFILEPATH":
                                sScript = goForm.MessageBoxButton1Script;
                                sReturn = goForm.MessageBoxPar1;
                                break;
                        }

                        if (goForm.MessageBoxOverrideButtonScript)
                        {
                            sArg = sScript;
                            DoEvent(sArg, ref goForm, "");
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(sScript))
                            {
                                obj = goForm;
                                ret = goScr.RunScript(sScript, ref obj, ref par_oReturn, ref par_bRunnext, ref par_sSections, null, sReturn, sInptVal, "", goForm.MessageBoxPar4, goForm.MessageBoxPar5);
                                goForm = (Form)obj;
                                if (ret)
                                {
                                    //if (goForm.CloseOnReturn)
                                    //Response.Redirect(goUI.Navigate("CLOSE", ""));
                                }
                            }

                            //CreateControls();
                            //DisplayInfo("ERROR");

                        }
                        break;


                    case "MESSAGEBOXBUTTONCLICKEDASSAVE":

                        // ReconcileFieldValues(ref goForm);
                        goForm.MessageBoxRemove();
                        sScript = "";
                        sReturn = "";
                        sButtVal = goTR.ExtractString(sArg, 1, "|");
                        sInptVal = goTR.ExtractString(sArg, 2, "|");
                        switch (sButtVal)
                        {
                            case "MESSAGEBOXBUTTON1":
                                sScript = goForm.MessageBoxButton1Script;
                                sReturn = goForm.MessageBoxPar1;
                                break;
                            case "MESSAGEBOXBUTTON2":
                                sScript = goForm.MessageBoxButton2Script;
                                sReturn = goForm.MessageBoxPar2;
                                break;
                            case "MESSAGEBOXBUTTON3":
                                sScript = goForm.MessageBoxButton3Script;
                                sReturn = goForm.MessageBoxPar3;
                                break;
                        }
                        if (!string.IsNullOrEmpty(sScript))
                        {
                            obj = goForm;
                            ret = goScr.RunScript(sScript, ref obj, ref par_oReturn, ref par_bRunnext, ref par_sSections, goForm.MessageBoxArray, sReturn, sInptVal, "", goForm.MessageBoxPar4, goForm.MessageBoxPar5);
                            goForm = (Form)obj;
                            if (ret)
                            {
                            }
                            else
                            {
                                //if (goForm.CloseOnReturn)
                                //    Response.Redirect(goUI.Navigate("CLOSE", ""));
                            }
                        }

                        switch (goForm.MessageBoxScriptInitiatedSave)
                        {
                            case "":
                                if (goForm.Save() == 0)
                                {
                                    //CreateControls();
                                    //if (goForm.ErrorOnSave == true)
                                    //    DisplayErrorInformation();
                                    //switch (goForm.MessageBoxScriptInitiatedSave) {
                                    //    case "":
                                    //        if (goForm.MessageBoxDisplay == true)
                                    //            DisplayMessageBox();
                                    //        break;
                                    //    default:
                                    //        if (goForm.MessageBoxDisplay == true)
                                    //            DisplayMessageBox(true);
                                    //        break;
                                    //}
                                }
                                else
                                {
                                    //Response.Redirect(goUI.Navigate("CLOSE", ""));
                                }
                                break;
                            default:

                                string sScr = goForm.MessageBoxScriptInitiatedSave;
                                obj = goForm;
                                ret = goScr.RunScript(sScr, ref obj, ref par_oReturn, ref par_bRunnext, ref par_sSections);
                                goForm = (Form)obj;
                                if (ret == true)
                                {
                                    goForm.MessageBoxScriptInitiatedSave = "";
                                    //if (goForm.CloseOnReturn)
                                    //    Response.Redirect(goUI.Navigate("CLOSE", ""));
                                }
                                goForm.MessageBoxScriptInitiatedSave = "";

                                //CreateControls();
                                //DisplayInfo("ERROR");
                                break;

                        }
                        break;

                    case "MESSAGEBOXBUTTONCLICKEDASSAVEANDKEEPOPEN":

                        //ReconcileFieldValues(ref goForm);
                        goForm.MessageBoxRemove();
                        sScript = "";
                        sReturn = "";
                        sButtVal = goTR.ExtractString(sArg, 1, "|");
                        sInptVal = goTR.ExtractString(sArg, 2, "|");
                        switch (sButtVal)
                        {
                            case "MESSAGEBOXBUTTON1":
                                sScript = goForm.MessageBoxButton1Script;
                                sReturn = goForm.MessageBoxPar1;
                                break;
                            case "MESSAGEBOXBUTTON2":
                                sScript = goForm.MessageBoxButton2Script;
                                sReturn = goForm.MessageBoxPar2;
                                break;
                            case "MESSAGEBOXBUTTON3":
                                sScript = goForm.MessageBoxButton3Script;
                                sReturn = goForm.MessageBoxPar3;
                                break;
                        }
                        if (!string.IsNullOrEmpty(sScript))
                        {
                            obj = goForm;
                            ret = goScr.RunScript(sScript, ref obj, ref par_oReturn, ref par_bRunnext, ref par_sSections, goForm.MessageBoxArray, sReturn, sInptVal, "", goForm.MessageBoxPar4, goForm.MessageBoxPar5);
                            goForm = (Form)obj;
                            if (ret)
                            {
                            }
                            else
                            {
                                //if (goForm.CloseOnReturn)
                                //Response.Redirect(goUI.Navigate("CLOSE", ""));
                            }
                        }

                        switch (goForm.MessageBoxScriptInitiatedSave)
                        {
                            case "":
                                goForm.Save(5);
                                //CreateControls();
                                //if (goForm.ErrorOnSave == true)
                                //    DisplayErrorInformation();
                                //switch (goForm.MessageBoxScriptInitiatedSave) {
                                //    case "":
                                //        if (goForm.MessageBoxDisplay == true)
                                //            DisplayMessageBox();
                                //        break;
                                //    default:
                                //        if (goForm.MessageBoxDisplay == true)
                                //            DisplayMessageBox(true, true);
                                //        break;
                                //}
                                break;
                            default:

                                string sScr = goForm.MessageBoxScriptInitiatedSave;
                                obj = goForm;
                                ret = goScr.RunScript(sScr, ref obj, ref par_oReturn, ref par_bRunnext, ref par_sSections);
                                goForm = (Form)obj;
                                if (ret == true)
                                {
                                    goForm.MessageBoxScriptInitiatedSave = "";

                                    //if (goForm.CloseOnReturn)
                                    //    Response.Redirect(goUI.Navigate("CLOSE", ""));
                                }
                                goForm.MessageBoxScriptInitiatedSave = "";

                                //CreateControls();
                                //DisplayInfo("ERROR");
                                break;

                        }
                        break;

                    case "INIT":
                        if (goForm.RecordDoesNotExist == true)
                        {
                            //DisplayRecordDoesNotExist();
                        }
                        else
                        {
                            //CreateControls();
                        }
                        //DisplayInfo("ERROR");
                        break;

                    case "BROWSE":
                        bool bShowBack = false;
                        bool bShowForward = false;
                        bool bClickThru = false;

                        //ReconcileFieldValues(ref goForm);

                        if (goForm.LinkboxAction == "clickthru")
                        {
                            //this form is a result of a linkbox clickthru
                            bClickThru = true;
                        }

                        if (goForm.Save(4) == 0)
                        {
                            //CreateControls();
                            //DisplayInfo("ERROR");
                        }
                        else
                        {

                            //if (bClickThru) {
                            //    if ((goForm.BrowsingLinkbox == null))
                            //        goForm.BrowsingLinkbox = goP.GetVar(goForm.LinkboxGUID + "_LinkboxSelector");
                            //}

                            //((StringBuilder)Session["sb"]).AppendLine("End-" + sProc + "(beforeBrowse): " + ((System.Diagnostics.Stopwatch)Session["sw"]).ElapsedMilliseconds);

                            //goForm.Browse(sArg, bShowBack, bShowForward);

                            //((StringBuilder)Session["sb"]).AppendLine("End-" + sProc + "(afterBrowse): " + ((System.Diagnostics.Stopwatch)Session["sw"]).ElapsedMilliseconds);

                            if (goForm.RecordDoesNotExist == true)
                            {
                                //DisplayRecordDoesNotExist();
                            }
                            else
                            {
                                obj = goForm;
                                ret = goScr.RunScript(goForm.Table + "_FormOnLoadRecord", ref obj, ref par_oReturn, ref par_bRunnext, ref par_sSections);
                                goForm = (Form)obj;
                                if (ret == false)
                                {
                                }
                                //var _with1 = M;
                                //_with1.ShowFirst = bShowBack;
                                //_with1.ShowPrevious = bShowBack;
                                //_with1.ShowNext = bShowForward;
                                //_with1.ShowLast = bShowForward;
                                //.RefreshMenus()
                                // CreateControls();
                            }
                            //DisplayInfo("ERROR");
                        }
                        break;

                    case "REFRESHLINKBOXES":
                        // ReconcileFieldValues(ref goForm);
                        //goForm.ReloadAllLinks();
                        //CreateControls();
                        //DisplayInfo("ERROR");
                        break;

                    case "DIAL":
                        //ReconcileFieldValues(ref goForm);
                        obj = goForm;
                        ret = goScr.RunScript("SystemFormControlOnDial", ref obj, ref par_oReturn, ref par_bRunnext, ref par_sSections, null, sArg);
                        goForm = (Form)obj;
                        if (ret == false)
                        {
                        }

                        //CreateControls();
                        //DisplayInfo("ERROR");
                        break;

                    case "ERROR":
                        //DisplayErrorInformation(sArg);
                        //DisplayInfo("ERROR");

                        break;
                    default:
                        //CreateControls();
                        //DisplayInfo("ERROR");

                        string sNDBID = sEvent;

                        if (sNDBID != "")
                        {

                        }
                        break;

                }

                return true;
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    // goErr.SetError(ex, 45105, sProc);
                    ErrorMessage = ex.ToString();

                }
                return false;
            }
        }

        private void ReconcileFieldValues(ref Form goForm)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //try
            //{
                #region Code

                //if (goForm.GetMode() == "PREVIEW")
                //    return;

                //DEBUG = DEBUG & "START [" & sProc & "] " & Now & vbCrLf

                clRowSet oRS = goForm.Rowset;
                int i = 0;
                string sField = null;
                int iTab = 0;
                string sOldValue = null;
                string sNewValue = null;
                int iState = 0;
                bool bGo = false;

                //If oRS.gsDirtyFields Is Nothing Then
                //    oRS.gsDirtyFields = New Generic.Dictionary(Of String, Tuple(Of Object, Object))()
                //End If

                //clear collection of link selections in preparation for new values
                //goForm.ClearLinkSelection()
                object _tempiState = iState;
                for (i = 0; i < goForm.lstFieldList.Count; i++)
                {
                    //FieldInfo oFieldInfo = (FieldInfo)goForm.FieldInformation(i);
                    //sField = oFieldInfo.sFieldName;
                    //iTab = oFieldInfo.iTab;
                    sField = goForm.lstFieldList[i];
                    //if this is a system field, don't even attempt edit
                    if (!goData.IsFieldSystem(sField, goForm.Table))
                    {
                        bGo = false;
                        var _clsNewValue = goForm.fielditems.Where(e => e.FieldName == sField).FirstOrDefault();
                        if (_clsNewValue == null)
                            continue;
                        switch (Strings.Left(Strings.UCase(sField), 4))
                        {

                            case "BTN_":
                                break;
                            //do nothing, this field never changes
                            case "GID_":
                                //do nothing for GID_ID only, this field never changes. 
                                //otherwise, treat as a text field
                                if (Strings.UCase(sField) != "GID_ID")
                                {
                                    _tempiState = iState;
                                    if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                    {
                                        iState = (int)_tempiState;
                                        if (iState == 0)
                                            bGo = true;
                                    }
                                    else
                                    {
                                        bGo = true;
                                    }
                                    if (bGo)
                                    {
                                        sOldValue = oRS.GetFieldVal(sField).ToString();
                                        //sNewValue = Request.Form(GetClientControlID(sField, iTab));

                                        //sNewValue = goForm.fielditems.Where(e=>e.FieldName == "")
                                        if (_clsNewValue != null)
                                        {
                                            sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                            if (sOldValue != sNewValue)
                                            {
                                                goForm.IsDirty = true;
                                                oRS.SetFieldVal(sField, sNewValue);
                                                //V_T adding the field to rowset dirty fields collection
                                                //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                            }
                                        }

                                    }
                                }
                                break;


                            case "LNK_":
                                //do nothing, linkbox takes care of writing to the clform.rowset
                                _tempiState = iState;
                                if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                {
                                    iState = (int)_tempiState;
                                    if (iState == 0)
                                        bGo = true;
                                    if (iState == 1)
                                        bGo = true;
                                    if (iState == 5)
                                        bGo = true;
                                    if (iState == 6)
                                        bGo = true;
                                }
                                else
                                {
                                    bGo = true;
                                }
                                if (bGo)
                                {
                                    string sLinked = "";

                                    string sLNKType = "NEW";
                                    //If Session["SA_BROWSER"] = "IE" Or Session["SA_BROWSER"] = "IE9" Then sLNKType = "OLD"
                                    //If Split(sField, "_")(2) = "DF" And (Session["SA_BROWSER"] = "IE" Or Session["SA_BROWSER"] = "IE9") Then sLNKType = "NEW"
                                    //If goForm.Table = "DF" And Split(sField, "_")(2) = "DF" Then sLNKType = "OLD"

                                    switch (sLNKType)
                                    {
                                        case "OLD":
                                            //sLinked = Request.Form(GetClientControlID(sField, iTab));
                                            if (_clsNewValue != null)
                                            {
                                                sLinked = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                            }
                                            break;
                                        default:
                                            //sLinked = Request.Form("F$LinkBoxSelectedRecord");
                                            //goForm.SetLinkSelection("LAST", Request.Form("F$LinkBoxSelectedRow"));
                                            if (_clsNewValue != null)
                                            {
                                                sLinked = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                                goForm.SetLinkSelection("LAST", sLinked);
                                            }

                                            break;
                                    }
                                    if ((sLinked != null) & !string.IsNullOrEmpty(sLinked))
                                    {
                                        //goForm.SetLinkSelection(sField, sLinked);
                                        sOldValue = oRS.GetFieldVal(sField).ToString();

                                        if (sOldValue.ToLower() != sLinked.ToLower())
                                        {
                                            goForm.IsDirty = true;
                                            oRS.ClearLinkAll(sField);
                                            oRS.SetLinkVal(sField, sLinked);
                                        }
                                    }
                                }
                                break;
                            case "MLS_":
                                _tempiState = iState;
                                if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                {
                                    iState = (int)_tempiState;
                                    if (iState == 0)
                                        bGo = true;
                                }
                                else
                                {
                                    bGo = true;
                                }
                                if (bGo)
                                {
                                    sOldValue = oRS.GetFieldVal(sField, clC.SELL_SYSTEM).ToString();
                                    //sNewValue = Request.Form(GetClientControlID(sField, iTab));
                                    if (_clsNewValue != null)
                                    {
                                        sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                    }
                                    if ((sNewValue == null))
                                        sNewValue = "";
                                    if (sOldValue != sNewValue)
                                    {
                                        goForm.IsDirty = true;
                                        oRS.SetFieldVal(sField, sNewValue, clC.SELL_SYSTEM);
                                        //V_T adding the field to rowset dirty fields collection
                                        //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                    }
                                }
                                break;
                            case "CHK_":
                                _tempiState = iState;
                                if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                {
                                    iState = (int)_tempiState;
                                    if (iState == 0)
                                        bGo = true;
                                }
                                else
                                {
                                    bGo = true;
                                }
                                if (bGo)
                                {
                                    sOldValue = oRS.GetFieldVal(sField, clC.SELL_SYSTEM).ToString();
                                    //sNewValue = Request.Form(GetClientControlID(Strings.UCase(sField), iTab));
                                    if (_clsNewValue != null)
                                    {
                                        sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                        if (sNewValue == "on")
                                        {
                                            sNewValue = "1";
                                        }
                                        else
                                        {
                                            sNewValue = "0";
                                        }
                                        if (sOldValue != sNewValue)
                                        {
                                            goForm.IsDirty = true;
                                            oRS.SetFieldVal(sField, sNewValue, clC.SELL_SYSTEM);
                                            //V_T adding the field to rowset dirty fields collection
                                            //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                        }
                                    }
                                }
                                break;
                            case "URL_":
                                _tempiState = iState;
                                if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                {
                                    iState = (int)_tempiState;
                                    if (iState == 0)
                                        bGo = true;
                                }
                                else
                                {
                                    bGo = true;
                                }
                                if (bGo)
                                {
                                    sOldValue = oRS.GetFieldVal(sField).ToString();
                                    //sNewValue = Request.Form(GetClientControlID(sField, iTab));
                                    if (_clsNewValue != null)
                                    {
                                        sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                        sNewValue = Strings.Replace(sNewValue, "|||delim|||", Constants.vbCrLf);
                                        if (sOldValue != sNewValue)
                                        {
                                            goForm.IsDirty = true;
                                            oRS.SetFieldVal(sField, sNewValue);
                                            //V_T adding the field to rowset dirty fields collection
                                            //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                        }
                                    }

                                }
                                break;
                            case "FIL_":
                                _tempiState = iState;
                                if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                {
                                    iState = (int)_tempiState;
                                    if (iState == 0)
                                        bGo = true;
                                }
                                else
                                {
                                    bGo = true;
                                }
                                if (bGo)
                                {
                                    sOldValue = oRS.GetFieldVal(sField).ToString();
                                    //sNewValue = Request.Form(GetClientControlID(sField, iTab));
                                    if (_clsNewValue != null)
                                    {
                                        sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                        sNewValue = Strings.Replace(sNewValue, "|||delim|||", Constants.vbCrLf);
                                        if (sOldValue != sNewValue)
                                        {
                                            goForm.IsDirty = true;
                                            oRS.SetFieldVal(sField, sNewValue);
                                            //V_T adding the field to rowset dirty fields collection
                                            //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                        }
                                    }

                                }
                                break;
                            case "DTE_":
                                _tempiState = iState;
                                if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                {
                                    iState = (int)_tempiState;
                                    if (iState == 0)
                                        bGo = true;
                                }
                                else
                                {
                                    bGo = true;
                                }
                                if (bGo)
                                {
                                    sOldValue = oRS.GetFieldVal(sField).ToString();
                                    // sNewValue = Request.Form(GetClientControlID(sField, iTab));
                                    if (_clsNewValue != null)
                                    {
                                        sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                        if (sOldValue != sNewValue)
                                        {
                                            goForm.IsDirty = true;
                                            oRS.SetFieldVal(sField, sNewValue);
                                            //V_T adding the field to rowset dirty fields collection
                                            //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                        }
                                    }

                                }
                                break;
                            case "TME_":
                                _tempiState = iState;
                                if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                {
                                    iState = (int)_tempiState;
                                    if (iState == 0)
                                        bGo = true;
                                }
                                else
                                {
                                    bGo = true;
                                }
                                if (bGo)
                                {
                                    sOldValue = oRS.GetFieldVal(sField).ToString();
                                    sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                    if (sOldValue != sNewValue)
                                    {
                                        goForm.IsDirty = true;
                                        oRS.SetFieldVal(sField, sNewValue);
                                        //V_T adding the field to rowset dirty fields collection
                                        //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                    }
                                }
                                break;
                            case "NDB_":
                                switch (Strings.Mid(Strings.UCase(sField), 5, 4))
                                {
                                    case "IFR_":
                                        _tempiState = iState;
                                        if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                        {
                                            iState = (int)_tempiState;
                                            if (iState == 0)
                                                bGo = true;
                                        }
                                        else
                                        {
                                            bGo = true;
                                        }
                                        if (bGo)
                                        {
                                            sOldValue = goForm.GetControlVal(sField);
                                            //sNewValue = Request.Form(GetClientControlID(Strings.UCase(sField), iTab));
                                        }
                                        break;
                                    case "CHK_":
                                        _tempiState = iState;
                                        if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                        {
                                            iState = (int)_tempiState;
                                            if (iState == 0)
                                                bGo = true;
                                        }
                                        else
                                        {
                                            bGo = true;
                                        }
                                        if (bGo)
                                        {
                                            sField = Strings.Mid(sField, 5);
                                            sOldValue = goForm.GetControlVal(sField);
                                            sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                            if (sNewValue == "on")
                                            {
                                                if (sOldValue != "CHECKED")
                                                {
                                                    goForm.IsDirty = true;
                                                    //V_T adding the field to rowset dirty fields collection
                                                    //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                                }
                                                goForm.SetControlVal(sField, "CHECKED");
                                            }
                                            else
                                            {
                                                if (sOldValue != "UNCHECKED")
                                                {
                                                    goForm.IsDirty = true;
                                                    //V_T adding the field to rowset dirty fields collection
                                                    //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                                }
                                                goForm.SetControlVal(sField, "UNCHECKED");
                                            }
                                        }
                                        break;
                                    case "TXT_":
                                    case "MMO_":
                                    case "TEL_":
                                        _tempiState = iState;
                                        if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                        {
                                            iState = (int)_tempiState;
                                            if (iState == 0)
                                                bGo = true;
                                        }
                                        else
                                        {
                                            bGo = true;
                                        }
                                        if (bGo)
                                        {
                                            sOldValue = goForm.GetControlVal(sField);
                                            sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                            if (sOldValue != sNewValue)
                                            {
                                                goForm.IsDirty = true;
                                                goForm.SetControlVal(sField, sNewValue);
                                                //V_T adding the field to rowset dirty fields collection
                                                //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                            }
                                        }
                                        break;
                                    case "MLS_":
                                        _tempiState = iState;
                                        if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                        {
                                            iState = (int)_tempiState;
                                            if (iState == 0)
                                                bGo = true;
                                        }
                                        else
                                        {
                                            bGo = true;
                                        }
                                        if (bGo)
                                        {
                                            sOldValue = goForm.GetControlVal(sField);
                                            sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                            if ((sNewValue == null))
                                                sNewValue = "";
                                            if (sOldValue != sNewValue)
                                            {
                                                goForm.IsDirty = true;
                                                goForm.SetControlVal(sField, sNewValue);
                                            }
                                        }
                                        break;
                                    default:
                                        break;
                                        //do nothing until supporting different field types
                                }
                                break;
                            case "TBC_":
                                //toolbar controls
                                switch (Strings.Mid(Strings.UCase(sField), 5, 4))
                                {
                                    case "CHK_":
                                        sField = Strings.Mid(sField, 5);
                                        sOldValue = goForm.GetControlVal(sField);
                                        sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                        if (sOldValue != sNewValue)
                                        {
                                            switch (sNewValue)
                                            {
                                                case "on":
                                                    if (sOldValue != "CHECKED")
                                                    {
                                                        goForm.IsDirty = true;
                                                        //V_T adding the field to rowset dirty fields collection
                                                        //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                                    }
                                                    goForm.SetControlVal(sField, "CHECKED");
                                                    break;
                                                default:
                                                    if (sOldValue != "UNCHECKED")
                                                    {
                                                        goForm.IsDirty = true;
                                                        //V_T adding the field to rowset dirty fields collection
                                                        //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                                    }
                                                    goForm.SetControlVal(sField, "UNCHECKED");
                                                    break;
                                            }
                                        }
                                        break;
                                    default:
                                        break;
                                        //do nothing until supporting different field types
                                }
                                break;

                            case "MMR_":
                                _tempiState = iState;
                                if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                {
                                    iState = (int)_tempiState;
                                    if (iState == 0)
                                        bGo = true;
                                }
                                else
                                {
                                    bGo = true;
                                }
                                if (bGo)
                                {
                                    sOldValue = oRS.GetFieldVal(sField).ToString();
                                    sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);

                                    //V_T 2/16/2015 MMR Change
                                    //Replace the image url with the base64 string
                                    if (!string.IsNullOrEmpty(sNewValue))
                                    {
                                        //sNewValue = ReplaceImageURLwithbase64String(sNewValue); //to-do
                                    }

                                    if (sOldValue != sNewValue)
                                    {
                                        goForm.IsDirty = true;
                                        oRS.SetFieldVal(sField, sNewValue);
                                        //V_T adding the field to rowset dirty fields collection
                                        //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                    }
                                }
                                break;
                            //V_T 4/24 Attachments Implementation
                            case "ADR_":
                                _tempiState = iState;
                                if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                {
                                    iState = (int)_tempiState;
                                    if (iState == 0)
                                        bGo = true;
                                }
                                else
                                {
                                    bGo = true;
                                }
                                if (bGo)
                                {

                                    if (Util.GetSessionValue(sField + "_Files") != null)
                                    {
                                        sNewValue = Util.GetSessionValue(sField + "_Files").ToString();
                                    }

                                    goForm.IsDirty = true;
                                    oRS.SetFieldVal(sField, sNewValue);
                                    //V_T adding the field to rowset dirty fields collection
                                    //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                }
                                break;
                            default:
                                _tempiState = iState;
                                if (goForm.GetControlState("STATE", sField, ref _tempiState))
                                {
                                    iState = (int)_tempiState;
                                    if (iState == 0)
                                        bGo = true;
                                }
                                else
                                {
                                    bGo = true;
                                }
                                if (bGo)
                                {
                                    sOldValue = oRS.GetFieldVal(sField).ToString();
                                    sNewValue = HttpUtility.UrlDecode(_clsNewValue.FieldValue);
                                    if (sOldValue != sNewValue)
                                    {
                                        goForm.IsDirty = true;
                                        oRS.SetFieldVal(sField, sNewValue);
                                        //'V_T adding the field to rowset dirty fields collection
                                        //AddDirtyFieldToCollection(oRS, sField, sOldValue, sNewValue)
                                    }
                                }
                                break;
                        }
                    }
                }
                //CType(Session["sb"], StringBuilder).AppendLine("End-" & sProc & ": " & CType(Session["sw"], System.Diagnostics.Stopwatch).ElapsedMilliseconds)
                goForm.Rowset = oRS;
                #endregion

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }

        public IList<Fields> RefreshFields(Form goForm, IList<Fields> fielditems)
        {
            var lstfilds = fielditems;
            IList<Fields> Changedfields = new List<Fields>();
            for (int i = 0; i < lstfilds.Count; i++)
            {
                string FieldName = lstfilds[i].DBFieldName.ToUpper().Replace("SPECIALSYMBOLEXIST", "#").Replace("SPECIALSYMBOLDOLLAR", "$");
                string fieldval = string.Empty;
                switch (Strings.Left(Strings.UCase(FieldName), 4))
                {
                    case "MLS_":
                        fieldval = goForm.doRS.GetFieldVal(FieldName, clC.SELL_SYSTEM).ToString();
                        break;
                    case "SI__":
                        fieldval = goForm.doRS.GetFieldVal(FieldName).ToString();
                        break;
                    case "CHK_":
                        fieldval = goForm.doRS.GetFieldVal(FieldName).ToString();
                        if (fieldval == "1")
                        {
                            fieldval = "on";
                        }
                        break;
                    case "NDB_":
                        if (Strings.UCase(FieldName).Contains("CHK_"))
                        {
                            if (!string.IsNullOrEmpty(goForm.GetControlVal(FieldName)))
                            {
                                fieldval = goForm.GetControlVal(FieldName).ToString();
                                if (fieldval == "1")
                                {
                                    fieldval = "on";
                                }
                            }
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(goForm.GetControlVal(FieldName)))
                            {
                                fieldval = goForm.GetControlVal(FieldName).ToString();
                            }
                        }
                        break;
                    case "TBC_":
                        if (Strings.UCase(FieldName).Contains("CHK_"))
                        {
                            if (!string.IsNullOrEmpty(goForm.GetControlVal(FieldName.Replace("TBC_", ""))))
                            {
                                fieldval = goForm.GetControlVal(FieldName.Replace("TBC_", "")).ToString();
                                if (fieldval == "1")
                                {
                                    fieldval = "on";
                                }
                            }
                        }
                        break;

                    case "MMO_":
                        fieldval = goForm.doRS.GetFieldVal(FieldName).ToString();

                        if (goForm.oVar.GetVar(FieldName + "_IsAppendTextInCursorPosition") != null && goForm.oVar.GetVar(FieldName + "_IsAppendTextInCursorPosition") == "true")
                        {
                            fieldval = fieldval + "[%Prepend/AppendText%]" + goForm.oVar.GetVar(FieldName + "_AppendText").ToString();

                            goForm.oVar.DeleteVar(FieldName + "_IsAppendTextInCursorPosition");
                            goForm.oVar.DeleteVar(FieldName + "_AppendText");
                        }

                        break;
                    default:
                        fieldval = goForm.doRS.GetFieldVal(FieldName).ToString();
                        break;
                }
                if (!fieldval.Equals(HttpUtility.UrlDecode(lstfilds[i].FieldValue)))
                {
                    Changedfields.Add(new Fields
                    {
                        FieldName = lstfilds[i].FieldName,
                        DBFieldName = FieldName,
                        FieldValue = fieldval
                    });
                }

            }
            return Changedfields;
        }

        //not using
        public IList<ControlState> RefreshControlState(Form goForm)
        {
            IList<ControlState> _lstControlState = new List<ControlState>();

            for (int i = 0; i < goForm.lstFieldList.Count; i++)
            {
                bool IsChanged = false;
                ControlState _ControlState = new ControlState();
                object oValue = null;
                var sField = goForm.lstFieldList[i].ToString();
                _ControlState.FieldName = sField;

                _ControlState.FieldPropertiy = new FieldPropertiy();
                _ControlState.FieldPropertiy.State = goForm.GetControlState(sField);
                if (_ControlState.FieldPropertiy.State != -1)
                {
                    IsChanged = true;
                }
                if (goForm.GetControlState("LABELTEXT", sField, ref oValue))
                {
                    _ControlState.FieldPropertiy.LabelText = oValue.ToString();
                    IsChanged = true;
                }


                if (goForm.GetControlState("TOOLTIP", sField, ref oValue))
                {
                    _ControlState.FieldPropertiy.ToolTip = oValue.ToString();
                    IsChanged = true;
                }

                if (goForm.GetControlState("LABELCOLOR", sField, ref oValue))
                {
                    _ControlState.FieldPropertiy.LabelColor = oValue.ToString();
                    IsChanged = true;
                }

                if (goForm.GetControlState("IMAGE", sField, ref oValue))
                {
                    _ControlState.FieldPropertiy.Image = oValue.ToString();
                    IsChanged = true;
                }

                if (goForm.GetControlState("FONTNAMES", sField, ref oValue))
                {
                    _ControlState.FieldPropertiy.FontNames = oValue.ToString();
                    IsChanged = true;
                }

                if (goForm.GetControlState("FONTSIZE", sField, ref oValue))
                {
                    _ControlState.FieldPropertiy.FontSize = oValue.ToString();
                    IsChanged = true;
                }
                if (IsChanged == true)
                    _lstControlState.Add(_ControlState);
            }

            return _lstControlState;
        }

        public IList<ControlState> RefreshControlState_1(Form goForm)
        {
            IList<ControlState> _lstControlState = new List<ControlState>();

            LoadControlSate(goForm, ref _lstControlState, goForm.PermControlState, "STATE");
            LoadControlSate(goForm, ref _lstControlState, goForm.ControlState, "STATE");
            LoadControlSate(goForm, ref _lstControlState, goForm.FldPro_LabelText, "LABELTEXT");
            LoadControlSate(goForm, ref _lstControlState, goForm.FldPro_ToolTip, "TOOLTIP");
            LoadControlSate(goForm, ref _lstControlState, goForm.FldPro_LabelColor, "LABELCOLOR");
            LoadControlSate(goForm, ref _lstControlState, goForm.FldPro_Image, "IMAGE");

            return _lstControlState;
        }


        private void LoadControlSate(Form goForm, ref IList<ControlState> _lstControlState, Collection _lObj, string Property)
        {
            var _kvpObj = Util.Collection2KeyValuePair(_lObj);
            object oValue = null;
            for (int i = 0; i < _kvpObj.Count; i++)
            {
                var _key = _kvpObj[i].Key;
                var _value = _kvpObj[i].Value;
                var _Data = _lstControlState.Where(p => p.FieldName.Equals(_key));
                if (_Data != null && _Data.Count() > 0)
                {
                    ControlState _ControlState = new ControlState();
                    if (goForm.GetControlState(Property, _key, ref oValue))
                    {
                        switch (Property)
                        {
                            case "STATE":
                                _lstControlState.Where(p => p.FieldName.Equals(_key)).FirstOrDefault().FieldPropertiy.State = int.Parse(oValue.ToString());
                                break;
                            case "LABELTEXT":
                                _lstControlState.Where(p => p.FieldName.Equals(_key)).FirstOrDefault().FieldPropertiy.LabelText = oValue.ToString();
                                break;
                            case "TOOLTIP":
                                _lstControlState.Where(p => p.FieldName.Equals(_key)).FirstOrDefault().FieldPropertiy.ToolTip = oValue.ToString();
                                break;
                            case "LABELCOLOR":
                                _lstControlState.Where(p => p.FieldName.Equals(_key)).FirstOrDefault().FieldPropertiy.LabelColor = oValue.ToString();
                                break;
                            case "IMAGE":
                                _lstControlState.Where(p => p.FieldName.Equals(_key)).FirstOrDefault().FieldPropertiy.Image = oValue.ToString();
                                break;
                            default:
                                break;
                        }
                    }
                }
                else
                {
                    ControlState _ControlState = new ControlState();
                    if (goForm.GetControlState(Property, _key, ref oValue))
                    {
                        _ControlState.FieldName = _key;
                        switch (Property)
                        {
                            case "STATE":
                                _ControlState.FieldPropertiy.State = int.Parse(oValue.ToString());
                                break;
                            case "LABELTEXT":
                                _ControlState.FieldPropertiy.LabelText = oValue.ToString();
                                break;
                            case "TOOLTIP":
                                _ControlState.FieldPropertiy.ToolTip = oValue.ToString();
                                break;
                            case "LABELCOLOR":
                                _ControlState.FieldPropertiy.LabelColor = oValue.ToString();
                                break;
                            case "IMAGE":
                                _ControlState.FieldPropertiy.Image = oValue.ToString();
                                break;
                            default:
                                break;
                        }
                        _lstControlState.Add(_ControlState);
                    }
                }
            }

        }

        public MessageBox SetDesktopMessageBox(object objectData, bool isDesktop)
        {
            clTransform goTR = (clTransform)Util.GetInstance("tr");
            MessageBox retval = new MessageBox();
            int MessageBoxStyle;
            bool MessageBoxDisplay;
            string MessageBoxTitle = "";
            string MessageBoxMessage = "";
            string MessageBoxInputDefaultValue = "";
            string MessageBoxButton1Label = "";
            string MessageBoxButton2Label = "";
            string MessageBoxButton3Label = "";
            if (isDesktop)
            {
                Desktop goDesktop = (Desktop)objectData;

                MessageBoxStyle = goDesktop.MessageBoxStyle;
                MessageBoxDisplay = goDesktop.MessageBoxDisplay;
                MessageBoxTitle = goDesktop.MessageBoxTitle;
                MessageBoxMessage = goDesktop.MessageBoxMessage;
                MessageBoxInputDefaultValue = goDesktop.MessageBoxInputDefaultValue;
                MessageBoxButton1Label = goDesktop.MessageBoxButton1Label;
                MessageBoxButton2Label = goDesktop.MessageBoxButton2Label;
                MessageBoxButton3Label = goDesktop.MessageBoxButton3Label;
            }
            else
            {
                Form goForm = (Form)objectData;

                MessageBoxStyle = goForm.MessageBoxStyle;
                MessageBoxDisplay = goForm.MessageBoxDisplay;
                MessageBoxTitle = goForm.MessageBoxTitle;
                MessageBoxMessage = goForm.MessageBoxMessage;
                MessageBoxInputDefaultValue = goForm.MessageBoxInputDefaultValue;
                MessageBoxButton1Label = goForm.MessageBoxButton1Label;
                MessageBoxButton2Label = goForm.MessageBoxButton2Label;
                MessageBoxButton3Label = goForm.MessageBoxButton3Label;
            }
            int iStyle = MessageBoxStyle;
            retval.MessageBoxDisplay = MessageBoxDisplay;
            if (retval.MessageBoxDisplay)
            {
                retval.PNL_MessageBoxVisible = true;
                retval.LBL_MsgBoxTitle = MessageBoxTitle;
                retval.LBL_MsgBoxMessage = goTR.Replace(MessageBoxMessage, Environment.NewLine, "<BR />");

                switch (iStyle)
                {
                    case clC.SELL_MB_DEFBUTTON3:
                        //def button 3
                        //BTN_MsgBox3.Focus();
                        //gsMBInFocus = BTN_MsgBox3.ClientID;
                        //gsMBDefault = BTN_MsgBox3.ClientID;
                        retval.BTN_MsgBox3_Focus = true;
                        //this.Attributes.Add("onKeyDown", "KeyDownHandler('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON3')");
                        iStyle = iStyle - clC.SELL_MB_DEFBUTTON3;
                        break;
                    case clC.SELL_MB_DEFBUTTON2:
                        //def button 2
                        //BTN_MsgBox2.Focus();
                        //gsMBInFocus = BTN_MsgBox2.ClientID;
                        //gsMBDefault = BTN_MsgBox2.ClientID;
                        retval.BTN_MsgBox2_Focus = true;
                        //this.Attributes.Add("onKeyDown", "KeyDownHandler('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON2')");
                        iStyle = iStyle - clC.SELL_MB_DEFBUTTON2;
                        break;
                    default:
                        //BTN_MsgBox1.Focus();
                        //gsMBInFocus = BTN_MsgBox1.ClientID;
                        //gsMBDefault = BTN_MsgBox1.ClientID;
                        retval.BTN_MsgBox1_Focus = true;

                        //this.Attributes.Add("onKeyDown", "KeyDownHandler('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON1')");
                        iStyle = iStyle;
                        break;
                }
                switch (iStyle)
                {
                    case clC.SELL_MB_INPUTBOX:

                        retval.TXT_MsgBoxInputVisible = true;
                        retval.TXT_MsgBoxInputText = MessageBoxInputDefaultValue;
                        retval.TXT_MsgBoxInput_Focus = true;
                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("OK", MessageBoxButton1Label);
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        retval.BTN_MsgBox1_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON1',document.getElementById('TXT_MsgBoxInput_Desktop').value);return false;";
                        retval.BTN_MsgBox2Visible = true;
                        retval.BTN_MsgBox2Text = GetButtonLabel("Cancel", MessageBoxButton2Label);
                        retval.BTN_MsgBox2ToolTip = retval.BTN_MsgBox2Text;
                        retval.BTN_MsgBox2_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON2','');return false;";
                        retval.BTN_MsgBox3Visible = false;
                        break;

                    case clC.SELL_MB_RETRYCANCEL:

                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("Retry", MessageBoxButton1Label);
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        retval.BTN_MsgBox1_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON1','');return false;";
                        retval.BTN_MsgBox2Visible = true;
                        retval.BTN_MsgBox2Text = GetButtonLabel("Cancel", MessageBoxButton2Label);
                        retval.BTN_MsgBox2ToolTip = retval.BTN_MsgBox2Text;
                        retval.BTN_MsgBox2_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON2','');return false;";

                        retval.BTN_MsgBox3Visible = false;
                        retval.TXT_MsgBoxInputVisible = false;
                        break;
                    case clC.SELL_MB_YESNO:

                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("Yes", MessageBoxButton1Label);
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        retval.BTN_MsgBox1_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON1','');return false;";
                        retval.BTN_MsgBox2Visible = true;
                        retval.BTN_MsgBox2Text = GetButtonLabel("No", MessageBoxButton2Label);
                        retval.BTN_MsgBox2ToolTip = retval.BTN_MsgBox2Text;
                        retval.BTN_MsgBox2_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON2','');return false;";

                        retval.BTN_MsgBox3Visible = false;
                        retval.TXT_MsgBoxInputVisible = false;
                        break;
                    case clC.SELL_MB_YESNOCANCEL:

                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("Yes", MessageBoxButton1Label);
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        retval.BTN_MsgBox1_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON1','');return false;";
                        retval.BTN_MsgBox2Visible = true;
                        retval.BTN_MsgBox2Text = GetButtonLabel("No", MessageBoxButton2Label);
                        retval.BTN_MsgBox2ToolTip = retval.BTN_MsgBox2Text;
                        retval.BTN_MsgBox2_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON2','');return false;";
                        retval.BTN_MsgBox3Visible = true;
                        retval.BTN_MsgBox3Text = GetButtonLabel("Cancel", MessageBoxButton3Label);
                        retval.BTN_MsgBox3ToolTip = retval.BTN_MsgBox3Text;
                        retval.BTN_MsgBox3_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON3','');return false;";

                        retval.TXT_MsgBoxInputVisible = false;
                        break;
                    case clC.SELL_MB_ABORTRETRYIGNORE:

                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("Abort", MessageBoxButton1Label);
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        retval.BTN_MsgBox1_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON1','');return false;";
                        retval.BTN_MsgBox2Visible = true;
                        retval.BTN_MsgBox2Text = GetButtonLabel("Retry", MessageBoxButton2Label);
                        retval.BTN_MsgBox2ToolTip = retval.BTN_MsgBox2Text;
                        retval.BTN_MsgBox2_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON2','');return false;";
                        retval.BTN_MsgBox3Visible = true;
                        retval.BTN_MsgBox3Text = GetButtonLabel("Ignore", MessageBoxButton3Label);
                        retval.BTN_MsgBox3ToolTip = retval.BTN_MsgBox3Text;
                        retval.BTN_MsgBox3_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON3','');return false;";

                        retval.TXT_MsgBoxInputVisible = false;
                        break;
                    case clC.SELL_MB_OKCANCEL:

                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("OK", MessageBoxButton1Label);
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        retval.BTN_MsgBox1_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON1','');return false;";
                        retval.BTN_MsgBox2Visible = true;
                        retval.BTN_MsgBox2Text = GetButtonLabel("Cancel", MessageBoxButton2Label);
                        retval.BTN_MsgBox2ToolTip = retval.BTN_MsgBox2Text;
                        retval.BTN_MsgBox2_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON2','');return false;";

                        retval.BTN_MsgBox3Visible = false;
                        retval.TXT_MsgBoxInputVisible = false;
                        break;
                    case clC.SELL_MB_OK:

                        retval.BTN_MsgBox1Visible = true;
                        retval.BTN_MsgBox1Text = GetButtonLabel("OK", MessageBoxButton1Label);
                        retval.BTN_MsgBox1ToolTip = retval.BTN_MsgBox1Text;
                        retval.BTN_MsgBox1_onClick = "javascript:MessageBoxClick('MESSAGEBOXBUTTONCLICKED','MESSAGEBOXBUTTON1','');return false;";

                        retval.BTN_MsgBox2Visible = false;
                        retval.BTN_MsgBox3Visible = false;
                        retval.TXT_MsgBoxInputVisible = false;
                        break;
                }
            }
            return retval;
        }
    }
}

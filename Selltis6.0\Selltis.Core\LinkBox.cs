﻿using Microsoft.VisualBasic;
using Selltis.BusinessLogic;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Selltis.Core
{
   public class LinkBox : ICloneable
    {
        public object Clone()
        {
            return this.MemberwiseClone();
        }
        private clProject goP;
	private clMetaData goMeta;
	private clTransform goTR;
	private clData goData;
	private clError goErr;
	private clLog goLog;
//	private clUI goUI;

	private clHistory goHist;
	private string gsLinkName = "";
	private string gsFile = "";
	private string gsFilterINI = "";
	private string gsTopRec = "";
	private bool gbSelectedOnly = false;
	private Collection gcOriginalVals = new Collection();
	private SortedList gcNDBSortedList = new SortedList();
	private bool gbIsNDB = false;
	private string gsURLToOpen = "";
	private bool gbOpenMe = false;
		//ALSO CAN BE "FILTER"
	private string gsFilterMode = "DEFAULT";
	private bool gbFilterChanged = false;
	private bool gbCheckTopRec = false;
	private clArray gaWhatsLinked;
	private DataTable goSelectedOnly = new DataTable();
	private string gsOriginalNDBLinks;
	private string gsOriginalNDBLinkNames;
	private int giShowNumLinks;
	private bool gbCountOn = false;
	private string gsLastAction = "";
	private int giLastSelectedOnlyCount = -1;
	private bool gbRecordAdded = false;
	private string gsRecordAddedInfo = "";
	private bool bFirstOn = true;
	private bool bLastOn = true;
		//can also be "TABLE"
	private string gsMode = "ROWSET";

	public bool OpenMe {
		get { return gbOpenMe; }
		set { gbOpenMe = false; }
	}

	public string LinkName {
		get { return gsLinkName; }
		set { gsLinkName = value; }
	}

	public string File {
		get { return gsFile; }
		set { gsFile = value; }
	}

	public DataTable SelectedOnlyStateTable {
		get { return goSelectedOnly; }
		set { goSelectedOnly = value; }
	}

	public string FilterMode {
		get { return gsFilterMode; }
		set { gsFilterMode = value; }
	}

	public string FilterINI {
		get { return gsFilterINI; }
		set {
			gsFilterINI = value;
			gsTopRec = "";
			gbFilterChanged = true;
		}
	}

	public bool FilterChanged {
		get {
			bool bTemp = gbFilterChanged;
			gbFilterChanged = false;
			return bTemp;
		}
		set { gbFilterChanged = value; }
	}

	public bool RecordAdded {
		get {
			bool bTemp = gbRecordAdded;
			gbRecordAdded = false;
			return bTemp;
		}
		set { gbRecordAdded = value; }
	}

	public string RecordAddedInfo {
		get { return gsRecordAddedInfo; }
		set { gsRecordAddedInfo = value; }
	}

	public Collection OriginalVals {
		get { return gcOriginalVals; }
		set { gcOriginalVals = value; }
	}

	public bool SelectedOnly {
		get { return gbSelectedOnly; }
		set { gbSelectedOnly = value; }
	}

	public int ShowNumLinks {
		get { return giShowNumLinks; }
		set { giShowNumLinks = value; }
	}

	public string TopRec {
		get { return gsTopRec; }
		set { gsTopRec = value; }
	}

	public SortedList NDBSortedList {
		get { return gcNDBSortedList; }
		set { gcNDBSortedList = value; }
	}

	public bool IsNDB {
		get { return gbIsNDB; }
		set { gbIsNDB = value; }
	}

	public string URLToOpen {
		get { return gsURLToOpen; }
		set { gsURLToOpen = value; }
	}

	public bool CheckTopRec {
		get {
			bool bTemp = gbCheckTopRec;
			gbCheckTopRec = false;
			return bTemp;
		}
		set { gbCheckTopRec = value; }
	}

	public clArray OriginalLinks {
		get { return gaWhatsLinked; }
		set { gaWhatsLinked = value; }
	}

	public string OriginalNDBLinks {
		get { return gsOriginalNDBLinks; }
		set { gsOriginalNDBLinks = value; }
	}

	public string OriginalNDBLinkNames {
		get { return gsOriginalNDBLinkNames; }
		set { gsOriginalNDBLinkNames = value; }
	}

	public bool CountOn {
		get { return gbCountOn; }
		set { gbCountOn = value; }
	}

	public string LastAction {
		get { return gsLastAction; }
		set { gsLastAction = value; }
	}

	public int LastSelectedOnlyCount {
		get { return giLastSelectedOnlyCount; }
		set { giLastSelectedOnlyCount = value; }
	}

	public bool BrowseFirstOn {
		get { return bFirstOn; }
		set { bFirstOn = value; }
	}

	public bool BrowseLastOn {
		get { return bLastOn; }
		set { bLastOn = value; }
	}

	public string Mode {
		get { return gsMode; }
		set { gsMode = value; }
	}

    private void Initialize()
    {
        string sProc = "clLinkbox::Initialize";
        //try {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
        //} catch (Exception ex) {
        //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) {
        //       // goErr.SetError(ex, sProc);
        //    }
        //}
    }

   
     
    public LinkBox()
    {
        string sProc = "clLinkbox::New";
        //try {
            Initialize();
        //} catch (Exception ex) {
        //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) {
        //      //  goErr.SetError(ex, , sProc);
        //    }
        //}
    }

    public LinkBox(string sLinkName, string sFilterINI)
    {
        string sProc = "clLinkbox::New";
        //try {
            Initialize();
            gsLinkName = sLinkName;
            gsFilterINI = sFilterINI;
            gsFile = gsLinkName; //Strings.Split(gsLinkName, "_")(2);
            gbOpenMe = true;
            int Valid =0;
            //get default selected only value
            int iVal= Convert.ToInt32(goTR.StringToNum(gsFilterINI, "SHOWSELECTEDONLY", ref Valid));
            switch (iVal) {
                case 0:
                    gbSelectedOnly = false;
                    break;
                case 1:
                    gbSelectedOnly = true;
                    break;
            }

        //} catch (Exception ex) {
        //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) {
        //      //  goErr.SetError(ex, , sProc);
        //    }
        //}
    }

	public string GetCondition()
	{
		string sProc = "clLinkbox::GetCondition";
		try {

			return goTR.StrRead(gsFilterINI, "CONDITION", "") + " " + goTR.StrRead(gsFilterINI, "CUSFILTER", "");
		} catch (Exception ex) {
			//if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) {
			//	//goErr.SetError(ex, , sProc);
			//}
			return "";
		}
	}

		public string GetDefaultCondition()
		{
			string sProc = "clLinkbox::GetCondition";
			try
			{

				return goTR.StrRead(gsFilterINI, "CONDITION", "");
			}
			catch (Exception ex)
			{
				//if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) {
				//	//goErr.SetError(ex, , sProc);
				//}
				return "";
			}
		}

		public string GetFilterConditionSummary()
	{
		string sProc = "clLinkbox::GetFilterConditionSummary";
		try {
			return goTR.GetFilterConditionSummary(gsFilterINI);
			// goTR.StrRead(gsFilterINI, "CONDITION", "")
		} catch (Exception ex) {
			//if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) {
			//	//goErr.SetError(ex, , sProc);
			//}
			return "";
		}
	}

	public string GetSort()
	{
		//MI 1/28/07 Replaced GetDefaultSort() with goData.GetDefaultSort(gsFile)
		string sProc = "clLinkbox::GetSort";
		try {
			return goTR.StrRead(gsFilterINI, "SORT", goData.GetDefaultSort(gsFile));
			//*** MI 1/28/07 Replaced GetDefaultSort() with goData.GetDefaultSort(gsFile)
		} catch (Exception ex) {
			//if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) {
			////	goErr.SetError(ex, , sProc);
			//}
			return goData.GetDefaultSort(gsFile);
			//*** MI 1/28/07 Replaced GetDefaultSort() with goData.GetDefaultSort(gsFile)
		}
	}

    //public string GetReverseSort()
    //{
    //    string sProc = "clLinkbox::ReverseSort";
    //    try {
    //        int i = 0;
    //        string sField = null;
    //        string sOrder = null;
    //        string sNewSort = null;
    //        clUtil oUtil = new clUtil();
    //        Collection cSort = goData.GetFilterSortFields("SORT=" + GetSort(), false);
    //        sNewSort = "";
    //        for (i = 1; i <= cSort.Count; i++) {
    //            sField = goTR.ExtractString(cSort.Item(i), 1, "|");
    //            sOrder = goTR.ExtractString(cSort.Item(i), 2, "|");
    //            if (goData.IsFieldValid(gsFile, sField)) {
    //                switch (sOrder) {
    //                    case "ASC":
    //                        sOrder = "DESC";
    //                        break;
    //                    case "DESC":
    //                        sOrder = "ASC";
    //                        break;
    //                }
    //                sNewSort = oUtil.BuildString(sNewSort, sField + " " + sOrder, ", ");
    //            }
    //        }
    //        return sNewSort;

    //    } catch (Exception ex) {
    //        if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) {
    //            goErr.SetError(ex, 45105, sProc);
    //        }
    //        return "";
    //    }
    //}

	public string GetDefaultSort()
	{
		//MI 1/27/07 Replaced Select Case with Return goData.GetDefaultSort(gsFile)
		string sProc = "clLinkbox::GetSortDirection";
		try {
			//*** MI 1/28/07 Commented section below
			//Dim sSort As String = ""
			//Select Case gsFile
			//    Case "AC", "AP", "EX", "MS", "OP", "QT", "TD"
			//        sSort = "DESC"
			//    Case Else
			//        sSort = "ASC"
			//End Select
			//Return "SYS_NAME " & sSort
			return goData.GetDefaultSort(gsFile);
			//*** MI 1/28/07
		} catch (Exception ex) {
			//if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) {
			////goErr.SetError(ex, , sProc);
			//}
			return "SYS_NAME";
		}
	}

    //public string GetDefaultReverseSort()
    //{
    //    //MI 1/28/07 Replaced GetDefaultSort() with goData.GetDefaultSort(gsFile)
    //    string sProc = "clLinkbox::GetDefaultReverseSort";
    //    try {
    //        int i = 0;
    //        string sField = null;
    //        string sOrder = null;
    //        string sNewSort = null;
    //        clUtil oUtil = new clUtil();
    //        //Dim cSort As Collection = goData.GetFilterSortFields("SORT=" & GetDefaultSort(), False)    '*** MI 1/28/07
    //        Collection cSort = goData.GetFilterSortFields("SORT=" + goData.GetDefaultSort(gsFile), false);
    //        //*** MI 1/28/07
    //        sNewSort = "";
    //        for (i = 1; i <= cSort.Count; i++) {
    //            sField = goTR.ExtractString(cSort.Item(i), 1, "|");
    //            sOrder = goTR.ExtractString(cSort.Item(i), 2, "|");
    //            if (goData.IsFieldValid(gsFile, sField)) {
    //                switch (sOrder) {
    //                    case "ASC":
    //                        sOrder = "DESC";
    //                        break;
    //                    case "DESC":
    //                        sOrder = "ASC";
    //                        break;
    //                }
    //                sNewSort = oUtil.BuildString(sNewSort, sField + " " + sOrder, ", ");
    //            }
    //        }
    //        return sNewSort;

    //    } catch (Exception ex) {
    //        if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) {
    //            goErr.SetError(ex, 45105, sProc);
    //        }
    //        return "";
    //    }
    //}

    //public string GetFirstSortField(string sOrder, ref string sOper)
    //{
    //    //MI 8/9/06  Making mods.

    //    string sProc = "clLinkbox::GetFirstSortField";

    //    try {
    //        Array aOrder = default(Array);
    //        int iPos = 0;
    //        aOrder = Strings.Split(sOrder, ", ");
    //        sOrder = aOrder(0);
    //        sOper = ">=";
    //        //*** MI 8/9/06 added space before ASC not to catch 'TXT_Tabasco DESC'
    //        //*** MI 8/9/06 Replaced ASC with A to catch 'TXT_Tabasco a', which is supported syntax
    //        iPos = Strings.InStr(Strings.UCase(sOrder), " A");
    //        if (iPos > 0) {
    //            sOrder = goTR.FromTo(sOrder, 1, iPos - 1);
    //            //*** MI 8/9/06 Replace(sOrder, " ASC", "")
    //            sOper = ">=";
    //        }
    //        //*** MI 8/9/06 added space before DESC not to catch 'TXT_Description ASC'
    //        //*** MI 8/9/06 Replaced DESC with D to catch 'TXT_Text d', which is supported syntax
    //        iPos = Strings.InStr(Strings.UCase(sOrder), " D");
    //        if (iPos > 0) {
    //            sOrder = goTR.FromTo(sOrder, 1, iPos - 1);
    //            //*** MI 8/9/06 Replace(sOrder, " DESC", "")
    //            sOper = "<=";
    //        }
    //        return sOrder;
    //    } catch (Exception ex) {
    //        if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) {
    //            goErr.SetError(ex, 45105, sProc);
    //        }
    //        return "";
    //    }
    //}

    //public object[] FilterAccess(string sFromFile)
    //{
    //    string sProc = "Linkbox.ascx::FilterAccess";
    //    object[] thisReturn = new object[3];


    //    try {
    //        if (goP.IsUserAdmin()) {
    //            //declaring an array
    //            thisReturn(0) = false;
    //            thisReturn(1) = false;
    //            thisReturn(2) = GetFilterConditionSummary();

    //        } else {
    //            string sFilter = "";
    //            bool bDisableAccess = false;
    //            bool bDisableTooltip = false;

    //            sFilter = goMeta.PageRead("GLOBAL", "LBF_" + gsFile, "", true);
    //            bDisableAccess = Convert.ToBoolean(Convert.ToInt32(goTR.StrRead(sFilter, "DISABLEFILTERACCESS", "0", true)));
    //            bDisableTooltip = Convert.ToBoolean(Convert.ToInt32(goTR.StrRead(sFilter, "DISABLEFILTERSUMMARYTOOLTIP", "0", true)));

    //            sFilter = goMeta.PageRead("GLOBAL", "LBF_" + Strings.UCase(sFromFile) + "_" + Strings.UCase(gsLinkName), "", true);
    //            if (!bDisableAccess)
    //                bDisableAccess = Convert.ToBoolean(Convert.ToInt32(goTR.StrRead(sFilter, "DISABLEFILTERACCESS", "0", true)));
    //            if (!bDisableTooltip)
    //                bDisableTooltip = Convert.ToBoolean(Convert.ToInt32(goTR.StrRead(sFilter, "DISABLEFILTERSUMMARYTOOLTIP", "0", true)));

    //            //declaring an array
    //            thisReturn(0) = bDisableAccess;
    //            thisReturn(1) = bDisableTooltip;

    //            if (!bDisableTooltip) {
    //                thisReturn(2) = GetFilterConditionSummary();
    //            } else {
    //                thisReturn(2) = "";
    //            }

    //        }

    //        return thisReturn;

    //    } catch (Exception ex) {
    //        if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE)) {
    //            goErr.SetError(ex, , sProc);
    //        }
    //        return thisReturn;
    //    }
    //}

    }
}

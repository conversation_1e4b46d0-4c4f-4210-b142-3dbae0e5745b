﻿  
  
  
CREATE PROCEDURE [dbo].[proc_OutsideSalesRepSegment_Dashboard]  
(@SegmentId Uniqueidentifier, @GMT_Time varchar(25), @LoginGID Uniqueidentifier)                                    
  AS                                    
  BEGIN                                    
  Set nocount on     
  
  --SELECT * FROM BU   
  --Select * from us where txt_fullname like '%STANTON%'  
  --[dbo].[proc_OutsideSalesRepSegment_Dashboard] '********-3932-3466-4255-312F362F3230','7'  
--EXEC [dbo].[proc_OutsideSalesRepSegment_Dashboard] @SegmentId = '********-3238-3530-4255-312F362F3230', @GMT_Time='-6', @LoginGID='********-6435-3035-5553-352F32362F32'  
  
 Declare @MyPrimaryAccounts int = 0    
 Declare @OverduePrimaryAccounts  int = 0  
 Declare @MyPrimaryAccountsWithoutContacts int = 0    
 Declare @MyPrimaryAccountsWithoutActivity int = 0    
 Declare @MyTeamSellAccounts int = 0  
 Declare @MyOverserveAccounts int = 0    
 Declare @MyUnderservedAccounts int = 0   
 Declare @MyContacts int = 0    
 Declare @MyNewContacts int = 0    
 Declare @MyKeyContacts int = 0  
 Declare @MyTasksDue int = 0    
 Declare @MyTasksOverdue int = 0    
 Declare @MyActivities int = 0  
 Declare @MyAcTeamSell int = 0  
 Declare @MySalesThisMonth int = 0    
 Declare @MySalesLastMonth int = 0  
 Declare @MyLeads int = 0  
 Declare @MyLeadsExptedVal money = 0  
 Declare @MyLeadsWeightedVal money = 0  
 Declare @MyTeamSellLeads int = 0  
 Declare @MyTeamSellLeadsExptedVal money = 0  
 Declare @MyTeamSellLeadsWeightedVal money = 0  
 Declare @MyNewLeads int = 0  
 Declare @MyNewLeadsExptedVal money = 0  
 Declare @MyNewLeadsWeightedVal money = 0  
 Declare @MyOverdueLeads int = 0  
 Declare @MyOverdueLeadsExptedVal money = 0  
 Declare @MyOverdueLeadsWeightedVal money = 0  
 Declare @MyOpenOppsqty int = 0    
 Declare @MyOpenOppsExptedVal money = 0    
 Declare @MyOpenOppsWeightedVal money = 0  
 Declare @MyTeamSellOpps_count int = 0    
 Declare @MyTeamSellOpps_expval money = 0    
 Declare @MyTeamSellOpps_wghtval money = 0  
 Declare @MyDueNext30DaysOpps_count int = 0    
 Declare @MyDueNext30DaysOpps_expval money = 0    
 Declare @MyDueNext30DaysOpps_wghtval money = 0  
 Declare @MyOverdueOpps_count int = 0    
 Declare @MyOverdueOpps_expval money = 0    
 Declare @MyOverdueOpps_wghtval money = 0  
 Declare @MyOpps_won_Qty int = 0    
 Declare @MyOpps_won_Tot money = 0    
 Declare @MyOpps_won_Rate  int = 0    
 Declare @MyOpps_Lost_Qty int = 0    
 Declare @MyOpps_Lost_Tot money = 0    
 Declare @MyOpps_Lost_Rate  int = 0  
 Declare @MyOpps_Cancelled_Qty int = 0    
 Declare @MyOpps_Cancelled_Tot money = 0    
 Declare @MyOpps_Cancelled_Rate  int = 0  
 Declare @OpenRFQsQty int = 0    
 Declare @OpenRFQstotal money = 0    
 Declare @OpenRFQsmargin int = 0    
 Declare @MyQuotesQty int = 0    
 Declare @MyQuotesTotal money = 0    
 Declare @MyQuotesMargin money= 0   
 Declare @OpenStrategicQuotesQty int = 0    
 Declare @OpenStrategicQuotesTotal money = 0    
 Declare @OpenStrategicQuotesMargin int = 0    
 Declare @NewStrategicQuotesQty int = 0    
 Declare @NewStrategicQuotesTotal money = 0    
 Declare @NewStrategicQuotesMargin int = 0  
 Declare @OverdueStrategicQuotesQty int = 0    
 Declare @OverdueStrategicQuotesTotal money = 0    
 Declare @OverdueStrategicQuotesMargin int = 0  
 Declare @StrategicQuotesWonQty int = 0    
 Declare @StrategicQuotesWonTotal money = 0    
 Declare @StrategicQuotesWonRate int = 0  
 Declare @StrategicQuotesLostQty int = 0    
 Declare @StrategicQuotesLostTotal money = 0    
 Declare @StrategicQuotesLostRate int = 0     
 Declare @StrategicQuotesCancelledQty int = 0    
 Declare @StrategicQuotesCancelledTotal money = 0    
 Declare @StrategicQuotesCancelledRate int = 0  
 --  
 Declare @AccountsWithoutContacts int = 0    
 Declare @AccountsWithoutActivities int = 0  
 Declare @MyNewAccounts int = 0    
 Declare @MyOpportunitiesforAccount int = 0   
 Declare @MyQuotesforAccount int = 0  
 Declare @MyActivitiesforAccount int = 0   
 Declare @MyOverdueContacts int = 0  
 Declare @OthersAcThisMonth int = 0    
 Declare @OthersAcLastMonth int = 0    
 Declare @MyRecentOrders int = 0    
 Declare @count_of_my_recent_orders_this_month int = 0                               
 Declare @total_amount_of_my_recent_order_this_month money = 0.0                     
 Declare @gross_profit_of_my_recent_order_this_month int = 0                               
 Declare @count_of_my_recent_orders_last_month int = 0                                        
 Declare @total_amount_of_my_recent_order_last_month money = 0.0                               
 Declare @gross_profit_of_my_recent_order_last_month int = 0  
 Declare @SalesThisMonthQty  int =0    
 Declare @SalesThisMonthTotal  int =0    
 Declare @SalesThisMonthMargin  int =0   
 Declare @SalesLastMonthQty  int =0    
 Declare @SalesLastMonthTotal  int =0    
 Declare @SalesLastMonthMargin  int =0    
 Declare @MyOpenREQ int = 0         
 Declare @MyStrategicQuotesOpe int = 0                                        
 Declare @MyStrategicQuotesNew int = 0                                        
 Declare @MyStrategicQuotesOverdue int = 0                               
 Declare @sum_of_my_open_expected_value money = 0.0                                        
 Declare @count_of_my_open_expected_value_weighted int = 0    
 Declare @count_of_my_open_orders int = 0                                        
 Declare @total_amount_of_my_open_order money = 0.0                                        
 Declare @gross_profit_of_my_open_order int = 0       
 Declare @MyQuotes int = 0     
 Declare @MyQuotePipeline int = 0   
 Declare @MyTaskstile int = 0     
  
  --DECLARE @GMT_Time VARCHAR(100) = '-6'  
 --DECLARE @GMTOffset INT, @TimeZone VARCHAR(100)  
  
 --SELECT TOP 1  
 --@GMTOffset = GMTOffset,   
 --@TimeZone = StandardTimeZone   
 --FROM DSTTimeZoneMapping WHERE GMTOffset = CAST(@GMT_Time AS INT)  
   
 --IF IIF(DATEPART(TZOFFSET, SYSDATETIMEOFFSET() AT TIME ZONE 'UTC' AT TIME ZONE @TimeZone) <> @GMTOffset * 60, 1, 0) = 1  
 --BEGIN  
 --SET @GMT_Time = CAST(CAST(@GMT_Time AS INT) +1 AS varchar(100))  
 --END  
  
  
 DECLARE @TODAY DATETIME   
 --SET @TODAY = CASE   
 --    WHEN CAST(@GMT_Time AS INT) = 6 THEN CAST(DATEADD(HH,-6,GETUTCDATE()) AS DATE) --  CENTRAL TIME  
 --    WHEN CAST(@GMT_TIME AS INT) = 7 THEN CAST(DATEADD(HH,-7,GETUTCDATE()) AS DATE) -- MOUNTAIN TIME  
 --    END  
  
  
 SET @GMT_Time = CAST(CAST(@GMT_Time AS INT)+1 AS VARCHAR(100))  
 SET @TODAY = CAST(DATEADD(HH,CAST(@GMT_TIME AS INT),GETUTCDATE()) AS DATE)   
 set @GMT_Time= abs(@GMT_Time)  
 Declare @TodayDate DATETIME = DATEADD(HH,CAST(@GMT_Time AS INT),@TODAY) --2025-02-12 06:00:00.000, 2025-02-12 07:00:00.000   
  
 --SELECT @TodayDate  
  
 Declare @LastMonthStartDate Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),CAST(DATEADD(DD,1,EOMONTH(DATEADD(MM,-2,@TODAY))) AS DATETIME))  
 --2025-01-01 06:00:00.000 2025-01-01 07:00:00.000  
  
 Declare @ThisMonthStartDate Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),CAST(DATEADD(DD,1,EOMONTH(DATEADD(MM,-1,@TODAY))) AS DATETIME))    
 --2025-02-01 06:00:00.000 2025-02-01 07:00:00.000  
  
 Declare @StartDateOfNextTwoWeeks Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),DATEADD(DAY,14,DATEADD(DAY,-((DATEPART(WEEKDAY, @TODAY) + 5) % 7),@TODAY)))  
 --2025-02-24 06:00:00.000 2025-02-24 07:00:00.000  
  
 Declare @EndDateOfNextTwoWeeks Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),DATEADD(DAY,28,DATEADD(DAY,-((DATEPART(WEEKDAY, @TODAY) + 5) % 7),@TODAY)))  
 --2025-03-10 06:00:00.000 2025-03-10 07:00:00.000   
  
 Declare @NextMonthStartDate Datetime =  DATEADD(HH,CAST(@GMT_Time AS INT),CAST(DATEADD(DD,1,EOMONTH(@TODAY)) AS DATETIME))  
 --2025-03-01 06:00:00.000 2025-03-01 07:00:00.000  
  
 --Declare @StartDateOfLastTwoWeeks Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),DATEADD(DAY, -14, DATEADD(DAY, -((DATEPART(WEEKDAY, @TODAY) + 5) % 7), @TODAY)))    
 ----2025-01-27 06:00:00.000 2025-01-27 07:00:00.000   
   
  
  Declare @StartDateOfLastTwoWeeks Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),DATEADD(DAY, -15, DATEADD(DAY, -((DATEPART(WEEKDAY, @TODAY) + 5) % 7), @TODAY)))    
 --2025-01-27 06:00:00.000 2025-01-27 07:00:00.000   
  
 --Declare @EndDateOfLastTwoWeeks Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),DATEADD(DAY, -((DATEPART(WEEKDAY, GETUTCDATE()) + 5) % 7), @TODAY))  
 ----SELECT DATEADD(SS,-1,@EndDateOfLastTwoWeeks) 2025-02-10 06:00:00.000 2025-02-10 07:00:00.000  
   
 Declare @EndDateOfLastTwoWeeks Datetime = DATEADD(HH,CAST(@GMT_Time AS INT),DATEADD(DAY, -((DATEPART(WEEKDAY, GETUTCDATE()) + 6) % 7), @TODAY))  
 --SELECT DATEADD(SS,-1,@EndDateOfLastTwoWeeks) 2025-02-10 06:00:00.000 2025-02-10 07:00:00.000  
  
  
  
 Declare @MonthAgoDate Datetime = DATEADD(SS,-1,DATEADD(HH,-1,DATEADD(MM,1,@TodayDate)))  
 -- '2025-02-12 07:00:00.000' '2025-03-12 05:59:59.000'  
  
 Declare @ThreeMonthsAgoDate Datetime = DATEADD(DD,1,DATEADD(MM,-3,@TodayDate))   
 Declare @FiveDaysAgoDate Datetime = DATEADD(DD,-5, @TodayDate) --2025-02-07 07:00:00.000  
 DECLARE @TomorrowDate Datetime = DATEADD(SS,-1,DATEADD(DD,1,@TodayDate))  
 Declare @NintyOneDaysAgoDate Datetime = DATEADD(MM,-3,DATEADD(SS,1,@TomorrowDate))  
  
--Overdue Primary Accounts   
 Select @OverduePrimaryAccounts = IsNull(Count(Distinct(CO.GID_ID)),0)  
FROM [CO]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]  
LEFT JOIN [BC] [BC00002] ON [BC00002].[GID_ID] = [CO].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00003] ON [BU00003].[GID_ID] = [CO].[GID_RELATED_BU]  
WHERE   
 [CO].[CHK_REVIEW] = 1 AND   
 ([CO].[DTT_NEXTREVIEWDATE] <= @TodayDate OR  
 ([CO].[DTT_NEXTREVIEWDATE] IS NULL)) AND  
 [CO].[CHK_PRIMARY] = 1 AND  
 [BU00003].[GID_ID] = @SegmentId  
  
--My Primary Accounts Without Contacts*    
 Select @MyPrimaryAccountsWithoutContacts = IsNull(Count(Distinct(CO.GID_ID)),0)    
FROM [CO]    
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]    
LEFT JOIN [BC] [BC00002] ON [BC00002].[GID_ID] = [CO].[GID_RELATED_BC]    
LEFT JOIN [BU] [BU00003] ON [BU00003].[GID_ID] = [CO].[GID_RELATED_BU]    
LEFT JOIN [CN_RELATED_CO] ON [CO].[GID_ID] = [CN_RELATED_CO].[GID_CO]    
LEFT JOIN [CN] [CN00004] ON [CN00004].[GID_ID] = [CN_RELATED_CO].[GID_CN]    
WHERE     
 [CO].[CHK_ACTIVEFIELD] = 1 AND     
 [CO].[CHK_PRIMARY] = 1 AND     
 [BU00003].[GID_ID]  = @SegmentId AND     
 ([CN00004].[BI__ID] < 1 OR   
 ([CN00004].[BI__ID] IS NULL))    
  
--My Primary Accounts Without Activity*    
 Select @MyPrimaryAccountsWithoutActivity = IsNull(Count(Distinct(CO.GID_ID)),0)    
FROM [CO]    
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [CO].[GID_TEAMLEADER_US]    
LEFT JOIN [BC] [BC00002] ON [BC00002].[GID_ID] = [CO].[GID_RELATED_BC]    
LEFT JOIN [BU] [BU00003] ON [BU00003].[GID_ID] = [CO].[GID_RELATED_BU]    
LEFT JOIN [AC_RELATED_CO] ON [CO].[GID_ID] = [AC_RELATED_CO].[GID_CO]    
LEFT JOIN [AC] [AC00004] ON [AC00004].[GID_ID] = [AC_RELATED_CO].[GID_AC]    
WHERE     
 [CO].[CHK_ACTIVEFIELD] = 1 AND     
 [CO].[CHK_PRIMARY] = 1 AND     
 [BU00003].[GID_ID] = @SegmentId AND     
 ([AC00004].[BI__ID] < 1 OR   
 ([AC00004].[BI__ID] IS NULL))   
  
--My Segment Team Sell Accounts    
 Select @MyTeamSellAccounts = IsNull(Count(Distinct(CO.GID_ID)),0)    
FROM [CO]    
LEFT JOIN [BU] [BU00001] ON [BU00001].[GID_ID] = [CO].[GID_RELATED_BU]    
WHERE     
 [CO].[CHK_ACTIVEFIELD] = 1 AND     
 [CO].[CHK_TEAMSELL] = 1 AND     
 [BU00001].[GID_ID] = @SegmentId  
  
--My Segment Overserve Accounts  
 Select @MyOverserveAccounts = IsNull(Count(Distinct(CO.GID_ID)),0)      
FROM [CO]    
LEFT JOIN [BU] [BU00001] ON [BU00001].[GID_ID] = [CO].[GID_RELATED_BU]    
WHERE     
 [CO].[CHK_ACTIVEFIELD] = 1 AND     
 [CO].[CHK_OVERSERVE] = 1 AND     
 [BU00001].[GID_ID] = @SegmentId    
  
--Underserved Accounts  
 Select @MyUnderservedAccounts = IsNull(Count(Distinct(CO.GID_ID)),0)    
FROM [CO]    
LEFT JOIN [BU] [BU00001] ON [BU00001].[GID_ID] = [CO].[GID_RELATED_BU]    
WHERE     
 [CO].[CHK_ACTIVEFIELD] = 1 AND     
 [CO].[CHK_OVERSERVE] = 1 AND     
 [BU00001].[GID_ID] = @SegmentId AND     
 (([CO].[DTT_LASTACSALES] <= @LastMonthStartDate OR   
 ([CO].[DTT_LASTACSALES] IS NULL)) AND   
 ([CO].[DTT_LASTOP] <= @LastMonthStartDate OR   
 ([CO].[DTT_LASTOP] IS NULL)) AND     
 ([CO].[DTT_LASTQT] <= @LastMonthStartDate OR  
 ([CO].[DTT_LASTQT] IS NULL)))    
  
--New Contacts  
 Select @MyNewContacts = ISNULL(Count(Distinct(CN.GID_ID)),0)    
FROM [CN]  
LEFT JOIN [CN_RELATED_CO] ON [CN].[GID_ID] = [CN_RELATED_CO].[GID_CN]  
LEFT JOIN [CO] [CO00001] ON [CO00001].[GID_ID] = [CN_RELATED_CO].[GID_CO]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [CO00001].[GID_RELATED_BU]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [CN].[GID_CreatedBy_US]  
WHERE (([CN].[DTT_CREATIONTIME] >= @LastMonthStartDate AND --'2025-02-01 05:00:00.000' AND   
([CN].[DTT_CREATIONTIME] <= DATEADD(SS,-1,@ThisMonthStartDate) OR --'2025-03-01 04:59:59.000' OR   
([CN].[DTT_CREATIONTIME] IS NULL))) AND   
[CN].[CHK_ACTIVEFIELD] = 1 AND   
[BU00002].[GID_ID] = @SegmentId) AND  
([CN].[SI__ShareState] = 2 OR   
(([CN].[SI__ShareState] < 2 OR  
([CN].[SI__ShareState] IS NULL)) AND  
[US00003].[GID_ID] = @LoginGID)) --'31633733-6433-3062-5553-31322f372f32'))  
  
  
--FROM [CN]    
--LEFT JOIN [CN_RELATED_CO] ON [CN].[GID_ID] = [CN_RELATED_CO].[GID_CN]    
--LEFT JOIN [CO] [CO00001] ON [CO00001].[GID_ID] = [CN_RELATED_CO].[GID_CO]    
--LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [CO00001].[GID_RELATED_BU]    
--WHERE     
-- ([CN].[DTT_CREATIONTIME] >= @LastMonthStartDate AND   
-- ([CN].[DTT_CREATIONTIME] <= DATEADD(SS,-1,@ThisMonthStartDate) OR     
-- ([CN].[DTT_CREATIONTIME] IS NULL))) AND     
-- [CN].[CHK_ACTIVEFIELD] = 1 AND     
-- [BU00002].[GID_ID] = @SegmentId    
   
--FROM [CN]  
--LEFT JOIN [CN_RELATED_CO] ON [CN].[GID_ID] = [CN_RELATED_CO].[GID_CN]  
--LEFT JOIN [CO] [CO00001] ON [CO00001].[GID_ID] = [CN_RELATED_CO].[GID_CO]  
--LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [CO00001].[GID_RELATED_BU]  
--LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [CN].[GID_CreatedBy_US]  
--WHERE (([CN].[DTT_CREATIONTIME] >= '2025-02-01 00:00:00.000' AND   
--([CN].[DTT_CREATIONTIME] <= '2025-02-28 23:59:59.000' OR   
--([CN].[DTT_CREATIONTIME] IS NULL))) AND   
--[CN].[CHK_ACTIVEFIELD] = 1 AND   
--[BU00002].[GID_ID] = @SegmentId) AND   
--([CN].[SI__ShareState] = 2 OR   
--(([CN].[SI__ShareState] < 2 OR   
--([CN].[SI__ShareState] IS NULL)) AND   
--[US00003].[GID_ID] = '38303437-6265-3061-5553-362f31332f32'))  
  
  
--Key Contacts  
 Select @MyKeyContacts = ISNULL(Count(Distinct(CN.GID_ID)),0)    
FROM [CN]    
LEFT JOIN [CN_RELATED_CO] ON [CN].[GID_ID] = [CN_RELATED_CO].[GID_CN]    
LEFT JOIN [CO] [CO00001] ON [CO00001].[GID_ID] = [CN_RELATED_CO].[GID_CO]    
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [CO00001].[GID_RELATED_BU]    
WHERE     
 [CN].[CHK_KEY] = 1 AND     
 [CN].[CHK_ACTIVEFIELD] = 1 AND     
 [BU00002].[GID_ID] = @SegmentId    
  
--Tasks Due Soon  
SELECT @MyTasksDue = ISNULL(COUNT(Distinct([TD].[GID_ID])),0)    
FROM [TD]  
LEFT JOIN [TD_ASSIGNEDTO_US] ON [TD].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_TD]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_US]  
LEFT JOIN [US_RELATED_BU] ON [US00001].[GID_ID] = [US_RELATED_BU].[GID_US]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [US_RELATED_BU].[GID_BU]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [TD].[GID_CreatedBy_US]  
WHERE   
(([BU00002].[GID_ID] = @SegmentId AND   
([TD].[MLS_STATUS] <> '4' OR   
([TD].[MLS_STATUS] IS NULL)) AND   
([TD].[DTT_DUETIME] >= @StartDateOfNextTwoWeeks AND --'2025-03-30 04:00:00.000' AND   
([TD].[DTT_DUETIME] <= DATEADD(SS,-1,DATEADD(HH,-1,@EndDateOfNextTwoWeeks)) OR --'2025-04-13 03:59:59.000' OR   
([TD].[DTT_DUETIME] IS NULL)))) AND   
([US00003].[GID_ID] = @LoginGID)) AND   
([TD].[SI__ShareState] = 2 OR   
(([TD].[SI__ShareState] < 2 OR   
([TD].[SI__ShareState] IS NULL)) AND   
[US00003].[GID_ID] = @LoginGID))  
  
--Overdue Tasks  
 SELECT @MyTasksOverdue = ISNULL(COUNT(Distinct([TD].[GID_ID])),0)    
FROM [TD]  
LEFT JOIN [TD_ASSIGNEDTO_US] ON [TD].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_TD]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_US]  
LEFT JOIN [US_RELATED_BU] ON [US00001].[GID_ID] = [US_RELATED_BU].[GID_US]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [US_RELATED_BU].[GID_BU]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [TD].[GID_CreatedBy_US]  
WHERE   
([BU00002].[GID_ID] = @SegmentId  AND   
([TD].[MLS_STATUS] <> '4' OR   
([TD].[MLS_STATUS] IS NULL)) AND   
([TD].[DTT_DUETIME] <= @TodayDate  OR --'2025-03-21 04:00:00.000' OR   
([TD].[DTT_DUETIME] IS NULL))) AND   
([TD].[SI__ShareState] = 2 OR   
(([TD].[SI__ShareState] < 2 OR   
([TD].[SI__ShareState] IS NULL)) AND   
[US00003].[GID_ID] = @LoginGID)) --'31633733-6433-3062-5553-31322f372f32'))  
  
  
--FROM [TD]  
--LEFT JOIN [TD_ASSIGNEDTO_US] ON [TD].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_TD]  
--LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [TD_ASSIGNEDTO_US].[GID_US]  
--LEFT JOIN [US_RELATED_BU] ON [US00001].[GID_ID] = [US_RELATED_BU].[GID_US]  
--LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [US_RELATED_BU].[GID_BU]  
--LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [TD].[GID_CreatedBy_US]  
--WHERE   
--(([BU00002].[GID_ID] = @SegmentId AND   
--([TD].[MLS_STATUS] <> '4' OR   
--([TD].[MLS_STATUS] IS NULL)) AND   
--([TD].[DTT_DUETIME] <= @TodayDate or --'2025-03-19 04:00:00.000' OR   
--([TD].[DTT_DUETIME] IS NULL))) AND   
--([US00003].[GID_ID]  = @LoginGID)) AND   
--([TD].[SI__ShareState] = 2 OR   
--(([TD].[SI__ShareState] < 2 OR   
--([TD].[SI__ShareState] IS NULL)) AND   
--[US00003].[GID_ID]  = @LoginGID))  
  
  
  
  
--Segment Team Sell Activities  
 SELECT @MyAcTeamSell = ISNULL(COUNT(Distinct([AC].[GID_ID])),0)     
FROM [AC]    
LEFT JOIN [BC] [BC00001] ON [BC00001].[GID_ID] = [AC].[GID_RELATED_BC]  
LEFT JOIN [US] [US00002] ON [US00002].[GID_ID] = [AC].[GID_CREDITEDTO_US]  
LEFT JOIN [CO] [CO00003] ON [CO00003].[GID_ID] = [AC].[GID_BILL_CO]  
LEFT JOIN [BU] [BU00004] ON [BU00004].[GID_ID] = [BC00001].[GID_RELATED_BU]  
LEFT JOIN [AC_RELATED_CO] ON [AC].[GID_ID] = [AC_RELATED_CO].[GID_AC]  
LEFT JOIN [CO] [CO00005] ON [CO00005].[GID_ID] = [AC_RELATED_CO].[GID_CO]  
LEFT JOIN [AC_RELATED_OP] ON [AC].[GID_ID] = [AC_RELATED_OP].[GID_AC]  
LEFT JOIN [OP] [OP00006] ON [OP00006].[GID_ID] = [AC_RELATED_OP].[GID_OP]  
LEFT JOIN [SO] [SO00007] ON [SO00007].[GID_ID] = [OP00006].[GID_FROM_SO]  
LEFT JOIN [US_RELATED_BC] ON [BC00001].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [AC].[GID_CreatedBy_US]  
WHERE   
(([BU00004].[GID_ID] = @SegmentId AND   
([CO00005].[CHK_TEAMSELL] = 1 OR   
[SO00007].[GID_ID] = '38626532-6232-3836-534f-372f31312f32')) AND   
([US00008].[GID_ID] = @LoginGID)) AND   
([AC].[SI__ShareState] = 2 OR   
(([AC].[SI__ShareState] < 2 OR   
([AC].[SI__ShareState] IS NULL)) AND   
[US00009].[GID_ID] = @LoginGID))  
  
--Sales Visits    
 SELECT @MySalesThisMonth= ISNULL(COUNT(Distinct([AC].[GID_ID])),0)     
FROM [AC]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]  
LEFT JOIN [BC] [BC00002] ON [BC00002].[GID_ID] = [AC].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00003] ON [BU00003].[GID_ID] = [BC00002].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00002].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [AC].[GID_CreatedBy_US]  
WHERE   
((([AC].[DTT_STARTTIME] >=  @ThisMonthStartDate AND --'2025-03-01 05:00:00.000' AND   
([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@NextMonthStartDate) OR --'2025-04-01 03:59:59.000' OR  
([AC].[DTT_STARTTIME] IS NULL))) AND   
[BU00003].[GID_ID]  = @SegmentId  AND  
[AC].[MLS_TYPE] = '11') AND   
([US00004].[GID_ID] = @LoginGID)) AND   
([AC].[SI__ShareState] = 2 OR   
(([AC].[SI__ShareState] < 2 OR  
([AC].[SI__ShareState] IS NULL)) AND   
[US00005].[GID_ID] = @LoginGID))  
  
--MySalesLastMonth                     
 SELECT @MySalesLastMonth= ISNULL(COUNT(Distinct([AC].[GID_ID])),0)     
FROM [AC]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [AC].[GID_CREDITEDTO_US]  
LEFT JOIN [BC] [BC00002] ON [BC00002].[GID_ID] = [AC].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00003] ON [BU00003].[GID_ID] = [BC00002].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00002].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [AC].[GID_CreatedBy_US]  
WHERE   
((([AC].[DTT_STARTTIME] >=  @LastMonthStartDate AND --'2025-02-01 05:00:00.000' AND   
([AC].[DTT_STARTTIME] <= DATEADD(SS,-1,@ThisMonthStartDate) OR --'2025-03-01 04:59:59.000' OR   
([AC].[DTT_STARTTIME] IS NULL))) AND  
[BU00003].[GID_ID] = @SegmentId AND   
[AC].[MLS_TYPE] = '11') AND   
([US00004].[GID_ID] = @LoginGID)) AND   
([AC].[SI__ShareState] = 2 OR   
(([AC].[SI__ShareState] < 2 OR   
([AC].[SI__ShareState] IS NULL)) AND   
[US00005].[GID_ID] = @LoginGID))  
  
--Segment Leads  
--Open Leads  
; WITH CTE AS(  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE]--, [OP].[CUR_WEIGHTEDOPPLINEVALUE]  
FROM [OP]    
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]  
LEFT JOIN [BC] [BC00002] ON [BC00002].[GID_ID] = [OP].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00003] ON [BU00003].[GID_ID] = [BC00002].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00002].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [OP_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [OP].[GID_CreatedBy_US]  
WHERE   
(([BU00003].[GID_ID] = @SegmentId AND   
([OP].[MLS_STATUS] = '0' OR   
([OP].[MLS_STATUS] IS NULL)) AND   
[OP].[MLS_SALESPROCESSSTAGE] = '1') AND   
([US00004].[GID_ID] = @LoginGID  OR   
[US00005].[GID_ID] = @LoginGID )) AND   
([OP].[SI__ShareState] = 2 OR   
(([OP].[SI__ShareState] < 2 OR   
([OP].[SI__ShareState] IS NULL)) AND   
[US00006].[GID_ID] = @LoginGID))  
)  
  
 SELECT @MyLeads = ISNULL(COUNT([GID_ID]),0),    
  @MyLeadsExptedVal=ISNULL(Sum([CUR_EXPECTEDVALUE]),0) --,@MyLeadsWeightedVal=ISNULL(Sum([CUR_WEIGHTEDOPPLINEVALUE]),0)  
 FROM CTE  
  
--Team Sell Leads  
; WITH CTE AS(  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE]--, [OP].[CUR_WEIGHTEDOPPLINEVALUE]  
FROM [OP]   
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]  
LEFT JOIN [BC] [BC00002] ON [BC00002].[GID_ID] = [OP].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00003] ON [BU00003].[GID_ID] = [BC00002].[GID_RELATED_BU]  
LEFT JOIN [SO] [SO00004] ON [SO00004].[GID_ID] = [OP].[GID_FROM_SO]  
LEFT JOIN [US_RELATED_BC] ON [BC00002].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [OP_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [OP].[GID_CreatedBy_US]  
WHERE (([BU00003].[GID_ID] = @SegmentId AND   
([OP].[MLS_STATUS] = '0' OR   
([OP].[MLS_STATUS] IS NULL)) AND   
[OP].[MLS_SALESPROCESSSTAGE] = '1' AND   
[SO00004].[GID_ID] = '38626532-6232-3836-534f-372f31312f32') AND   
([US00005].[GID_ID] = @LoginGID OR   
[US00006].[GID_ID]  = @LoginGID)) AND   
([OP].[SI__ShareState] = 2 OR (([OP].[SI__ShareState] < 2 OR   
([OP].[SI__ShareState] IS NULL)) AND   
[US00007].[GID_ID]  = @LoginGID))  
)  
 SELECT @MyTeamSellLeads = ISNULL(COUNT([GID_ID]),0),                        
  @MyTeamSellLeadsExptedVal=ISNULL(Sum([CUR_EXPECTEDVALUE]),0)  --,@MyTeamSellLeadsWeightedVal=ISNULL(Sum([CUR_WEIGHTEDOPPLINEVALUE]),0)  
 FROM CTE  
  
--New Leads  
; WITH CTE AS(  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE]--, [OP].[CUR_WEIGHTEDOPPLINEVALUE]   
FROM [OP]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]  
LEFT JOIN [BC] [BC00002] ON [BC00002].[GID_ID] = [OP].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00003] ON [BU00003].[GID_ID] = [BC00002].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00002].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [OP_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [OP].[GID_CreatedBy_US]  
WHERE   
(([BU00003].[GID_ID]  = @SegmentId AND   
([OP].[MLS_STATUS] = '0' OR  
([OP].[MLS_STATUS] IS NULL)) AND   
[OP].[MLS_SALESPROCESSSTAGE] = '1' AND   
([OP].[DTT_CREATIONTIME] >=  @StartDateOfLastTwoWeeks AND -- '2025-03-02 05:00:00.000' AND   
([OP].[DTT_CREATIONTIME] <= DATEADD(SS,-1,@EndDateOfLastTwoWeeks) OR --'2025-03-16 03:59:59.000' OR   
([OP].[DTT_CREATIONTIME] IS NULL)))) AND   
([US00004].[GID_ID] = @LoginGID OR  
[US00005].[GID_ID] = @LoginGID)) AND   
([OP].[SI__ShareState] = 2 OR   
(([OP].[SI__ShareState] < 2 OR   
([OP].[SI__ShareState] IS NULL)) AND   
[US00006].[GID_ID] = @LoginGID))  
)  
  
 SELECT @MyNewLeads = ISNULL(COUNT([GID_ID]),0),                        
  @MyNewLeadsExptedVal=ISNULL(Sum([CUR_EXPECTEDVALUE]),0) --,@MyNewLeadsWeightedVal=ISNULL(Sum([CUR_WEIGHTEDOPPLINEVALUE]),0)    
 FROM CTE  
  
--Overdue Leads  
; WITH CTE AS(  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE]--, [OP].[CUR_WEIGHTEDOPPLINEVALUE]   
 FROM [OP]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [OP].[GID_CREDITEDTO_US]  
LEFT JOIN [BC] [BC00002] ON [BC00002].[GID_ID] = [OP].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00003] ON [BU00003].[GID_ID] = [BC00002].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00002].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [OP_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [OP].[GID_CreatedBy_US]  
WHERE   
(([BU00003].[GID_ID] = @SegmentId AND   
([OP].[MLS_STATUS] = '0' OR   
([OP].[MLS_STATUS] IS NULL)) AND   
[OP].[MLS_SALESPROCESSSTAGE] = '1' AND   
([OP].[DTT_NEXTACTIONDATE] <= @TodayDate OR --'2025-03-20 05:00:00.000'  '2025-03-19 04:00:00.000' OR   
([OP].[DTT_NEXTACTIONDATE] IS NULL))) AND   
([US00004].[GID_ID] = @LoginGID OR   
[US00005].[GID_ID] = @LoginGID)) AND   
([OP].[SI__ShareState] = 2 OR   
(([OP].[SI__ShareState] < 2 OR   
([OP].[SI__ShareState] IS NULL)) AND   
[US00006].[GID_ID] = @LoginGID))  
 )  
  
SELECT @MyOverdueLeads = ISNULL(COUNT([GID_ID]),0),                        
  @MyOverdueLeadsExptedVal=ISNULL(Sum([CUR_EXPECTEDVALUE]),0) --,@MyOverdueLeadsWeightedVal=ISNULL(Sum([CUR_WEIGHTEDOPPLINEVALUE]),0)    
 FROM CTE  
  
--My Segment Open Opportunities  
--Open Opportunities    
; WITH CTE AS(  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE], [OP].[CUR_WEIGHTEDOPPLINEVALUE]  
FROM [OP]  
LEFT JOIN [BC] [BC00001] ON [BC00001].[GID_ID] = [OP].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [BC00001].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00001].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [OP_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [OP].[GID_CreatedBy_US]  
WHERE   
(([BU00002].[GID_ID] = @SegmentId  AND   
([OP].[MLS_STATUS] = '0' OR   
([OP].[MLS_STATUS] IS NULL)) AND   
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR   
([OP].[MLS_SALESPROCESSSTAGE] IS NULL))) AND   
([US00003].[GID_ID] = @LoginGID  OR   
[US00004].[GID_ID] = @LoginGID )) AND   
([OP].[SI__ShareState] = 2 OR   
(([OP].[SI__ShareState] < 2 OR   
([OP].[SI__ShareState] IS NULL)) AND   
[US00005].[GID_ID] = @LoginGID ))  
 )  
  
 SELECT @MyOpenOppsqty = ISNULL(COUNT([GID_ID]),0),                  
  @MyOpenOppsExptedVal=ISNULL(Sum([CUR_EXPECTEDVALUE]),0),                        
  @MyOpenOppsWeightedVal=ISNULL(Sum([CUR_WEIGHTEDOPPLINEVALUE]),0)    
 FROM CTE  
  
--Team Sell Opportunities    
 ;WITH CTE AS(  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE], [OP].[CUR_WEIGHTEDEXPECTEDVALUE]   
FROM [OP]  
LEFT JOIN [SO] [SO00001] ON [SO00001].[GID_ID] = [OP].[GID_FROM_SO]  
LEFT JOIN [BC] [BC00002] ON [BC00002].[GID_ID] = [OP].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00003] ON [BU00003].[GID_ID] = [BC00002].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00002].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [OP_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00006] ON [US00006].[GID_ID] = [OP].[GID_CreatedBy_US]  
WHERE   
((([OP].[MLS_STATUS] = '0' OR   
([OP].[MLS_STATUS] IS NULL)) AND   
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR   
([OP].[MLS_SALESPROCESSSTAGE] IS NULL)) AND   
[SO00001].[GID_ID] = '38626532-6232-3836-534f-372f31312f32' AND   
[BU00003].[GID_ID] = @SegmentId ) AND   
([US00004].[GID_ID] = @LoginGID  OR   
[US00005].[GID_ID] = @LoginGID )) AND   
([OP].[SI__ShareState] = 2 OR   
(([OP].[SI__ShareState] < 2 OR   
([OP].[SI__ShareState] IS NULL)) AND   
[US00006].[GID_ID] = @LoginGID ))  
)  
  
 SELECT @MyTeamSellOpps_count = ISNULL(COUNT([GID_ID]),0),                          
  @MyTeamSellOpps_expval = ISNULL(Sum([CUR_EXPECTEDVALUE]),0),                          
  @MyTeamSellOpps_wghtval = ISNULL(Sum([CUR_WEIGHTEDEXPECTEDVALUE]),0)  
 FROM CTE  
   
--Opportunities Due(Next 30 days)    
; WITH CTE AS(  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE], [OP].[CUR_WEIGHTEDEXPECTEDVALUE]   
FROM [OP]  
LEFT JOIN [BC] [BC00001] ON [BC00001].[GID_ID] = [OP].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [BC00001].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00001].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [OP_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [OP].[GID_CreatedBy_US]  
WHERE   
(([BU00002].[GID_ID] = @SegmentId AND   
([OP].[MLS_STATUS] = '0' OR   
([OP].[MLS_STATUS] IS NULL)) AND   
(([OP].[DTT_EXPCLOSEDATE] >=  @TodayDate AND --'2025-03-19 04:00:00.000' AND   
([OP].[DTT_EXPCLOSEDATE] <= @MonthAgoDate OR  --'2025-04-19 03:59:59.000' OR   
([OP].[DTT_EXPCLOSEDATE] IS NULL))) OR   
([OP].[DTT_NEXTACTIONDATE] >=  @TodayDate AND --'2025-03-19 04:00:00.000' AND   
([OP].[DTT_NEXTACTIONDATE] <= @MonthAgoDate OR  --'2025-04-19 03:59:59.000' OR   
([OP].[DTT_NEXTACTIONDATE] IS NULL)))) AND   
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR   
([OP].[MLS_SALESPROCESSSTAGE] IS NULL))) AND   
([US00003].[GID_ID] = @LoginGID OR   
[US00004].[GID_ID] = @LoginGID)) AND   
([OP].[SI__ShareState] = 2 OR   
(([OP].[SI__ShareState] < 2 OR   
([OP].[SI__ShareState] IS NULL)) AND   
[US00005].[GID_ID] = @LoginGID))  
 )  
  
 SELECT @MyDueNext30DaysOpps_count = ISNULL(COUNT([GID_ID]),0),                          
  @MyDueNext30DaysOpps_expval = ISNULL(Sum([CUR_EXPECTEDVALUE]),0),                          
  @MyDueNext30DaysOpps_wghtval = ISNULL(Sum([CUR_WEIGHTEDEXPECTEDVALUE]),0)   
 FROM CTE  
  
--Overdue  Opportunities   
; WITH CTE AS(  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_EXPECTEDVALUE], [OP].[CUR_WEIGHTEDEXPECTEDVALUE]    
 FROM [OP]  
LEFT JOIN [BC] [BC00001] ON [BC00001].[GID_ID] = [OP].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [BC00001].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00001].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [OP_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [OP].[GID_CreatedBy_US]  
WHERE   
(([BU00002].[GID_ID] = @SegmentId AND   
([OP].[MLS_STATUS] = '0' OR   
([OP].[MLS_STATUS] IS NULL)) AND   
(([OP].[DTT_EXPCLOSEDATE] <= @TodayDate OR --'2025-03-19 04:00:00.000' OR   
([OP].[DTT_EXPCLOSEDATE] IS NULL)) OR   
([OP].[DTT_NEXTACTIONDATE] <= @TodayDate OR --'2025-03-19 04:00:00.000' OR   
([OP].[DTT_NEXTACTIONDATE] IS NULL))) AND   
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR   
([OP].[MLS_SALESPROCESSSTAGE] IS NULL))) AND   
([US00003].[GID_ID] = @LoginGID OR   
[US00004].[GID_ID] = @LoginGID)) AND   
([OP].[SI__ShareState] = 2 OR   
(([OP].[SI__ShareState] < 2 OR   
([OP].[SI__ShareState] IS NULL)) AND   
[US00005].[GID_ID] = @LoginGID))  
 )  
  
 SELECT @MyOverdueOpps_count = ISNULL(COUNT([GID_ID]),0),                          
  @MyOverdueOpps_expval = ISNULL(Sum([CUR_EXPECTEDVALUE]),0),                          
  @MyOverdueOpps_wghtval = ISNULL(Sum([CUR_WEIGHTEDEXPECTEDVALUE]),0)  
 FROM CTE  
  
--Segment Closed Opportunities  
  --Won Opportunities  
; WITH CTE AS(  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_OPPLINEVALUE]  
 FROM [OP]  
LEFT JOIN [BC] [BC00001] ON [BC00001].[GID_ID] = [OP].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [BC00001].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00001].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [OP_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [OP].[GID_CreatedBy_US]  
WHERE   
((([OP].[DTT_DATECLOSED] >= @ThreeMonthsAgoDate AND --'2024-12-20 05:00:00.000' AND   
([OP].[DTT_DATECLOSED] <= DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR --'2025-03-20 03:59:59.000' OR   
([OP].[DTT_DATECLOSED] IS NULL))) AND   
[BU00002].[GID_ID] = @SegmentId AND   
[OP].[MLS_STATUS] = '3' AND   
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR   
([OP].[MLS_SALESPROCESSSTAGE] IS NULL))) AND   
([US00003].[GID_ID] = @LoginGID OR   
[US00004].[GID_ID] = @LoginGID)) AND   
([OP].[SI__ShareState] = 2 OR   
(([OP].[SI__ShareState] < 2 OR   
([OP].[SI__ShareState] IS NULL)) AND   
[US00005].[GID_ID] = @LoginGID))  
)  
  
 SELECT @MyOpps_won_Qty = ISNULL(COUNT([GID_ID]),0),                          
  @MyOpps_won_Tot = ISNULL(Sum([CUR_OPPLINEVALUE]),0)    
 FROM CTE  
  
--Lost Opportunities   
; WITH CTE AS(  
 SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_OPPLINEVALUE]  
 FROM [OP]  
LEFT JOIN [BC] [BC00001] ON [BC00001].[GID_ID] = [OP].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [BC00001].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00001].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [OP_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [OP].[GID_CreatedBy_US]  
WHERE   
((([OP].[DTT_DATECLOSED] >= @ThreeMonthsAgoDate AND --'2024-12-20 05:00:00.000' AND   
([OP].[DTT_DATECLOSED] <= DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR --'2025-03-20 03:59:59.000' OR   
([OP].[DTT_DATECLOSED] IS NULL))) AND   
[BU00002].[GID_ID] = @SegmentId AND   
[OP].[MLS_STATUS] = '4' AND   
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR   
([OP].[MLS_SALESPROCESSSTAGE] IS NULL))) AND   
([US00003].[GID_ID] = @LoginGID OR   
[US00004].[GID_ID] = @LoginGID)) AND   
([OP].[SI__ShareState] = 2 OR   
(([OP].[SI__ShareState] < 2 OR   
([OP].[SI__ShareState] IS NULL)) AND   
[US00005].[GID_ID] = @LoginGID))  
)  
  
 SELECT @MyOpps_Lost_Qty = ISNULL(COUNT([GID_ID]),0.0),                          
  @MyOpps_Lost_Tot = ISNULL(Sum([CUR_OPPLINEVALUE]),0.0)  
 FROM CTE  
  
--Cancelled Opportunities  
; WITH CTE AS(  
SELECT DISTINCT [OP].[GID_ID], [OP].[CUR_OPPLINEVALUE]  
FROM [OP]  
LEFT JOIN [BC] [BC00001] ON [BC00001].[GID_ID] = [OP].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [BC00001].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00001].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [OP_INVOLVES_US] ON [OP].[GID_ID] = [OP_INVOLVES_US].[GID_OP]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [OP_INVOLVES_US].[GID_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [OP].[GID_CreatedBy_US]  
WHERE   
((([OP].[DTT_DATECLOSED] >=  @ThreeMonthsAgoDate AND --'2024-12-20 05:00:00.000' AND   
([OP].[DTT_DATECLOSED] <= DATEADD(SS,-1,DATEADD(DD,1,@TodayDate)) OR --'2025-03-20 03:59:59.000' OR  
([OP].[DTT_DATECLOSED] IS NULL))) AND  
[BU00002].[GID_ID] = @SegmentId AND  
[OP].[MLS_STATUS] = '5' AND   
([OP].[MLS_SALESPROCESSSTAGE] <> '1' OR   
([OP].[MLS_SALESPROCESSSTAGE] IS NULL))) AND  
([US00003].[GID_ID] = @LoginGID OR  
[US00004].[GID_ID] = @LoginGID)) AND   
([OP].[SI__ShareState] = 2 OR   
(([OP].[SI__ShareState] < 2 OR   
([OP].[SI__ShareState] IS NULL)) AND  
[US00005].[GID_ID] = @LoginGID))  
)  
  
 SELECT @MyOpps_Cancelled_Qty = ISNULL(COUNT([GID_ID]),0),                          
  @MyOpps_Cancelled_Tot = ISNULL(Sum([CUR_OPPLINEVALUE]),0)    
 FROM CTE  
                  
-- Calculate the won rate, ensuring no division by zero                  
SET @MyOpps_won_Rate =                   
    CASE                   
        WHEN CAST(@MyOpps_won_Qty AS MONEY) + CAST(@MyOpps_Lost_Qty AS MONEY) = 0 THEN 0  -- Return 0 if the total is zero                  
        ELSE (CAST(@MyOpps_won_Qty AS MONEY)/(CAST(@MyOpps_won_Qty AS MONEY) + CAST(@MyOpps_Lost_Qty AS MONEY)) )*100              
    END                  
                  
-- Calculate the lost rate, ensuring no division by zero                  
SET @MyOpps_Lost_Rate =                   
    CASE                   
        WHEN CAST(@MyOpps_won_Qty AS MONEY) + CAST(@MyOpps_Lost_Qty AS MONEY) = 0 THEN 0  -- Return 0 if the total is zero                  
        ELSE (CAST(@MyOpps_Lost_Qty AS MONEY)/(CAST(@MyOpps_won_Qty AS MONEY) + CAST(@MyOpps_Lost_Qty AS MONEY)))*100  
    END                  
                  
-- Calculate the cancelled rate, ensuring no division by zero                  
SET @MyOpps_Cancelled_Rate =                   
    CASE                   
        WHEN CAST(@MyOpps_won_Qty AS MONEY) + CAST(@MyOpps_Lost_Qty AS MONEY) + CAST(@MyOpps_Cancelled_Qty AS MONEY) = 0 THEN 0  -- Return 0 if the total is zero                  
        ELSE (CAST(@MyOpps_Cancelled_Qty AS MONEY)/(CAST(@MyOpps_won_Qty AS MONEY) + CAST(@MyOpps_Lost_Qty AS MONEY) + CAST(@MyOpps_Cancelled_Qty AS MONEY)) )*100               
    END     
  
--Open RFQs  
; WITH CTE AS(  
 SELECT Distinct [QT].[GID_ID],   
[QT].[CUR_LINETOTALOPEN],   
--[QT].CUR_ExpectedValue,  
[QT].SI__GROSSMARGIN    
FROM [QT]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
LEFT JOIN [BC] [BC00002] ON [BC00002].[GID_ID] = [QT].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00003] ON [BU00003].[GID_ID] = [BC00002].[GID_RELATED_BU]  
LEFT JOIN [US_RELATED_BC] ON [BC00002].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00005] ON [US00005].[GID_ID] = [QT].[GID_CreatedBy_US]  
WHERE   
(([BU00003].[GID_ID] = @SegmentId AND  
([QT].[MLS_STATUS] = '0' OR   
([QT].[MLS_STATUS] IS NULL)) AND  
(([QT].[MLS_STAGE] <> '6' OR  
([QT].[MLS_STAGE] IS NULL)) AND  
([QT].[MLS_STAGE] <> '7' OR   
([QT].[MLS_STAGE] IS NULL)) AND  
([QT].[MLS_STAGE] <> '0' AND   
[QT].[MLS_STAGE] IS NOT NULL) AND   
([QT].[MLS_STAGE] <> '8' OR   
([QT].[MLS_STAGE] IS NULL)))) AND   
([US00004].[GID_ID] = @LoginGID)) AND   
([QT].[SI__ShareState] = 2 OR   
(([QT].[SI__ShareState] < 2 OR   
([QT].[SI__ShareState] IS NULL)) AND   
[US00005].[GID_ID] = @LoginGID))  
 )  
  
 SELECT @OpenRFQsQty = ISNULL(COUNT([GID_ID]),0),    
  @OpenRFQstotal = ISNULL(sum([CUR_LINETOTALOPEN]),0),      
  --@OpenRFQstotal = ISNULL(sum(CUR_ExpectedValue),0),  
  @OpenRFQsmargin = ISNULL(AVG(SI__GROSSMARGIN),0)  
 FROM CTE  
  
--My Segment Open Quotes    
; WITH CTE AS(  
 SELECT Distinct [QT].[GID_ID],   
[QT].[CUR_LINETOTALOPEN],   
--[QT].CUR_ExpectedValue,  
[QT].SI__GROSSMARGIN  
FROM [QT]  
LEFT JOIN [BU] [BU00001] ON [BU00001].[GID_ID] = [QT].[GID_RELATED_BU]  
LEFT JOIN [BC] [BC00002] ON [BC00002].[GID_ID] = [QT].[GID_RELATED_BC]  
LEFT JOIN [US_RELATED_BC] ON [BC00002].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00004] ON [US00004].[GID_ID] = [QT].[GID_CreatedBy_US]  
WHERE  
(([BU00001].[GID_ID] = @SegmentId AND  
([QT].[MLS_STATUS] = '0' OR   
([QT].[MLS_STATUS] IS NULL))) AND  
([US00003].[GID_ID] = @LoginGID)) AND   
([QT].[SI__ShareState] = 2 OR  
(([QT].[SI__ShareState] < 2 OR   
([QT].[SI__ShareState] IS NULL)) AND  
[US00004].[GID_ID] = @LoginGID))  
)  
  
 SELECT @MyQuotesQty = ISNULL(COUNT([GID_ID]),0),    
  @MyQuotesTotal = ISNULL(SUM([CUR_LINETOTALOPEN]),0),  
  --@MyQuotesTotal = ISNULL(SUM(CUR_ExpectedValue),0),    
  @MyQuotesMargin = ISNULL(AVG(SI__GROSSMARGIN),0)    
 FROM CTE  
  
--Open Strategic Quotes  
; WITH CTE AS(  
 SELECT Distinct [QT].[GID_ID],   
[QT].[CUR_LINETOTALOPEN],  
[QT].CUR_ExpectedValue,  
[QT].SI__GROSSMARGIN   
  
FROM [QT]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [QT].[GID_RELATED_BU]  
LEFT JOIN [QL] [QL00003] ON [QL00003].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00004] ON [PF00004].[GID_ID] = [QL00003].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00005] ON [PC00005].[GID_ID] = [PF00004].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00006] ON [VE00006].[GID_ID] = [QL00003].[GID_FOR_VE]  
LEFT JOIN [BC] [BC00007] ON [BC00007].[GID_ID] = [QT].[GID_RELATED_BC]  
LEFT JOIN [US_RELATED_BC] ON [BC00007].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [QT].[GID_CreatedBy_US]  
WHERE   
((([QT].[CHK_BUDGET] = 0 OR   
([QT].[CHK_BUDGET] IS NULL)) AND   
([QT].[MLS_STATUS] = '0' OR  
([QT].[MLS_STATUS] IS NULL)) AND   
[BU00002].[GID_ID] = @SegmentId AND   
([QT].[CUR_LINETOTALOPEN] >= '20000' OR   
([QT].[CUR_LINETOTALOPEN] >= '5000' AND  
([PC00005].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR   
[PF00004].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
[PF00004].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR   
[VE00006].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
[VE00006].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR   
[VE00006].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))) AND   
([US00008].[GID_ID] = @LoginGID)) AND   
([QT].[SI__ShareState] = 2 OR   
(([QT].[SI__ShareState] < 2 OR   
([QT].[SI__ShareState] IS NULL)) AND   
[US00009].[GID_ID] = @LoginGID))  
 )  
  
 SELECT @OpenStrategicQuotesQty = ISNULL(COUNT([GID_ID]),0),                    
  @OpenStrategicQuotesTotal = ISNULL(SUM([CUR_LINETOTALOPEN]),0),   
  --@OpenStrategicQuotesTotal = ISNULL(SUM(CUR_ExpectedValue),0),   
  @OpenStrategicQuotesMargin = ISNULL(AVG(SI__GROSSMARGIN),0)  
 FROM CTE  
  
--New Strategic Quotes  
; WITH CTE AS(  
 SELECT Distinct [QT].[GID_ID],   
[QT].[CUR_LINETOTALOPEN],  
[QT].CUR_ExpectedValue,  
[QT].SI__GROSSMARGIN   
FROM [QT]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [QT].[GID_RELATED_BU]  
LEFT JOIN [QL] [QL00003] ON [QL00003].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00004] ON [PF00004].[GID_ID] = [QL00003].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00005] ON [PC00005].[GID_ID] = [PF00004].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00006] ON [VE00006].[GID_ID] = [QL00003].[GID_FOR_VE]  
LEFT JOIN [BC] [BC00007] ON [BC00007].[GID_ID] = [QT].[GID_RELATED_BC]  
LEFT JOIN [US_RELATED_BC] ON [BC00007].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [QT].[GID_CreatedBy_US]  
WHERE   
((([QT].[DTT_CREATIONTIME] >= DATEADD(HH,1,@StartDateOfLastTwoWeeks) AND --'2025-03-02 05:00:00.000' AND   
([QT].[DTT_CREATIONTIME] <= DATEADD(SS,-1,@EndDateOfLastTwoWeeks) OR --'2025-03-16 03:59:59.000' OR   
([QT].[DTT_CREATIONTIME] IS NULL))) AND  
([QT].[CHK_BUDGET] = 0 OR   
([QT].[CHK_BUDGET] IS NULL)) AND   
([QT].[MLS_STATUS] = '0' OR   
([QT].[MLS_STATUS] IS NULL)) AND   
[BU00002].[GID_ID] = @SegmentId AND   
([QT].[CUR_LINETOTALOPEN] >= '20000' OR   
([QT].[CUR_LINETOTALOPEN] >= '5000' AND   
([PC00005].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR   
[PF00004].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
[PF00004].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR   
[VE00006].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR  
[VE00006].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR  
[VE00006].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))) AND   
([US00008].[GID_ID] = @LoginGID)) AND   
([QT].[SI__ShareState] = 2 OR   
(([QT].[SI__ShareState] < 2 OR   
([QT].[SI__ShareState] IS NULL)) AND  
[US00009].[GID_ID] = @LoginGID))  
 )  
  
 SELECT @NewStrategicQuotesQty = ISNULL(COUNT([GID_ID]),0),                           
  @NewStrategicQuotesTotal = ISNULL(SUM([CUR_LINETOTALOPEN]),0),   
  --@NewStrategicQuotesTotal = ISNULL(SUM(CUR_ExpectedValue),0),  
  @NewStrategicQuotesMargin = ISNULL(AVG(SI__GROSSMARGIN),0)  
 FROM CTE  
  
--Overdue Strategic Quotes  
; WITH CTE AS(  
 SELECT Distinct [QT].[GID_ID],   
[QT].[CUR_LINETOTALOPEN],   
--[QT].CUR_ExpectedValue,  
[QT].SI__GROSSMARGIN   
FROM [QT]  
LEFT JOIN [US] [US00001] ON [US00001].[GID_ID] = [QT].[GID_CREDITEDTO_US]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [QT].[GID_RELATED_BU]  
LEFT JOIN [QL] [QL00003] ON [QL00003].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00004] ON [PF00004].[GID_ID] = [QL00003].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00005] ON [PC00005].[GID_ID] = [PF00004].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00006] ON [VE00006].[GID_ID] = [QL00003].[GID_FOR_VE]  
LEFT JOIN [BC] [BC00007] ON [BC00007].[GID_ID] = [QT].[GID_RELATED_BC]  
LEFT JOIN [US_RELATED_BC] ON [BC00007].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00009] ON [US00009].[GID_ID] = [QT].[GID_CreatedBy_US]  
WHERE   
((([QT].[CHK_BUDGET] = 0 OR  
([QT].[CHK_BUDGET] IS NULL)) AND   
([QT].[MLS_STATUS] = '0' OR   
([QT].[MLS_STATUS] IS NULL)) AND   
([QT].[DTT_CREATIONTIME] < @FiveDaysAgoDate OR  --'2025-03-14 04:00:00.000' OR  
([QT].[DTT_CREATIONTIME] IS NULL)) AND  
[BU00002].[GID_ID] = @SegmentId AND  
((([QT].[DTT_EXPCLOSEDATE] IS NULL) OR   
[QT].[DTT_EXPCLOSEDATE] = '1753-01-02 23:59:59.000') OR   
([QT].[DTT_EXPCLOSEDATE] <=  @TodayDate OR --'2025-03-19 04:00:00.000' OR   
([QT].[DTT_EXPCLOSEDATE] IS NULL)) OR  
([QT].[DTT_NEXTACTIONDATE] <=  @TodayDate OR --'2025-03-19 04:00:00.000' OR   
([QT].[DTT_NEXTACTIONDATE] IS NULL))) AND  
([QT].[CUR_LINETOTALOPEN] >= '20000' OR  
([QT].[CUR_LINETOTALOPEN] >= '5000' AND   
([PC00005].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR  
[PF00004].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR  
[VE00006].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
[VE00006].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR   
[VE00006].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))) AND   
([US00008].[GID_ID] = @LoginGID)) AND   
([QT].[SI__ShareState] = 2 OR   
(([QT].[SI__ShareState] < 2 OR   
([QT].[SI__ShareState] IS NULL)) AND   
[US00009].[GID_ID] = @LoginGID))  
 )  
  
 SELECT @OverdueStrategicQuotesQty = ISNULL(COUNT([GID_ID]),0),                         
  @OverdueStrategicQuotesTotal = ISNULL(SUM([CUR_LINETOTALOPEN]),0),    
  --@OverdueStrategicQuotesTotal = ISNULL(SUM(CUR_ExpectedValue),0),    
  @OverdueStrategicQuotesMargin = ISNULL(AVG([SI__GROSSMARGIN]),0)    
 FROM CTE  
  
--Strategic Quotes - Won  
; WITH CTE AS(   
 SELECT Distinct [QT].[GID_ID], [QT].CUR_LineTotalWon, [QT].SI__GROSSMARGIN   
FROM [QT]  
LEFT JOIN [BC] [BC00001] ON [BC00001].[GID_ID] = [QT].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [BC00001].[GID_RELATED_BU]  
LEFT JOIN [QL] [QL00003] ON [QL00003].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00004] ON [PF00004].[GID_ID] = [QL00003].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00005] ON [PC00005].[GID_ID] = [PF00004].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00006] ON [VE00006].[GID_ID] = [QL00003].[GID_FOR_VE]  
LEFT JOIN [US_RELATED_BC] ON [BC00001].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [QT].[GID_CreatedBy_US]  
WHERE   
((([QT].[DTT_DATECLOSED] >=  @NintyOneDaysAgoDate AND --'2024-12-20 05:00:00.000' AND   
([QT].[DTT_DATECLOSED] <= @TomorrowDate OR --'2025-03-20 03:59:59.000' OR   
([QT].[DTT_DATECLOSED] IS NULL))) AND   
[QT].[MLS_STATUS] = '2' AND  
[BU00002].[GID_ID]  = @SegmentId AND   
([QT].[CUR_TOTALAMOUNT] >= '20000' OR  
([QT].[CUR_TOTALAMOUNT] >= '5000' AND  
([PC00005].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR   
[PF00004].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
[PF00004].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR  
[VE00006].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
[VE00006].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR   
[VE00006].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))) AND   
([US00007].[GID_ID] = @LoginGID)) AND   
([QT].[SI__ShareState] = 2 OR  
(([QT].[SI__ShareState] < 2 OR   
([QT].[SI__ShareState] IS NULL)) AND   
[US00008].[GID_ID] = @LoginGID))  
)  
  
 SELECT @StrategicQuotesWonQty = ISNULL(COUNT([GID_ID]),0),   
   @StrategicQuotesWonTotal = ISNULL(sum(CUR_LineTotalWon),0)   
  --@StrategicQuotesWonTotal = ISNULL(sum(CUR_LineTotalOpen),0)   
 FROM CTE  
  
--Strategic Quotes - Lost  
; WITH CTE AS(   
 SELECT Distinct [QT].[GID_ID], [QT].CUR_LineTotalLost, [QT].SI__GROSSMARGIN   
FROM [QT]  
LEFT JOIN [BC] [BC00001] ON [BC00001].[GID_ID] = [QT].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [BC00001].[GID_RELATED_BU]  
LEFT JOIN [QL] [QL00003] ON [QL00003].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00004] ON [PF00004].[GID_ID] = [QL00003].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00005] ON [PC00005].[GID_ID] = [PF00004].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00006] ON [VE00006].[GID_ID] = [QL00003].[GID_FOR_VE]  
LEFT JOIN [US_RELATED_BC] ON [BC00001].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [QT].[GID_CreatedBy_US]  
WHERE   
((([QT].[DTT_DATECLOSED] >= @NintyOneDaysAgoDate AND --'2024-12-20 05:00:00.000' AND   
([QT].[DTT_DATECLOSED] <= @TomorrowDate OR --'2025-03-20 03:59:59.000' OR   
([QT].[DTT_DATECLOSED] IS NULL))) AND  
[QT].[MLS_STATUS] = '3' AND   
[BU00002].[GID_ID] = @SegmentId AND   
([QT].[CUR_TOTALAMOUNT] >= '20000' OR   
([QT].[CUR_TOTALAMOUNT] >= '5000' AND  
([PC00005].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR  
[PF00004].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR  
[PF00004].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR  
[VE00006].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR   
[VE00006].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR  
[VE00006].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))) AND   
([US00007].[GID_ID] = @LoginGID)) AND   
([QT].[SI__ShareState] = 2 OR   
(([QT].[SI__ShareState] < 2 OR   
([QT].[SI__ShareState] IS NULL)) AND   
[US00008].[GID_ID] = @LoginGID))  
)  
  
 SELECT @StrategicQuotesLostQty = ISNULL(COUNT([GID_ID]),0),                           
  @StrategicQuotesLostTotal = ISNULL(SUM(CUR_LineTotalLost),0)    
  --@StrategicQuotesLostTotal = ISNULL(SUM(CUR_LineTotalOpen),0)   
 FROM CTE  
  
--Strategic Quotes - Cancelled  
; WITH CTE AS(   
 SELECT Distinct [QT].[GID_ID], [QT].CUR_LineTotalCancelled, [QT].SI__GROSSMARGIN    
  FROM [QT]  
LEFT JOIN [BC] [BC00001] ON [BC00001].[GID_ID] = [QT].[GID_RELATED_BC]  
LEFT JOIN [BU] [BU00002] ON [BU00002].[GID_ID] = [BC00001].[GID_RELATED_BU]  
LEFT JOIN [QL] [QL00003] ON [QL00003].[GID_IN_QT] = [QT].[GID_ID]  
LEFT JOIN [PF] [PF00004] ON [PF00004].[GID_ID] = [QL00003].[GID_RELATED_PF]  
LEFT JOIN [PC] [PC00005] ON [PC00005].[GID_ID] = [PF00004].[GID_RELATED_PC]  
LEFT JOIN [VE] [VE00006] ON [VE00006].[GID_ID] = [QL00003].[GID_FOR_VE]  
LEFT JOIN [US_RELATED_BC] ON [BC00001].[GID_ID] = [US_RELATED_BC].[GID_BC]  
LEFT JOIN [US] [US00007] ON [US00007].[GID_ID] = [US_RELATED_BC].[GID_US]  
LEFT JOIN [US] [US00008] ON [US00008].[GID_ID] = [QT].[GID_CreatedBy_US]  
WHERE   
((([QT].[DTT_DATECLOSED] >= @NintyOneDaysAgoDate AND --'2024-12-20 05:00:00.000' AND  
([QT].[DTT_DATECLOSED] <= @TomorrowDate OR --'2025-03-20 03:59:59.000' OR  
([QT].[DTT_DATECLOSED] IS NULL))) AND  
[QT].[MLS_STATUS] = '4' AND  
[BU00002].[GID_ID] = @SegmentId AND  
([QT].[CUR_TOTALAMOUNT] >= '20000' OR  
([QT].[CUR_TOTALAMOUNT] >= '5000' AND   
([PC00005].[GID_ID] = 'd9a72d88-2d95-420f-5043-ae4d00b98a49' OR   
[QT].[MLS_TYPE] = '6' OR  
[PF00004].[GID_ID] = '81d0b3f5-18cf-4783-5046-aeaf0083381c' OR   
[PF00004].[GID_ID] = '33323435-3861-3665-5046-382f382f3230' OR  
[VE00006].[GID_ID] = 'b93a1e43-92a1-44c0-5645-aee3007a9025' OR  
[VE00006].[GID_ID] = 'cbe98a8e-7940-45a5-5645-aee3007a9025' OR  
[VE00006].[GID_ID] = 'e4ab56f9-6415-42a8-5645-aee3007a9025')))) AND  
([US00007].[GID_ID] = @LoginGID)) AND   
([QT].[SI__ShareState] = 2 OR  
(([QT].[SI__ShareState] < 2 OR  
([QT].[SI__ShareState] IS NULL)) AND  
[US00008].[GID_ID] = @LoginGID))  
)  
  
 SELECT @StrategicQuotesCancelledQty= ISNULL(COUNT([GID_ID]),0),                           
  @StrategicQuotesCancelledTotal=ISNULL(SUM(CUR_LineTotalCancelled),0)  
  --@StrategicQuotesCancelledTotal=ISNULL(SUM(CUR_LineTotalOpen),0)  
  --@StrategicQuotesCancelledTotal=ISNULL(SUM([CUR_TOTALAMOUNT]),0)  
 FROM CTE  
  
-- Calculate the won rate, ensuring no division by zero                  
SELECT @StrategicQuotesWonRate =                   
    CASE                   
        WHEN CAST(@StrategicQuotesWonQty AS MONEY) + CAST(@StrategicQuotesLostQty AS MONEY) = 0 THEN 0  -- Return 0 if the total is zero                  
        ELSE (CAST(@StrategicQuotesWonQty AS MONEY) / (CAST(@StrategicQuotesWonQty AS MONEY) + CAST(@StrategicQuotesLostQty AS MONEY)))*100             
    END,                  
 @StrategicQuotesLostRate =                   
    CASE                   
        WHEN CAST(@StrategicQuotesWonQty AS MONEY) + CAST(@StrategicQuotesLostQty AS MONEY) = 0 THEN 0  -- Return 0 if the total is zero                  
        ELSE (CAST(@StrategicQuotesLostQty AS MONEY) / (CAST(@StrategicQuotesWonQty AS MONEY) + CAST(@StrategicQuotesLostQty AS MONEY)))*100                
    END,    
 @StrategicQuotesCancelledRate =                   
    CASE                   
        WHEN CAST(@StrategicQuotesWonQty AS MONEY) + CAST(@StrategicQuotesLostQty AS MONEY) + CAST(@StrategicQuotesCancelledQty AS MONEY) = 0 THEN 0  -- Return 0 if the total is zero                  
        ELSE (CAST(@StrategicQuotesCancelledQty AS MONEY) / (CAST(@StrategicQuotesWonQty AS MONEY) + CAST(@StrategicQuotesLostQty AS MONEY) + CAST(@StrategicQuotesCancelledQty AS MONEY)))*100             
    END   
  
Select     
 @MyPrimaryAccounts As 'MyPrimaryAccounts'  
,@OverduePrimaryAccounts As 'OverduePrimaryAccounts'  
,@MyPrimaryAccountsWithoutContacts As 'MyPrimaryAccountsWithoutContacts'  
,@MyPrimaryAccountsWithoutActivity AS 'MyPrimaryAccountsWithoutActivity'    
,@MyTeamSellAccounts As 'MyTeamSellAccounts'  
,@MyOverserveAccounts As 'MyOverserveAccounts'  
,@MyUnderservedAccounts As 'MyUnderservedAccounts'  
,@MyContacts As 'MyContacts'  
,@MyNewContacts As 'MyNewContacts'  
,@MyKeyContacts As 'MyKeyContacts'  
,@MyTasksDue As 'MyTasksDueSoon'  
,@MyTasksOverdue As 'MyTasksOverdue'  
,@MyActivities As 'MyActivities'  
,@MyAcTeamSell As 'MyAcTeamSell'  
,@MySalesThisMonth As 'MySalesThisMonth'  
,@MySalesLastMonth As 'MySalesLastMonth'  
,@MyLeads As'MyLeads'  ,@MyLeadsExptedVal As 'MyLeadsExptedVal'  
,@MyLeadsWeightedVal As 'MyLeadsWeightedVal'  
,@MyTeamSellLeads As 'MyTeamSellLeads'  
,@MyTeamSellLeadsExptedVal As 'MyTeamSellLeadsExptedVal'  
,@MyTeamSellLeadsWeightedVal As 'MyTeamSellLeadsWeightedVal'  
,@MyNewLeads As 'MyNewLeads'  
,@MyNewLeadsExptedVal As 'MyNewLeadsExptedVal'  
,@MyNewLeadsWeightedVal As 'MyNewLeadsWeightedVal'  
,@MyOverdueLeads As 'MyOverdueLeads'  
,@MyOverdueLeadsExptedVal As 'MyOverdueLeadsExptedVal'  
,@MyOverdueLeadsWeightedVal As 'MyOverdueLeadsWeightedVal'  
,@MyOpenOppsqty As 'MyOpenOppsqty'  
,@MyOpenOppsExptedVal As 'MyOpenOppsExptedVal'  
,@MyOpenOppsWeightedVal As 'MyOpenOppsWeightedVal'  
,@MyTeamSellOpps_count As 'MyTeamSellOpps_count'  
,@MyTeamSellOpps_expval As 'MyTeamSellOpps_expval'  
,@MyTeamSellOpps_wghtval As 'MyTeamSellOpps_wghtval'  
,@MyDueNext30DaysOpps_count As 'MyDueNext30DaysOpps_count'  
,@MyDueNext30DaysOpps_expval As 'MyDueNext30DaysOpps_expval'  
,@MyDueNext30DaysOpps_wghtval As 'MyDueNext30DaysOpps_wghtval'  
,@MyOverdueOpps_count As 'MyOverdueOpps_count'  
,@MyOverdueOpps_expval As 'MyOverdueOpps_expval'  
,@MyOverdueOpps_wghtval As 'MyOverdueOpps_wghtval'  
,@MyOpps_won_Qty As 'MyOpps_won_Qty'  
,@MyOpps_won_Tot As 'MyOpps_won_Tot'  
,@MyOpps_won_Rate As 'MyOpps_won_Rate'  
,@MyOpps_Lost_Qty As 'MyOpps_Lost_Qty'  
,@MyOpps_Lost_Tot As 'MyOpps_Lost_Tot'  
,@MyOpps_Lost_Rate As 'MyOpps_Lost_Rate'  
,@MyOpps_Cancelled_Qty As 'MyOpps_Cancelled_Qty'  
,@MyOpps_Cancelled_Tot As 'MyOpps_Cancelled_Tot'  
,@MyOpps_Cancelled_Rate As 'MyOpps_Cancelled_Rate'  
,@OpenRFQsQty As 'OpenRFQsQty'  
,@OpenRFQstotal As 'OpenRFQstotal'  
,@OpenRFQsmargin As 'OpenRFQsmargin'  
,@MyQuotesQty As 'MyQuotesQty'  
,@MyQuotesTotal As 'MyQuotesTotal'  
,cast(@MyQuotesMargin as int) As 'MyQuotesMargin'  
,@OpenStrategicQuotesQty As 'OpenStrategicQuotesQty'  
,@OpenStrategicQuotesTotal As 'OpenStrategicQuotesTotal'  
,@OpenStrategicQuotesMargin As 'OpenStrategicQuotesMargin'  
,@NewStrategicQuotesQty As 'NewStrategicQuotesQty'  
,@NewStrategicQuotesTotal As 'NewStrategicQuotesTotal'  
,@NewStrategicQuotesMargin As 'NewStrategicQuotesMargin'  
,@OverdueStrategicQuotesQty As 'OverdueStrategicQuotesQty'  
,@OverdueStrategicQuotesTotal As 'OverdueStrategicQuotesTotal'  
,@OverdueStrategicQuotesMargin As 'OverdueStrategicQuotesMargin'  
,@StrategicQuotesWonQty As 'StrategicQuotesWonQty'  
,@StrategicQuotesWonTotal As 'StrategicQuotesWonTotal'  
,@StrategicQuotesWonRate As 'StrategicQuotesWonRate'  
,@StrategicQuotesLostQty  As 'StrategicQuotesLostQty'  
,@StrategicQuotesLostTotal As 'StrategicQuotesLostTotal'  
,@StrategicQuotesLostRate As 'StrategicQuotesLostRate'  
,@StrategicQuotesCancelledQty As 'StrategicQuotesCancelledQty'  
,@StrategicQuotesCancelledTotal As 'StrategicQuotesCancelledTotal'  
,@StrategicQuotesCancelledRate As 'StrategicQuotesCancelledRate'  
--  
,@AccountsWithoutContacts 'AccountsWithoutContacts'    
,@AccountsWithoutActivities 'AccountsWithoutActivities'   
,@MyNewAccounts As 'MyNewAccounts'  
,@MyOpportunitiesforAccount As 'MyOpportunitiesforAccount'    
,@MyQuotesforAccount As 'MyQuotesforAccount'    
,@MyActivitiesforAccount As 'MyActivitiesforAccount'   
,@MyOverdueContacts As 'MyOverdueContacts'  
,@OthersAcThisMonth As 'OthersAcThisMonth'                            
,@OthersAcLastMonth As 'OthersAcLastMonth'    
,@MyRecentOrders  As 'MyRecentOrders'   
,@count_of_my_recent_orders_this_month As 'count_of_my_recent_orders_this_month'  
,@total_amount_of_my_recent_order_this_month  As 'total_amount_of_my_recent_order_this_month'  
,@gross_profit_of_my_recent_order_this_month  As 'gross_profit_of_my_recent_order_this_month'  
,@count_of_my_recent_orders_last_month As 'count_of_my_recent_orders_last_month'  
,@total_amount_of_my_recent_order_last_month  As 'total_amount_of_my_recent_order_last_month'  
,@gross_profit_of_my_recent_order_last_month  As 'gross_profit_of_my_recent_order_last_month'  
,@SalesThisMonthQty  As 'SalesThisMonthQty'  
,@SalesThisMonthTotal As 'SalesThisMonthTotal'  
,@SalesThisMonthMargin As 'SalesThisMonthMargin'  
,@SalesLastMonthQty As 'SalesLastMonthQty'  
,@SalesLastMonthTotal As 'SalesLastMonthTotal'  
,@SalesLastMonthMargin As 'SalesLastMonthMargin'  
,@MyStrategicQuotesOpe As 'MyStrategicQuotesOpe'    
,@MyStrategicQuotesNew As 'MyStrategicQuotesNew'    
,@MyStrategicQuotesOverdue As 'MyStrategicQuotesOverdue'    
,@sum_of_my_open_expected_value As 'Sum_of_my_open_expected_value'    
,@count_of_my_open_expected_value_weighted As 'Count_of_my_open_expected_value_weighted'    
,@count_of_my_open_orders As 'Count_of_my_open_orders'  
,@total_amount_of_my_open_order As 'Total_amount_of_my_open_order'  
,@gross_profit_of_my_open_order As 'Gross_profit_of_my_open_order'  
,@MyQuotes As 'MyQuotes'  
,@MyQuotePipeline As 'MyQuotePipeline'  
,@MyTaskstile As 'MyTaskstile'  
    
END    
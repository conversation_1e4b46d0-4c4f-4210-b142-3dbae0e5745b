﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;
using System.Data.SqlClient;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;
        //private clScrMng goScr;
        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";


        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();


           
        }


        public ScriptsCustom()
        {
            Initialize();
        }

        // SGR 25102016 
       

        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }

        public bool AC_FormControlOnChange_BTN_RFQLETTER_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // VS 12/16/2014 Moved user prompts to Custom WOP
            string sPrompts;

            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", null, true, "XX");

            sPrompts = goTR.StrRead(sWOP, "AC_RFQLETTER");

            scriptManager.RunScript("AddTextToField", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "MMO_Letter", sPrompts, "PREPEND");
            doForm.MoveToField("MMO_Letter");

            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO 02012015 TKT#300: Gray Out Merged CheckBox
            doForm.SetControlState("CHK_Merged", 4);

            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_FormOnSave_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO 02012015 TKT#300: On CancelSave Set CN_Merge 
            if (doForm.oVar.GetVar("CancelSave") == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO 02012015 TKT#300: Updated for generic messagebox, Merge Functionality - run at end of   CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CN_Merge")) != "1")
                    {
                        // Don't allow merge of contact to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID"))
                            // doForm.MessageBox("You cannot merge a contact to itself.  Please select a different merge to contact.", clC.SELL_MB_OK, , , , , , "MessageBoxEvent", , , doForm, , "OK", , , , "MergeCNFail")
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CN", "MergeFail");
                        else
                            // doForm.MessageBox("This contact will be merged to the target contact, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") & "'. Blank fields on the target contact will be filled from this contact record and all links will be copied to the target contact. Are you sure you want to merge this contact?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "MergeCN")
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CN", "Merge");
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections,  clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO 02012015 Disable chk_merged
            doForm.SetControlState("CHK_Merged", 4);
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections,clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO 02012015 TKT#300: On CancelSave Set CO_Merge 
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO 02012015 TKT#300: Updated for generic messagebox, Merge Functionality - run at end of   CO_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CO_Merge")) != "1")
                    {
                        // Don't allow merge of company to itself
                        if (doForm.doRS.GetFieldVal("GID_ID") == doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID"))
                            // doForm.MessageBox("You cannot merge a company to itself.  Please select a different merge to company.", clC.SELL_MB_OK, , , , , , "MessageBoxEvent", , , doForm, , "OK", , , , "MergeCOFail")
                            doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CO", "MergeFail");
                        else
                            // doForm.MessageBox("This company will be merged to the target company, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") & "'. Blank fields on the target company will be filled from this company record and all links will be copied to the target company. Are you sure you want to merge this company?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "MergeCO")
                            doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CO", "Merge");
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool FIND_FormControlOnChange_BTN_OPSearch_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            // CS 8/17/09: Do NOT consider already saved filter of the desktop.

            // VS 01192015 Tkt#232 : Added Job # to Find Opp dialog
            // VS 06262015 Tkt#601 : Added Model #, Fluid, Material to Find Opp dialog
            // VS 06262015 Tkt#640 : Added Drawing # to Find Opp dialog
            par_bRunNext = false;

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form oForm = (Form)par_doCallingObject;

            // Find dialog; OP tab Search button
            string sProduct;
            string sCredTo;
            string sCompany;
            string sDescr;
            string sOrigByCn;
            // Custom Added QuoteNo,JobNO
            string sQuoteNo;
            string sJobNo;
            // VS 06262015
            string sModelNo;
            string sFluid1;
            // Dim sFluid2 As String
            string sMaterial;
            // VS *********
            string sDrawingNo;


            string sView;
            int iCondCount = 0;
            string sViewCondition;
            string sNewCondition;
            int iOrigCondCount = 0;
            int i;
            string sFilter = "";

            // Get values from form
            sProduct = Strings.Trim(oForm.GetControlVal("NDB_TXT_PRODUCTNAME"));
            if (sProduct != "")
            {
                sProduct = goTR.ConvertStringForQS(goTR.PrepareForSQL(sProduct), "LNK_FOR_PD%%SYS_NAME", "OP", true);

            }
            sCredTo = Strings.Trim(oForm.GetControlVal("NDB_TXT_CREDTOUSER"));
            if (sCredTo != "")
            {
                sCredTo = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCredTo), "LNK_CREDITEDTO_US%%SYS_NAME", "OP", true);

            }
            sCompany = Strings.Trim(oForm.GetControlVal("NDB_TXT_FORCO"));
            if (sCompany != "")
            {
                sCompany = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCompany), "LNK_FOR_CO%%SYS_NAME", "OP", true);

            }
            sDescr = Strings.Trim(oForm.GetControlVal("NDB_TXT_DESCR"));
            if (sDescr != "")
            {
                sDescr = goTR.ConvertStringForQS(goTR.PrepareForSQL(sDescr), "TXT_DESCRIPTION", "OP", true);

            }
            sOrigByCn = Strings.Trim(oForm.GetControlVal("NDB_TXT_ORIGBYCN"));
            if (sOrigByCn != "")
            {
                sOrigByCn = goTR.ConvertStringForQS(goTR.PrepareForSQL(sOrigByCn), "LNK_ORIGINATEDBY_CN%%SYS_NAME", "OP", true);

            }
            // Custom Added QuoteNo,JobNO
            sQuoteNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_QuoteNo"));
            if (sQuoteNo != "")
            {
                sQuoteNo = goTR.ConvertStringForQS(goTR.PrepareForSQL(sQuoteNo), "TXT_QUOTENO", "OP", true);

            }
            sJobNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_JobNo"));
            if (sJobNo != "")
            {
                sJobNo = goTR.ConvertStringForQS(goTR.PrepareForSQL(sJobNo), "TXT_JOBNO", "OP", true);

            }
            // VS 06262015
            sModelNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_ModelNo"));
            if (sModelNo != "")
            {
                sModelNo = goTR.ConvertStringForQS(goTR.PrepareForSQL(sModelNo), "TXT_MODELNUM", "OP", true);

            }
            sFluid1 = Strings.Trim(oForm.GetControlVal("NDB_TXT_Fluid"));
            if (sFluid1 != "")
            {
                sFluid1 = goTR.ConvertStringForQS(goTR.PrepareForSQL(sFluid1), "TXT_Fluid1", "OP", true);

            }
            // sFluid2 = Trim(oForm.GetControlVal("NDB_TXT_Fluid"))
            // If sFluid2 <> "" Then
            // sFluid2 = goTR.ConvertStringForQS(goTR.PrepareForSQL(sFluid2), "TXT_Fluid2", "OP", True)
            // End If
            sMaterial = oForm.GetControlVal("NDB_MLS_OPMaterial");
            // VS 07232015
            sDrawingNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_DRAWINGNUMBER"));
            if (sDrawingNo != "")
            {
                sDrawingNo = goTR.ConvertStringForQS(goTR.PrepareForSQL(sDrawingNo), "TXT_DRAWINGNUMBER", "OP", true);

            }


            // Use values to filter Opp - Search Results desktop if it exists
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_613458B2-E5B9-48DF-5858-9AF500E6CB08");
            // Edit views in DT

            // View 1:Opp - Search Results
            sView = oDesktop.GetViewMetadata("VIE_3FE5E28A-5A96-4E7F-5858-9AF500E6CB08");
            // iCondCount = goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"))


            // 'If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            // If iCondCount = 1 Then
            // If goTR.StrRead(sView, "C1FIELDNAME") = "<%ALL%>" Then
            // iCondCount = 0 'Will overwrite these values
            // End If
            // End If
            // Original condition count
            iOrigCondCount = iCondCount;

            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (sProduct != "")
                iCondCount = iCondCount + 1;
            if (sCredTo != "")
                iCondCount = iCondCount + 1;
            if (sCompany != "")
                iCondCount = iCondCount + 1;
            if (sDescr != "")
                iCondCount = iCondCount + 1;
            if (sOrigByCn != "")
                iCondCount = iCondCount + 1;
            // Custom Added QuoteNo,JobNO
            if (sQuoteNo != "")
                iCondCount = iCondCount + 1;
            if (sJobNo != "")
                iCondCount = iCondCount + 1;
            // VS 06262015
            if (sModelNo != "")
                iCondCount = iCondCount + 1;
            if (sFluid1 != "")
                iCondCount = iCondCount + 2;
            // If sFluid2 <> "" Then iCondCount = iCondCount + 1
            if (sMaterial != "0")
                iCondCount = iCondCount + 1;
            // VS 07232015
            if (sDrawingNo != "")
                iCondCount = iCondCount + 1;


            // Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (sProduct != "")
            {
                // Add 'For Product' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_FOR_PD%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sProduct);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND LNK_FOR_PD%%SYS_NAME[" + sProduct + "";
                else
                    sFilter = "LNK_FOR_PD%%SYS_NAME[" + sProduct + "";
            }
            if (sCredTo != "")
            {
                // Add 'Orig by User' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_CREDITEDTO_US%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCredTo);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND LNK_CREDITEDTO_US%%SYS_NAME[" + sCredTo + "";
                else
                    sFilter = "LNK_CREDITEDTO_US%%SYS_NAME[" + sCredTo + "";
            }
            if (sCompany != "")
            {
                // Add 'For Company' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_FOR_CO%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCompany);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND LNK_FOR_CO%%SYS_NAME[" + sCompany + "";
                else
                    sFilter = "LNK_FOR_CO%%SYS_NAME[" + sCompany + "";
            }
            if (sDescr != "")
            {
                // Add 'Description' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_DESCRIPTION%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sDescr);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_DESCRIPTION[" + sDescr + "";
                else
                    sFilter = "TXT_DESCRIPTION[" + sDescr + "";
            }
            if (sOrigByCn != "")
            {
                // Add 'Orig By Contact' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_ORIGINATEDBY_CN%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sOrigByCn);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND LNK_ORIGINATEDBY_CN%%SYS_NAME[" + sOrigByCn + "";
                else
                    sFilter = "LNK_ORIGINATEDBY_CN%%SYS_NAME[" + sOrigByCn + "";
            }
            // Custom Added QuoteNo,JobNO
            if (sQuoteNo != "")
            {
                // Add 'Quote No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_QUOTENO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sQuoteNo);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_QUOTENO[" + sQuoteNo + "";
                else
                    sFilter = "TXT_QUOTENO[" + sQuoteNo + "";
            }
            if (sJobNo != "")
            {
                // Add 'Job No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_JOBNO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sJobNo);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_JOBNO[" + sJobNo + "";
                else
                    sFilter = "TXT_JOBNO[" + sJobNo + "";
            }
            // VS 06262015
            if (sModelNo != "")
            {
                // Add 'Model Num' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_MODELNUM%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sModelNo);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_MODELNUM[" + sModelNo + "";
                else
                    sFilter = "TXT_MODELNUM[" + sModelNo + "";
            }
            if (sFluid1 != "")
            {
                // Add 'Fluid1' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_FLUID1%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFluid1);
                goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                i = i + 1;
                // Add 'Fluid2' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_FLUID2%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFluid1);
                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND (TXT_FLUID1[" + sFluid1 + " OR TXT_FLUID2[" + sFluid1 + ")";
                else
                    sFilter = "(TXT_FLUID1[" + sFluid1 + " OR TXT_FLUID2[" + sFluid1 + ")";
            }
            // If sFluid2 <> "" Then
            // 'Add 'Fluid2' condition
            // goTR.StrWrite(sView, "C" & i & "FIELDNAME", "<%TXT_FLUID2%>")
            // goTR.StrWrite(sView, "C" & i & "CONDITION", "[") 'contains
            // goTR.StrWrite(sView, "C" & i & "VALUE1", sFluid2)
            // i = i + 1
            // If sFilter <> "" Then
            // sFilter = sFilter & " AND TXT_FLUID2[" & sFluid2 & ""
            // Else
            // sFilter = "TXT_FLUID2[" & sFluid2 & ""
            // End If
            // End If
            if (sMaterial != "0")
            {
                // Add 'Material' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MLS_MATERIAL%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "="); // Equals
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", goTR.StringToNum(sMaterial,"",ref par_iValid));
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND MLS_MATERIAL=" + goTR.StringToNum(sMaterial,"",ref par_iValid) + "";

                }
                else
                {
                    sFilter = "MLS_MATERIAL=" + goTR.StringToNum(sMaterial,"",ref par_iValid) + "";

                }
            }
            // VS 07232015
            if (sDrawingNo != "")
            {
                // Add 'Drawing Num' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_DRAWINGNUMBER%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sDrawingNo);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_DRAWINGNUMBER[" + sDrawingNo + "";
                else
                    sFilter = "TXT_DRAWINGNUMBER[" + sDrawingNo + "";
            }

            // Edit CONDITION= line in view MD
            // sViewCondition = goTR.StrRead(sView, "CONDITION")
            // If sViewCondition = "" Then
            sNewCondition = sFilter; // No filter in view already
                                     // Else
                                     // sNewCondition = sViewCondition & " AND " & sFilter
                                     // End If
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_3FE5E28A-5A96-4E7F-5858-9AF500E6CB08", sView);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;


            // Que OP Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
            goUI.Queue("", "");


            par_doCallingObject = oForm;
            return true;
        }

        public bool GetDefaultSort(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }

        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO Tic#380
            par_bRunNext = false;

            // VS 02162015 TKT#349 : Fill lnk_connected_iu from LNK_FOR_CO%%LNK_RELATED_IU when CRL_OP
            // If doForm.GetMode() = "CREATION" Then
            // doForm.doRS.SetFieldVal("LNK_CONNECTED_IU", doForm.doRS.GetFieldVal("LNK_FOR_CO%%LNK_RELATED_IU"))
            // End If

            // SKO Tic#356 Fill lnk_connected_iu from LNK_FOR_CO%%LNK_RELATED_IU when CRL_OP not from AC
            if (doForm.GetMode() == "CREATION")
            {
                string sID = doForm.GetCreateLinkedSourceRecSUID;
                // If the ID is not blank, find out if coming from an AC
                if (sID != "" & sID != null)
                {
                    if (goTR.GetFileFromSUID(sID) != "AC")
                        doForm.doRS.SetFieldVal("LNK_CONNECTED_IU", doForm.doRS.GetFieldVal("LNK_FOR_CO%%LNK_RELATED_IU"));
                }
                // SKO 03052015 Ticket385: Generate Sequential NUMBERS

                // 'DVF 03-05-2015 Removed fill of two fields below per customer.

                // If doForm.doRS.GetFieldVal("TXT_QUOTENO", 1) = "" Then
                // doForm.doRS.SetFieldVal("TXT_QUOTENO", OP_GenerateSequentialQUOTENO())
                // End If
                // If doForm.doRS.GetFieldVal("TXT_JOBNO", 1) = "" Then
                // doForm.doRS.SetFieldVal("TXT_JOBNO", OP_GenerateSequentialJOBNO())
                // End If

                if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_OPPNO", 1)) == "")
                {
                    doForm.doRS.SetFieldVal("TXT_OPPNO", OP_GenerateSequentialOPPNO());

                }
            }




            string sColor = Convert.ToString(goP.GetVar("sMandatoryFieldColor"));

            // SKO Tic#356 When stage = Job, enforce status won
            // SKO Tic#380 exp close date required when the stage is changed to job
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STAGE", 2)) == 2)
            {
                doForm.doRS.SetFieldVal("MLS_STATUS", 2, 2); // Won
                doForm.SetFieldProperty("DTE_ExpCloseDate", "LABELCOLOR", sColor);
            }
            else
                doForm.SetFieldProperty("DTE_ExpCloseDate", "LABELCOLOR", "#000000");
            // SKO Tic#380 ship date required when stage changes to shipped
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STAGE", 2)) == 3)
            {
                doForm.SetFieldProperty("DTE_SHIPPED", "LABELCOLOR", sColor);

            }
            else
            {
                doForm.SetFieldProperty("DTE_SHIPPED", "LABELCOLOR", "#000000");

            }



            // PORTING FROM COMMENCE IN PROGRESS (ALMOST DONE). Owner: RH. Original owner: PJ.

            // 2004/12/22 10:11:51 MAR Changed MMO_NOTES to MMO_JOURNAL in SetVar.

            // Set journal field locked
            doForm.SetControlState("MMO_Journal", 1);

            // CS uncomment----------- FIELD LABELS -----------
            // doForm.SetFieldProperty("CHK_Q01", "LABELTEXT", "Leverage")
            // doForm.SetFieldProperty("CHK_Q02", "LABELTEXT", "Executive Buy-In")
            // doForm.SetFieldProperty("CHK_Q03", "LABELTEXT", "CEO Contacted")
            // doForm.SetFieldProperty("CHK_Q04", "LABELTEXT", "Front End Buy-In")
            // doForm.SetFieldProperty("CHK_Q05", "LABELTEXT", "Competition ID'd")
            // doForm.SetFieldProperty("CHK_Q06", "LABELTEXT", "Champion Built")
            // doForm.SetFieldProperty("CHK_Q07", "LABELTEXT", "Influences ID'd")
            // doForm.SetFieldProperty("CHK_Q08", "LABELTEXT", "Needs Assessed")
            // doForm.SetFieldProperty("CHK_Q09", "LABELTEXT", "Approved/Funded")
            // doForm.SetFieldProperty("CHK_Q10", "LABELTEXT", "Decision Process")
            // doForm.SetFieldProperty("CHK_Q11", "LABELTEXT", "Timing Estimate")
            // doForm.SetFieldProperty("CHK_Q12", "LABELTEXT", "Key Questions")
            // doForm.SetFieldProperty("CHK_Q13", "LABELTEXT", "Present/Demo")
            // doForm.SetFieldProperty("CHK_Q14", "LABELTEXT", "Quoted")
            // doForm.SetFieldProperty("CHK_Q15", "LABELTEXT", "Marked Status")

            // ----------- VARIABLES -------------
            doForm.oVar.SetVar("lLenJournal", Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")));

            // Set mandatory field color
            doForm.SetFieldProperty("DTE_Time", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("TME_Time", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_CreditedTo_US", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_For_Co", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_for_PD", "LABELCOLOR", sColor);
            // SKO Tic#380
            // doForm.SetFieldProperty("DTE_ExpCloseDate", "LABELCOLOR", sColor)
            doForm.SetFieldProperty("SR__Qty", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("CUR_UnitValue", "LABELCOLOR", sColor);


            // Set button field tooltips
            doForm.SetFieldProperties("BTN_CALCPROBABILITY_1", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Calculate Opportunity value and value index");
            doForm.SetFieldProperties("BTN_LINKCOMPANY", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Link Company of the linked Contact");
            doForm.SetFieldProperties("BTN_INSERTLINE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend a Journal line");
            doForm.SetFieldProperties("BTN_INSERTDATE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend the date and time in the Notes field");
            doForm.SetFieldProperties("BTN_INSERTPRODQS", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend questions for the linked Product in the Notes field");
            doForm.SetFieldProperties("BTN_CALCPROBABILITY", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Calculate probability %");
            doForm.SetFieldProperties("BTN_LINKCOMPANY_1", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Link Companies of the linked Contacts");
            doForm.SetFieldProperties("BTN_INSERTPRESALE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend Presale Questions defined in Workgroup options in the Questionnaire field");
            doForm.SetFieldProperties("CHK_CREATETODO", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Create a linked To Do");


            // ----------- STATES --------------
            scriptManager.RunScript("Opp_ManageControlState", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            // CS 8/4/08
            // Check if this Opp was created as a result of a Create Linked from an AC.
            // copy AC MMO_Journal (Lead only) to OP MMO_Journal when the OP is created from the AC
            if (doForm.GetMode() == "CREATION")
            {
                string sID = doForm.GetCreateLinkedSourceRecSUID;
                // If the ID is not blank, find out if coming from an AC
                if (sID != "" & sID != null)
                {
                    if (goTR.GetFileFromSUID(sID) == "AC")
                    {
                        // Check if this AC is Lead
                        clRowSet doRSAC = new clRowSet("AC", 3, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "GID_ID,MLS_PURPOSE,MMO_JOURNAL");
                        if (doRSAC.GetFirst() == 1)
                        {
                            if (Convert.ToInt32(doRSAC.GetFieldVal("MLS_PURPOSE", 2)) == 8)
                            {
                                doForm.doRS.SetFieldVal("MMO_JOURNAL", doRSAC.GetFieldVal("MMO_JOURNAL"));

                            }
                        }
                        else
                        {
                        }
                    }
                }
            }

            // SGR:12/12/2014 Ticket No# 159'
            // Dim doForm As clForm = par_doCallingObject
            string sColors = "#000000";
            doForm.SetFieldProperty("SR__QTY", "LABELCOLOR", sColors);
            doForm.SetFieldProperty("LNK_FOR_PD", "LABELCOLOR", sColors);
            doForm.SetFieldProperty("CUR_UNITVALUE", "LABELCOLOR", sColors);

            // NC:1561
            if (doForm.GetMode() == "CREATION" & doForm.RowsetType == "CRL_OP")
            {
                string sID = doForm.GetCreateLinkedSourceRecSUID;
                // If the ID is not blank, find out if coming from an AC
                if (sID != "" & sID != null)
                {
                    if (goTR.GetFileFromSUID(sID) == "CN")
                    {
                        clRowSet doRSAC = new clRowSet("CN", clC.SELL_READONLY, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "LNK_RELATED_SO");
                        if (doRSAC.GetFirst() == 1)
                            doForm.doRS.SetFieldVal("LNK_FROM_SO", doRSAC.GetFieldVal("LNK_RELATED_SO"));
                        else
                        {
                        }
                    }
                }
            }
         
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);


            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections ,clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // SGR:12/12/2014 Ticket No# 159'
            Form doForm = (Form)par_doCallingObject;
            string sColor = "#000000";
            doForm.SetFieldProperty("SR__QTY", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_FOR_PD", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("CUR_UNITVALUE", "LABELCOLOR", sColor);

            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnSave_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused. '
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // SGR:12/12/2014 Ticket No# 159'
            par_bRunNext = false;

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            // SGR:12/12/2014 Ticket No# 159'
            Form doForm = (Form)par_doCallingObject;
            string sDateStamp = "";
            string sJournal;


            // *******************ENFORCE*************************

            // REVIEW: 
            // DateTimeDiff returning 0 and null.  RAH: Changed all instances to use Val
            // Review month and year field requirements - used for sorting views in Cmc.
            // Runscript UpdateContactItem - this procedure is not fixed
            // UpdateProject - have not ported this

            // Dim i As Integer
            bool bIAmInvolved;
            string sMeVal;
            // Dim sMsgResponse As String
            long lStatusVal;
            string sCreatedDate;
            string sCloseDate;
            // Dim sDateWon As String
            bool bEnforceVal;
            // Dim sValueFld As Decimal
            // Dim sWork As String
            // Dim iMonthCreatedNum As String
            // Dim lCreateDate As Long
            // Dim sMonthCreated As String
            // Dim lCloseDate As Long
            // Dim iMonthCloseNum As String
            bool bResult;
            string sScriptVar;


            // CS 7/6/07: In MD
            // If doForm.doRS.GetLinkCount("LNK_CREDITEDTO_US") = 0 Then
            // doForm.movetotab(0)
            // doForm.MoveToField("LNK_CREDITEDTO_US")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "LNK_CREDITEDTO_US"), "", "", "", "", "", "", "", "", "LNK_CREDITEDTO_US")
            // Return False
            // End If

            // If doForm.doRS.GetLinkCount("LNK_FOR_CO") = 0 Then
            // doForm.MoveToField("LNK_FOR_CO")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "LNK_FOR_CO"), "", "", "", "", "", "", "", "", "LNK_FOR_CO")
            // Return False
            // End If

            // If doForm.doRS.GetLinkCount("LNK_FOR_PD") = 0 Then
            // doForm.MoveToField("LNK_FOR_PD")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "LNK_FOR_PD"), "", "", "", "", "", "", "", "", "LNK_FOR_PD")
            // Return False
            // End If

            // If doForm.doRS.GetFieldVal("DTE_TIME") = "" Then
            // doForm.MoveToField("DTE_TIME")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "DTE_TIME"), "", "", "", "", "", "", "", "", "DTE_TIME")
            // Return False
            // End If

            // If doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE") = "" Then
            // doForm.MoveToField("DTE_EXPCLOSEDATE")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "DTE_EXPCLOSEDATE"), "", "", "", "", "", "", "", "", "DTE_EXPCLOSEDATE")
            // Return False
            // End If

            // If doForm.doRS.GetFieldVal("SR__QTY") = 0 Then
            // doForm.MoveToField("SR__QTY")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "SR__QTY"), "", "", "", "", "", "", "", "", "SR__QTY")
            // Return False
            // End If

            lStatusVal = Convert.ToInt64(doForm.doRS.GetFieldVal("MLS_STATUS", 2));

            // SKO Tic# 385 On save, fill DTE_EXPCLOSEDATE with DTE_TIME +30 days.
            if (!goTR.IsDate(Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2))) | Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1)) == "")
            {
                string sExpCloseDate;
                sExpCloseDate = Convert.ToString(DateAndTime.DateAdd(DateInterval.Day, 30, Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_TIME", 2))));
                doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", sExpCloseDate, 2);
            }

            // Next Action Date - check only when status is Open or On Hold
            // SKO 09012016 Ticket#1223 :Please make next action required on opp unless the status is closed, cancelled or lost.
            // SKO 09022016 Ticket#1236 :please also add won and deleted to the not require a next action date.(Won 2, Delete 5)
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceNextActionDate"))
            {
                switch (lStatusVal)
                {
                    case 0:
                    case 1:
                    case 6:
                    case 7:
                    case 8:
                    case 9      // Open 0, On Hold 1, Quoted 6, 1st Followup 7, 2nd Followup 8,Pending 9
                   :
                        {
                            if (!goTR.IsDate(Convert.ToString(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1))) | Convert.ToString(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1)) == "")
                            {
                                doForm.MoveToTab(1);
                                doForm.MoveToField("DTE_NEXTACTIONDATE");
                                // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE")
                                goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE");
                                return false;
                            }
                            else
                            {
                                // Prompt user to update Next Action date if the current value is < Today
                                // and if the user is the 'Credited To' or 'Peer' in the Opportunity.
                                sMeVal = goP.GetMe("ID");
                                bIAmInvolved = false;

                                if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US")) == sMeVal)
                                {
                                    bIAmInvolved = true;

                                }
                                if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US")) == sMeVal)
                                {
                                    bIAmInvolved = true;

                                }
                                // goP.TraceLine("bIAmInvolved: '" & bIAmInvolved & "'", "", sProc)
                                // goP.TraceLine("Next Action is " & doForm.GetFieldVal("DTE_NEXTACTIONDATE", 2), "", sProc)
                                // goP.TraceLine("DateSys is " & Format(goTR.NowLocal(), "yyyy-MM-dd hh:mm:ss.fff"), "", sProc)
                                if (Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1)) < Convert.ToDateTime(Strings.Format(goTR.NowLocal(), "yyyy-MM-dd")) & bIAmInvolved == true)
                                {
                                    // goP.TraceLine("NextReview was found to be < than DateSys()", "", sProc)
                                    // lMsgResponse - may return long int. 

                                    // Check if we already asked.
                                    if (Convert.ToString(doForm.oVar.GetVar("Op_FormOnSave_NA_Date")) != "1")
                                    {
                                        // CS 6/13/11: Now set in call to messagebox doForm.oVar.SetVar("Op_FormOnSave_NA_Date", "1")
                                        doForm.MessageBox("Would you like to update the 'Next Action' date with 2 weeks from today?" + Constants.vbCrLf + Constants.vbCrLf + "Currently it is " + doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE") + ".", clC.SELL_MB_YESNO + clC.SELL_MB_DEFBUTTON1, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Yes", "No", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Op_FormOnSave_NADate", "Op_FormOnSave_NA_Date");

                                        return true;
                                    }
                                }
                            }

                            break;
                        }
                }

                switch (lStatusVal)
                {
                    case 6      // Quoted
                   :
                        {
                            doForm.doRS.SetFieldVal("CHK_Q14", 1, 2);
                            break;
                        }
                }
            }

            // *** StatusFunc ***
            // Status is Open
            // Make sure this is not called other than from (after) Enforce

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceAndFillStatusRelatedFields"))
            {
                switch (lStatusVal)
                {
                    case 0:
                    case 6        // Open, Quoted
                   :
                        {
                            // Make sure the Expected Close Date field is not earlier than Date Created
                            // First test whether either of the two fields is empty

                            if (!goTR.IsDate(Convert.ToString(doForm.doRS.GetFieldVal("DTE_TIME", 1))) | (Convert.ToString(doForm.doRS.GetFieldVal("DTE_TIME", 1)) == ""))
                                doForm.doRS.SetFieldVal("DTE_TIME", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                            else
                                sCreatedDate = (Convert.ToString(doForm.doRS.GetFieldVal("DTE_TIME", 2)));

                            if (!goTR.IsDate(Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1))) | (Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1)) == ""))
                                doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                            else
                                sCloseDate = (Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2)));


                            if (Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2)) < Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_TIME", 2)))
                            {
                                sCloseDate = "";
                                // doForm.SetFieldVal("DTE_EXPCLOSEDATE", sCloseDate, 2)  'RAH: I commented this out as it was a little confusing and unneccessary.  
                                // Additionally, knowing what the older date is has some value

                                // doForm.MoveToTab(2)    'RAH:There is no need to move to a tab as the field is on the top of the form.
                                doForm.MoveToField("DTE_EXPCLOSEDATE");
                                goErr.SetWarning(30200, sProc, "", "The Expected Close Date can't be earlier than the Date Created.", "", "", "", "", "", "", "", "", "DTE_EXpCloseDate");
                                return false;
                            }

                            break;
                        }

                    case 2:
                    case 3   // Won, Lost
             :
                        {
                            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_REASONWONLOST", 2)) == 0)
                            {
                                doForm.MoveToTab(2);
                                doForm.MoveToField("MLS_REASONWONLOST");
                                // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "MLS_REASONWONLOST"), "", "", "", "", "", "", "", "", "MLS_REASONWONLOST")
                                goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("MLS_REASONWONLOST"), "", "", "", "", "", "", "", "", "MLS_REASONWONLOST");
                                return false;
                            }

                            // Fill 'Date Won' field if empty
                            if (!goTR.IsDate(Convert.ToString(doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1))) | (Convert.ToString(doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1)) == ""))
                                doForm.doRS.SetFieldVal("DTE_DATECLOSED", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);

                            // Fill Expected Close Date field if empty
                            if (!goTR.IsDate(Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2))) | (Convert.ToString(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1)) == ""))
                                doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                            break;
                        }

                    case 1      // On Hold
             :
                        {
                            break;
                        }

                    case 4:
                    case 5   // Cancelled, Delete
           :
                        {
                            // Fill 'Date Won' field (if empty) with today, the date we lost or cancelled the opportunity
                            if (!goTR.IsDate(Convert.ToString(doForm.doRS.GetFieldVal("DTE_DATECLOSED", 2))) | Convert.ToString(doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1)) == "")
                                doForm.doRS.SetFieldVal("DTE_DATECLOSED", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                            break;
                        }
                }
            }

            // StatusFunc	***This has been added above
            // '	CalcReviewOverdue	***No longer used. Field no longer exists on Opp
            // CalcID				  'Calculates ID number. No longer needed

            // -----	CalcProbability	  'Calculates Value, Probability % and Value Index
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CalcProbability"))
            {
                scriptManager.RunScript("Opp_CalcProbability", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

            }

            // CS: Moving below to RecOnSave
            // '-----	Connect Vendors
            // goScr.RunScript("Opp_ConnectVendors", doForm)

            // '----- Set the Related Territory connection from Related Company's territory
            // doForm.ClearLinkAll("LNK_Related_TE")
            // sWork = doForm.GetFieldVal("LNK_FOR_CO%%LNK_In_TE")
            // doForm.SetFieldVal("LNK_Related_TE", sWork)

            // '----- Set Related Division from Product's Division
            // doForm.ClearLinkAll("LNK_RELATED_DV")
            // sWork = doForm.GetFieldVal("LNK_FOR_PD%%LNK_RELATED_DV")
            // doForm.SetFieldVal("LNK_RELATED_DV", sWork)

            // ----- Fill Year, Month fields
            // Done in RecordOnSave script

            // CS: Already in RecOnSave
            // '-----	ConnectUsers	*** Fills Involves User with Credited to and Peer
            // doForm.SetFieldVal("LNK_INVOLVES_US", doForm.GetFieldVal("LNK_CREDITEDTO_US"))
            // doForm.SetFieldVal("LNK_INVOLVES_US", doForm.GetFieldVal("LNK_PEER_US"))

            // Cs: Already in RecOnSave
            // ----- ConnectContacts
            // doForm.SetFieldVal("LNK_INVOLVES_CN", doForm.GetFieldVal("LNK_ORIGINATEDBY_CN"))

            // -----	ConnectSponsors		*** Sponsor removed as sales cycle check box

            // UpdateContactInterestedInItem	*** Review this procedure - not sure how to handle rowset object
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "UpdateContactInterestedInProduct"))
            {
                if (Convert.ToString(doForm.oVar.GetVar("Opp_UpdateContactItem_Ran")) != "1")
                {
                    scriptManager.RunScript("Opp_UpdateContactItem", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

                }
            }

            // -----	UpdateProject	*** updates project with connections from Opp
            // CS 4/3/09
            // ---Copy Notes to Journal on creation of a lead
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CopyNotesToJournalIfNewOpp"))
            {
                if (doForm.GetMode() == "CREATION")
                {
                    // CS 4/28/09: Make sure Notes field has a value
                    if (Convert.ToString(doForm.doRS.GetFieldVal("MMO_NOTES")) != "")
                    {
                        // CS 4/20/09
                        // Check if there is a value in the Journal field already
                        sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMO_JOURNAL"));
                        if (sJournal != "")
                        {
                            doForm.doRS.SetFieldVal("MMO_JOURNAL", sJournal + " " + doForm.doRS.GetFieldVal("MMO_NOTES"));

                        }
                        else
                        {
                            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", null, "CODE", "USERNOOFFSETLABEL", null); // returns var sDateStamp
                            doForm.doRS.SetFieldVal("MMO_JOURNAL", sJournal + sDateStamp + " " + doForm.doRS.GetFieldVal("MMO_NOTES"));
                        }
                    }
                }
            }

            // -----	CreateActLog
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CreateActivity"))
            {
                // If doForm.GetMode() <> "CREATION" Then		'and doForm.GetFieldVal("MLS_STATUS",2) = 0 Then

                // check if we already ran this script. If so don't run it again.
                if (Convert.ToString(doForm.oVar.GetVar("Opp_CreateActLog_Ran")) != "1")
                {
                    // Only try to create an AC log if have the option set in workgroup options to do so
                    if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEOPP_CREATE_AC")) == "1")
                    {
                        bResult = scriptManager.RunScript("Opp_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

                    }
                }
            }

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "DisplayErrorMessages"))
            {
                // If we had previously run this script which called updating linked CN's next action/contact
                // dates and/or creating an ACT the commit of the Contact  or AC had failed we need to display a message on the AC form to
                // to the user.
                sScriptVar = Convert.ToString(doForm.oVar.GetVar("ScriptMessages"));
                if (sScriptVar != "")
                {
                    if (Strings.Len(sScriptVar) < 500)
                        doForm.MessageBox(sScriptVar, 0, null, null, null, null, null, "MessageBoxEvent", null, null, null, null, "OK", null, null, null, "OP_FormOnSave_ScriptMessages");
                    else
                        doForm.MessageBox(Strings.Left(sScriptVar, 497) + "...", 0, null, null, null, null, null, "MessageBoxEvent", null, null, null, null, "OK", null, null, null, "OP_FormOnSave_ScriptMessages");
                    return true;
                }
            }

            // SKO Tic#356
            // Quote Date is saved automatically when the TXT_QUOTENO is entered and saved
            if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO", 1)) != "" & (Convert.ToString(doForm.doRS.GetFieldVal("DTE_QUOTED", 1)) == ""))
            {
                doForm.doRS.SetFieldVal("DTE_QUOTED", Strings.Format(goTR.NowUTC(), "yyyy-MM-dd"));// goTR.NowLocal()

            }
            if (Convert.ToString(doForm.doRS.GetFieldVal("DTE_QUOTED", 1)) != "" & (Convert.ToString(doForm.doRS.GetFieldVal("DTE_TIME", 1)) != ""))
            {
                long lDays = Convert.ToInt32(DateAndTime.DateDiff(DateInterval.Day, Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_TIME", 2)), Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_QUOTED", 2))));
                if (lDays == 0)
                    lDays = 1;
                if (lDays < 0)
                {
                    goErr.SetWarning(30029, sProc, doForm.GetFieldLabel("DTE_QUOTED") + " cannot be earlier than " + doForm.GetFieldLabel("DTE_TIME"), "", "", "", "", "", "", "", "", "", "");
                    return false;
                }
                doForm.doRS.SetFieldVal("SR__DAYSTOQUOTE", lDays, 2);
            }

            // Job Date is saved automatically when the TXT_JOB# is entered and saved
            if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_JOBNO", 1)) != "" & (Convert.ToString(doForm.doRS.GetFieldVal("DTE_JOBNOENTERED", 1)) == ""))
            {
                doForm.doRS.SetFieldVal("DTE_JOBNOENTERED", Strings.Format(goTR.NowUTC(), "yyyy-MM-dd"));// goTR.NowLocal()

            }
            if (Convert.ToString(doForm.doRS.GetFieldVal("DTE_JOBNOENTERED", 1)) != "" & (Convert.ToString(doForm.doRS.GetFieldVal("DTE_QUOTED", 1)) != ""))
            {
                long lJobDays = Convert.ToInt32(DateAndTime.DateDiff(DateInterval.Day, Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_QUOTED", 2)), Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_JOBNOENTERED", 2))));
                if (lJobDays == 0)
                    lJobDays = 1;
                if (lJobDays < 0)
                {
                    goErr.SetWarning(30029, sProc, doForm.GetFieldLabel("DTE_JOBNOENTERED") + " cannot be earlier than " + doForm.GetFieldLabel("DTE_QUOTED"), "", "", "", "", "", "", "", "", "", "");
                    return false;
                }
                doForm.doRS.SetFieldVal("SR__QUOTEDTOJOB", lJobDays, 2);
            }

            if (Convert.ToString(doForm.doRS.GetFieldVal("DTE_SHIPPED", 1)) != "" & (Convert.ToString(doForm.doRS.GetFieldVal("DTE_JOBNOENTERED", 1)) != ""))
            {
                long lSHIPPEDDays = Convert.ToInt32(DateAndTime.DateDiff(DateInterval.Day, Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_JOBNOENTERED", 2)), Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_SHIPPED", 2))));
                if (lSHIPPEDDays == 0)
                    lSHIPPEDDays = 1;
                if (lSHIPPEDDays < 0)
                {
                    goErr.SetWarning(30029, sProc, doForm.GetFieldLabel("DTE_SHIPPED") + " cannot be earlier than " + doForm.GetFieldLabel("DTE_JOBNOENTERED"), "", "", "", "", "", "", "", "", "", "");
                    return false;
                }
                doForm.doRS.SetFieldVal("SR__JOBTOSHIPPED", lSHIPPEDDays, 2);
            }

            // SKO Tic#379
            if (Convert.ToString(doForm.doRS.GetFieldVal("DTE_APPROVAL", 1)) != "" & (Convert.ToString(doForm.doRS.GetFieldVal("DTE_JOBNOENTERED", 1)) != ""))
            {
                long lAPPROVALDays = Convert.ToInt32(DateAndTime.DateDiff(DateInterval.Day, Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_JOBNOENTERED", 2)), Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_APPROVAL", 2))));
                if (lAPPROVALDays == 0)
                {
                    lAPPROVALDays = 1;

                }
                if (lAPPROVALDays < 0)
                {
                    goErr.SetWarning(30029, sProc, doForm.GetFieldLabel("DTE_APPROVAL") + " cannot be earlier than " + doForm.GetFieldLabel("DTE_JOBNOENTERED"), "", "", "", "", "", "", "", "", "", "");
                    return false;
                }
                doForm.doRS.SetFieldVal("SR__JOBTOAPPROVAL", lAPPROVALDays, 2);
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_RecordOnSave_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            // VS 20162015 TKT#348
            clArray doLink = new clArray();
            bool bACUpdate = false;
            int iNewStatus = Convert.ToInt32(doRS.GetFieldVal("MLS_STATUS", 2));
            DateTime d1;
            string s1 = "";
            DateTime d2;
            string s2 = "";

            // DVF Fill SR__DaysToApproval and SR__DaysToDrawingComplete
            // d1 = doRS.GetFieldVal("DTT_APPROVAL", 2)
            // d2 = doRS.GetFieldVal("DTT_DRAWINGCOMPLETE", 2)
            // If d1 <> "" And d2 <> "" Then doRS.SetFieldVal("SR__DAYSTODRAWINGCOMPLETE ", DateDiff("d", d1, d2))

            // d1 = doRS.GetFieldVal("DTT_APPROVALRECEIVED", 2)
            // d2 = doRS.GetFieldVal("DTT_DRAWINGCOMPLETE", 2)
            // If d1 <> "" And d2 <> "" Then doRS.SetFieldVal("SR__DAYSTAPPROVALRECEIVED", DateDiff("d", d1, d2))

            // VS 11132015 TKT#797 : Fill SR__DAYSTOFABRECEIVED
            d1 = Convert.ToDateTime(doRS.GetFieldVal("DTT_FABRECEIVEDDATE", 2));
            s1 = Convert.ToString(doRS.GetFieldVal("DTT_FABRECEIVEDDATE"));
            d2 = Convert.ToDateTime(doRS.GetFieldVal("DTT_APPROVALRECEIVED", 2));
            s2 = Convert.ToString(doRS.GetFieldVal("DTT_APPROVALRECEIVED"));
            if (s1 != "" & s2 != "")
                doRS.SetFieldVal("SR__DAYSTOFABRECEIVED", Convert.ToInt32(DateAndTime.DateDiff("d", d2, d1)));

            // VS 20162015 TKT#348 : Updated Lead To Opp and Lead To Won Opp
            // Lead To Opp & Lead to Won Opp
            oTable = null;
            doLink = doRS.GetLinkVal("LNK_Related_AC", ref doLink, true, 0, -1, "A_a", ref oTable);
            for (int i = 1; i <= doLink.GetDimension(); i++)
            {
                // Get RS of Leads
                clRowSet doACRS = new clRowSet("AC", 1, "GID_ID='" + doLink.GetItem(i) + "'", null, "MLS_LeadToOpp, MLS_LeadToWonOPP", -1, null, null, null, null, null, true, true, true, true, -1, null, true);
                if (doACRS.GetFirst() == 1)
                {
                    do
                    {
                        if (Convert.ToInt32(doACRS.GetFieldVal("MLS_LeadToOpp", 2)) != 1)
                        {
                            doACRS.SetFieldVal("MLS_LeadToOpp", 1, 2);
                            bACUpdate = true;
                        }

                        if (iNewStatus == 2 & Convert.ToInt32(doACRS.GetFieldVal("MLS_LeadToWonOPP", 2)) != 1)
                        {
                            doACRS.SetFieldVal("MLS_LeadToWonOpp", 1, 2);
                            bACUpdate = true;
                        }

                        if (bACUpdate)
                        {
                            if (doACRS.Commit() == 0)
                            {
                                if (goErr.GetLastError("NUMBER") == "E47250")
                                    // failed due to permissions, write to log
                                    goLog.Log(sProc, "OP update of connected Lead To Opp list failed for Lead '" + doACRS.GetCurrentRecID() + " due to permissions.'",1,false,false);
                                else
                                    // write message to log with error
                                    goLog.Log(sProc, "OP update of connected Lead To Opp list failed for OP '" + doACRS.GetCurrentRecID() + " with error " + goErr.GetLastError("NUMBER") + "'", 1, false, true);
                            }
                        }

                        if (doACRS.GetNext() == 0)
                            break;
                    }
                    while (true)// Yes// Yes
        ;
                }
            }
            // VS 20162015 TKT#348 ---------Update connected Lead To Opp and Lead To won Opp
            par_doCallingObject = doRS;
            return true;
        }


        public bool OP_FormControlOnChange_MLS_STAGE_Pre(ref object par_doCallingObject ,ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sColor = Convert.ToString(goP.GetVar("sMandatoryFieldColor"));

            // SKO Tic#356 When stage = Job, enforce status won
            // SKO Tic#380 exp close date required when the stage is changed to job
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STAGE", 2)) == 2)
            {
                doForm.doRS.SetFieldVal("MLS_STATUS", 2, 2); // Won
                doForm.SetFieldProperty("DTE_ExpCloseDate", "LABELCOLOR", sColor);
            }
            else
                doForm.SetFieldProperty("DTE_ExpCloseDate", "LABELCOLOR", "#000000");
            // SKO Tic#380 ship date required when stage changes to shipped
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STAGE", 2)) == 3)
            {
                doForm.SetFieldProperty("DTE_SHIPPED", "LABELCOLOR", sColor);

            }
            else
            {
                doForm.SetFieldProperty("DTE_SHIPPED", "LABELCOLOR", "#000000");

            }


            par_doCallingObject = doForm;
            return true;
        }


        public bool OP_FormControlOnChange_Chk_q07_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sUSID = goP.GetMe("ID");
            clRowSet doUSRS;

            // SKO Tic# 385  3/05/2015 
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("Chk_q07", 2)) == 1)
            {
                doForm.doRS.SetFieldVal("DTE_APPROVAL",Strings.Format(goTR.NowUTC(), "yyyy-MM-dd"));
                doUSRS = new clRowSet("US", 3, "GID_ID='" + sUSID + "'", null/* Conversion error: Set to default value for this argument */, "TXT_NameLast");
                if (doUSRS.GetFirst() == 1)
                {
                    doForm.doRS.SetFieldVal("TXT_APPROVALDRAWINGCHECKED", doUSRS.GetFieldVal("TXT_NameLast"));

                }
            }
            else
            {
                doForm.doRS.SetFieldVal("DTE_APPROVAL", "");
                doForm.doRS.SetFieldVal("TXT_APPROVALDRAWINGCHECKED", "");
            }

            // SKO Tic# 385 On save, fill DTE_EXPCLOSEDATE with DTE_TIME +30 days.
            // If Not goTR.IsDate(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2)) Or doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1) = "" Then
            // Dim sExpCloseDate As String
            // sExpCloseDate = DateAdd(DateInterval.Day, 30, doForm.doRS.GetFieldVal("DTE_TIME", 2))
            // doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", sExpCloseDate, 2)
            // End If

            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormControlOnChange_chk_drawingcomplete_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sUSID = goP.GetMe("ID");
            clRowSet doUSRS;

            // SKO Tic# 385  3/05/2015 
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("chk_drawingcomplete", 2)) == 1)
            {
                doForm.doRS.SetFieldVal("DTE_DRAWINGCOMPLETE", Strings.Format(goTR.NowUTC(), "yyyy-MM-dd"));
                doUSRS = new clRowSet("US", 3, "GID_ID='" + sUSID + "'", null/* Conversion error: Set to default value for this argument */, "TXT_NameLast");
                if (doUSRS.GetFirst() == 1)
                    doForm.doRS.SetFieldVal("TXT_DRAWINGCOMPLETEUSER", doUSRS.GetFieldVal("TXT_NameLast"));
            }
            else
            {
                doForm.doRS.SetFieldVal("DTE_DRAWINGCOMPLETE", "");
                doForm.doRS.SetFieldVal("TXT_DRAWINGCOMPLETEUSER", "");
            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormControlOnChange_chk_approvalreceived_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sUSID = goP.GetMe("ID");
            clRowSet doUSRS;

            // SKO Tic# 385  3/05/2015 
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("chk_approvalreceived", 2)) == 1)
            {
                doForm.doRS.SetFieldVal("DTE_APPROVALRECEIVED", Strings.Format(goTR.NowUTC(), "yyyy-MM-dd"));
                doUSRS = new clRowSet("US", 3, "GID_ID='" + sUSID + "'", null/* Conversion error: Set to default value for this argument */, "TXT_NameLast");
                if (doUSRS.GetFirst() == 1)
                    doForm.doRS.SetFieldVal("TXT_approvalreceived", doUSRS.GetFieldVal("TXT_NameLast"));
            }
            else
            {
                doForm.doRS.SetFieldVal("DTE_APPROVALRECEIVED", "");
                doForm.doRS.SetFieldVal("TXT_approvalreceived", "");
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormControlOnChange_CHK_FABRECEIVED_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // VS 11132015 TKT#797 : Fill details when CHK_FABRECEIVED is checked.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sUSID = goP.GetMe("ID");
            clRowSet doUSRS;

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_FABRECEIVED", 2)) == 1)
            {
                doForm.doRS.SetFieldVal("DTE_FABRECEIVEDDATE", Strings.Format(goTR.NowUTC(), "yyyy-MM-dd"));
                doUSRS = new clRowSet("US", 3, "GID_ID='" + sUSID + "'", null/* Conversion error: Set to default value for this argument */, "TXT_NameLast");
                if (doUSRS.GetFirst() == 1)
                    doForm.doRS.SetFieldVal("TXT_FABRECEIVEDUSER", doUSRS.GetFieldVal("TXT_NameLast"));
            }
            else
            {
                doForm.doRS.SetFieldVal("DTE_FABRECEIVEDDATE", "");
                doForm.doRS.SetFieldVal("TXT_FABRECEIVEDUSER", "");
            }

            par_doCallingObject = doForm;
            return true;
        }
        public string OP_GenerateSequentialQUOTENO()
        {

            // SKO 03052015 Ticket385: Generate SequentialQUOTENO
            int iServiceNo = Convert.ToInt32(goMeta.LineRead("", "OTH_OPPORTUNITYQUOTENO", "NEXTQUOTENO","",false,""));
            DateTime dtNow = goTR.NowLocal(); // Current local time
            int iMonth = dtNow.Month;
            // Dim Month As String
            string Year = dtNow.ToString("yyyy");

            string iServiceNoText = iServiceNo.ToString() + "-" + Year.ToString();

            goMeta.LineWrite("", "OTH_OPPORTUNITYQUOTENO", "NEXTQUOTENO", iServiceNo + 1,ref par_oConnection,"","");

            return iServiceNoText.ToString();
        }

        public string OP_GenerateSequentialJOBNO()
        {

            // SKO 03052015 Ticket385: Generate SequentialJOBNO
            int iServiceNo = Convert.ToInt32(goMeta.LineRead("", "OTH_OPPORTUNITYJOBNO", "NEXTJOBNO","",false,""));
            DateTime dtNow = goTR.NowLocal(); // Current local time
            int iMonth = dtNow.Month;
            // Dim Month As String
            string Year = dtNow.ToString("yyyy");

            string iServiceNoText = iServiceNo.ToString() + "-" + Year.ToString();

            goMeta.LineWrite("", "OTH_OPPORTUNITYJOBNO", "NEXTJOBNO", iServiceNo + 1, ref par_oConnection, "", "");

            return iServiceNoText.ToString();
        }
        public string OP_GenerateSequentialOPPNO()
        {

            // SKO 03052015 Ticket385: Generate SequentialOPPNO
            int iServiceNo = Convert.ToInt32(Strings.RTrim(Strings.LTrim(goMeta.LineRead("", "OTH_OPPORTUNITYOPPNO", "NEXTOPPNO"))));
            DateTime dtNow = goTR.NowLocal(); // Current local time
            int iMonth = dtNow.Month;
            string Month = "";
            string Year = dtNow.ToString("yyyy");
            string iServiceNoText;

            if (Strings.Len(iServiceNo.ToString()) == 1)
            {
                Month = "0000";

            }
            else if (Strings.Len(iServiceNo.ToString()) == 2)
            {
                Month = "000";

            }
            else if (Strings.Len(iServiceNo.ToString()) == 3)
            {
                Month = "00";

            }
            else if (Strings.Len(iServiceNo.ToString()) == 4)
            {
                Month = "0";

            }

            if (Month != "")
            {
                iServiceNoText = Month.ToString() + "" + iServiceNo.ToString() + "-" + Year.ToString();

            }
            else
            {
                iServiceNoText = iServiceNo.ToString() + "-" + Year.ToString();

            }

            goMeta.LineWrite("", "OTH_OPPORTUNITYOPPNO", "NEXTOPPNO", iServiceNo + 1,ref par_oConnection,"","");

            return iServiceNoText.ToString();
        }

        public bool OP_FormControlOnChange_Chk_1stfollowup_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO Tic# 467  4/09/2015 Upon checking chk_1stfollowup, fill dte_1stfollowup, and move status to 1stfollowup
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("Chk_1stfollowup", 2)) == 1)
            {
                doForm.doRS.SetFieldVal("DTE_1stfollowup", Strings.Format(goTR.NowUTC(), "yyyy-MM-dd"));
                doForm.doRS.SetFieldVal("MLS_STATUS", 7, 2); // 1stfollowup
            }
            par_doCallingObject = doForm;
            return true;
        }
        public DateTime GetNextDate(DayOfWeek day)
        {
            DateTime now = goTR.NowUTC();
            int today = System.Convert.ToInt32(now.DayOfWeek);
            int find = System.Convert.ToInt32(day);

            int delta = find - today;
            if (delta > 0)
            {
                return now.AddDays(delta);

            }
            else
            {
                return now.AddDays(7 + delta);

            }
        }
        public bool OP_FormControlOnChange_Chk_2ndfollowup_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO Tic# 467  4/09/2015 Upon checking 2nd followup, fill dte_2ndfollowup, and move dte_nextaction to “next week”
            // SKO Tic# 691  8/27/2015 Upon checking 2nd followup checkbox, status to change to 2nd follow up
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("Chk_2ndfollowup", 2)) == 1)
            {
                doForm.doRS.SetFieldVal("dte_2ndfollowup", Strings.Format(goTR.NowUTC(), "yyyy-MM-dd"));
                doForm.doRS.SetFieldVal("DTE_NEXTACTIONDATE", Strings.Format(GetNextDate(DayOfWeek.Monday), "yyyy-MM-dd"));
                doForm.doRS.SetFieldVal("MLS_STATUS", 8, 2); // US_8=2nd Followup
            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool TD_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn , ref bool par_bRunNext , ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;



            // NC Ticket 1502 Setting drawingcomplete and date when todo is completed

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_COMPLETED", 2)) == 1)
            {
                clRowSet doRSTD;
                string sops;
                string[] splitops = null; 

                sops = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_OP%%GID_ID"));
                splitops = sops.Split(Constants.vbCrLf.ToCharArray());

                for (int i = 0; i <= splitops.GetUpperBound(0); i++)
                {
                    doRSTD = new clRowSet("OP", 1, "GID_ID='" + splitops[i] + "'", null/* Conversion error: Set to default value for this argument */, "CHK_DRAWINGCOMPLETE,DTE_DRAWINGCOMPLETE");
                    if (doRSTD.GetFirst() == 1)
                    {
                        doRSTD.SetFieldVal("CHK_DRAWINGCOMPLETE", 1);
                        doRSTD.SetFieldVal("DTE_DRAWINGCOMPLETE", doForm.doRS.GetFieldVal("DTE_DATECOMPLETED"));
                        doRSTD.Commit();
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool XW_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", null, false/* Conversion error: Set to default value for this argument */, "XX");

            // VS 12/16/2014 Added for AC RFQ Letter
            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                return false;
            }

            // Read values from MD and set in the form fields
            doForm.SetControlVal("NDB_MMO_RFQLETTER", goTR.StrRead(sWOP, "AC_RFQLETTER"));

            par_doCallingObject = doForm;
            return true;
        }

        public bool XW_FormOnSave_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", null, false, "XX");

            // VS 12/16/2014 Added for AC RFQ Letter
            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                return false;
            }

            // Write values to MD
            goTR.StrWrite(ref sWOP, "AC_RFQLETTER", doForm.GetControlVal("NDB_MMO_RFQLETTER"));

            // VS Write to Product XX
            goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP, null, null, "XX");

            doForm.CloseOnReturn = true;
            doForm.CancelSave();
            par_doCallingObject = doForm;
            return true;
        }
        public bool MessageBoxEvent_Post(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            clArray doLink = new clArray();
            clArray doQuotes = new clArray();
            int i = 0;
            int j = 0;
            // Dim doQTRS As clRowSet

            try
            {
                switch (Strings.UCase(par_s5))
                {
                    case "MERGE":
                        {
                            doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                            switch (Strings.UCase(par_s1))
                            {
                                case "YES":
                                    {
                                        // run merge script, continue save
                                        scriptManager.RunScript("MergeRecord",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections,null,"","","","","");
                                        break;
                                    }

                                case "NO":
                                    {
                                        // Clear merged to co linkbox, continue save
                                        doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                        break;
                                    }

                                case "CANCEL":
                                    {
                                        // Clear merged to co linkbox, cancel save
                                        doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }

                    case "MERGEFAIL":
                        {
                            doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                            switch (Strings.UCase(par_s1))
                            {
                                case "OK":
                                    {
                                        // Clear merged to co linkbox, cancel save
                                        doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                        doForm.oVar.SetVar("CancelSave", "1");
                                        break;
                                    }
                            }

                            break;
                        }
                }
            }

            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool MergeRecord_Pre(ref object par_doCallingObject , ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // SKO TKT#300 Added for merge
            par_bRunNext = false;

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName()) + "'", null, "**", -1, null, null, null, null, null, true, true, true, true, -1, null, true);
                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Microsoft.VisualBasic.Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField).ToString() == "")
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));

                                    }
                                    break;
                                }

                            case "MMO":
                                {
                                    // Append
                                    if (doRSMergeTo.GetFieldVal(sField).ToString() == "")
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));

                                    }
                                    else
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMergeTo.GetFieldVal(sField) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + doRSMerge.GetFieldVal("SYS_Name") + " ==" + Constants.vbCrLf + doRSMerge.GetFieldVal(sField));

                                    }
                                    break;
                                }

                            case "CHK":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);

                                    }
                                    break;
                                }

                            case "MLS":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);

                                    }
                                    break;
                                }
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9).ToString());
                        if (sLinkType[4] == "NN" | sLinkType[1] == "2")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (Convert.ToString(doRSMergeTo.GetFieldVal(aLinks.GetItem(i))) == "")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);
                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;

            par_doCallingObject = doRSMerge;
            return true;
        }
         public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_MoveQuoteLine_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            string sDirection = "";
            string sLtGt = "";
            string sFirstLast = "";
            string sMoveSortDir = "";

            string sRecID = "";
            string sQuoteID = "";
            string sSelLineNO = "";
            string sMoveLineNO = "";
            double dSelLineNO = 0;
            double dMoveLineNO = 0;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot edit Quote Lines.");
                return true;
            }

            if (par_s3.ToUpper() == "UP")
            {
                sDirection = "up";
                sLtGt = "<";
                sFirstLast = "first";
                sMoveSortDir = "desc";
            }
            else if (par_s3.ToUpper() == "DOWN")
            {
                sDirection = "down";
                sLtGt = ">";
                sFirstLast = "last";
                sMoveSortDir = "asc";
            }
            else
            {
                return false;
            }

            sQuoteID = doForm.doRS.GetFieldVal("GID_ID").ToString();
            sRecID = doForm.GetLinkSelection("LNK_Connected_QL");

            if (string.IsNullOrEmpty(sRecID))
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }

            clRowSet doQLSelRS = new clRowSet("QL", clC.SELL_EDIT, "GID_ID=" + sRecID, "", "*,**", 1, par_bBypassValidation: true);
            if (doQLSelRS.GetFirst() == 0)
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }
            sSelLineNO = doQLSelRS.GetFieldVal("SR__LineNo").ToString();
            dSelLineNO = Convert.ToDouble(doQLSelRS.GetFieldVal("SR__LineNo"));

            clRowSet doQLMoveRS = new clRowSet("QL", clC.SELL_EDIT, ("LNK_IN_QT=" + sQuoteID + " AND SR__LineNo" + sLtGt + sSelLineNO), "SR__LineNo " + sMoveSortDir, "*,**", 1, par_bBypassValidation: true);
            if (doQLMoveRS.GetFirst() == 0)
            {
                doForm.MessageBox("This is the " + sFirstLast + " Qoute Line. Please select another Qoute Line to move " + sDirection + ".");
                par_doCallingObject = doForm;
                return false;
            }
            sMoveLineNO = doQLMoveRS.GetFieldVal("SR__LineNo").ToString();
            dMoveLineNO = Convert.ToDouble(doQLMoveRS.GetFieldVal("SR__LineNo"));

            doQLSelRS.SetFieldVal("SR__LineNo", dMoveLineNO, 2);
            doQLMoveRS.SetFieldVal("SR__LineNo", dSelLineNO, 2);

            if (doQLSelRS.Commit() == 0 || doQLMoveRS.Commit() == 0)
            {
                doForm.MessageBox("Could not save the Quote Lines. Please reopen the quote and try again.");
                par_doCallingObject = doForm;
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");
            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote_draft.docx";
                }
               
            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote.docx";
                }
               
            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                        
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);

            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);

            //rsQL.SetFieldVal("TXT_Model", sModelText);      
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }
        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_RELATED_PD,SR__QTY,CUR_UNITPRICE,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("CUR_UNITPRICE", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UNITPRICE|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UNITPRICE|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curValue);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("CUR_UNITPRICE", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);

            //This will generate line no's in mobile.
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }

        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }
       
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;


            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            clRowSet doRS1 = null;


            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS1.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doRS1.SetFieldVal("CUR_VALUE", curValue);
                    doRS1.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doRS1;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                    doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doForm;
            }

            return true;

        }
        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ENDUSER_CO", rsOP.GetFieldVal("LNK_ENGINEERING_CO"));
            doFormQT.doRS.SetFieldVal("LNK_DISTREP_CO", rsOP.GetFieldVal("LNK_DISTRIBUTOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECOMPLETED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    //doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }

            return true;

        }
        public bool OL_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.oVar.SetVar("OppID", doRS.GetFieldVal("LNK_IN_OP"));

            par_doCallingObject = doRS;

            return true;
        }

        public bool OL_RecordAfterDelete_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_s2 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sID = doRS.oVar.GetVar("OppID").ToString();

            if (!string.IsNullOrEmpty(sID))
            {
                clRowSet doOPLines = default(clRowSet);

                //Create a rowset of quote lines linked to the deleted quote.
                doOPLines = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo,Gid_id", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    StringBuilder sbQuery = new StringBuilder();
                    for (int i = 1; i <= doOPLines.Count(); i++)
                    {
                        double iLineno = Convert.ToDouble(doOPLines.GetFieldVal("SR__LineNo"));
                        string sGid_id = Convert.ToString(doOPLines.GetFieldVal("Gid_ID"));
                        string sTemp = "#" + goTR.Pad(i.ToString(), 6, " ", "L", true, "R");
                        sbQuery.AppendLine("Update OL Set SR__LineNo='" + i.ToString() + "',sys_name='" + sTemp + "' where Gid_id='" + sGid_id + "' AND SR__LineNo = '" + iLineno.ToString() + "'");

                        if (doOPLines.GetNext() == 0)
                        {
                            break;
                        }

                    }
                    if (!string.IsNullOrEmpty(sbQuery.ToString()))
                    {
                        bool bretval = goData.RunSQLQuery(sbQuery.ToString());
                    }

                }

                doOPLines = null;

            }

            par_doCallingObject = doRS;

            return true;
        }
    }

}

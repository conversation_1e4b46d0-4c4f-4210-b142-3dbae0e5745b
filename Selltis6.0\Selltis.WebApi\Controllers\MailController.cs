﻿using Selltis.BusinessLogic;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web;
using System.Web.Http;
using Newtonsoft.Json;
using System.IO;
using System.Data.SqlClient;
using System.Web.Http.Cors;
using Selltis.Core;
using System.Collections;
using Selltis.WebApi.Models;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using Newtonsoft.Json.Linq;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;

namespace Selltis.WebApi.Controllers
{
    [Authorize]
    [EnableCors(origins: "*", headers: "*", methods: "*")]

    public class MailController : ApiController
    {
        [HttpPost]
        public HttpResponseMessage LogMail([FromBody] Models.ParamUpdateModel parameters)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(parameters.userName, parameters.hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }

            HttpContext.Current.Session["HostName"] = parameters.hostName;
            clInit _clint = new clInit();
            JArray jsonArray = JArray.Parse(parameters.par_sDataSet);

            string blobFolder = jsonArray.FirstOrDefault()?["blobFolder"]?.ToString();

            DataTable par_oDataSet = JsonConvert.DeserializeObject<DataTable>(parameters.par_sDataSet);

            clEmail clEmail = new clEmail();

            if (string.IsNullOrEmpty(parameters.strCompareField))
            {
                string result = clEmail.LogMessage(par_oDataSet);
                response = Request.CreateResponse(HttpStatusCode.OK, result);
            }
            else
            {
                //DataTable par_attachments = JsonConvert.DeserializeObject<DataTable>(parameters.strCompareField);
                //string result = clEmail.LogMessage_With_Attachments(par_oDataSet, par_attachments, parameters.hostName);

                var blobConn = System.Configuration.ConfigurationManager.AppSettings["BlobConnectionString"];
                var container = System.Configuration.ConfigurationManager.AppSettings["BlobContainerName"];
                var service = new BlobServiceClient(blobConn);
                var client = service.GetBlobContainerClient(container);

                var dtAttach = new DataTable();
                dtAttach.Columns.Add("FileName", typeof(string));
                dtAttach.Columns.Add("FileStream", typeof(string)); // base64



                foreach (BlobItem item in client.GetBlobs(prefix: blobFolder + "/"))
                {
                    var blob = client.GetBlobClient(item.Name);
                    // download content
                    var download = blob.DownloadContent();
                    byte[] bytes = download.Value.Content.ToArray();


                    // add a row
                    var row = dtAttach.NewRow();
                    row["FileName"] = Path.GetFileName(item.Name);
                    row["FileStream"] = Convert.ToBase64String(bytes);
                    dtAttach.Rows.Add(row);
                }

                // call your existing business logic
                string result = clEmail.LogMessage_With_Attachments(
                    par_oDataSet,
                    dtAttach,
                    parameters.hostName);

                response = Request.CreateResponse(HttpStatusCode.OK, result);
            }
            return response;

        }
        [HttpPost]
        public HttpResponseMessage ValidateUser([FromBody] Models.ParamModel parameters)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(parameters.userName, parameters.hostName, parameters.password) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }
            else
                return Request.CreateResponse(HttpStatusCode.OK);
        }

        [HttpPost]
        public HttpResponseMessage NewRS([FromBody] Models.ParamModel parameters)
        {
            HttpResponseMessage response;
            try
            {
                HttpContext.Current.Session.Clear();
                clCache.ClearCache();    

                if (Logon(parameters.userName, parameters.hostName) == false)
                {
                    response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                    return response;
                }

                HttpContext.Current.Session["HostName"] = parameters.hostName;
                clInit _clint = new clInit();
                clLog goLog = (clLog)Util.GetInstance("log");
                //goLog.Log("Login in NewRS() - ", "Success", 1, false, true);

                if (parameters.par_sGenFieldsDefs == "NAV")
                {
                    clRowSet rs = new clRowSet(parameters.sFile, parameters.iType, parameters.sCondition, parameters.sSort, parameters.sFields, parameters.iTop, "", "");
                    rs.ToTable();

                    rs.SetFieldVal("TXT_ExternalID", parameters.sINI, 2);
                    rs.SetFieldVal("TXT_ExternalSource", parameters.par_sGenFieldsDefs, 2);
                    rs.Commit();
                    response = Request.CreateResponse(HttpStatusCode.OK, rs.dtTransTable);
                }
                else if (parameters.par_sGenFieldsDefs == "WOP")
                {
                    response = Get_LogLink_Dialog_Configuration(parameters);
                }
                else if (parameters.par_sGenFieldsDefs == "POP")
                {
                    response = Get_POP_Configuration(parameters);
                }
                else
                {
                    DataTable dtData = new DataTable();
                    try
                    {
                        parameters.iTop = 20000;
                        goLog.Log("Export to excel:NewRS()", parameters.sFile + " Started", 1, false, true);
                        clRowSet rs = new clRowSet(parameters.sFile, parameters.iType, parameters.sCondition, parameters.sSort, parameters.sFields, parameters.iTop, parameters.sINI, parameters.par_sGenFieldsDefs);

                        if (rs.oDataSet?.Tables.Count > 0)
                        {
                            dtData = rs.oDataSet?.Tables[0];
                            goLog.Log("Export to excel:NewRS()", "Completed", 1, false, true);
                            MergeNNColumns(rs, dtData);
                        }

                        TransformDataTable(ref dtData, parameters.sFile);

                        UpdateColumnLabels(ref dtData, parameters.sFile);

                        response = Request.CreateResponse(HttpStatusCode.OK, dtData);
                    }
                    catch (Exception ex)
                    {
                        goLog.Log("Export to excel:NewRS()", "Error: " + ex.ToString(), 1, false, true);
                        response = Request.CreateResponse(HttpStatusCode.InternalServerError, ex.Message);
                    }

                }
            }
            catch (Exception ex)
            {
                response = Request.CreateResponse(HttpStatusCode.InternalServerError, ex.Message);
            }

            return response;

        }

        private static void MergeNNColumns(clRowSet rs, DataTable dtData)
        {
            clLog goLog = (clLog)Util.GetInstance("log");
            try
            {
                goLog.Log("Export to excel:MergeNNColumns()", "Started", 1, false, true);
                //this code is for merge multi link (NN) columns to the 1st data table
                if (rs.oDataSet.Tables.Count > 1)
                {
                    DataTable dt1 = rs.oDataSet.Tables[1];
                    ArrayList LNK_NNColumns_list = new ArrayList();
                    foreach (DataRow _row in dt1.Rows)
                    {
                        LNK_NNColumns_list.Add(_row["LinkName"]);
                        dtData.Columns.Add(_row["LinkName"].ToString(), typeof(string));
                    }

                    foreach (DataRow _row in dtData.Rows)
                    {
                        int iTemp = 2;
                        string sTempval = "";
                        foreach (string _newcol in LNK_NNColumns_list)
                        {
                            foreach (DataRow _row2 in rs.oDataSet.Tables[iTemp].Rows)
                            {
                                if (_row2["BASE%%GID_ID"].ToString() == _row["GID_ID"].ToString())
                                {
                                    if (string.IsNullOrEmpty(sTempval))
                                        sTempval = Convert.ToString(_row2[_newcol]);
                                    else
                                        //sTempval = sTempval + "," + Convert.ToString(_row2[_newcol]);
                                        sTempval = sTempval + Environment.NewLine + Convert.ToString(_row2[_newcol]);

                                }
                            }

                            _row[_newcol] = sTempval;
                            sTempval = "";
                            iTemp = iTemp + 1;
                        }
                    }


                    //var query = from sourceRow in dt.AsEnumerable()
                    //            from targetTableIndex in Enumerable.Range(2, LNK_Columns_list.Count)
                    //            let targetTable = rs.oDataSet.Tables[targetTableIndex]
                    //            from targetRow in targetTable.AsEnumerable()
                    //            where targetRow.Field<Guid>("BASE%%GID_ID") == sourceRow.Field<Guid>("GID_ID")
                    //            from targetColumn in LNK_Columns_list.ToArray()
                    //            group targetRow.Field<string>(targetColumn.ToString() ) by sourceRow into g
                    //            select new
                    //            {
                    //                SourceRow = g.Key,
                    //                ConcatenatedValues = string.Join(",", g)
                    //            };

                    //foreach (var result in query)
                    //{
                    //    DataRow sourceRow = result.SourceRow;
                    //    string concatenatedValues = result.ConcatenatedValues;

                    //    foreach (string targetColumn in LNK_Columns_list)
                    //    {
                    //        sourceRow[targetColumn] = concatenatedValues;
                    //    }
                    //}

                }
                goLog.Log("Export to excel:MergeNNColumns()", "Completed", 1, false, true);
            }
            catch (Exception ex)
            {
                goLog.Log("Export to excel:MergeNNColumns()", "Error: " + ex.ToString(), 1, false, true);
                throw;
            }

        }

        private void UpdateColumnLabels(ref DataTable dt, string sFileName)
        {
            clLog goLog = (clLog)Util.GetInstance("log");
            try
            {
                clData _godata = (clData)Util.GetInstance("data");
                clMetaData goMeta = (clMetaData)Util.GetInstance("meta");
                clTransform goTR = (clTransform)Util.GetInstance("tr");
                int iValid = 0;
                goLog.Log("Export to excel:UpdateColumnLabels()", "Started", 1, false, true);

                //OTH_OL_CONTACTSYNC_FILEDSDEF
                string sOLcontactfieldslabels = "";
                if (sFileName.ToLower() == "cn")
                {
                    sOLcontactfieldslabels = goMeta.PageRead("GLOBAL", "OTH_OL_CONTACTSYNC_FILEDSDEF", "");
                }

                foreach (DataColumn _col in dt.Columns)
                {
                    if (sFileName.ToLower() == "cn" && !string.IsNullOrEmpty(sOLcontactfieldslabels))
                    {
                        //goLog.Log("Export to excel:UpdateColumnLabels()", sOLcontactfieldslabels, 1, false, true);
                        string slabelName = goTR.StrRead(sOLcontactfieldslabels, _col.ColumnName, "");
                        if (string.IsNullOrEmpty(slabelName))
                        {
                            _col.ColumnName = _godata.GetFieldFullLabelFromName(sFileName, _col.ColumnName, ref iValid);
                        }
                        else
                        {
                            _col.ColumnName = slabelName;
                        }
                    }
                    else
                    {
                        _col.ColumnName = _godata.GetFieldFullLabelFromName(sFileName, _col.ColumnName, ref iValid);
                    }

                }
                goLog.Log("Export to excel:UpdateColumnLabels()", "Completed", 1, false, true);
            }
            catch (Exception ex)
            {
                goLog.Log("Export to excel:UpdateColumnLabels()", "Error " + ex.ToString(), 1, false, true);
            }


        }

        private void TransformDataTable(ref DataTable dt, string sFileName)
        {
            clLog goLog = (clLog)Util.GetInstance("log");
            clTransform goTR = (clTransform)Util.GetInstance("tr");

            try
            {
                goLog.Log("Export to excel:TransformDataTable()", "Started", 1, false, true);
                DataTable dt1 = dt.Copy();
                ArrayList _col_remove_list = new ArrayList();
                foreach (DataColumn _col in dt.Columns)
                {
                    string columnName = _col.ColumnName;
                    bool bIsColTransformeRequired = Regex.IsMatch(columnName, @"^(DTD_|DTM_|DTY_|DTQ_|DTE_|DTT_|MLS_|%%MLS_|CUR_|CHK_)");

                    //for LNK fields
                    if (columnName.StartsWith("LNK_") && columnName.Contains("%%MLS_"))
                    {
                        bIsColTransformeRequired = true;
                    }

                    if (bIsColTransformeRequired)
                    {
                        string sActualColName = _col.ColumnName;
                        int iOrdinal = _col.Ordinal;
                        dt1.Columns[_col.ColumnName].ColumnName = _col.ColumnName + "_|tmp";
                        dt1.Columns.Add(sActualColName, typeof(string));
                        dt1.Columns[sActualColName].SetOrdinal(iOrdinal);
                        _col_remove_list.Add(_col.ColumnName + "_|tmp");
                    }
                    else if (_col.ColumnName == "BI__ID")
                    {
                        _col_remove_list.Add(_col.ColumnName);
                    }


                }
                dt = null;

                int co = 0;
                foreach (DataColumn _col in dt1.Columns)
                {
                    co++;
                    if (_col.ColumnName.Contains("MLS_") && _col.ColumnName.EndsWith("_|tmp"))
                    {
                        TransformMLSValues(ref dt1, _col.ColumnName, sFileName);
                    }
                    else if ((_col.ColumnName.StartsWith("DTT_") || _col.ColumnName.StartsWith("DTD_") || _col.ColumnName.StartsWith("DTM_") || _col.ColumnName.StartsWith("DTY_") ||
                        _col.ColumnName.StartsWith("DTQ_") || _col.ColumnName.StartsWith("DTE_")) && _col.ColumnName.EndsWith("_|tmp"))
                    {
                        //IEnumerable<DataRow> rows = dt1.Rows.Cast<DataRow>().Where(r => r[_col.ColumnName].ToString() == "02-01-1753 23:59:59" 
                        //                                                                || r[_col.ColumnName].ToString() == "1/2/1753 11:59:59 PM");

                        //rows.ToList().ForEach(r => r.SetField(_col.ColumnName.Replace("_|tmp", ""), ""));

                        //IEnumerable<DataRow> rows1 = dt1.Rows.Cast<DataRow>().Where(r => r[_col.ColumnName].ToString() != "02-01-1753 23:59:59");

                        //rows1.ToList().ForEach(r => r.SetField(_col.ColumnName.Replace("_|tmp", ""), r[_col.ColumnName]));

                        foreach (DataRow _row in dt1.Rows)
                        {

                            string sval = Convert.ToString(_row[_col.ColumnName]);
                            if (sval == "1/2/1753 11:59:59 PM" || sval == "02-01-1753 23:59:59" || sval.Contains("1753"))
                            {
                                _row[_col.ColumnName.Replace("_|tmp", "")] = "";
                            }
                            else
                            {
                                try
                                {
                                    DateTime utcTime = Convert.ToDateTime(sval);
                                    TimeZoneInfo targetTimeZone = TimeZoneInfo.FindSystemTimeZoneById(goTR.oUserTimeZone.ToString());
                                    DateTime userTime = TimeZoneInfo.ConvertTimeFromUtc(utcTime, targetTimeZone);
                                    _row[_col.ColumnName.Replace("_|tmp", "")] = userTime.ToString();
                                }
                                catch
                                {
                                    _row[_col.ColumnName.Replace("_|tmp", "")] = sval;
                                }

                                //DateTimeZone userTimeZone = DateTimeZoneProviders.Tzdb[goTR.oUserTimeZone.ToString()];
                                //Instant instant = Instant.FromDateTimeUtc(utcDateTime.ToUniversalTime());
                                //_row[_col.ColumnName.Replace("_|tmp", "")] = instant.ToDateTimeUtc();
                            }
                        }

                    }
                    else if (_col.ColumnName.StartsWith("CUR_") && _col.ColumnName.EndsWith("_|tmp"))
                    {


                        IEnumerable<DataRow> rows1 = dt1.Rows.Cast<DataRow>();

                        rows1.ToList().ForEach(r => r.SetField(_col.ColumnName.Replace("_|tmp", ""), String.Format("{0:C}", r[_col.ColumnName])));

                    }
                    else if (_col.ColumnName.StartsWith("CHK_") && _col.ColumnName.EndsWith("_|tmp"))
                    {

                        IEnumerable<DataRow> rows = dt1.Rows.Cast<DataRow>().Where(r => r[_col.ColumnName].ToString() == "1");

                        rows.ToList().ForEach(r => r.SetField(_col.ColumnName.Replace("_|tmp", ""), "Yes"));

                        IEnumerable<DataRow> rows1 = dt1.Rows.Cast<DataRow>().Where(r => r[_col.ColumnName].ToString() == "0");

                        rows1.ToList().ForEach(r => r.SetField(_col.ColumnName.Replace("_|tmp", ""), "No"));

                    }
                    //For_Ticket 4798
                    else if (_col.ColumnName.StartsWith("MMO_"))
                    {
                        IEnumerable<DataRow> rows1 = dt1.Rows.Cast<DataRow>();
                        clUtil goUt = new clUtil();

                        foreach (var a in rows1)
                        {
                            string fieldValue = goUt.StripHTML(a[co - 1].ToString());
                            a[co - 1] = fieldValue;
                        }
                    }
                }


                //dt1.AcceptChanges();
                foreach (string _colname in _col_remove_list)
                {
                    dt1.Columns.Remove(_colname);
                }

                //dt1.AcceptChanges();
                dt = dt1.Copy();
                goLog.Log("Export to excel:TransformDataTable()", "Completed", 1, false, true);
            }
            catch (Exception ex)
            {
                goLog.Log("Export to excel:TransformDataTable()", "Error: " + ex.ToString(), 1, false, true);
            }

        }
        public void TransformMLSValues(ref DataTable dt1, string ColName, string FileName)
        {

            DataView dvMlsdata = new DataView(dt1, "", "", DataViewRowState.CurrentRows);
            DataTable distinctValues = dvMlsdata.ToTable(true, ColName);
            string sMLSName = "";

            if (ColName.Contains("%%MLS_"))
            {
                FileName = ColName.Substring(ColName.IndexOf("%%") - 2, 2);
                sMLSName = ColName.Substring(ColName.IndexOf("%%") + 6).Replace("_|tmp", "");
            }
            else
            {
                sMLSName = ColName.Replace("MLS_", "").Replace("_|tmp", "");
            }

            if (distinctValues != null && distinctValues.Rows.Count > 0)
            {
                foreach (DataRow _row in distinctValues.Rows)
                {
                    string sMLSValue = Convert.ToString(_row[ColName]);
                    int itemp = 0;
                    if (Int32.TryParse(sMLSValue, out itemp))
                    {
                        clList _clList = new clList();

                        string sMLSText = _clList.LReadSeek(FileName + ":" + sMLSName, "KEY", sMLSValue);

                        IEnumerable<DataRow> rows = dt1.Rows.Cast<DataRow>().Where(r => r[ColName].ToString() == sMLSValue);

                        rows.ToList().ForEach(r => r.SetField(ColName.Replace("_|tmp", ""), sMLSText));
                    }
                }
            }

        }

        private bool Logon(string userName, string hostName, string pwd = "")
        {
            string sHostName = (hostName == "localhost" || string.IsNullOrEmpty(hostName)) ? "default" : hostName;
            if (string.IsNullOrEmpty(userName))
                userName = "system";
            //Load Settings
            DataSet ds = new DataSet();
            string myXMLfile = System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString() + sHostName + "/SiteSettings.xml";

            FileStream fsReadXml = new FileStream(myXMLfile, FileMode.Open, FileAccess.Read);
            ds.ReadXml(fsReadXml);
            fsReadXml.Close();
            fsReadXml.Dispose();
            HttpContext.Current.Session[hostName + "_SiteSettings"] = ds.Tables[0];

            //Login Process 
            string connStr = ds.Tables[0].Rows[0]["ConnectionString"].ToString();
            HttpContext.Current.Session[sHostName + "_" + "ConnString"] = connStr;

            //SqlConnection sqlConnection = new SqlConnection(connStr);

            //if (sqlConnection.State == ConnectionState.Closed)
            //    sqlConnection.Open();

            bool retval = false;

            using (SqlConnection sqlConnection = new SqlConnection(connStr))
            {
                try
                {
                    sqlConnection.Open();
                }
                catch (TimeoutException ex)
                {
                    System.Threading.Thread.Sleep(5000);
                    sqlConnection.Open();
                }
                
                string sql = string.Empty;

                string IsSSoEnabled = "false";
                try
                {
                    if (ds.Tables[0].Columns.Contains("UseSSO"))
                    {
                        IsSSoEnabled = ds.Tables[0].Rows[0]["UseSSO"].ToString();
                    }
                    else
                    {
                        IsSSoEnabled = "false";
                    }
                }
                catch
                {
                }

                if (string.IsNullOrEmpty(pwd))
                {
                    if (IsSSoEnabled == "true")
                    {
                        sql = "Select b.* from US a Join XU b on b.GID_UserID = a.GID_ID WHERE a.CHK_ActiveField=1 AND cast(a.EML_Email as nvarchar(250))='" + userName + "'";
                    }
                    else if (IsSSoEnabled == "false")
                    {
                        sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'";
                    }
                    else
                    {
                        sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'";
                    }

                }
                else
                {
                    if (IsSSoEnabled == "true")
                    {
                        sql = "Select b.* from US a Join XU b on b.GID_UserID = a.GID_ID WHERE a.CHK_ActiveField=1 AND cast(a.EML_Email as nvarchar(250))='" + pwd + "'";
                    }
                    else if (IsSSoEnabled == "false")
                    {
                        sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'  and [TXT_Password]='" + pwd + "'";
                    }
                    else
                    {
                        sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "' and [TXT_Password]='" + pwd + "'";
                    }
                }

                SqlCommand comm = new SqlCommand(sql, sqlConnection);
                //comm.Parameters.AddWithValue("@username", userName);
                //comm.Parameters.AddWithValue("@pwd", pwd);

                SqlDataReader reader = comm.ExecuteReader();
                if (reader.HasRows)
                {
                    DataTable dt = new DataTable();
                    dt.Load(reader);
                    if (dt.Rows.Count == 0)
                    {
                        //return false;
                        retval = false;
                    }
                    else
                    {

                        HttpContext.Current.Session["USERID"] = dt.Rows[0]["GID_UserID"].ToString();
                        HttpContext.Current.Session["LOGINID"] = dt.Rows[0]["GID_ID"].ToString();
                        HttpContext.Current.Session[sHostName + "_" + "LOGINNAME"] = dt.Rows[0]["TXT_LogonName"].ToString();
                        //return true;
                        retval = true;
                    }
                }
                else
                {
                    // return false;
                    retval = false;
                }


            }

            return retval;


        }

        private const string Par_sVarName = "UpdateKey";

        [HttpPost]
        public HttpResponseMessage UpsertRecordsByDataset([FromBody] Models.ParamUpdateModel param)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(param.userName, param.hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }
            HttpContext.Current.Session["HostName"] = param.hostName;
            clInit _clint = new clInit();


            clProject goP = HttpContext.Current.Session["goP"] as clProject;
            clTransform goTr = HttpContext.Current.Session["goTr"] as clTransform;

            if (!string.IsNullOrEmpty(param.sInstructions))
            {

                int i = Convert.ToInt32(goTr.StrRead(param.sInstructions, "TranslateIdCount", "0"));
                List<string> list = new List<string>();

                for (int k1 = 1; k1 < i; k1++)
                {
                    list.Add(goTr.StrRead(param.sInstructions, "TranslateField" + k1, " "));
                }
                string[] strFields = list.ToArray();
                goP.SetVar("TranslateFields", strFields);
            }
            goP.SetVar("ExternalSource", param.strExtSource);

            DataSet par_oDataSet = JsonConvert.DeserializeObject<DataSet>(param.par_sDataSet);



            goP.SetVar("bBypassValidation", true);
            goP.SetVar("bNoRecordOnSave", false);


            DataSet dsReturn = new DataSet();


            clError goErr = HttpContext.Current.Session["goErr"] as clError;
            clData goData = HttpContext.Current.Session["goData"] as clData;
            clScrMngRowSet goScr = new clScrMngRowSet(); // As clScrMng = HttpContext.Current.Session("goScr")
            goScr.Initialize();

            clLog goLog = HttpContext.Current.Session["goLog"] as clLog;
            int j;
            int k;
            string sCurrentTableName = "";
            clRowSet oURS = null;
            string sCurrentGID = "";


            DataTable dt2 = new DataTable();
            DataColumn dc2;
            DataRow dr2;
            bool bEditError = false;
            string sLogLine = "";
            string sTransType = "";
            DataTable dtStore;
            bool bAdd = false;
            sCurrentTableName = par_oDataSet.Tables[0].TableName;


            dc2 = new DataColumn("Adds", typeof(string));
            dt2.Columns.Add(dc2);
            dc2 = new DataColumn("Edits", typeof(string));
            dt2.Columns.Add(dc2);
            dc2 = new DataColumn("Errors", typeof(string));
            dt2.Columns.Add(dc2);

            int iAdds = 0;
            int iEdits = 0;
            int iErrors = 0;

            // *****************************************
            object oStore;
            StringBuilder sb = new StringBuilder();
            DataTable dtCount;
            string strInternalId = "";
            // ****************************************
            // New UpdateKey feature 
            bool bCommit = true;
            // ****************************************

            dr2 = dt2.NewRow();
            for (j = 0; j <= par_oDataSet.Tables[0].Rows.Count - 1; j++)
            {
                // ****************************************
                // New UpdateKey feature 
                bCommit = true;
                // ****************************************
                dtCount = goData.GetTNID(param.strExtSource, sCurrentTableName, "", par_oDataSet.Tables[0].Rows[j][param.strCompareField].ToString());
                // -- PJ: Old way - only checking first TN record
                // If dtCount.Rows.Count > 0 Then
                // strInternalId = dtCount.Rows(0).Item(0).ToString
                // End If

                // PJ 8/4/10: New way - checking multiple TNs for valid record in file
                if (dtCount.Rows.Count > 0)
                {
                    foreach (DataRow dr in dtCount.Rows)
                    {
                        strInternalId = dr[0].ToString();
                        sb = new StringBuilder();
                        sb.Append("GID_ID");
                        sb.Append("='");
                        sb.Append(strInternalId);
                        sb.Append("'");
                        oURS = new clRowSet(sCurrentTableName, clC.SELL_EDIT, sb.ToString());
                        if (oURS.Count() == 0)
                            // delete TN record because no matching record
                            // currently no method for this!!
                            strInternalId = "";
                        else
                            // This internal id is valid
                            break;
                    }
                }


                bEditError = false;
                bAdd = false;
                if (strInternalId == "")
                    bAdd = true;
                strInternalId = "";


                if (bAdd == false)
                {

                    // Added for LimitTo system
                    if (goP.GetVar("LimitTo").ToString().ToUpper() == "ADDS")
                        goto MoveOn;
                    // ****************************************
                    // New UpdateKey feature                    
                    if (goP.GetVar(Par_sVarName).ToString() != "")
                        bCommit = false;
                    // ****************************************
                    // Set field values for record being edited                        
                    for (k = 0; k <= par_oDataSet.Tables[0].Columns.Count - 1; k++)
                    {
                        if (par_oDataSet.Tables[0].Columns[k].ColumnName != "EXT_ID")
                        {
                            if ((par_oDataSet.Tables[0].Rows[j][k]) != System.DBNull.Value)
                            {

                                // ****************************************
                                // New UpdateKey feature
                                // Get value of current field
                                oStore = par_oDataSet.Tables[0].Rows[j][k];
                                // If current column name = key column then
                                string sColumnName = par_oDataSet.Tables[0].Columns[k].ColumnName.ToUpper();
                                string sKeyColumn = goP.GetVar("UpdateKey").ToString().ToUpper();
                                string sSellKeyValue = "";
                                if (sColumnName == sKeyColumn)
                                {
                                    // If value of current field = value of key column in rowset
                                    if (oURS != null)
                                        sSellKeyValue = oURS.GetFieldVal(par_oDataSet.Tables[0].Columns[k].ColumnName, clC.SELL_FRIENDLY).ToString();
                                    if (oStore.ToString() == sSellKeyValue)
                                        bCommit = true;
                                }
                                // ****************************************

                                oStore = par_oDataSet.Tables[0].Rows[j][k];
                                if (oStore.ToString() == "null;" | oStore.ToString() == "null")
                                    oStore = "";

                                if (testTranslate(par_oDataSet.Tables[0].Columns[k].ColumnName))
                                {
                                    int colnameLength = par_oDataSet.Tables[0].Columns[k].ColumnName.Length;
                                    dtStore = goData.GetTNID(goP.GetVar("ExternalSource").ToString(), par_oDataSet.Tables[0].Columns[k].ColumnName.Substring(colnameLength - 2, 2), "", oStore.ToString());
                                    if (dtStore.Rows.Count > 0)
                                        oStore = dtStore.Rows[0][0].ToString();
                                    else
                                        oStore = "";
                                }

                                if (oStore.ToString() != "")
                                {
                                    if (oURS.SetFieldVal(par_oDataSet.Tables[0].Columns[k].ColumnName, oStore, clC.SELL_FRIENDLY) == 0)
                                    {
                                        if (par_oDataSet.Tables[0].Columns[k].ColumnName.Substring(0, 3) != "LNK")
                                        {
                                            // log error
                                            sLogLine = "Field: " + par_oDataSet.Tables[0].Columns[k].ColumnName + "  Value: " + par_oDataSet.Tables[0].Rows[j][k];
                                            goLog.Log("ws_RowSet::UpsertRecordsByDataset", sLogLine, 1, false/* Conversion error: Set to default value for this argument */, true);

                                            // dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                                            // dr("STATUS") = "Error"
                                            // dr("DETAILS") = "Error on SetFieldVal: " & goErr.GetLastError("MESSAGE")

                                            bEditError = true;
                                            iErrors = iErrors + 1;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        sTransType = "Edited";
                    }
                }
                else
                {
                    // Added for LimitTo system
                    if (goP.GetVar("LimitTo").ToString().ToUpper() == "EDITS")
                        goto MoveOn;

                    oURS = new clRowSet(sCurrentTableName, clC.SELL_ADD, sb.ToString());
                    // Set field values for record being added      
                    if (par_oDataSet.Tables[0].Rows[j][param.strCompareField].ToString() != "" | par_oDataSet.Tables[0].Rows[j][param.strCompareField] != System.DBNull.Value)
                        oURS.SetFieldVal("TXT_ExternalID", par_oDataSet.Tables[0].Rows[j][param.strCompareField], clC.SELL_FRIENDLY);

                    if (param.strExtSource != "")
                        oURS.SetFieldVal("TXT_ExternalSource", param.strExtSource, clC.SELL_FRIENDLY);

                    for (k = 0; k <= par_oDataSet.Tables[0].Columns.Count - 1; k++)
                    {
                        if (par_oDataSet.Tables[0].Columns[k].ColumnName != "EXT_ID")
                        {
                            if (par_oDataSet.Tables[0].Rows[j][k] != System.DBNull.Value)
                            {
                                oStore = par_oDataSet.Tables[0].Rows[j][k];
                                if (oStore.ToString() == "null;" | oStore.ToString() == "null")
                                    oStore = "";

                                if (testTranslate(par_oDataSet.Tables[0].Columns[k].ColumnName))
                                {
                                    int colnameLength = par_oDataSet.Tables[0].Columns[k].ColumnName.Length;
                                    dtStore = goData.GetTNID(goP.GetVar("ExternalSource").ToString(), par_oDataSet.Tables[0].Columns[k].ColumnName.Substring(colnameLength - 2, 2), "", oStore.ToString());
                                    if (dtStore.Rows.Count > 0)
                                        oStore = dtStore.Rows[0][0].ToString();
                                    else
                                        oStore = "";
                                }

                                if (oStore.ToString() != "")
                                {
                                    if (oURS.SetFieldVal(par_oDataSet.Tables[0].Columns[k].ColumnName, oStore, clC.SELL_FRIENDLY) == 0)
                                    {
                                        if (par_oDataSet.Tables[0].Columns[k].ColumnName.Substring(0, 3) != "LNK")
                                        {
                                            // log error
                                            sLogLine = "Field: " + par_oDataSet.Tables[0].Columns[k].ColumnName + "  Value: " + par_oDataSet.Tables[0].Rows[j][k];
                                            goLog.Log("ws_RowSet::UpsertRecordsByDataset", sLogLine, 1, false/* Conversion error: Set to default value for this argument */, true);
                                            iErrors = iErrors + 1;
                                            break;
                                        }
                                    }
                                }
                            }
                        }

                        sTransType = "Added";
                    }
                }

                if (oURS.Count() == 1)
                {
                    // Commit record    
                    sCurrentGID = oURS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY).ToString();
                    if (bEditError == false)
                    {
                        // oERS.bBypassValidation = True
                        SetRowsetBypass(oURS);

                        // ****************************************
                        // New UpdateKey feature 
                        if (bCommit == true)
                        {
                            // ****************************************
                            if (oURS.Commit() == 0)
                            {
                                // log error
                                // dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                                // dr("STATUS") = "Error"
                                // dr("DETAILS") = "Error on Commit: " & goErr.GetLastError("MESSAGE")
                                bEditError = true;
                                iErrors = iErrors + 1;
                            }
                            else
                                // log edit
                                // dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                                // dr("STATUS") = sTransType
                                // dr("GID_ID") = sCurrentGID

                                if (sTransType == "Edited")
                                iEdits = iEdits + 1;
                            else if (sTransType == "Added")
                                iAdds = iAdds + 1;
                        }
                    }
                }
                oURS = null/* TODO Change to default(_) if this is not a reference type */;
                MoveOn:
                ;
            }
            dr2["Adds"] = iAdds.ToString();
            dr2["Edits"] = iEdits.ToString();
            dr2["Errors"] = iErrors.ToString();
            dt2.Rows.Add(dr2);


            // dsReturn.Tables.Add(dt)
            dsReturn.Tables.Add(dt2);

            // Catch ex As Exception

            // If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            // goErr.SetError(ex, 45105, "ws_RowSet::UpsertRecordsByDataset")
            // End If

            // End Try
            response = Request.CreateResponse(HttpStatusCode.OK, dsReturn);
            return response;

        }

        private string GetFields(string sFileName)
        {
            string sFields = string.Empty;
            if (sFileName == "QT")
            {
                sFields = "LNK_CREDITEDTO_US%%TXT_FULLNAME,DTT_CreationTime,DTT_MODTIME,DTT_TIME,DTT_EXPCLOSEDATE,MLS_STATUS,TXT_OPPORTUNITYTYPE,CUR_TOTALAMOUNTUSD,LNK_FOR_CO%%SYS_NAME,TXT_DESCRIPTION,TXT_QUOTENO,TXT_LinkedOppNo,CHK_PRIMARYQUOTE,MLS_LOB,GID_ID";
            }
            else if (sFileName == "OP")
            {
                sFields = "TXT_OPPORTUNITYNAME,TXT_OPPNO,MLS_OPPORTUNITYTYPE,MLS_REGION,MLS_LOB,LNK_CREDITEDTO_US%%TXT_FULLNAME,LNK_FOR_CO%%TXT_COMPANYNAME,LNK_ORIGINATEDBY_CN%%TXT_FULLNAME,LNK_ORIGINATEDBY_CN%%TXT_CONTACTCODE,MLS_Stage,MLS_Currency,CUR_AMOUNT,CUR_RENTALAMOUNT,CUR_TOTALAMOUNT,CUR_TOTALAMOUNTUSD,DTT_ExpCloseDate,DTT_CREATIONTIME,DTT_MODTIME,MLS_TYPEOFBID,LNK_LINKED_WL%%TXT_WLNO,GID_ID";
            }


            return sFields;
        }

        private bool testTranslate(string strField)
        {
            string[] strTest;
            clProject goP = HttpContext.Current.Session["goP"] as clProject;
            try
            {
                strTest = (string[])goP.GetVar("TranslateFields");
            }
            catch (Exception ex)
            {
                return false;
            }

            int i = 0;

            for (i = 0; i < strTest.Count(); i++)
            {
                if (strTest[i].ToUpper() == strField.ToUpper())
                    return true;
            }

            return false;
        }

        private void SetRowsetBypass(clRowSet oRS)
        {
            // Dim goP As clProject = HttpContext.Current.Session("goP")
            clProject goP = HttpContext.Current.Session["goP"] as clProject;

            bool oVar1 = System.Convert.ToBoolean(goP.GetVar("bBypassValidation"));
            switch (oVar1)
            {
                case true:
                    {
                        oRS.bBypassValidation = true;
                        break;
                    }

                case false:
                    {
                        oRS.bBypassValidation = false;
                        break;
                    }
            }

            bool oVar2 = System.Convert.ToBoolean(goP.GetVar("bNoRecordOnSave"));
            switch (oVar2)
            {
                case true:
                    {
                        oRS.bNoRecordOnSave = true;
                        break;
                    }

                case false:
                    {
                        oRS.bNoRecordOnSave = false;
                        break;
                    }
            }
        }
        public HttpResponseMessage Get_LogLink_Dialog_Configuration([FromBody] Models.ParamModel parameters)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(parameters.userName, parameters.hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }
            HttpContext.Current.Session["HostName"] = parameters.hostName;
            clInit _clint = new clInit();
            clMetaData goMeta = (clMetaData)Util.GetInstance("meta");
            clTransform goTR = (clTransform)Util.GetInstance("tr");
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "XX");
            int iLinkCount = Convert.ToInt32(goTR.StrRead(sWOP, "ADDIN_LINKRECS_COUNT", 0));
            int iLinkfiltercount = Convert.ToInt32(goTR.StrRead(sWOP, "ADDIN_LINKRECS_Filter_COUNT", 3));
            int iDefTab = Convert.ToInt32(goTR.StrRead(sWOP, "ADDIN_LINKRECS_DEFTAB", 1));
            List<LinkDialogModel> LinkDialogModel = new List<LinkDialogModel>();
            clData _godata = (clData)Util.GetInstance("data");
            for (int i = 1; i <= iLinkCount; i++)
            {
                LinkDialogModel _LinkDialogModel = new LinkDialogModel();
                string sFileName = goTR.StrRead(sWOP, "ADDIN_LINKRECS_FILE" + (i).ToString(), 0);
                string sSearchField = goTR.StrRead(sWOP, "ADDIN_LINKRECS_FILE" + (i).ToString() + "_SearchField", "SYS_NAME");
                string linkField = goTR.StrRead(sWOP, "ADDIN_LINKRECS_FILE" + (i).ToString() + "_LNKFIELD", 0);
                _LinkDialogModel.ordinal = i;
                _LinkDialogModel.tabName = _godata.GetFileLabelFromName(sFileName);
                _LinkDialogModel.tabTableName = sFileName;
                _LinkDialogModel.pageSize = 10;
                _LinkDialogModel.tabWidth = 20;
                _LinkDialogModel.linkField = linkField;
                _LinkDialogModel.linkValue = linkField;
                if (i == iDefTab)
                {
                    _LinkDialogModel.defaultTab = "true";
                }
                else
                {
                    _LinkDialogModel.defaultTab = "false";
                }
                _LinkDialogModel.searchField = sSearchField;
                _LinkDialogModel.searchFieldHintText = sSearchField;
                //column
                string sColumns = goTR.StrRead(sWOP, "ADDIN_LINKRECS_FILE" + (i).ToString() + "_COLUMNS", 0);
                string[] scols = sColumns.Split(',');
                List<columns> ColumnModel = new List<columns>();
                int iValid = 0;
                foreach (string _col in scols)
                {
                    var _column = new columns();
                    _column.name = _col;
                    _column.width = 100;
                    _column.fontStyle = "Normal";
                    _column.fontSize = "13";
                    _column.fontName = "Arial";
                    _column.label = _godata.GetFieldFullLabelFromName(sFileName, _column.name, ref iValid);
                    _column.visible = "true";
                    ColumnModel.Add(_column);
                    _LinkDialogModel.columns = ColumnModel;
                }
                // Filter
                List<Filters> FiltersModel = new List<Filters>();
                for (int j = 1; j <= iLinkfiltercount; j++)
                {
                    Filters _Filter = new Filters();
                    string sFilter = goTR.StrRead(sWOP, "ADDIN_LINKRECS_FILE" + (i).ToString() + "_FILTER" + (j).ToString() + "_CONDITION", 0);
                    string filterName = goTR.StrRead(sWOP, "ADDIN_LINKRECS_FILE" + (i).ToString() + "_FILTER" + (j).ToString() + "_Name", "");
                    if (filterName != null && filterName != "")
                    {
                        string sFilterSort = goTR.StrRead(sWOP, "ADDIN_LINKRECS_FILE" + (i).ToString() + "_FILTER" + (j).ToString() + "_SORT", 0);
                        _Filter.ordinal = j;
                        _Filter.filterName = filterName;
                        _Filter.filtervalue = filterName;
                        _Filter.filterCondition = sFilter;
                        _Filter.filterSort = sFilterSort;
                        if (_Filter.ordinal == 1)
                        { _Filter.defaultFilter = "true"; }
                        else
                        { _Filter.defaultFilter = "false"; }
                        FiltersModel.Add(_Filter);
                        _LinkDialogModel.filters = FiltersModel;
                    }
                }

                LinkDialogModel.Add(_LinkDialogModel);
            }
            return Request.CreateResponse(HttpStatusCode.OK, LinkDialogModel);
        }

        public HttpResponseMessage Get_POP_Configuration([FromBody] Models.ParamModel parameters)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(parameters.userName, parameters.hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }
            HttpContext.Current.Session["HostName"] = parameters.hostName;
            clInit _clint = new clInit();
            clMetaData goMeta = (clMetaData)Util.GetInstance("meta");
            clTransform goTR = (clTransform)Util.GetInstance("tr");
            clData _godata = (clData)Util.GetInstance("data");
            SelltisOutlookLinkSettings _outlooklinkSettings = new SelltisOutlookLinkSettings();

            if (!string.IsNullOrEmpty(parameters.userName))
            {
                // clRowSet doUserRs = new clRowSet("US", clC.SELL_READONLY, "EML_EMAIL='" + parameters.userName + "'", "", "GID_ID", 1);
                string sUserId = Convert.ToString(HttpContext.Current.Session["USERID"]);

                if (!string.IsNullOrEmpty(sUserId))
                {
                    //string sID = Convert.ToString(doUserRs.GetFieldVal("GID_ID"));
                    string gsPOP = Convert.ToString(goMeta.PageRead(sUserId, "POP_PERSONAL_OPTIONS", "", true));
                    string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "XX");

                    string Cal_Conduit = goTR.StrRead(gsPOP, "OUTLOOKCAL_SYNCDIRECTION", "3");
                    string Con_Conduit = goTR.StrRead(gsPOP, "OUTLOOKCONTACT_SYNCDIRECTION", "3");
                    string Task_Conduit = goTR.StrRead(gsPOP, "OUTLOOKTODO_SYNCDIRECTION", "3");
                    string Cal_ConduitOverwrite = goTR.StrRead(gsPOP, "OUTLOOKCAL_SYNCOVERWRITE", "1");
                    string Con_ConduitOverwrite = goTR.StrRead(gsPOP, "OUTLOOKCONTACT_SYNCOVERWRITE", "1");
                    string Task_ConduitOverwrite = goTR.StrRead(gsPOP, "OUTLOOKTODO_SYNCDIRECTION", "1");

                    string Sync_Cal_Name = goTR.StrRead(gsPOP, "OUTLOOKCAL_SYNCCALENDARITEMNAME", "");
                    int Sync_Private_Appointments = Convert.ToInt32(goTR.StrRead(gsPOP, "OUTLOOKCAL_SYNCPRIVATEAPPOINTMENTS", "0"));
                    int Sync_Apps_FromMonth = Convert.ToInt32(goTR.StrRead(gsPOP, "OUTLOOKCAL_SYNCCONDFROMMONTHS", "1"));
                    int Sync_Apps_ToMonth = Convert.ToInt32(goTR.StrRead(gsPOP, "OUTLOOKCAL_SYNCCONDTOMONTHS", "12"));

                    string sTZLocale = goTR.StrRead(gsPOP, "TZLOCALE", "");
                    string sTimezoneLabel = goTR.UTC_GetTimeZoneLabel(sTZLocale);

                    _outlooklinkSettings.UserTimeZoneIANA = sTZLocale;
                    _outlooklinkSettings.UserTimeZone = sTimezoneLabel;
                    _outlooklinkSettings.CalendarSyncEnabled = Cal_Conduit == "3" ? false : true;
                    _outlooklinkSettings.ContactSyncEnabled = Con_Conduit == "3" ? false : true;
                    _outlooklinkSettings.TaskSyncEnabled = Task_Conduit == "3" ? false : true;
                    _outlooklinkSettings.CalendarSyncType = Conduitstring(Cal_Conduit, Cal_ConduitOverwrite);
                    _outlooklinkSettings.ContactSyncType = Conduitstring(Con_Conduit, Con_ConduitOverwrite);
                    _outlooklinkSettings.TaskSyncType = Conduitstring(Task_Conduit, Task_ConduitOverwrite);
                    _outlooklinkSettings.SyncOutlookCalendarName = Sync_Cal_Name;
                    _outlooklinkSettings.SyncPrivateAppointments = Sync_Private_Appointments == 1 ? true : false;
                    _outlooklinkSettings.SyncAppointmentsFromMonth = Sync_Apps_FromMonth;
                    _outlooklinkSettings.SyncAppointmentsToMonth = Sync_Apps_ToMonth;

                    string sFilterAddition = " AND (DTT_MODTIME> '[%LastSyncRunDateTime%]')";

                    int iCalendarAutoSyncEnabled = Convert.ToInt32(goTR.StrRead(gsPOP, "OUTLOOKCAL_AUTOSYNC", "0"));
                    int iCalendarAutoSyncInterval = Convert.ToInt32(goTR.StrRead(gsPOP, "OUTLOOKCAL_AUTOSYNCINTERVAL", "0"));
                    string sCalendarFileName = "AP";
                    string sCalendarFields = "BI__ID,GID_ID,SYS_NAME,CHK_ALARM,CHK_OLADDED,LI__ALARMINTERVAL,DTE_STARTTIME,DTE_ENDTIME,TME_ENDTIME,TME_STARTTIME,MMO_NOTES,TXT_DESCRIPTION,CHK_RECURRENCE,SI__Pattern,SI__No_of_Occurrences,DTT_Recur_StartDate,DTT_Repeat_Enddate,SI__Day_Repeat_Every_Day,SI__AllDay_Event,TXT_OUTLOOKID";
                    string sCalanderFilter = goTR.StrRead(gsPOP, "OUTLOOKCAL_FILTER_CONDITION", "");
                    string sCalendarSort = "DTT_MODTIME";

                    _outlooklinkSettings.CalendarAutoSyncEnabled = iCalendarAutoSyncEnabled == 1 ? true : false;
                    _outlooklinkSettings.CalendarAutoSyncInterval = iCalendarAutoSyncInterval;
                    _outlooklinkSettings.CalendarFileName = sCalendarFileName;
                    _outlooklinkSettings.CalendarFields = sCalendarFields;
                    _outlooklinkSettings.CalanderFilter = sCalanderFilter + sFilterAddition;
                    _outlooklinkSettings.CalendarSort = sCalendarSort;

                    int iContactAutoSyncEnabled = Convert.ToInt32(goTR.StrRead(gsPOP, "OUTLOOKCONTACT_AUTOSYNC", "0"));
                    int iContactAutoSyncInterval = Convert.ToInt32(goTR.StrRead(gsPOP, "OUTLOOKCONTACT_AUTOSYNCINTERVAL", "0"));
                    string sContactFileName = "CN";
                    string sContactFields = goTR.StrRead(sWOP, "ADDIN_CONTACTSYNC_FIELDS", "BI__ID,GID_ID,SYS_NAME,TXT_NAMEFIRST,TXT_NAMELAST,TEL_CELLPHONE,TEL_BUSPHONE,EML_EMAIL,TXT_ADDRMAILING,TXT_MAILINGCITY,TXT_MAILINGSTATE,TXT_MAILINGZIP,TXT_COUNTRYMAILING,TXT_TITLETEXT,MMO_NOTE,TXT_OUTLOOKID");
                    // string sContactFields = "BI__ID,GID_ID,SYS_NAME,TXT_NAMEFIRST,TXT_NAMELAST,TEL_CELLPHONE,TEL_BUSPHONE,EML_EMAIL,TXT_ADDRMAILING,TXT_MAILINGCITY,TXT_MAILINGSTATE,TXT_MAILINGZIP,TXT_COUNTRYMAILING,TXT_TITLETEXT,MMO_NOTE,TXT_OUTLOOKID";
                    string sContactFilter = goTR.StrRead(gsPOP, "OUTLOOKCONTACT_FILTER_CONDITION", "");
                    string sContactSort = "DTT_MODTIME";

                    _outlooklinkSettings.ContactAutoSyncEnabled = iContactAutoSyncEnabled == 1 ? true : false;
                    _outlooklinkSettings.ContactAutoSyncInterval = iContactAutoSyncInterval;
                    _outlooklinkSettings.ContactFileName = sContactFileName;
                    _outlooklinkSettings.ContactFields = sContactFields;
                    _outlooklinkSettings.ContactFilter = sContactFilter + sFilterAddition;
                    _outlooklinkSettings.ContactSort = sContactSort;

                    int iTaskAutoSyncEnabled = Convert.ToInt32(goTR.StrRead(gsPOP, "OUTLOOKTODO_AUTOSYNC", "0"));
                    int iTaskAutoSyncInterval = Convert.ToInt32(goTR.StrRead(gsPOP, "OUTLOOKTODO_AUTOSYNCINTERVAL", "0"));
                    string sTaskFileName = "TD";
                    string sTaskFields = "BI__ID,GID_ID,SYS_NAME,DTE_STARTDATE,DTE_DUETIME,MMO_NOTES,TXT_DESCRIPTION,CHK_COMPLETED,MLS_STATUS,DTE_DATECOMPLETED,SI__PERCCOMPLETE,MLS_PRIORITY,TXT_OUTLOOKID";
                    string sTaskFilter = goTR.StrRead(gsPOP, "OUTLOOKTODO_FILTER_CONDITION", "");
                    string sTaskSort = "BI__ID";

                    _outlooklinkSettings.TaskAutoSyncEnabled = iTaskAutoSyncEnabled == 1 ? true : false;
                    _outlooklinkSettings.TaskAutoSyncInterval = iTaskAutoSyncInterval;
                    _outlooklinkSettings.TaskFileName = sTaskFileName;
                    _outlooklinkSettings.TaskFields = sTaskFields;
                    _outlooklinkSettings.TaskFilter = sTaskFilter + sFilterAddition;
                    _outlooklinkSettings.TaskSort = sTaskSort;

                }
            }

            return Request.CreateResponse(HttpStatusCode.OK, _outlooklinkSettings);
        }

        private string Conduitstring(string direction, string overwriteType)
        {
            if (direction == "1")
                return "Sellti_To_Outlook";
            else if (direction == "2")
                return "Outlook_To_Selltis";
            else if (direction == "3")
                return "Inactive";
            else if (direction == "0")
            {
                if (overwriteType == "0")
                    return "Two_way_sync|Selltis_Overwrites_Outlook";
                else if (overwriteType == "1")
                    return "Two_way_sync|Outlook_overwrites_selltis";
                else
                    return "Two_way_sync";
            }
            else
                return "";
        }

        [HttpPost]
        public HttpResponseMessage SyncAppointmentsFromOutlook([FromBody] Models.ParamOLAppointmentModel parameters)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(parameters.userName, parameters.hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }


            HttpContext.Current.Session["HostName"] = parameters.hostName;
            clInit _clint = new clInit();
            clLog goLog = (clLog)Util.GetInstance("log");

            string sUserGID = Convert.ToString(HttpContext.Current.Session["USERID"]);
            int isuccessCount = 0;
            int iTotal = parameters.OLAppointments.Count;

            foreach (OLAppointment _appointment in parameters.OLAppointments)
            {
                string SelltisId = GetSelltisId(_appointment.OutlookId, "AP");
                clRowSet doAPRS = null;
                bool bproceed = false;

                if (string.IsNullOrEmpty(SelltisId))
                {
                    doAPRS = new clRowSet("AP", clC.SELL_ADD, "", "", "TXT_OutlookID,TXT_DESCRIPTION,MMO_NOTES,TXT_DESCRIPTION,CHK_RECURRENCE,DTT_STARTTIME,DTT_ENDTIME", -1, "", "", "", "", "", true);
                    bproceed = true;
                }
                else
                {
                    doAPRS = new clRowSet("AP", clC.SELL_EDIT, "GID_ID='" + SelltisId + "'", "", "TXT_OutlookID,TXT_DESCRIPTION,MMO_NOTES,TXT_DESCRIPTION,CHK_RECURRENCE,DTT_STARTTIME,DTT_ENDTIME", -1, "", "", "", "", "", true);

                    if (doAPRS.GetFirst() == 1)
                    {
                        bproceed = true;
                    }
                }

                if (bproceed)
                {
                    doAPRS.SetFieldVal("TXT_DESCRIPTION", _appointment.Description);
                    doAPRS.SetFieldVal("MMO_NOTES", _appointment.Notes);
                    doAPRS.SetFieldVal("DTT_STARTTIME", _appointment.StartTime, clC.SELL_SYSTEM);
                    doAPRS.SetFieldVal("DTT_ENDTIME", _appointment.EndTime, clC.SELL_SYSTEM);
                    doAPRS.SetFieldVal("CHK_RECURRENCE", _appointment.RecurrenceAppointment, 2);
                    doAPRS.SetFieldVal("LNK_COORDINATEDBY_US", sUserGID);
                    doAPRS.SetFieldVal("LNK_INVOLVES_US", sUserGID);
                    doAPRS.SetFieldVal("TXT_OutlookID", _appointment.OutlookId);

                    if (doAPRS.Commit() == 1)
                    {
                        isuccessCount++;
                    }
                    else
                    {
                        goLog.Log("Sync OL Appointment", "Error in sync - " + _appointment.Description, 1, false, true);
                    }
                }
                doAPRS = null;
            }

            response = Request.CreateResponse(HttpStatusCode.OK, isuccessCount.ToString() + "  of " + iTotal.ToString() + " appointments has been synced to selltis.");
            return response;

        }

        private string GetSelltisId(string outlookId, string FileName)
        {
            string sSQL = "Select GID_ID From " + FileName + " where TXT_OutlookID='" + outlookId + "'";
            clData _godata = (clData)Util.GetInstance("data");

            System.Data.SqlClient.SqlConnection sqlConnection1 = _godata.GetConnection();
            System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();

            cmd.CommandText = sSQL;
            cmd.CommandType = System.Data.CommandType.Text;
            cmd.Connection = sqlConnection1;

            string sGidId = Convert.ToString(cmd.ExecuteScalar());

            cmd = null;
            sqlConnection1.Close();
            sqlConnection1 = null;

            return sGidId;
        }

        [HttpPost]
        public HttpResponseMessage SyncContactsFromOutlook([FromBody] Models.ParamOLContactModel parameters)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(parameters.userName, parameters.hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }


            HttpContext.Current.Session["HostName"] = parameters.hostName;
            clInit _clint = new clInit();
            clLog goLog = (clLog)Util.GetInstance("log");
            clData godata = (clData)Util.GetInstance("data");
            clMetaData goMeta = (clMetaData)Util.GetInstance("meta");
            clTransform goTR = (clTransform)Util.GetInstance("tr");

            string sUserGID = Convert.ToString(HttpContext.Current.Session["USERID"]);
            int isuccessCount = 0;
            int iTotal = parameters.OLContacts.Count;

            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "XX");
            string sContactFields = goTR.StrRead(sWOP, "ADDIN_CONTACTSYNC_FIELDS", "TXT_OutlookID,CHK_ACTIVEFIELD,TXT_NAMEFIRST,TXT_NAMELAST,TEL_CELLPHONE,TEL_BUSPHONE,EML_EMAIL,TXT_ADDRMAILING,TXT_MAILINGCITY,TXT_MAILINGSTATE,TXT_MAILINGZIP,TXT_COUNTRYMAILING,TXT_TITLETEXT,MMO_NOTE");

            foreach (OLContact _contact in parameters.OLContacts)
            {

                string SelltisId = GetSelltisId(_contact.OutlookId, "CN");
                bool bproceed = false;
                clRowSet doCNRS = null;

                if (string.IsNullOrEmpty(SelltisId))
                {
                    doCNRS = new clRowSet("CN", clC.SELL_ADD, "", "", sContactFields, -1, "", "", "", "", "", true);
                    bproceed = true;
                }
                else
                {
                    doCNRS = new clRowSet("CN", clC.SELL_EDIT, "GID_ID='" + SelltisId + "'", "", sContactFields, -1, "", "", "", "", "", true);
                    if (doCNRS.GetFirst() == 1)
                    {
                        bproceed = true;
                    }
                }

                if (bproceed)
                {
                    doCNRS.SetFieldVal("TXT_NAMEFIRST", _contact.FirstName);
                    doCNRS.SetFieldVal("TXT_NAMELAST", _contact.LastName);
                    doCNRS.SetFieldVal("TEL_CELLPHONE", _contact.CellPhone);
                    doCNRS.SetFieldVal("TEL_BUSPHONE", _contact.BusinessPhone);
                    doCNRS.SetFieldVal("EML_EMAIL", _contact.Email);

                    if (godata.IsFieldValid("CN", "TXT_ADDRMAILING"))
                    {
                        doCNRS.SetFieldVal("TXT_ADDRMAILING", _contact.Address);
                    }
                    else
                    {
                        doCNRS.SetFieldVal("TXT_ADDRBUSINESS", _contact.Address);
                    }

                    if (godata.IsFieldValid("CN", "TXT_MAILINGCITY"))
                    {
                        doCNRS.SetFieldVal("TXT_MAILINGCITY", _contact.City);
                    }
                    else
                    {
                        doCNRS.SetFieldVal("TXT_CITYBUSINESS", _contact.City);
                    }

                    if (godata.IsFieldValid("CN", "TXT_MAILINGSTATE"))
                    {
                        doCNRS.SetFieldVal("TXT_MAILINGSTATE", _contact.State);
                    }
                    else
                    {
                        doCNRS.SetFieldVal("TXT_STATEBUSINESS", _contact.State);
                    }

                    if (godata.IsFieldValid("CN", "TXT_MAILINGZIP"))
                    {
                        doCNRS.SetFieldVal("TXT_MAILINGZIP", _contact.Zip);
                    }
                    else
                    {
                        doCNRS.SetFieldVal("TXT_ZIPBUSINESS", _contact.Zip);
                    }

                    if (godata.IsFieldValid("CN", "TXT_COUNTRYMAILING"))
                    {
                        doCNRS.SetFieldVal("TXT_COUNTRYMAILING", _contact.Country);
                    }
                    else
                    {
                        doCNRS.SetFieldVal("TXT_COUNTRYBUSINESS", _contact.Country);
                    }

                    doCNRS.SetFieldVal("TXT_TITLETEXT", _contact.JobTitle);
                    doCNRS.SetFieldVal("MMO_NOTE", _contact.Note);
                    doCNRS.SetFieldVal("CHK_ACTIVEFIELD", 1, 2);
                    doCNRS.SetFieldVal("TXT_OutlookID", _contact.OutlookId);

                    if (doCNRS.Commit() == 1)
                    {
                        isuccessCount++;
                    }
                    else
                    {
                        goLog.Log("Sync OL Contact", "Error in sync - " + _contact.FirstName + "," + _contact.LastName, 1, false, true);
                    }
                }
                doCNRS = null;
            }

            response = Request.CreateResponse(HttpStatusCode.OK, isuccessCount.ToString() + "  of " + iTotal.ToString() + " contacts has been synced to selltis.");
            return response;

        }

        [HttpPost]
        public HttpResponseMessage SyncTasksFromOutlook([FromBody] Models.ParamOLTaskModel parameters)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(parameters.userName, parameters.hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }


            HttpContext.Current.Session["HostName"] = parameters.hostName;
            clInit _clint = new clInit();
            clLog goLog = (clLog)Util.GetInstance("log");

            string sUserGID = Convert.ToString(HttpContext.Current.Session["USERID"]);
            int isuccessCount = 0;
            int iTotal = parameters.OLTasks.Count;

            foreach (OLTask _task in parameters.OLTasks)
            {

                string SelltisId = GetSelltisId(_task.OutlookId, "TD");
                bool bproceed = false;
                clRowSet doTDRS = null;

                if (string.IsNullOrEmpty(SelltisId))
                {
                    doTDRS = new clRowSet("TD", clC.SELL_ADD, "", "", "LNK_ASSIGNEDTO_US,TXT_DESCRIPTION,DTT_STARTDATE,DTT_DUETIME,MMO_NOTES,SI__PERCCOMPLETE,MLS_PRIORITY,TXT_OUTLOOKID,CHK_COMPLETED,MLS_STATUS,DTE_DATECOMPLETED", -1, "", "", "", "", "", true);
                    bproceed = true;
                }
                else
                {
                    doTDRS = new clRowSet("TD", clC.SELL_EDIT, "GID_ID='" + SelltisId + "'", "", "LNK_ASSIGNEDTO_US,TXT_DESCRIPTION,DTT_STARTDATE,DTT_DUETIME,MMO_NOTES,SI__PERCCOMPLETE,MLS_PRIORITY,TXT_OUTLOOKID,CHK_COMPLETED,MLS_STATUS,DTE_DATECOMPLETED", -1, "", "", "", "", "", true);
                    if (doTDRS.GetFirst() == 1)
                    {
                        bproceed = true;
                    }
                }

                if (bproceed)
                {
                    doTDRS.SetFieldVal("TXT_DESCRIPTION", _task.Subject);
                    doTDRS.SetFieldVal("DTT_STARTDATE", _task.StartDate);
                    doTDRS.SetFieldVal("DTT_DUETIME", _task.DueDate);
                    doTDRS.SetFieldVal("MMO_NOTES", _task.Notes);
                    doTDRS.SetFieldVal("SI__PERCCOMPLETE", _task.PercentageCompleted);
                    doTDRS.SetFieldVal("MLS_PRIORITY", _task.Priority, 2);
                    doTDRS.SetFieldVal("CHK_COMPLETED", _task.Completed, 2);
                    doTDRS.SetFieldVal("DTE_DATECOMPLETED", _task.CompletedDate);
                    doTDRS.SetFieldVal("LNK_ASSIGNEDTO_US", sUserGID);
                    doTDRS.SetFieldVal("TXT_OutlookID", _task.OutlookId);

                    if (doTDRS.Commit() == 1)
                    {
                        isuccessCount++;
                    }
                    else
                    {
                        goLog.Log("Sync OL Contact", "Error in sync - " + _task.Subject, 1, false, true);
                    }
                }
                doTDRS = null;
            }

            response = Request.CreateResponse(HttpStatusCode.OK, isuccessCount.ToString() + "  of " + iTotal.ToString() + " tasks has been synced to selltis.");
            return response;

        }

        [HttpPost]
        public HttpResponseMessage SyncOutlookIDsToSelltis([FromBody] Models.ParamOLModel parameters)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(parameters.userName, parameters.hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }

            HttpContext.Current.Session["HostName"] = parameters.hostName;
            clInit _clint = new clInit();
            clLog goLog = (clLog)Util.GetInstance("log");

            string sUserGID = Convert.ToString(HttpContext.Current.Session["USERID"]);
            StringBuilder sSQl = new StringBuilder();

            string sFileName = "";

            if (parameters.fileName.ToLower() == "appointment")
            {
                sFileName = "AP";
            }
            else if (parameters.fileName.ToLower() == "contact")
            {
                sFileName = "CN";
            }
            else if (parameters.fileName.ToLower() == "task")
            {
                sFileName = "TD";
            }

            if (!string.IsNullOrEmpty(sFileName))
            {
                foreach (OLItem _item in parameters.OLItems)
                {
                    sSQl.AppendLine("Update " + sFileName + " SET TXT_OUTLOOKID='" + _item.OutlookId + "' WHERE GID_ID='" + _item.SelltisId + "'");
                }

                if (sSQl != null && sSQl.Length > 0)
                {
                    clData _godata = (clData)Util.GetInstance("data");
                    if (_godata.RunSQLQuery(sSQl.ToString()))
                    {
                        response = Request.CreateResponse(HttpStatusCode.OK, "Outlook Ids has been synced to selltis.");
                    }
                    else
                    {
                        response = Request.CreateResponse(HttpStatusCode.InternalServerError, "Error in updating Outlook Ids in Selltis.");
                    }
                }
                else
                {
                    response = Request.CreateResponse(HttpStatusCode.BadRequest, "No data found");
                }
            }
            else
            {
                response = Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid file name");
            }


            return response;

        }
    }
}

﻿using Microsoft.VisualBasic;
using Selltis.BusinessLogic;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Selltis.Core
{
    public class Form
    {
        public string TableName { get; set; }
        public string RecordId { get; set; }
        public string Type { get; set; }
        public string FormMetaData { get; set; }
        public string MetaData_OthSettings { get; set; }
        public string FLDMetaData { get; set; }
        public clRowSet FormRowSet { get; set; }
        public MessageBox FormMessageBox { get; set; }
        //public clVar FormVar { get; set; }
        public string Mode { get; set; }
        public IList<Fields> fielditems { get; set; }//using while saving

        public LinkBox goLinkbox { get; set; }
        //MessageBox
        public string MBMessage { get; set; }
        public bool ShowStatusBar { get; set; }
        public string StatusBarField { get; set; }

        //had to remove
        //public string gsFieldList { get; set; }
        //public string gsFieldInFocus { get; set; }
        //public string gsVisibleLinks { get; set; }
        public List<KeyvaluePair> lsPermControlState { get; set; }
        public List<KeyvaluePair> lsFieldPropertyTooltip { get; set; }
        public List<KeyvaluePair> lsTabPropertyTooltip { get; set; }
        public List<KeyvaluePair> lsControlState { get; set; }
        public List<KeyvaluePair> lsHideFields { get; set; }
        public List<KeyvaluePair> lsFieldPropertyLabelText { get; set; }
        public string LinkboxAction { get; set; }

        public clVar oVar = new clVar();
        
        public string showAsterisk { get; set; }

        #region Private Properties
        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        //private clUI goUI;
        private clHistory goHist;
        private ScriptManager goScr;
        private clRowSet goRowset;
        private clArray gaArray;

        private string gsTable;
        private string gsType;
        private string gsRecordID;
        private string gsMeta;
        private string gsMeta_OthSettings;
        private string gsSection;
        private string gsPage;
        private string gsGUID;
        private string gsTitle;
        private string gsDesktopGUID;
        private string gsViewID;
        private string gsFieldInFocus;
        private string gsOpenURL;
        public string gsURL_Title = "Selltis";
        public string gsURL_Params = "";
        private string gsFLDMeta = "";

        private string gsVisibleLinks = "";
        private string gsMode;
        private string gsTitleForHistory;
        private string gsLinkboxAction;
        private string gsLinkboxGUID;
        private string gsNDBID;
        private string gsRecordIndex;
        private string gsIsCreateLinkedFromGUID;
        private string gsEmail = "";
        private string gsTopRecValue = "";
        private string gsPOP = "";
        private string gsTELEPHONY = "";
        private string gsFORMDISPLAYGRID = "0";
        private string gsFIELDTOOLTIPS = "1";
        private string gsFIELDTOOLTIPSSYSNAME = "0";
        private string gsFIRSTDAYOFWEEK = "0";
        private string gsFieldList = "";

        private string gsDisplayMeta = "";
        private int giRecShareState;
        private int giSelectedTab = 0;
        private int giFirstTab = 1;

        private int giFormType = 0;
        private bool gbNoRecordExists = false;
        private bool gbIsDirty = false;
        private bool gbErrorOnSave = false;
        private bool gRecordExists = true;
        private bool gbBrowseMode = false;
        private bool gbShowFirst = false;
        private bool gbShowLast = false;
        private bool gbCancelSave = false;
        private bool gbCloseOnReturn = false;
        private bool gbRefreshAllLinkNames = false;
        private bool gbReloadForm = false;

        private bool gbMenuDisplay = true;
        private IList<string> gcFormScripts;
        private Collection gcFieldList = new Collection();
        private Collection gcDupFields = new Collection();
        private Collection gcFieldInformation = new Collection();
        private Collection gcControlState = new Collection();
        private Collection gcPermControlState = new Collection();
        private Collection gcNDBFieldVals = new Collection();
        private Collection gcFilterINIs = new Collection();
        private Collection gcLinkSelection = new Collection();
        private Collection gcLinkLocked = new Collection();
        private Collection gcLinkUnlocked = new Collection();
        private Collection gcRefreshLinks = new Collection();
        private Collection gcTabPosToID = new Collection();
        private Collection gcFieldsRequired = new Collection();
        private Collection gcFirstFieldsOnTab = new Collection();
        private Collection gcReloadLinks = new Collection();
        private Collection gcHideFields = new Collection();
        private Collection gcHideTabs = new Collection();
        private Collection gcFieldLabels = new Collection();
        private Collection gcSelectableLinks = new Collection();
        private Collection gcFieldOrder = new Collection();

        private SortedList gcCursorPosition = new SortedList();
        //for storing field properties
        private Collection gcFldPro_LabelText = new Collection();
        private Collection gcFldPro_ToolTip = new Collection();
        private Collection gcFldPro_LabelColor = new Collection();
        private Collection gcFldPro_Image = new Collection();
        private Collection gcFldPro_FontNames = new Collection();

        private Collection gcFldPro_FontSize = new Collection();
        //for storing tab properties
        private Collection gcTabPro_LabelColor = new Collection();
        private Collection gcTabPro_FontNames = new Collection();
        private Collection gcTabPro_FontSize = new Collection();

        private Collection gcTabPro_Tooltip = new Collection();
        //for storing Lnk Table Type field column show / hide status
        private Collection gcFld_Lnk_Table_Col_State = new Collection();
        //messagebox
        private string gsMBFormMode = "NORMAL";
        private string gsMBScriptInitiatedSave = "";
        private bool gbMBDisplay = false;
        private string gsMBMessage = "";
        private int giMBStyle = clC.SELL_MB_OK;
        private string gsMBTitle = "";
        private string gsMBButton1Label = "";
        private string gsMBButton2Label = "";
        private string gsMBButton3Label = "";
        private string gsMBInputDefaultValue = "";
        private string gsMBButton1Script = "";
        private string gsMBButton2Script = "";
        private string gsMBButton3Script = "";
        private string gsMBPar1 = "";
        private string gsMBPar2 = "";
        private string gsMBPar3 = "";
        private string gsMBPar4 = "";
        private string gsMBPar5 = "";
        private bool gbMBOverrideButtonScript = false;
        private string gsMBFlag = "";
        private bool gbMBRichText = false;

        //message panel
        private bool gbMessagePanelDisplay = false;
        private string gsMessagePanelMessage = "";
        private string gsMessagePanelBackgroundColor = "#FFFFD0";
        private string gsMessagePanelTextColor = "#000000";
        private string gsMessagePanelImage = "";
        #endregion

        #region "Public Properties"
        public bool CloseOnReturn
        {
            get { return gbCloseOnReturn; }
            set { gbCloseOnReturn = value; }
        }
        public List<string> lstFieldList
        {
            get;
            set;
        }
        //public string DialPhoneNumber
        //{
        //    get { return goUI.gsDialPhoneNumber; }
        //    set { goUI.DialPhoneNumber(value); }
        //}

        public clRowSet doRS
        {
            get { return goRowset; }
            set { value = goRowset; }
        }

        public string SendEmail
        {
            get { return gsEmail; }
            set { gsEmail = value; }
        }
        public string DialPhoneNumber { get; set; }
        public string FieldInFocus
        {
            get { return gsFieldInFocus; }
            set { gsFieldInFocus = value; }
        }
        public Collection FieldList
        {
            get { return gcFieldList; }
        }

        //public string FRFControlName
        //{
        //    get { return gsFRFControlName; }
        //    set { gsFRFControlName = value; }
        //}

        public string AllFormFields
        {
            get { return gsFieldList; }
        }

        public int FirstTab
        {
            get { return giFirstTab; }
            set { giFirstTab = value; }
        }

        public int FormType
        {
            get { return giFormType; }
        }

        public string GUID
        {
            get { return gsGUID; }
        }

        public string GetCreateLinkedSourceRecSUID
        {
            get { return gsIsCreateLinkedFromGUID; }
        }

        //public string HistoryKey
        //{
        //    get { return gsHistoryKey; }
        //    set { gsHistoryKey = value; }
        //}

        public bool IsDirty
        {
            get { return gbIsDirty; }
            set { gbIsDirty = value; }
        }

        public string MDPage
        {
            get { return gsPage; }
        }

        public string MDSection
        {
            get { return gsSection; }
        }

        public bool MenuDisplay
        {
            get { return gbMenuDisplay; }
            set { gbMenuDisplay = value; }
        }

        public string MenuMeta
        {
            get { return gsDisplayMeta; }
        }

        public clArray MessageBoxArray
        {
            get { return gaArray; }
        }

        public string MessageBoxButton1Label
        {
            get { return gsMBButton1Label; }
        }

        public string MessageBoxButton1Script
        {
            get { return gsMBButton1Script; }
        }

        public string MessageBoxButton2Label
        {
            get { return gsMBButton2Label; }
        }

        public string MessageBoxButton2Script
        {
            get { return gsMBButton2Script; }
        }

        public string MessageBoxButton3Label
        {
            get { return gsMBButton3Label; }
        }

        public string MessageBoxButton3Script
        {
            get { return gsMBButton3Script; }
        }

        public bool MessageBoxDisplay
        {
            get { return gbMBDisplay; }
        }

        public string MessageBoxFormMode
        {
            get { return gsMBFormMode; }
        }

        public string MessageBoxInputDefaultValue
        {
            get { return gsMBInputDefaultValue; }
        }

        public string MessageBoxMessage
        {
            get { return gsMBMessage; }
        }

        public string MessageBoxPar1
        {
            get { return gsMBPar1; }
        }

        public string MessageBoxPar2
        {
            get { return gsMBPar2; }
        }

        public string MessageBoxPar3
        {
            get { return gsMBPar3; }
        }

        public string MessageBoxPar4
        {
            get { return gsMBPar4; }
        }

        public string MessageBoxPar5
        {
            get { return gsMBPar5; }
        }

        public string MessageBoxFlag
        {
            get { return gsMBFlag; }
        }

        public bool MessageBoxRichText
        {
            get { return gbMBRichText; }
        }

        public string MessageBoxScriptInitiatedSave
        {
            get { return gsMBScriptInitiatedSave; }
            set { gsMBScriptInitiatedSave = value; }
        }

        public int MessageBoxStyle
        {
            get { return giMBStyle; }
        }

        public string MessageBoxTitle
        {
            get { return gsMBTitle; }
        }

        public bool MessageBoxOverrideButtonScript
        {
            get { return gbMBOverrideButtonScript; }
            set { gbMBOverrideButtonScript = value; }
        }

        public bool MessagePanelDisplay
        {
            get { return gbMessagePanelDisplay; }
        }

        public string MessagePanelMessage
        {
            get { return gsMessagePanelMessage; }
        }

        public string MessagePanelBackgroundColor
        {
            get { return gsMessagePanelBackgroundColor; }
        }

        public string MessagePanelTextColor
        {
            get { return gsMessagePanelTextColor; }
        }

        public string MessagePanelImage
        {
            get { return gsMessagePanelImage; }
        }

        public string Metadata
        {
            get { return gsMeta; }
        }

        public string NDBID
        {
            get { return gsNDBID; }
        }

        public string OpenURL
        {
            get { return gsOpenURL; }
            set { gsOpenURL = value; }
        }

        public void OpenURLExternal(string par_sURL, string par_sWindowTitle = "Selltis", string par_sParams = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //try
            //{
                gsOpenURL = par_sURL;
                gsURL_Title = par_sWindowTitle;
                gsURL_Params = par_sParams;
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        // goErr.SetError(ex, , sProc);
            //    }
            //}
        }

        public bool RecordDoesNotExist
        {
            get { return gbNoRecordExists; }
        }

        public string RecordID
        {
            get { return gsRecordID; }
        }

        public int RecordShareState
        {
            get
            {
                if ((goRowset == null))
                    return -1;
                return Convert.ToInt32(goRowset.GetFieldVal("SI__SHARESTATE", 2).ToString());
            }
        }

        public bool RefreshAllLinkNames
        {
            get { return gbRefreshAllLinkNames; }
            set { gbRefreshAllLinkNames = value; }
        }

        public bool ReloadForm
        {
            get { return gbReloadForm; }
            set { gbReloadForm = value; }
        }

        public clRowSet Rowset
        {
            get { return goRowset; }
            set { goRowset = value; }
        }

        public string RowsetType
        {
            get { return gsType; }
        }

        public bool ShowFirst
        {
            get { return gbShowFirst; }
        }

        public bool ShowLast
        {
            get { return gbShowLast; }
        }

        public string Table
        {
            get { return gsTable; }
        }

        public int TabInFocus
        {
            get
            {
                //If giSelectedTab = 0 Then giSelectedTab = GetTabIDByPos(1)
                //Return giSelectedTab
                //if (giSelectedTab == 0)
                //{
                //    return GetTabIDByPos(1);
                //}
                //else
                //{
                //    return giSelectedTab;
                //}
                // above if else condition commented because if giSelectedTab is zero - it need to returns zero ------- RN
                return giSelectedTab;
            }
            set { giSelectedTab = value; }
        }

        public string Title
        {
            get { return gsTitle; }
            set { gsTitle = value; }
        }

        public string TitleForHistory
        {
            get { return gsTitleForHistory; }
            set { gsTitleForHistory = value; }
        }
        public bool ErrorOnSave
        {
            get
            {
                bool bTemp = gbErrorOnSave;
                gbErrorOnSave = false;
                return bTemp;
            }
        }

        #endregion

        // private string gsPOP = "";
        // private string gsMeta = "";
        //  private string gsType = "";

        public Form(string sFormID, int iFormType = clC.SELL_FORM_TYPE_NDB)
        {

            string sProc = "clForm:New";
            //try
            //{
                InitializeLists();

                //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
                giFormType = iFormType;

                goMeta = (clMetaData)Util.GetInstance("meta");
                goP = (clProject)Util.GetInstance("p");
                goTR = (clTransform)Util.GetInstance("tr");
                goData = (clData)Util.GetInstance("data");
                goLog = (clLog)Util.GetInstance("log");

                //create guid for this instance
                // gsGUID = Convert.ToString(goData.GenGuid());

                switch (iFormType)
                {
                    case clC.SELL_FORM_TYPE_NDB:

                        // if (Strings.UCase(gsType) == "PREVIEW")
                        // {
                        //gsMeta = HttpContext.Current.Session["FormDesignerMetadata"];
                        // }
                        // else
                        // {
                        gsMeta = GetMetadata(sFormID);
                        // }

                        //get POP md
                        gsPOP = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "", true);
                        gsTELEPHONY = goTR.StrRead(gsPOP, "TELEPHONY", "");
                        gsFORMDISPLAYGRID = goTR.StrRead(gsPOP, "FORMDISPLAYGRID", "0");
                        gsFIELDTOOLTIPS = goTR.StrRead(gsPOP, "FIELDTOOLTIPS", "1");
                        gsFIELDTOOLTIPSSYSNAME = goTR.StrRead(gsPOP, "FIELDTOOLTIPSSYSNAME", "0");
                        gsFIRSTDAYOFWEEK = goTR.StrRead(gsPOP, "FIRSTDAYOFWEEK", "0");

                        //GET VIEW MENU META
                        gsDisplayMeta = goMeta.PageRead("GLOBAL", "MNU_FORM");

                        //get form scripts
                        // gcFormScripts = goScr._GetFormScripts(sFormID);

                        //for form menu needs
                        gsSection = "GLOBAL";
                        gsPage = "FRM_" + sFormID;
                        gsTable = sFormID;

                        //first, let's check to see if rec exists

                        if (Strings.UCase(gsType) == "PREVIEW")
                        {
                            gsTitle = goTR.StrRead(gsMeta, "TITLE", "") + " (Designer Preview)";
                            //glStrReadCount = glStrReadCount + 1
                            //goLog.Log(sProc, "TITLE")
                            gsMode = "PREVIEW";
                        }
                        else
                        {
                            gsTitle = goTR.StrRead(gsMeta, "TITLE", "");
                            //glStrReadCount = glStrReadCount + 1
                            //goLog.Log(sProc, "TITLE")
                            gsMode = "NDB";

                        }

                        //get form field list
                        gcFieldList = GetFieldList();

                        GetHeaderFieldsList();

                        //get hidden fields
                        GetOtherFormInfo();

                        //get tab info
                        int i = 0;
                        int iTabCount = 0;
                        int iTabCounter = 0;
                        int iTabPosition = 0;
                        iTabCount = Convert.ToInt32(goTR.StrRead(gsMeta, "TABCOUNT", "0"));
                        for (i = 1; i <= 99; i++)
                        {
                            iTabPosition = Convert.ToInt32((goTR.StrRead(gsMeta, "TAB" + i + "POSITION", "")) == "" ? 0 : Convert.ToInt32(goTR.StrRead(gsMeta, "TAB" + i + "POSITION", "")));
                            if (iTabPosition != 0)
                            {
                                SetTabPosToID(iTabPosition, i);
                                iTabCounter = iTabCounter + 1;
                                if (iTabCounter == iTabCount)
                                    break; // TODO: might not be correct. Was : Exit For
                            }
                        }


                        break;
                    case clC.SELL_FORM_TYPE_CVS:
                        gsPage = sFormID;

                        break;
                    case clC.SELL_FORM_TYPE_FRF:
                        gsPage = sFormID;

                        break;
                }

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}

        }



        //private clMetaData goMeta;
        //private clTransform goTR;
        //private clData goData;
        //private clProject goP;
        //ByVal sTable As String, ByVal sRecordID As String, _
        //            Optional ByVal sType As String = "", _
        //            Optional ByVal sDTGUID As String = "", Optional ByVal sViewID As String = "", _
        //            Optional ByVal sArg As String = "", Optional ByVal sLinkboxAction As String = "", _
        //            Optional ByVal sLinkboxGUID As String = "", Optional ByVal par_sReuseGUID As String = "", _
        //            Optional ByVal iTabInFocus As Integer = -1
        public Form(string tableName, string recordID, string type = "", string sDTGUID = "", string sViewID = ""
            , string sArg = "", string sLinkboxAction = "", string sLinkboxGUID = "", string par_sReuseGUID = "", int iTabInFocus = -1)
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            //goErr
            goScr = new ScriptManager();
            InitializeLists();
            oVar = new clVar();
            if (!string.IsNullOrEmpty(par_sReuseGUID))
            {
                gsGUID = par_sReuseGUID;
            }
            else
            {
                gsGUID = goData.GenGuid();
            }
            //'record desktop guid for toprec/selrec state save after save
            gsDesktopGUID = sDTGUID;
            //if (gsDesktopGUID == "" && goHist.CurrentObjType == "DESKTOP")
            //{
            //    gsDesktopGUID = goUI.GetLastSelected("LASTOPENDESKTOPGUID");
            //}

            //globalize initialization information
            gsTable = tableName;
            gsType = type;
            gsRecordID = recordID;
            gsLinkboxAction = sLinkboxAction;
            gsLinkboxGUID = sLinkboxGUID;

            TableName = tableName;
            RecordId = recordID;
            Type = type;
            TabInFocus = 0;
            //TabInFocus = 1;
            FormMetaData = GetMetadata(TableName.ToUpper());

            //'check for record index in it's view
            if (gsRecordID.Contains("_"))
            {
                gsRecordIndex = goTR.ExtractString(gsRecordID, 2, "_");
                gsRecordID = goTR.ExtractString(gsRecordID, 1, "_");
            }
            if (type.ToUpper() == "PREVIEW")
            {
                //gsMeta = HttpContext.Current.Session("FormDesignerMetadata");
            }
            else
            {
                //when comes from manage form to get preview..J
                if (Util.GetSessionValue("FormDesignerMetadata") != null)
                {
                    gsMeta = Util.GetSessionValue("FormDesignerMetadata").ToString();
                    FormMetaData = Util.GetSessionValue("FormDesignerMetadata").ToString();
                    Util.ClearSessionValue("FormDesignerMetadata");
                }
                else
                {
                    gsMeta = GetMetadata(gsTable);
                }

            }

            gsPOP = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "", true);
            gsTELEPHONY = goTR.StrRead(gsPOP, "TELEPHONY", "");
            gsFORMDISPLAYGRID = goTR.StrRead(gsPOP, "FORMDISPLAYGRID", "0");
            gsFIELDTOOLTIPS = goTR.StrRead(gsPOP, "FIELDTOOLTIPS", "1");
            gsFIELDTOOLTIPSSYSNAME = goTR.StrRead(gsPOP, "FIELDTOOLTIPSSYSNAME", "0");
            gsFIRSTDAYOFWEEK = goTR.StrRead(gsPOP, "FIRSTDAYOFWEEK", "0");
            showAsterisk  = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "SHOW_ASTERISK_FOR_REQ_FIELDS","0");
            //GET VIEW MENU META
            gsDisplayMeta = goMeta.PageRead("GLOBAL", "MNU_FORM");

            //validate table value          
            if (string.IsNullOrEmpty(gsTable))
            {
                gsTable = goTR.GetFileFromSUID(gsRecordID);
                if (string.IsNullOrEmpty(gsTable))
                {
                    gbNoRecordExists = true;
                    gsTitle = "<Record Not Found>";
                    gsTitleForHistory = gsTitle;
                    return;
                }
            }

            //get form field list
            gcFieldList = GetFieldList();

            GetHeaderFieldsList();

            //get tab info
            int j = 0;
            int iTabCount = 0;
            int iTabCounter = 0;
            int iTabPosition = 0;
            iTabCount = Convert.ToInt32(goTR.StrRead(gsMeta, "TABCOUNT", "0"));
            for (j = 1; j <= 99; j++)
            {
                iTabPosition = Convert.ToInt32((goTR.StrRead(gsMeta, "TAB" + j + "POSITION", "")) == "" ? 0 : Convert.ToInt32(goTR.StrRead(gsMeta, "TAB" + j + "POSITION", "")));
                if (iTabPosition != 0)
                {
                    SetTabPosToID(iTabPosition, j);
                    iTabCounter = iTabCounter + 1;
                    if (iTabCounter == iTabCount)
                        break; // TODO: might not be correct. Was : Exit For
                }
            }

            //get fields required
            gsFLDMeta = goMeta.PageRead("GLOBAL", "FLD_" + gsTable);
            string sDef = goTR.StrRead(gsFLDMeta, "FIELDSREQUIRED", "", false);

            string[] aDef = Strings.Split(sDef, "|");
            int i = 0;
            for (i = 0; i <= aDef.Length - 1; i++)
            {
                if (!string.IsNullOrEmpty(aDef[i]) & !gcFieldsRequired.Contains(Strings.UCase(aDef[i])))
                    gcFieldsRequired.Add(Strings.UCase(aDef[i]), Strings.UCase(aDef[i]));
            }



            MetaData_OthSettings = goMeta.PageRead("GLOBAL", "OTH_SETTINGS_FRM_" + tableName, "", false, goP.GetProduct());
            sDef = goTR.StrRead(gsMeta_OthSettings, "SELECTABLELINKBOXLINES", "", false);
            aDef = Strings.Split(sDef, "|");
            for (i = 0; i <= aDef.Length - 1; i++)
            {
                if (!string.IsNullOrEmpty(aDef[i]) & !gcSelectableLinks.Contains(Strings.UCase(aDef[i])))
                    gcSelectableLinks.Add(Strings.UCase(aDef[i]), Strings.UCase(aDef[i]));
            }

            //get form scripts
            gcFormScripts = goScr._GetViewScripts(TableName);

            //for form menu needs
            gsSection = "GLOBAL";
            gsPage = "FRM_" + gsTable;

            //first, let's check to see if rec exists
            if (type.ToUpper() == "PREVIEW")
            {
                gsTitle = goData.GetFileLabelFromName(gsTable) + " (Designer Preview)";
                gsTitleForHistory = gsTitle;
                gsMode = "PREVIEW";
            }
            else
            {
                //get rowset of record
                FormRowSet = GetRowset(TableName, RecordId, Type);
                goRowset = FormRowSet;

                if (goRowset.GetFirst() == 1)
                {
                    giRecShareState = Convert.ToInt32(goRowset.GetFieldVal("SI__SHARESTATE", 2));


                    if (string.IsNullOrEmpty(type) == false)
                    {
                        //store form title and determine "Mode"
                        switch (Strings.Left(type, 4).ToUpper())
                        {
                            case "CRU_":
                            case "CRL_":
                                //creation
                                //gsTitle = goData.GetFileLabelFromName(gsTable) + " (New)";
                                //gsTitleForHistory = gsTitle;
                                Title = goData.GetFileLabelFromName(TableName) + " (New)";
                                gsMode = "CREATION";
                                break;
                            default:
                                //modification or creation or design?
                                //gsTitle = goData.GetFileLabelFromName(gsTable) + " (Details)";
                                //string sName = goRowset.GetFieldVal("SYS_NAME");
                                //if (string.IsNullOrEmpty(sName))
                                //    sName = "Details";
                                //gsTitleForHistory = goData.GetFileLabelFromName(gsTable) + " (" + sName + ")";
                                //SetSelRec(gsRecordID);
                                Title = goData.GetFileLabelFromName(TableName) + " (Details)";
                                gsMode = "MODIF";
                                break;
                        }

                        ////set any ndb crl values here
                        //if (gsMode == "CREATION")
                        //{
                        //    Title = goData.GetFileLabelFromName(TableName) + " (Details)";
                        //}


                    }
                    else
                    {
                        gsMode = "MODIF";
                        Title = goData.GetFileLabelFromName(TableName) + " (Details)";
                    }

                    //if (gsMode == "MODIF")
                    //{
                    //    Title = goData.GetFileLabelFromName(TableName) + " (Details)";
                    //}
                    //else if (gsMode == "CREATION")
                    //{
                    //    Title = goData.GetFileLabelFromName(TableName) + " (New)";
                    //}

                    //Commenting this as this is calling for edit mode also so that user can not able to edit the record though he has permissions..J
                    //if (!goData.GetAddPermission(tableName))
                    //{
                    //    SetControlState("BTN_SAVE", 4, 0, true);
                    //}

                }
                else
                {
                    Title = goData.GetFileLabelFromName(TableName) + " (Record Not Found)";
                    gbNoRecordExists = true;
                }

            }

            if (gbNoRecordExists == false)
            {
                switch (Strings.Left(gsType, 4))
                {
                    case "CRL_":
                    case "CRU_":
                        if (!goData.GetAddPermission(gsTable))
                        {
                            //Gray out the form save button
                            SetControlState("BTN_SAVE", 4, 0, true);
                            //Tell the user
                            MessagePanel("You don't have permission to create " + goData.GetFileLabelPlurFrName(gsTable) + ".");
                            //Exit Sub
                            //goto StartHereForNoEditPerms;
                        }
                        break;
                    default:
                        if (!goData.GetRecordPermission(goRowset.GetFieldVal("GID_ID").ToString(), "E"))
                        {
                            //Gray out the form save button
                            SetControlState("BTN_SAVE", 4, 0, true);
                            //Tell the user
                            MessagePanel("You don't have permission to edit this record.");
                            //goto StartHereForNoEditPerms;
                        }
                        break;
                }
            }


            if (Strings.Left(gsType, 4) == "CRL_")
            {
                //will be used in cus scripts..J
                //TCKT raised when OP_FormOnloadRecord_Pre in labconco throws error page..J
                goData.LastSelected.SelectedRecordID = recordID;
                gsIsCreateLinkedFromGUID = goData.LastSelected.SelectedRecordID;  
            }                


            ////get rowset of record
            //FormRowSet = GetRowset(TableName, RecordId, Type);
            //goRowset = FormRowSet;            

            //if (string.IsNullOrEmpty(type) == false)
            //{
            //    var typePrefix = type.Substring(0, 4);

            //    if (typePrefix == "CRU_" || typePrefix == "CRL_")
            //    {
            //        gsMode = "CREATION";

            //    }
            //    else
            //    {
            //        gsMode = "MODIF";
            //    }
            //}
            //else
            //{
            //    gsMode = "MODIF";
            //}

            //if (gsMode == "MODIF")
            //{
            //    Title = goData.GetFileLabelFromName(TableName) + " (Details)";
            //}
            //else if (gsMode == "CREATION")
            //{
            //    Title = goData.GetFileLabelFromName(TableName) + " (New)";
            //}

            //if (!goData.GetAddPermission(tableName))
            //{
            //    SetControlState("BTN_SAVE", 4, 0, true);
            //}


            if (goRowset.GetFirst() == 1)
            {
                gsRecordID = goRowset.GetFieldVal("GID_ID").ToString();
            }

            Mode = gsMode;
        }
        public void InitializeLists()
        {
            lsPermControlState = new List<KeyvaluePair>();
            lsControlState = new List<KeyvaluePair>();
            lsHideFields = new List<KeyvaluePair>();
            lsFieldPropertyTooltip = new List<KeyvaluePair>();
            lsTabPropertyTooltip = new List<KeyvaluePair>();
            lsFieldPropertyLabelText = new List<KeyvaluePair>();
        }
        private Collection GetFieldList()
        {
            //PURPOSE:
            //		Reads form metadata and creates a collection of all fields defined on the form
            //       ALSO creates a hard-return delimted string of all fields on form, accessible with .AllFormFields property
            //PARAMETERS:
            //		None
            //RETURNS:
            //		A collection of fields on the form.  (KEY = FIELDNAME IN UPPERCASE, VALUE = TAB)
            //AUTHOR: WT
            string sProc = "clForm::GetFieldList";

            try
            {
                int iTabCount = 0;
                int i = 0;
                int j = 0;
                int iTab = 0;
                string sField = null;
                string sLabel = null;
                string sKey = null;
                string sValue = null;
                Collection cFieldList = new Collection();
                lstFieldList = new List<string>();

                gcFieldLabels.Clear();

                bool bFoundFirstFormField = false;
                bool bFoundFirstTabField = false;
                int par_iValid = 4;
                //get the total tab count.  want to read all tab metadata
                iTabCount = Convert.ToInt32(goTR.StrRead(gsMeta, "TABCOUNT", "0"));
                //glStrReadCount = glStrReadCount + 1
                //goLog.Log(sProc, "TABCOUNT")
                for (iTab = 0; iTab <= iTabCount; iTab++)
                {
                    int iRowCount = 0;
                    int iRowCountTemp = 0;
                    int iColStart = Convert.ToInt32(iTab.ToString() + "1");
                    int iColEnd = Convert.ToInt32(iTab.ToString() + "6");
                    //get the largest rowcount in the column set
                    //use this number as the loop counter to make sure i get all the fields in all 6 columns
                    for (i = iColStart; i <= iColEnd; i++)
                    {
                        int _tempint = 0;
                        int.TryParse(goTR.StrRead(gsMeta, "COL" + i + "ROWCOUNT"), out _tempint);
                        iRowCountTemp = _tempint;
                        //glStrReadCount = glStrReadCount + 1
                        //goLog.Log(sProc, "COL" & i & "ROWCOUNT")
                        if (iRowCountTemp > iRowCount)
                            iRowCount = iRowCountTemp;
                    }

                    //reset 
                    bFoundFirstTabField = false;

                    for (j = 1; j <= iRowCount; j++)
                    {
                        for (i = iColStart; i <= iColEnd; i++)
                        {
                            sKey = i.ToString().PadLeft(3, '0') + j.ToString().PadLeft(3, '0');
                            sValue = goTR.StrRead(gsMeta, sKey);
                            //glStrReadCount = glStrReadCount + 1
                            //goLog.Log(sProc, sKey)
                            switch (Strings.UCase(sValue))
                            {
                                case "":
                                case "[BLANK CELL]":
                                case "[COLSPAN PLACEHOLDER]":
                                case "[ROWSPAN PLACEHOLDER]":
                                    break;
                                //do nothing in this cell
                                default:
                                    //this must be a field!
                                    sField = Strings.UCase(goTR.ExtractString(sValue, 1, ","));

                                    if (ShowStatusBar==false)
                                    {
                                        ShowStatusBar = CheckStatusBar(sValue);

                                        if (ShowStatusBar)
                                            StatusBarField = sField;
                                    }

                                    //get the label while i'm here
                                    string sStateValue = "";
                                    sLabel = goTR.ExtractString(sValue, 2, ",");
                                    if (sLabel == clC.EOT.ToString())
                                        sLabel = "";
                                    if (string.IsNullOrEmpty(sLabel))
                                        sLabel = goData.GetFieldLabelFromName(this.Table, sField, ref par_iValid);
                                    if (sLabel.ToUpper() == "[NO LABEL]")
                                        sLabel = "";
                                    if (!gcFieldLabels.Contains(sField))
                                        gcFieldLabels.Add(sLabel, sField);

                                    if (!cFieldList.Contains(Strings.UCase(sField)))
                                    {
                                        cFieldList.Add(iTab, Strings.UCase(sField));
                                        lstFieldList.Add(sField.ToUpper());
                                        //11/9/10 added list of fields, hard-return delimited
                                        if (string.IsNullOrEmpty(gsFieldList))
                                        {
                                            gsFieldList = Strings.UCase(sField.Replace("#", "specialsymbolexist").Replace("$", "specialsymboldollar"));
                                        }
                                        else
                                        {
                                            gsFieldList = gsFieldList + Constants.vbCrLf + Strings.UCase(sField.Replace("#", "specialsymbolexist").Replace("$", "specialsymboldollar"));
                                        }

                                    }
                                    else
                                    {
                                        string sOldVal = cFieldList[sField.ToUpper()].ToString();
                                        string sNewVal = sOldVal + "|" + iTab;
                                        cFieldList.Remove(Strings.UCase(sField));
                                        lstFieldList.Remove(sField.ToUpper());
                                        cFieldList.Add(sNewVal, Strings.UCase(sField));
                                        lstFieldList.Add(sField.ToUpper());
                                        if (gcDupFields.Contains(Strings.UCase(sField)))
                                            gcDupFields.Add(sField, Strings.UCase(sField));
                                    }
                                    string _temptooltip = "";
                                    //get first field for focus
                                    //what type of field is not "focus-able"
                                    switch (Strings.Left(sField, 4))
                                    {
                                        case "BTN_":
                                        case "GID_":
                                            break;
                                        //DO NOT ALLOW FOCUS
                                        default:
                                            switch (Strings.Left(sField, 8))
                                            {
                                                case "NDB_LBL_":
                                                    break;
                                                //DO NOT ALLOW FOCUS

                                                default:

                                                    //ALLOW FOCUS, but must be enabled/visible
                                                    bool bActiveField = true;

                                                    if (goData.IsFieldSystem(sField, Table))
                                                    {
                                                        bActiveField = false;
                                                    }
                                                    else
                                                    {
                                                        switch (Strings.Left(sField, 4))
                                                        {
                                                            case "LNK_":
                                                                object iStateValue = 0;

                                                                if (GetControlState("STATE", sField, ref  iStateValue) == true)
                                                                {
                                                                    switch (int.Parse(iStateValue.ToString()))
                                                                    {
                                                                        case 0:
                                                                            //Active
                                                                            break;
                                                                        //no change
                                                                        case 1:
                                                                            //Inactive
                                                                            break;
                                                                        //no change
                                                                        case 2:
                                                                            //Invisible
                                                                            bActiveField = false;
                                                                            break;
                                                                        case 4:
                                                                            //Grayed
                                                                            bActiveField = false;
                                                                            break;
                                                                        case 5:
                                                                            //link button grayed
                                                                            bActiveField = false;
                                                                            break;
                                                                        case 6:
                                                                            //link button invisible
                                                                            bActiveField = false;
                                                                            break;
                                                                    }
                                                                }
                                                                break;
                                                            default:
                                                                //get control state information
                                                                iStateValue = 0;
                                                                if (GetControlState("STATE", sField, ref  iStateValue) == true)
                                                                {
                                                                    switch (int.Parse(iStateValue.ToString()))
                                                                    {
                                                                        case 0:
                                                                            //Active
                                                                            break;
                                                                        //no change
                                                                        case 1:
                                                                            //Inactive
                                                                            bActiveField = false;
                                                                            break;
                                                                        case 2:
                                                                            //Invisible
                                                                            bActiveField = false;
                                                                            break;
                                                                        case 4:
                                                                            //Grayed
                                                                            bActiveField = false;
                                                                            break;
                                                                    }
                                                                }
                                                                break;
                                                        }
                                                    }

                                                    if (bActiveField)
                                                    {
                                                        if (!bFoundFirstTabField)
                                                        {
                                                            if (string.IsNullOrEmpty(gsFieldInFocus))
                                                                gsFieldInFocus = Strings.UCase(sField);
                                                            gcFirstFieldsOnTab.Add(sField.ToUpper(), iTab.ToString());
                                                            bFoundFirstTabField = true;
                                                        }
                                                    }

                                                    break;
                                            }
                                            break;
                                    }

                                    //SAVE VISIBLE LINKS TO LIST
                                    if (iTab == 0 | iTab == TabInFocus)
                                    {
                                        switch (Strings.Left(sField, 4))
                                        {
                                            case "LNK_":
                                                if (string.IsNullOrEmpty(gsVisibleLinks))
                                                {
                                                    gsVisibleLinks = sField;
                                                }
                                                else
                                                {
                                                    gsVisibleLinks = gsVisibleLinks + "," + sField;
                                                }

                                                break;
                                            default:
                                                break;
                                            //not a link
                                        }
                                    }

                                    break;
                            }
                        }
                    }
                }
                return cFieldList;
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    goErr.SetError(ex, 45105, sProc);
                //}
                return new Collection();
            }
        }

        private bool CheckStatusBar(string sValue)
        {
            string showStatusBar = goTR.ExtractString(sValue, 15, ",");
            
            if (showStatusBar == "1")
            {
                return true;
            }
            return false;
        }

        private void GetHeaderFieldsList()
        {
            string sKey = "", sValue = "", sField = "";

            for (int i = 1; i <= 6; i++)
            {
                sKey = "HEADER00"+i;
                sValue = goTR.StrRead(gsMeta, sKey,"");

                switch (Strings.UCase(sValue))
                {
                    case "":
                    case "[BLANK CELL]":
                    case "[COLSPAN PLACEHOLDER]":
                    case "[ROWSPAN PLACEHOLDER]":
                        break;
                    //do nothing in this cell
                    default:
                        sField = Strings.UCase(goTR.ExtractString(sValue, 1, ","));

                        if (ShowStatusBar == false)
                        {
                            ShowStatusBar = CheckStatusBar(sValue);
                            
                            if (ShowStatusBar)
                                StatusBarField = sField;
                        }

                        if (!gsFieldList.ToUpper().Contains(sField))
                        {
                            if (string.IsNullOrEmpty(gsFieldList))
                            {
                                gsFieldList = Strings.UCase(sField);
                            }
                            else
                            {
                                gsFieldList = gsFieldList + Constants.vbCrLf + Strings.UCase(sField);
                            }
                        }
                        break;
                }

            }
        }
        public void MessageBoxRemove()
        {
            //PURPOSE:
            //		removes msgbox from form
            //PARAMETERS:
            //		None
            //RETURNS:
            //		Nothing
            //AUTHOR: WT
            string sProc = "clForm::MessageBoxRemove";
            //try
            //{
                gbMBDisplay = false;
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }
        public void MessageBox(string par_sMessage, int par_iStyle = clC.SELL_MB_OK, string par_sTitle = "Selltis", string par_sButton1Label = "", string par_sButton2Label = "", string par_sButton3Label = "", string par_sInputDefaultValue = "", string par_sButton1Script = "", string par_sButton2Script = "", string par_sButton3Script = "",

        object par_doCallingObject = null, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "", string par_sFlag = "", bool par_bRichText = false)
        {
            //PURPOSE:
            //       Displays a message box within the form (under the toolbars) with 1, 2, or 3 buttons,
            //       and, optionally, an input field. Depending on the parameters, clicking the buttons
            //       executes clScripts.MessageBoxEvent script, which uses the parameters (see par_s1 to par_s3 below)
            //       to execute code for each button click.
            //PARAMETERS:
            //       par_iStyle: is not Microsoft.VisualBasic.MsgBoxStyle data type to allow supporting
            //           InputBox as a style and not to have to support all standard MsgBox styles. Initially,
            //           the following styles should be supported:
            //               SELL_MB_OK (default)
            //               SELL_MB_YESNO()
            //               SELL_MB_YESNOCANCEL()
            //               SELL_MB_INPUTBOX (OK button only)
            //               SELL_MB_ABORTRETRYIGNORE = &H2
            //               SELL_MB_OKCANCEL = &H1
            //               SELL_MB_RETRYCANCEL = &H5
            //               SELL_MB_DEFBUTTON1()
            //               SELL_MB_DEFBUTTON2()
            //               SELL_MB_DEFBUTTON3()
            //           Icons are not supported to keep the spec tight.
            //           Styles can be added (for ex clc.SELL_MB_YESNOCANCEL + clc.SELL_MB_DEFBUTTON2).
            //       par_sButton<1-3>Label allows applying custom labels to the buttons. Leaving these parameters
            //           blank causes default button labels to be used. In the case of SELL_MB_INPUTBOX,
            //           par_sButton1Label relabels the OK button.
            //       par_sInputDefaultValue is the value to enter into the input field by default if the style
            //           is SELL_MB_INPUTBOX, else it's ignored.
            //       par_sButton<1-3>Script allows defining script procedure names that will be executed when each button
            //           is clicked. If a script is not defined, the button click causes Return False to be executed.
            //           Typically the code behind yes, No, Cance, and OK buttons is in clScripts.MessageBoxEvent()
            //           script. This script discerns which button was clicked via par_s1 parameter.
            //       par_doCallingObject: Unused. The form object under which this method (MessageBox) is running
            //           is sent to MessageBoxEvent script in par_doCallingObject parameter.
            //       par_doArray: clArray object through which you can pass an array of string values to the
            //           MessageBoxEvent script.
            //       par_s1: String that identifies via MessageBoxEvent's par_s1 parameter which button was clicked.
            //           Example: "YES" or "1".
            //       par_s2: String that identifies via MessageBoxEvent's par_s1 parameter which button was clicked.
            //           Example: "NO" or "2".
            //       par_s3: String that identifies via MessageBoxEvent's par_s1 parameter which button was clicked.
            //           Example: "CANCEL" or "3".
            //       par_s4: Sent unchanged to MessageBoxEvent script as par_s4. Use this for passing one string
            //           variable to MessageBoxevent script.
            //       par_s5: Name of calling script plus description of the purpose, e.g. 
            //           "CO_FormOnSave_OfferToLinkTeamLeaderToContacts". Sent unchanged to MessageBoxEvent script as par_s5.
            //NOTE:
            //       The value the user enters in the input box is passed to MessageBoxEvent script via par_s2 parameter.

            string sProc = "clForm::MessageBox";

            //try
            //{
                gbMBDisplay = true;
                gsMBMessage = par_sMessage;
                giMBStyle = par_iStyle;
                gsMBTitle = par_sTitle;
                gsMBButton1Label = par_sButton1Label;
                gsMBButton2Label = par_sButton2Label;
                gsMBButton3Label = par_sButton3Label;
                gsMBInputDefaultValue = par_sInputDefaultValue;
                gsMBButton1Script = par_sButton1Script;
                gsMBButton2Script = par_sButton2Script;
                gsMBButton3Script = par_sButton3Script;
                gsMBPar1 = par_s1;
                gsMBPar2 = par_s2;
                gsMBPar3 = par_s3;
                gsMBPar4 = par_s4;
                gsMBPar5 = par_s5;
                gsMBFlag = par_sFlag;
                gbMBRichText = par_bRichText;

                //redirect - open form
                //goUI.SetVar(GUID, Me)
                //HttpContext.Current.Response.Redirect(goUI.Navigate("FORMOBJECT", GUID))

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }
        public void MoveToTab(int iTab, string sField = "")
        {
            //PURPOSE:
            //		gives focus to tab
            //PARAMETERS:
            //		iTab:                     tab number that needs focus
            //       sField:                   if tab unknown, you can send field and form will find first tab that this field appears on, in this case, iTab is ignored
            //                                 if field not found, tab selected is not changed '0
            //RETURNS:
            //		Nothing
            //AUTHOR: WT
            string sProc = "clForm::MoveToTab";

            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
                switch (sField)
                {
                    case "":
                        giSelectedTab = iTab;
                        break;
                    default:
                        switch (iTab)
                        {
                            case -1:
                                //tab unknown, get from field list
                                if (gcFieldList.Contains(Strings.UCase(sField)))
                                {
                                    string sFieldInfo = gcFieldList[sField.ToUpper()].ToString();
                                    int iTempTab = 0;
                                    int.TryParse(goTR.ExtractString(sFieldInfo, 1, "|"), out iTempTab);
                                    if (iTempTab != 0)
                                        giSelectedTab = iTempTab;
                                    //do not change tab unless explicity need to
                                    if (goP.GetProduct() == "MB")
                                        if (iTempTab == 0)
                                            giSelectedTab = giFirstTab;
                                    //do not change tab unless explicity need to
                                    gsFieldInFocus = sField;
                                }
                                break;
                            default:
                                giSelectedTab = iTab;
                                gsFieldInFocus = sField;
                                break;
                        }

                        break;
                }

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }
        public int Save(int iSaveType = 0, bool bRunFormOnSave = true, string sScriptInitiated = "")
        {
            //PURPOSE:
            //		Saves a form by any combination of the following: calling the FormOnSave script, committing the rowset, closing the form
            //PARAMETERS:
            //		iSaveType:                        determines save behavior.  can be any of the following integers:
            //                                               0 = save as called from form control, save and close, redirect to previous item in history
            //                                               1 = save for scripting, save and close, redirect to previous item in history
            //                                               2 = save for scripting, save and remove item from history, no redirect...left up to script to redirect
            //                                               3 = save for scripting, save, but do not close (do not remove from history) and keep rowset available
            //                                               4 = save for form browsing or other form events that require IsDirty flag to be considered
            //                                               5 = save for 'save and leave open', do not close (do not remove from history) and keep rowset available, reopen as edit
            //		bRunFormOnSave:                     True/False, determines whether or not the run formonsave script.  Used mainly by scripting. 
            //		sScriptInitiated:                   The name of the script that initiated the pseudo-save. For example, if the script is saving a record because the user clicked a custom toolbar control (like "Send")...this is a "Save", though the form control is not initially aware of it.
            //RETURNS:
            //		1 on success, 0 on failure
            //AUTHOR: WT
            string sProc = "clForm::Save";
            //try
            //{
                //V_T Setting Email Alerts flag to 'ON' to send email alerts in form save
                goP.SetVar("EmailAlertsSave", "ON");

                int iResult = 0;

                if (iSaveType == 5)
                {
                    gsMBFormMode = "SAVEANDKEEPOPEN";
                }
                else
                {
                    gsMBFormMode = "SAVE";
                }
                gsMBScriptInitiatedSave = sScriptInitiated;
                //remove any messageboxes that user chose to ignore...don't want them keeping the form open for no reason.
                MessageBoxRemove();
                if (gbIsDirty == false)
                {
                    if (iSaveType == 0)
                        gbIsDirty = true;
                    switch (Strings.Left(gsType, 4))
                    {
                        case "CRL_":
                        case "CRU_":
                            gbIsDirty = true;
                            break;
                        default:
                            gbIsDirty = goRowset.bRSIsDirty;
                            break;
                    }
                }
                if (gbIsDirty)
                {
                    switch (Strings.Left(gsType, 4))
                    {
                        case "CRL_":
                        case "CRU_":
                            //check the add permission
                            if (!goData.GetAddPermission(gsTable))
                            {
                                MessageBox("You don't have permission to create " + goData.GetFileLabelPlurFrName(gsTable) + ".");
                                gsMBScriptInitiatedSave = "";
                                //1/6/09 wt made this change.
                                gsMBFormMode = "NORMAL";
                                //1/6/09 wt made this change.
                                return 0;
                            }
                            break;
                        default:
                            //Modification mode
                            if (!goData.GetRecordPermission(goRowset.GetFieldVal("GID_ID").ToString(), "E"))
                            {
                                MessageBox("You don't have permission to edit the " + goData.GetFileLabelFromName(gsTable) + " '" + goRowset.GetFieldVal("SYS_Name") + "'.");
                                gsMBScriptInitiatedSave = "";
                                //1/6/09 wt made this change.
                                gsMBFormMode = "NORMAL";
                                //1/6/09 wt made this change.
                                return 0;
                            }
                            break;
                    }
                }

                if (gbIsDirty)
                {
                    //run form on save script before commit
                    if (bRunFormOnSave)
                    {
                        object par_oReturn = null;
                        bool par_bRunNext = true;
                        string par_sSections = "";
                        object _this = this;
                        goScr = new ScriptManager();
                        ScriptManager _ScriptManager = new ScriptManager();
                        var result = _ScriptManager.RunScript(gsTable + "_FormOnSave", ref _this, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                        if (result == false)
                        {
                            gbErrorOnSave = true;
                            gsMBScriptInitiatedSave = "";
                            //12/4/07 wt made this change.
                            gsMBFormMode = "NORMAL";
                            //12/4/07 wt made this change.
                            //if formonsave returns 0, then i cancel the save process to display some error/messagebox to the user.
                            //at this time, i have to reset the "script that initiated save" var to "", that way, when the msgbox shows, 
                            //i don't think you initiated it with a save script and i'm back to "normal" mode.  
                            return 0;
                        }
                        else
                        {
                            gbErrorOnSave = false;
                        }
                    }
                    //if script calls for messagebox, cancel save and return to form
                    if (gbMBDisplay == true)
                        return 0;
                }

                if (!gbCancelSave)
                {
                    //get record GID & name
                    string sGID = goRowset.GetFieldVal("GID_ID").ToString();
                    string sSYSNAME = goRowset.GetFieldVal("SYS_NAME").ToString();

                    if (gbIsDirty)
                    {
                        //V_T 4/24/2015 Attachments functionality Implementation
                        //Save the attachments in the 'AD' table

                        //get all the 'ADR_' fields
                        ArrayList ADRFields = new ArrayList();
                        ArrayList ADRFieldsValues = new ArrayList();
                        clAttachments objclAttachments = new clAttachments();
                        //V_T Get all ADR fields
                        for (int index = 0; index <= goRowset.oDataSet.Tables[0].Columns.Count - 1; index++)
                        {
                            if (goRowset.oDataSet.Tables[0].Columns[index].ColumnName.Contains("ADR_"))
                            {
                                ADRFields.Add(goRowset.oDataSet.Tables[0].Columns[index].ColumnName);
                            }
                        }
                        //commit
                        iResult = goRowset.Commit();
                        //V_T reset the Email Alerts flag to empty
                        goP.SetVar("EmailAlertsSave", "");
                        if (iResult == 0)
                        {
                            //field requirements not met error
                            if (goErr.GetLastError("NUMBER") == "E47260")
                            {
                                string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                                if (!string.IsNullOrEmpty(sField))
                                {
                                    string sLabel = null;
                                    if (gcFieldLabels.Contains(Strings.UCase(sField)))
                                    {
                                        sLabel = gcFieldLabels[sField.ToUpper()].ToString();
                                    }
                                    else
                                    {
                                        sLabel = Strings.UCase(sField);
                                    }

                                    goErr.SetWarning(30029, sProc, "", sLabel, "", "", "", "", "", "", "", "", sField);
                                    MoveToTab(-1, sField);
                                }
                            }
                            gbErrorOnSave = true;
                            return 0;
                        }
                        else
                        {
                            //V_T 4/24/2015 Attachments functionality Implementation
                            //Save the attachments in the 'AD' table for all the ADR Fields
                            //do it for each ADR field in the row set

                            string Key = "";
                            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
                            {
                                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
                            }

                            Util.SetSessionValue(Key + "_" + "SelectedRecordID", goRowset.GetFieldVal("GID_ID").ToString());
                            Util.SetSessionValue("SavedFormGID_ID", goRowset.GetFieldVal("GID_ID").ToString());
                            Util.SetSessionValue("SavedFormLabelName", goRowset.GetFieldVal("SYS_NAME").ToString());
                            for (int index = 0; index <= ADRFields.Count - 1; index++)
                            {
                                objclAttachments.SaveAttachments(ADRFields[index].ToString(), goRowset.GetFieldVal(ADRFields[index].ToString()).ToString(), sGID, goRowset.GetFileName());
                            }
                        }
                        //if not dirty, then "save" was good
                    }
                    else
                    {
                        iResult = 1;
                    }
                    object par_oReturn = null;
                    bool par_bRunNext = true;
                    string par_sSections = "";
                    object _this = this;
                    //call formaftersave event
                    //goScr = new ScriptManager();
                    var result = goScr.RunScript(gsTable + "_FormAfterSave", ref _this, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
                    if (result == false)
                    {
                        gbErrorOnSave = true;
                        return 0;
                    }
                    else
                    {
                        gbErrorOnSave = false;
                    }
                    //VNK- Commented out, need to bring it out later..
                    //if this save was a result of form opened from dt, then make changes to dt's selected view's selected record information
                    switch (gsDesktopGUID)
                    {
                        case "":
                            //if this was a "create new" from a linkbox selector
                            if (goP.GetVar(gsLinkboxGUID + "_LinkboxSelector").GetType().Name != "String")
                            {
                                LinkBox oLinkbox = (LinkBox)goP.GetVar(gsLinkboxGUID + "_LinkboxSelector");
                                if ((oLinkbox != null))
                                {
                                    string sTopRec = GetTopRecValueForLinkbox(ref sGID, oLinkbox);
                                    if (gsLinkboxAction == "new")
                                    {
                                        oLinkbox.RecordAdded = true;
                                        if (oLinkbox.SelectedOnly)
                                        {
                                            oLinkbox.RecordAddedInfo = sGID + Strings.Chr(1) + sSYSNAME;
                                        }
                                        else
                                        {
                                            oLinkbox.TopRec = sTopRec;
                                            oLinkbox.CheckTopRec = true;
                                        }
                                    }
                                }
                            }
                            break;
                        default:
                            //switch (Strings.UCase(Strings.Left(gsType, 4)))
                            //{
                            //    case "CRU_":
                            //    case "CRL_":
                            //        if (iResult == 1)
                            //           SetTopRec(sGID);
                            //        break;
                            //    default:
                            //        //NO PERMANENT TOPREC CHANGES NEEDED
                            //        if (iResult == 1)
                            //          //  SetTopRec(sGID, true);
                            //        break;
                            //}
                            break;
                    }

                    //if asked to close, then if save was good, close form
                    switch (iSaveType)
                    {
                        //***062608 added 1 to case
                        case 0:
                        case 1:
                            //save as called from form control
                            switch (iResult)
                            {
                                case 0:
                                    return iResult;
                                case 1:
                                    gsMBScriptInitiatedSave = "";
                                    //1/17/10 wt made this change.
                                    gsMBFormMode = "NORMAL";
                                    //1/17/10 wt made this change.
                                    //System.Web.HttpContext.Current.Response.Redirect(goUI.Navigate("CLOSE", ""));                            
                                    break;
                            }
                            break;
                        case 2:
                            //save for scripting, save and remove item from history, no redirect...left up to script to redirect
                            switch (iResult)
                            {
                                case 0:
                                    return iResult;
                                case 1:
                                    gsMBScriptInitiatedSave = "";
                                    //1/17/10 wt made this change.
                                    gsMBFormMode = "NORMAL";
                                    //1/17/10 wt made this change.

                                    //goUI.Navigate("CLOSE", "");
                                    return iResult;
                            }
                            break;
                        case 3:
                        case 4:
                            //save for scripting, save, but do not close (do not remove from history) and keep rowset available
                            //4=browsing, no redirect here
                            if (iResult == 1)
                            {
                                gsMBScriptInitiatedSave = "";
                                //1/17/10 wt made this change.
                                gsMBFormMode = "NORMAL";
                                //1/17/10 wt made this change.
                            }

                            return iResult;
                        case 5:
                            //save for 'save and leave open, do not close (do not remove from history) and keep rowset available, reopen as edit

                            switch (iResult)
                            {
                                case 0:
                                    return iResult;
                                case 1:
                                    gsMBScriptInitiatedSave = "";
                                    //1/17/10 wt made this change.
                                    gsMBFormMode = "NORMAL";
                                    //1/17/10 wt made this change.
                                    Form oNewForm = null;
                                    oNewForm = new Form(this.Table, this.RecordID, "", this.gsDesktopGUID, this.gsViewID, "", this.gsLinkboxAction, this.gsLinkboxGUID, this.GUID);
                                    oNewForm.TabInFocus = this.giSelectedTab;
                                    //goUI.SetVar(this.GUID, oNewForm);

                                    //HttpContext.Current.Response.Redirect(goUI.Navigate("RELOAD", this.GUID));
                                    ClUI goUI = new ClUI();
                                    //goUI.ReleaseQueue();
                                    //25042019 tckt #2793:None of the checkboxes on the top of the forms to create another record when you save an leave open are working.  On JCI the from ac the create qt is not working...J
                                    goUI.Queue("RELOAD", oNewForm);
                                    return iResult;
                            }
                            break;
                    }
                }
                else
                {
                    //HttpContext.Current.Response.Redirect(goUI.Navigate("CLOSE", ""));
                    return iResult;
                }
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}
            return 0;
        }
        private string GetTopRecValueForLinkbox(ref string sGID, LinkBox oLinkbox)
        {
            //PURPOSE:
            //		creates a toprec value for a linkbox, if this record is being added via the 'create new' link in linkbox selector
            //PARAMETERS:
            //		sGID:          added rec's gid_id
            //		oLinkbox:      the linkbox object
            //RETURNS:
            //		toprec string
            //AUTHOR: WT
            string sProc = "clForm::GetTopRecValueForLinkbox";

            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                clUtil oUtil = new clUtil();
                string sSort = oLinkbox.GetSort();
                return oUtil.GetTopRec(gsTable, sSort, sGID);
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    goErr.SetError(ex, 45105, sProc);
                //}
                return "";
            }
        }
        public clRowSet GetRowset(string tableName, string recordId, string type)
        {
            try
            {
                clRowSet oRS;
                if (!string.IsNullOrEmpty(type))
                {
                    switch (type.Substring(0, 4).ToUpper())
                    {
                        case "CRU_":
                            if (type.ToUpper() == "CRU_")
                                type = type.ToUpper() + tableName;
                            oRS = new clRowSet(tableName, clC.SELL_ADD, "", "", "**", -1, "", "", type);
                            break;
                        case "CRL_":
                            if (recordId == "")
                                recordId = goData.LastSelected.SelectedRecordID;
                            oRS = new clRowSet(tableName, clC.SELL_ADD, "", "", "**", -1, "", "", type.ToUpper(), recordId);
                            break;
                        default:
                            oRS = new clRowSet(tableName, clC.SELL_ADD, "", "", "**", -1, "", "", type.ToUpper());
                            break;
                    }
                }
                else
                {
                    if (gsVisibleLinks == "")
                        gsVisibleLinks = "***";
                    else
                        gsVisibleLinks = "***," + gsVisibleLinks;
                    oRS = new clRowSet(tableName, clC.SELL_EDIT, "GID_ID='" + recordId + "'", "", gsVisibleLinks);
                }
                return oRS;
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }
        public bool GetControlStateOLD(string Property, string FieldName, ref object StateValue)
        {
            switch (Property.ToUpper())
            {
                case "STATE":

                    if (lsPermControlState.Contains(lsPermControlState.Find(p => p.Key.Contains(FieldName))))
                    {
                        StateValue = Convert.ToInt32(lsPermControlState.Find(p => p.Key.Contains(FieldName)).Value);
                        return true;
                    }
                    else
                    {
                        if (lsControlState.Contains(lsControlState.Find(p => p.Key.Contains(FieldName))))
                        {
                            StateValue = Convert.ToInt32(lsControlState.Find(p => p.Key.Contains(FieldName)).Value);
                            return true;
                        }
                        else
                        {
                            StateValue = -1;
                            return false;
                        }
                    }
                case "TOOLTIP":

                    if (lsFieldPropertyTooltip.Contains(lsFieldPropertyTooltip.Find(p => p.Value.Contains(FieldName))))
                    {
                        StateValue = lsFieldPropertyTooltip.Find(p => p.Value.Contains(FieldName)).Key;
                        return true;
                    }
                    else if (lsTabPropertyTooltip.Contains(lsTabPropertyTooltip.Find(p => p.Value.Contains(FieldName))))
                    {
                        StateValue = lsTabPropertyTooltip.Find(p => p.Value.Contains(FieldName)).Value;
                        return true;

                    }
                    else
                    {
                        StateValue = "";
                        return false;
                    }
                case "LABELTEXT":

                    if (lsFieldPropertyLabelText.Contains(lsFieldPropertyLabelText.Find(p => p.Key.Contains(FieldName))))
                    {
                        StateValue = lsFieldPropertyLabelText.Find(p => p.Key.Contains(FieldName)).Value;
                        return true;
                    }
                    else
                    {
                        StateValue = "";
                        return false;
                    }
                default:
                    return false;
            }
        }

        #region Public Properties ControlState
        public Collection ControlState
        {
            get { return gcControlState; }
        }
        public Collection PermControlState
        {
            get { return gcPermControlState; }
        }
        public Collection FldPro_LabelText
        {
            get { return gcFldPro_LabelText; }
        }
        public Collection FldPro_ToolTip
        {
            get { return gcFldPro_ToolTip; }
        }
        public Collection FldPro_LabelColor
        {
            get { return gcFldPro_LabelColor; }
        }
        public Collection FldPro_Image
        {
            get { return gcFldPro_Image; }
        }
        public Collection FldPro_FontNames
        {
            get { return gcFldPro_FontNames; }
        }
        public Collection TabPro_FontSize
        {
            get { return gcFldPro_FontSize; }
        }
        public Collection Fld_Lnk_Table_Col_State
        {
            get { return gcFld_Lnk_Table_Col_State; }
        }
        #endregion

        /// <summary>
        /// Reads control state collection to get value for a particular fields state
        /// </summary>
        /// <param name="sProperty"> STATE/LABELTEXT/TOOLTIP/IMAGE/LABELCOLOR/FONTNAMES/FONTSIZE</param>
        /// <param name="sField">What field's property are you looking for?</param>
        /// <param name="oValue">Returns the value of the property by reference</param>
        /// <returns>True if the field property exists in the collection, False if it does not.</returns>
        public bool GetControlState(string sProperty, string sField, ref object oValue)
        {
            try
            {
                switch (Strings.UCase(sProperty))
                {
                    case "STATE":
                        if (gcPermControlState.Contains(sField))
                        {
                            oValue = gcPermControlState[sField];
                            return true;
                        }
                        else
                        {
                            if (gcControlState.Contains(sField))
                            {
                                oValue = gcControlState[sField];
                                return true;
                            }
                            else
                            {
                                oValue = -1;
                                return false;
                            }
                        }
                        break;
                    case "LABELTEXT":
                        if (gcFldPro_LabelText.Contains(sField))
                        {
                            oValue = gcFldPro_LabelText[sField].ToString();
                            return true;
                        }
                        else
                        {
                            oValue = "";
                            return false;
                        }
                        break;
                    case "TOOLTIP":
                        if (gcFldPro_ToolTip.Contains(sField))
                        {
                            oValue = gcFldPro_ToolTip[sField].ToString();
                            return true;
                        }
                        else if (gcTabPro_Tooltip.Contains(sField))
                        {
                            oValue = gcTabPro_Tooltip[sField].ToString();
                            return true;
                        }
                        else
                        {
                            oValue = "";
                            return false;
                        }
                        break;
                    case "LABELCOLOR":
                        if (gcFldPro_LabelColor.Contains(sField))
                        {
                            oValue = gcFldPro_LabelColor[sField].ToString();
                            return true;
                        }
                        else if (gcTabPro_LabelColor.Contains(sField))
                        {
                            oValue = gcTabPro_LabelColor[sField].ToString();
                            return true;
                        }
                        else
                        {
                            oValue = "";
                            return false;
                        }
                        break;
                    case "IMAGE":
                        if (gcFldPro_Image.Contains(sField))
                        {
                            oValue = gcFldPro_Image[sField].ToString();
                            return true;
                        }
                        else
                        {
                            oValue = "";
                            return false;
                        }
                        break;
                    case "FONTNAMES":
                        if (gcFldPro_FontNames.Contains(sField))
                        {
                            oValue = gcFldPro_FontNames[sField].ToString();
                            return true;
                        }
                        else if (gcTabPro_FontNames.Contains(sField))
                        {
                            oValue = gcTabPro_FontNames[sField].ToString();
                            return true;
                        }
                        else
                        {
                            oValue = "";
                            return false;
                        }
                        break;
                    case "FONTSIZE":
                        if (gcFldPro_FontSize.Contains(sField))
                        {
                            oValue = gcFldPro_FontSize[sField];
                            return true;
                        }
                        else if (gcTabPro_FontSize.Contains(sField))
                        {
                            oValue = gcTabPro_FontSize[sField].ToString();
                            return true;
                        }
                        else
                        {
                            oValue = -1;
                            return false;
                        }
                        break;
                }
                return false;
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return false;
            }

        }
        public int GetControlState(string sField)
        {
            try
            {
                int iState = -1;
                if (gcPermControlState.Contains(Strings.UCase(sField)))
                {
                    iState = (int)gcPermControlState[Strings.UCase(sField)];
                }
                else
                {
                    if (gcControlState.Contains(Strings.UCase(sField)))
                    {
                        iState = (int)gcControlState[Strings.UCase(sField)];
                    }
                    else
                    {
                        iState = -1;
                    }
                }
                return iState;
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return -1;
            }

        }
        //Unused
        public void SetControlStateUnused(string Control, int State, int Tab = 0, bool Permanent = false)
        {
            switch (Tab)
            {
                case 0:
                    switch (Permanent)
                    {
                        case false:
                            if (lsControlState.Contains(lsControlState.Find(p => p.Key.Contains(Control.ToUpper()))))
                            {
                                lsControlState.Remove(lsControlState.Find(p => p.Key.Contains(Control.ToUpper())));
                                lsControlState.Add(new KeyvaluePair
                                {
                                    Key = Control.ToUpper(),
                                    Value = State.ToString()
                                });
                            }
                            else
                            {
                                lsControlState.Add(new KeyvaluePair
                                {
                                    Key = Control.ToUpper(),
                                    Value = State.ToString()
                                });
                            }
                            break;
                        case true:
                            //use this collection to store more sensitive control state information, one that scripts cannot clear
                            if (lsPermControlState.Contains(lsControlState.Find(p => p.Key.Contains(Control.ToUpper()))))
                            {
                                lsPermControlState.Remove(lsControlState.Find(p => p.Key.Contains(Control.ToUpper())));
                                lsPermControlState.Add(new KeyvaluePair
                                {
                                    Key = Control.ToUpper(),
                                    Value = State.ToString()
                                });
                            }
                            else
                            {
                                lsPermControlState.Add(new KeyvaluePair
                                {
                                    Key = Control.ToUpper(),
                                    Value = State.ToString()
                                });
                            }
                            break;
                    }
                    break;
                default:
                    lsControlState.Add(new KeyvaluePair
                    {
                        Key = Control.ToUpper(),
                        Value = State + "[" + Tab + "]"
                    });
                    break;
            }


        }
        public void SetControlState(string sControl, int iState, int iTab = 0, bool Permanent = false)
        {
            //try
            //{
                switch (iTab)
                {
                    case 0:
                        switch (Permanent)
                        {
                            case false:
                                if (gcControlState.Contains(Strings.UCase(sControl)))
                                {
                                    gcControlState.Remove(Strings.UCase(sControl));
                                    gcControlState.Add(iState, Strings.UCase(sControl));
                                }
                                else
                                {
                                    gcControlState.Add(iState, Strings.UCase(sControl));
                                }
                                break;
                            case true:
                                //use this collection to store more sensitive control state information, one that scripts cannot clear
                                if (gcPermControlState.Contains(Strings.UCase(sControl)))
                                {
                                    gcPermControlState.Remove(Strings.UCase(sControl));
                                    gcPermControlState.Add(iState, Strings.UCase(sControl));
                                }
                                else
                                {
                                    gcPermControlState.Add(iState, Strings.UCase(sControl));
                                }
                                break;
                        }

                        break;
                    default:
                        gcControlState.Add(iState + "[" + iTab + "]", Strings.UCase(sControl));
                        break;
                }
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, 45105, sProc);
            //    }
            //}


        }
        //Unused
        public bool SetFieldPropertiesUnused(string FieldName, int State = -1, int XPos = -1, int YPos = -1, int Width = -1, int Height = -1, string LabelText = "^^^^^", int LabelAbove = -1, int LabelWidth = -1,
            string Tooltip = "^^^^^", string Image = "^^^^^", string LabelColor = "^^^^^", string FontNames = "^^^^^", int FontSize = -1)
        {
            try
            {
                if (State != -1)
                {
                    if (lsControlState.Contains(lsControlState.Find(p => p.Key.Contains(FieldName))))
                    {
                        lsControlState.Remove(lsControlState.Find(p => p.Key.Contains(FieldName)));
                        lsControlState.Add(new KeyvaluePair
                        {
                            Key = FieldName,
                            Value = State.ToString()
                        });
                    }
                    else
                    {
                        lsControlState.Add(new KeyvaluePair
                        {
                            Key = FieldName,
                            Value = State.ToString()
                        });
                    }
                }
                if (Tooltip != "^^^^^")
                {
                    if (lsFieldPropertyTooltip.Contains(lsFieldPropertyTooltip.Find(p => p.Key.Contains(FieldName))))
                    {
                        lsFieldPropertyTooltip.Remove(lsFieldPropertyTooltip.Find(p => p.Value.Contains(FieldName)));
                        lsFieldPropertyTooltip.Add(new KeyvaluePair
                        {
                            Key = Tooltip,
                            Value = FieldName
                        });
                    }
                    else
                    {
                        lsFieldPropertyTooltip.Add(new KeyvaluePair
                        {
                            Key = Tooltip,
                            Value = FieldName
                        });
                    }

                }

                if (LabelText != "^^^^^")
                {
                    if (lsFieldPropertyLabelText.Contains(lsFieldPropertyLabelText.Find(p => p.Key.Contains(FieldName))))
                    {
                        lsFieldPropertyLabelText.Remove(lsFieldPropertyLabelText.Find(p => p.Key.Contains(FieldName)));
                        lsFieldPropertyLabelText.Add(new KeyvaluePair
                        {
                            Key = FieldName,
                            Value = State.ToString()
                        });
                    }
                    else
                    {
                        lsControlState.Add(new KeyvaluePair
                        {
                            Key = FieldName,
                            Value = State.ToString()
                        });
                    }
                }
                if (Image != "^^^^^")
                {
                    //only supported currently in btn_ controls
                    if (gcFldPro_Image.Contains(FieldName))
                    {
                        gcFldPro_Image.Remove(FieldName);
                        gcFldPro_Image.Add(Image, FieldName);
                    }
                    else
                    {
                        gcFldPro_Image.Add(Image, FieldName);
                    }
                }
                if (LabelColor != "^^^^^")
                {
                    if (gcFldPro_LabelColor.Contains(FieldName))
                    {
                        gcFldPro_LabelColor.Remove(FieldName);
                        gcFldPro_LabelColor.Add(LabelColor, FieldName);
                    }
                    else
                    {
                        gcFldPro_LabelColor.Add(LabelColor, FieldName);
                    }
                }
                if (FontNames != "^^^^^")
                {
                    if (gcFldPro_FontNames.Contains(FieldName))
                    {
                        gcFldPro_FontNames.Remove(FieldName);
                        gcFldPro_FontNames.Add(FontNames, FieldName);
                    }
                    else
                    {
                        gcFldPro_FontNames.Add(FontNames, FieldName);
                    }
                }
                if (FontSize != -1)
                {
                    if (gcFldPro_FontSize.Contains(FieldName))
                    {
                        gcFldPro_FontSize.Remove(FieldName);
                        gcFldPro_FontSize.Add(FontSize, FieldName);
                    }
                    else
                    {
                        gcFldPro_FontSize.Add(FontSize, FieldName);
                    }
                }


                return true;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public bool SetFieldProperties(string par_sFieldName, int par_iState = -1, int XPos = -1, int YPos = -1, int par_lWidth = -1, int par_lHeight = -1,
            string par_sLabelText = "^^^^^", int par_iLabelAbove = -1, int par_lLabelWidth = -1,
            string par_sToolTip = "^^^^^", string par_sImage = "^^^^^", string par_sLabelColor = "^^^^^", string par_sFontNames = "^^^^^", int par_lFontSize = -1)
        {
            try
            {
                string sField = par_sFieldName;
                if (par_iState != -1)
                {
                    if (gcControlState.Contains(sField))
                    {
                        gcControlState.Remove(sField);
                        gcControlState.Add(par_iState, sField);
                    }
                    else
                    {
                        gcControlState.Add(par_iState, sField);
                    }
                }
                if (par_lWidth != -1)
                {
                    //unsupported now
                }
                if (par_lHeight != -1)
                {
                    //unsupported now
                }
                if (par_sLabelText != "^^^^^")
                {
                    if (gcFldPro_LabelText.Contains(sField))
                    {
                        gcFldPro_LabelText.Remove(sField);
                        gcFldPro_LabelText.Add(par_sLabelText, sField);
                    }
                    else
                    {
                        gcFldPro_LabelText.Add(par_sLabelText, sField);
                    }
                }
                if (par_iLabelAbove != -1)
                {
                    //unsupported now
                }
                if (par_lLabelWidth != -1)
                {
                    //unsupported now
                }
                if (par_sToolTip != "^^^^^")
                {
                    if (gcFldPro_ToolTip.Contains(sField))
                    {
                        gcFldPro_ToolTip.Remove(sField);
                        gcFldPro_ToolTip.Add(par_sToolTip, sField);
                    }
                    else
                    {
                        gcFldPro_ToolTip.Add(par_sToolTip, sField);
                    }
                }
                if (par_sImage != "^^^^^")
                {
                    //only supported currently in btn_ controls
                    if (gcFldPro_Image.Contains(sField))
                    {
                        gcFldPro_Image.Remove(sField);
                        gcFldPro_Image.Add(par_sImage, sField);
                    }
                    else
                    {
                        gcFldPro_Image.Add(par_sImage, sField);
                    }
                }
                if (par_sLabelColor != "^^^^^")
                {
                    if (gcFldPro_LabelColor.Contains(sField))
                    {
                        gcFldPro_LabelColor.Remove(sField);
                        gcFldPro_LabelColor.Add(par_sLabelColor, sField);
                    }
                    else
                    {
                        gcFldPro_LabelColor.Add(par_sLabelColor, sField);
                    }
                }
                if (par_sFontNames != "^^^^^")
                {
                    if (gcFldPro_FontNames.Contains(sField))
                    {
                        gcFldPro_FontNames.Remove(sField);
                        gcFldPro_FontNames.Add(par_sFontNames, sField);
                    }
                    else
                    {
                        gcFldPro_FontNames.Add(par_sFontNames, sField);
                    }
                }
                if (par_lFontSize != -1)
                {
                    if (gcFldPro_FontSize.Contains(sField))
                    {
                        gcFldPro_FontSize.Remove(sField);
                        gcFldPro_FontSize.Add(par_lFontSize, sField);
                    }
                    else
                    {
                        gcFldPro_FontSize.Add(par_lFontSize, sField);
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return false;
            }
        }
        public bool SetFieldProperty(string FieldName, string Property, object Value)
        {
            switch (Property.ToUpper())
            {
                case "STATE":
                    return SetFieldProperties(FieldName, Convert.ToInt32(Value));
                case "LABELTEXT":
                    return SetFieldProperties(FieldName, -1, -1, -1, -1, -1, Value.ToString());
                case "TOOLTIP":
                    return SetFieldProperties(FieldName, -1, -1, -1, -1, -1, "^^^^^", -1, -1, Value.ToString());
                case "IMAGE":
                    return SetFieldProperties(FieldName, -1, -1, -1, -1, -1, "^^^^^", -1, -1, "^^^^^", Value.ToString());
                case "LABELCOLOR":
                    return SetFieldProperties(FieldName, -1, -1, -1, -1, -1, "^^^^^", -1, -1, "^^^^^", "^^^^^",Value.ToString());
                case "FONTNAMES":
                    return SetFieldProperties(FieldName, -1, -1, -1, -1, -1, "^^^^^", -1, -1, "^^^^^", "^^^^^", "^^^^^", Value.ToString());
                case "FONTSIZE":
                    return SetFieldProperties(FieldName, -1, -1, -1, -1, -1, "^^^^^", -1, -1, "^^^^^", "^^^^^", "^^^^^", "^^^^^", Convert.ToInt32(Value));
                default:
                    return false;
            }
        }
        public void SetFieldRequiredFlag(string FieldName, bool Required)
        {
            string sLabel = GetFieldLabel(FieldName);
            if (Required)
            {
                string color = goP.GetVar("sMandatoryFieldColor").ToString();
                SetFieldProperty(FieldName, "LABELCOLOR", color);
                if (sLabel.Contains("*"))
                {
                    SetFieldProperty(FieldName, "LABELTEXT", sLabel);
                }
                else {
                    if (showAsterisk == "1")
                    {
                        SetFieldProperty(FieldName, "LABELTEXT", "* " + sLabel);
                    }
                }
            }
            else
            {
                SetFieldProperty(FieldName, "LABELCOLOR", "#666666");
                sLabel = sLabel.Replace("* ", "");
                SetFieldProperty(FieldName, "LABELTEXT", sLabel);
            }

        }

       

        public void SetTabPosToID(int iPos, int iID)
        {
            //PURPOSE:
            //		adds position:id translation for form tabs
            //PARAMETERS:
            //		iPos:  tab position
            //       iID:  tab id
            //RETURNS:
            //		Nothing
            //AUTHOR: WT
            string sProc = "clForm::SetTabPosToID";
            //try
            //{
                if (this.gcTabPosToID.Contains(iPos.ToString()))
                {
                    gcTabPosToID.Remove(iPos.ToString());
                    gcTabPosToID.Add(iID, iPos.ToString());
                }
                else
                {
                    gcTabPosToID.Add(iID, iPos.ToString());
                }
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }
        public int GetTabIDByPos(int iPos)
        {
            //PURPOSE:
            //		gets tab id based on it's position
            //PARAMETERS:
            //		iPos:  tab position
            //RETURNS:
            //		tab id as integer
            //AUTHOR: WT
            string sProc = "clForm::GetTabIDByPos";

            //try
            //{
                if (this.gcTabPosToID.Contains(iPos.ToString()))
                {
                    return Convert.ToInt32(gcTabPosToID[iPos.ToString()]);
                   // return Convert.ToInt32(gcTabPosToID[iPos]);
                }
                else
                {
                    return 1;
                }
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}
            return 1;
        }
        public int GetTab()
        {
            //PURPOSE:
            //		returns tab in focus
            //PARAMETERS:
            //		None
            //RETURNS:
            //		Nothing
            //AUTHOR: WT

            try
            {
                return giSelectedTab;
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return 0;
            }
        }
        public void GetOtherFormInfo()
        {
            string sField = null;

            string sMD = goTR.StrRead(MetaData_OthSettings, "HIDEFIELDS", "");
            string[] aMD = sMD.Split('|');
            int i = 0;
            for (i = 0; i <= aMD.Length - 1; i++)
            {
                sField = aMD[i].ToUpper();
                if (!string.IsNullOrEmpty(sField) && !lsHideFields.Contains(lsHideFields.Find(field => field.Key.Contains(sField))))
                    lsHideFields.Add(new KeyvaluePair
                    {
                        Key = sField,
                        Value = sField
                    });
            }
            sMD = goTR.StrRead(MetaData_OthSettings, "HIDEFIELDS_CREATION", "");
            aMD = sMD.Split('|');
            for (i = 0; i <= aMD.Length - 1; i++)
            {
                sField = aMD[i].ToUpper();
                if (!string.IsNullOrEmpty(sField) && !lsHideFields.Contains(lsHideFields.Find(field => field.Key.Contains("CREATION_" + sField))))
                    lsHideFields.Add(new KeyvaluePair
                    {
                        Key = "CREATION_" + sField,
                        Value = "CREATION_" + sField
                    });
            }
            sMD = goTR.StrRead(MetaData_OthSettings, "HIDEFIELDS_MODIF", "");
            aMD = sMD.Split('|');
            for (i = 0; i <= aMD.Length - 1; i++)
            {
                sField = aMD[i].ToUpper();
                if (!string.IsNullOrEmpty(sField) & !lsHideFields.Contains(lsHideFields.Find(field => field.Key.Contains("MODIF_" + sField))))
                    lsHideFields.Add(new KeyvaluePair
                    {
                        Key = "MODIF_" + sField,
                        Value = "MODIF_" + sField
                    });
            }
        }
        public void MoveToField(string Field)
        {
            string proc = "clForm::MoveToField";
            //goLog.Log(proc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            FieldInFocus = Field.ToUpper();
        }
        //public void MessageBox(string Message)
        //{
        //    MBMessage = Message;
        //}
        public string GetToolTip(string Field, ref string Tooltip)
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goP = (clProject)Util.GetInstance("p");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");

            string PersonalOptionsMetaData = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "", true);

            bool FIELDTOOLTIPS;
            bool FIELDTOOLTIPSSYSNAME;

            if (goTR.StrRead(PersonalOptionsMetaData, "FIELDTOOLTIPS", "1") == "1")
                FIELDTOOLTIPS = true;
            else
                FIELDTOOLTIPS = false;

            if (goTR.StrRead(PersonalOptionsMetaData, "FIELDTOOLTIPSSYSNAME", "0") == "1")
                FIELDTOOLTIPSSYSNAME = true;
            else
                FIELDTOOLTIPSSYSNAME = false;

            //int parValid = 4;
            //string Tooltip = goData.GetFieldLabelFromName(TableName, Field, ref parValid);
            string SystemTooltip = "[" + Field + "]";

            //List<KeyValuePair<string, string>> ToolTipsEmptyLabels = GetToolTipsforEmpltyLabels();

            //if (Tooltip == "" && ToolTipsEmptyLabels.Contains(ToolTipsEmptyLabels.Find(p => p.Key.Contains(Field))))
            //{
            //    Tooltip = ToolTipsEmptyLabels.Find(p => p.Key.Contains(Field)).Value;
            //}


            //set tooltip names
            if (FIELDTOOLTIPS)
            {
                if (FIELDTOOLTIPSSYSNAME)
                {
                    Tooltip = Tooltip + " " + SystemTooltip;
                }
                else
                {
                    Tooltip = Tooltip;
                }
            }
            else
            {
                if (FIELDTOOLTIPSSYSNAME)
                {
                    Tooltip = SystemTooltip;
                }
                else
                {
                    Tooltip = "";
                }
            }

            return Tooltip;
        }
        private string GetMetadata(string sFile)
        {
            //PURPOSE:
            //		Reads in form metadata from disk
            //PARAMETERS:
            //		sFile:          Two character file representations as in "CN"
            //RETURNS:
            //		form metadata as string
            //AUTHOR: WT
            string sProc = "clForm::GetMetadata";
            try
            {
                //Return goMeta.PageRead("GLOBAL", "FRM_XX")
                return goMeta.PageRead("GLOBAL", "FRM_" + Strings.UCase(sFile));
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    goErr.SetError(ex, 45105, sProc);
                //}
                return "";
            }
        }
        public bool IsFieldHidden(string FieldName)
        {
            if (!string.IsNullOrEmpty(Type))
            {
                if (lsHideFields.Contains(lsHideFields.Find(field => field.Key.Contains(FieldName))))
                    return true;
                if (lsHideFields.Contains(lsHideFields.Find(field => field.Key.Contains("CREATION_" + FieldName))))
                    return true;
            }
            else
            {
                if (lsHideFields.Contains(lsHideFields.Find(field => field.Key.Contains(FieldName))))
                    return true;
                if (lsHideFields.Contains(lsHideFields.Find(field => field.Key.Contains("MODIF_" + FieldName))))
                    return true;
            }
            return false;
        }
        public class KeyvaluePair
        {
            //public string FieldName { get; set; }
            public string Key { get; set; }
            public string Value { get; set; }
        }
        public string GetMode()
        {
            string proc = "clForm::GetMode";
            // goLog.Log(proc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return gsMode;
        }
        public string GetOpeningMode()
        {
            //PURPOSE:
            //		Returns the type passed during the creation of the form (if any).
            //       Returns the 'form opening type' passed during the creation (if any).
            //       Types are text of CRL page IDs that followes the "CRL_<filename>:" part,
            //       for example:
            //           ----------------        -------------
            //           CRL Page ID             Opening Mode
            //           ----------------        -------------
            //           CRL_AC:EMAILSENT        "EMAILSENT"
            //           CRL_AC:LEAD             "LEAD"
            //           CRL_MS:REPLYTOALL       "REPLYTOALL"
            //           CRL_AC                  ""
            //PARAMETERS:
            //		None.
            //RETURNS:
            //		String (can be empty).
            //HOW IT WORKS:
            //		Look in :sCreationparams memeber and return the value, if any
            //EXAMPLE:
            //		doForm=GetOpeningMode()
            //string sProc = "clForm::GetOpeningMode";

            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                //To fix tckt# 1801:2017-09-25 16:41 AD after a reply is given to a message the "Read" and Replied" icons must be checked like in 5.5..
                if (gsType.Contains(":"))     //if (Strings.InStr(gsType, ":") == 1)
                {
                    return Strings.Right(gsType, Strings.Len(gsType) - Strings.InStr(gsType, ":"));
                }
                else
                {
                    return "";
                }
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return "";
            }
        }
        public string GetControlVal(string sControlID, string sLinkFieldName = "GID_ID")
        {
            //PURPOSE:
            //		returns the value of a non-databound field
            //PARAMETERS:
            //		sControlID:         non-databound field name
            //       sLinkFieldName:     if is ndb link field, return this field value
            //                           other options for sLinkFieldName: "SYS_NAME"
            //RETURNS:
            //		string value of non-databound field
            //       for links (ndb included) the string is vbcrlf delimited
            //AUTHOR: WT        

            try
            {
                sControlID = Strings.UCase(sControlID);
                string sPrefix = Strings.Left(sControlID, 4);
                string sVal = "";
                string sKey;
                switch (sPrefix)
                {
                    case "CHK_":
                        sKey = sControlID;
                        if (gcNDBFieldVals.Contains(sKey))
                        {
                            sVal = gcNDBFieldVals[sKey].ToString();
                        }
                        else
                        {
                            sVal = null;
                        }
                        break;
                    case "NDB_":
                        sPrefix = Strings.Mid(sControlID, 5, 4);
                        switch (sPrefix)
                        {
                            case "TXT_":
                            case "MMO_":
                            case "CHK_":
                            case "MLS_":
                            case "TEL_":
                            case "IFR_":
                                sKey = sControlID;
                                if (gcNDBFieldVals.Contains(sKey))
                                {
                                    sVal = gcNDBFieldVals[sKey].ToString();
                                }
                                else
                                {
                                    sVal = null;
                                }
                                break;
                            case "LNK_":
                                sKey = sControlID + "_" + sLinkFieldName;
                                if (gcNDBFieldVals.Contains(sKey))
                                {
                                    sVal = gcNDBFieldVals[sKey].ToString();
                                }
                                else
                                {
                                    sVal = null;
                                }
                                break;
                        }
                        break;
                }
                return sVal;
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return "";
            }
        }
        public Form CreateForm(string par_sFile, string par_sLinkToRecord = "", string par_sOpeningMode = "")
        {
            //PURPOSE:
            //		Create a new record on a form and, if record id given, links it to that record
            //PARAMETERS:
            //		par_sFile: filename for which to create a form
            //		par_sLinkToRecord: if <> "" create a linked record, otherwise create an unlinked one
            //		par_sOpeningMode: form opening mode, e.g. "LEAD", "MESSAGEREPLY", "MESSAGEFORWARD",
            //			etc. Each form may support different modes. See clForm::ProcessParams() and
            //			FormOnLoad scripts.
            //RETURNS:
            //		True if successful, False if an error occurred. An error is set if False.
            //string sProc = "clForm::CreateForm";

            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                Form oForm = default(Form);
                string sType = "";

                switch (par_sLinkToRecord)
                {
                    case "":
                        if (!string.IsNullOrEmpty(par_sOpeningMode))
                            par_sOpeningMode = ":" + par_sOpeningMode;
                        sType = "CRU_" + par_sFile + par_sOpeningMode;
                        break;
                    default:
                        if (!string.IsNullOrEmpty(par_sOpeningMode))
                            par_sOpeningMode = ":" + par_sOpeningMode;
                        sType = "CRL_" + par_sFile + par_sOpeningMode;
                        break;
                }
                oForm = new Form(par_sFile, par_sLinkToRecord, sType);
                return oForm;
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return null;
            }
        }
        public string GetRecordID()
        {
            //PURPOSE:
            //		returns GID_ID of record open in form
            //PARAMETERS:
            //		None
            //RETURNS:
            //		Nothing
            //AUTHOR: WT
            //string sProc = "clForm::GetRecordID";            
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                return gsRecordID;
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return "";
            }
        }
        public string GetFieldLabel(string sField)
        {
            //PURPOSE:
            //		returns the label of given field as defined in MD, overwritten by state settings
            //PARAMETERS:
            //		sField:         system name of field
            //RETURNS:
            //		string value of field's label as defined in MD or state settings
            //AUTHOR: WT        
            //string sProc = "clForm::GetFieldLabel";

            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                sField = Strings.UCase(sField);
                if (gcFieldLabels.Contains(sField))
                {
                    object sStateValue = "";
                    string sLabel = gcFieldLabels[sField].ToString();
                    if (GetControlState("LABELTEXT", sField, ref sStateValue) == true)
                    {
                        sLabel = sStateValue.ToString();
                    }
                    if (string.IsNullOrEmpty(sLabel))
                        sLabel = goData.GetFieldLabel(gsTable, sField);
                    return sLabel;
                }
                else
                {
                    return "";
                }
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return "";
            }
        }
        public void SetControlVal(string sControlID, string sValue, bool bRemoveLinkToSelf = false)
        {
            //try
            //{
                string sKey = Strings.UCase(sControlID);

                if (string.IsNullOrEmpty(sValue))
                {
                    if (gcNDBFieldVals.Contains(sKey))
                    {
                        gcNDBFieldVals.Remove(sKey);
                    }
                    sKey = Strings.UCase(sControlID) + "_GID_ID";
                    if (gcNDBFieldVals.Contains(sKey))
                    {
                        gcNDBFieldVals.Remove(sKey);
                    }
                    sKey = Strings.UCase(sControlID) + "_SYS_NAME";
                    if (gcNDBFieldVals.Contains(sKey))
                    {
                        gcNDBFieldVals.Remove(sKey);
                    }
                    return;
                }

                sKey = Strings.UCase(sControlID);

                //else if there is an actual value
                if (goTR.ExtractString(sKey, 1, "_") == "NDB" & goTR.ExtractString(sKey, 2, "_") == "LNK" & goTR.ExtractString(sKey, 5, "_") == clC.EOT.ToString())
                {
                    string sName = null;
                    string sNewKey = null;
                    string[] aVals = Strings.Split(sValue, Constants.vbCrLf);
                    int i = 0;
                    string sIDs = "";
                    string sNames = "";
                    for (i = 0; i <= aVals.Length - 1; i++)
                    {
                        sValue = aVals[i];
                        if (!string.IsNullOrEmpty(sValue) & sValue != clC.EOT.ToString())
                        {
                            bool bAdd = true;
                            if (bRemoveLinkToSelf)
                            {
                                if (sValue == goP.GetUserTID())
                                    bAdd = false;
                            }
                            if (bAdd)
                            {
                                if (Strings.InStr(sIDs, sValue) == 0)
                                {
                                    clRowSet oRS = new clRowSet(goTR.GetFileFromSUID(sValue), 3, "GID_ID='" + sValue + "'", "", "SYS_Name");
                                    //MI 11/5/09 Added 'SYS_Name' to sFields
                                    if (oRS.GetFirst() == 1)
                                    {
                                        sName = oRS.GetFieldVal("SYS_NAME").ToString();
                                    }
                                    else
                                    {
                                        sName = "";
                                    }

                                    //10/19/10
                                    //wt changed the following so value does not contain vbcrlf if only a single link is stored
                                    switch (sIDs)
                                    {
                                        case "":
                                            sIDs = sValue;
                                            break;
                                        default:
                                            sIDs = sIDs + Constants.vbCrLf + sValue;
                                            break;
                                    }

                                    switch (sNames)
                                    {
                                        case "":
                                            sNames = sName;
                                            break;
                                        default:
                                            sNames = sNames + Constants.vbCrLf + sName;
                                            break;
                                    }

                                    //sIDs = sIDs & sValue & vbCrLf
                                    //sNames = sNames & sName & vbCrLf
                                }
                            }
                        }
                    }
                    sNewKey = sKey + "_GID_ID";
                    if (gcNDBFieldVals.Contains(sNewKey))
                    {
                        gcNDBFieldVals.Remove(sNewKey);
                        gcNDBFieldVals.Add(sIDs, sNewKey);
                    }
                    else
                    {
                        gcNDBFieldVals.Add(sIDs, sNewKey);
                    }
                    sNewKey = sKey + "_SYS_NAME";
                    if (gcNDBFieldVals.Contains(sNewKey))
                    {
                        gcNDBFieldVals.Remove(sNewKey);
                        gcNDBFieldVals.Add(sNames, sNewKey);
                    }
                    else
                    {
                        gcNDBFieldVals.Add(sNames, sNewKey);
                    }
                }
                else
                {
                    if (gcNDBFieldVals.Contains(sKey))
                    {
                        gcNDBFieldVals.Remove(sKey);
                        gcNDBFieldVals.Add(sValue, sKey);
                    }
                    else
                    {
                        gcNDBFieldVals.Add(sValue, sKey);
                    }
                }
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }
        public bool SaveEnabled()
        {
            try
            {
                switch (Strings.Left(gsType, 4))
                {
                    case "CRL_":
                    case "CRU_":
                        //check the add permission
                        if (!goData.GetAddPermission(gsTable))
                            return false;
                        break;
                    default:
                        //Modification mode
                        if (!goData.GetRecordPermission(goRowset.GetFieldVal("GID_ID").ToString(), "E"))
                            return false;
                        break;
                }
                return true;
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return false;
            }
        }
        public int GetControlLinkCount(string sControlID)
        {
            try
            {
                string sKey = Strings.UCase(sControlID) + "_GID_ID";
                string sValue = null;
                if (Strings.Left(sKey, 8) == "NDB_LNK_")
                {
                    if (gcNDBFieldVals.Contains(sKey))
                    {
                        sValue = gcNDBFieldVals[sKey].ToString();
                        if (Strings.InStr(sValue, Constants.vbCrLf) == 0)
                        {
                            if (!string.IsNullOrEmpty(sValue))
                            {
                                return 1;
                            }
                            else
                            {
                                return 0;
                            }
                        }
                        else
                        {
                            //11/15/10 removed "-1" since last release we removed the last string-ending hard return
                            return Strings.Split(sValue, Constants.vbCrLf).Length;
                            // - 1
                        }
                    }
                    else
                    {
                        return 0;
                    }
                }
                else
                {
                    return 0;
                }
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return 0;
            }
        }
        public void CancelSave()
        {
            //try
            //{
                gbCancelSave = true;
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }
        public void OpenForm()
        {
            //try
            //{
                clVar goVar = new clVar();
                goVar.SetVar(GUID, this);
                //HttpContext.Current.Response.Redirect(goUI.Navigate("FORMOBJECT", GUID));
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }
        public string GetLinkSelection(string sLinkName)
        {
            sLinkName = Strings.UCase(sLinkName);
            try
            {
                if (gcLinkSelection.Contains(sLinkName))
                {
                    return gcLinkSelection[sLinkName].ToString();
                }
                else
                {
                    return "";
                }
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return "";
            }
        }
        public void SetLinkSelection(string sLinkName, string sRecordID)
        {
            sLinkName = Strings.UCase(sLinkName);
            //try
            //{
                if (gcLinkSelection.Contains(sLinkName))
                {
                    gcLinkSelection.Remove(sLinkName);
                    gcLinkSelection.Add(sRecordID, sLinkName);
                }
                else
                {
                    gcLinkSelection.Add(sRecordID, sLinkName);
                }
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }

        public void SetLnk_Table_Col_State(string sLinkName, string sColName,int bHide=0,int breadOnly=0)
        {
            sLinkName = Strings.UCase(sLinkName) + "||" + Strings.UCase(sColName);

            string sState = "0";

            if(bHide == 1)
            {
                sState = "1"; //hide col
            }
            else if(breadOnly == 1)
            {
                sState = "2";//read-only
            }
          
            if (gcFld_Lnk_Table_Col_State.Contains(sLinkName))
            {
                gcFld_Lnk_Table_Col_State.Remove(sLinkName);
                gcFld_Lnk_Table_Col_State.Add(sState, sLinkName);
            }
            else
            {
                gcFld_Lnk_Table_Col_State.Add(sState, sLinkName);
            }
           
        }

        public void MessagePanel(string par_sMessage, string par_sBackgroundColor = "#FFFFB0", string par_sTextColor = "#000000", string par_sImage = "", bool par_bAppend = false)
        {
            //try
            //{
                gbMessagePanelDisplay = true;
                switch (par_bAppend)
                {
                    case false:
                        gsMessagePanelMessage = par_sMessage.Replace("'", "").Replace("\r\n", "");
                        break;
                    case true:
                        gsMessagePanelMessage = gsMessagePanelMessage.Replace("'", "").Replace("\r\n", "") + Constants.vbCrLf + Constants.vbCrLf + par_sMessage.Replace("'", "").Replace("\r\n", "");
                        break;
                }
                gsMessagePanelBackgroundColor = par_sBackgroundColor;
                gsMessagePanelTextColor = par_sTextColor;
                gsMessagePanelImage = par_sImage;
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }
        public void RefreshLinkNames(string sLinkname, bool bRemove = false)
        {
            //try
            //{
                sLinkname = Strings.UCase(sLinkname);
                switch (bRemove)
                {
                    case true:
                        if (gcRefreshLinks.Contains(sLinkname))
                        {
                            gcRefreshLinks.Remove(sLinkname);
                        }
                        break;
                    case false:
                        if (!gcRefreshLinks.Contains(sLinkname))
                        {
                            gcRefreshLinks.Add(sLinkname, sLinkname);
                        }
                        break;
                }

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }

        public bool IsLinkToRefresh(string sLinkname)
        {
            string sProc = "clForm::IsLinkToRefresh";
           // goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            try
            {
                sLinkname = sLinkname.ToUpper();
                if (gcRefreshLinks.Contains(sLinkname))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                //if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                //{
                //    goErr.SetError(ex, 45105, sProc);
                //}

                return false;
            }
        }
        public void ClearControlState()
        {
            //try
            //{
                gcControlState.Clear();
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }
        public void Delete()
        {
            clRowSet oRS = new clRowSet(gsTable, clC.SELL_EDIT, "GID_ID='" + gsRecordID + "'");
            int iResult = 0;
            if (oRS.GetFirst() == 1)
                iResult = oRS.DeleteRecord();
            if (iResult == 1)
                Util.SetSessionValue("IsRecordDeleted", true);
        }

        public bool IsFieldRequired(string sField)
        {
            if (gcFieldsRequired.Contains(Strings.UCase(sField)))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public void SetFilterINI(string sField, string sFilterINI)
        {
            //PURPOSE:
            //		sets a linkbox selectors current filter state
            //PARAMETERS:
            //		sField:                 field name 
            //		sFilterINI:             filter ini string
            //RETURNS:
            //		nothing
            //AUTHOR: WT
            string sProc = "clForm::SetFilterINI";

            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
                if (gcFilterINIs.Contains(sField))
                {
                    gcFilterINIs.Remove(sField);
                }
                gcFilterINIs.Add(sFilterINI, sField);
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}
        }

        public string GetFilterINI(string sField)
        {
            try
            {
                if (gcFilterINIs.Contains(sField))
                {
                    return gcFilterINIs[sField].ToString();
                }
                else
                {
                    return "";
                }
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    //goErr.SetError(ex, 45105, sProc);
                //}
                return "";
            }
        }
    }
    public class Fields
    {
        public string FieldName { get; set; }
        public string DBFieldName { get; set; }
        public string FieldValue
        {
            get;

            set;
        }
    }
    public class FormRefeshData
    {
        public MessageBox MessageBox { get; set; }
        public MessageBoxPanel MessageBoxPanel { get; set; }
        public IList<Fields> Fields { get; set; }
        public IList<Fields> RefreshLinkFields { get; set; }
        public IList<ControlState> ControlState { get; set; }
        public string OpenURL { get; set; }
        public string Title { get; set; }
        public List<cTabs> Tabs { get; set; }
        public string FieldInFocus { get; set; }
        public string FileName { get; set; }
        public string RecordId { get; set; }
        public string Type { get; set; }
        public string ErrorMessage { get; set; }
        public bool IsNavigate { get; set; }
        public string NavigateType { get; set; }

        public string PreviousUrlForSendOption { get; set; }
        public IFrameData _iFrameData { get; set; }
        public string TabInFocus { get; set; }
        public ExternalUrlItems ExternalUrlItems { get; set; }

        public string HistoryKey { get; set; }

        public bool IsNavigateToPrevious { get; set; }
        public bool ExecuteSendPopup { get; set; }

        public string DownloadFileData { get; set; }
        public string DetailsPageURL { get; set; }
        public string PreviewFileData { get; set; }
        public string SendFileData { get; set; }
        public string SendInclusionsData { get; set; }
    }

    public class ExternalUrlItems
    {
        public string sTitle { get; set; }
        public string sFilePath { get; set; }
        public string sParams { get; set; }
    }

    public class IFrameData
    {
        public string FieldName { get; set; }
        public string FilePath { get; set; }
    }
    public class cTabs
    {
        public int TabPosition { get; set; }
        public string TabName { get; set; }
        public int TabIndex { get; set; }
        public string TabIcon { get; set; }
    }
    public class FieldPropertiy
    {
        /// <summary>
        /// integer value of state.  following are the values allowed:
        //-1 Leave as is
        //0	Active	Active
        //1	Inactive	Visible, but locked—the user can’t click the control
        //2	Invisible	Invisible
        //4	Grayed	Grayed out
        /// </summary>
        public int State { get; set; }
        public string LabelText { get; set; }
        public string ToolTip { get; set; }
        public string Image { get; set; }
        public string LabelColor { get; set; }
        public string FontNames { get; set; }
        public string FontSize { get; set; }
    }
    public class ControlState
    {
        public string FieldName { get; set; }
        private FieldPropertiy fieldpropertiy;
        public FieldPropertiy FieldPropertiy
        {
            get
            {
                if (fieldpropertiy == null)
                    fieldpropertiy = new FieldPropertiy();
                return fieldpropertiy;
            }
            set
            {
                fieldpropertiy = value;
                if (value == null)
                    fieldpropertiy = value = new FieldPropertiy();
            }
        }
    }
    public class clRow
    {
        public int RowNo { get; set; }
        public IList<clColumn> Columns { get; set; }
    }
    public class clColumn
    {
        public int ColumnNo { get; set; }
        public int ColSpan { get; set; }
        public int RowSpan { get; set; }
        public string FieldName { get; set; }
    }

}

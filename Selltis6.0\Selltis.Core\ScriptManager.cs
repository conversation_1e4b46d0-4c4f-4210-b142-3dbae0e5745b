﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Reflection;
using System.Security.Policy;
using System.Configuration;

namespace Selltis.Core
{
    public class ScriptManager
    {
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clProject goP;
        private clLog goLog;
        private clError goErr;

        //Scripts _scripts = new Scripts();
        //ScriptManager __scriptManager = new ScriptManager();

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
        }
        public ScriptManager()
        {
            Initialize();
        }
        public IList<string> _GetFormScripts(string par_sFile)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //Return oScriptsCustom._GetFormScripts(par_sFile, oScripts._GetFormScripts(par_sFile))

            //AppDomain domain = null;
            
            IList<string> cScriptsForFile = new List<string>();
            //try
            //{
                string sMethodName = null;
                string sMethodNameNoPrePost = null;
                System.Type mType = default(System.Type);

                //Get main clScripts events
                mType = typeof(Scripts);
                foreach (System.Reflection.MethodInfo mMethod in mType.GetMethods(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.IgnoreCase))
                {
                    sMethodName = mMethod.Name;
                    sMethodNameNoPrePost = sMethodName;
                    if (sMethodName.StartsWith(par_sFile + "_FormTabOnClick", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                    else if (sMethodName.StartsWith(par_sFile + "_FormControlOnChange", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                    else if (sMethodName.StartsWith(par_sFile + "_FormControlOnEnter", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                    else if (sMethodName.StartsWith(par_sFile + "_FormControlOnLeave", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                }

                //Get custom script events

                //string cusPath = Util.GetCustomFilesPath();
                //cusPath = cusPath + "Selltis.Custom.dll";

                //string cusDLLName = Util.GetCustomDLLName();
                //string dllFolder = HttpContext.Current.Request.MapPath("/") + "bin\\";
                //dynamic dll = Assembly.LoadFile(dllFolder + cusDLLName);

                if (Util.GetSessionValue("CUSTOMDLLEXISTED") != null)
                {
                    if (Convert.ToBoolean(Util.GetSessionValue("CUSTOMDLLEXISTED")) == false)
                    {
                        return cScriptsForFile;
                    }
                }


                string cusDLLFilePath = Util.GetCustomDLL_FilePath();
                dynamic dll = Assembly.LoadFile(cusDLLFilePath);

                mType = dll.GetType("Selltis.Custom.ScriptsCustom");
                              

                //new code added to load the custom dll in a seperate app domain
                //AppDomainSetup domaininfo = new AppDomainSetup();
                //domaininfo.ApplicationBase = dllFolder;
                //Evidence adevidence = AppDomain.CurrentDomain.Evidence;
                //domain = AppDomain.CreateDomain("MyDomain", adevidence, domaininfo);

                //Type type = typeof(Proxy);
                //var value = (Proxy)domain.CreateInstanceAndUnwrap(
                //    type.Assembly.FullName,
                //    type.FullName);

                //var dll = value.GetAssembly(dllFolder + cusDLLName);
                //mType = dll.GetType("Selltis.Custom.ScriptsCustom");

                
                foreach (System.Reflection.MethodInfo mMethod in mType.GetMethods(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.IgnoreCase))
                {
                    sMethodName = mMethod.Name;
                    sMethodNameNoPrePost = sMethodName;
                    //Remove _Pre and _Post
                    if (Microsoft.VisualBasic.Strings.Right(sMethodNameNoPrePost.ToUpper(), 4) == "_PRE")
                        sMethodNameNoPrePost = goTR.FromTo(sMethodNameNoPrePost, 1, Microsoft.VisualBasic.Strings.Len(sMethodNameNoPrePost) - 4);
                    if (Microsoft.VisualBasic.Strings.Right(sMethodNameNoPrePost.ToUpper(), 5) == "_POST")
                        sMethodNameNoPrePost = goTR.FromTo(sMethodNameNoPrePost, 1, Microsoft.VisualBasic.Strings.Len(sMethodNameNoPrePost) - 5);
                    if (sMethodName.StartsWith(par_sFile + "_FormTabOnClick", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                    else if (sMethodName.StartsWith(par_sFile + "_FormControlOnChange", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                    else if (sMethodName.StartsWith(par_sFile + "_FormControlOnEnter", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                    else if (sMethodName.StartsWith(par_sFile + "_FormControlOnLeave", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                }

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        //goErr.SetError(ex, 45100, sProc);
            //    }     
            //    //if(domain != null)
            //    //{
            //    //    AppDomain.Unload(domain);
            //    //}
            //}
            //finally
            //{
            //    //if (domain != null)
            //    //{
            //    //    AppDomain.Unload(domain);
            //    //}
            //}

            return cScriptsForFile;

        }
        public IList<string> _GetViewScripts(string par_sFile)
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //Return oScriptsCustom._GetViewScripts(par_sFile, oScripts._GetViewScripts(par_sFile))

           // AppDomain domain = null;

            IList<string> cScriptsForFile = new List<string>();
            //try
            //{
                string sMethodName = null;
                string sMethodNameNoPrePost = null;
                System.Type mType = default(System.Type);

                //Get main clScripts events
                mType = typeof(Scripts);
                foreach (System.Reflection.MethodInfo mMethod in mType.GetMethods(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.IgnoreCase))
                {
                    sMethodName = mMethod.Name;
                    sMethodNameNoPrePost = sMethodName;
                    if (sMethodName.StartsWith(par_sFile + "_ViewControlOnChange_", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                    else if (sMethodName.StartsWith(par_sFile + "_ViewControlOnEnter_", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                    else if (sMethodName.StartsWith(par_sFile + "_ViewControlOnLeave_", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                }

                //Get custom script events
                //string cusPath = Util.GetCustomFilesPath();
                //cusPath = cusPath + "Selltis.Custom.dll";

                //dynamic dll = Assembly.LoadFile(cusPath);
                //mType = dll.GetType("Selltis.Custom.ScriptsCustom");

                //string cusDLLName = Util.GetCustomDLLName();
                //string dllFolder = HttpContext.Current.Request.MapPath("/") + "bin\\";

                ////new code added to load the custom dll in a seperate app domain
                //AppDomainSetup domaininfo = new AppDomainSetup();
                //domaininfo.ApplicationBase = dllFolder; //HttpContext.Current.Request.MapPath("/") + "bin\\";
                //Evidence adevidence = AppDomain.CurrentDomain.Evidence;
                //domain = AppDomain.CreateDomain("MyDomain", adevidence, domaininfo);

                //Type type = typeof(Proxy);
                //var value = (Proxy)domain.CreateInstanceAndUnwrap(
                //    type.Assembly.FullName,
                //    type.FullName);

                //var dll = value.GetAssembly(dllFolder + cusDLLName);
                //mType = dll.GetType("Selltis.Custom.ScriptsCustom");

                //string cusDLLName = Util.GetCustomDLLName();
                //string dllFolder = HttpContext.Current.Request.MapPath("/") + "bin\\";
                //dynamic dll = Assembly.LoadFile(dllFolder + cusDLLName);

                if (!(Util.GetSessionValue("CUSTOMDLLEXISTED") == null))
                {
                    if (Convert.ToBoolean(Util.GetSessionValue("CUSTOMDLLEXISTED")) == false)
                    {
                        return cScriptsForFile;
                    }
                }



                string cusDLLFilePath = Util.GetCustomDLL_FilePath();
                dynamic dll = Assembly.LoadFile(cusDLLFilePath);

                mType = dll.GetType("Selltis.Custom.ScriptsCustom");
                

                foreach (System.Reflection.MethodInfo mMethod in mType.GetMethods(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.IgnoreCase))
                {
                    sMethodName = mMethod.Name;
                    sMethodNameNoPrePost = sMethodName;
                    //Remove _Pre and _Post
                    if (Microsoft.VisualBasic.Strings.Right(sMethodNameNoPrePost.ToUpper(), 4) == "_PRE")
                        sMethodNameNoPrePost = goTR.FromTo(sMethodNameNoPrePost, 1, Microsoft.VisualBasic.Strings.Len(sMethodNameNoPrePost) - 4);
                    if (Microsoft.VisualBasic.Strings.Right(sMethodNameNoPrePost.ToUpper(), 5) == "_POST")
                        sMethodNameNoPrePost = goTR.FromTo(sMethodNameNoPrePost, 1, Microsoft.VisualBasic.Strings.Len(sMethodNameNoPrePost) - 5);
                    if (sMethodName.StartsWith(par_sFile + "_ViewControlOnChange_", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                    else if (sMethodName.StartsWith(par_sFile + "_ViewControlOnEnter_", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                    else if (sMethodName.StartsWith(par_sFile + "_ViewControlOnLeave_", StringComparison.CurrentCultureIgnoreCase))
                    {
                        cScriptsForFile.Add(sMethodNameNoPrePost);
                    }
                }

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45100, sProc);
            //    }
            //    //if (domain != null)
            //    //{
            //    //    AppDomain.Unload(domain);
            //    //}
            //}
            //finally
            //{
            //    //if (domain != null)
            //    //{
            //    //    AppDomain.Unload(domain);
            //    //}
            //}

            return cScriptsForFile;

        }

        public bool AddScript(object par_oScript, string par_sName, string par_sSource)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Compile(string par_sProcName, string par_sSource, string par_sName = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            return (true);
        }
        public bool Execute(object par_oScript, object par_oParent, object par_oInfomess = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            return true;
        }
        public string GetFriendlyName(long par_lScriptNumber)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return "";
        }
        public bool IsSectionEnabled(string par_sProc, string par_sSections, string par_sSectionName, bool par_bRunByDefault = true)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start: '" + par_sProc + "' section '" + par_sSectionName + "'.", clC.SELL_LOGLEVEL_DEBUG, true);

            string sDefault = null;
            bool bResult = false;

            //try
            //{
                if (par_bRunByDefault)
                {
                    sDefault = "1";
                }
                else
                {
                    sDefault = "0";
                }

                if (goTR.StrRead(par_sSections, par_sSectionName, sDefault, false) == "1")
                {
                    bResult = true;
                }
                else
                {
                    bResult = false;
                }
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}

            return bResult;

        }
        public bool RemoveScript(object par_oScript)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool RunCustomScript(string par_sScriptName, ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, object par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
           // AppDomain domain = null;

            //try
            //{
                string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
                ////goLog.Log(sProc, "Start: '" + par_sScriptName + "'", clC.SELL_LOGLEVEL_DEBUG, true);                       

                bool bResult = false;
                System.Type mType; //= typeof(ScriptsCustom);

                //dynamic dll = Assembly.LoadFile(HttpContext.Current.Request.MapPath("/") + "//bin" + "//Selltis.Core.dll");

                //string cusPath = Util.GetCustomFilesPath();
                //cusPath = cusPath + "Selltis.Custom.dll";

                //dynamic dll = Assembly.LoadFile(cusPath);
                //mType = dll.GetType("Selltis.Custom.ScriptsCustom");

                //string cusDLLName = Util.GetCustomDLLName();
                //string dllFolder = HttpContext.Current.Request.MapPath("/") + "bin\\";

                //AppDomainSetup domaininfo = new AppDomainSetup();
                //domaininfo.ApplicationBase = dllFolder; //HttpContext.Current.Request.MapPath("/") + "bin\\";
                //Evidence adevidence = AppDomain.CurrentDomain.Evidence;
                //domain = AppDomain.CreateDomain("MyDomain", adevidence, domaininfo);

                //Type type = typeof(Proxy);
                //var value = (Proxy)domain.CreateInstanceAndUnwrap(
                //    type.Assembly.FullName,
                //    type.FullName);

                //var dll = value.GetAssembly(dllFolder + cusDLLName);
                //mType = dll.GetType("Selltis.Custom.ScriptsCustom");

                //string cusDLLName = Util.GetCustomDLLName();
                //string dllFolder = HttpContext.Current.Request.MapPath("/") + "bin\\";
                //dynamic dll = Assembly.LoadFile(dllFolder + cusDLLName);


                if (Util.GetSessionValue("CUSTOMDLLEXISTED") != null)
                {
                    if (Convert.ToBoolean(Util.GetSessionValue("CUSTOMDLLEXISTED")) == false)
                    {
                        return true;
                    }
                }

                string cusDLLFilePath = Util.GetCustomDLL_FilePath();
                dynamic dll;
                
                string sHostingEnvironment = ConfigurationManager.AppSettings["HostingEnvironment"].ToString().ToLower();
                if (sHostingEnvironment == "debugging")
                {
                    dll = Assembly.LoadFrom(cusDLLFilePath);
                }
                else
                {
                    dll = Assembly.LoadFile(cusDLLFilePath);
                }

                mType = dll.GetType("Selltis.Custom.ScriptsCustom");

                System.Reflection.MethodInfo mInfo = mType.GetMethod(par_sScriptName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.IgnoreCase);
                if ((mInfo != null))
                {
                    //object[] oParamArray = {
                    //                            par_doCallingObject,
                    //                            par_doArray,
                    //                            par_s1,
                    //                            par_s2,
                    //                            par_s3,
                    //                            par_s4,
                    //                            par_s5,
                    //                            par_oReturn,
                    //                            par_bRunNext,
                    //                            par_sSections
                    //                        };
                    object[] oParamArray = {
			                                par_doCallingObject,
                                            par_oReturn,
			                                par_bRunNext,
			                                par_sSections,
			                                par_doArray,
			                                par_s1,
			                                par_s2,
			                                par_s3,
			                                par_s4,
			                                par_s5,
			                                
		                               };
                    if (par_bRunNext)
                    {
                        //goErr.SetError();
                        //MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.

                        // ScriptsCustom _scriptsCustom = new ScriptsCustom();
                        var oClass = Activator.CreateInstance(mType);
                        bResult = Convert.ToBoolean(mInfo.Invoke(oClass, oParamArray));  //need to add customscript 


                        //Set all ByRef variables in case the called custom script modified them
                        par_doCallingObject = oParamArray[0];
                        par_oReturn = oParamArray[1];
                        par_bRunNext = Convert.ToBoolean(oParamArray[2]);
                        par_sSections = oParamArray[3].ToString();
                    }
                }
                else
                {
                    bResult = true;
                }

                return bResult;

            //}
            //catch (Exception)
            //{
            //    //if (domain != null)
            //    //{
            //    //    AppDomain.Unload(domain);
            //    //}

            //    throw;
            //}
            //finally
            //{
            //    //if (domain != null)
            //    //{
            //    //    AppDomain.Unload(domain);
            //    //}

            //}

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="par_sScriptName">Method Name</param>
        /// <param name="par_doCallingObject">Form or Object</param>
        /// <param name="par_oReturn">Defualt Null</param>
        /// <param name="par_bRunNext">Defualt True</param>
        /// <param name="par_sSections">Defualt Empty or Null string</param>
        /// <param name="par_doArray">Defualt Null</param>
        /// <param name="par_s1"></param>
        /// <param name="par_s2"></param>
        /// <param name="par_s3"></param>
        /// <param name="par_s4"></param>
        /// <param name="par_s5"></param>
        /// <returns></returns>
        public bool RunScript(string par_sScriptName, ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, object par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start: '" + par_sScriptName + "'.", clC.SELL_LOGLEVEL_DEBUG, true);

            bool bResult = false;
            //Dim bRunNext As Boolean = True  'par_bRunNext  
            System.Type mType = typeof(Scripts);
            System.Reflection.MethodInfo mInfo = mType.GetMethod(par_sScriptName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.IgnoreCase);
            if ((mInfo != null))
            {
                //Method found in clScripts
                //Pre script: if not found returns True and bRunNext remains True
                bResult = RunCustomScript(par_sScriptName + "_Pre", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5);
                //Main script - runs only if the pre script isn't overriding it (bRunNext = True) and didn't return false
                object[] oParamArray = {
			                                par_doCallingObject,
                                            par_oReturn,
			                                par_bRunNext,
			                                par_sSections,
			                                par_doArray,
			                                par_s1,
			                                par_s2,
			                                par_s3,
			                                par_s4,
			                                par_s5,
			                                
		                               };
                if (par_bRunNext & bResult)
                {
                    //goErr.SetError();
                    //MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
                    //Scripts _scripts = new Scripts();
                    var oClass = Activator.CreateInstance(mType);
                    bResult = Convert.ToBoolean(mInfo.Invoke(oClass, oParamArray));
                    par_bRunNext = Convert.ToBoolean(oParamArray[2]);
                    //bRunNext
                }
                //Post script - run only if the main script didn't return false and the _Pre script didn't set bRunNext to false
                if (par_bRunNext & bResult)
                {
                    //string oparamarray = oParamArray[0].ToString();
                    //string oparamarray1 = oParamArray[1].ToString();
                    string oparamarray3 = oParamArray[3].ToString();
                    bool oparamarray2 = Convert.ToBoolean(oParamArray[2]);
                    //string oparamarray4 = oParamArray[4].ToString();
                    //string oparamarray5 = oParamArray[5].ToString();
                    //string oparamarray6 = oParamArray[6].ToString();
                    //string oparamarray7 = oParamArray[7].ToString();
                    //string oparamarray8 = oParamArray[8].ToString();                    
                    //string oparamarray9 = oParamArray[9].ToString();

                    bResult = RunCustomScript(par_sScriptName + "_Post", ref oParamArray[0], ref oParamArray[1], ref  oparamarray2, ref  oparamarray3, oParamArray[4], oParamArray[5].ToString(), oParamArray[6].ToString(), oParamArray[7].ToString(), oParamArray[8].ToString(), oParamArray[9].ToString());
                    //Set all ByRef variables in case the called custom script modified them
                    par_doCallingObject = oParamArray[0];
                    par_oReturn = oParamArray[1];
                    par_bRunNext = Convert.ToBoolean(oparamarray2);
                    par_sSections = oparamarray3;
                }
            }
            else
            {
                //Method not found in clScripts, check whether we have a custom '_PRE', main, or '_POST' script
                bResult = RunCustomScript(par_sScriptName + "_Pre", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5);
                if (par_bRunNext & bResult)
                    bResult = RunCustomScript(par_sScriptName, ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5);
                if (par_bRunNext & bResult)
                    bResult = RunCustomScript(par_sScriptName + "_Post", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5);
            }

            //if (bResult)
            //    goErr.SetError();

            return bResult;

        }


        public void ScriptError(string Message)
        {
            //Management of errors occurring during script management
            string proc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(proc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //Here we can add information in sMessage if necessary
            goLog.LogError(Message);
        }
        public string Validate(string Source)
        {
            string proc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(proc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return "";
        }
        public bool VerifySyntax(string Code, string ScriptName)
        {
            string proc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(proc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }





    }

    public class Proxy : MarshalByRefObject
    {
        public Assembly GetAssembly(string assemblyPath)
        {
            try
            {
                return Assembly.LoadFile(assemblyPath);
            }
            catch (Exception)
            {
                return null;
                // throw new InvalidOperationException(ex);
            }
        }
    }

}
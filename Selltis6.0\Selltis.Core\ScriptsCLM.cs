﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.IO;
using System.Globalization;
using System.Net;
using System.Web.Script.Serialization;
using System.Xml;
using System.Text.RegularExpressions;
using System.Diagnostics;

namespace Selltis.Core
{
    public partial class Scripts
    {

        //private clMetaData goMeta;
        //private clTransform goTR;
        //private clData goData;
        //private clProject goP;
        //private clLog goLog;
        //private clError goErr;
        //private clPerm goPerm;
        //private ClUI goUI;
        //ScriptManager scriptManager = new ScriptManager();

        int par_iValid = 4;
        string par_sDelim = " ";
        //object par_oReturn = null;
        //bool par_bRunNext = false;
        //string par_sSections = "";

        string sDelim = "";

        System.Data.SqlClient.SqlConnection par_oConnection = null;
        DataTable oTable = null;
        string sError;

        //public void Initialize()
        //{
        //    goMeta = (clMetaData)Util.GetInstance("meta");
        //    goTR = (clTransform)Util.GetInstance("tr");
        //    goData = (clData)Util.GetInstance("data");
        //    goP = (clProject)Util.GetInstance("p");
        //    goErr = (clError)Util.GetInstance("err");
        //    goLog = (clLog)Util.GetInstance("log");
        //    goUI = new ClUI();
        //}




        //Constant Contacts partial script.cs methods..J

        //BTN_CreateLinkedEmailDrop
        public bool MC_ViewControlOnChange_BTN_CreateLinkedEmailDrop(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Desktop oDesktop = (Desktop)par_doCallingObject;
            string sID = ""; string sFile = "";

            string Key = "";
            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
            {
                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
            }

            //string sID = goUI.GetLastSelected("SELECTEDRECORDID");
            //string sFile = goUI.GetLastSelected("SELECTEDRECORDFILE");
            if (Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
                sID = Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();
            else
                sID = "";

            sFile = goTR.GetFileFromSUID(sID);  //used in 6.0
            string sView = oDesktop.GetViewMetadata("VIE_5B30D1A0-7B85-4C8A-5858-A4BA00A86D76");
            string sCompanyName = "";
            string sFilter = "";

            Form Doform = new Form("CS", "", "CRU_", goData.GenGuid(), "VIE_5B30D1A0-7B85-4C8A-5858-A4BA00A86D76"); ////used in 6.0
            //Form Doform = new Form("CS", "", "CRU_", oDesktop.GUID, "VIE_5B30D1A0-7B85-4C8A-5858-A4BA00A86D76");
            Form Dofrom1 = new Form(sFile, sID, "");
            Doform.doRS.SetFieldVal("LNK_Related_GR", Dofrom1.doRS.GetFieldVal("LNK_Related_GR"));
            Doform.doRS.SetFieldVal("LNK_Related_MC", sID);

            //Doform.OpenForm();
            goUI.Queue("FORM", Doform);
            return true;

        }
        public bool MC_ViewControlOnChange_BTN_CreateLinkedEmailJob(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Desktop oDesktop = (Desktop)par_doCallingObject;
            string sID = ""; string sFile = "";

            string Key = "";
            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
            {
                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
            }

            //string sID = goUI.GetLastSelected("SELECTEDRECORDID");
            //string sFile = goUI.GetLastSelected("SELECTEDRECORDFILE");
            if (Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
                sID = Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();
            else
                sID = "";

            sFile = goTR.GetFileFromSUID(sID);
            string sView = oDesktop.GetViewMetadata("VIE_5B30D1A0-7B85-4C8A-5858-A4BA00A86D76");
            string sCompanyName = "";
            string sFilter = "";

            Form Doform = new Form("CS", "", "CRU_", goData.GenGuid(), "VIE_5B30D1A0-7B85-4C8A-5858-A4BA00A86D76");
            //Form Doform = new Form("CS", "", "CRU_", oDesktop.GUID, "VIE_5B30D1A0-7B85-4C8A-5858-A4BA00A86D76");
            Form Dofrom1 = new Form(sFile, sID, "");
            Doform.doRS.SetFieldVal("LNK_Related_GR", Dofrom1.doRS.GetFieldVal("LNK_Related_GR"));
            Doform.doRS.SetFieldVal("LNK_Related_MC", sID);

            Doform.OpenForm();

            return true;

        }

        public bool CS_FormOnLoadRecord(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //For notes on how to create a custom script, see clScrMng. ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.SetControlState("TXT_CCCAMPAIGNID", 4);         //Grayed Out
            doForm.SetControlState("TXT_CCLISTID", 4);            //Grayed Out

            doForm.SetControlState("NDB_TXT_CampaignName", 2);            //Invisible
            doForm.SetControlState("MLS_Status", 4);            //Grayed Out
            doForm.SetControlState("NDB_TXT_LISTNAME", 4);            //Grayed Out

            doForm.SetControlState("NDB_TXT_LISTNAME", 4);            //Grayed Out

            doForm.SetControlState("TXT_VALIDATESTATUS", 2);            //Invisible
            doForm.SetControlState("TXT_LISTCREATEDSTATUS", 2);            //Invisible
            doForm.SetControlState("TXT_CCCREATED", 2);            //Invisible

            doForm.SetControlState("NDB_LBL_VALIDATESTAUS", 2);            //Invisible
            doForm.SetControlState("NDB_lbl_ListStatus", 2);            //Invisible
            doForm.SetControlState("NDB_lbl_CCCreatedStatus", 2);            //Invisible
            doForm.SetControlState("TXT_CCScheduleID", 2);            //Invisible

            doForm.SetControlState("BI__Sends", 4);            //Grayed Out
            doForm.SetControlState("BI__Opens", 4);            //Grayed Out
            doForm.SetControlState("BI__clicks", 4);            //Grayed Out
            doForm.SetControlState("BI__forwards", 4);            //Grayed Out
            doForm.SetControlState("BI__unsubscribes", 4);            //Grayed Out
            doForm.SetControlState("BI__bounces", 4);            //Grayed Out
            doForm.SetControlState("BI__spamcount", 4);            //Grayed Out
            doForm.SetControlState("BTN_CCUnschedule", 4);
            doForm.SetControlState("SR__Openrate", 4);            //Grayed Out
            doForm.SetControlState("SR__Bouncerate", 4);            //Grayed Out
            doForm.SetControlState("SR__Clickrate", 4);            //Grayed Out
            doForm.SetControlState("SR__Forwardrate", 4);            //Grayed Out
            doForm.SetControlState("SR__Unsubscriberate", 4);            //Grayed Out

            //If doForm.doRS.GetFieldVal("TXT_VALIDATESTATUS").ToString().ToLower().Equals("validated") Then
            //    doForm.SetControlState("NDB_LBL_VALIDATESTAUS", 1)   'visible            
            //End If

            if (doForm.doRS.GetFieldVal("TXT_LISTCREATEDSTATUS").ToString().ToLower().Equals("list created"))
            {
                doForm.SetControlState("NDB_lbl_ListStatus", 1);                //visible  
                doForm.SetControlState("BTN_CCNEWLIST_ADD", 4);                //Grayed
            }

            if (doForm.doRS.GetFieldVal("TXT_CCCREATED").ToString().ToLower().Equals("constant contact email draft created"))
            {
                doForm.SetControlState("NDB_lbl_CCCreatedStatus", 1);                //visible     
            }

            doForm.SetControlState("BTN_Copy", 2);            //Invisible  
            //Check Status
            string sCampaignID = doForm.doRS.GetFieldVal("TXT_CCCampaignID").ToString();
            string sStatus = "";
            if (!string.IsNullOrEmpty(sCampaignID))
            {
                //VNK 08272015 Check the Campaign exists in CC
                if (ConstCont_Campaign_IsExisted(sCampaignID, doForm))
                {
                    sStatus = ConstCont_Campaign_GetStatus(sCampaignID, doForm);
                    if (sStatus.ToLower().Equals("scheduled") | sStatus.ToLower().Equals("sent"))
                    {
                        doForm.SetControlState("BTN_SAVE", 4);
                        doForm.SetControlState("BTN_SAVEANDLEAVEOPEN", 4);
                        doForm.SetControlState("BTN_CCNEWLIST_ADD", 4);                        //Grayed  
                        doForm.SetControlState("BTN_CCNEWCAMPAIGN_ADD", 4);                        //Grayed  
                        doForm.SetControlState("BTN_VALIDATECONTACTS", 4);                        //Grayed  
                        doForm.SetControlState("BTN_Copy", 0);                        //Visible  
                        doForm.SetControlState("LNK_RELATED_MC", 5);                        //Grayed  
                        doForm.SetControlState("LNK_RELATED_GR", 5);                        //Grayed  
                        if (sStatus.ToLower().Equals("scheduled"))
                        {
                            doForm.SetControlState("BTN_CCUnschedule", 0);
                        }
                        doForm.MessagePanel("This email drop has already been " + sStatus);
                    }
                    doForm.doRS.SetFieldVal("MLS_STATUS", sStatus);
                    doForm.Save(3);
                }
                else
                {
                    doForm.MessagePanel("This email drop has been removed from constant contact ");
                }
            }
            //MMO_TEXTCONTENT
            //doForm.SetControlState("MMO_TEXTCONTENT", 2)   'Invisible
            //doForm.SetControlState("NDB_TXT_Subject", 2)   'Invisible
            //doForm.SetControlState("NDB_TXT_FromName", 2)   'Invisible
            //doForm.SetControlState("NDB_TXT_FromEmailAddress", 2)   'Invisible
            //doForm.SetControlState("NDB_TXT_ReplyEmailAddress", 2)   'Invisible
            //doForm.SetControlState("BTN_CCNewCampaign_Add", 2)   'Invisible

            //doForm.SetControlState("NDB_TXT_ListName", 2)   'Invisible
            //doForm.SetControlState("BTN_CCNewList_Add", 2)   'Invisible

            return true;

        }
        public bool MC_FormOnLoadRecord(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //For notes on how to create a custom script, see clScrMng. ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            doForm.SetControlState("BI__Sends", 4);            //Grayed Out
            doForm.SetControlState("BI__Opens", 4);            //Grayed Out
            doForm.SetControlState("BI__clicks", 4);            //Grayed Out
            doForm.SetControlState("BI__forwards", 4);            //Grayed Out
            doForm.SetControlState("BI__unsubscribes", 4);            //Grayed Out
            doForm.SetControlState("BI__bounces", 4);            //Grayed Out
            doForm.SetControlState("BI__spamcount", 4);            //Grayed Out
            doForm.SetControlState("BTN_CCUnschedule", 4);

            doForm.SetControlState("SR__Openrate", 4);            //Grayed Out
            doForm.SetControlState("SR__Bouncerate", 4);            //Grayed Out
            doForm.SetControlState("SR__Clickrate", 4);            //Grayed Out
            doForm.SetControlState("SR__Forwardrate", 4);            //Grayed Out
            doForm.SetControlState("SR__Unsubscriberate", 4);            //Grayed Out

            return true;
        }

        public bool CS_FormControlOnChange_BTN_COPY(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form OdoForm = (Form)par_doCallingObject;
            Form Doform = new Form("CS", "", "CRU_", "", "VIE_5B30D1A0-7B85-4C8A-5858-A4BA00A86D76");
            Doform.doRS.SetFieldVal("LNK_Related_MC", OdoForm.doRS.GetFieldVal("LNK_Related_MC"));
            Doform.doRS.SetFieldVal("LNK_Related_GR", OdoForm.doRS.GetFieldVal("LNK_Related_GR"));
            Doform.doRS.SetFieldVal("LNK_CONNECTED_CN", OdoForm.doRS.GetFieldVal("LNK_CONNECTED_CN"));
            Doform.doRS.SetFieldVal("MMR_EMAILCONTENT", OdoForm.doRS.GetFieldVal("MMR_EMAILCONTENT"));
            OdoForm.Save(2);
            Doform.OpenForm();

            return true;
        }
        public bool CS_FormControlOnChange_BTN_SENDTESTEMAIL(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_CCCAMPAIGNID").ToString()))
            {
                doForm.MessageBox("Please save the campaign before sending a test Email");
                par_doCallingObject = doForm;
                return false;
            }

            doForm.MessageBox("Enter up to 5 email addresses separated by a comma \",\"", clC.SELL_MB_INPUTBOX, "Email Address(es)", "OK", "CANCEL", "", "", "MESSAGEBOXEVENT", "MESSAGEBOXEVENT", "", doForm, null, "OK", "CANCEL", "", "", "CS_FORMCONTROLONCHANGE_BTN_SENDTESTEMAIL_EMAILADDRESSES");
            par_doCallingObject = doForm;
            return true;
        }

        private bool CS_SENDTESTEMAIL(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sPersonalNote = par_s3;
            string[] sEmails = doForm.oVar.GetVar("SENDTESTEMAIL_EMAILADDRESSES").ToString().Split(',');
            string sCleanEmailList = "";
            string sCampaignID = doForm.doRS.GetFieldVal("TXT_CCCampaignID").ToString();
            string sHTMLTEXT = "";
            switch (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_HTMLTEXTFORMAT", 2).ToString()))
            {
                case 0:
                    sHTMLTEXT = "HTML";
                    break;
                case 1:
                    sHTMLTEXT = "TEXT";
                    break;
                default:
                    sHTMLTEXT = "HTML_AND_TEXT";
                    break;
            }

            foreach (string Str in sEmails)
            {
                if (ValidateEmail(Str) == true)
                {
                    if (string.IsNullOrEmpty(sCleanEmailList))
                    {
                        sCleanEmailList = "" + Str.Trim() + "";
                    }
                    else
                    {
                        sCleanEmailList = "" + Str.Trim() + "," + sCleanEmailList;
                    }
                }
            }

            if (string.IsNullOrEmpty(sCampaignID))
            {
                doForm.MessageBox("Please save the campaign before sending a test EMail");
                par_doCallingObject = doForm;
                return false;
            }
            if (string.IsNullOrEmpty(sCleanEmailList))
            {
                doForm.MessageBox("Please enter valid EMail addresses");
                par_doCallingObject = doForm;
                return false;
            }

            ConstCont_TestEMail_Send(sCampaignID, sHTMLTEXT, sCleanEmailList, sPersonalNote, doForm);
            doForm.MessageBox("Test Email sent Successfully");
            par_doCallingObject = doForm;
            par_bRunNext = false;
            return true;
        }

        #region "Create Constant Contact Campaign"
        public bool CS_FormControlOnChange_BTN_CCNEWCAMPAIGN_ADD(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sNewCampaignURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns?api_key=ma4jgf2zu926c3ggw5dq9hpm";
            string sExistingCampaignURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns?status=ALL&api_key=ma4jgf2zu926c3ggw5dq9hpm";
            StringBuilder sData = new StringBuilder();
            string sEmailContent = doForm.doRS.GetFieldVal("MMR_EmailContent").ToString();
            string sTextContent = doForm.doRS.GetFieldVal("MMP_EmailContent").ToString();
            string sCCName = doForm.doRS.GetFieldVal("LNK_RELATED_MC%%SYS_NAME%%").ToString();
            //doForm.doRS.GetFieldVal("LNK_RELATED_MC%%SYSNAME")
            string sCampaignName = sCCName + doForm.doRS.GetFieldVal("TXT_CONSTANTCONTACTCAMPAIGNNAME") + DateTime.Now.ToString("MMddyyy HHmmm");
            //doForm.GetControlVal("NDB_TXT_CampaignName")
            sEmailContent = sEmailContent.Replace("‘", "");
            sTextContent = sTextContent.Replace("‘", "");

            //VR, Restore links in text content
            Regex r = new Regex("<a.*?href=(\"|')(?<href>.*?)(\"|').*?>(?<value>.*?)</a>");
            foreach (Match match in r.Matches(sEmailContent))
            {
                string _href = match.Groups["href"].Value;
                string _value = match.Groups["value"].Value;
                _value = _value.Replace("&rsquo;", string.Empty);
                //try
                //{
                //    System.Xml.XmlDocument _xValue = new System.Xml.XmlDocument();
                //    _xValue.LoadXml(_value);
                //    _value = _xValue.DocumentElement.InnerText;

                //}
                //catch (Exception ex)
                //{
                //}
                int _Index = sTextContent.IndexOf(_value);
                int _strlength = _value.Length;
                //sTextContent = Regex.Replace(sTextContent, "\b" & _value & "\b", _value & "(" & _href & ")")
                //sTextContent = sTextContent.Replace(_value, _href)
                if (_Index > -1)
                {
                    StringBuilder aStringBuilder = new StringBuilder(sTextContent);
                    aStringBuilder.Remove(_Index, _strlength);
                    aStringBuilder.Insert(_Index, _value + "(" + _href + ")");
                    sTextContent = aStringBuilder.ToString();
                }
            }

            //sEmailContent = sEmailContent.Replace(vbCrLf, "")

            if (string.IsNullOrEmpty(sCCName))
            {
                doForm.MessageBox("Please select a Campaign");
                par_doCallingObject = doForm;
                return false;
            }

            if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_CCListID").ToString()))
            {
                doForm.MessageBox("Please create list and upload Contacts before creating an Email Draft.");
                par_doCallingObject = doForm;
                return false;
            }

            if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_SUBJECT").ToString()))
            {
                doForm.MessageBox("Subject should not be empty.");
                par_doCallingObject = doForm;
                return false;
            }

            if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_FROMNAME").ToString()))
            {
                doForm.MessageBox("From Name should not be empty.");
                par_doCallingObject = doForm;
                return false;
            }

            if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_FROMEMAILADDRESS").ToString()))
            {
                doForm.MessageBox("From email address should not be empty.");
                par_doCallingObject = doForm;
                return false;
            }

            if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_REPLYEMAILADDRESS").ToString()))
            {
                doForm.MessageBox("Reply email address should not be empty.");
                par_doCallingObject = doForm;
                return false;
            }
            if (!CheckVerifiedEmail(doForm.doRS.GetFieldVal("TXT_REPLYEMAILADDRESS").ToString().ToLower(), doForm))
            {
                doForm.MessageBox("Please enter a valid Constant Contact Reply Email address for this campaign type.");
                par_doCallingObject = doForm;
                return false;
            }

            if (!CheckVerifiedEmail(doForm.doRS.GetFieldVal("TXT_FROMEMAILADDRESS").ToString().ToLower(), doForm))
            {
                doForm.MessageBox("Please enter a valid Constant Contact From Email address for this campaign type..");
                par_doCallingObject = doForm;
                return false;
            }
            //If Not (doForm.GetControlVal("NDB_TXT_REPLYEMAILADDRESS").ToString().ToLower().Equals("<EMAIL>") Or doForm.GetControlVal("NDB_TXT_REPLYEMAILADDRESS").ToString().ToLower().Equals("<EMAIL>")) Then
            //    doForm.MessageBox("Reply email address <NAME_EMAIL> (or) <EMAIL>.")
            //    Return False
            //End If

            //If Not (doForm.GetControlVal("NDB_TXT_FROMEMAILADDRESS").ToString().ToLower().Equals("<EMAIL>") Or doForm.GetControlVal("NDB_TXT_FROMEMAILADDRESS").ToString().ToLower().Equals("<EMAIL>")) Then
            //    doForm.MessageBox("From email address <NAME_EMAIL> (or) <EMAIL>.")
            //    Return False
            //End If

            if (string.IsNullOrEmpty(sEmailContent))
            {
                doForm.MessageBox("email content should not be empty.");
                par_doCallingObject = doForm;
            }

            //Check id Campaign name already exists
            //If ConstCont_Campaign_CheckExistance(doForm.GetControlVal("NDB_TXT_CampaignName")) = True Then
            if (ConstCont_Campaign_CheckExistance(sCampaignName, doForm) == true)
            {
                doForm.MessageBox("Campaign with same name already exists.");
                par_doCallingObject = doForm;
                return false;
            }

            //Save the Form before continuing
            if (doForm.Save(3) == 0)
            {
                return false;
            }


            string sListID = doForm.doRS.GetFieldVal("TXT_CCListID").ToString();
            string sSubject = doForm.doRS.GetFieldVal("TXT_Subject").ToString();
            string sFromName = doForm.doRS.GetFieldVal("TXT_FromName").ToString();
            string sFromEmailAddress = doForm.doRS.GetFieldVal("TXT_FromEmailAddress").ToString();
            string sReplyEmailAddress = doForm.doRS.GetFieldVal("TXT_ReplyEmailAddress").ToString();


            string sCampID = ConstCont_Campaign_Create(sListID, sCampaignName, sSubject, sFromName, sFromEmailAddress, sReplyEmailAddress, sEmailContent, sTextContent, doForm);
            doForm.doRS.SetFieldVal("TXT_CCCampaignID", sCampID);

            if (!string.IsNullOrEmpty(sCampID))
            {
                doForm.SetControlState("NDB_lbl_CCCreatedStatus", 1);
                //visible   
                doForm.SetControlVal("NDB_lbl_CCCreatedStatus", "Constant Contact Email Draft created");
                doForm.doRS.SetFieldVal("TXT_CCCREATED", "Constant Contact Email Draft created");
                doForm.doRS.SetFieldVal("MLS_STATUS", "DRAFT");
                if (doForm.Save(3) == 0)
                {
                    return false;
                }
            }
            else
            {
                //do not clear values/data from Email Content and Template when it is failed
                //doForm.doRS.SetFieldVal("MLS_TEMPLATE", "<Make Selection>");
                //doForm.doRS.SetFieldVal("MMR_EmailContent", string.Empty);

                //If doForm.Save(3) = 0 Then
                //    Return False
                //End If
            }

            par_doCallingObject = doForm;
            return true;
        }
        #endregion



        public bool CS_FormControlOnChange_BTN_CCNEWLIST_ADD(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //doForm.SetControlState("BTN_CCNewList", 0)   'Active
            //doForm.SetControlState("NDB_TXT_ListName", 2)   'Invisible
            //doForm.SetControlState("BTN_CCNewList_Add", 2)   'Invisible

            //Contact update to list
            //Dim arGroups As clArray = doForm.doRS.GetFieldVal("LNK_RELATED_GR", 2)

            clArray srCN = (clArray)doForm.doRS.GetFieldVal("LNK_CONNECTED_CN", 2);
            int i = 1;
            string sWhere = "";
            StringBuilder sData = new StringBuilder();
            string sListID = null;
            string sURI = "";
            string sMethod = "POST";
            string sCCName = doForm.doRS.GetFieldVal("LNK_RELATED_MC%%SYS_NAME%%").ToString();
            //doForm.doRS.GetFieldVal("LNK_RELATED_MC%%SYSNAME")

            if (string.IsNullOrEmpty(sCCName))
            {
                doForm.MessageBox("Please select a Campaign");
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.GetLinkCount("LNK_CONNECTED_CN") == 0)
            {
                doForm.MessageBox("Please Validate Contacts before creating List");
                par_doCallingObject = doForm;
                return false;
            }

            if (string.IsNullOrEmpty(doForm.GetControlVal("NDB_TXT_LISTNAME")))
            {
                doForm.MessageBox("List name should not be empty (or) Validate Contacts");
                par_doCallingObject = doForm;
                return false;
            }

            if (ConstCont_ContactList_CheckExistance(doForm.GetControlVal("NDB_TXT_LISTNAME"), doForm))
            {
                doForm.MessageBox("List Name already exists.");
                par_doCallingObject = doForm;
                return false;
            }

            //For i = 1 To srCN.GetDimension
            //    If i = 1 Then
            //        sWhere = "GID_ID='" & srCN.GetItem(i) & "'"
            //    Else
            //        sWhere = sWhere & " OR GID_ID='" & srCN.GetItem(i) & "'"
            //    End If
            //Next

            //Dim doCNRS As New clRowSet("CN", 3, sWhere, , "TXT_NAMELAST,TXT_NAMEFIRST,TXT_COMPANYNAMETEXT,EML_EMAIL,TEL_BUSPHONE,TEL_MAINPHONE,TEL_FAX,TXT_ADDRHOME,TXT_CITYHOME,TXT_STATEHOME,TXT_ZIPHOME,TXT_COUNTRYHOME,LNK_RELATED_JF")

            //'Save the Form before continuing
            //If doForm.Save(3) = 0 Then
            //    Return False
            //End If
            //1. Create List
            sListID = ConstCont_ContactList_Create(doForm.GetControlVal("NDB_TXT_LISTNAME"), doForm);

            //Add List to MLS MetaData
            //CS_AddContactLists(doForm)

            doForm.doRS.SetFieldVal("TXT_CCListID", sListID);

            //VR Commented
            //If doCNRS.GetFirst = 1 Then
            //    Do
            //        'Check Email Existance
            //        Dim sEmailid As String = doCNRS.GetFieldVal("EML_EMAIL")
            //        If Not String.IsNullOrEmpty(sEmailid) Then
            //            Dim sContactID As String = ""
            //            sContactID = ConstCont_Contact_GetIDbyEMail(sEmailid)

            //            'Check Contact Existance
            //            If sContactID = "" Then
            //                ' Create Contact 
            //                ConstCont_Contact_Create(sListID, sEmailid, doCNRS.GetFieldVal("TXT_NAMEFIRST"), doCNRS.GetFieldVal("TXT_NAMELAST"), doCNRS.GetFieldVal("TEL_MAINPHONE"))

            //            Else
            //                'Update Contact's List
            //                ConstCont_Contact_UpdateList(sContactID, sListID, sEmailid)

            //            End If
            //        End If

            //        If doCNRS.GetNext = 0 Then Exit Do
            //    Loop
            //End If

            DataTable _dsData = new DataTable();
            BulkUploadContacts(_dsData, sListID, doForm);

            //If doCNRS.oDataSet.Tables.Count > 0 Then
            //_dsData = doCNRS.oDataSet.Tables(0)
            // BulkUploadContacts(_dsData, sListID)
            //For index = 0 To _dsData.Rows.Count - 1
            //    'Check Email Existance
            //    Dim sEmailid As String = _dsData.Rows(index)("EML_EMAIL").ToString()
            //    If Not String.IsNullOrEmpty(sEmailid) Then
            //        Dim sContactID As String = ""
            //        sContactID = ConstCont_Contact_GetIDbyEMail(sEmailid)

            //        'Check Contact Existance
            //        If sContactID = "" Then
            //            ' Create Contact 
            //            ConstCont_Contact_Create(sListID, sEmailid, _dsData.Rows(index)("TXT_NAMEFIRST").ToString(), _dsData.Rows(index)("TXT_NAMELAST").ToString(), _dsData.Rows(index)("TEL_MAINPHONE").ToString())

            //        Else
            //            'Update Contact's List
            //            ConstCont_Contact_UpdateList(sContactID, sListID, sEmailid)

            //        End If
            //    End If
            //Next

            // End If

            doForm.SetControlState("NDB_lbl_ListStatus", 1);
            //visible   
            doForm.SetControlVal("NDB_lbl_ListStatus", "List Created");
            doForm.doRS.SetFieldVal("TXT_LISTCREATEDSTATUS", "List Created");
            doForm.SetControlState("BTN_CCNEWLIST_ADD", 4);
            //Grayed  
            //Save the Form 
            if (doForm.Save(3) == 0)
            {
                return false;
            }

            par_doCallingObject = doForm;
            return true;
        }

        //VR Upload Contacts to the exisisted list
        private bool CS_FormControlOnChange_BTN_CCNEWLIST_UPLOAD(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.GetFieldVal("MLS_CONTACTLISTS").Equals("<Make Selection>"))
            {
                doForm.MessageBox("Please select Campaign list."); par_doCallingObject = doForm;
                return false;
            }
            DataTable _sdatasrCN = (DataTable)doForm.doRS.GetFieldVal("LNK_CONNECTED_CN", 0);
            clArray srCN = (clArray)doForm.doRS.GetFieldVal("LNK_CONNECTED_CN", 2);
            int i = 1;
            string sWhere = "";
            StringBuilder sData = new StringBuilder();
            string sListID = null;
            string sURI = "";
            string sMethod = "POST";

            if (doForm.doRS.GetLinkCount("LNK_CONNECTED_CN") == 0)
            {
                doForm.MessageBox("Please Validate Contacts before creating List"); par_doCallingObject = doForm;
                return false;
            }

            for (i = 1; i <= srCN.GetDimension(); i++)
            {
                if (i == 1)
                {
                    sWhere = "GID_ID='" + srCN.GetItem(i) + "'";
                }
                else
                {
                    sWhere = sWhere + " OR GID_ID='" + srCN.GetItem(i) + "'";
                }
            }

            clRowSet doCNRS = new clRowSet("CN", 3, sWhere, "", "TXT_NAMELAST,TXT_NAMEFIRST,TXT_COMPANYNAMETEXT,EML_EMAIL,TEL_BUSPHONE,TEL_MAINPHONE,TEL_FAX,TXT_ADDRHOME,TXT_CITYHOME,TXT_STATEHOME,TXT_ZIPHOME,TXT_COUNTRYHOME,LNK_RELATED_JF");


            //Save the Form before continuing
            if (doForm.Save(3) == 0)
            {
                return false;
            }

            sListID = GetSelectedListID(doForm.doRS.GetFieldVal("MLS_CONTACTLISTS").ToString(), doForm);

            doForm.doRS.SetFieldVal("TXT_CCListID", sListID);


            if (doCNRS.GetFirst() == 1)
            {
                do
                {
                    //Check Email Existance
                    string sContactID = "";
                    sContactID = ConstCont_Contact_GetIDbyEMail(doCNRS.GetFieldVal("EML_EMAIL").ToString(), doForm);

                    //Check Contact Existance
                    if (string.IsNullOrEmpty(sContactID))
                    {
                        // Create Contact 
                        ConstCont_Contact_Create(sListID, doCNRS.GetFieldVal("EML_EMAIL").ToString(), doCNRS.GetFieldVal("TXT_NAMEFIRST").ToString(), doCNRS.GetFieldVal("TXT_NAMELAST").ToString(), doCNRS.GetFieldVal("TEL_MAINPHONE").ToString(), doForm);

                    }
                    else
                    {
                        //Update Contact's List
                        ConstCont_Contact_UpdateList(sContactID, sListID, doCNRS.GetFieldVal("EML_EMAIL").ToString(), doForm);
                    }

                    if (doCNRS.GetNext() == 0)
                        break; // TODO: might not be correct. Was : Exit Do
                } while (true);
            }

            //Save the Form 
            if (doForm.Save(3) == 0)
            {
                return false;
            }
            par_doCallingObject = doForm;
            return true;
        }

        //Use Templates
        private bool CS_FormControlOnChange_MLS_TEMPLATE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            Form doForm = (Form)par_doCallingObject;

            string _templateName = doForm.doRS.GetFieldVal("MLS_TEMPLATE").ToString();
            if (_templateName.Equals("<Make Selection>"))
            {
                doForm.doRS.SetFieldVal("MMR_EmailContent", string.Empty);
                par_doCallingObject = doForm;
                return false;
            }
            //string _path = HttpContext.Current.Server.MapPath("~/Templates/CCTemplates");

            //string _path = HttpContext.Current.Server.MapPath("" + ((DataTable)Util.GetVar("SiteSettings")).Rows[0]["TemplatesPath"].ToString() + "CCTemplates ");

            string _path = Util.GetTemplatesPath() + "\\CCTemplates";

            if (System.IO.Directory.Exists(_path))
            {
                if (System.IO.File.Exists(_path + "/cus_" + _templateName + ".html"))
                {
                    string _strhtml = File.ReadAllText(_path + "/cus_" + _templateName + ".html");
                    doForm.doRS.SetFieldVal("MMR_EmailContent", _strhtml);

                }

            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CS_FormControlOnChange_BTN_CCUploadContacts(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //doForm.SetControlState("BTN_CCNewList", 0)   'Active
            //doForm.SetControlState("NDB_TXT_ListName", 2)   'Invisible
            //doForm.SetControlState("BTN_CCNewList_Add", 2)   'Invisible

            //Contact update to list
            //Dim arGroups As clArray = doForm.doRS.GetFieldVal("LNK_RELATED_GR", 2)
            clArray srCN = (clArray)doForm.doRS.GetFieldVal("LNK_CONNECTED_CN", 2);
            int i = 1;
            string sWhere = "";
            StringBuilder sData = new StringBuilder();
            string sResponse = null;
            string sListID = null;
            string sURI = "";
            string sMethod = "POST";
            string sCreateListURI = "https://api.constantcontact.com/v2/lists?api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
            //ma4jgf2zu926c3ggw5dq9hpm"
            string sCreateContURI = "https://api.constantcontact.com/v2/contacts?action_by=ACTION_BY_OWNER&api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
            //ma4jgf2zu926c3ggw5dq9hpm"
            string sUpdateContURI = "https://api.constantcontact.com/v2/contacts/";
            string sGetContactURI = "https://api.constantcontact.com/v2/contacts?";

            if (doForm.doRS.GetLinkCount("LNK_CONNECTED_CN") == 0)
            {
                doForm.MessageBox("Please Validate Contacts before creating List"); par_doCallingObject = doForm;
                return false;
            }

            //If arGroups.GetDimension = 0 Then
            //    doForm.MessageBox("Please select a Group with Contacts")
            //    Return False
            //End If

            for (i = 1; i <= srCN.GetDimension(); i++)
            {
                if (i == 1)
                {
                    sWhere = "GID_ID='" + srCN.GetItem(i) + "'";
                }
                else
                {
                    sWhere = sWhere + " OR GID_ID='" + srCN.GetItem(i) + "'";
                }
            }


            clRowSet doCNRS = new clRowSet("CN", 3, sWhere, "", "TXT_NAMELAST,TXT_NAMEFIRST,TXT_COMPANYNAMETEXT,EML_EMAIL,TEL_BUSPHONE,TEL_MAINPHONE,TEL_FAX,TXT_ADDRHOME,TXT_CITYHOME,TXT_STATEHOME,TXT_ZIPHOME,TXT_COUNTRYHOME,LNK_RELATED_JF");

            //If doCNRS.GetFirst = 0 Then
            //    doForm.MessageBox("Please select a Group with Contacts")
            //    Return False
            //End If

            //Save the Form before continuing
            if (doForm.Save(3) == 0)
            {
                return false;
            }

            //If List is not selected create new
            bool bRetError = false;
            if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_CCListID").ToString()))
            {
                //Check for List Name Pre-Existance
                sURI = "https://api.constantcontact.com/v2/lists?api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
                //ma4jgf2zu926c3ggw5dq9hpm"

                sResponse = httpPost(ref bRetError, sURI, sData, "GET", doForm);
                string sNewListName = doForm.GetControlVal("NDB_TXT_LISTNAME");
                DataTable dtLists = new DataTable();
                dtLists = ParseJSON_DataSet(sResponse);

                foreach (DataRow row_loopVariable in dtLists.Rows)
                {
                    //row = row_loopVariable;
                    if (row_loopVariable["name"].ToString() == sNewListName)
                    {
                        doForm.MessageBox("List Name already exists."); par_doCallingObject = doForm;
                        return false;
                    }
                }

                //1. Create List
                sData.Append("{" + "\"name\": \"" + doForm.GetControlVal("NDB_TXT_LISTNAME") + "\"," + "\"status\": \"ACTIVE\"" + "}");
                sResponse = httpPost(ref bRetError, sCreateListURI, sData, sMethod, doForm);
                sListID = ParseJSON(sResponse, "id");

                //Add List to MLS MetaData
                par_doCallingObject = doForm;
                CS_AddContactLists(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            }
            else
            {
                sListID = GetSelectedListID(doForm.doRS.GetFieldVal("MLS_CONTACTLISTS").ToString(), doForm);
            }

            doForm.doRS.SetFieldVal("TXT_CCListID", sListID);


            if (doCNRS.GetFirst() == 1)
            {
                do
                {
                    //Check if Email Already exists
                    string sURL = "";
                    sData = new StringBuilder();
                    sURL = sGetContactURI + "email=" + doCNRS.GetFieldVal("EML_EMAIL") + "&status=ALL&limit=1&api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
                    //ma4jgf2zu926c3ggw5dq9hpm"

                    sResponse = httpPost(ref bRetError, sURL, sData, "GET", doForm);
                    string sContactID = "";
                    sContactID = ParseJSON_ExistingData(sResponse, "id");

                    //Check Contact Existance
                    if (string.IsNullOrEmpty(sContactID))
                    {
                        // Create Contact 

                        //Get Job Function
                        clRowSet doJFName = new clRowSet("JF", 3, "GID_ID='" + doCNRS.GetFieldVal("LNK_RELATED_JF") + "'", "", "TXT_JOBFUNCNAME");
                        string sJFName = "";
                        if (doJFName.GetFirst() == 1)
                        {
                            sJFName = doJFName.GetFieldVal("TXT_JOBFUNCNAME").ToString();
                        }

                        sMethod = "POST";
                        sData.Append("{" + "\"status\": \"ACTIVE\"," + "\"fax\": \"" + doCNRS.GetFieldVal("TEL_FAX") + "\"," + "\"addresses\": [ {" + "\"line1\": \"" + doCNRS.GetFieldVal("TXT_ADDRHOME") + "\"," + "\"city\": \"" + doCNRS.GetFieldVal("TXT_CITYHOME") + "\"," + "\"address_type\": \"PERSONAL\"," + "\"state_code\": \"" + doCNRS.GetFieldVal("TXT_STATEHOME") + "\"," + "\"country_code\": \"" + doCNRS.GetFieldVal("TXT_COUNTRYHOME") + "\"," + "\"postal_code\": \"" + doCNRS.GetFieldVal("TXT_ZIPHOME") + "\"" + "}]," + "\"lists\": [{" + "\"id\": \"" + sListID + "\"," + "\"status\": \"ACTIVE\"" + "}]," + "\"email_addresses\": [{" + "\"status\": \"ACTIVE\"," + "\"email_address\": \"" + doCNRS.GetFieldVal("EML_EMAIL") + "\"" + "}]," + "\"first_name\": \"" + doCNRS.GetFieldVal("TXT_NAMEFIRST") + "\"," + "\"last_name\": \"" + doCNRS.GetFieldVal("TXT_NAMELAST") + "\"," + "\"job_title\": \"" + sJFName + "\"," + "\"company_name\": \"" + doCNRS.GetFieldVal("TXT_COMPANYNAMETEXT") + "\"," + "\"home_phone\": \"" + doCNRS.GetFieldVal("TEL_MAINPHONE") + "\"," + "\"work_phone\": \"" + doCNRS.GetFieldVal("TEL_BUSPHONE") + "\"" + "}");
                        httpPost(ref bRetError, sCreateContURI, sData, "POST", doForm);
                    }
                    else
                    {
                        //Update List of Contact
                        sURI = sUpdateContURI + sContactID + "?action_by=ACTION_BY_OWNER&api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
                        //ma4jgf2zu926c3ggw5dq9hpm"
                        sMethod = "PUT";

                        //Get Existing Contact Data for List
                        DataTable dtLists = ParseJSON_GetTable(sResponse, "lists");
                        int j = 0;
                        string sListString = "";
                        bool binList = false;
                        for (j = 0; j <= dtLists.Rows.Count - 1; j++)
                        {
                            DataRow row = dtLists.Rows[j];
                            if (sListID == row["id"].ToString())
                                binList = true;
                            if (j == 0)
                            {
                                sListString = "{\"id\": \"" + row["id"] + "\"," + "\"status\": \"" + row["status"] + "\"}";
                            }
                            else
                            {
                                sListString = sListString + "," + "{\"id\": \"" + row["id"] + "\"," + "\"status\": \"" + row["status"] + "\"}";
                            }
                        }

                        if (!binList)
                        {
                            if (!string.IsNullOrEmpty(sListString))
                            {
                                sListString = sListString + "," + "{\"id\": \"" + sListID + "\"," + "\"status\": \"ACTIVE\"}";
                            }
                            else
                            {
                                sListString = "{\"id\": \"" + sListID + "\"," + "\"status\": \"ACTIVE\"}";
                            }

                        }


                        sData.Append("{" + "\"id\": \"" + sContactID + "\"," + "\"status\": \"ACTIVE\"," + "\"lists\": [" + sListString + "]," + "\"email_addresses\": [{" + "\"status\": \"ACTIVE\"," + "\"email_address\": \"" + doCNRS.GetFieldVal("EML_EMAIL") + "\"" + "}]" + "}");
                        httpPost(ref bRetError, sURI, sData, sMethod, doForm);

                    }

                    if (doCNRS.GetNext() == 0)
                        break; // TODO: might not be correct. Was : Exit Do
                } while (true);
            }

            //Save the Form 
            doForm.Save(3);
            par_doCallingObject = doForm;
            return true;
        }

        public bool CS_FormControlOnChange_BTN_CCNEWTEMPLATE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplatePage = "LST_CS:TEMPLATE";
            string sTemplateName = "";

            string sTemplate = goMeta.PageRead("GLOBAL", sTemplatePage, "", false, "XX");
            string[] aTemplates = sTemplate.Split(Constants.vbCrLf.ToCharArray());
            int TemplateMaxCount = aTemplates.Length;

            int i = 0;
            int iMaxNum = 0;
            string sTemplateCurr = "";
            string sCurrNum = "";
            int NumLength = 0;
            int NumStart = 0;
            int iCurrNum = 0;

            for (i = 0; i <= TemplateMaxCount - 1; i++)
            {
                sTemplateCurr = aTemplates[i].Trim();
                if (sTemplateCurr.Length > 0)
                {
                    sCurrNum = sTemplateCurr.Substring(0, (Strings.InStr(sTemplateCurr, "=") - 1));
                    NumStart = Strings.InStr(sCurrNum, "_");
                    NumLength = Strings.Len(sCurrNum) - NumStart;
                    sCurrNum = sCurrNum.Substring(NumStart, NumLength);

                    if (Information.IsNumeric(sCurrNum))
                    {
                        iCurrNum = Convert.ToInt32(goTR.StringToNum(sCurrNum, "", ref par_iValid));
                        if (iCurrNum > iMaxNum)
                        {
                            iMaxNum = iCurrNum;
                        }
                    }
                }
            }

            //Need to be changed
            sTemplateName = "Template" + goTR.NumToString(iMaxNum + 1);

            System.Data.SqlClient.SqlConnection oCon = new System.Data.SqlClient.SqlConnection();
            goMeta.LineWrite("GLOBAL", sTemplatePage, "US_" + goTR.NumToString(iMaxNum + 1), sTemplateName, ref oCon, "XX");

            goData.LoadListData();
            //goData.LoadSchemaData()
            //doForm.RefreshAllLinkNames = True
            //doForm.ReloadForm = True

            //goUI.RefreshOpenForm();   //commntd in 6.0

            //goUI.Navigate("REFRESH", "")
            par_doCallingObject = doForm;
            return true;
        }

        public bool CS_FormControlOnChange_BTN_CCSENDNOW(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            //Dim zone As PublicDomain.TzTimeZone
            //Dim dtDateTime As DateTime
            //Dim sDateTime As String
            //Dim sCurrentUser As String = goP.GetMe("ID")
            //Dim sResponse As String = ""
            string sCampaignID = doForm.doRS.GetFieldVal("TXT_CCCampaignID").ToString();
            string sStatus = "";

            string sCCName = doForm.doRS.GetFieldVal("LNK_RELATED_MC%%SYS_NAME%%").ToString();
            //doForm.doRS.GetFieldVal("LNK_RELATED_MC%%SYSNAME")
            if (string.IsNullOrEmpty(sCCName))
            {
                doForm.MessageBox("Please select a Campaign"); par_doCallingObject = doForm;
                return false;
            }

            if (string.IsNullOrEmpty(sCampaignID))
            {
                doForm.MessageBox("Please Create Constant Contact Email Draft"); par_doCallingObject = doForm;
                return false;
            }

            //Dim sExistingCampaignURI As String = "https://api.constantcontact.com/v2/emailmarketing/campaigns/" & sCampaignID & "?api_key=ma4jgf2zu926c3ggw5dq9hpm"
            //Dim sScheduleURI As String = "https://api.constantcontact.com/v2/emailmarketing/campaigns/"
            //Dim sURI As String = sScheduleURI + sCampaignID + "/schedules?api_key=ma4jgf2zu926c3ggw5dq9hpm"
            //Dim sData As New StringBuilder

            //Get Email Campaign Status
            //sResponse = httpPost(sExistingCampaignURI, sData, "GET")
            //sStatus = ParseJSON_ExistingData(sResponse, "status", "root")
            sStatus = ConstCont_Campaign_GetStatus(sCampaignID, doForm);

            if (sStatus == "DRAFT" | sStatus == "SENT")
            {
                //Continue
            }
            else
            {
                doForm.MessageBox("Email Drop already scheduled."); par_doCallingObject = doForm;
                return false;
            }

            // zone = goTR.UTC_GetUserTimeZone(sCurrentUser)
            //dtDateTime = goTR.UTC_LocalToUTC(doForm.doRS.GetFieldVal("DTT_SCHEDULEDDATE", 2))
            //sDateTime = goTR.DateToString(dtDateTime.Date) & "T" & dtDateTime.TimeOfDay.ToString("hh\:mm\:ss") & ".000Z"

            //sData.Append("{" & """scheduled_date"": """ & sDateTime & """" & "}")

            //sResponse = httpPost(sURI, sData, "POST")

            System.DateTime sDate = Convert.ToDateTime(doForm.doRS.GetFieldVal("DTT_SCHEDULEDDATE", 2));

            PublicDomain.TzTimeZone zone = default(PublicDomain.TzTimeZone);
            DateTime dtDateTime = default(DateTime);
            string sDateTime = null;
            string sCurrentUser = goP.GetMe("ID");

            zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
            dtDateTime = goTR.UTC_LocalToUTC(ref sDate);
            sDateTime = goTR.DateToString(dtDateTime.Date, "", ref par_iValid) + "T" + dtDateTime.TimeOfDay.ToString("hh\\:mm\\:ss") + ".000Z";

            if (dtDateTime < DateTime.UtcNow)
            {
                doForm.MessageBox("error message : Scheduled date is before the current time");
                par_doCallingObject = doForm;
                return false;
            }

            string scID = ConstCont_Schedule_Create(sCampaignID, Convert.ToDateTime(doForm.doRS.GetFieldVal("DTT_SCHEDULEDDATE", 2)), doForm);

            if (!string.IsNullOrEmpty(scID))
            {
                doForm.doRS.SetFieldVal("TXT_CCScheduleID", scID);
                doForm.doRS.SetFieldVal("MLS_STATUS", "SCHEDULED");
                doForm.SetControlState("BTN_SAVE", 4);
                doForm.SetControlState("BTN_SAVEANDLEAVEOPEN", 4);
                doForm.SetControlState("BTN_CCNEWLIST_ADD", 4);                //Grayed  
                doForm.SetControlState("BTN_CCNEWCAMPAIGN_ADD", 4);                //Grayed  
                doForm.SetControlState("BTN_VALIDATECONTACTS", 4);                //Grayed  

                doForm.SetControlState("BTN_CCUnschedule", 0);

                doForm.SetControlState("LNK_RELATED_MC", 5);                //Grayed  
                doForm.SetControlState("LNK_RELATED_GR", 5);                //Grayed  
                doForm.MessagePanel("This email drop has been scheduled.");

                if (doForm.Save(3) == 0)
                {
                    return false;
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        //Unschedule
        public bool CS_FormControlOnChange_BTN_CCUnschedule(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            //Dim zone As PublicDomain.TzTimeZone
            //Dim dtDateTime As DateTime
            //Dim sDateTime As String
            //Dim sCurrentUser As String = goP.GetMe("ID")
            //Dim sResponse As String = ""
            string sCampaignID = doForm.doRS.GetFieldVal("TXT_CCCampaignID").ToString();
            string CCScheduleID = doForm.doRS.GetFieldVal("TXT_CCScheduleID").ToString();


            string scID = ConstCont_Schedule_Delete(sCampaignID, CCScheduleID, doForm);

            if (string.IsNullOrEmpty(scID))
            {
                doForm.SetControlState("BTN_CCUnschedule", 4);
                doForm.doRS.SetFieldVal("DTE_SCHEDULEDDATE", "");
                doForm.doRS.SetFieldVal("TME_SCHEDULEDDATE", "");
                string sStatus = ConstCont_Campaign_GetStatus(sCampaignID, doForm);
                doForm.doRS.SetFieldVal("MLS_STATUS", sStatus);
                doForm.MessagePanel("This email drop has been Unscheduled.");

                if (doForm.Save(3) == 0)
                {
                    return false;
                }
            }
            par_doCallingObject = doForm;
            return true;
        }
        //Validate Contact 
        public bool CS_FormControlOnChange_BTN_VALIDATECONTACTS(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            string sMD = goMeta.PageRead("GLOBAL", "OTH_CCCAMPAIGNVALIDATETRACKING", "", false, "XX");

            Stopwatch stwatch = new Stopwatch();
            stwatch.Start();

            Form doForm = (Form)par_doCallingObject;
            clArray arGroups = (clArray)doForm.doRS.GetFieldVal("LNK_RELATED_GR", 2);
            string sWhere = "";
            clArray arCN = new clArray();
            //Dim sbarCN As New StringBuilder
            int ContCount = 0;
            int TotalCount = 0;
            doForm.doRS.ClearLinkAll("LNK_CONNECTED_CN");

            string sCCName = doForm.doRS.GetFieldVal("LNK_RELATED_MC%%SYS_NAME%%").ToString();
            //doForm.doRS.GetFieldVal("LNK_RELATED_MC%%SYSNAME")
            if (string.IsNullOrEmpty(sCCName))
            {
                doForm.MessageBox("Please select a Campaign"); par_doCallingObject = doForm;
                return false;
            }

            if (arGroups.GetDimension() == 0)
            {
                doForm.MessageBox("Please select a Group"); par_doCallingObject = doForm;
                return false;
            }

            StringBuilder sData = new StringBuilder();
            sData.Append("{");
            sData.Append("\"import_data\": [");
            //VNK 08242015
            //-----------'VNK 08242015 Start
            string sCHK_MAILINGLISTField = "CHK_CCMailList";

            clRowSet doMCRS = new clRowSet("MC", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_RELATED_MC") + "'", "", "MLS_CAMPAIGNFOR", 1);

            dynamic sCampaignFor = doMCRS.GetFieldVal("MLS_CAMPAIGNFOR");
            int iCampaignForID = 0;
            iCampaignForID = Convert.ToInt32(doMCRS.GetFieldVal("MLS_CAMPAIGNFOR", 2));
            sCHK_MAILINGLISTField = sCHK_MAILINGLISTField + (iCampaignForID + 1).ToString();

            //Dim table As DataTable = doForm.doRS.oDataSet.Tables(4)
            int iTable = 4;

            DataTable _dtsFile = doForm.doRS.oDataSet.Tables[1];
            for (int index = 0; index <= _dtsFile.Rows.Count; index++)
            {
                if (_dtsFile.Rows[index][0].ToString().ToLower().Contains("lnk_connected_cn"))
                {
                    iTable = index + 2;
                    break; // TODO: might not be correct. Was : Exit For
                }
            }

            for (int i = 1; i <= arGroups.GetDimension(); i++)
            {
                sWhere = "LNK_RELATED_GR='" + arGroups.GetItem(i) + "'";
                //sWhere = "LNK_RELATED_GR='" & arGroups.GetItem(i) & "'" & " AND " & sCHK_MAILINGLISTField & "= 1"

                clRowSet _doCNRS = new clRowSet("CN", 3, sWhere, "", "EML_EMAIL,TXT_NAMEFIRST,TXT_NAMELAST," + sCHK_MAILINGLISTField);

                DataTable _QdataGR = new DataTable();
                if (_doCNRS.oDataSet.Tables.Count > 0)
                {
                    _QdataGR = _doCNRS.oDataSet.Tables[0];

                    for (int index = 0; index <= _QdataGR.Rows.Count - 1; index++)
                    {
                        TotalCount = TotalCount + 1;
                        if (_QdataGR.Rows[index][sCHK_MAILINGLISTField].ToString().Equals("1"))
                        {
                            if (!string.IsNullOrEmpty(_QdataGR.Rows[index]["EML_EMAIL"].ToString()))
                            {

                                if (ValidateEmail(_QdataGR.Rows[index]["EML_EMAIL"].ToString()))
                                {
                                    arCN.Add(_QdataGR.Rows[index]["GID_ID"].ToString());
                                    //AddLink(iTable, "LNK_CONNECTED_CN", _QdataGR.Rows(index)("GID_ID").ToString(), 0, doForm, "CS_CN_CONNECTED")

                                    //sbarCN.Append(_QdataGR.Rows(index)("GID_ID").ToString() & vbCrLf)
                                    ContCount = ContCount + 1;

                                    sData.Append("{" + "\"email_addresses\": [" + "\"" + _QdataGR.Rows[index]["EML_EMAIL"].ToString() + "\"" + "]," + "\"job_title\": \"\"," + "\"company_name\": \"\"," + "\"home_phone\": \"\"" + "},");
                                }
                            }
                        }
                    }

                }

                //-----------'VNK 08242015 END
            }

            if (ContCount > 0)
            {
                sData.Remove(sData.Length - 1, 1);

                sData.Append("],\"lists\":[" + "\"AlistsZ1\"],");

                sData.Append("\"column_names\": [" + "\"EMAIL\"," + "\"JOB TITLE\"," + "\"COMPANY NAME\"," + "\"HOME PHONE\"" + "]}");

                goP.SetVar("par_ClstData", sData.ToString());
            }


            //If arCN.GetDimension = 0 Then
            //    doForm.MessageBox("Please select a Group with valid Contacts (Mailing List is checked and contains a valid email address).")
            //    Return False
            //End If

            if (ContCount == 0)
            {
                doForm.MessageBox("Please select a Group with valid Contacts (Mailing List is checked and contains a valid email address)."); par_doCallingObject = doForm;
                return false;
            }

            //goP.SetVar("par_LNK_CONNECTED_CN", sbarCN.ToString())
            doForm.doRS.SetLinkVal("LNK_CONNECTED_CN", arCN);
            //doForm.doRS.SetLinkVal("LNK_CONNECTED_CN", sbarCN.ToString())

            string sCampaignName = sCCName + doForm.doRS.GetFieldVal("TXT_CONSTANTCONTACTCAMPAIGNNAME") + DateTime.Now.ToString("MMddyyyHHmmmssffff");
            //doForm.GetControlVal("NDB_TXT_CampaignName")
            doForm.SetControlVal("NDB_TXT_LISTNAME", sCampaignName);
            //doForm.SetControlState("NDB_LBL_VALIDATESTAUS", 1)   'visible   
            //doForm.SetControlVal("NDB_LBL_VALIDATESTAUS", "Validated")
            //doForm.doRS.SetFieldVal("TXT_VALIDATESTATUS", "Validated")
            //Save the Form 
            //If doForm.Save(3) = 0 Then
            //    Return False
            //End If
            doForm.SetControlState("BTN_CCNEWLIST_ADD", 0);

            stwatch.Stop();
            TimeSpan th = stwatch.Elapsed;

            doForm.MessageBox(ContCount.ToString() + " out of " + TotalCount + " Contacts are eligible for Campaign.(" + th.TotalSeconds.ToString() + " Sec)");


            par_doCallingObject = doForm;
            return true;
        }

        private bool AddLink(int iTable, string sLinkName, string sID, int lCurrentRow, Form doForm, string sTableName)
        {

            string sProc = "clRowset::AddLink";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            if (Strings.Len(sID) < 5)
                return false;

            string expression = "";
            DataTable table = doForm.doRS.oDataSet.Tables[iTable];

            //try
            //{

                if (Strings.InStr(sLinkName, "%%") > 0)
                    sLinkName = goTR.ExtractString(sLinkName, 1, "%%");

                DataSet ds = doForm.doRS.oDataSet;

                if (iTable == 0)
                {
                    doForm.doRS.oDataSet.Tables[iTable].Rows[lCurrentRow][sLinkName + "%%GID_ID"] = sID;
                    doForm.doRS.oDataSet.Tables[iTable].Rows[lCurrentRow][sLinkName + "%%SYS_Name"] = "";
                }
                else
                {
                    expression = "[" + sLinkName + "%%GID_ID]='" + sID + "' AND [BASE%%GID_ID]='" + doForm.doRS.oDataSet.Tables[0].Rows[lCurrentRow]["GID_ID"].ToString() + "'";

                    DataRow[] foundRows = null;

                    // Use the Select method to find all rows matching the filter.
                    foundRows = table.Select(expression);
                    //Record already exists in datatable
                    if (foundRows.GetUpperBound(0) == 0)
                    {
                        return true;
                    }
                    else
                    {
                        DataRow row = default(DataRow);
                        row = doForm.doRS.oDataSet.Tables[iTable].NewRow();
                        row["BASE%%GID_ID"] = doForm.doRS.oDataSet.Tables[0].Rows[lCurrentRow]["GID_ID"];
                        row[sLinkName + "%%GID_ID"] = sID;
                        //Dim sTableName As String = goTR.ExtractString(oLinkData.LinkID, 1, ",") & "_" & _
                        //goTR.ExtractString(oLinkData.LinkID, 2, ",") & "_" & _
                        //goTR.ExtractString(oLinkData.LinkID, 3, ",")
                        row["GID_ID"] = genGuid(sTableName);
                        doForm.doRS.oDataSet.Tables[iTable].Rows.Add(row);

                    }
                }
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45100, sProc);
            //    }
            //}
            return true;

        }


        private string genGuid(string TableName)
        {

            TableName = TableName.ToUpper();

            byte[] a1 = Encoding.ASCII.GetBytes(Guid.NewGuid().ToString().Substring(0, 8));
            byte[] a2 = Encoding.ASCII.GetBytes(TableName.Substring(0, 2));
            byte[] a3 = Encoding.ASCII.GetBytes(DateTime.UtcNow.ToString().Substring(0, 6));

            byte[] a = new byte[a1.Length + a2.Length + (a3.Length - 1) + 1];
            System.Buffer.BlockCopy(a1, 0, a, 0, a1.Length);
            System.Buffer.BlockCopy(a2, 0, a, a1.Length, a2.Length);
            System.Buffer.BlockCopy(a3, 0, a, a1.Length + a2.Length, a3.Length);

            Guid sguid = new Guid(a);

            return sguid.ToString();
        }

        public bool CS_FormControlOnChange_MLS_CONTACTLISTS(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sSelectedListName = doForm.doRS.GetFieldVal("MLS_CONTACTLISTS").ToString();
            string sSelectedListID = "";

            sSelectedListID = GetSelectedListID(sSelectedListName, doForm);

            doForm.doRS.SetFieldVal("TXT_CCLISTID", sSelectedListID);
            par_doCallingObject = doForm;
            return true;
        }

        public bool CS_AddContactLists(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Dim sURI As String = "https://api.constantcontact.com/v2/lists?api_key=ma4jgf2zu926c3ggw5dq9hpm"
            //Dim sData As New StringBuilder
            //Dim sResponse As String = httpPost(sURI, sData, "GET")
            string sNewListName = doForm.GetControlVal("NDB_TXT_LISTNAME");

            //Dim dtLists As New DataTable
            //dtLists = ParseJSON_DataSet(sResponse)

            //For Each row In dtLists.Rows
            //    If row("name") = sNewListName Then
            //        doForm.MessageBox("List Name already exists.")
            //        Return False
            //    End If
            //Next

            string sTemplatePage = "LST_CS:CONTACTLISTS";
            string sTemplateName = "";

            string sTemplate = goMeta.PageRead("GLOBAL", sTemplatePage, "", false, "XX");
            string[] aTemplates = sTemplate.Split(Constants.vbCrLf.ToCharArray());
            int TemplateMaxCount = aTemplates.Length;

            int i = 0;
            int iMaxNum = 0;
            string sTemplateCurr = "";
            string sCurrNum = "";
            int NumLength = 0;
            int NumStart = 0;
            int iCurrNum = 0;

            int par_iValid = 4;
            for (i = 0; i <= TemplateMaxCount - 1; i++)
            {
                sTemplateCurr = aTemplates[i].Trim();
                if (sTemplateCurr.Length > 0)
                {
                    sCurrNum = sTemplateCurr.Substring(0, (Strings.InStr(sTemplateCurr, "=") - 1));
                    NumStart = Strings.InStr(sCurrNum, "_");
                    NumLength = Strings.Len(sCurrNum) - NumStart;
                    sCurrNum = sCurrNum.Substring(NumStart, NumLength);

                    if (Information.IsNumeric(sCurrNum))
                    {
                        iCurrNum = Convert.ToInt32(goTR.StringToNum(sCurrNum, "", ref par_iValid));
                        if (iCurrNum > iMaxNum)
                        {
                            iMaxNum = iCurrNum;
                        }
                    }
                }
            }

            //Need to be changed
            sTemplateName = sNewListName;
            System.Data.SqlClient.SqlConnection par_oConnection = new System.Data.SqlClient.SqlConnection();
            goMeta.LineWrite("GLOBAL", sTemplatePage, "US_" + goTR.NumToString(iMaxNum + 1), sTemplateName, ref par_oConnection, "", "XX");

            //Reload List and Form
            goData.LoadListData();
            //goUI.RefreshOpenForm();

            //Select the Item in List
            doForm.doRS.SetFieldVal("MLS_CONTACTLISTS", iMaxNum + 1, 2);
            par_doCallingObject = doForm;
            return true;
        }

        public bool CS_FormControlOnChange_BTN_Loadstatistics(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);



            Form doForm = (Form)par_doCallingObject;
            if (!string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_CCCampaignID").ToString()))
            {
                StringBuilder sData = new StringBuilder();

                //'Get Campaign Details
                //Dim sccuri As String = "https://api.constantcontact.com/v2/emailmarketing/campaigns/" & doForm.doRS.GetFieldVal("TXT_CCCampaignID") & "?api_key=ma4jgf2zu926c3ggw5dq9hpm"
                //Dim _sResponse As String = httpPost(sccuri, sData, "GET")
                //Dim dtResults As DataTable = ParseJSON_GetTable(_sResponse, "click_through_details")

                //Dim sclickurls As DataRow()
                //sclickurls = dtResults.[Select]("url_uid='1120587499902'")
                //If sclickurls.Length > 0 Then
                //    For Each sclickurl In sclickurls
                //        Dim _saction As String = "Click on link " + sclickurl("url").ToString()
                //    Next
                //End If

                string sTrackingURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns/" + doForm.doRS.GetFieldVal("TXT_CCCampaignID") + "/tracking/reports/summary?api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
                //ma4jgf2zu926c3ggw5dq9hpm"
                bool bRetError = false;
                string sResponse = httpPost(ref bRetError, sTrackingURI, new StringBuilder(), "GET", doForm);
                string sends = string.Empty;
                string Opens = string.Empty;
                string clicks = string.Empty;
                string forwards = string.Empty;
                string unsubscribes = string.Empty;
                string bounces = string.Empty;
                string spam_count = string.Empty;

                sends = ParseJSON(sResponse, "sends");
                Opens = ParseJSON(sResponse, "Opens");
                clicks = ParseJSON(sResponse, "clicks");
                forwards = ParseJSON(sResponse, "forwards");
                unsubscribes = ParseJSON(sResponse, "unsubscribes");
                bounces = ParseJSON(sResponse, "bounces");
                spam_count = ParseJSON(sResponse, "spam_count");

                doForm.doRS.SetFieldVal("BI__Sends", sends);
                doForm.doRS.SetFieldVal("BI__Opens", Opens);
                doForm.doRS.SetFieldVal("BI__clicks", clicks);
                doForm.doRS.SetFieldVal("BI__forwards", forwards);
                doForm.doRS.SetFieldVal("BI__unsubscribes", unsubscribes);
                doForm.doRS.SetFieldVal("BI__bounces", bounces);
                doForm.doRS.SetFieldVal("BI__spamcount", spam_count);
                if (Convert.ToInt64(sends) != 0)
                {
                    //try
                    //{
                        dynamic _Openrate = (Convert.ToInt64(Opens) / Convert.ToInt64(sends)) * 100;
                        dynamic _Clickrate = (Convert.ToInt64(clicks) / Convert.ToInt64(sends)) * 100;
                        dynamic _Forwardrate = (Convert.ToInt64(forwards) / Convert.ToInt64(sends)) * 100;
                        dynamic _Unsubscriberate = (Convert.ToInt64(unsubscribes) / Convert.ToInt64(sends)) * 100;
                        dynamic _Bouncerate = (Convert.ToInt64(bounces) / Convert.ToInt64(sends)) * 100;

                        doForm.doRS.SetFieldVal("SR__Openrate", _Openrate);
                        doForm.doRS.SetFieldVal("SR__Clickrate", _Clickrate);
                        doForm.doRS.SetFieldVal("SR__Forwardrate", _Forwardrate);
                        doForm.doRS.SetFieldVal("SR__Unsubscriberate", _Unsubscriberate);
                        doForm.doRS.SetFieldVal("SR__Bouncerate", _Bouncerate);

                    //}
                    //catch (Exception ex)
                    //{
                    //}
                }

                if (doForm.Save(3) == 0)
                {
                    return false;
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CheckVerifiedEmail(string sEmail, Form sdoform = null)
        {

            string sURI = "https://api.constantcontact.com/v2/account/verifiedemailaddresses?status=CONFIRMED&api_key=" + GetConsatntContactAutho("APIKEY", "", sdoform);
            //ma4jgf2zu926c3ggw5dq9hpm"
            StringBuilder sData = new StringBuilder();
            string sMethod = "GET";
            string sResponse = "";
            string sID = "";
            bool bRetError = false;
            sResponse = httpPost(ref bRetError, sURI, sData, sMethod, sdoform);

            DataTable dtLists = new DataTable();
            dtLists = ParseJSON_DataSet(sResponse);


            if ((dtLists != null))
            {
                DataRow[] rows = dtLists.Select("email_address='" + sEmail + "'");
                if ((rows != null))
                {
                    if (rows.Length > 0)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public string ConstCont_ContactList_Create(string sListName, Form sdoform = null)
        {

            string sCreateListURI = "https://api.constantcontact.com/v2/lists?api_key=" + GetConsatntContactAutho("APIKEY", "", sdoform);
            //ma4jgf2zu926c3ggw5dq9hpm"
            StringBuilder sData = new StringBuilder();
            string sMethod = "POST";
            string sResponse = "";
            string sListID = "";
            bool bRetError = false;
            sData.Append("{" + "\"name\": \"" + sListName + "\"," + "\"status\": \"ACTIVE\"" + "}");
            sResponse = httpPost(ref bRetError, sCreateListURI, sData, sMethod, sdoform);
            sListID = ParseJSON(sResponse, "id");

            return sListID;
        }
        public DataTable ConstCont_ContactList_GetAll(Form doform = null)
        {

            string sURI = "https://api.constantcontact.com/v2/lists?api_key=" + GetConsatntContactAutho("APIKEY", "", doform);
            //ma4jgf2zu926c3ggw5dq9hpm"
            string sResponse = "";
            StringBuilder sData = new StringBuilder();
            string sMethod = "GET";
            bool bRetError = false;
            sResponse = httpPost(ref bRetError, sURI, sData, sMethod, doform);

            DataTable dtLists = new DataTable();
            dtLists = ParseJSON_DataSet(sResponse);

            return dtLists;
        }
        public bool ConstCont_ContactList_CheckExistance(string sNewListName, Form doform = null)
        {

            DataTable dtLists = ConstCont_ContactList_GetAll(doform);
            bool bExists = false;

            if ((dtLists != null))
            {
                sNewListName = sNewListName.Replace("'", "");
                DataRow[] rows = dtLists.Select("name='" + sNewListName + "'");
                if ((rows != null))
                {
                    if (rows.Length > 0)
                    {
                        bExists = true;
                    }
                }
            }

            return bExists;
        }
        public bool ConstCont_Contact_Create(string sListID, string sEmail_Addresses, string sFirst_Name, string sLast_Name, string sHome_Phone, Form sdoForm = null)
        {
            try
            {
                string sCreateContURI = "https://api.constantcontact.com/v2/contacts?action_by=ACTION_BY_OWNER&api_key=" + GetConsatntContactAutho("APIKEY", "", sdoForm);
                //ma4jgf2zu926c3ggw5dq9hpm"
                string sMethod = "";
                StringBuilder sData = new StringBuilder();

                sMethod = "POST";
                sData.Append("{" + "\"status\": \"ACTIVE\"," + "\"addresses\": [ ]," + "\"lists\": [{" + "\"id\": \"" + sListID + "\"," + "\"status\": \"ACTIVE\"" + "}]," + "\"email_addresses\": [{" + "\"status\": \"ACTIVE\"," + "\"email_address\": \"" + sEmail_Addresses + "\"" + "}]," + "\"first_name\": \"" + sFirst_Name + "\"," + "\"last_name\": \"" + sLast_Name + "\"," + "\"home_phone\": \"" + sHome_Phone + "\"" + "}");
                bool bRetError = false;
                httpPost(ref bRetError, sCreateContURI, sData, "POST", sdoForm);
                return true;

            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public string ConstCont_Contact_UpdateList(string sContactID, string sListID, string sEmail_Addresses, Form doForm = null)
        {
            //Update List of Contact
            try
            {
                StringBuilder sData = new StringBuilder();
                DataTable dtLists = ConstCont_Contact_GetLists(sContactID, doForm);
                int j = 0;
                string sListString = "";
                bool binList = false;
                string sURI = "";
                string sMethod = "";
                string sUpdateContURI = "https://api.constantcontact.com/v2/contacts/";
                bool bRetError = false;
                sURI = sUpdateContURI + sContactID + "?action_by=ACTION_BY_OWNER&api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
                //ma4jgf2zu926c3ggw5dq9hpm"
                sMethod = "PUT";
                for (j = 0; j <= dtLists.Rows.Count - 1; j++)
                {
                    DataRow row = dtLists.Rows[j];
                    if (sListID == row["id"].ToString())
                        binList = true;
                    if (j == 0)
                    {
                        sListString = "{\"id\": \"" + row["id"] + "\"," + "\"status\": \"" + row["status"] + "\"}";
                    }
                    else
                    {
                        sListString = sListString + "," + "{\"id\": \"" + row["id"] + "\"," + "\"status\": \"" + row["status"] + "\"}";
                    }
                }

                sListString = sListString + "," + "{\"id\": \"" + sListID + "\"," + "\"status\": \"ACTIVE\"}";
                sListString = sListString.TrimStart(',');
                sData.Append("{" + "\"id\": \"" + sContactID + "\"," + "\"status\": \"ACTIVE\"," + "\"lists\": [" + sListString + "]," + "\"email_addresses\": [{" + "\"status\": \"ACTIVE\"," + "\"email_address\": \"" + sEmail_Addresses + "\"" + "}]" + "}");
                httpPost(ref bRetError, sURI, sData, sMethod, doForm);


                return "";
            }
            catch (Exception ex)
            {
                return "";
            }
        }
        public string ConstCont_Contact_GetData(string sContactID, Form doForm = null)
        {

            string sResponse = "";

            try
            {
                string sGetContactURI = "https://api.constantcontact.com/v2/contacts/";
                StringBuilder sData = new StringBuilder();
                string sMethod = "GET";
                string sURL = "";

                sURL = sGetContactURI + sContactID + "?api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
                //ma4jgf2zu926c3ggw5dq9hpm"
                bool bRetError = false;
                sResponse = httpPost(ref bRetError, sURL, sData, sMethod, doForm);

                return sResponse;
            }
            catch (Exception ex)
            {
                return sResponse;
            }
        }
        public DataTable ConstCont_Contact_GetLists(string sContactID, Form doForm = null)
        {

            DataTable dtLists = new DataTable();

            try
            {
                string sGetContactURI = "https://api.constantcontact.com/v2/contacts/";
                string sResponse = "";
                StringBuilder sData = new StringBuilder();
                string sMethod = "GET";
                string sURL = "";

                sURL = sGetContactURI + sContactID + "?api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
                //ma4jgf2zu926c3ggw5dq9hpm"
                bool bRetError = false;
                sResponse = httpPost(ref bRetError, sURL, sData, sMethod, doForm);
                dtLists = ParseJSON_GetTable(sResponse, "lists");

                return dtLists;
            }
            catch (Exception ex)
            {
                return dtLists;
            }
        }
        public string ConstCont_Contact_GetIDbyEMail(string email, Form doForm = null)
        {

            string sGetContactURI = "https://api.constantcontact.com/v2/contacts?";
            StringBuilder sData = new StringBuilder();
            string sMethod = "GET";
            string sResponse = "";
            string sContactID = "";

            //Check if Email Already exists
            string sURL = "";
            sURL = sGetContactURI + "email=" + email + "&status=ALL&limit=1&api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
            //ma4jgf2zu926c3ggw5dq9hpm"
            bool bRetError = false;
            sResponse = httpPost(ref bRetError, sURL, sData, sMethod, doForm);
            sContactID = ParseJSON_ExistingData(sResponse, "id");

            return sContactID;
        }
        //public string ConstCont_Campaign_Create(string sListID, string sCampaignName, string sSubject, string sFromName, string sFromEmailAddress, string sReplyEmailAddress, string sEmailContent, string sTextContent, Form sdoform = null)
        //{
        //    string sCampID = "";

        //    try
        //    {
        //        StringBuilder sData = new StringBuilder();
        //        string sNewCampaignURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns?api_key=" + GetConsatntContactAutho("APIKEY", "", sdoform);
        //        //ma4jgf2zu926c3ggw5dq9hpm"
        //        string sResponse = "";
        //        bool bRetError = false;
        //        sEmailContent = sEmailContent.Trim();
        //        sEmailContent = Strings.Replace(sEmailContent, "\"", "\\\"");
        //        sEmailContent = Strings.Replace(sEmailContent, Constants.vbCrLf, "");
        //        sEmailContent = Strings.Replace(sEmailContent, Constants.vbTab, "\\t");

        //        sTextContent = sTextContent.Trim();
        //        sTextContent = Strings.Replace(sTextContent, "\"", "\\\"");
        //        sTextContent = Strings.Replace(sTextContent, Constants.vbTab, "\\t");
        //        sTextContent = Strings.Replace(sTextContent, Constants.vbCr, "\\n");
        //        sTextContent = Strings.Replace(sTextContent, Constants.vbLf, "\\n");
        //        sTextContent = Strings.Replace(sTextContent, Constants.vbCrLf, "\\n");
        //        sTextContent = "\\n" + sTextContent + " \\n ";
        //        //Add new line at the beginning and end of the text to avoid "~~~" coming on same line with text when email is sent.

        //        if (!sEmailContent.ToLower().Contains("<body"))
        //        {
        //            sEmailContent = "<body>" + sEmailContent + "</body>";
        //        }
        //        if (!sEmailContent.ToLower().Contains("<html"))
        //        {
        //            sEmailContent = "<html>" + sEmailContent + "</html>";
        //        }
        //        //sEmailContent = "<html><body>" & sEmailContent & "</body></html>"
        //        //sTextContent = Replace(sTextContent, vbCrLf, "")

        //        //"""template_type"": ""STOCK""," & _

        //        //-----------'VNK 01222016 adding escape char
        //        sSubject = sSubject.Replace("\"", "\\\"");
        //        sCampaignName = sCampaignName.Replace("\"", "\\\"");
        //        sFromName = sFromName.Replace("\"", "\\\"");

        //        //sData.Append("{" + "\"name\": \"" + sCampaignName + "\"," + "\"subject\": \"" + sSubject + "\"," + "\"status\": \"DRAFT\"," + "\"from_name\": \"" + sFromName + "\"," + "\"from_email\": \"" + sFromEmailAddress.ToLower() + "\"," + "\"reply_to_email\": \"" + sReplyEmailAddress.ToLower() + "\"," + "\"is_permission_reminder_enabled\": false," + "\"permission_reminder_text\": \"\"," + "\"is_view_as_webpage_enabled\": false," + "\"view_as_web_page_text\": \"Having trouble viewing this email?\"," + "\"view_as_web_page_link_text\": \"Click here\"," + "\"greeting_salutations\": \"Hello\"," + "\"greeting_name\": \"FIRST_NAME\"," + "\"greeting_string\": \"Dear\"," + "\"email_content\": \"" + sEmailContent + "\"," + "\"text_content\": \"~" + sTextContent + "\"," + "\"message_footer\": {" + "\"city\": \"HOUSTON\"," + "\"state\": \"TX\"," + "\"country\": \"US\"," + "\"organization_name\": \"Selltis Technologies\"," + "\"address_line_1\": \"9990 Richmond Ave, Suite 250 N\"," + "\"postal_code\": \"77042\"," + "\"include_forward_email\": true," + "\"forward_email_link_text\": \"Forward this email\"," + "\"include_subscribe_link\": true," + "\"subscribe_link_text\": \"Subscribe Me!\"" + "}," + "\"sent_to_contact_lists\": [{" + "\"id\": \"" + sListID + "\"" + "}]" + "}");
        //        sData.Append("{" + "\"name\": \"" + sCampaignName + "\"," + "\"subject\": \"" + sSubject + "\"," + "\"status\": \"DRAFT\"," + "\"from_name\": \"" + sFromName + "\"," + "\"from_email\": \"" + sFromEmailAddress.ToLower() + "\"," + "\"reply_to_email\": \"" + sReplyEmailAddress.ToLower() + "\"," + "\"is_permission_reminder_enabled\": false," + "\"permission_reminder_text\": \"\"," + "\"is_view_as_webpage_enabled\": false," + "\"view_as_web_page_text\": \"Having trouble viewing this email?\"," + "\"view_as_web_page_link_text\": \"Click here\"," + "\"greeting_salutations\": \"Hello\"," + "\"greeting_name\": \"FIRST_NAME\"," + "\"greeting_string\": \"Dear\"," + "\"email_content\": \"" + sEmailContent + "\"," + "\"text_content\": \"~" + sTextContent + "\"," + "\"message_footer\": {" + "\"city\": \"" + goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "CampaignFor_" + iCampaignFor.ToString + "_CITY", "") + "\"," + "\"state\": \"" + goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "CampaignFor_" + iCampaignFor.ToString + "_STATE", "") + "\"," + "\"country\": \"" + goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "CampaignFor_" + iCampaignFor.ToString + "_COUNTRY", "") + "\"," + "\"organization_name\": \"" + goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "CampaignFor_" + iCampaignFor.ToString + "_ORGANIZATION_NAME", "") + "\"," + "\"address_line_1\": \"" + goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "CampaignFor_" + iCampaignFor.ToString + "_ADDRESS_LINE_1", "") + "\"," + "\"postal_code\": \"" + goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "CampaignFor_" + iCampaignFor.ToString + "_POSTAL_CODE", "") + "\"," + "\"include_forward_email\": true," + "\"forward_email_link_text\": \"Forward this email\"," + "\"include_subscribe_link\": true," + "\"subscribe_link_text\": \"Subscribe Me!\"" + "}," + "\"sent_to_contact_lists\": [{" + "\"id\": \"" + sListID + "\"" + "}]" + "}");

        //        //"""message_footer"": {" & _
        //        //                        """city"": """ & ecc.city & """," & _
        //        //                        """state"": """ & ecc.state & """," & _
        //        //                        """country"": """ & ecc.country & """," & _
        //        //                        """organization_name"": """ & ecc.organization_name & """," & _
        //        //                        """address_line_1"": """ & ecc.address_line_1 & """," & _
        //        //                        """address_line_2"": """ & ecc.address_line_2 & """," & _
        //        //                        """address_line_3"": """ & ecc.address_line_3 & """," & _
        //        //                        """international_state"": """ & ecc.international_state & """," & _
        //        //                        """postal_code"": """ & ecc.postal_code & """," & _
        //        //                        """include_forward_email"": true," & _
        //        //                        """forward_email_link_text"": ""Forward this email""," & _
        //        //                        """include_subscribe_link"": true," & _
        //        //                        """subscribe_link_text"": ""Subscribe Me!""" & _
        //        //                    "}," & _

        //        //-----------'VNK 01132016 - Removing the special char
        //        foreach (char _char in sData.ToString())
        //        {
        //            int inc = Convert.ToInt32(Strings.AscW(_char));
        //            if (inc == 8203)
        //            {
        //                sData = sData.Replace(_char.ToString(), "");
        //            }
        //        }
        //        sResponse = httpPost(ref bRetError, sNewCampaignURI, sData, "POST", sdoform);
        //        if (string.IsNullOrEmpty(sResponse))
        //        {
        //            return "";
        //        }
        //        sCampID = ParseJSON(sResponse, "id");

        //        return sCampID;
        //    }
        //    catch (Exception ex)
        //    {
        //        return sCampID;
        //    }
        //}


        public string ConstCont_Campaign_Create(string sListID, string sCampaignName, string sSubject, string sFromName, string sFromEmailAddress, string sReplyEmailAddress, string sEmailContent, string sTextContent, Form sdoform = null)
        {
            string sCampID = "";

            try
            {
                StringBuilder sData = new StringBuilder();
                string sNewCampaignURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns?api_key=" + GetConsatntContactAutho("APIKEY", "", sdoform);
                //ma4jgf2zu926c3ggw5dq9hpm"
                string sResponse = "";
                bool bRetError = false;

                //VS ******** : Read Company Address details from Metadata OTH_CONSTANTCONTACT_DATA
                int iCampaignFor = 0;
                clRowSet doRelMC = new clRowSet("MC", clC.SELL_READONLY, "GID_ID='" + sdoform.doRS.GetFieldVal("LNK_RELATED_MC") + "'", "", "MLS_CAMPAIGNFOR");
                if (doRelMC.GetFirst() == 1)
                {
                    iCampaignFor = Convert.ToInt32(doRelMC.GetFieldVal("MLS_CAMPAIGNFOR", 2));
                }

                sEmailContent = sEmailContent.Trim();
                sEmailContent = Strings.Replace(sEmailContent, "\"", "\\\"");
                sEmailContent = Strings.Replace(sEmailContent, Constants.vbCrLf, "");
                sEmailContent = Strings.Replace(sEmailContent, Constants.vbTab, "\\t");
                sEmailContent = sEmailContent.Replace("\n", " ");

                sTextContent = sTextContent.Trim();
                sTextContent = Strings.Replace(sTextContent, "\"", "\\\"");
                sTextContent = Strings.Replace(sTextContent, Constants.vbTab, "\\t");
                sTextContent = Strings.Replace(sTextContent, Constants.vbCr, "\\n");
                sTextContent = Strings.Replace(sTextContent, Constants.vbLf, "\\n");
                sTextContent = Strings.Replace(sTextContent, Constants.vbCrLf, "\\n");
                sTextContent = "\\n" + sTextContent + " \\n ";
                //Add new line at the beginning and end of the text to avoid "~~~" coming on same line with text when email is sent.

                if (!sEmailContent.ToLower().Contains("<body"))
                {
                    sEmailContent = "<body>" + sEmailContent + "</body>";
                }
                if (!sEmailContent.ToLower().Contains("<html"))
                {
                    sEmailContent = "<html>" + sEmailContent + "</html>";
                }
                //sEmailContent = "<html><body>" & sEmailContent & "</body></html>"
                //sTextContent = Replace(sTextContent, vbCrLf, "")

                //"""template_type"": ""STOCK""," & _

                //-----------'VNK 01222016 adding escape char
                sSubject = sSubject.Replace("\"", "\\\"");
                sCampaignName = sCampaignName.Replace("\"", "\\\"");
                sFromName = sFromName.Replace("\"", "\\\"");

                sData.Append("{" + "\"name\": \"" + sCampaignName + "\"," + "\"subject\": \"" + sSubject + "\"," + "\"status\": \"DRAFT\"," + "\"from_name\": \"" + sFromName + "\"," + "\"from_email\": \"" + sFromEmailAddress.ToLower() + "\"," + "\"reply_to_email\": \"" + sReplyEmailAddress.ToLower() + "\"," + "\"is_permission_reminder_enabled\": false," + "\"permission_reminder_text\": \"\"," + "\"is_view_as_webpage_enabled\": false," + "\"view_as_web_page_text\": \"Having trouble viewing this email?\"," + "\"view_as_web_page_link_text\": \"Click here\"," + "\"greeting_salutations\": \"Hello\"," + "\"greeting_name\": \"FIRST_NAME\"," + "\"greeting_string\": \"Dear\"," + "\"email_content\": \"" + sEmailContent + "\"," + "\"text_content\": \"~" + sTextContent + "\"," + "\"message_footer\": {" + "\"city\": \"" + goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "CampaignFor_" + iCampaignFor.ToString() + "_CITY", "") + "\"," + "\"state\": \"" + goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "CampaignFor_" + iCampaignFor.ToString() + "_STATE", "") + "\"," + "\"country\": \"" + goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "CampaignFor_" + iCampaignFor.ToString() + "_COUNTRY", "") + "\"," + "\"organization_name\": \"" + goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "CampaignFor_" + iCampaignFor.ToString() + "_ORGANIZATION_NAME", "") + "\"," + "\"address_line_1\": \"" + goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "CampaignFor_" + iCampaignFor.ToString() + "_ADDRESS_LINE_1", "") + "\"," + "\"postal_code\": \"" + goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "CampaignFor_" + iCampaignFor.ToString() + "_POSTAL_CODE", "") + "\"," + "\"include_forward_email\": true," + "\"forward_email_link_text\": \"Forward this email\"," + "\"include_subscribe_link\": true," + "\"subscribe_link_text\": \"Subscribe Me!\"" + "}," + "\"sent_to_contact_lists\": [{" + "\"id\": \"" + sListID + "\"" + "}]" + "}");

                //"""message_footer"": {" & _
                //            """city"": ""HOUSTON""," & _
                //            """state"": ""TX""," & _
                //            """country"": ""US""," & _
                //            """organization_name"": ""SalesProcess360""," & _
                //            """organization_name"": """ & goMeta.LineRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA", "ORGANIZATION_NAME_" & iCampaignFor.ToString, "") & """," & _
                //            """address_line_1"": ""9990 Richmond Ave, Suite 250 N""," & _
                //            """postal_code"": ""77042""," & _
                //            """include_forward_email"": true," & _
                //            """forward_email_link_text"": ""Forward this email""," & _
                //            """include_subscribe_link"": true," & _
                //            """subscribe_link_text"": ""Subscribe Me!""" & _
                //        "}," & _

                //"""organization_name"": ""Selltis Technologies""," & _

                //"""message_footer"": {" & _
                //                        """city"": """ & ecc.city & """," & _
                //                        """state"": """ & ecc.state & """," & _
                //                        """country"": """ & ecc.country & """," & _
                //                        """organization_name"": """ & ecc.organization_name & """," & _
                //                        """address_line_1"": """ & ecc.address_line_1 & """," & _
                //                        """address_line_2"": """ & ecc.address_line_2 & """," & _
                //                        """address_line_3"": """ & ecc.address_line_3 & """," & _
                //                        """international_state"": """ & ecc.international_state & """," & _
                //                        """postal_code"": """ & ecc.postal_code & """," & _
                //                        """include_forward_email"": true," & _
                //                        """forward_email_link_text"": ""Forward this email""," & _
                //                        """include_subscribe_link"": true," & _
                //                        """subscribe_link_text"": ""Subscribe Me!""" & _
                //                    "}," & _

                //-----------'VNK 01202016 - Removing the special char
                foreach (char _char in sData.ToString())
                {
                    int inc = Convert.ToInt32(Strings.AscW(_char));
                    if (inc == 8203)
                    {
                        sData = sData.Replace(_char.ToString(), "");
                    }
                }


                sResponse = httpPost(ref bRetError, sNewCampaignURI, sData, "POST", sdoform);
                if (string.IsNullOrEmpty(sResponse))
                {
                    return "";
                }
                sCampID = ParseJSON(sResponse, "id");

                return sCampID;
            }
            catch (Exception ex)
            {
                return sCampID;
            }
        }
        public bool ConstCont_Campaign_CheckExistance(string sCampaignName, Form doForm = null)
        {
            try
            {
                string sExistingCampaignURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns?status=ALL&api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
                //ma4jgf2zu926c3ggw5dq9hpm"
                StringBuilder sData = new StringBuilder();
                string sCampID = "";
                bool bRetError = false;
                string sResponse = httpPost(ref bRetError, sExistingCampaignURI, sData, "GET", doForm);
                DataTable _data = ParseJSON_ResultDataSet(sResponse);
                sCampaignName = sCampaignName.Replace("'", "");
                DataRow[] _dtrow = _data.Select("name = '" + sCampaignName + "'");
                if (_dtrow.Length > 0)
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public string ConstCont_Campaign_GetStatus(string sCampaignID, Form doForm = null)
        {

            string sExistingCampaignURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns/" + sCampaignID + "?api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
            //ma4jgf2zu926c3ggw5dq9hpm"
            string sScheduleURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns/";
            string sURI = sScheduleURI + sCampaignID + "/schedules?api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
            //ma4jgf2zu926c3ggw5dq9hpm"
            StringBuilder sData = new StringBuilder();

            //Get Email Campaign Status
            bool sRetError = false;
            string sResponse = httpPost(ref sRetError, sExistingCampaignURI, sData, "GET", doForm);
            string sStatus = ParseJSON_ExistingData(sResponse, "status", "root");

            //Dim sExistingCampaignURI As String = "https://api.constantcontact.com/v2/emailmarketing/campaigns?status=ALL&api_key=" & GetConsatntContactAutho("APIKEY", "")    'ma4jgf2zu926c3ggw5dq9hpm"
            //Dim sData As New StringBuilder
            //Dim sCampID As String = ""
            //Dim sStatus As String = ""

            //Dim sResponse As String = httpPost(sExistingCampaignURI, sData, "GET")
            //sCampID = ParseJSON_ExistingData(sResponse, "id")

            //Get Email Campaign Status
            //sResponse = httpPost(sExistingCampaignURI, sData, "GET")
            //sStatus = ParseJSON_ExistingData(sResponse, "status", "root")
            return sStatus;
        }
        public bool ConstCont_Campaign_IsExisted(string sCampaignID, Form doForm = null)
        {

            string sExistingCampaignURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns/" + sCampaignID + "?api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
            //ma4jgf2zu926c3ggw5dq9hpm"
            string sScheduleURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns/";
            string sURI = sScheduleURI + sCampaignID + "/schedules?api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
            //ma4jgf2zu926c3ggw5dq9hpm"
            StringBuilder sData = new StringBuilder();

            //Get Email Campaign Status
            bool IsExisted = true;
            string sResponse = httpPost(ref IsExisted, sExistingCampaignURI, sData, "GET", doForm);
            IsExisted = !IsExisted;
            return IsExisted;
        }
        public string ConstCont_Schedule_Create(string sCampaignID, System.DateTime sDate, Form sdoform = null)
        {
            try
            {
                string sScheduleURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns/";
                string sURI = sScheduleURI + sCampaignID + "/schedules?api_key=" + GetConsatntContactAutho("APIKEY", "", sdoform);
                //ma4jgf2zu926c3ggw5dq9hpm"
                string sResponse = "";
                StringBuilder sData = new StringBuilder();
                PublicDomain.TzTimeZone zone = default(PublicDomain.TzTimeZone);
                DateTime dtDateTime = default(DateTime);
                string sDateTime = null;
                string sCurrentUser = goP.GetMe("ID");
                int par_iValid = 4;
                bool bRetError = false;
                zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
                dtDateTime = goTR.UTC_LocalToUTC(ref sDate);
                sDateTime = goTR.DateToString(dtDateTime.Date, "", ref par_iValid) + "T" + dtDateTime.TimeOfDay.ToString("hh\\:mm\\:ss") + ".000Z";

                sData.Append("{" + "\"scheduled_date\": \"" + sDateTime + "\"" + "}");

                sResponse = httpPost(ref bRetError, sURI, sData, "POST", sdoform);
                if (string.IsNullOrEmpty(sResponse))
                {
                    return "";
                }
                string retID = ParseJSON(sResponse, "id");
                return retID;
                return "";
            }
            catch (Exception ex)
            {
                return "";
            }
        }
        public string ConstCont_Schedule_Delete(string sCampaignID, string scheduleId, Form sdoform = null)
        {
            try
            {
                //https://api.constantcontact.com/v2/emailmarketing/campaigns/{campaignId}/schedules/{scheduleId}
                string sUnScheduleURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns/";
                string sURI = sUnScheduleURI + sCampaignID + "/schedules/" + scheduleId + "?api_key=" + GetConsatntContactAutho("APIKEY", "", sdoform);
                //ma4jgf2zu926c3ggw5dq9hpm"
                string sResponse = "";
                StringBuilder sData = new StringBuilder();
                bool bRetError = false;
                sResponse = httpPost(ref bRetError, sURI, sData, "DELETE", sdoform);
                if (string.IsNullOrEmpty(sResponse))
                {
                    return "";
                }
                return sResponse;
                return "";
            }
            catch (Exception ex)
            {
                return "";
            }
        }
        public string ConstCont_TestEMail_Send(string sCampaignID, string sSendHTMLTEXT, string sEMailAddressesCSV, string sPersonalMessage, Form sdoform = null)
        {
            //Purpose: Send Test EMail from Constant Contact
            //Parameters
            //sCampaignID - Campaing ID to Test Send EMail
            //sSendHTMLTEXT - Format of the Test EMail. Supports HTML,TEXT, HTML_AND_TEXT
            //sEMailAddressesCSV - Comma Separated string of <NAME_EMAIL>,<EMAIL>
            //sPersonalMessage - Personal MEssage attached at the beginning of the EMail

            try
            {
                //https://api.constantcontact.com/v2/emailmarketing/campaigns/{campaignId}/schedules/{scheduleId}
                string sTestURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns/";
                string sURI = sTestURI + sCampaignID + "/tests?api_key=" + GetConsatntContactAutho("APIKEY", "", sdoform);
                //ma4jgf2zu926c3ggw5dq9hpm"
                string sResponse = "";
                StringBuilder sData = new StringBuilder();
                sPersonalMessage = sPersonalMessage.Replace(Constants.vbCrLf, "\\n");
                sPersonalMessage = sPersonalMessage.Replace(Constants.vbCr, "\\n");
                sPersonalMessage = sPersonalMessage.Replace(Constants.vbLf, "\\n");
                sPersonalMessage = sPersonalMessage.Replace(Strings.Chr(4).ToString(), "");
                //replace EOT

                string[] sEmails = sEMailAddressesCSV.Split(',');
                string sCleanEmailList = "";

                foreach (string Str in sEmails)
                {
                    if (ValidateEmail(Str) == true)
                    {
                        if (string.IsNullOrEmpty(sCleanEmailList))
                        {
                            sCleanEmailList = "\"" + Str.Trim() + "\"";
                        }
                        else
                        {
                            sCleanEmailList = "\"" + Str.Trim() + "\"," + sCleanEmailList;
                        }
                    }
                }


                sData.Append("{" + (!string.IsNullOrEmpty(sPersonalMessage.Trim()) ? "\"personal_message\": \"\\n " + sPersonalMessage.Trim() + " \\n\"," : "") + "\"email_addresses\": [" + sCleanEmailList + "]," + "\"format\": \"" + sSendHTMLTEXT + "\"" + "}");
                bool bRetError = false;
                if (!string.IsNullOrEmpty(sCleanEmailList))
                {
                    sResponse = httpPost(ref bRetError, sURI, sData, "POST", sdoform);
                }

                if (string.IsNullOrEmpty(sResponse))
                {
                    return "";
                }
                return sResponse;
                return "";
            }
            catch (Exception ex)
            {
                return "";
            }
        }
        public string ParseJSON(string sJSONString, string sParamter)
        {

            string response = "{root:" + sJSONString + "}";
            JavaScriptSerializer js = new JavaScriptSerializer();
            XmlDocument xd1 = new XmlDocument();
            xd1 = (XmlDocument)Newtonsoft.Json.JsonConvert.DeserializeXmlNode(response, "root");
            DataSet jsondataset = new DataSet();
            jsondataset.ReadXml(new XmlNodeReader(xd1));
            string st = jsondataset.Tables[0].Rows[0][sParamter].ToString();

            return st;
        }
        public string ParseJSON_ExistingData(string sJSONString, string sParamter, string sTableName = "results")
        {

            string st = "";
            string response = "{root:" + sJSONString + "}";
            System.Web.Script.Serialization.JavaScriptSerializer js = new System.Web.Script.Serialization.JavaScriptSerializer();
            System.Xml.XmlDocument xd1 = new System.Xml.XmlDocument();
            xd1 = (System.Xml.XmlDocument)Newtonsoft.Json.JsonConvert.DeserializeXmlNode(response, "root");
            DataSet jsondataset = new DataSet();
            jsondataset.ReadXml(new System.Xml.XmlNodeReader(xd1));
            if (jsondataset.Tables.Contains(sTableName))
            {
                st = jsondataset.Tables[sTableName].Rows[0][sParamter].ToString();
            }

            return st;
        }
        public DataTable ParseJSON_ResultDataSet(string sJSONString)
        {

            DataTable dtResults = new DataTable();
            string response = "{root:" + sJSONString + "}";
            JavaScriptSerializer js = new JavaScriptSerializer();
            XmlDocument xd1 = new XmlDocument();
            xd1 = (XmlDocument)Newtonsoft.Json.JsonConvert.DeserializeXmlNode(response, "root");
            DataSet jsondataset = new DataSet();
            jsondataset.ReadXml(new XmlNodeReader(xd1));
            if (jsondataset.Tables.Contains("results"))
            {
                dtResults = jsondataset.Tables["results"];
            }

            return dtResults;
        }
        public DataTable ParseJSON_DataSet(string sJSONString)
        {

            DataTable dtResults = new DataTable();
            string response = "{root:" + sJSONString + "}";
            JavaScriptSerializer js = new JavaScriptSerializer();
            XmlDocument xd1 = new XmlDocument();
            xd1 = (XmlDocument)Newtonsoft.Json.JsonConvert.DeserializeXmlNode(response, "root");
            DataSet jsondataset = new DataSet();
            jsondataset.ReadXml(new XmlNodeReader(xd1));
            if (jsondataset.Tables.Count > 0)
            {
                dtResults = jsondataset.Tables[0];
            }

            return dtResults;
        }
        public DataTable ParseJSON_GetTable(string sJSONString, string sTableName)
        {

            DataTable dtResults = new DataTable();
            string response = "{root:" + sJSONString + "}";
            JavaScriptSerializer js = new JavaScriptSerializer();
            XmlDocument xd1 = new XmlDocument();
            xd1 = (XmlDocument)Newtonsoft.Json.JsonConvert.DeserializeXmlNode(response, "root");
            DataSet jsondataset = new DataSet();
            jsondataset.ReadXml(new XmlNodeReader(xd1));
            if (jsondataset.Tables.Contains(sTableName))
            {
                dtResults = jsondataset.Tables[sTableName];
            }

            return dtResults;
        }
        public string GetSelectedListID(string sSelectedListName, Form doForm = null)
        {

            string sResponse = "";
            StringBuilder sData = new StringBuilder();
            string sSelectedListID = "";

            //Check for List Name Pre-Existance
            string sURI = "https://api.constantcontact.com/v2/lists?api_key=" + GetConsatntContactAutho("APIKEY", "", doForm);
            //ma4jgf2zu926c3ggw5dq9hpm"
            bool bRetError = false;
            sResponse = httpPost(ref bRetError, sURI, sData, "GET", doForm);

            DataTable dtLists = new DataTable();
            dtLists = ParseJSON_DataSet(sResponse);

            foreach (DataRow row in dtLists.Rows)
            {
                if (row["name"].ToString() == sSelectedListName)
                {
                    sSelectedListID = row["id"].ToString();
                    break; // TODO: might not be correct. Was : Exit For
                }
            }

            return sSelectedListID;
        }
        //VNK 08272015 sRetError Added
        public string httpPost(ref bool sRetError, string URI, StringBuilder s, string sOperation = "POST", Form sdoform = null, string CampaignFor = "")
        {
            try
            {
                string contentType = "application/json";
                Stream dataStream = default(Stream);
                WebResponse response = default(WebResponse);
                // Create a request for the URL. 
                URI = URI.Replace(" ", "");
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(URI);
                // set the credentials.
                //request.Headers.Add("Authorization", "Bearer 2cc3ac20-f55d-43eb-84ad-200a1577dd3b")
                request.Headers.Add("Authorization", "Bearer " + GetConsatntContactAutho("AccessToken", CampaignFor, sdoform));
                // Add Request Header
                request.Accept = "application/json";
                request.Timeout = 100000;
                string st = s.ToString();
                if (!String.IsNullOrEmpty(st))
                {
                    // Set the Method property of the request to POST.
                    request.Method = sOperation;
                    request.Timeout = 190000;
                    //SetCredentials(Authdata, ref request);
                    // Create POST data and convert it to a byte array.    
                    byte[] byteArray = Encoding.UTF8.GetBytes(st);
                    // Set the ContentType property of the WebRequest.
                    request.ContentType = contentType;
                    // Set the ContentLength property of the WebRequest.
                    request.ContentLength = byteArray.Length;
                    // Get the request stream.
                    dataStream = request.GetRequestStream();
                    // Write the data to the request stream.            
                    dataStream.Write(byteArray, 0, byteArray.Length);
                    // Close the Stream object.
                    dataStream.Close();
                    // Get the response.
                    response = request.GetResponse();
                    // Display the status.
                    //Console.WriteLine(DirectCast(response, HttpWebResponse).StatusDescription)
                }
                else
                {
                    request.Method = sOperation;
                    response = request.GetResponse();
                }

                // Get the stream containing content returned by the server.
                dataStream = response.GetResponseStream();
                // Open the stream using a StreamReader for easy access.
                StreamReader reader = new StreamReader(dataStream);
                // Read the content.
                string responseFromServer = reader.ReadToEnd();
                // Display the content.
                //Console.WriteLine(responseFromServer)
                // Clean up the streams.
                reader.Close();
                dataStream.Close();
                response.Close();
                //Console.Write(responseFromServer)
                sRetError = false;
                return responseFromServer;

            }
            catch (WebException ex)
            {
                if (ex.Response != null)
                {
                    //' can use ex.Response.Status, .StatusDescription
                    if (ex.Response.ContentLength != 0)
                    {
                        using (Stream stream = ex.Response.GetResponseStream())
                        {
                            using (StreamReader reader = new StreamReader(stream))
                            {
                                string sEXmsg = reader.ReadToEnd();
                                sEXmsg = ParseJSON(sEXmsg, "error_message");

                                if (sRetError)
                                {
                                    sRetError = true;
                                    return sEXmsg;
                                }
                                if ((sdoform != null))
                                {
                                    Form sdfrom = (Form)sdoform;
                                    sdfrom.MessageBox(sEXmsg);
                                    return "";
                                }
                                return "";
                            }
                        }
                    }
                }
                return "";
            }
        }
        public string LoadJsonBulkContacts(DataTable sContacts, string sList)
        {

            string sData = "{" + "\"import_data\": [";

            if ((sContacts != null))
            {
                if (sContacts.Rows.Count > 0)
                {
                    for (int index = 0; index <= sContacts.Rows.Count - 1; index++)
                    {
                        sData = sData + "{" + "\"email_addresses\": [" + "\"" + sContacts.Rows[index]["EML_EMAIL"].ToString() + "\"" + "]," + "\"first_name\": \"" + sContacts.Rows[index]["TXT_NAMEFIRST"].ToString() + "\"," + "\"last_name\": \"" + sContacts.Rows[index]["TXT_NAMELAST"].ToString() + "\"," + "\"job_title\": \"\"," + "\"company_name\": \"\"," + "\"home_phone\": \"\"" + "},";
                    }
                }
            }

            sData = sData.TrimEnd(',');
            sData = sData + "],\"lists\":[" + "\"" + sList + "\"],";
            sData = sData + "\"column_names\": [" + "\"EMAIL\"," + "\"FIRST NAME\"," + "\"LAST NAME\"," + "\"JOB TITLE\"," + "\"COMPANY NAME\"," + "\"HOME PHONE\"" + "]}";

            return sData;
        }

        public string BulkUploadContacts(DataTable sContacts, string sList, object sdoform = null)
        {
            string sURI = "https://api.constantcontact.com/v2/activities/addcontacts?api_key=" + GetConsatntContactAutho("APIKEY", "", sdoform);
            //ma4jgf2zu926c3ggw5dq9hpm"
            string sMethod = "Post";
            //Dim sData As String = LoadJsonBulkContacts(sContacts, sList)
            string sData = goP.GetVar("par_ClstData").ToString();
            sData = sData.Replace("AlistsZ1", sList);
            //AlistsZ1
            StringBuilder _sdata = new StringBuilder();
            _sdata.Append(sData);

            bool bRetError = false;
            return httpPost(ref bRetError, sURI, _sdata, sMethod, (Form)sdoform);
        }
        public bool ValidateEmail(string email)
        {
            System.Text.RegularExpressions.Regex emailRegex = new System.Text.RegularExpressions.Regex("^(?<user>[^@]+)@(?<host>.+)$");
            System.Text.RegularExpressions.Match emailMatch = emailRegex.Match(email);
            return emailMatch.Success;
        }

        public bool CS_FormControlOnChange_LNK_Related_MC(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            clRowSet ssClrs = new clRowSet("MC", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_Related_MC") + "'", "", "LNK_Related_GR");
            if (ssClrs.GetFirst() == 1)
            {
                doForm.doRS.SetFieldVal("LNK_Related_GR", ssClrs.GetFieldVal("LNK_Related_GR"));
            }
            doForm.SetControlVal("NDB_TXT_LISTNAME", "");
            doForm.doRS.SetFieldVal("TXT_CCLISTID", "");
            doForm.doRS.SetFieldVal("TXT_CCCAMPAIGNID", "");
            doForm.SetControlState("BTN_CCNEWLIST_ADD", 0);
            doForm.doRS.ClearLinkAll("LNK_CONNECTED_CN");

            return true;
        }
        public bool CS_FormControlOnChange_LNK_Related_GR(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            doForm.SetControlVal("NDB_TXT_LISTNAME", "");
            doForm.doRS.SetFieldVal("TXT_CCLISTID", "");
            doForm.doRS.SetFieldVal("TXT_CCCAMPAIGNID", "");
            doForm.SetControlState("BTN_CCNEWLIST_ADD", 0);
            doForm.doRS.ClearLinkAll("LNK_CONNECTED_CN");

            return true;
        }
        public bool CS_FormControlOnChange_BTN_Track(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // EmailCampaignTracking(doForm.doRS.GetFieldVal("TXT_CCCAMPAIGNID"))
            dynamic CampaignFor = goData.GetFieldValueFromRec(doForm.doRS.GetFieldVal("LNK_RELATED_MC").ToString(), "MLS_CAMPAIGNFOR", 2);
            if (CampaignFor != null)
            {
                EmailCampaignTrackingM(doForm.doRS.GetFieldVal("TXT_CCCAMPAIGNID").ToString(), CampaignFor.ToString());
            }

            return true;
        }
        public bool CS_FormControlOnChange_BTN_TrackAll(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            dynamic CampaignFor = goData.GetFieldValueFromRec(doForm.doRS.GetFieldVal("LNK_RELATED_MC").ToString(), "MLS_CAMPAIGNFOR", 2);
            EmailCampaignTrackingM("", CampaignFor);

            return true;
        }
        public string ConstantCampaignStatistics(string sCampaignID, string _lCampaignFor = "")
        {
            //try
            //{
                string sTrackingURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns/" + sCampaignID + "/tracking/reports/summary?api_key=" + GetConsatntContactAutho("APIKEY", _lCampaignFor);
                //ma4jgf2zu926c3ggw5dq9hpm"
                bool bRetError = false;
                string sResponse = httpPost(ref bRetError, sTrackingURI, new StringBuilder(), "GET", CampaignFor: _lCampaignFor);

                string _TXT_EmailDropSYS_Name = string.Empty;
                string sMD = goMeta.PageRead("GLOBAL", "OTH_CCCAMPAIGNTRACKING", "", false, "XX");
                goTR.StrWrite(ref sMD, "CCCount", "(In CCCstati)(" + sCampaignID + ")");
                goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");

                string sends = string.Empty;
                string Opens = string.Empty;
                string clicks = string.Empty;
                string forwards = string.Empty;
                string unsubscribes = string.Empty;
                string bounces = string.Empty;
                string spam_count = string.Empty;
                string sCCNameID = string.Empty;
                //doForm.doRS.GetFieldVal("LNK_RELATED_MC")

                clRowSet stCNRS = new clRowSet("CS", 1, "TXT_CCCAMPAIGNID='" + sCampaignID + "'", "", "");
                if (stCNRS.GetFirst() == 1)
                {
                    stCNRS.SetFieldVal("MLS_STATUS", "SENT");
                    //sCCNameID = stCNRS.GetFieldVal("LNK_RELATED_MC")

                    sends = ParseJSON(sResponse, "sends");
                    Opens = ParseJSON(sResponse, "Opens");
                    clicks = ParseJSON(sResponse, "clicks");
                    forwards = ParseJSON(sResponse, "forwards");
                    unsubscribes = ParseJSON(sResponse, "unsubscribes");
                    bounces = ParseJSON(sResponse, "bounces");
                    spam_count = ParseJSON(sResponse, "spam_count");

                    _TXT_EmailDropSYS_Name = stCNRS.GetFieldVal("TXT_CONSTANTCONTACTCAMPAIGNNAME").ToString();

                    stCNRS.SetFieldVal("BI__Sends", sends);
                    stCNRS.SetFieldVal("BI__Opens", Opens);
                    stCNRS.SetFieldVal("BI__clicks", clicks);
                    stCNRS.SetFieldVal("BI__forwards", forwards);
                    stCNRS.SetFieldVal("BI__unsubscribes", unsubscribes);
                    stCNRS.SetFieldVal("BI__bounces", bounces);
                    stCNRS.SetFieldVal("BI__spamcount", spam_count);

                    if (Convert.ToInt64(sends) != 0)
                    {
                        //try
                        //{
                            dynamic _Openrate = (Convert.ToInt64(Opens) / Convert.ToInt64(sends)) * 100;
                            dynamic _Clickrate = (Convert.ToInt64(clicks) / Convert.ToInt64(sends)) * 100;
                            dynamic _Forwardrate = (Convert.ToInt64(forwards) / Convert.ToInt64(sends)) * 100;
                            dynamic _Unsubscriberate = (Convert.ToInt64(unsubscribes) / Convert.ToInt64(sends)) * 100;
                            dynamic _Bouncerate = (Convert.ToInt64(bounces) / Convert.ToInt64(sends)) * 100;

                            stCNRS.SetFieldVal("SR__Openrate", _Openrate);
                            stCNRS.SetFieldVal("SR__Clickrate", _Clickrate);
                            stCNRS.SetFieldVal("SR__Forwardrate", _Forwardrate);
                            stCNRS.SetFieldVal("SR__Unsubscriberate", _Unsubscriberate);
                            stCNRS.SetFieldVal("SR__Bouncerate", _Bouncerate);

                        //}
                        //catch (Exception ex)
                        //{
                        //}
                    }
                    stCNRS.Commit();
                }

                goTR.StrWrite(ref sMD, "CCCount", "(END CCCstati)(" + sCampaignID + ")");
                goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                return _TXT_EmailDropSYS_Name;

            //}
            //catch (Exception ex)
            //{
            //}
            return string.Empty;

        }

        public bool MarketingCampaignStatistics(string sMarketingCampaignID = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sWhere = "";
            if (!string.IsNullOrEmpty(sMarketingCampaignID))
            {
                sWhere = "GID_ID='" + sMarketingCampaignID + "'";
            }


            try
            {
                clRowSet stCNRS = new clRowSet("MC", clC.SELL_EDIT, sWhere, "", "");
                if (stCNRS.GetFirst() == 1)
                {
                    do
                    {

                        double _Tsends = 0;
                        double _TOpens = 0;
                        double _Tclicks = 0;
                        double _Tforwards = 0;
                        double _Tunsubscribes = 0;
                        double _Tbounces = 0;
                        double _Tspam_count = 0;

                        clRowSet CSstCNRS = new clRowSet("CS", 1, "LNK_RELATED_MC='" + stCNRS.GetFieldVal("GID_ID") + "'", "", "");
                        if (CSstCNRS.GetFirst() == 1)
                        {
                            do
                            {
                                _Tsends = Convert.ToDouble(CSstCNRS.GetFieldVal("BI__Sends")) + _Tsends;
                                _TOpens = Convert.ToDouble(CSstCNRS.GetFieldVal("BI__Opens")) + _TOpens;
                                _Tclicks = Convert.ToDouble(CSstCNRS.GetFieldVal("BI__clicks")) + _Tclicks;
                                _Tforwards = Convert.ToDouble(CSstCNRS.GetFieldVal("BI__forwards")) + _Tforwards;
                                _Tunsubscribes = Convert.ToDouble(CSstCNRS.GetFieldVal("BI__unsubscribes")) + _Tunsubscribes;
                                _Tbounces = Convert.ToDouble(CSstCNRS.GetFieldVal("BI__bounces")) + _Tbounces;
                                _Tspam_count = Convert.ToDouble(CSstCNRS.GetFieldVal("BI__spamcount")) + _Tspam_count;

                                if (CSstCNRS.GetNext() == 0)
                                    break; // TODO: might not be correct. Was : Exit Do
                            } while (true);
                        }

                        //stCNRS.SetFieldVal("BI__Sends", _Tsends.ToString())
                        //stCNRS.SetFieldVal("BI__Opens", _TOpens.ToString())
                        //stCNRS.SetFieldVal("BI__clicks", _Tclicks.ToString())
                        //stCNRS.SetFieldVal("BI__forwards", _Tforwards.ToString())
                        //stCNRS.SetFieldVal("BI__unsubscribes", _Tunsubscribes.ToString())
                        //stCNRS.SetFieldVal("BI__bounces", _Tbounces.ToString())
                        //stCNRS.SetFieldVal("BI__spamcount", _Tspam_count.ToString())

                        stCNRS.SetFieldVal("BI__EmailRecipients", _Tsends.ToString());
                        stCNRS.SetFieldVal("BI__EmailOpened", _TOpens.ToString());
                        stCNRS.SetFieldVal("BI__EmailClickThru", _Tclicks.ToString());
                        stCNRS.SetFieldVal("BI__Emailforwards", _Tforwards.ToString());
                        stCNRS.SetFieldVal("BI__EmailUnsubscribe", _Tunsubscribes.ToString());
                        stCNRS.SetFieldVal("BI__EmailBounce", _Tbounces.ToString());
                        stCNRS.SetFieldVal("BI__Emailspamcount", _Tspam_count.ToString());
                        if (_Tsends != 0)
                        {
                            //try
                            //{
                                dynamic _Openrate = (_TOpens / _Tsends) * 100;
                                dynamic _Clickrate = (_Tclicks / _Tsends) * 100;
                                dynamic _Forwardrate = (_Tforwards / _Tsends) * 100;
                                dynamic _Unsubscriberate = (_Tunsubscribes / _Tsends) * 100;
                                dynamic _Bouncerate = (_Tbounces / _Tsends) * 100;

                                stCNRS.SetFieldVal("SR__Openrate", _Openrate);
                                stCNRS.SetFieldVal("SR__Clickrate", _Clickrate);
                                stCNRS.SetFieldVal("SR__Forwardrate", _Forwardrate);
                                stCNRS.SetFieldVal("SR__Unsubscriberate", _Unsubscriberate);
                                stCNRS.SetFieldVal("SR__Bouncerate", _Bouncerate);

                            //}
                            //catch (Exception ex)
                            //{
                            //}
                        }
                        if (stCNRS.Commit() == 0)
                        {
                        }
                        if (stCNRS.GetNext() == 0)
                            break; // TODO: might not be correct. Was : Exit Do
                    } while (true);
                }
            }
            catch (Exception ex)
            {
                return false;
            }
            return true;
        }

        public bool EmailCampaignTracking_NewCampaigns(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //To-Do load Campaign For

            //Dim _CAMPAIGNFOR As String = "LST_MC:CAMPAIGNFOR"

            //Dim _CAMPAIGNFORMETA As String = goMeta.PageRead("GLOBAL", _CAMPAIGNFOR, , , "XX")
            //Dim _aCAMPAIGNFOR() As String = _CAMPAIGNFORMETA.Split(vbCrLf)
            //Dim _aCAMPAIGNFORMaxCount As Integer = _aCAMPAIGNFOR.Length

            //Dim i As Integer = 0
            //Dim iMaxNum As Integer = 0
            //Dim sCAMPAIGNFORCurr As String = ""
            //Dim sCurrNum As String = ""
            //Dim NumLength As Integer = 0
            //Dim NumStart As Integer = 0
            //Dim iCurrNum As Integer = 0

            //For i = 0 To _aCAMPAIGNFORMaxCount - 1
            //    sCAMPAIGNFORCurr = _aCAMPAIGNFOR(i).Trim
            //    If sCAMPAIGNFORCurr.Length > 0 Then
            //        sCurrNum = sCAMPAIGNFORCurr.Substring(0, (InStr(sCAMPAIGNFORCurr, "=") - 1))
            //        NumStart = InStr(sCurrNum, "_")
            //        NumLength = Len(sCurrNum) - NumStart
            //        sCurrNum = sCurrNum.Substring(NumStart, NumLength)

            //        If IsNumeric(sCurrNum) Then
            //            iCurrNum = goTR.StringToNum(sCurrNum)
            //            If iCurrNum > iMaxNum Then
            //                iMaxNum = iCurrNum
            //            End If
            //        End If
            //    End If
            //Next


            //Dim sMD As String = goMeta.PageRead("GLOBAL", "OTH_CCCAMPAIGNTRACKING", , , "XX")
            //For index = 0 To iMaxNum
            //    goTR.StrWrite(sMD, "CCCount", "Started for " & index)
            //    goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, , , "XX")
            //    EmailCampaignTrackingM("", index.ToString())
            //    goTR.StrWrite(sMD, "CCCount", "End for " & index)
            //    goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, , , "XX")
            //Next

            //Track campaigns not older than a week
            clArray doMCArray = default(clArray);
            string sCurrentUser = goP.GetMe("ID");
            PublicDomain.TzTimeZone zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
            DateTime dtOldDate = zone.ToLocalTime(System.DateTime.UtcNow);
            //Go back 1 week
            dtOldDate = DateAndTime.DateAdd(DateInterval.Day, -2, dtOldDate);
            sDelim = "|";
            string sOldDate = goTR.DateTimeToSysString(dtOldDate, ref par_iValid, ref sDelim);

            goMeta.LineWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", "CCCount", "Started", ref par_oConnection);
            clRowSet doCSRS = new clRowSet("CS", clC.SELL_READONLY, "MLS_STATUS=3 AND DTT_SCHEDULEDDATE>'" + sOldDate + "'", "DTT_SCHEDULEDDATE DESC", "TXT_CCCAMPAIGNID");
            if (doCSRS.GetFirst() == 1)
            {
                do
                {
                    EmailCampaignTrackingM(doCSRS.GetFieldVal("TXT_CCCAMPAIGNID").ToString());
                    doMCArray = new clArray();
                    doMCArray = (clArray)doCSRS.GetFieldVal("LNK_RELATED_MC", 2);
                    for (int i = 1; i <= doMCArray.GetDimension(); i++)
                    {
                        MarketingCampaignStatistics(doMCArray.GetItem(i));
                    }
                    if (doCSRS.GetNext() == 0)
                        break; // TODO: might not be correct. Was : Exit Do
                } while (true);
            }
            goMeta.LineWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", "CCCount", "Ended", ref par_oConnection);
            sDelim = "|";
            goMeta.LineWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", "LastRunDate", goTR.DateTimeToSysString(goTR.NowUTC(), ref par_iValid, ref sDelim), ref par_oConnection);

            return true;

        }

        public bool EmailCampaignTracking_OldCampaigns(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //Track campaigns older than a week
            clArray doMCArray = default(clArray);
            string sCurrentUser = goP.GetMe("ID");
            PublicDomain.TzTimeZone zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
            DateTime dtOldDate = zone.ToLocalTime(System.DateTime.UtcNow);
            //Go back 1 week
            dtOldDate = DateAndTime.DateAdd(DateInterval.Day, -2, dtOldDate);
            sDelim = "|";
            string sOldDate = goTR.DateTimeToSysString(dtOldDate, ref par_iValid, ref sDelim);

            goMeta.LineWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", "CCCount", "Started", ref par_oConnection);
            clRowSet doCSRS = new clRowSet("CS", clC.SELL_READONLY, "MLS_STATUS=3 AND DTT_SCHEDULEDDATE<='" + sOldDate + "'", "DTT_SCHEDULEDDATE DESC", "TXT_CCCAMPAIGNID");
            if (doCSRS.GetFirst() == 1)
            {
                do
                {
                    EmailCampaignTrackingM(doCSRS.GetFieldVal("TXT_CCCAMPAIGNID").ToString());
                    doMCArray = new clArray();
                    doMCArray = (clArray)doCSRS.GetFieldVal("LNK_RELATED_MC", 2);
                    for (int i = 1; i <= doMCArray.GetDimension(); i++)
                    {
                        MarketingCampaignStatistics(doMCArray.GetItem(i));
                    }
                    if (doCSRS.GetNext() == 0)
                        break; // TODO: might not be correct. Was : Exit Do
                } while (true);
            }
            goMeta.LineWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", "CCCount", "Ended", ref par_oConnection);
            sDelim = "|";
            goMeta.LineWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", "LastRunDate", goTR.DateTimeToSysString(goTR.NowUTC(), ref par_iValid, ref sDelim), ref par_oConnection);

            return true;

        }


        public bool EmailCampaignTrackingM(string sCampaignID = "", string _lCampaignFor = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            //try
            //{
                //Dim sID As String = goUI.GetLastSelected("SELECTEDRECORDID")
                string sID = Guid.NewGuid().ToString();
                goLog.Log("EmailCampaignTracking", "Start" + DateTime.Now.ToString("MMDDYYYY HHmmmsss"));
                string sSentCampaignURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns?status=SENT&api_key=" + GetConsatntContactAutho("APIKEY", _lCampaignFor);
                //ma4jgf2zu926c3ggw5dq9hpm"
                StringBuilder sData = new StringBuilder();
                System.DateTime dtDateTime = default(System.DateTime);
                string sDateTime = "";
                int CCCount = 0;
                string sCurrentUser = goP.GetMe("ID");
                PublicDomain.TzTimeZone zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
                bool bRetError = false;
                string sResponse = httpPost(ref bRetError, sSentCampaignURI, sData, "GET", CampaignFor: _lCampaignFor);
                DataTable dtSentCampaigns = ParseJSON_ResultDataSet(sResponse);
                string _TXT_EmailDropSYS_Name = string.Empty;



                if (!string.IsNullOrEmpty(sCampaignID))
                {
                    DataView sCcDataview = new DataView();
                    sCcDataview.Table = dtSentCampaigns;
                    sCcDataview.RowFilter = "id='" + sCampaignID + "'";
                    dtSentCampaigns = sCcDataview.ToTable();
                    //For Each row In dtSentCampaigns.Rows
                    //    If row("id") <> sCampaignID Then
                    //        row.Delete()
                    //    End If
                    //Next
                    dtSentCampaigns.AcceptChanges();

                }

                string sTrackingURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns/";
                string sDate = "";
                //Dim sCampaignID As String = ""
                string sMD = goMeta.PageRead("GLOBAL", "OTH_CCCAMPAIGNTRACKING", "", false, "XX");
                sDateTime = goTR.StrRead(sMD, "LastRunDate");
                dtDateTime = goTR.StringToDateTime(sDateTime, "", "", ref par_iValid);
                sDate = goTR.DateToString(dtDateTime.Date, "", ref par_iValid) + "T" + dtDateTime.TimeOfDay.ToString("hh\\:mm\\:ss") + ".000Z";
                string sMarketingField = "MMO_MARKETINGHISTORY";
                dynamic sCampaignName = string.Empty;
                string par_sDelim = "|";
                goTR.StrWrite(ref sMD, "LastStartDate", goTR.DateTimeToSysString(goTR.NowUTC(), ref par_iValid, ref par_sDelim));
                goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");


                int iOpenScore = 1;
                int iClickScore = 5;
                int iForwardScore = 5;
                string _errormassge = "";

                if (dtSentCampaigns.Rows.Count > 0)
                {
                    foreach (DataRow row_loopVariable in dtSentCampaigns.Rows)
                    {
                        //row = row_loopVariable;
                        try
                        {
                            CCCount = CCCount + 1;
                            sCampaignID = row_loopVariable["id"].ToString();
                            sCampaignName = row_loopVariable["name"];
                            goTR.StrWrite(ref sMD, "CCCount", CCCount.ToString() + "out of (" + dtSentCampaigns.Rows.Count.ToString() + ")(" + sCampaignID + " Start)");
                            goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                            _TXT_EmailDropSYS_Name = string.Empty;
                            //try
                            //{
                                _TXT_EmailDropSYS_Name = ConstantCampaignStatistics(sCampaignID, _lCampaignFor);

                            //}
                            //catch (Exception ex)
                            //{
                            //}

                            //----------------------------------------------------------------------------------------------------------------------
                            //VS 07312015 "Campaign For" of the Cmapign to determine DTT_LastScoreUpdate and SR__MArketingScore Fields
                            string sCampaignFor = "";
                            int iCampaignForID = 0;
                            string sMarketingScoreField = "SR__MARKETINGSCORE";
                            string sMarketingScoreDateField = "DTT_LASTMARKETINGSCOREUPDATE";
                            string sMarketingHistoryPrependText = "";
                            //Dim doCSRS As clRowSet = New clRowSet("CS", 3, "TXT_CCCAMPAIGNID='" & sCampaignID & "'", , "MLS_CAMPAIGNFOR", 1)
                            clRowSet doCSRS = new clRowSet("CS", 3, "TXT_CCCAMPAIGNID='" + sCampaignID + "'", "", "LNK_RELATED_MC", 1);

                            if (doCSRS.GetFirst() == 1)
                            {
                                clRowSet doMCRS = new clRowSet("MC", 3, "GID_ID='" + doCSRS.GetFieldVal("LNK_RELATED_MC") + "'", "", "MLS_CAMPAIGNFOR", 1);
                                //sCampaignFor = doCSRS.GetFieldVal("MLS_CAMPAIGNFOR")
                                //iCampaignForID = doCSRS.GetFieldVal("MLS_CAMPAIGNFOR", 2)
                                sCampaignFor = doMCRS.GetFieldVal("MLS_CAMPAIGNFOR").ToString();
                                iCampaignForID = Convert.ToInt32(doMCRS.GetFieldVal("MLS_CAMPAIGNFOR", 2));
                                //Add Suffix to the FieldName if not default CampaignFor eg: CampaignFor=SP360 then SR__MARKETINGSCORESP360 or DTT_LASTMARKETINGSCOREUPDATESP360
                                if (iCampaignForID == 1)
                                {
                                    sMarketingScoreField = sMarketingScoreField + "SALESPROCESS360";
                                    sMarketingScoreDateField = sMarketingScoreDateField + "SALESPROCESS360";
                                    sMarketingHistoryPrependText = sCampaignFor + ": ";
                                }
                            }
                            //----------------------------------------------------------------------------------------------------------------------


                            if (!string.IsNullOrEmpty(_TXT_EmailDropSYS_Name))
                            {
                                goTR.StrWrite(ref sMD, "CCCount", CCCount.ToString() + "out of (" + dtSentCampaigns.Rows.Count.ToString() + ")(" + sCampaignID + " Start 7432)");
                                goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                string sFinalURI = "";
                                string sURI = sTrackingURI + sCampaignID + "/tracking/";
                                DataTable dtResults = new DataTable();
                                string sWhere = "";


                                //Get Campaign Details
                                string sccuri = sTrackingURI + sCampaignID + "?api_key=" + GetConsatntContactAutho("APIKEY", _lCampaignFor);
                                //ma4jgf2zu926c3ggw5dq9hpm"
                                sResponse = httpPost(ref bRetError, sccuri, sData, "GET", CampaignFor: _lCampaignFor);
                                DataTable click_through_details = new DataTable();
                                click_through_details = ParseJSON_GetTable(sResponse, "click_through_details");

                                //Master DataTable   -   To gather all the result sets and process at once
                                DataTable dtMaster = new DataTable();
                                dtMaster.Columns.Add("email_address");
                                dtMaster.Columns.Add("date");
                                dtMaster.Columns.Add("action");
                                dtMaster.Columns.Add("score");
                                dtMaster.Columns.Add("link_id");
                                dtMaster.Columns.Add("bounce_code");
                                //VNK 08272015
                                dtMaster.Columns.Add("bounce_description");
                                //VNK 08272015
                                goTR.StrWrite(ref sMD, "CCCount", CCCount.ToString() + "out of (" + dtSentCampaigns.Rows.Count.ToString() + ")(" + sCampaignID + " Start 7445)");
                                goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                //1.Sends
                                //try
                                //{
                                    sFinalURI = sURI + "sends?created_since=" + sDate + "&api_key=" + GetConsatntContactAutho("APIKEY", _lCampaignFor);
                                    //ma4jgf2zu926c3ggw5dq9hpm"                                    
                                    sResponse = httpPost(ref bRetError, sFinalURI, sData, "GET", CampaignFor: _lCampaignFor);
                                    dtResults = ParseJSON_ResultDataSet(sResponse);
                                    foreach (DataRow Resultrow_loopVariable in dtResults.Rows)
                                    {
                                        //Resultrow = Resultrow_loopVariable;
                                        DataRow InsertRow = dtMaster.NewRow();
                                        InsertRow["email_address"] = Resultrow_loopVariable["email_address"];
                                        InsertRow["date"] = Resultrow_loopVariable["send_date"];
                                        InsertRow["action"] = "Send";
                                        InsertRow["score"] = 0;
                                        dtMaster.Rows.Add(InsertRow);
                                    }

                                //}
                                //catch (Exception ex)
                                //{
                                //}
                                goTR.StrWrite(ref sMD, "CCCount", CCCount.ToString() + "out of (" + dtSentCampaigns.Rows.Count.ToString() + ")(" + sCampaignID + " Start 7463)");
                                goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                //try
                                //{
                                    //2.Opens
                                    sFinalURI = sURI + "opens?created_since=" + sDate + "&api_key=" + GetConsatntContactAutho("APIKEY", _lCampaignFor);
                                    //ma4jgf2zu926c3ggw5dq9hpm"

                                    sResponse = httpPost(ref bRetError, sFinalURI, sData, "GET", CampaignFor: _lCampaignFor);
                                    dtResults = ParseJSON_ResultDataSet(sResponse);
                                    foreach (DataRow Resultrow_loopVariable in dtResults.Rows)
                                    {
                                        //Resultrow = Resultrow_loopVariable;
                                        DataRow InsertRow = dtMaster.NewRow();
                                        InsertRow["email_address"] = Resultrow_loopVariable["email_address"];
                                        InsertRow["date"] = Resultrow_loopVariable["open_date"];
                                        InsertRow["action"] = "Open";
                                        InsertRow["score"] = iOpenScore;
                                        dtMaster.Rows.Add(InsertRow);
                                    }

                                //}
                                //catch (Exception ex)
                                //{
                                //}
                                goTR.StrWrite(ref sMD, "CCCount", CCCount.ToString() + "out of (" + dtSentCampaigns.Rows.Count.ToString() + ")(" + sCampaignID + " Start 7481)");
                                goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                //try
                                //{
                                    //3. Clicks
                                    sFinalURI = sURI + "clicks?created_since=" + sDate + "&api_key=" + GetConsatntContactAutho("APIKEY", _lCampaignFor);
                                    //ma4jgf2zu926c3ggw5dq9hpm"
                                    sResponse = httpPost(ref bRetError, sFinalURI, sData, "GET", CampaignFor: _lCampaignFor);
                                    dtResults = ParseJSON_ResultDataSet(sResponse);
                                    foreach (DataRow Resultrow_loopVariable in dtResults.Rows)
                                    {
                                        //Resultrow = Resultrow_loopVariable;
                                        DataRow InsertRow = dtMaster.NewRow();
                                        InsertRow["email_address"] = Resultrow_loopVariable["email_address"];
                                        InsertRow["date"] = Resultrow_loopVariable["click_date"];
                                        InsertRow["action"] = "Click";
                                        InsertRow["score"] = iClickScore;
                                        //link_id
                                        InsertRow["link_id"] = Resultrow_loopVariable["link_id"];
                                        dtMaster.Rows.Add(InsertRow);
                                    }

                                //}
                                //catch (Exception ex)
                                //{
                                //}
                                goTR.StrWrite(ref sMD, "CCCount", CCCount.ToString() + "out of (" + dtSentCampaigns.Rows.Count.ToString() + ")(" + sCampaignID + " Start 7499)");
                                goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                //try
                                //{
                                    //4.Forwards
                                    sFinalURI = sURI + "forwards?created_since=" + sDate + "&api_key=" + GetConsatntContactAutho("APIKEY", _lCampaignFor);
                                    //ma4jgf2zu926c3ggw5dq9hpm"
                                    sResponse = httpPost(ref bRetError, sFinalURI, sData, "GET", CampaignFor: _lCampaignFor);
                                    dtResults = ParseJSON_ResultDataSet(sResponse);
                                    foreach (DataRow Resultrow_loopVariable in dtResults.Rows)
                                    {
                                        //Resultrow = Resultrow_loopVariable;
                                        DataRow InsertRow = dtMaster.NewRow();
                                        InsertRow["email_address"] = Resultrow_loopVariable["email_address"];
                                        InsertRow["date"] = Resultrow_loopVariable["forward_date"];
                                        InsertRow["action"] = "Forward";
                                        InsertRow["score"] = iForwardScore;
                                        dtMaster.Rows.Add(InsertRow);
                                    }

                                //}
                                //catch (Exception ex)
                                //{
                                //}
                                goTR.StrWrite(ref sMD, "CCCount", CCCount.ToString() + "out of (" + dtSentCampaigns.Rows.Count.ToString() + ")(" + sCampaignID + " Start 7517)");
                                goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                //try
                                //{
                                    //5.Unsubscribed
                                    sFinalURI = sURI + "unsubscribes?created_since=" + sDate + "&api_key=" + GetConsatntContactAutho("APIKEY", _lCampaignFor);
                                    //ma4jgf2zu926c3ggw5dq9hpm"
                                    sResponse = httpPost(ref bRetError, sFinalURI, sData, "GET", CampaignFor: _lCampaignFor);
                                    dtResults = ParseJSON_ResultDataSet(sResponse);
                                    foreach (DataRow Resultrow_loopVariable in dtResults.Rows)
                                    {
                                        //Resultrow = Resultrow_loopVariable;
                                        DataRow InsertRow = dtMaster.NewRow();
                                        InsertRow["email_address"] = Resultrow_loopVariable["email_address"];
                                        InsertRow["date"] = Resultrow_loopVariable["unsubscribe_date"];
                                        InsertRow["action"] = "Unsubscribe";
                                        InsertRow["score"] = 0;
                                        dtMaster.Rows.Add(InsertRow);
                                    }

                                //}
                                //catch (Exception ex)
                                //{
                                //}
                                goTR.StrWrite(ref sMD, "CCCount", CCCount.ToString() + "out of (" + dtSentCampaigns.Rows.Count.ToString() + ")(" + sCampaignID + " Start 7535)");
                                goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                //try
                                //{
                                    //6.Bounces
                                    sFinalURI = sURI + "bounces?created_since=" + sDate + "&api_key=" + GetConsatntContactAutho("APIKEY", _lCampaignFor);
                                    //ma4jgf2zu926c3ggw5dq9hpm"
                                    sResponse = httpPost(ref bRetError, sFinalURI, sData, "GET", CampaignFor: _lCampaignFor);
                                    dtResults = ParseJSON_ResultDataSet(sResponse);
                                    foreach (DataRow Resultrow_loopVariable in dtResults.Rows)
                                    {
                                        //Resultrow = Resultrow_loopVariable;
                                        DataRow InsertRow = dtMaster.NewRow();
                                        InsertRow["email_address"] = Resultrow_loopVariable["email_address"];
                                        InsertRow["date"] = Resultrow_loopVariable["bounce_date"];
                                        InsertRow["action"] = "Bounce";
                                        InsertRow["score"] = 0;
                                        //VNK 08272015
                                        InsertRow["bounce_code"] = Resultrow_loopVariable["bounce_code"];
                                        InsertRow["bounce_description"] = Resultrow_loopVariable["bounce_description"];
                                        dtMaster.Rows.Add(InsertRow);
                                    }

                                //}
                                //catch (Exception ex)
                                //{
                                //}
                                goTR.StrWrite(ref sMD, "CCCount", CCCount.ToString() + "out of (" + dtSentCampaigns.Rows.Count.ToString() + ")(" + sCampaignID + " Start 7553)");
                                goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                //6.Link Clicks
                                //sURI = sURI & "clicks/linkId?created_since=" & sDate & "&api_key=ma4jgf2zu926c3ggw5dq9hpm"

                                //Get Distinct Emails
                                DataTable dtDistEmails = new DataTable();
                                dtDistEmails = dtMaster.DefaultView.ToTable(true, "email_address");
                                if (dtDistEmails.Rows.Count > 0)
                                {
                                    for (int i = 0; i <= dtDistEmails.Rows.Count - 1; i++)
                                    {
                                        if (i == 0)
                                        {
                                            sWhere = "EML_EMAIL='" + dtDistEmails.Rows[i]["email_address"] + "'";
                                        }
                                        else
                                        {
                                            sWhere = sWhere + " OR EML_EMAIL='" + dtDistEmails.Rows[i]["email_address"] + "'";
                                        }
                                    }
                                    //VNK 08272015
                                    sWhere = "(" + sWhere + ") AND LNK_RELATED_CS='" + doCSRS.GetFieldVal("GID_ID") + "'";
                                    if (!string.IsNullOrEmpty(sWhere))
                                    {
                                        //Dim dtCNRS As New clRowSet("CN", 1, sWhere, , "EML_EMAIL")
                                        clRowSet dtCNRS = new clRowSet("CN", 1, sWhere, "", "EML_EMAIL", -1, "", "", "", "", "", true);
                                        if (dtCNRS.GetFirst() == 1)
                                        {
                                            goTR.StrWrite(ref sMD, "CCCount", CCCount.ToString() + "out of (" + dtSentCampaigns.Rows.Count.ToString() + ")(" + sCampaignID + " Start 7573)");
                                            goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                            DataTable _sTabledtCNRS = dtCNRS.oDataSet.Tables[0];
                                            for (int _sIndex = 0; _sIndex <= _sTabledtCNRS.Rows.Count - 1; _sIndex++)
                                            {
                                                try
                                                {
                                                    string sNotes = "";
                                                    double dScore = 0;
                                                    DataRow[] foundRows = null;
                                                    dynamic _sEmailId = _sTabledtCNRS.Rows[_sIndex]["EML_EMAIL"];
                                                    foundRows = dtMaster.Select("email_address='" + _sTabledtCNRS.Rows[_sIndex]["EML_EMAIL"] + "'", "date ASC");
                                                    //dScore = _sTabledtCNRS.Rows(_sIndex)(sMarketingScoreField)  '("SR__MARKETINGSCORE")
                                                    if (!_sTabledtCNRS.Rows[_sIndex][sMarketingScoreField].Equals(System.DBNull.Value))
                                                    {
                                                        dScore = Convert.ToDouble(_sTabledtCNRS.Rows[_sIndex][sMarketingScoreField]);
                                                    }

                                                    //sNotes = _sTabledtCNRS.Rows(_sIndex)(sMarketingField)

                                                    if (!_sTabledtCNRS.Rows[_sIndex][sMarketingField].Equals(System.DBNull.Value))
                                                    {
                                                        sNotes = _sTabledtCNRS.Rows[_sIndex][sMarketingField].ToString();
                                                    }

                                                    //VS 07302015 Calculate lastActionDate
                                                    System.DateTime dtMaxActionDate = default(System.DateTime);
                                                    if (!dtCNRS.GetFieldVal(sMarketingScoreDateField, 2).Equals(System.DBNull.Value))
                                                    {
                                                        dtMaxActionDate = Convert.ToDateTime(dtCNRS.GetFieldVal(sMarketingScoreDateField, 2));
                                                    }

                                                    clArray opendodrops = new clArray();
                                                    oTable = null;
                                                    opendodrops = dtCNRS.GetLinkVal("LNK_Opened_CS", ref opendodrops, false, 0, -1, "A_a", ref oTable);

                                                    clArray clickdodrops = new clArray();
                                                    oTable = null;
                                                    clickdodrops = dtCNRS.GetLinkVal("LNK_Clicked_CS", ref clickdodrops, false, 0, -1, "A_a", ref oTable);

                                                    clArray forwarddodrops = new clArray();
                                                    oTable = null;
                                                    forwarddodrops = dtCNRS.GetLinkVal("LNK_Forwarded_CS", ref forwarddodrops, false, 0, -1, "A_a", ref oTable);

                                                    clArray unsubscribedodrops = new clArray();
                                                    oTable = null;
                                                    unsubscribedodrops = dtCNRS.GetLinkVal("LNK_Unsubscribed_CS", ref unsubscribedodrops, false, 0, -1, "A_a", ref oTable);
                                                    //BOUNCED
                                                    clArray BOUNCEDdodrops = new clArray();
                                                    oTable = null;
                                                    BOUNCEDdodrops = dtCNRS.GetLinkVal("LNK_BOUNCED_CS", ref BOUNCEDdodrops, false, 0, -1, "A_a", ref oTable);

                                                    foreach (DataRow foundrow_loopVariable in foundRows)
                                                    {
                                                        //foundrow = foundrow_loopVariable;
                                                        //sNotes = "Score Changed from " & dScore.ToString & " to " & (dScore + foundrow("score")).ToString & vbCrLf & _
                                                        //            foundrow("action") & " for Drop: " & sCampaignName & "(" & sCampaignID & ")" & " at " & foundrow("date") & vbCrLf & sNotes
                                                        //sNotes = "Score Changed from " & dScore.ToString & " to " & (dScore + foundrow("score")).ToString & vbCrLf & _
                                                        //          foundrow("action") & " for Drop: " & sCampaignName & " at " & foundrow("date") & vbCrLf & sNotes

                                                        DateTime _ChangedDate = default(DateTime);
                                                        _ChangedDate = Convert.ToDateTime(foundrow_loopVariable["date"]);
                                                        string _saction = foundrow_loopVariable["action"].ToString();

                                                        //Get URL
                                                        if (foundrow_loopVariable["action"].ToString() == "Click")
                                                        {
                                                            _saction = foundrow_loopVariable["action"].ToString();
                                                            DataRow[] sclickurls = null;
                                                            sclickurls = click_through_details.Select("url_uid='" + foundrow_loopVariable["link_id"] + "'");
                                                            if (sclickurls.Length > 0)
                                                            {
                                                                foreach (DataRow sclickurl_loopVariable in sclickurls)
                                                                {
                                                                    //sclickurl = sclickurl_loopVariable;
                                                                    _saction = "Click on link " + sclickurl_loopVariable["url"].ToString();
                                                                }
                                                            }
                                                        }
                                                        //VNK 08272015
                                                        string _sbousceDesc = string.Empty;
                                                        if (foundrow_loopVariable["action"].ToString() == "Bounce")
                                                        {
                                                            _saction = foundrow_loopVariable["action"].ToString();
                                                            string _bounce_code = foundrow_loopVariable["bounce_code"].ToString();
                                                            if (_bounce_code == "V")
                                                            {
                                                                _sbousceDesc = "(AutoReply)";
                                                            }
                                                            //VNK 12172015
                                                            //
                                                            //0- None
                                                            //1- B - non-existent
                                                            //2- D - undeliverable
                                                            //3- S - suspended
                                                            //4- Z - Blocked
                                                            //5- F - recipient's mailbox is full
                                                            //6- V - recipient has set an autoreply, but the message was delivered;
                                                            //US_0 = None
                                                            //US_1 = Non - existent
                                                            //US_2 = Undeliverable
                                                            //US_3 = Suspended
                                                            //US_4 = Blocked
                                                            //US_5=Mailbox full
                                                            //US_6=Vacation/Auto replay
                                                            //US_99 = Other

                                                            switch (_bounce_code)
                                                            {
                                                                case "B":
                                                                    _sbousceDesc = "(Non Existent)";
                                                                    dtCNRS.SetFieldVal("MLS_Bouncereason", 1, 2);
                                                                    break;
                                                                case "D":
                                                                    _sbousceDesc = "(Undeliverable)";
                                                                    dtCNRS.SetFieldVal("MLS_Bouncereason", 2, 2);
                                                                    break;
                                                                case "S":
                                                                    _sbousceDesc = "(Suspended)";
                                                                    dtCNRS.SetFieldVal("MLS_Bouncereason", 3, 2);
                                                                    break;
                                                                case "Z":
                                                                    _sbousceDesc = "(Blocked)";
                                                                    dtCNRS.SetFieldVal("MLS_Bouncereason", 4, 2);
                                                                    break;
                                                                case "F":
                                                                    _sbousceDesc = "(Mailbox full)";
                                                                    dtCNRS.SetFieldVal("MLS_Bouncereason", 5, 2);
                                                                    break;
                                                                case "V":
                                                                    _sbousceDesc = "(Vacation/AutoReplay)";
                                                                    dtCNRS.SetFieldVal("MLS_Bouncereason", 6, 2);
                                                                    break;
                                                                default:
                                                                    _sbousceDesc = "(Other)";
                                                                    dtCNRS.SetFieldVal("MLS_Bouncereason", 99, 2);
                                                                    break;
                                                            }

                                                        }

                                                        //VNK 08272015
                                                        sNotes = sMarketingHistoryPrependText + _saction + " Drop" + _sbousceDesc + ": " + _TXT_EmailDropSYS_Name + " on " + _ChangedDate.ToString("yyyy-MM-dd HH:mmm") + Constants.vbCrLf + "Score Changed from " + dScore.ToString() + " to " + (dScore + Convert.ToDouble(foundrow_loopVariable["score"])).ToString() + Constants.vbCrLf + sNotes;

                                                        dScore = dScore + Convert.ToDouble(foundrow_loopVariable["score"]);

                                                        //VNK 08202015 -Contact Statistics
                                                        //----------------------------------
                                                        //try
                                                        //{
                                                            clRowSet stCSRS = new clRowSet("CS", 3, "TXT_CCCAMPAIGNID='" + sCampaignID + "'", "", "");

                                                            switch (foundrow_loopVariable["action"].ToString().ToLower())
                                                            {
                                                                case "open":
                                                                    opendodrops.Add(stCSRS.GetFieldVal("GID_ID").ToString());
                                                                    break;
                                                                case "click":
                                                                    clickdodrops.Add(stCSRS.GetFieldVal("GID_ID").ToString());
                                                                    break;
                                                                case "forwards":
                                                                    forwarddodrops.Add(stCSRS.GetFieldVal("GID_ID").ToString());
                                                                    break;
                                                                case "unsubscribe":
                                                                    unsubscribedodrops.Add(stCSRS.GetFieldVal("GID_ID").ToString());
                                                                    break;
                                                                //VNK 08242015
                                                                //-------------Start VNK 08242015
                                                                case "bounce":
                                                                    BOUNCEDdodrops.Add(stCSRS.GetFieldVal("GID_ID").ToString());
                                                                    break;
                                                                //-------------End VNK 08242015
                                                            }

                                                        //}
                                                        //catch (Exception ex)
                                                        //{
                                                        //}
                                                        //----------------------------------

                                                        if ((foundrow_loopVariable["action"].ToString() == "Bounce" & foundrow_loopVariable["bounce_code"].ToString() != "V") | foundrow_loopVariable["action"].ToString() == "Unsubscribe")
                                                        {
                                                            //dtCNRS.SetFieldVal("CHK_MAILINGLIST", 0, 2)
                                                            //VNK 08242015
                                                            //-------------Start VNK 08242015
                                                            string _MailListfieldID = "CHK_CCMailList";
                                                            _MailListfieldID = _MailListfieldID + (iCampaignForID + 1).ToString();
                                                            dtCNRS.SetFieldVal(_MailListfieldID, 0, 2);
                                                            //-------------End VNK 08242015
                                                        }

                                                        goTR.StrWrite(ref sMD, "CCCount", "GetMe before ");
                                        goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD,"" ,"" , "XX");
                                        _errormassge = _errormassge + "GetMe before " + ";Emailid" + _sEmailId + Constants.vbCrLf;


                                        goTR.StrWrite(ref sMD, "CCCount", "GetMe after " + sCurrentUser);
                                        goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD,"" ,"" , "XX");
                                        System.DateTime dtUsersToday = new System.DateTime(_ChangedDate.Year, _ChangedDate.Month, _ChangedDate.Day, _ChangedDate.Hour, _ChangedDate.Minute, _ChangedDate.Second);
                                        dtUsersToday = zone.ToLocalTime(dtUsersToday);
                                        //VS 07312015 Get Max Update Date
                                        //If dtMaxActionDate < _ChangedDate Then
                                        //    dtMaxActionDate = _ChangedDate
                                        //End If
                                        _errormassge = _errormassge + "dtMaxActionDate before " + ";Emailid" + _sEmailId + Constants.vbCrLf;
                                        goTR.StrWrite(ref sMD, "CCCount", "dtMaxActionDate before " + ";Emailid" + _sEmailId);
                                        goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                        if (dtMaxActionDate < dtUsersToday) {
	                                        dtMaxActionDate = dtUsersToday;
                                        }
                                        _errormassge = _errormassge + "dtMaxActionDate after " + ";Emailid" + _sEmailId + Constants.vbCrLf;
                                        goTR.StrWrite(ref sMD, "CCCount", "dtMaxActionDate after " + ";Emailid" + _sEmailId);
                                        goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD,"" ,"" , "XX");

                                                        //goTR.StrWrite(ref sMD, "CCCount", "GetMe before " + ";Emailid" + _sEmailId);
                                                        //goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");

                                                        //string sCurrentUser = goP.GetMe("ID");

                                                        //goTR.StrWrite(ref sMD, "CCCount", "GetMe after " + sCurrentUser + ";Emailid" + _sEmailId);
                                                        //goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                                        //PublicDomain.TzTimeZone zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
                                                        //System.DateTime dtUsersToday = new System.DateTime(_ChangedDate.Year, _ChangedDate.Month, _ChangedDate.Day, _ChangedDate.Hour, _ChangedDate.Minute, _ChangedDate.Second);
                                                        //dtUsersToday = zone.ToLocalTime(dtUsersToday);
                                                        ////VS 07312015 Get Max Update Date
                                                        ////If dtMaxActionDate < _ChangedDate Then
                                                        ////    dtMaxActionDate = _ChangedDate
                                                        ////End If
                                                        //goTR.StrWrite(ref sMD, "CCCount", "dtMaxActionDate before " + ";Emailid" + _sEmailId);
                                                        //goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                                        //if (dtMaxActionDate < dtUsersToday)
                                                        //{
                                                        //    dtMaxActionDate = dtUsersToday;
                                                        //}
                                                        //goTR.StrWrite(ref sMD, "CCCount", "dtMaxActionDate before " + ";Emailid" + _sEmailId);
                                                        //goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                                    }

                                                    dtCNRS.SetLinkVal("LNK_Opened_CS", opendodrops);
                                                    dtCNRS.SetLinkVal("LNK_Clicked_CS", clickdodrops);
                                                    dtCNRS.SetLinkVal("LNK_Forwarded_CS", forwarddodrops);
                                                    dtCNRS.SetLinkVal("LNK_Unsubscribed_CS", unsubscribedodrops);
                                                    dtCNRS.SetLinkVal("LNK_BOUNCED_CS", BOUNCEDdodrops);

                                                    dtCNRS.SetFieldVal(sMarketingField, sNotes);
                                                    dtCNRS.SetFieldVal(sMarketingScoreField, dScore, 2);
                                                    //dtCNRS.SetFieldVal("SR__MARKETINGSCORE", dScore)
                                                    //VNK 09102015 Convert to CST
                                                    dtMaxActionDate = dtMaxActionDate.AddHours(-5);
                                                    dtCNRS.SetFieldVal(sMarketingScoreDateField, dtMaxActionDate, 2);
                                                    if (dtCNRS.Commit() == 0)
                                                    {
                                                        goErr.SetError(45105, sProc, "", sID);
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    goTR.StrWrite(ref sMD, "CCCount", "Campaign for:" + _lCampaignFor + " Email " + _sTabledtCNRS.Rows[_sIndex]["EML_EMAIL"] + " ; 12554-error" + ex.ToString());
                                                    goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                                                }
                                                dtCNRS.GetNext();
                                            }
                                        }
                                        //If dtCNRS.GetFirst = 1 Then
                                        //    Do
                                        //        Dim sNotes As String = ""
                                        //        Dim dScore As Double = 0
                                        //        Dim foundRows As DataRow()
                                        //        foundRows = dtMaster.[Select]("email_address='" & dtCNRS.GetFieldVal("EML_EMAIL") & "'", "date ASC")
                                        //        dScore = dtCNRS.GetFieldVal("SR__MARKETINGSCORE")
                                        //        sNotes = dtCNRS.GetFieldVal(sMarketingField)

                                        //        For Each foundrow In foundRows
                                        //            sNotes = "Score Changed from " & dScore.ToString & " to " & (dScore + foundrow("score")).ToString & vbCrLf & _
                                        //                        foundrow("action") & " for Campaign " & sCampaignName & "(" & sCampaignID & ")" & " at " & foundrow("date") & vbCrLf & sNotes
                                        //            dScore = dScore + foundrow("score")

                                        //            If foundrow("action") = "Bounce" Or foundrow("action") = "Unsubscribe" Then
                                        //                dtCNRS.SetFieldVal("CHK_MAILINGLIST", 0, 2)
                                        //            End If
                                        //        Next

                                        //        dtCNRS.SetFieldVal(sMarketingField, sNotes)
                                        //        dtCNRS.SetFieldVal("SR__MARKETINGSCORE", dScore)
                                        //        If dtCNRS.Commit() = 0 Then
                                        //            goErr.SetError(45105, sProc, "", sID)
                                        //        End If
                                        //        If dtCNRS.GetNext = 0 Then Exit Do
                                        //    Loop
                                        //End If
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            goTR.StrWrite(ref sMD, "CCCount", "8673-error" + ex.ToString());
                            goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");
                        }
                        goTR.StrWrite(ref sMD, "CCCount", CCCount.ToString() + "out of (" + dtSentCampaigns.Rows.Count.ToString() + ")(" + sCampaignID + " End)");
                        goMeta.PageWrite("GLOBAL", "OTH_CCCAMPAIGNTRACKING", sMD, "", "", "XX");

                    }
                }

            //}
            //catch (Exception ex)
            //{
            //}

            return true;
        }

        //VNK ******** -For Individual Account
        public string GetConsatntContactAutho(string sDataFor, string CampaignFor = "", object doForm = null)
        {
            //sDataFor Should be - AccessToken/APIKEY
            //Campaign is CampaignFor Value Eg- 0/1/2 etc..

            string retval = "";
            Form doForm1 = (Form)doForm;
            if (CampaignFor == string.Empty)
            {
                CampaignFor = goData.GetFieldValueFromRec(doForm1.doRS.GetFieldVal("LNK_RELATED_MC").ToString(), "MLS_CAMPAIGNFOR", 2).ToString();
            }

            //VS ******** : Moved to OTH_CONSTANTCONTACT_DATA
            //Dim sMG As String = goMeta.PageRead("GLOBAL", "OTH_ConstantContactAutho");
            string sMG = goMeta.PageRead("GLOBAL", "OTH_CONSTANTCONTACT_DATA");
            dynamic CampaignName = goTR.StrRead(sMG, "CampaignFor_" + CampaignFor + "_Name");
            retval = goTR.StrRead(sMG, "CampaignFor_" + CampaignFor + "_" + sDataFor);

            return retval;
        }

        public bool AutoUpdateScheduledDropStatus(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            clRowSet doScheduledCSRS = new clRowSet("CS", clC.SELL_EDIT, "MLS_STATUS=2", "DTT_SCHEDULEDDATE DESC", "", -1, "", "", "", "", "", true, true);
            string sStatus = "";
            string sCampaignID = "";
            string sAPIKey = "";
            string sAccessToken = "";
            string sExistingCampaignURI = "";
            StringBuilder sData = new StringBuilder();
            string sResponse = "";
            int iCampaignFor = 0;
            clRowSet doMCRS = null;

            if (doScheduledCSRS.GetFirst() == 1)
            {
                do
                {
                    sCampaignID = doScheduledCSRS.GetFieldVal("TXT_CCCAMPAIGNID").ToString();

                    if (!string.IsNullOrEmpty(sCampaignID))
                    {
                        doMCRS = new clRowSet("MC", 3, "GID_ID='" + doScheduledCSRS.GetFieldVal("LNK_RELATED_MC") + "'", "", "MLS_CAMPAIGNFOR", 1);
                        if (doMCRS.GetFirst() == 1)
                        {
                            iCampaignFor = Convert.ToInt32(doMCRS.GetFieldVal("MLS_CAMPAIGNFOR", 2));
                            sAPIKey = GetConsatntContactAutho("APIKEY", iCampaignFor.ToString(), null);
                            sAccessToken = GetConsatntContactAutho("ACCESSTOKEN", iCampaignFor.ToString(), null);
                            sExistingCampaignURI = "https://api.constantcontact.com/v2/emailmarketing/campaigns/" + sCampaignID + "?api_key=" + sAPIKey;

                            bool sRetError = false;
                            sResponse = httpPost2(ref sRetError, sExistingCampaignURI, sData, sAccessToken, "GET");
                            sStatus = ParseJSON_ExistingData(sResponse, "status", "root");

                            //sStatus = ConstCont_Campaign_GetStatus(sCampaignID, Nothing)
                            if (sStatus.ToUpper() == "SENT")
                            {
                                doScheduledCSRS.SetFieldVal("MLS_STATUS", sStatus);
                                ConstantCampaignStatistics(sCampaignID, iCampaignFor.ToString());
                                MarketingCampaignStatistics(doMCRS.GetFieldVal("GID_ID").ToString());
                                //doScheduledCSRS.Commit()
                            }
                        }
                        doMCRS = null;
                    }
                    sStatus = "";
                    if (doScheduledCSRS.GetNext() == 0)
                        break; // TODO: might not be correct. Was : Exit Do
                } while (true);
            }

            return true;
        }
        public string httpPost2(ref bool sRetError, string URI, StringBuilder s, string sAccessToken, string sOperation = "POST")
        {

            try
            {
                string contentType = "application/json";
                Stream dataStream = default(Stream);
                WebResponse response = default(WebResponse);
                // Create a request for the URL. 
                URI = URI.Replace(" ", "");
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(URI);
                // set the credentials.
                //request.Headers.Add("Authorization", "Bearer 2cc3ac20-f55d-43eb-84ad-200a1577dd3b")
                request.Headers.Add("Authorization", "Bearer " + sAccessToken);
                // Add Request Header
                request.Accept = "application/json";
                request.Timeout = 100000;
                string st = s.ToString();
                if (!String.IsNullOrEmpty(st))
                {
                    // Set the Method property of the request to POST.
                    request.Method = sOperation;
                    request.Timeout = 190000;
                    //SetCredentials(Authdata, ref request);
                    // Create POST data and convert it to a byte array.    
                    byte[] byteArray = Encoding.UTF8.GetBytes(st);
                    // Set the ContentType property of the WebRequest.
                    request.ContentType = contentType;
                    // Set the ContentLength property of the WebRequest.
                    request.ContentLength = byteArray.Length;
                    // Get the request stream.
                    dataStream = request.GetRequestStream();
                    // Write the data to the request stream.            
                    dataStream.Write(byteArray, 0, byteArray.Length);
                    // Close the Stream object.
                    dataStream.Close();
                    // Get the response.
                    response = request.GetResponse();
                    // Display the status.
                    //Console.WriteLine(DirectCast(response, HttpWebResponse).StatusDescription)
                }
                else
                {
                    request.Method = sOperation;
                    response = request.GetResponse();
                }

                // Get the stream containing content returned by the server.
                dataStream = response.GetResponseStream();
                // Open the stream using a StreamReader for easy access.
                StreamReader reader = new StreamReader(dataStream);
                // Read the content.
                string responseFromServer = reader.ReadToEnd();
                // Display the content.
                //Console.WriteLine(responseFromServer)
                // Clean up the streams.
                reader.Close();
                dataStream.Close();
                response.Close();
                //Console.Write(responseFromServer)
                sRetError = false;
                return responseFromServer;

            }
            catch (WebException ex)
            {
                if (ex.Response != null)
                {
                    //' can use ex.Response.Status, .StatusDescription
                    if (ex.Response.ContentLength != 0)
                    {
                        using (Stream stream = ex.Response.GetResponseStream())
                        {
                            using (StreamReader reader = new StreamReader(stream))
                            {
                                string sEXmsg = reader.ReadToEnd();
                                sEXmsg = ParseJSON(sEXmsg, "error_message");
                                return sEXmsg;
                            }
                        }
                    }
                }

                return "";
            }
        }



    }
}

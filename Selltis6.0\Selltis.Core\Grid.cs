﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using Selltis.BusinessLogic;
using Microsoft.VisualBasic;

namespace Selltis.Core
{
    public class Grid : View
    {
        private clTransform goTR;
        private clData godata;
        private bool isLoadFromSession = true;
        private string _viewMetaData = string.Empty;
        public Grid(string _viewId, bool _isTabView, string _section, string _desktopmetadata, int _index, bool _isLoadFromSession = true, int _TotalIndex = 0, string _key = "")
            : base(_viewId, _isTabView, _section, _desktopmetadata, _index, _isLoadFromSession, _TotalIndex, _key)
        {
            godata = (clData)Util.GetInstance("data");
            goTR = (clTransform)Util.GetInstance("tr");

            isLoadFromSession = _isLoadFromSession;
            _viewMetaData = ViewMetaData;

            //if (System.Web.HttpContext.Current.Session[ViewKey] != null)
            //{
            //    SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(ViewKey);
            //    SelectedRecord = _SessionViewInfo.LastSelectedRecord;
            //    if (isLoadFromSession == true)
            //    {
            //        _viewMetaData = _SessionViewInfo.ViewMetaData;
            //    }
            //}

            if (Util.GetSessionValue(Key + "_" + ViewKey) != null && isLoadFromSession == true)
            {
                SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + ViewKey);
                SelectedRecord = _SessionViewInfo.LastSelectedRecord;
                SelectedRowIndex = _SessionViewInfo.LastSelectedRowIndex;
                _viewMetaData = _SessionViewInfo.ViewMetaData;
                PageSize = _SessionViewInfo.PageSize;
            }
            else
            {
                string _val = goTR.StrRead(_viewMetaData, "SHOWTOP", 10);
                if (!string.IsNullOrEmpty(_val))
                {
                    _val = _val.ToUpper();
                }
                PageSize = _val == "ALL" ? 1000 : Convert.ToInt32(goTR.StrRead(_viewMetaData, "SHOWTOP", 10));
            }


            GetColumnsCollection();
            GetSortColumnsCollection();
            LinksTop = Convert.ToInt32(goTR.StrRead(_viewMetaData, "LINKSTOP", "5"));
            //string _val = goTR.StrRead(_viewMetaData, "SHOWTOP", 10);
            //PageSize = _val == "All" ? 0 : Convert.ToInt32(goTR.StrRead(_viewMetaData, "SHOWTOP", 10));
            if (PageSize == 10000)
                PageSize = 10;
            File = goTR.StrRead(_viewMetaData, "FILE");
            IDColumn = "GID_ID";
        }

        private void GetColumnsCollection()
        {
            goTR = (clTransform)Util.GetInstance("tr");
            godata = (clData)Util.GetInstance("data");
            int ColumnCount = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COLCOUNT", 0));
            IsShowSaveButton = false;
            Columns = new List<GridColumn>();

            ViewWidth = 0;

            for (int i = 1; i <= ColumnCount; i++)//starting from 1
            {

                //sInvalidFields = goTR.GetFieldsFromLine(gsTable, sField, True, False)

                var sInvalidFields = goTR.GetFieldsFromLine(TableName, goTR.StrRead(_viewMetaData, "COL" + i + "FIELD", "").ToUpper(), true, false);

                if (string.IsNullOrEmpty(sInvalidFields) == false)
                {
                    IsInvalid = true;
                    break;
                }
                string GetWidth = "";
                GridColumn _gridcolumn = new GridColumn();
                _gridcolumn.Name = goTR.StrRead(_viewMetaData, "COL" + i + "FIELD", "").ToUpper();
                _gridcolumn.AllowEdit = (goTR.StrRead(_viewMetaData, "COL" + i + "MLSISEDIT", "").ToUpper() == "1" ? true : false);
                _gridcolumn.NameOrg = goTR.StrRead(_viewMetaData, "COL" + i + "FIELD", "").ToUpper();
                _gridcolumn.Name = goTR.GetFieldsFromLine(TableName, _gridcolumn.NameOrg).ToUpper();
                _gridcolumn.Title = goTR.StrRead(_viewMetaData, "COL" + i + "LABEL", "");
                _gridcolumn.Alignment = goTR.StrRead(_viewMetaData, "COL" + i + "ALIGNMENT", "");
                GetWidth = goTR.StrRead(_viewMetaData, "COL" + i + "WIDTH", 0);
                _gridcolumn.Width = Convert.ToInt32((GetWidth != "") ? GetWidth : "0") * 6; //; //;

                if (_gridcolumn.AllowEdit)
                {
                    IsShowSaveButton = true;
                }
             

                ViewWidth = ViewWidth + (_gridcolumn.Width);

                int _isLink = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "DISPLAYASLINK", 0));
                int _isIcon = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "DISPLAYASICON", 0));
                int _isButton = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "DISPLAYASBUTTON", 0));   //To set bustton style for MLS columns..J
                int _isIconButton = Convert.ToInt32(goTR.StrRead(_viewMetaData, "COL" + i + "DISPLAYASICONBUTTON", 0));   //To set bustton style for MLS columns..S1


                bool _isSortable = false;
                if (!goTR.IsFieldCombined(_gridcolumn.NameOrg) && godata.IsFieldSortable(_gridcolumn.Name, TableName))
                {
                    _isSortable = true;
                }
                else
                {
                    _isSortable = false;
                }

                //bool _isSortable = godata.IsFieldSortable(_gridcolumn.Name, TableName);
                _gridcolumn.IsSortable = _isSortable;
                _gridcolumn.IsLink = _isLink == 1 ? true : false;
                _gridcolumn.IsIcon = _isIcon == 1 ? true : false;
                _gridcolumn.IsButton = _isButton == 1 ? true : false;
                _gridcolumn.IsIconButton = _isIconButton == 1 ? true : false;
                _gridcolumn.IsVisible = true;
                if (_isLink == 1)
                {
                    string sLink = _gridcolumn.Name;
                    if (Strings.Left(sLink, 4) == "LNK_" & goTR.ExtractString(sLink, 2, "%%") != "GID_ID")
                    {
                        //"#$%^&*"
                        //modify then gen field
                        _gridcolumn.NameOrg = _gridcolumn.NameOrg + "#$%^&*<%" + goTR.ExtractString(sLink, 1, "%%") + "%%GID_ID%>";
                    }
                }
                _gridcolumn.Name = _gridcolumn.Name.Replace("<%", string.Empty).Replace("%>", string.Empty).Replace("%%", "__");

                //if (System.Web.HttpContext.Current.Session[ViewKey] != null && isLoadFromSession == true)
                //{
                //    SessionViewInfo _SessionViewInfo = new SessionViewInfo();
                //    _SessionViewInfo = (SessionViewInfo)System.Web.HttpContext.Current.Session[ViewKey];
                //    if (_SessionViewInfo.Fields.IndexOf(_gridcolumn.Name.Replace("__", "%%")) > -1)
                //    {
                //        Columns.Add(_gridcolumn);
                //    }
                //}
                //else
                //{
                //if (Fields.IndexOf(_gridcolumn.Name.Replace("__", "%%")) > -1)
                //{ 
                if (string.IsNullOrEmpty(_gridcolumn.Title))
                {
                    _gridcolumn.Title = " ";
                }
                Columns.Add(_gridcolumn);
                //}
                //}
                //Columns.Add(_gridcolumn);
            }

            string _enableBulkSave = goTR.StrRead(_viewMetaData, "ENABLEBULKSAVE", "false");

            if (_enableBulkSave == "1")
            {
                IsShowSaveButton = true;
            }
            else
            {
                IsShowSaveButton = false;
            }
            GridColumn _Tgridcolumn = new GridColumn();
            _Tgridcolumn.Name = "GID_ID";
            _Tgridcolumn.NameOrg = "<%GID_ID%>";
            _Tgridcolumn.Title = "GID_ID";
            _Tgridcolumn.Alignment = "left";
            _Tgridcolumn.Width = 20 * 6;
            _Tgridcolumn.IsLink = false;
            _Tgridcolumn.IsVisible = false;
            _Tgridcolumn.AllowEdit = false;
            Columns.Add(_Tgridcolumn);
        }

        private void GetSortColumnsCollection()
        {
            Microsoft.VisualBasic.Collection gcSortFields = godata.GetFilterSortFields("SORT=" + goTR.StrRead(_viewMetaData, "SORT", ""), false);
            Sorts = new List<string>();
            for (int i = 1; i <= gcSortFields.Count; i++)
            {
                string sSortField = gcSortFields[i].ToString();

                sSortField = goTR.ExtractString(sSortField, 1, "|");

                if (string.IsNullOrEmpty(DefaultSortField))
                {
                    DefaultSortField = sSortField;
                    DefaultSortDirection = goTR.ExtractString(gcSortFields[i].ToString(), 2, "|");
                }

                sSortField = "<%" + sSortField + "%>";
                string sFieldFormatted = goTR.GetFieldsFromLine(TableName, sSortField);

                var sInvalidFields = goTR.GetFieldsFromLine(TableName, sSortField, true, false);
                if (string.IsNullOrEmpty(sInvalidFields) == false)
                {
                    IsInvalid = true;
                    break;
                }

                Sorts.Add(sSortField);
            }
        }

        public IList<GridColumn> Columns { get; set; }
        public IList<string> Sorts { get; set; }
        public int PageSize { get; set; }
        public string IDColumn { get; set; }
        public int LinksTop { get; set; }
        public string File { get; set; }
        public bool IsInvalid { get; set; }
        public string DefaultSortField { get; set; }
        public string DefaultSortDirection { get; set; }
        public int ViewWidth { get; set; }

    }
}
